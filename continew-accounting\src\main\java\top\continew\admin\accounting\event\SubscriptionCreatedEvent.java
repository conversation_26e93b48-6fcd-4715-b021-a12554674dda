package top.continew.admin.accounting.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订阅创建事件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Getter
public class SubscriptionCreatedEvent extends ApplicationEvent {

    /**
     * 订阅ID
     */
    private final Long subscriptionId;

    /**
     * 群组ID
     */
    private final Long groupId;

    /**
     * 套餐ID
     */
    private final Long planId;

    /**
     * 套餐名称
     */
    private final String planName;

    /**
     * 用户ID
     */
    private final Long userId;

    /**
     * 支付金额
     */
    private final BigDecimal amountPaid;

    /**
     * 币种
     */
    private final String currency;

    /**
     * 计费周期
     */
    private final String billingCycle;

    /**
     * 开始时间
     */
    private final LocalDateTime startDate;

    /**
     * 结束时间
     */
    private final LocalDateTime endDate;

    /**
     * 支付方式
     */
    private final String paymentMethod;

    /**
     * 创建时间
     */
    private final LocalDateTime createTime;

    public SubscriptionCreatedEvent(Object source, Long subscriptionId, Long groupId, Long planId, String planName,
                                   Long userId, BigDecimal amountPaid, String currency, String billingCycle,
                                   LocalDateTime startDate, LocalDateTime endDate, String paymentMethod,
                                   LocalDateTime createTime) {
        super(source);
        this.subscriptionId = subscriptionId;
        this.groupId = groupId;
        this.planId = planId;
        this.planName = planName;
        this.userId = userId;
        this.amountPaid = amountPaid;
        this.currency = currency;
        this.billingCycle = billingCycle;
        this.startDate = startDate;
        this.endDate = endDate;
        this.paymentMethod = paymentMethod;
        this.createTime = createTime;
    }
}
