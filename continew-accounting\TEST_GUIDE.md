# ContiNew Admin 记账模块测试指南

## 概述

本文档介绍了ContiNew Admin记账模块的测试策略、测试结构和执行方法。

## 测试架构

### 测试分层

```
src/test/java/
├── base/                    # 测试基类
│   ├── BaseTest.java       # 通用测试基类
│   ├── BaseServiceTest.java # Service层测试基类
│   ├── BaseControllerTest.java # Controller层测试基类
│   └── BaseIntegrationTest.java # 集成测试基类
├── service/                # Service层单元测试
├── controller/             # Controller层单元测试
├── integration/            # 集成测试
├── util/                   # 测试工具类
└── AccountingTestSuite.java # 测试套件
```

### 测试类型

1. **单元测试 (Unit Tests)**
   - 测试单个类或方法的功能
   - 使用Mock对象隔离依赖
   - 快速执行，覆盖率要求 >80%

2. **集成测试 (Integration Tests)**
   - 测试多个组件的协作
   - 使用TestContainers提供真实环境
   - 验证端到端功能

3. **控制器测试 (Controller Tests)**
   - 测试HTTP接口
   - 使用MockMvc模拟请求
   - 验证请求响应格式

## 测试配置

### 依赖配置

```xml
<!-- 测试依赖 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-test</artifactId>
    <scope>test</scope>
</dependency>

<!-- H2 内存数据库 -->
<dependency>
    <groupId>com.h2database</groupId>
    <artifactId>h2</artifactId>
    <scope>test</scope>
</dependency>

<!-- TestContainers -->
<dependency>
    <groupId>org.testcontainers</groupId>
    <artifactId>junit-jupiter</artifactId>
    <scope>test</scope>
</dependency>
```

### 测试配置文件

- `application-test.yml`: 测试环境配置
- 使用H2内存数据库进行单元测试
- 使用TestContainers进行集成测试

## 运行测试

### 命令行运行

```bash
# 运行所有测试
mvn test

# 运行单元测试
mvn surefire:test

# 运行集成测试
mvn failsafe:integration-test

# 生成覆盖率报告
mvn jacoco:report

# 检查覆盖率
mvn jacoco:check
```

### 脚本运行

```bash
# Windows
run-tests.bat

# Linux/Mac
chmod +x run-tests.sh
./run-tests.sh
```

### IDE运行

- 在IDE中右键点击测试类或方法运行
- 运行整个测试套件：`AccountingTestSuite`
- 查看覆盖率报告

## 测试覆盖率

### 覆盖率要求

- **指令覆盖率**: ≥80%
- **分支覆盖率**: ≥75%
- **类覆盖率**: ≥90%
- **方法覆盖率**: ≥85%

### 覆盖率报告

测试完成后，覆盖率报告位于：
- HTML报告: `target/site/jacoco/index.html`
- XML报告: `target/site/jacoco/jacoco.xml`

## 测试最佳实践

### 1. 测试命名

```java
// 好的命名
@Test
void testCreateGroup_WithValidData_ShouldReturnGroupId()

// 不好的命名
@Test
void test1()
```

### 2. 测试结构 (AAA模式)

```java
@Test
void testCreateGroup() {
    // Arrange (Given)
    GroupCreateReq request = TestDataUtil.createTestGroupCreateReq();
    
    // Act (When)
    Long groupId = groupService.add(request);
    
    // Assert (Then)
    assertNotNull(groupId);
    verify(groupMapper).insert(any(GroupDO.class));
}
```

### 3. 使用测试数据工具

```java
// 使用TestDataUtil创建测试数据
GroupDO testGroup = TestDataUtil.createTestGroup();
GroupCreateReq createReq = TestDataUtil.createTestGroupCreateReq();
```

### 4. Mock使用

```java
@Mock
private GroupMapper groupMapper;

@InjectMocks
private GroupServiceImpl groupService;

@Test
void testGetById() {
    // Given
    when(groupMapper.selectById(1L)).thenReturn(testGroup);
    
    // When
    GroupResp result = groupService.get(1L);
    
    // Then
    assertNotNull(result);
    verify(groupMapper).selectById(1L);
}
```

### 5. 异常测试

```java
@Test
void testCreateGroup_WithInvalidData_ShouldThrowException() {
    // Given
    GroupCreateReq invalidReq = new GroupCreateReq();
    
    // When & Then
    assertThrows(ValidationException.class, () -> {
        groupService.add(invalidReq);
    });
}
```

## 持续集成

### GitHub Actions配置

```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-java@v3
        with:
          java-version: '17'
      - run: mvn test
      - run: mvn jacoco:report
      - uses: codecov/codecov-action@v3
```

### 质量门禁

- 所有测试必须通过
- 覆盖率不能低于设定阈值
- 不能有严重的代码质量问题

## 故障排除

### 常见问题

1. **测试数据库连接失败**
   - 检查H2数据库配置
   - 确认TestContainers正常启动

2. **Mock对象未生效**
   - 检查@Mock和@InjectMocks注解
   - 确认使用@ExtendWith(MockitoExtension.class)

3. **覆盖率不达标**
   - 增加边界条件测试
   - 添加异常场景测试
   - 测试所有公共方法

4. **集成测试超时**
   - 检查TestContainers启动状态
   - 增加超时时间配置
   - 检查网络连接

### 调试技巧

1. 使用`@Disabled`临时跳过测试
2. 使用`@TestMethodOrder`控制测试执行顺序
3. 使用`@Timeout`设置测试超时时间
4. 查看详细的测试日志输出

## 参考资料

- [JUnit 5 用户指南](https://junit.org/junit5/docs/current/user-guide/)
- [Mockito 文档](https://javadoc.io/doc/org.mockito/mockito-core/latest/org/mockito/Mockito.html)
- [TestContainers 文档](https://www.testcontainers.org/)
- [Spring Boot 测试指南](https://spring.io/guides/gs/testing-web/)
- [JaCoCo 文档](https://www.jacoco.org/jacoco/trunk/doc/)
