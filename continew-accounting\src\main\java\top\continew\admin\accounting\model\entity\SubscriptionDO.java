package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.extension.crud.model.entity.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订阅记录实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("acc_subscription")
public class SubscriptionDO extends BaseDO {

    /**
     * 群组ID
     */
    private Long groupId;

    /**
     * 套餐ID
     */
    private Long planId;

    /**
     * 订阅用户ID
     */
    private Long userId;

    /**
     * 状态(ACTIVE/EXPIRED/CANCELLED/SUSPENDED)
     */
    private String status;

    /**
     * 开始时间
     */
    private LocalDateTime startDate;

    /**
     * 结束时间
     */
    private LocalDateTime endDate;

    /**
     * 自动续费(0:否 1:是)
     */
    private Boolean autoRenew;

    /**
     * 支付方式
     */
    private String paymentMethod;

    /**
     * 支付金额
     */
    private BigDecimal amountPaid;

    /**
     * 币种
     */
    private String currency;

    /**
     * 计费周期
     */
    private String billingCycle;

    /**
     * 试用结束时间
     */
    private LocalDateTime trialEndDate;

    /**
     * 取消时间
     */
    private LocalDateTime cancelledAt;

    /**
     * 取消原因
     */
    private String cancellationReason;
}
