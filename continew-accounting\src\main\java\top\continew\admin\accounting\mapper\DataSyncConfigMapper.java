package top.continew.admin.accounting.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.accounting.model.entity.DataSyncConfigDO;
import top.continew.admin.accounting.model.query.DataSyncConfigQuery;
import top.continew.admin.accounting.model.resp.DataSyncConfigListResp;
import top.continew.starter.extension.crud.mapper.BaseMapper;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 数据同步配置 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface DataSyncConfigMapper extends BaseMapper<DataSyncConfigDO> {

    /**
     * 分页查询
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<DataSyncConfigListResp> selectPageList(Page<DataSyncConfigListResp> page, @Param("query") DataSyncConfigQuery query);

    /**
     * 根据群组ID查询启用的配置
     *
     * @param groupId 群组ID
     * @return 配置列表
     */
    List<DataSyncConfigDO> selectEnabledByGroupId(@Param("groupId") Long groupId);

    /**
     * 根据数据类型查询配置
     *
     * @param groupId  群组ID
     * @param dataType 数据类型
     * @return 配置列表
     */
    List<DataSyncConfigDO> selectByDataType(@Param("groupId") Long groupId, @Param("dataType") String dataType);

    /**
     * 根据同步频率查询需要执行的配置
     *
     * @param frequency 同步频率
     * @param now       当前时间
     * @return 配置列表
     */
    List<DataSyncConfigDO> selectByFrequencyAndTime(@Param("frequency") String frequency, @Param("now") LocalDateTime now);

    /**
     * 查询过期的配置（长时间未同步）
     *
     * @param hours 小时数
     * @return 配置列表
     */
    List<DataSyncConfigDO> selectExpiredConfigs(@Param("hours") Integer hours);

    /**
     * 更新同步状态
     *
     * @param id         配置ID
     * @param status     同步状态
     * @param result     同步结果
     * @param syncTime   同步时间
     * @param nextTime   下次同步时间
     * @param errorMsg   错误信息
     * @param retryCount 重试次数
     */
    void updateSyncStatus(@Param("id") Long id,
                          @Param("status") String status,
                          @Param("result") String result,
                          @Param("syncTime") LocalDateTime syncTime,
                          @Param("nextTime") LocalDateTime nextTime,
                          @Param("errorMsg") String errorMsg,
                          @Param("retryCount") Integer retryCount);

    /**
     * 更新同步统计
     *
     * @param id        配置ID
     * @param statsJson 统计JSON
     */
    void updateSyncStats(@Param("id") Long id, @Param("statsJson") String statsJson);

    /**
     * 重置重试次数
     *
     * @param id 配置ID
     */
    void resetRetryCount(@Param("id") Long id);

    /**
     * 增加重试次数
     *
     * @param id 配置ID
     */
    void incrementRetryCount(@Param("id") Long id);

    /**
     * 批量启用配置
     *
     * @param ids 配置ID列表
     */
    void batchEnable(@Param("ids") List<Long> ids);

    /**
     * 批量禁用配置
     *
     * @param ids 配置ID列表
     */
    void batchDisable(@Param("ids") List<Long> ids);

    /**
     * 查询配置统计
     *
     * @param groupId 群组ID
     * @return 统计信息
     */
    Map<String, Object> selectConfigStatistics(@Param("groupId") Long groupId);

    /**
     * 查询同步性能统计
     *
     * @param configId  配置ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 性能统计
     */
    Map<String, Object> selectPerformanceStatistics(@Param("configId") Long configId,
                                                     @Param("startDate") String startDate,
                                                     @Param("endDate") String endDate);

    /**
     * 查询健康状态
     *
     * @param configId 配置ID
     * @return 健康状态
     */
    Map<String, Object> selectHealthStatus(@Param("configId") Long configId);

    /**
     * 查询配置依赖关系
     *
     * @param configId 配置ID
     * @return 依赖关系
     */
    List<Map<String, Object>> selectConfigDependencies(@Param("configId") Long configId);

    /**
     * 验证配置名称唯一性
     *
     * @param groupId    群组ID
     * @param configName 配置名称
     * @param excludeId  排除的配置ID
     * @return 是否存在
     */
    Boolean existsByConfigName(@Param("groupId") Long groupId,
                               @Param("configName") String configName,
                               @Param("excludeId") Long excludeId);

    /**
     * 查询配置模板
     *
     * @param sourceType 数据源类型
     * @param targetType 目标类型
     * @return 配置模板
     */
    Map<String, Object> selectConfigTemplate(@Param("sourceType") String sourceType,
                                              @Param("targetType") String targetType);

    /**
     * 导出配置
     *
     * @param configIds 配置ID列表
     * @return 导出数据
     */
    List<Map<String, Object>> selectConfigsForExport(@Param("configIds") List<Long> configIds);

    /**
     * 验证导入数据
     *
     * @param groupId    群组ID
     * @param configName 配置名称
     * @return 验证结果
     */
    Map<String, Object> validateImportData(@Param("groupId") Long groupId,
                                            @Param("configName") String configName);
}
