package top.continew.admin.accounting.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import top.continew.admin.accounting.interceptor.UsageLimitInterceptor;

/**
 * 记账模块Web配置
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Configuration
@RequiredArgsConstructor
public class AccountingWebConfig implements WebMvcConfigurer {

    private final UsageLimitInterceptor usageLimitInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册使用限制拦截器
        registry.addInterceptor(usageLimitInterceptor)
                .addPathPatterns("/api/v1/accounting/**")  // 拦截记账相关API
                .addPathPatterns("/api/v1/groups/**")      // 拦截群组相关API
                .addPathPatterns("/api/v1/transactions/**") // 拦截交易相关API
                .addPathPatterns("/api/v1/reports/**")     // 拦截报表相关API
                .addPathPatterns("/api/v1/ocr/**")         // 拦截OCR相关API
                .excludePathPatterns(
                        "/api/v1/accounting/subscription/plans",  // 排除套餐查询
                        "/api/v1/accounting/subscription/current", // 排除当前订阅查询
                        "/api/v1/accounting/usage/current"        // 排除使用量查询
                );
    }
}
