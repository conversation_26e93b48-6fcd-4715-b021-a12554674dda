package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 债务创建请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "债务创建请求")
public class DebtCreateReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    @NotNull(message = "群组ID不能为空")
    private Long groupId;

    /**
     * 债权人ID
     */
    @Schema(description = "债权人ID", example = "1")
    @NotNull(message = "债权人ID不能为空")
    private Long creditorId;

    /**
     * 债务人ID
     */
    @Schema(description = "债务人ID", example = "2")
    @NotNull(message = "债务人ID不能为空")
    private Long debtorId;

    /**
     * 金额
     */
    @Schema(description = "金额", example = "1000.00")
    @NotNull(message = "金额不能为空")
    @DecimalMin(value = "0.01", message = "金额必须大于0")
    @Digits(integer = 13, fraction = 2, message = "金额格式不正确")
    private BigDecimal amount;

    /**
     * 币种
     */
    @Schema(description = "币种", example = "CNY")
    @NotBlank(message = "币种不能为空")
    @Size(max = 10, message = "币种长度不能超过10个字符")
    private String currency;

    /**
     * 描述
     */
    @Schema(description = "描述", example = "借款用于购买设备")
    @Size(max = 500, message = "描述长度不能超过500个字符")
    private String description;

    /**
     * 到期时间
     */
    @Schema(description = "到期时间", example = "2025-02-01 00:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dueDate;

    /**
     * 关联交易ID
     */
    @Schema(description = "关联交易ID", example = "1")
    private Long transactionId;

    /**
     * 债务类型
     */
    @Schema(description = "债务类型", example = "PERSONAL")
    @NotBlank(message = "债务类型不能为空")
    @Size(max = 20, message = "债务类型长度不能超过20个字符")
    private String debtType;

    /**
     * 利率（年化）
     */
    @Schema(description = "利率", example = "0.05")
    @DecimalMin(value = "0", message = "利率不能为负数")
    @DecimalMax(value = "1", message = "利率不能超过100%")
    @Digits(integer = 1, fraction = 4, message = "利率格式不正确")
    private BigDecimal interestRate;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "紧急借款")
    @Size(max = 1000, message = "备注长度不能超过1000个字符")
    private String remark;

    /**
     * 是否自动创建还款计划
     */
    @Schema(description = "是否自动创建还款计划", example = "false")
    private Boolean autoCreatePaymentPlan = false;

    /**
     * 还款期数（如果自动创建还款计划）
     */
    @Schema(description = "还款期数", example = "12")
    @Min(value = 1, message = "还款期数必须大于0")
    @Max(value = 120, message = "还款期数不能超过120期")
    private Integer paymentPeriods;

    /**
     * 还款频率（如果自动创建还款计划）
     */
    @Schema(description = "还款频率", example = "MONTHLY")
    @Size(max = 20, message = "还款频率长度不能超过20个字符")
    private String paymentFrequency;
}
