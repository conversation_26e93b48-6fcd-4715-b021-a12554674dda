package top.continew.admin.accounting.service.notification;

import top.continew.admin.accounting.enums.NotificationChannelEnum;
import top.continew.admin.accounting.model.entity.NotificationDO;

import java.util.List;
import java.util.Map;

/**
 * 通知渠道接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface NotificationChannel {

    /**
     * 获取渠道类型
     *
     * @return 渠道类型
     */
    NotificationChannelEnum getChannelType();

    /**
     * 是否支持该通知类型
     *
     * @param notificationType 通知类型
     * @return 是否支持
     */
    boolean supports(String notificationType);

    /**
     * 发送通知
     *
     * @param notification 通知信息
     * @param targetUsers 目标用户列表
     * @return 发送结果
     */
    Map<String, Object> sendNotification(NotificationDO notification, List<Long> targetUsers);

    /**
     * 批量发送通知
     *
     * @param notification 通知信息
     * @param targetUsers 目标用户列表
     * @return 发送结果
     */
    Map<String, Object> batchSendNotification(NotificationDO notification, List<Long> targetUsers);

    /**
     * 测试连接
     *
     * @param config 配置参数
     * @return 测试结果
     */
    Map<String, Object> testConnection(Map<String, Object> config);

    /**
     * 获取渠道状态
     *
     * @return 渠道状态
     */
    Map<String, Object> getChannelStatus();

    /**
     * 验证配置
     *
     * @param config 配置参数
     * @return 验证结果
     */
    Map<String, Object> validateConfig(Map<String, Object> config);

    /**
     * 格式化消息内容
     *
     * @param notification 通知信息
     * @param targetUser 目标用户
     * @return 格式化后的内容
     */
    Map<String, String> formatMessage(NotificationDO notification, Long targetUser);

    /**
     * 获取用户联系方式
     *
     * @param userId 用户ID
     * @return 联系方式
     */
    String getUserContact(Long userId);

    /**
     * 是否启用
     *
     * @return 是否启用
     */
    boolean isEnabled();

    /**
     * 获取发送限制
     *
     * @return 发送限制配置
     */
    Map<String, Object> getSendLimits();

    /**
     * 检查发送限制
     *
     * @param targetUsers 目标用户列表
     * @return 是否可以发送
     */
    boolean checkSendLimits(List<Long> targetUsers);

    /**
     * 获取错误重试策略
     *
     * @return 重试策略
     */
    Map<String, Object> getRetryStrategy();

    /**
     * 处理发送回调
     *
     * @param callbackData 回调数据
     * @return 处理结果
     */
    Map<String, Object> handleCallback(Map<String, Object> callbackData);

}
