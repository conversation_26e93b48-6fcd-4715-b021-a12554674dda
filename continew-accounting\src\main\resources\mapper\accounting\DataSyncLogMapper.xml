<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.accounting.mapper.DataSyncLogMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="top.continew.admin.accounting.model.entity.DataSyncLogDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="config_id" property="configId" jdbcType="BIGINT"/>
        <result column="sync_id" property="syncId" jdbcType="VARCHAR"/>
        <result column="sync_type" property="syncType" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="duration" property="duration" jdbcType="BIGINT"/>
        <result column="total_count" property="totalCount" jdbcType="INTEGER"/>
        <result column="success_count" property="successCount" jdbcType="INTEGER"/>
        <result column="failed_count" property="failedCount" jdbcType="INTEGER"/>
        <result column="conflict_count" property="conflictCount" jdbcType="INTEGER"/>
        <result column="data_size" property="dataSize" jdbcType="BIGINT"/>
        <result column="throughput" property="throughput" jdbcType="DECIMAL"/>
        <result column="error_message" property="errorMessage" jdbcType="TEXT"/>
        <result column="error_details" property="errorDetails" jdbcType="LONGTEXT"/>
        <result column="sync_details" property="syncDetails" jdbcType="LONGTEXT"/>
        <result column="performance_metrics" property="performanceMetrics" jdbcType="LONGTEXT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, config_id, sync_id, sync_type, status, start_time, end_time, duration,
        total_count, success_count, failed_count, conflict_count, data_size, throughput,
        error_message, error_details, sync_details, performance_metrics, create_time, create_by
    </sql>

    <!-- 查询同步统计信息 -->
    <select id="selectSyncStatistics" resultType="java.util.Map">
        SELECT 
            COUNT(*) as totalSyncs,
            COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completedSyncs,
            COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failedSyncs,
            COUNT(CASE WHEN status = 'RUNNING' THEN 1 END) as runningSyncs,
            COUNT(CASE WHEN status = 'STOPPED' THEN 1 END) as stoppedSyncs,
            SUM(total_count) as totalRecords,
            SUM(success_count) as successRecords,
            SUM(failed_count) as failedRecords,
            SUM(conflict_count) as conflictRecords,
            AVG(duration) as avgDuration,
            MAX(duration) as maxDuration,
            MIN(duration) as minDuration,
            AVG(throughput) as avgThroughput,
            MAX(throughput) as maxThroughput,
            SUM(data_size) as totalDataSize
        FROM acc_data_sync_log
        <where>
            <if test="configId != null">
                AND config_id = #{configId}
            </if>
            <if test="startDate != null and startDate != ''">
                AND start_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND start_time &lt;= #{endDate}
            </if>
            <if test="syncType != null and syncType != ''">
                AND sync_type = #{syncType}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
    </select>

    <!-- 查询性能分析数据 -->
    <select id="selectPerformanceAnalysis" resultType="java.util.Map">
        SELECT 
            config_id,
            sync_type,
            COUNT(*) as syncCount,
            AVG(duration) as avgDuration,
            MIN(duration) as minDuration,
            MAX(duration) as maxDuration,
            STDDEV(duration) as stdDevDuration,
            AVG(throughput) as avgThroughput,
            MAX(throughput) as maxThroughput,
            AVG(total_count) as avgRecordCount,
            SUM(total_count) as totalRecords,
            SUM(success_count) as totalSuccess,
            SUM(failed_count) as totalFailed,
            CASE 
                WHEN SUM(total_count) > 0 THEN ROUND((SUM(success_count) * 100.0 / SUM(total_count)), 2)
                ELSE 0 
            END as successRate,
            AVG(data_size) as avgDataSize,
            SUM(data_size) as totalDataSize
        FROM acc_data_sync_log
        <where>
            <if test="configId != null">
                AND config_id = #{configId}
            </if>
            <if test="startDate != null and startDate != ''">
                AND start_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND start_time &lt;= #{endDate}
            </if>
            AND status = 'COMPLETED'
        </where>
        GROUP BY config_id, sync_type
        ORDER BY 
        <choose>
            <when test="orderBy == 'avgDuration'">
                avgDuration ASC
            </when>
            <when test="orderBy == 'throughput'">
                avgThroughput DESC
            </when>
            <when test="orderBy == 'successRate'">
                successRate DESC
            </when>
            <otherwise>
                syncCount DESC
            </otherwise>
        </choose>
    </select>

    <!-- 查询错误分析数据 -->
    <select id="selectErrorAnalysis" resultType="java.util.Map">
        SELECT 
            error_message,
            COUNT(*) as errorCount,
            COUNT(DISTINCT config_id) as affectedConfigs,
            MIN(start_time) as firstOccurrence,
            MAX(start_time) as lastOccurrence,
            AVG(failed_count) as avgFailedRecords,
            SUM(failed_count) as totalFailedRecords,
            GROUP_CONCAT(DISTINCT sync_type) as affectedSyncTypes
        FROM acc_data_sync_log
        <where>
            AND status = 'FAILED'
            AND error_message IS NOT NULL
            AND error_message != ''
            <if test="configId != null">
                AND config_id = #{configId}
            </if>
            <if test="startDate != null and startDate != ''">
                AND start_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND start_time &lt;= #{endDate}
            </if>
        </where>
        GROUP BY error_message
        ORDER BY errorCount DESC, lastOccurrence DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询冲突分析数据 -->
    <select id="selectConflictAnalysis" resultType="java.util.Map">
        SELECT 
            config_id,
            sync_type,
            COUNT(*) as syncCount,
            SUM(conflict_count) as totalConflicts,
            AVG(conflict_count) as avgConflicts,
            MAX(conflict_count) as maxConflicts,
            SUM(total_count) as totalRecords,
            CASE 
                WHEN SUM(total_count) > 0 THEN ROUND((SUM(conflict_count) * 100.0 / SUM(total_count)), 2)
                ELSE 0 
            END as conflictRate,
            MIN(start_time) as firstConflict,
            MAX(start_time) as lastConflict
        FROM acc_data_sync_log
        <where>
            AND conflict_count > 0
            <if test="configId != null">
                AND config_id = #{configId}
            </if>
            <if test="startDate != null and startDate != ''">
                AND start_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND start_time &lt;= #{endDate}
            </if>
        </where>
        GROUP BY config_id, sync_type
        ORDER BY conflictRate DESC, totalConflicts DESC
    </select>

    <!-- 查询重试分析数据 -->
    <select id="selectRetryAnalysis" resultType="java.util.Map">
        SELECT 
            sync_id,
            config_id,
            sync_type,
            COUNT(*) as retryCount,
            MIN(start_time) as firstAttempt,
            MAX(start_time) as lastAttempt,
            TIMESTAMPDIFF(MINUTE, MIN(start_time), MAX(start_time)) as retryDuration,
            MAX(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) as finallySucceeded,
            GROUP_CONCAT(status ORDER BY start_time) as statusHistory,
            GROUP_CONCAT(error_message ORDER BY start_time SEPARATOR ' | ') as errorHistory
        FROM acc_data_sync_log
        WHERE sync_id IN (
            SELECT sync_id 
            FROM acc_data_sync_log 
            GROUP BY sync_id 
            HAVING COUNT(*) > 1
        )
        <if test="configId != null">
            AND config_id = #{configId}
        </if>
        <if test="startDate != null and startDate != ''">
            AND start_time >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND start_time &lt;= #{endDate}
        </if>
        GROUP BY sync_id, config_id, sync_type
        ORDER BY retryCount DESC, retryDuration DESC
    </select>

    <!-- 查询趋势分析数据 -->
    <select id="selectTrendAnalysis" resultType="java.util.Map">
        SELECT 
            <choose>
                <when test="groupBy == 'HOUR'">
                    DATE_FORMAT(start_time, '%Y-%m-%d %H:00:00') as timeGroup
                </when>
                <when test="groupBy == 'DAY'">
                    DATE(start_time) as timeGroup
                </when>
                <when test="groupBy == 'WEEK'">
                    DATE_FORMAT(start_time, '%Y-%u') as timeGroup
                </when>
                <when test="groupBy == 'MONTH'">
                    DATE_FORMAT(start_time, '%Y-%m') as timeGroup
                </when>
                <otherwise>
                    DATE(start_time) as timeGroup
                </otherwise>
            </choose>,
            COUNT(*) as syncCount,
            COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completedCount,
            COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failedCount,
            SUM(total_count) as totalRecords,
            SUM(success_count) as successRecords,
            SUM(failed_count) as failedRecords,
            SUM(conflict_count) as conflictRecords,
            AVG(duration) as avgDuration,
            AVG(throughput) as avgThroughput,
            SUM(data_size) as totalDataSize,
            CASE 
                WHEN SUM(total_count) > 0 THEN ROUND((SUM(success_count) * 100.0 / SUM(total_count)), 2)
                ELSE 0 
            END as successRate
        FROM acc_data_sync_log
        <where>
            <if test="configId != null">
                AND config_id = #{configId}
            </if>
            <if test="startDate != null and startDate != ''">
                AND start_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND start_time &lt;= #{endDate}
            </if>
            <if test="syncType != null and syncType != ''">
                AND sync_type = #{syncType}
            </if>
        </where>
        GROUP BY timeGroup
        ORDER BY timeGroup DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 批量插入同步日志 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO acc_data_sync_log (
            config_id, sync_id, sync_type, status, start_time, end_time, duration,
            total_count, success_count, failed_count, conflict_count, data_size, throughput,
            error_message, error_details, sync_details, performance_metrics, create_time, create_by
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.configId}, #{item.syncId}, #{item.syncType}, #{item.status},
                #{item.startTime}, #{item.endTime}, #{item.duration}, #{item.totalCount},
                #{item.successCount}, #{item.failedCount}, #{item.conflictCount},
                #{item.dataSize}, #{item.throughput}, #{item.errorMessage}, #{item.errorDetails},
                #{item.syncDetails}, #{item.performanceMetrics}, #{item.createTime}, #{item.createBy}
            )
        </foreach>
    </insert>

    <!-- 批量更新日志状态 -->
    <update id="batchUpdateStatus">
        UPDATE acc_data_sync_log 
        SET status = #{status}
        WHERE id IN
        <foreach collection="logIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 清理过期日志 -->
    <delete id="cleanupExpiredLogs">
        DELETE FROM acc_data_sync_log
        WHERE create_time &lt; DATE_SUB(NOW(), INTERVAL #{retentionDays} DAY)
        <if test="configId != null">
            AND config_id = #{configId}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
    </delete>

    <!-- 查询日志摘要 -->
    <select id="selectLogSummary" resultType="java.util.Map">
        SELECT 
            l.id,
            l.sync_id,
            l.sync_type,
            l.status,
            l.start_time,
            l.end_time,
            l.duration,
            l.total_count,
            l.success_count,
            l.failed_count,
            l.conflict_count,
            l.throughput,
            l.error_message,
            c.config_name,
            c.source_type,
            c.target_type
        FROM acc_data_sync_log l
        LEFT JOIN acc_data_sync_config c ON l.config_id = c.id
        <where>
            <if test="configId != null">
                AND l.config_id = #{configId}
            </if>
            <if test="syncId != null and syncId != ''">
                AND l.sync_id = #{syncId}
            </if>
            <if test="status != null and status != ''">
                AND l.status = #{status}
            </if>
            <if test="startDate != null and startDate != ''">
                AND l.start_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND l.start_time &lt;= #{endDate}
            </if>
        </where>
        ORDER BY l.start_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询最近同步状态 -->
    <select id="selectRecentSyncStatus" resultType="java.util.Map">
        SELECT 
            config_id,
            MAX(start_time) as lastSyncTime,
            MAX(CASE WHEN status = 'COMPLETED' THEN start_time END) as lastSuccessTime,
            MAX(CASE WHEN status = 'FAILED' THEN start_time END) as lastFailureTime,
            COUNT(*) as totalSyncs,
            COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as successCount,
            COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failureCount,
            AVG(duration) as avgDuration,
            SUM(total_count) as totalRecords,
            SUM(success_count) as totalSuccess,
            SUM(failed_count) as totalFailed
        FROM acc_data_sync_log
        WHERE start_time >= DATE_SUB(NOW(), INTERVAL #{recentDays} DAY)
        <if test="configIds != null and configIds.size() > 0">
            AND config_id IN
            <foreach collection="configIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY config_id
        ORDER BY lastSyncTime DESC
    </select>

</mapper>
