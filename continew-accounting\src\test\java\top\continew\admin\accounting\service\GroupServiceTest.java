package top.continew.admin.accounting.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import top.continew.admin.accounting.base.BaseServiceTest;
import top.continew.admin.accounting.mapper.GroupMapper;
import top.continew.admin.accounting.model.entity.GroupDO;
import top.continew.admin.accounting.model.req.GroupCreateReq;
import top.continew.admin.accounting.model.req.GroupQueryReq;
import top.continew.admin.accounting.model.req.GroupUpdateReq;
import top.continew.admin.accounting.model.resp.GroupResp;
import top.continew.admin.accounting.service.impl.GroupServiceImpl;
import top.continew.admin.accounting.util.TestDataUtil;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 群组服务测试
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@ExtendWith(MockitoExtension.class)
class GroupServiceTest extends BaseServiceTest {

    @Mock
    private GroupMapper groupMapper;

    @InjectMocks
    private GroupServiceImpl groupService;

    private GroupDO testGroup;
    private GroupCreateReq createReq;
    private GroupUpdateReq updateReq;

    @BeforeEach
    @Override
    protected void beforeEach() {
        super.beforeEach();
        testGroup = TestDataUtil.createTestGroup();
        createReq = TestDataUtil.createTestGroupCreateReq();
        
        updateReq = new GroupUpdateReq();
        updateReq.setName("更新后的群组名称");
        updateReq.setDescription("更新后的描述");
        updateReq.setCurrency("USD");
    }

    @Test
    void testAdd() {
        // Given
        when(groupMapper.insert(any(GroupDO.class))).thenReturn(1);
        when(groupMapper.selectById(anyLong())).thenReturn(testGroup);

        // When
        Long groupId = groupService.add(createReq);

        // Then
        assertNotNull(groupId);
        verify(groupMapper).insert(any(GroupDO.class));
    }

    @Test
    void testGetById() {
        // Given
        when(groupMapper.selectById(1L)).thenReturn(testGroup);

        // When
        GroupResp result = groupService.get(1L);

        // Then
        assertNotNull(result);
        assertEquals(testGroup.getName(), result.getName());
        assertEquals(testGroup.getDescription(), result.getDescription());
        verify(groupMapper).selectById(1L);
    }

    @Test
    void testUpdate() {
        // Given
        when(groupMapper.selectById(1L)).thenReturn(testGroup);
        when(groupMapper.updateById(any(GroupDO.class))).thenReturn(1);

        // When
        assertDoesNotThrow(() -> groupService.update(updateReq, 1L));

        // Then
        verify(groupMapper).selectById(1L);
        verify(groupMapper).updateById(any(GroupDO.class));
    }

    @Test
    void testDelete() {
        // Given
        when(groupMapper.selectById(1L)).thenReturn(testGroup);
        when(groupMapper.deleteById(1L)).thenReturn(1);

        // When
        assertDoesNotThrow(() -> groupService.delete(Arrays.asList(1L)));

        // Then
        verify(groupMapper).selectById(1L);
        verify(groupMapper).deleteById(1L);
    }

    @Test
    void testPage() {
        // Given
        GroupQueryReq queryReq = new GroupQueryReq();
        queryReq.setName("测试");
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPage(1);
        pageQuery.setSize(10);

        List<GroupDO> groups = Arrays.asList(testGroup);
        when(groupMapper.selectPage(any(), any())).thenReturn(groups);
        when(groupMapper.selectCount(any())).thenReturn(1L);

        // When
        PageResp<GroupResp> result = groupService.page(queryReq, pageQuery);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getList().size());
        assertEquals(testGroup.getName(), result.getList().get(0).getName());
    }

    @Test
    void testList() {
        // Given
        GroupQueryReq queryReq = new GroupQueryReq();
        List<GroupDO> groups = Arrays.asList(testGroup);
        when(groupMapper.selectList(any())).thenReturn(groups);

        // When
        List<GroupResp> result = groupService.list(queryReq);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testGroup.getName(), result.get(0).getName());
    }

    @Test
    void testAddMember() {
        // Given
        when(groupMapper.selectById(1L)).thenReturn(testGroup);
        when(groupMapper.updateById(any(GroupDO.class))).thenReturn(1);

        // When
        assertDoesNotThrow(() -> groupService.addMember(1L, 2L, "MEMBER"));

        // Then
        verify(groupMapper).selectById(1L);
        verify(groupMapper).updateById(any(GroupDO.class));
    }

    @Test
    void testRemoveMember() {
        // Given
        when(groupMapper.selectById(1L)).thenReturn(testGroup);
        when(groupMapper.updateById(any(GroupDO.class))).thenReturn(1);

        // When
        assertDoesNotThrow(() -> groupService.removeMember(1L, 2L));

        // Then
        verify(groupMapper).selectById(1L);
        verify(groupMapper).updateById(any(GroupDO.class));
    }

    @Test
    void testGenerateInviteCode() {
        // Given
        when(groupMapper.selectById(1L)).thenReturn(testGroup);
        when(groupMapper.updateById(any(GroupDO.class))).thenReturn(1);

        // When
        String inviteCode = groupService.generateInviteCode(1L);

        // Then
        assertNotNull(inviteCode);
        assertFalse(inviteCode.isEmpty());
        verify(groupMapper).selectById(1L);
        verify(groupMapper).updateById(any(GroupDO.class));
    }

    @Test
    void testJoinByInviteCode() {
        // Given
        when(groupMapper.selectOne(any())).thenReturn(testGroup);
        when(groupMapper.updateById(any(GroupDO.class))).thenReturn(1);

        // When
        Long groupId = groupService.joinByInviteCode("TEST123", 2L);

        // Then
        assertNotNull(groupId);
        assertEquals(testGroup.getId(), groupId);
        verify(groupMapper).selectOne(any());
        verify(groupMapper).updateById(any(GroupDO.class));
    }

    @Test
    void testGetStatistics() {
        // Given
        when(groupMapper.selectById(1L)).thenReturn(testGroup);

        // When
        var statistics = groupService.getStatistics(1L);

        // Then
        assertNotNull(statistics);
        verify(groupMapper).selectById(1L);
    }

}
