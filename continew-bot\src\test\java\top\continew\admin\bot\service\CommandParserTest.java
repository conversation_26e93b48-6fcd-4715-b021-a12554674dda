package top.continew.admin.bot.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import top.continew.admin.accounting.enums.TransactionType;
import top.continew.admin.bot.model.dto.ParsedCommand;

import java.math.BigDecimal;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 命令解析器测试
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
class CommandParserTest {

    private CommandParser commandParser;

    @BeforeEach
    void setUp() {
        commandParser = new CommandParser();
    }

    @Test
    void testParseBasicExpenseCommand() {
        // Given
        String command = "-50 午餐";

        // When
        ParsedCommand result = commandParser.parseCommand(command);

        // Then
        assertNotNull(result);
        assertTrue(result.getValid());
        assertEquals(TransactionType.EXPENSE, result.getType());
        assertEquals(new BigDecimal("50"), result.getAmount());
        assertEquals("午餐", result.getDescription());
        assertEquals("CNY", result.getCurrency());
    }

    @Test
    void testParseBasicIncomeCommand() {
        // Given
        String command = "+1000 工资";

        // When
        ParsedCommand result = commandParser.parseCommand(command);

        // Then
        assertNotNull(result);
        assertTrue(result.getValid());
        assertEquals(TransactionType.INCOME, result.getType());
        assertEquals(new BigDecimal("1000"), result.getAmount());
        assertEquals("工资", result.getDescription());
    }

    @Test
    void testParseCommandWithCategory() {
        // Given
        String command = "-50 午餐 @餐饮";

        // When
        ParsedCommand result = commandParser.parseCommand(command);

        // Then
        assertNotNull(result);
        assertTrue(result.getValid());
        assertEquals("餐饮", result.getCategory());
        assertEquals("午餐", result.getDescription());
    }

    @Test
    void testParseCommandWithTags() {
        // Given
        String command = "-50 午餐 #日常 #工作";

        // When
        ParsedCommand result = commandParser.parseCommand(command);

        // Then
        assertNotNull(result);
        assertTrue(result.getValid());
        assertEquals(List.of("日常", "工作"), result.getTags());
    }

    @Test
    void testParseCommandWithCurrency() {
        // Given
        String command = "-50 $USD 午餐";

        // When
        ParsedCommand result = commandParser.parseCommand(command);

        // Then
        assertNotNull(result);
        assertTrue(result.getValid());
        assertEquals("USD", result.getCurrency());
    }

    @Test
    void testParseCommandWithMultiplier() {
        // Given
        String command = "-20*3 咖啡";

        // When
        ParsedCommand result = commandParser.parseCommand(command);

        // Then
        assertNotNull(result);
        assertTrue(result.getValid());
        assertEquals(new BigDecimal("60"), result.getAmount());
        assertEquals("咖啡", result.getDescription());
    }

    @Test
    void testParseCommandWithSplit() {
        // Given
        String command = "-100 聚餐 /split @张三 @李四";

        // When
        ParsedCommand result = commandParser.parseCommand(command);

        // Then
        assertNotNull(result);
        assertTrue(result.getValid());
        assertTrue(result.getSplitEnabled());
        assertEquals(List.of("张三", "李四"), result.getSplitUsers());
    }

    @Test
    void testParseCommandWithSplitCount() {
        // Given
        String command = "-150 聚餐 /split 3";

        // When
        ParsedCommand result = commandParser.parseCommand(command);

        // Then
        assertNotNull(result);
        assertTrue(result.getValid());
        assertTrue(result.getSplitEnabled());
        assertEquals(3, result.getSplitCount());
    }

    @Test
    void testParseCommandWithWallet() {
        // Given
        String command = "-50 午餐 钱包:支付宝";

        // When
        ParsedCommand result = commandParser.parseCommand(command);

        // Then
        assertNotNull(result);
        assertTrue(result.getValid());
        assertEquals("支付宝", result.getWallet());
    }

    @Test
    void testParseTransferCommand() {
        // Given
        String command = "从支付宝到银行卡转账500";

        // When
        ParsedCommand result = commandParser.parseCommand(command);

        // Then
        assertNotNull(result);
        assertTrue(result.getValid());
        assertEquals(TransactionType.TRANSFER, result.getType());
        assertEquals("支付宝", result.getFromWallet());
        assertEquals("银行卡", result.getToWallet());
    }

    @Test
    void testParseRecurringCommand() {
        // Given
        String command = "-50 每天 咖啡";

        // When
        ParsedCommand result = commandParser.parseCommand(command);

        // Then
        assertNotNull(result);
        assertTrue(result.getValid());
        assertTrue(result.getRecurringEnabled());
        assertEquals("每天", result.getRecurringPattern());
    }

    @Test
    void testParseNaturalLanguageCommand() {
        // Given
        String command = "花了50块钱买咖啡";

        // When
        ParsedCommand result = commandParser.parseCommand(command);

        // Then
        assertNotNull(result);
        assertTrue(result.getValid());
        assertEquals(TransactionType.EXPENSE, result.getType());
        assertEquals(new BigDecimal("50"), result.getAmount());
    }

    @Test
    void testParseNaturalLanguageIncomeCommand() {
        // Given
        String command = "今天赚了1000工资";

        // When
        ParsedCommand result = commandParser.parseCommand(command);

        // Then
        assertNotNull(result);
        assertTrue(result.getValid());
        assertEquals(TransactionType.INCOME, result.getType());
        assertEquals(new BigDecimal("1000"), result.getAmount());
    }

    @Test
    void testParseComplexCommand() {
        // Given
        String command = "-50 ¥CNY 午餐 @餐饮 #日常 #工作 昨天 钱包:支付宝";

        // When
        ParsedCommand result = commandParser.parseCommand(command);

        // Then
        assertNotNull(result);
        assertTrue(result.getValid());
        assertEquals(TransactionType.EXPENSE, result.getType());
        assertEquals(new BigDecimal("50"), result.getAmount());
        assertEquals("CNY", result.getCurrency());
        assertEquals("午餐", result.getDescription());
        assertEquals("餐饮", result.getCategory());
        assertEquals(List.of("日常", "工作"), result.getTags());
        assertEquals("支付宝", result.getWallet());
        assertNotNull(result.getTransactionTime());
    }

    @Test
    void testParseInvalidCommand() {
        // Given
        String command = "无效命令";

        // When
        ParsedCommand result = commandParser.parseCommand(command);

        // Then
        assertNotNull(result);
        assertFalse(result.getValid());
        assertNotNull(result.getErrorMessage());
    }

    @Test
    void testParseEmptyCommand() {
        // Given
        String command = "";

        // When
        ParsedCommand result = commandParser.parseCommand(command);

        // Then
        assertNull(result);
    }

    @Test
    void testParseNullCommand() {
        // Given
        String command = null;

        // When
        ParsedCommand result = commandParser.parseCommand(command);

        // Then
        assertNull(result);
    }

    @Test
    void testSuggestCategory() {
        // Test餐饮类
        assertEquals("餐饮", commandParser.suggestCategory("午餐"));
        assertEquals("餐饮", commandParser.suggestCategory("咖啡"));
        assertEquals("餐饮", commandParser.suggestCategory("外卖"));

        // Test交通类
        assertEquals("交通", commandParser.suggestCategory("打车"));
        assertEquals("交通", commandParser.suggestCategory("地铁"));
        assertEquals("交通", commandParser.suggestCategory("油费"));

        // Test购物类
        assertEquals("购物", commandParser.suggestCategory("购物"));
        assertEquals("购物", commandParser.suggestCategory("淘宝"));

        // Test未知类别
        assertNull(commandParser.suggestCategory("未知描述"));
    }

    @Test
    void testSuggestTags() {
        List<String> tags = commandParser.suggestTags("早餐工作日常");
        assertTrue(tags.contains("早餐"));
        assertTrue(tags.contains("工作"));
        assertTrue(tags.contains("日常"));
    }

    @Test
    void testIsValidCommandSyntax() {
        assertTrue(commandParser.isValidCommandSyntax("-50 午餐"));
        assertTrue(commandParser.isValidCommandSyntax("+1000 工资"));
        assertTrue(commandParser.isValidCommandSyntax("花了50买咖啡"));
        assertTrue(commandParser.isValidCommandSyntax("/help"));
        
        assertFalse(commandParser.isValidCommandSyntax(""));
        assertFalse(commandParser.isValidCommandSyntax("无效命令"));
    }

    @Test
    void testGetCommandSuggestions() {
        List<String> suggestions = commandParser.getCommandSuggestions("吃");
        assertFalse(suggestions.isEmpty());
        assertTrue(suggestions.stream().anyMatch(s -> s.contains("餐饮")));

        suggestions = commandParser.getCommandSuggestions("车");
        assertFalse(suggestions.isEmpty());
        assertTrue(suggestions.stream().anyMatch(s -> s.contains("交通")));

        suggestions = commandParser.getCommandSuggestions("");
        assertFalse(suggestions.isEmpty());
    }

    @Test
    void testGetHelpMessage() {
        String help = commandParser.getHelpMessage();
        assertNotNull(help);
        assertFalse(help.isEmpty());
        assertTrue(help.contains("记账命令格式"));
    }
}
