package top.continew.admin.accounting.service.notification;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.continew.admin.accounting.enums.NotificationChannelEnum;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 通知渠道工厂
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class NotificationChannelFactory {

    private final List<NotificationChannel> channels;
    
    /**
     * 渠道缓存
     */
    private final Map<String, NotificationChannel> channelCache = new ConcurrentHashMap<>();

    /**
     * 获取通知渠道
     *
     * @param channelCode 渠道代码
     * @return 通知渠道
     */
    public NotificationChannel getChannel(String channelCode) {
        return channelCache.computeIfAbsent(channelCode, code -> {
            return channels.stream()
                    .filter(channel -> channel.getChannelType().getCode().equals(code))
                    .findFirst()
                    .orElse(null);
        });
    }

    /**
     * 获取通知渠道
     *
     * @param channelEnum 渠道枚举
     * @return 通知渠道
     */
    public NotificationChannel getChannel(NotificationChannelEnum channelEnum) {
        return getChannel(channelEnum.getCode());
    }

    /**
     * 获取支持指定通知类型的渠道列表
     *
     * @param notificationType 通知类型
     * @return 支持的渠道列表
     */
    public List<NotificationChannel> getSupportedChannels(String notificationType) {
        return channels.stream()
                .filter(channel -> channel.supports(notificationType))
                .filter(NotificationChannel::isEnabled)
                .collect(Collectors.toList());
    }

    /**
     * 获取所有可用渠道
     *
     * @return 可用渠道列表
     */
    public List<NotificationChannel> getAvailableChannels() {
        return channels.stream()
                .filter(NotificationChannel::isEnabled)
                .collect(Collectors.toList());
    }

    /**
     * 获取所有渠道
     *
     * @return 所有渠道列表
     */
    public List<NotificationChannel> getAllChannels() {
        return channels;
    }

    /**
     * 检查渠道是否存在
     *
     * @param channelCode 渠道代码
     * @return 是否存在
     */
    public boolean hasChannel(String channelCode) {
        return getChannel(channelCode) != null;
    }

    /**
     * 检查渠道是否可用
     *
     * @param channelCode 渠道代码
     * @return 是否可用
     */
    public boolean isChannelAvailable(String channelCode) {
        NotificationChannel channel = getChannel(channelCode);
        return channel != null && channel.isEnabled();
    }

    /**
     * 获取渠道状态
     *
     * @return 渠道状态列表
     */
    public List<Map<String, Object>> getChannelStatuses() {
        return channels.stream()
                .map(NotificationChannel::getChannelStatus)
                .collect(Collectors.toList());
    }

    /**
     * 测试渠道连接
     *
     * @param channelCode 渠道代码
     * @param config 配置参数
     * @return 测试结果
     */
    public Map<String, Object> testChannelConnection(String channelCode, Map<String, Object> config) {
        NotificationChannel channel = getChannel(channelCode);
        if (channel == null) {
            return Map.of(
                    "success", false,
                    "errorMessage", "渠道不存在: " + channelCode
            );
        }
        
        return channel.testConnection(config);
    }

    /**
     * 验证渠道配置
     *
     * @param channelCode 渠道代码
     * @param config 配置参数
     * @return 验证结果
     */
    public Map<String, Object> validateChannelConfig(String channelCode, Map<String, Object> config) {
        NotificationChannel channel = getChannel(channelCode);
        if (channel == null) {
            return Map.of(
                    "valid", false,
                    "message", "渠道不存在: " + channelCode
            );
        }
        
        return channel.validateConfig(config);
    }

    /**
     * 获取渠道发送限制
     *
     * @param channelCode 渠道代码
     * @return 发送限制
     */
    public Map<String, Object> getChannelLimits(String channelCode) {
        NotificationChannel channel = getChannel(channelCode);
        if (channel == null) {
            return Map.of();
        }
        
        return channel.getSendLimits();
    }

    /**
     * 检查渠道发送限制
     *
     * @param channelCode 渠道代码
     * @param targetUsers 目标用户列表
     * @return 是否可以发送
     */
    public boolean checkChannelLimits(String channelCode, List<Long> targetUsers) {
        NotificationChannel channel = getChannel(channelCode);
        if (channel == null) {
            return false;
        }
        
        return channel.checkSendLimits(targetUsers);
    }

    /**
     * 获取渠道重试策略
     *
     * @param channelCode 渠道代码
     * @return 重试策略
     */
    public Map<String, Object> getChannelRetryStrategy(String channelCode) {
        NotificationChannel channel = getChannel(channelCode);
        if (channel == null) {
            return Map.of();
        }
        
        return channel.getRetryStrategy();
    }

    /**
     * 处理渠道回调
     *
     * @param channelCode 渠道代码
     * @param callbackData 回调数据
     * @return 处理结果
     */
    public Map<String, Object> handleChannelCallback(String channelCode, Map<String, Object> callbackData) {
        NotificationChannel channel = getChannel(channelCode);
        if (channel == null) {
            return Map.of(
                    "processed", false,
                    "errorMessage", "渠道不存在: " + channelCode
            );
        }
        
        return channel.handleCallback(callbackData);
    }

    /**
     * 过滤可用渠道
     *
     * @param channelCodes 渠道代码列表
     * @param notificationType 通知类型
     * @return 过滤后的渠道列表
     */
    public List<String> filterAvailableChannels(List<String> channelCodes, String notificationType) {
        if (CollUtil.isEmpty(channelCodes)) {
            return getSupportedChannels(notificationType).stream()
                    .map(channel -> channel.getChannelType().getCode())
                    .collect(Collectors.toList());
        }
        
        return channelCodes.stream()
                .filter(this::isChannelAvailable)
                .filter(channelCode -> {
                    NotificationChannel channel = getChannel(channelCode);
                    return channel != null && channel.supports(notificationType);
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取渠道优先级排序
     *
     * @param channelCodes 渠道代码列表
     * @param notificationType 通知类型
     * @return 按优先级排序的渠道列表
     */
    public List<String> sortChannelsByPriority(List<String> channelCodes, String notificationType) {
        // 根据通知类型和渠道特性进行优先级排序
        return channelCodes.stream()
                .sorted((c1, c2) -> {
                    NotificationChannelEnum enum1 = NotificationChannelEnum.getByCode(c1);
                    NotificationChannelEnum enum2 = NotificationChannelEnum.getByCode(c2);
                    
                    if (enum1 == null || enum2 == null) {
                        return 0;
                    }
                    
                    // 实时性要求高的通知类型优先使用即时渠道
                    if (List.of("SECURITY", "SYSTEM").contains(notificationType)) {
                        boolean instant1 = enum1.isInstantChannel();
                        boolean instant2 = enum2.isInstantChannel();
                        if (instant1 != instant2) {
                            return instant1 ? -1 : 1;
                        }
                    }
                    
                    // 默认按渠道顺序
                    return Integer.compare(enum1.ordinal(), enum2.ordinal());
                })
                .collect(Collectors.toList());
    }

}
