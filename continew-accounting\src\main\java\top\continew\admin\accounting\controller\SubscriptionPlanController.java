package top.continew.admin.accounting.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.accounting.model.req.SubscriptionPlanCreateReq;
import top.continew.admin.accounting.model.req.SubscriptionPlanUpdateReq;
import top.continew.admin.accounting.model.resp.SubscriptionPlanDetailResp;
import top.continew.admin.accounting.model.resp.SubscriptionPlanListResp;
import top.continew.admin.accounting.model.query.SubscriptionPlanQuery;
import top.continew.admin.accounting.service.SubscriptionPlanService;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.controller.BaseController;
import top.continew.starter.core.util.response.R;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 订阅套餐管理 API
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "订阅套餐管理 API")
@RestController
@RequiredArgsConstructor
@Validated
@CrudRequestMapping(value = "/accounting/subscription/plan", api = {CrudRequestMapping.Api.PAGE, CrudRequestMapping.Api.GET, CrudRequestMapping.Api.ADD, CrudRequestMapping.Api.UPDATE, CrudRequestMapping.Api.DELETE})
public class SubscriptionPlanController extends BaseController<SubscriptionPlanService, SubscriptionPlanListResp, SubscriptionPlanDetailResp, SubscriptionPlanQuery, SubscriptionPlanCreateReq> {

    @Operation(summary = "查询启用的套餐列表", description = "查询所有启用状态的订阅套餐")
    @GetMapping("/enabled")
    public R<List<SubscriptionPlanListResp>> listEnabled() {
        return R.ok(baseService.listEnabledPlans());
    }

    @Operation(summary = "根据代码查询套餐", description = "根据套餐代码查询套餐详情")
    @GetMapping("/code/{code}")
    public R<SubscriptionPlanDetailResp> getByCode(
            @Parameter(description = "套餐代码", example = "PRO") 
            @PathVariable String code) {
        return R.ok(baseService.getByCode(code));
    }

    @Operation(summary = "查询热门套餐", description = "查询标记为热门的订阅套餐")
    @GetMapping("/popular")
    public R<List<SubscriptionPlanListResp>> listPopular() {
        return R.ok(baseService.listPopularPlans());
    }

    @Operation(summary = "更新套餐", description = "更新订阅套餐信息")
    @PutMapping("/{id}")
    public R<Void> update(
            @Parameter(description = "套餐ID", example = "1") 
            @PathVariable Long id,
            @Valid @RequestBody SubscriptionPlanUpdateReq req) {
        baseService.update(req, id);
        return R.ok();
    }

    @Operation(summary = "启用套餐", description = "启用指定的订阅套餐")
    @PutMapping("/{id}/enable")
    public R<Void> enable(
            @Parameter(description = "套餐ID", example = "1") 
            @PathVariable Long id) {
        baseService.enable(id);
        return R.ok();
    }

    @Operation(summary = "禁用套餐", description = "禁用指定的订阅套餐")
    @PutMapping("/{id}/disable")
    public R<Void> disable(
            @Parameter(description = "套餐ID", example = "1") 
            @PathVariable Long id) {
        baseService.disable(id);
        return R.ok();
    }

    @Operation(summary = "批量更新排序", description = "批量更新套餐排序")
    @PutMapping("/sort")
    public R<Void> updateSortOrder(
            @Parameter(description = "套餐ID列表") 
            @RequestBody @NotNull List<Long> ids) {
        baseService.updateSortOrder(ids);
        return R.ok();
    }

    @Operation(summary = "检查套餐是否存在", description = "检查指定ID的套餐是否存在")
    @GetMapping("/{id}/exists")
    public R<Boolean> exists(
            @Parameter(description = "套餐ID", example = "1") 
            @PathVariable Long id) {
        return R.ok(baseService.exists(id));
    }

    @Operation(summary = "检查套餐代码是否存在", description = "检查套餐代码是否已被使用")
    @GetMapping("/code/{code}/exists")
    public R<Boolean> existsByCode(
            @Parameter(description = "套餐代码", example = "PRO") 
            @PathVariable String code,
            @Parameter(description = "排除的套餐ID", example = "1") 
            @RequestParam(required = false) Long excludeId) {
        return R.ok(baseService.existsByCode(code, excludeId));
    }

    @Operation(summary = "计算套餐价格", description = "根据套餐和计费周期计算价格")
    @GetMapping("/{planId}/price")
    public R<BigDecimal> calculatePrice(
            @Parameter(description = "套餐ID", example = "1") 
            @PathVariable Long planId,
            @Parameter(description = "计费周期", example = "MONTHLY") 
            @RequestParam String billingCycle) {
        return R.ok(baseService.getPlanPrice(planId, billingCycle));
    }

    @Operation(summary = "验证套餐功能", description = "验证套餐是否包含指定功能")
    @GetMapping("/{planId}/feature/{featureName}")
    public R<Boolean> validateFeature(
            @Parameter(description = "套餐ID", example = "1") 
            @PathVariable Long planId,
            @Parameter(description = "功能名称", example = "advancedReports") 
            @PathVariable String featureName) {
        return R.ok(baseService.validatePlanFeature(planId, featureName));
    }

    @Operation(summary = "获取套餐限制", description = "获取套餐的指定限制值")
    @GetMapping("/{planId}/limit/{limitName}")
    public R<Integer> getPlanLimit(
            @Parameter(description = "套餐ID", example = "1") 
            @PathVariable Long planId,
            @Parameter(description = "限制名称", example = "maxTransactionsPerMonth") 
            @PathVariable String limitName) {
        return R.ok(baseService.getPlanLimit(planId, limitName));
    }
}
