package top.continew.admin.accounting.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.extension.crud.model.query.PageQuery;

import java.time.LocalDateTime;

/**
 * 数据同步日志查询条件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "数据同步日志查询条件")
public class DataSyncLogQuery extends PageQuery {

    /**
     * 配置ID
     */
    @Schema(description = "配置ID", example = "1")
    private Long configId;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 同步ID
     */
    @Schema(description = "同步ID", example = "sync_20250101_120000_001")
    private String syncId;

    /**
     * 同步类型
     */
    @Schema(description = "同步类型", example = "INCREMENTAL")
    private String syncType;

    /**
     * 同步方向
     */
    @Schema(description = "同步方向", example = "TO_TARGET")
    private String syncDirection;

    /**
     * 操作类型
     */
    @Schema(description = "操作类型", example = "CREATE")
    private String operationType;

    /**
     * 数据类型
     */
    @Schema(description = "数据类型", example = "TRANSACTION")
    private String dataType;

    /**
     * 同步状态
     */
    @Schema(description = "同步状态", example = "SUCCESS")
    private String status;

    /**
     * 触发方式
     */
    @Schema(description = "触发方式", example = "MANUAL")
    private String triggerType;

    /**
     * 触发用户ID
     */
    @Schema(description = "触发用户ID", example = "1")
    private Long triggerUserId;

    /**
     * 是否重试
     */
    @Schema(description = "是否重试", example = "false")
    private Boolean isRetry;

    /**
     * 开始时间范围 - 开始
     */
    @Schema(description = "开始时间范围 - 开始", example = "2025-01-01 00:00:00")
    private LocalDateTime startTimeStart;

    /**
     * 开始时间范围 - 结束
     */
    @Schema(description = "开始时间范围 - 结束", example = "2025-01-31 23:59:59")
    private LocalDateTime startTimeEnd;

    /**
     * 结束时间范围 - 开始
     */
    @Schema(description = "结束时间范围 - 开始", example = "2025-01-01 00:00:00")
    private LocalDateTime endTimeStart;

    /**
     * 结束时间范围 - 结束
     */
    @Schema(description = "结束时间范围 - 结束", example = "2025-01-31 23:59:59")
    private LocalDateTime endTimeEnd;

    /**
     * 最小处理记录数
     */
    @Schema(description = "最小处理记录数", example = "100")
    private Integer minRecordsProcessed;

    /**
     * 最大处理记录数
     */
    @Schema(description = "最大处理记录数", example = "1000")
    private Integer maxRecordsProcessed;

    /**
     * 最小执行时长（毫秒）
     */
    @Schema(description = "最小执行时长（毫秒）", example = "1000")
    private Long minDurationMs;

    /**
     * 最大执行时长（毫秒）
     */
    @Schema(description = "最大执行时长（毫秒）", example = "300000")
    private Long maxDurationMs;

    /**
     * 最小成功率
     */
    @Schema(description = "最小成功率", example = "90.0")
    private Double minSuccessRate;

    /**
     * 最大成功率
     */
    @Schema(description = "最大成功率", example = "100.0")
    private Double maxSuccessRate;

    /**
     * 关键词搜索
     */
    @Schema(description = "关键词搜索", example = "error")
    private String keyword;
}
