package top.continew.admin.api.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;

/**
 * API群组更新请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "API群组更新请求")
public class ApiGroupUpdateReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组名称
     */
    @Schema(description = "群组名称", example = "我的记账群组")
    @Size(max = 50, message = "群组名称长度不能超过 {max} 个字符")
    private String name;

    /**
     * 群组描述
     */
    @Schema(description = "群组描述", example = "用于家庭日常开支记录")
    @Size(max = 200, message = "群组描述长度不能超过 {max} 个字符")
    private String description;

    /**
     * 群组头像
     */
    @Schema(description = "群组头像URL", example = "https://example.com/avatar.jpg")
    @Size(max = 500, message = "群组头像URL长度不能超过 {max} 个字符")
    private String avatar;

    /**
     * 默认货币
     */
    @Schema(description = "默认货币", example = "CNY")
    @Size(max = 10, message = "默认货币长度不能超过 {max} 个字符")
    private String defaultCurrency;

    /**
     * 是否公开
     */
    @Schema(description = "是否公开群组", example = "false")
    private Boolean isPublic;

    /**
     * 群组设置
     */
    @Schema(description = "群组设置")
    private ApiGroupCreateReq.GroupSettings settings;
}
