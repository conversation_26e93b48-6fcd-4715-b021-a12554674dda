package top.continew.admin.accounting.model.entity.settings;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 群组设置
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "群组设置")
public class GroupSettings {

    /**
     * 是否启用自动记账
     */
    @Schema(description = "是否启用自动记账")
    private Boolean autoRecording = true;

    /**
     * 是否启用分摊功能
     */
    @Schema(description = "是否启用分摊功能")
    private Boolean enableSplit = true;

    /**
     * 是否启用债务跟踪
     */
    @Schema(description = "是否启用债务跟踪")
    private Boolean enableDebtTracking = true;

    /**
     * 默认分类列表
     */
    @Schema(description = "默认分类列表")
    private List<String> defaultCategories = List.of(
        "餐饮", "交通", "购物", "娱乐", "住宿", "其他"
    );

    /**
     * 自动标签规则
     */
    @Schema(description = "自动标签规则")
    private List<AutoTagRule> autoTagRules;

    /**
     * 通知设置
     */
    @Schema(description = "通知设置")
    private NotificationSettings notifications;

    /**
     * 权限设置
     */
    @Schema(description = "权限设置")
    private PermissionSettings permissions;

    /**
     * 自动标签规则
     */
    @Data
    @Schema(description = "自动标签规则")
    public static class AutoTagRule {
        /**
         * 规则名称
         */
        @Schema(description = "规则名称")
        private String name;

        /**
         * 匹配条件
         */
        @Schema(description = "匹配条件")
        private String condition;

        /**
         * 自动添加的标签
         */
        @Schema(description = "自动添加的标签")
        private List<String> tags;

        /**
         * 是否启用
         */
        @Schema(description = "是否启用")
        private Boolean enabled = true;
    }

    /**
     * 通知设置
     */
    @Data
    @Schema(description = "通知设置")
    public static class NotificationSettings {
        /**
         * 是否启用大额交易通知
         */
        @Schema(description = "是否启用大额交易通知")
        private Boolean largeAmountAlert = true;

        /**
         * 大额交易阈值
         */
        @Schema(description = "大额交易阈值")
        private Double largeAmountThreshold = 1000.0;

        /**
         * 是否启用预算超支通知
         */
        @Schema(description = "是否启用预算超支通知")
        private Boolean budgetExceededAlert = true;

        /**
         * 是否启用每日汇总
         */
        @Schema(description = "是否启用每日汇总")
        private Boolean dailySummary = false;

        /**
         * 每日汇总时间 (HH:mm格式)
         */
        @Schema(description = "每日汇总时间")
        private String dailySummaryTime = "20:00";
    }

    /**
     * 权限设置
     */
    @Data
    @Schema(description = "权限设置")
    public static class PermissionSettings {
        /**
         * 是否允许成员查看所有账单
         */
        @Schema(description = "是否允许成员查看所有账单")
        private Boolean memberCanViewAll = true;

        /**
         * 是否允许成员编辑自己的账单
         */
        @Schema(description = "是否允许成员编辑自己的账单")
        private Boolean memberCanEditOwn = true;

        /**
         * 是否允许成员删除自己的账单
         */
        @Schema(description = "是否允许成员删除自己的账单")
        private Boolean memberCanDeleteOwn = false;

        /**
         * 是否允许成员发起分摊
         */
        @Schema(description = "是否允许成员发起分摊")
        private Boolean memberCanInitiateSplit = true;

        /**
         * 账单编辑时间限制(小时)
         */
        @Schema(description = "账单编辑时间限制(小时)")
        private Integer editTimeLimit = 24;
    }
}
