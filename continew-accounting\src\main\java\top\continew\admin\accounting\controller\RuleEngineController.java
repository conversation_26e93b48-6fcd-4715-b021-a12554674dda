package top.continew.admin.accounting.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.accounting.model.query.RuleEngineQuery;
import top.continew.admin.accounting.model.req.RuleEngineCreateReq;
import top.continew.admin.accounting.model.req.RuleEngineUpdateReq;
import top.continew.admin.accounting.model.req.RuleExecutionReq;
import top.continew.admin.accounting.model.resp.RuleEngineDetailResp;
import top.continew.admin.accounting.model.resp.RuleEngineResp;
import top.continew.admin.accounting.model.resp.RuleExecutionResp;
import top.continew.admin.accounting.service.RuleEngineService;
import top.continew.admin.common.model.resp.LabelValueResp;
import top.continew.admin.common.model.resp.R;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 规则引擎控制器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "规则引擎管理")
@RestController
@RequestMapping("/accounting/rule-engine")
@RequiredArgsConstructor
@Validated
public class RuleEngineController {

    private final RuleEngineService ruleEngineService;

    // ==================== 规则管理 ====================

    @Operation(summary = "创建规则", description = "创建新的规则引擎规则")
    @PostMapping
    public R<Long> createRule(@Valid @RequestBody RuleEngineCreateReq createReq) {
        Long ruleId = ruleEngineService.createRule(createReq);
        return R.ok("创建规则成功", ruleId);
    }

    @Operation(summary = "更新规则", description = "更新规则引擎规则")
    @PutMapping("/{ruleId}")
    public R<Void> updateRule(@Parameter(description = "规则ID") @PathVariable Long ruleId,
                              @Valid @RequestBody RuleEngineUpdateReq updateReq) {
        ruleEngineService.updateRule(ruleId, updateReq);
        return R.ok("更新规则成功");
    }

    @Operation(summary = "删除规则", description = "删除规则引擎规则")
    @DeleteMapping("/{ruleId}")
    public R<Void> deleteRule(@Parameter(description = "规则ID") @PathVariable Long ruleId) {
        ruleEngineService.deleteRule(ruleId);
        return R.ok("删除规则成功");
    }

    @Operation(summary = "批量删除规则", description = "批量删除规则引擎规则")
    @DeleteMapping("/batch")
    public R<Void> deleteRules(@Parameter(description = "规则ID列表") @RequestBody @NotEmpty List<Long> ruleIds) {
        ruleEngineService.deleteRules(ruleIds);
        return R.ok("批量删除规则成功");
    }

    @Operation(summary = "获取规则详情", description = "获取规则引擎规则详情")
    @GetMapping("/{ruleId}")
    public R<RuleEngineDetailResp> getRuleDetail(@Parameter(description = "规则ID") @PathVariable Long ruleId) {
        RuleEngineDetailResp detail = ruleEngineService.getRuleDetail(ruleId);
        return R.ok(detail);
    }

    @Operation(summary = "分页查询规则", description = "分页查询规则引擎规则")
    @GetMapping("/page")
    public R<IPage<RuleEngineResp>> pageRules(@Valid RuleEngineQuery query) {
        IPage<RuleEngineResp> page = ruleEngineService.pageRules(query);
        return R.ok(page);
    }

    @Operation(summary = "列表查询规则", description = "列表查询规则引擎规则")
    @GetMapping("/list")
    public R<List<RuleEngineResp>> listRules(@Valid RuleEngineQuery query) {
        List<RuleEngineResp> list = ruleEngineService.listRules(query);
        return R.ok(list);
    }

    @Operation(summary = "复制规则", description = "复制规则引擎规则")
    @PostMapping("/{ruleId}/copy")
    public R<Long> copyRule(@Parameter(description = "规则ID") @PathVariable Long ruleId,
                            @Parameter(description = "新规则名称") @RequestParam String ruleName) {
        Long newRuleId = ruleEngineService.copyRule(ruleId, ruleName);
        return R.ok("复制规则成功", newRuleId);
    }

    @Operation(summary = "启用/禁用规则", description = "启用或禁用规则引擎规则")
    @PutMapping("/{ruleId}/toggle")
    public R<Void> toggleRuleStatus(@Parameter(description = "规则ID") @PathVariable Long ruleId,
                                    @Parameter(description = "是否启用") @RequestParam Boolean enabled) {
        ruleEngineService.toggleRuleStatus(ruleId, enabled);
        return R.ok(enabled ? "启用规则成功" : "禁用规则成功");
    }

    @Operation(summary = "发布规则", description = "发布规则引擎规则")
    @PutMapping("/{ruleId}/publish")
    public R<Void> publishRule(@Parameter(description = "规则ID") @PathVariable Long ruleId) {
        ruleEngineService.publishRule(ruleId);
        return R.ok("发布规则成功");
    }

    @Operation(summary = "取消发布规则", description = "取消发布规则引擎规则")
    @PutMapping("/{ruleId}/unpublish")
    public R<Void> unpublishRule(@Parameter(description = "规则ID") @PathVariable Long ruleId) {
        ruleEngineService.unpublishRule(ruleId);
        return R.ok("取消发布规则成功");
    }

    // ==================== 规则版本管理 ====================

    @Operation(summary = "创建规则版本", description = "创建规则版本")
    @PostMapping("/{ruleId}/versions")
    public R<String> createRuleVersion(@Parameter(description = "规则ID") @PathVariable Long ruleId,
                                       @Parameter(description = "版本说明") @RequestParam String versionNote) {
        String version = ruleEngineService.createRuleVersion(ruleId, versionNote);
        return R.ok("创建版本成功", version);
    }

    @Operation(summary = "回滚到指定版本", description = "回滚规则到指定版本")
    @PutMapping("/{ruleId}/versions/{version}/rollback")
    public R<Void> rollbackToVersion(@Parameter(description = "规则ID") @PathVariable Long ruleId,
                                     @Parameter(description = "版本号") @PathVariable String version) {
        ruleEngineService.rollbackToVersion(ruleId, version);
        return R.ok("回滚版本成功");
    }

    @Operation(summary = "获取规则版本历史", description = "获取规则版本历史")
    @GetMapping("/{ruleId}/versions")
    public R<List<Map<String, Object>>> getRuleVersionHistory(@Parameter(description = "规则ID") @PathVariable Long ruleId) {
        List<Map<String, Object>> versions = ruleEngineService.getRuleVersionHistory(ruleId);
        return R.ok(versions);
    }

    @Operation(summary = "比较规则版本", description = "比较规则版本差异")
    @GetMapping("/{ruleId}/versions/compare")
    public R<Map<String, Object>> compareRuleVersions(@Parameter(description = "规则ID") @PathVariable Long ruleId,
                                                      @Parameter(description = "版本1") @RequestParam String version1,
                                                      @Parameter(description = "版本2") @RequestParam String version2) {
        Map<String, Object> comparison = ruleEngineService.compareRuleVersions(ruleId, version1, version2);
        return R.ok(comparison);
    }

    // ==================== 规则执行 ====================

    @Operation(summary = "执行规则", description = "同步执行规则")
    @PostMapping("/execute")
    public R<RuleExecutionResp> executeRules(@Valid @RequestBody RuleExecutionReq executionReq) {
        RuleExecutionResp result = ruleEngineService.executeRules(executionReq);
        return R.ok(result);
    }

    @Operation(summary = "异步执行规则", description = "异步执行规则")
    @PostMapping("/execute/async")
    public R<Long> executeRulesAsync(@Valid @RequestBody RuleExecutionReq executionReq) {
        Long executionId = ruleEngineService.executeRulesAsync(executionReq);
        return R.ok("异步执行已启动", executionId);
    }

    @Operation(summary = "获取执行状态", description = "获取规则执行状态")
    @GetMapping("/executions/{executionId}")
    public R<RuleExecutionResp> getExecutionStatus(@Parameter(description = "执行ID") @PathVariable Long executionId) {
        RuleExecutionResp status = ruleEngineService.getExecutionStatus(executionId);
        return R.ok(status);
    }

    @Operation(summary = "取消执行", description = "取消规则执行")
    @PutMapping("/executions/{executionId}/cancel")
    public R<Void> cancelExecution(@Parameter(description = "执行ID") @PathVariable Long executionId) {
        ruleEngineService.cancelExecution(executionId);
        return R.ok("取消执行成功");
    }

    @Operation(summary = "重新执行", description = "重新执行规则")
    @PostMapping("/executions/{executionId}/retry")
    public R<Long> reExecute(@Parameter(description = "执行ID") @PathVariable Long executionId) {
        Long newExecutionId = ruleEngineService.reExecute(executionId);
        return R.ok("重新执行已启动", newExecutionId);
    }

    @Operation(summary = "测试规则", description = "测试规则配置")
    @PostMapping("/{ruleId}/test")
    public R<Map<String, Object>> testRule(@Parameter(description = "规则ID") @PathVariable Long ruleId,
                                           @RequestBody Map<String, Object> testData) {
        Map<String, Object> result = ruleEngineService.testRule(ruleId, testData);
        return R.ok(result);
    }

    @Operation(summary = "验证规则配置", description = "验证规则配置")
    @PostMapping("/{ruleId}/validate")
    public R<Map<String, Object>> validateRuleConfig(@Parameter(description = "规则ID") @PathVariable Long ruleId) {
        Map<String, Object> result = ruleEngineService.validateRuleConfig(ruleId);
        return R.ok(result);
    }

    // ==================== 规则调度 ====================

    @Operation(summary = "启动规则调度", description = "启动规则调度任务")
    @PutMapping("/{ruleId}/schedule/start")
    public R<Void> startRuleSchedule(@Parameter(description = "规则ID") @PathVariable Long ruleId) {
        ruleEngineService.startRuleSchedule(ruleId);
        return R.ok("启动调度成功");
    }

    @Operation(summary = "停止规则调度", description = "停止规则调度任务")
    @PutMapping("/{ruleId}/schedule/stop")
    public R<Void> stopRuleSchedule(@Parameter(description = "规则ID") @PathVariable Long ruleId) {
        ruleEngineService.stopRuleSchedule(ruleId);
        return R.ok("停止调度成功");
    }

    @Operation(summary = "暂停规则调度", description = "暂停规则调度任务")
    @PutMapping("/{ruleId}/schedule/pause")
    public R<Void> pauseRuleSchedule(@Parameter(description = "规则ID") @PathVariable Long ruleId) {
        ruleEngineService.pauseRuleSchedule(ruleId);
        return R.ok("暂停调度成功");
    }

    @Operation(summary = "恢复规则调度", description = "恢复规则调度任务")
    @PutMapping("/{ruleId}/schedule/resume")
    public R<Void> resumeRuleSchedule(@Parameter(description = "规则ID") @PathVariable Long ruleId) {
        ruleEngineService.resumeRuleSchedule(ruleId);
        return R.ok("恢复调度成功");
    }

    @Operation(summary = "立即执行调度任务", description = "立即执行调度任务")
    @PostMapping("/{ruleId}/schedule/trigger")
    public R<Long> triggerScheduleNow(@Parameter(description = "规则ID") @PathVariable Long ruleId) {
        Long executionId = ruleEngineService.triggerScheduleNow(ruleId);
        return R.ok("立即执行已启动", executionId);
    }

    @Operation(summary = "获取下次执行时间", description = "获取规则下次执行时间")
    @GetMapping("/{ruleId}/schedule/next-times")
    public R<List<String>> getNextExecutionTimes(@Parameter(description = "规则ID") @PathVariable Long ruleId,
                                                 @Parameter(description = "获取数量") @RequestParam(defaultValue = "5") Integer count) {
        List<String> times = ruleEngineService.getNextExecutionTimes(ruleId, count);
        return R.ok(times);
    }

    // ==================== 统计分析 ====================

    @Operation(summary = "获取规则执行统计", description = "获取规则执行统计")
    @GetMapping("/{ruleId}/stats/execution")
    public R<Map<String, Object>> getRuleExecutionStats(@Parameter(description = "规则ID") @PathVariable Long ruleId,
                                                        @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days) {
        Map<String, Object> stats = ruleEngineService.getRuleExecutionStats(ruleId, days);
        return R.ok(stats);
    }

    @Operation(summary = "获取规则性能统计", description = "获取规则性能统计")
    @GetMapping("/{ruleId}/stats/performance")
    public R<Map<String, Object>> getRulePerformanceStats(@Parameter(description = "规则ID") @PathVariable Long ruleId,
                                                          @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days) {
        Map<String, Object> stats = ruleEngineService.getRulePerformanceStats(ruleId, days);
        return R.ok(stats);
    }

    @Operation(summary = "获取规则执行趋势", description = "获取规则执行趋势")
    @GetMapping("/{ruleId}/stats/trend")
    public R<List<Map<String, Object>>> getRuleExecutionTrend(@Parameter(description = "规则ID") @PathVariable Long ruleId,
                                                              @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days) {
        List<Map<String, Object>> trend = ruleEngineService.getRuleExecutionTrend(ruleId, days);
        return R.ok(trend);
    }

    @Operation(summary = "获取热门规则排行", description = "获取热门规则排行")
    @GetMapping("/stats/popular")
    public R<List<Map<String, Object>>> getPopularRules(@Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days,
                                                        @Parameter(description = "返回数量") @RequestParam(defaultValue = "10") Integer limit) {
        List<Map<String, Object>> rules = ruleEngineService.getPopularRules(null, days, limit);
        return R.ok(rules);
    }

    @Operation(summary = "获取规则类型统计", description = "获取规则类型统计")
    @GetMapping("/stats/types")
    public R<List<Map<String, Object>>> getRuleTypeStats() {
        List<Map<String, Object>> stats = ruleEngineService.getRuleTypeStats(null);
        return R.ok(stats);
    }

    @Operation(summary = "获取规则执行历史", description = "获取规则执行历史")
    @GetMapping("/{ruleId}/history")
    public R<List<Map<String, Object>>> getRuleExecutionHistory(@Parameter(description = "规则ID") @PathVariable Long ruleId,
                                                                @Parameter(description = "返回数量") @RequestParam(defaultValue = "20") Integer limit) {
        List<Map<String, Object>> history = ruleEngineService.getRuleExecutionHistory(ruleId, limit);
        return R.ok(history);
    }

    @Operation(summary = "获取群组规则统计", description = "获取群组规则统计")
    @GetMapping("/stats/group")
    public R<Map<String, Object>> getGroupRuleStats() {
        Map<String, Object> stats = ruleEngineService.getGroupRuleStats(null);
        return R.ok(stats);
    }

    @Operation(summary = "获取系统规则统计", description = "获取系统规则统计")
    @GetMapping("/stats/system")
    public R<Map<String, Object>> getSystemRuleStats() {
        Map<String, Object> stats = ruleEngineService.getSystemRuleStats();
        return R.ok(stats);
    }

    // ==================== 规则依赖和冲突 ====================

    @Operation(summary = "分析规则依赖", description = "分析规则依赖关系")
    @GetMapping("/{ruleId}/dependencies")
    public R<Map<String, Object>> analyzeRuleDependencies(@Parameter(description = "规则ID") @PathVariable Long ruleId) {
        Map<String, Object> dependencies = ruleEngineService.analyzeRuleDependencies(ruleId);
        return R.ok(dependencies);
    }

    @Operation(summary = "检测规则冲突", description = "检测规则冲突")
    @GetMapping("/{ruleId}/conflicts")
    public R<List<Map<String, Object>>> detectRuleConflicts(@Parameter(description = "规则ID") @PathVariable Long ruleId) {
        List<Map<String, Object>> conflicts = ruleEngineService.detectRuleConflicts(ruleId);
        return R.ok(conflicts);
    }

    @Operation(summary = "获取规则影响分析", description = "获取规则影响分析")
    @GetMapping("/{ruleId}/impact")
    public R<Map<String, Object>> getRuleImpactAnalysis(@Parameter(description = "规则ID") @PathVariable Long ruleId) {
        Map<String, Object> impact = ruleEngineService.getRuleImpactAnalysis(ruleId);
        return R.ok(impact);
    }

    // ==================== 规则模板和预设 ====================

    @Operation(summary = "获取规则模板列表", description = "获取规则模板列表")
    @GetMapping("/templates")
    public R<List<Map<String, Object>>> getRuleTemplates(@Parameter(description = "规则类型") @RequestParam(required = false) String ruleType) {
        List<Map<String, Object>> templates = ruleEngineService.getRuleTemplates(ruleType);
        return R.ok(templates);
    }

    @Operation(summary = "从模板创建规则", description = "从模板创建规则")
    @PostMapping("/templates/{templateId}/create")
    public R<Long> createRuleFromTemplate(@Parameter(description = "模板ID") @PathVariable Long templateId,
                                          @Parameter(description = "规则名称") @RequestParam String ruleName,
                                          @RequestBody(required = false) Map<String, Object> customParams) {
        Long ruleId = ruleEngineService.createRuleFromTemplate(templateId, ruleName, customParams);
        return R.ok("从模板创建规则成功", ruleId);
    }

    @Operation(summary = "保存为模板", description = "将规则保存为模板")
    @PostMapping("/{ruleId}/save-as-template")
    public R<Long> saveAsTemplate(@Parameter(description = "规则ID") @PathVariable Long ruleId,
                                  @Parameter(description = "模板名称") @RequestParam String templateName) {
        Long templateId = ruleEngineService.saveAsTemplate(ruleId, templateName);
        return R.ok("保存为模板成功", templateId);
    }

    // ==================== 辅助接口 ====================

    @Operation(summary = "获取规则类型选项", description = "获取规则类型选项")
    @GetMapping("/options/rule-types")
    public R<List<LabelValueResp<String>>> getRuleTypeOptions() {
        List<LabelValueResp<String>> options = ruleEngineService.getRuleTypeOptions();
        return R.ok(options);
    }

    @Operation(summary = "获取触发类型选项", description = "获取触发类型选项")
    @GetMapping("/options/trigger-types")
    public R<List<LabelValueResp<String>>> getTriggerTypeOptions() {
        List<LabelValueResp<String>> options = ruleEngineService.getTriggerTypeOptions();
        return R.ok(options);
    }

    @Operation(summary = "获取事件类型选项", description = "获取事件类型选项")
    @GetMapping("/options/event-types")
    public R<List<LabelValueResp<String>>> getEventTypeOptions(@Parameter(description = "触发类型") @RequestParam String triggerType) {
        List<LabelValueResp<String>> options = ruleEngineService.getEventTypeOptions(triggerType);
        return R.ok(options);
    }

    @Operation(summary = "获取动作类型选项", description = "获取动作类型选项")
    @GetMapping("/options/action-types")
    public R<List<LabelValueResp<String>>> getActionTypeOptions(@Parameter(description = "规则类型") @RequestParam String ruleType) {
        List<LabelValueResp<String>> options = ruleEngineService.getActionTypeOptions(ruleType);
        return R.ok(options);
    }

    @Operation(summary = "获取操作符选项", description = "获取操作符选项")
    @GetMapping("/options/operators")
    public R<List<LabelValueResp<String>>> getOperatorOptions(@Parameter(description = "数据类型") @RequestParam String dataType) {
        List<LabelValueResp<String>> options = ruleEngineService.getOperatorOptions(dataType);
        return R.ok(options);
    }

    @Operation(summary = "获取字段选项", description = "获取字段选项")
    @GetMapping("/options/fields")
    public R<List<LabelValueResp<String>>> getFieldOptions(@Parameter(description = "实体类型") @RequestParam String entityType) {
        List<LabelValueResp<String>> options = ruleEngineService.getFieldOptions(entityType);
        return R.ok(options);
    }

    @Operation(summary = "检查规则名称是否存在", description = "检查规则名称是否存在")
    @GetMapping("/check-name")
    public R<Boolean> checkRuleName(@Parameter(description = "规则名称") @RequestParam String ruleName,
                                    @Parameter(description = "排除的规则ID") @RequestParam(required = false) Long excludeId) {
        Boolean exists = ruleEngineService.existsByName(ruleName, null, excludeId);
        return R.ok(exists);
    }
}
