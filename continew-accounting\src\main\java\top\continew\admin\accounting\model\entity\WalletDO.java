package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.data.mybatis.plus.base.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 钱包实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("acc_wallet")
@Schema(description = "钱包信息")
public class WalletDO extends BaseEntity {

    /**
     * 群组ID
     */
    @Schema(description = "群组ID")
    private Long groupId;

    /**
     * 币种
     */
    @Schema(description = "币种")
    private String currency;

    /**
     * 余额
     */
    @Schema(description = "余额")
    private BigDecimal balance;

    /**
     * 冻结金额
     */
    @Schema(description = "冻结金额")
    private BigDecimal frozenAmount;

    /**
     * 最后更新时间
     */
    @Schema(description = "最后更新时间")
    private LocalDateTime lastUpdateTime;
}
