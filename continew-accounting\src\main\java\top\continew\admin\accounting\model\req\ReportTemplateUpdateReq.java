package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 报表模板更新请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "报表模板更新请求")
public class ReportTemplateUpdateReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模板名称
     */
    @Schema(description = "模板名称", example = "月度财务报表")
    @Size(max = 100, message = "模板名称长度不能超过100个字符")
    private String templateName;

    /**
     * 模板描述
     */
    @Schema(description = "模板描述", example = "用于生成月度收支统计报表")
    @Size(max = 500, message = "模板描述长度不能超过500个字符")
    private String templateDescription;

    /**
     * 模板类型
     */
    @Schema(description = "模板类型", example = "FINANCIAL_OVERVIEW", allowableValues = {"FINANCIAL_OVERVIEW", "CATEGORY_ANALYSIS", "MEMBER_ANALYSIS", "TREND_ANALYSIS", "CUSTOM_REPORT"})
    private String templateType;

    /**
     * 报表配置
     */
    @Schema(description = "报表配置")
    private ReportTemplateCreateReq.ReportConfiguration reportConfig;

    /**
     * 数据源配置
     */
    @Schema(description = "数据源配置")
    private ReportTemplateCreateReq.DataSourceConfiguration dataSourceConfig;

    /**
     * 布局配置
     */
    @Schema(description = "布局配置")
    private ReportTemplateCreateReq.LayoutConfiguration layoutConfig;

    /**
     * 图表配置
     */
    @Schema(description = "图表配置")
    private List<ReportTemplateCreateReq.ChartConfiguration> chartConfigs;

    /**
     * 过滤器配置
     */
    @Schema(description = "过滤器配置")
    private List<ReportTemplateCreateReq.FilterConfiguration> filterConfigs;

    /**
     * 导出配置
     */
    @Schema(description = "导出配置")
    private ReportTemplateCreateReq.ExportConfiguration exportConfig;

    /**
     * 调度配置
     */
    @Schema(description = "调度配置")
    private ReportTemplateCreateReq.ScheduleConfiguration scheduleConfig;

    /**
     * 权限配置
     */
    @Schema(description = "权限配置")
    private ReportTemplateCreateReq.PermissionConfiguration permissionConfig;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    /**
     * 是否公开
     */
    @Schema(description = "是否公开", example = "false")
    private Boolean isPublic;

    /**
     * 标签列表
     */
    @Schema(description = "标签列表", example = "[\"财务\", \"月报\"]")
    private List<String> tags;

    /**
     * 扩展属性
     */
    @Schema(description = "扩展属性")
    private Map<String, Object> attributes;

    /**
     * 更新原因
     */
    @Schema(description = "更新原因", example = "优化报表布局")
    @Size(max = 200, message = "更新原因长度不能超过200个字符")
    private String updateReason;

    /**
     * 版本说明
     */
    @Schema(description = "版本说明", example = "v2.1 - 新增成本分析图表")
    @Size(max = 100, message = "版本说明长度不能超过100个字符")
    private String versionNote;
}
