package top.continew.admin.accounting.model.entity.split;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.accounting.enums.SplitType;

import java.math.BigDecimal;
import java.util.List;

/**
 * 分摊信息
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "分摊信息")
public class SplitInfo {

    /**
     * 分摊类型
     */
    @Schema(description = "分摊类型")
    private SplitType splitType;

    /**
     * 分摊参与者列表
     */
    @Schema(description = "分摊参与者列表")
    private List<SplitParticipant> participants;

    /**
     * 是否已完成分摊
     */
    @Schema(description = "是否已完成分摊")
    private Boolean completed = false;

    /**
     * 分摊创建时间
     */
    @Schema(description = "分摊创建时间")
    private String createdAt;

    /**
     * 分摊完成时间
     */
    @Schema(description = "分摊完成时间")
    private String completedAt;

    /**
     * 分摊参与者
     */
    @Data
    @Schema(description = "分摊参与者")
    public static class SplitParticipant {
        /**
         * 用户ID
         */
        @Schema(description = "用户ID")
        private Long userId;

        /**
         * 用户昵称
         */
        @Schema(description = "用户昵称")
        private String nickname;

        /**
         * 分摊金额
         */
        @Schema(description = "分摊金额")
        private BigDecimal amount;

        /**
         * 分摊比例 (仅在按比例分摊时使用)
         */
        @Schema(description = "分摊比例")
        private BigDecimal ratio;

        /**
         * 是否已确认
         */
        @Schema(description = "是否已确认")
        private Boolean confirmed = false;

        /**
         * 确认时间
         */
        @Schema(description = "确认时间")
        private String confirmedAt;

        /**
         * 是否已支付
         */
        @Schema(description = "是否已支付")
        private Boolean paid = false;

        /**
         * 支付时间
         */
        @Schema(description = "支付时间")
        private String paidAt;
    }
}
