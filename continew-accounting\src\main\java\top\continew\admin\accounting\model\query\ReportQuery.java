package top.continew.admin.accounting.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 报表查询条件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "报表查询条件")
public class ReportQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 开始日期
     */
    @Schema(description = "开始日期", example = "2025-01-01")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @Schema(description = "结束日期", example = "2025-01-31")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型", example = "EXPENSE")
    private String type;

    /**
     * 分类ID列表
     */
    @Schema(description = "分类ID列表")
    private List<Long> categoryIds;

    /**
     * 标签列表
     */
    @Schema(description = "标签列表")
    private List<String> tags;

    /**
     * 钱包ID列表
     */
    @Schema(description = "钱包ID列表")
    private List<Long> walletIds;

    /**
     * 成员ID列表
     */
    @Schema(description = "成员ID列表")
    private List<Long> memberIds;

    /**
     * 币种
     */
    @Schema(description = "币种", example = "CNY")
    private String currency;

    /**
     * 统计维度
     */
    @Schema(description = "统计维度", example = "DAILY")
    private String dimension;

    /**
     * 分组字段
     */
    @Schema(description = "分组字段", example = "category")
    private String groupBy;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段", example = "amount")
    private String orderBy;

    /**
     * 排序方向
     */
    @Schema(description = "排序方向", example = "DESC")
    private String orderDirection;

    /**
     * 限制数量
     */
    @Schema(description = "限制数量", example = "10")
    private Integer limit;

    /**
     * 是否包含子分类
     */
    @Schema(description = "是否包含子分类", example = "true")
    private Boolean includeSubCategories;

    /**
     * 最小金额
     */
    @Schema(description = "最小金额", example = "0.01")
    private java.math.BigDecimal minAmount;

    /**
     * 最大金额
     */
    @Schema(description = "最大金额", example = "10000.00")
    private java.math.BigDecimal maxAmount;
}
