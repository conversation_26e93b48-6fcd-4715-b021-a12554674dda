package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 数据钻取分析响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "数据钻取分析响应")
public class DrillDownAnalysisResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 钻取ID
     */
    @Schema(description = "钻取ID", example = "drill_123456")
    private String drillId;

    /**
     * 当前级别
     */
    @Schema(description = "当前级别", example = "2")
    private Integer currentLevel;

    /**
     * 钻取路径
     */
    @Schema(description = "钻取路径")
    private List<DrillPathItem> drillPath;

    /**
     * 当前数据
     */
    @Schema(description = "当前数据")
    private List<DrillDataItem> currentData;

    /**
     * 面包屑导航
     */
    @Schema(description = "面包屑导航")
    private List<BreadcrumbItem> breadcrumbs;

    /**
     * 可钻取项
     */
    @Schema(description = "可钻取项")
    private List<DrillableItem> drillableItems;

    /**
     * 汇总信息
     */
    @Schema(description = "汇总信息")
    private DrillSummary summary;

    /**
     * 上下文信息
     */
    @Schema(description = "上下文信息")
    private Map<String, Object> context;

    /**
     * 生成时间
     */
    @Schema(description = "生成时间")
    private LocalDateTime generatedAt;

    /**
     * 钻取路径项
     */
    @Data
    @Schema(description = "钻取路径项")
    public static class DrillPathItem implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 级别
         */
        @Schema(description = "级别", example = "1")
        private Integer level;

        /**
         * 维度名称
         */
        @Schema(description = "维度名称", example = "category")
        private String dimension;

        /**
         * 维度显示名称
         */
        @Schema(description = "维度显示名称", example = "分类")
        private String dimensionDisplayName;

        /**
         * 值
         */
        @Schema(description = "值", example = "餐饮")
        private String value;

        /**
         * 显示值
         */
        @Schema(description = "显示值", example = "餐饮消费")
        private String displayValue;

        /**
         * 过滤条件
         */
        @Schema(description = "过滤条件")
        private Map<String, Object> filters;
    }

    /**
     * 钻取数据项
     */
    @Data
    @Schema(description = "钻取数据项")
    public static class DrillDataItem implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 项目ID
         */
        @Schema(description = "项目ID", example = "item_001")
        private String itemId;

        /**
         * 项目名称
         */
        @Schema(description = "项目名称", example = "快餐")
        private String itemName;

        /**
         * 项目值
         */
        @Schema(description = "项目值", example = "1250.50")
        private BigDecimal itemValue;

        /**
         * 百分比
         */
        @Schema(description = "百分比", example = "25.5")
        private Double percentage;

        /**
         * 排名
         */
        @Schema(description = "排名", example = "1")
        private Integer rank;

        /**
         * 是否可钻取
         */
        @Schema(description = "是否可钻取", example = "true")
        private Boolean drillable;

        /**
         * 子项数量
         */
        @Schema(description = "子项数量", example = "5")
        private Integer childCount;

        /**
         * 扩展属性
         */
        @Schema(description = "扩展属性")
        private Map<String, Object> attributes;
    }

    /**
     * 面包屑项
     */
    @Data
    @Schema(description = "面包屑项")
    public static class BreadcrumbItem implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 名称
         */
        @Schema(description = "名称", example = "全部")
        private String name;

        /**
         * 值
         */
        @Schema(description = "值", example = "all")
        private String value;

        /**
         * 级别
         */
        @Schema(description = "级别", example = "0")
        private Integer level;

        /**
         * 是否可点击
         */
        @Schema(description = "是否可点击", example = "true")
        private Boolean clickable;

        /**
         * 钻取参数
         */
        @Schema(description = "钻取参数")
        private Map<String, Object> drillParams;
    }

    /**
     * 可钻取项
     */
    @Data
    @Schema(description = "可钻取项")
    public static class DrillableItem implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 维度名称
         */
        @Schema(description = "维度名称", example = "subcategory")
        private String dimension;

        /**
         * 维度显示名称
         */
        @Schema(description = "维度显示名称", example = "子分类")
        private String dimensionDisplayName;

        /**
         * 钻取方向
         */
        @Schema(description = "钻取方向", example = "DOWN")
        private String direction;

        /**
         * 是否可用
         */
        @Schema(description = "是否可用", example = "true")
        private Boolean available;

        /**
         * 预期数据量
         */
        @Schema(description = "预期数据量", example = "15")
        private Integer expectedDataCount;
    }

    /**
     * 钻取汇总
     */
    @Data
    @Schema(description = "钻取汇总")
    public static class DrillSummary implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 总计
         */
        @Schema(description = "总计", example = "5000.00")
        private BigDecimal total;

        /**
         * 项目数量
         */
        @Schema(description = "项目数量", example = "20")
        private Integer itemCount;

        /**
         * 平均值
         */
        @Schema(description = "平均值", example = "250.00")
        private BigDecimal average;

        /**
         * 最大值
         */
        @Schema(description = "最大值", example = "800.00")
        private BigDecimal maximum;

        /**
         * 最小值
         */
        @Schema(description = "最小值", example = "50.00")
        private BigDecimal minimum;

        /**
         * 标准差
         */
        @Schema(description = "标准差", example = "125.50")
        private Double standardDeviation;

        /**
         * 变异系数
         */
        @Schema(description = "变异系数", example = "0.502")
        private Double coefficientOfVariation;
    }
}
