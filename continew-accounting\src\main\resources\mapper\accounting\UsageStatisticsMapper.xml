<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.accounting.mapper.UsageStatisticsMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="top.continew.admin.accounting.model.entity.UsageStatisticsDO">
        <id column="id" property="id" />
        <result column="group_id" property="groupId" />
        <result column="stat_date" property="statDate" />
        <result column="transaction_count" property="transactionCount" />
        <result column="ocr_count" property="ocrCount" />
        <result column="api_calls" property="apiCalls" />
        <result column="storage_used" property="storageUsed" />
        <result column="export_count" property="exportCount" />
        <result column="webhook_calls" property="webhookCalls" />
        <result column="active_users" property="activeUsers" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, group_id, stat_date, transaction_count, ocr_count, api_calls, storage_used,
        export_count, webhook_calls, active_users, create_time, update_time
    </sql>

    <!-- 根据群组和日期查询使用记录 -->
    <select id="selectByGroupAndDate" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM acc_usage_statistics 
        WHERE group_id = #{groupId} 
        AND stat_date = #{statDate}
    </select>

    <!-- 查询月度使用量 -->
    <select id="selectMonthlyUsage" resultMap="BaseResultMap">
        SELECT 
            #{groupId} AS group_id,
            #{startDate} AS stat_date,
            COALESCE(SUM(transaction_count), 0) AS transaction_count,
            COALESCE(SUM(ocr_count), 0) AS ocr_count,
            COALESCE(SUM(api_calls), 0) AS api_calls,
            COALESCE(SUM(storage_used), 0) AS storage_used,
            COALESCE(SUM(export_count), 0) AS export_count,
            COALESCE(SUM(webhook_calls), 0) AS webhook_calls,
            COALESCE(AVG(active_users), 0) AS active_users,
            NOW() AS create_time,
            NOW() AS update_time
        FROM acc_usage_statistics 
        WHERE group_id = #{groupId} 
        AND stat_date >= #{startDate} 
        AND stat_date <= #{endDate}
    </select>

    <!-- 查询使用趋势 -->
    <select id="selectUsageTrend" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM acc_usage_statistics 
        WHERE group_id = #{groupId} 
        AND stat_date >= #{startDate} 
        AND stat_date <= #{endDate}
        ORDER BY stat_date ASC
    </select>

    <!-- 查询总使用量 -->
    <select id="selectTotalUsage" resultMap="BaseResultMap">
        SELECT 
            #{groupId} AS group_id,
            #{startDate} AS stat_date,
            COALESCE(SUM(transaction_count), 0) AS transaction_count,
            COALESCE(SUM(ocr_count), 0) AS ocr_count,
            COALESCE(SUM(api_calls), 0) AS api_calls,
            COALESCE(SUM(storage_used), 0) AS storage_used,
            COALESCE(SUM(export_count), 0) AS export_count,
            COALESCE(SUM(webhook_calls), 0) AS webhook_calls,
            COALESCE(AVG(active_users), 0) AS active_users,
            NOW() AS create_time,
            NOW() AS update_time
        FROM acc_usage_statistics 
        WHERE group_id = #{groupId} 
        AND stat_date >= #{startDate} 
        AND stat_date <= #{endDate}
    </select>

    <!-- 查询超限群组 -->
    <select id="selectOverLimitGroups" parameterType="string" resultType="long">
        SELECT DISTINCT us.group_id
        FROM acc_usage_statistics us
        INNER JOIN acc_subscription s ON us.group_id = s.group_id
        INNER JOIN acc_subscription_plan p ON s.plan_id = p.id
        WHERE s.status = 'ACTIVE' 
        AND s.end_date > NOW()
        AND us.stat_date >= DATE_FORMAT(NOW(), '%Y-%m-01')
        AND (
            <choose>
                <when test="limitType == 'maxTransactionsPerMonth'">
                    us.transaction_count >= CAST(JSON_UNQUOTE(JSON_EXTRACT(p.limits, '$.maxTransactionsPerMonth')) AS UNSIGNED)
                </when>
                <when test="limitType == 'maxOcrPerMonth'">
                    us.ocr_count >= CAST(JSON_UNQUOTE(JSON_EXTRACT(p.limits, '$.maxOcrPerMonth')) AS UNSIGNED)
                </when>
                <when test="limitType == 'maxApiCallsPerMonth'">
                    us.api_calls >= CAST(JSON_UNQUOTE(JSON_EXTRACT(p.limits, '$.maxApiCallsPerMonth')) AS UNSIGNED)
                </when>
                <when test="limitType == 'maxExportsPerMonth'">
                    us.export_count >= CAST(JSON_UNQUOTE(JSON_EXTRACT(p.limits, '$.maxExportsPerMonth')) AS UNSIGNED)
                </when>
                <when test="limitType == 'maxWebhooksPerMonth'">
                    us.webhook_calls >= CAST(JSON_UNQUOTE(JSON_EXTRACT(p.limits, '$.maxWebhooksPerMonth')) AS UNSIGNED)
                </when>
                <otherwise>
                    1 = 0
                </otherwise>
            </choose>
        )
    </select>

    <!-- 重置月度统计 -->
    <update id="resetMonthlyStatistics" parameterType="date">
        UPDATE acc_usage_statistics 
        SET transaction_count = 0,
            ocr_count = 0,
            api_calls = 0,
            storage_used = 0,
            export_count = 0,
            webhook_calls = 0,
            active_users = 0,
            update_time = NOW()
        WHERE stat_date >= DATE_FORMAT(#{lastMonth}, '%Y-%m-01')
        AND stat_date < DATE_FORMAT(NOW(), '%Y-%m-01')
    </update>

    <!-- 删除过期统计数据 -->
    <delete id="deleteExpiredStatistics" parameterType="date">
        DELETE FROM acc_usage_statistics 
        WHERE stat_date < #{cutoffDate}
    </delete>

    <!-- 查询群组使用排行 -->
    <select id="selectGroupUsageRanking" resultType="map">
        SELECT 
            us.group_id,
            g.name AS groupName,
            SUM(us.transaction_count) AS totalTransactions,
            SUM(us.ocr_count) AS totalOcrUsage,
            SUM(us.api_calls) AS totalApiCalls,
            SUM(us.storage_used) AS totalStorageUsed,
            SUM(us.export_count) AS totalExports,
            SUM(us.webhook_calls) AS totalWebhookCalls,
            AVG(us.active_users) AS avgActiveUsers
        FROM acc_usage_statistics us
        LEFT JOIN acc_group g ON us.group_id = g.id
        WHERE us.stat_date >= #{startDate} 
        AND us.stat_date <= #{endDate}
        GROUP BY us.group_id, g.name
        ORDER BY 
        <choose>
            <when test="orderBy == 'transactions'">totalTransactions DESC</when>
            <when test="orderBy == 'ocr'">totalOcrUsage DESC</when>
            <when test="orderBy == 'api'">totalApiCalls DESC</when>
            <when test="orderBy == 'storage'">totalStorageUsed DESC</when>
            <when test="orderBy == 'exports'">totalExports DESC</when>
            <when test="orderBy == 'webhooks'">totalWebhookCalls DESC</when>
            <when test="orderBy == 'users'">avgActiveUsers DESC</when>
            <otherwise>totalTransactions DESC</otherwise>
        </choose>
        LIMIT #{limit}
    </select>

    <!-- 查询使用量热力图数据 -->
    <select id="selectUsageHeatmapData" resultType="map">
        SELECT 
            stat_date,
            SUM(transaction_count) AS transactionCount,
            SUM(ocr_count) AS ocrCount,
            SUM(api_calls) AS apiCalls,
            SUM(export_count) AS exportCount,
            SUM(webhook_calls) AS webhookCalls,
            AVG(active_users) AS avgActiveUsers
        FROM acc_usage_statistics 
        WHERE stat_date >= #{startDate} 
        AND stat_date <= #{endDate}
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        GROUP BY stat_date
        ORDER BY stat_date ASC
    </select>

    <!-- 查询使用量预测数据 -->
    <select id="selectUsageForecastData" resultType="map">
        SELECT 
            stat_date,
            transaction_count,
            ocr_count,
            api_calls,
            storage_used,
            export_count,
            webhook_calls,
            active_users,
            -- 计算7天移动平均
            AVG(transaction_count) OVER (ORDER BY stat_date ROWS BETWEEN 6 PRECEDING AND CURRENT ROW) AS transactionAvg7d,
            AVG(ocr_count) OVER (ORDER BY stat_date ROWS BETWEEN 6 PRECEDING AND CURRENT ROW) AS ocrAvg7d,
            AVG(api_calls) OVER (ORDER BY stat_date ROWS BETWEEN 6 PRECEDING AND CURRENT ROW) AS apiAvg7d,
            -- 计算增长率
            LAG(transaction_count, 1) OVER (ORDER BY stat_date) AS prevTransactionCount,
            LAG(ocr_count, 1) OVER (ORDER BY stat_date) AS prevOcrCount,
            LAG(api_calls, 1) OVER (ORDER BY stat_date) AS prevApiCalls
        FROM acc_usage_statistics 
        WHERE group_id = #{groupId} 
        AND stat_date >= #{startDate} 
        AND stat_date <= #{endDate}
        ORDER BY stat_date ASC
    </select>

    <!-- 查询异常使用检测 -->
    <select id="selectAnomalousUsage" resultType="map">
        SELECT 
            us.group_id,
            g.name AS groupName,
            us.stat_date,
            us.transaction_count,
            us.ocr_count,
            us.api_calls,
            us.storage_used,
            -- 计算与平均值的偏差
            ABS(us.transaction_count - avg_stats.avg_transaction_count) AS transaction_deviation,
            ABS(us.ocr_count - avg_stats.avg_ocr_count) AS ocr_deviation,
            ABS(us.api_calls - avg_stats.avg_api_calls) AS api_deviation,
            -- 计算偏差倍数
            CASE 
                WHEN avg_stats.avg_transaction_count > 0 
                THEN us.transaction_count / avg_stats.avg_transaction_count 
                ELSE 0 
            END AS transaction_ratio,
            CASE 
                WHEN avg_stats.avg_ocr_count > 0 
                THEN us.ocr_count / avg_stats.avg_ocr_count 
                ELSE 0 
            END AS ocr_ratio,
            CASE 
                WHEN avg_stats.avg_api_calls > 0 
                THEN us.api_calls / avg_stats.avg_api_calls 
                ELSE 0 
            END AS api_ratio
        FROM acc_usage_statistics us
        LEFT JOIN acc_group g ON us.group_id = g.id
        INNER JOIN (
            SELECT 
                group_id,
                AVG(transaction_count) AS avg_transaction_count,
                AVG(ocr_count) AS avg_ocr_count,
                AVG(api_calls) AS avg_api_calls,
                STDDEV(transaction_count) AS std_transaction_count,
                STDDEV(ocr_count) AS std_ocr_count,
                STDDEV(api_calls) AS std_api_calls
            FROM acc_usage_statistics 
            WHERE stat_date >= DATE_SUB(#{checkDate}, INTERVAL 30 DAY)
            AND stat_date < #{checkDate}
            GROUP BY group_id
        ) avg_stats ON us.group_id = avg_stats.group_id
        WHERE us.stat_date = #{checkDate}
        AND (
            us.transaction_count > avg_stats.avg_transaction_count + 2 * avg_stats.std_transaction_count
            OR us.ocr_count > avg_stats.avg_ocr_count + 2 * avg_stats.std_ocr_count
            OR us.api_calls > avg_stats.avg_api_calls + 2 * avg_stats.std_api_calls
        )
        ORDER BY 
            GREATEST(
                COALESCE(transaction_ratio, 0),
                COALESCE(ocr_ratio, 0),
                COALESCE(api_ratio, 0)
            ) DESC
    </select>

</mapper>
