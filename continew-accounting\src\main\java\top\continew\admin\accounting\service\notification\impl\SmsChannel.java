package top.continew.admin.accounting.service.notification.impl;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.continew.admin.accounting.enums.NotificationChannelEnum;
import top.continew.admin.accounting.model.entity.NotificationDO;
import top.continew.admin.accounting.service.notification.AbstractNotificationChannel;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 短信通知渠道实现
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SmsChannel extends AbstractNotificationChannel {

    @Override
    public String getChannelCode() {
        return NotificationChannelEnum.SMS.getCode();
    }

    @Override
    public String getChannelName() {
        return NotificationChannelEnum.SMS.getName();
    }

    @Override
    public boolean isEnabled() {
        // 检查短信服务配置
        Map<String, Object> config = getChannelConfig();
        return config != null && Boolean.TRUE.equals(config.get("enabled"));
    }

    @Override
    protected Map<String, Object> doSendSingle(NotificationDO notification, Long targetUserId, Map<String, Object> config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取用户手机号
            String phoneNumber = getUserPhoneNumber(targetUserId);
            if (StrUtil.isBlank(phoneNumber)) {
                throw new RuntimeException("用户手机号为空");
            }

            // 格式化短信内容
            String smsContent = formatSmsContent(notification, config);
            
            // 验证短信内容长度
            if (smsContent.length() > 500) {
                throw new RuntimeException("短信内容超过长度限制");
            }

            // 发送短信
            Map<String, Object> sendResult = sendSms(phoneNumber, smsContent, config);
            
            result.put("success", true);
            result.put("phoneNumber", phoneNumber);
            result.put("content", smsContent);
            result.put("messageId", sendResult.get("messageId"));
            result.put("cost", sendResult.get("cost"));
            
            log.info("短信发送成功 - 用户: {}, 手机号: {}, 消息ID: {}", 
                    targetUserId, phoneNumber, sendResult.get("messageId"));
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            log.error("短信发送失败 - 用户: {}, 错误: {}", targetUserId, e.getMessage(), e);
        }
        
        return result;
    }

    @Override
    protected Map<String, Object> doBatchSend(NotificationDO notification, List<Long> targetUserIds, Map<String, Object> config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取所有用户的手机号
            Map<Long, String> userPhoneMap = getUserPhoneNumbers(targetUserIds);
            
            // 过滤有效手机号
            List<String> validPhoneNumbers = userPhoneMap.values().stream()
                    .filter(StrUtil::isNotBlank)
                    .toList();
            
            if (validPhoneNumbers.isEmpty()) {
                throw new RuntimeException("没有有效的手机号");
            }

            // 格式化短信内容
            String smsContent = formatSmsContent(notification, config);
            
            // 批量发送短信
            Map<String, Object> sendResult = batchSendSms(validPhoneNumbers, smsContent, config);
            
            result.put("success", true);
            result.put("totalCount", targetUserIds.size());
            result.put("validCount", validPhoneNumbers.size());
            result.put("sentCount", sendResult.get("sentCount"));
            result.put("failedCount", sendResult.get("failedCount"));
            result.put("batchId", sendResult.get("batchId"));
            result.put("totalCost", sendResult.get("totalCost"));
            
            log.info("批量短信发送完成 - 总数: {}, 有效: {}, 成功: {}, 失败: {}", 
                    targetUserIds.size(), validPhoneNumbers.size(), 
                    sendResult.get("sentCount"), sendResult.get("failedCount"));
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            log.error("批量短信发送失败 - 用户数: {}, 错误: {}", targetUserIds.size(), e.getMessage(), e);
        }
        
        return result;
    }

    @Override
    public Map<String, Object> testConnection(Map<String, Object> config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 验证配置参数
            validateConfig(config);
            
            // 发送测试短信
            String testPhone = (String) config.get("testPhone");
            if (StrUtil.isNotBlank(testPhone)) {
                Map<String, Object> testResult = sendSms(testPhone, "ContiNew记账系统测试短信", config);
                result.put("success", true);
                result.put("messageId", testResult.get("messageId"));
                result.put("message", "测试短信发送成功");
            } else {
                result.put("success", true);
                result.put("message", "配置验证通过，但未提供测试手机号");
            }
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            log.error("短信渠道测试失败: {}", e.getMessage(), e);
        }
        
        return result;
    }

    @Override
    public boolean validateConfig(Map<String, Object> config) {
        if (config == null) {
            throw new IllegalArgumentException("短信配置不能为空");
        }
        
        String accessKeyId = (String) config.get("accessKeyId");
        String accessKeySecret = (String) config.get("accessKeySecret");
        String signName = (String) config.get("signName");
        String templateCode = (String) config.get("templateCode");
        
        if (StrUtil.isBlank(accessKeyId)) {
            throw new IllegalArgumentException("AccessKeyId不能为空");
        }
        if (StrUtil.isBlank(accessKeySecret)) {
            throw new IllegalArgumentException("AccessKeySecret不能为空");
        }
        if (StrUtil.isBlank(signName)) {
            throw new IllegalArgumentException("短信签名不能为空");
        }
        if (StrUtil.isBlank(templateCode)) {
            throw new IllegalArgumentException("短信模板代码不能为空");
        }
        
        return true;
    }

    @Override
    public Map<String, Object> getChannelConfig() {
        // 从配置中心或数据库获取短信渠道配置
        Map<String, Object> config = new HashMap<>();
        config.put("enabled", false); // 默认禁用，需要配置后启用
        config.put("provider", "aliyun"); // 短信服务提供商
        config.put("accessKeyId", "");
        config.put("accessKeySecret", "");
        config.put("signName", "ContiNew记账");
        config.put("templateCode", "SMS_123456789");
        config.put("rateLimit", 100); // 每分钟限制100条
        config.put("maxLength", 500); // 最大长度500字符
        return config;
    }

    @Override
    public void updateChannelConfig(Map<String, Object> config) {
        // 更新短信渠道配置到配置中心或数据库
        validateConfig(config);
        log.info("短信渠道配置已更新");
    }

    /**
     * 获取用户手机号
     */
    private String getUserPhoneNumber(Long userId) {
        // 从用户服务获取手机号
        // 这里应该调用用户服务的接口
        return ""; // 占位符，实际需要实现
    }

    /**
     * 批量获取用户手机号
     */
    private Map<Long, String> getUserPhoneNumbers(List<Long> userIds) {
        // 从用户服务批量获取手机号
        // 这里应该调用用户服务的接口
        return new HashMap<>(); // 占位符，实际需要实现
    }

    /**
     * 格式化短信内容
     */
    private String formatSmsContent(NotificationDO notification, Map<String, Object> config) {
        String content = notification.getContent();
        
        // 移除HTML标签
        content = content.replaceAll("<[^>]+>", "");
        
        // 限制长度
        Integer maxLength = (Integer) config.getOrDefault("maxLength", 500);
        if (content.length() > maxLength) {
            content = content.substring(0, maxLength - 3) + "...";
        }
        
        return content;
    }

    /**
     * 发送单条短信
     */
    private Map<String, Object> sendSms(String phoneNumber, String content, Map<String, Object> config) {
        // 实际的短信发送逻辑
        // 这里应该调用短信服务提供商的API
        Map<String, Object> result = new HashMap<>();
        result.put("messageId", "SMS_" + System.currentTimeMillis());
        result.put("cost", 0.05); // 短信费用
        return result;
    }

    /**
     * 批量发送短信
     */
    private Map<String, Object> batchSendSms(List<String> phoneNumbers, String content, Map<String, Object> config) {
        // 实际的批量短信发送逻辑
        // 这里应该调用短信服务提供商的批量API
        Map<String, Object> result = new HashMap<>();
        result.put("batchId", "BATCH_" + System.currentTimeMillis());
        result.put("sentCount", phoneNumbers.size());
        result.put("failedCount", 0);
        result.put("totalCost", phoneNumbers.size() * 0.05);
        return result;
    }

    @Override
    public int getPriority() {
        return 3; // 短信优先级中等
    }

    @Override
    public boolean supportsRetry() {
        return true;
    }

    @Override
    public long getRetryInterval() {
        return 60000; // 1分钟重试间隔
    }

    @Override
    public int getMaxRetries() {
        return 3;
    }

    @Override
    public boolean supportsCallback() {
        return true; // 支持短信状态回调
    }

    @Override
    public void handleCallback(Map<String, Object> callbackData) {
        // 处理短信状态回调
        String messageId = (String) callbackData.get("messageId");
        String status = (String) callbackData.get("status");
        
        log.info("收到短信状态回调 - 消息ID: {}, 状态: {}", messageId, status);
        
        // 更新发送状态
        // 这里应该更新数据库中的发送状态
    }

}
