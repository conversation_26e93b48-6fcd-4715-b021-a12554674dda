package top.continew.admin.accounting.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.accounting.model.query.BudgetQuery;
import top.continew.admin.accounting.model.query.CostAnalysisQuery;
import top.continew.admin.accounting.model.req.*;
import top.continew.admin.accounting.model.resp.*;
import top.continew.admin.accounting.service.FinancialManagementService;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.log.annotation.Log;
import top.continew.starter.web.model.R;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * 财务管理控制器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "财务管理 API")
@RestController
@RequiredArgsConstructor
@Validated
@RequestMapping("/accounting/financial")
public class FinancialManagementController {

    private final FinancialManagementService financialManagementService;

    // ==================== 预算管理 ====================

    @Operation(summary = "创建预算", description = "创建新的预算计划")
    @Log(value = "创建预算")
    @PostMapping("/budget")
    public R<Long> createBudget(@Valid @RequestBody BudgetCreateReq req) {
        return R.ok(financialManagementService.createBudget(req));
    }

    @Operation(summary = "更新预算", description = "更新预算信息")
    @Log(value = "更新预算")
    @PutMapping("/budget/{budgetId}")
    public R<Void> updateBudget(@Parameter(description = "预算ID") @PathVariable Long budgetId,
                                @Valid @RequestBody BudgetUpdateReq req) {
        financialManagementService.updateBudget(budgetId, req);
        return R.ok();
    }

    @Operation(summary = "删除预算", description = "删除预算")
    @Log(value = "删除预算")
    @DeleteMapping("/budget/{budgetId}")
    public R<Void> deleteBudget(@Parameter(description = "预算ID") @PathVariable Long budgetId) {
        financialManagementService.deleteBudget(budgetId);
        return R.ok();
    }

    @Operation(summary = "获取预算列表", description = "分页查询预算列表")
    @GetMapping("/budget")
    public R<PageResp<BudgetListResp>> getBudgetList(@Valid BudgetQuery query) {
        return R.ok(financialManagementService.getBudgetList(query));
    }

    @Operation(summary = "获取预算详情", description = "根据ID获取预算详细信息")
    @GetMapping("/budget/{budgetId}")
    public R<BudgetDetailResp> getBudgetDetail(@Parameter(description = "预算ID") @PathVariable Long budgetId) {
        return R.ok(financialManagementService.getBudgetDetail(budgetId));
    }

    @Operation(summary = "获取预算执行情况", description = "获取预算执行分析")
    @GetMapping("/budget/{budgetId}/execution")
    public R<BudgetExecutionResp> getBudgetExecution(@Parameter(description = "预算ID") @PathVariable Long budgetId) {
        return R.ok(financialManagementService.getBudgetExecution(budgetId));
    }

    // ==================== 审核管理 ====================

    @Operation(summary = "提交审核", description = "提交财务审核申请")
    @Log(value = "提交审核")
    @PostMapping("/audit")
    public R<Long> submitAudit(@Valid @RequestBody AuditSubmitReq req) {
        return R.ok(financialManagementService.submitAudit(req));
    }

    @Operation(summary = "审核通过", description = "审核通过操作")
    @Log(value = "审核通过")
    @PostMapping("/audit/{auditId}/approve")
    public R<Void> approveAudit(@Parameter(description = "审核ID") @PathVariable Long auditId,
                                @Valid @RequestBody AuditReq req) {
        financialManagementService.approveAudit(auditId, req);
        return R.ok();
    }

    @Operation(summary = "批量审核", description = "批量审核操作")
    @Log(value = "批量审核")
    @PostMapping("/audit/batch")
    public R<Void> batchAudit(@Valid @RequestBody BatchAuditReq req) {
        financialManagementService.batchAudit(req);
        return R.ok();
    }

    @Operation(summary = "获取审核详情", description = "根据ID获取审核详细信息")
    @GetMapping("/audit/{auditId}")
    public R<AuditDetailResp> getAuditDetail(@Parameter(description = "审核ID") @PathVariable Long auditId) {
        return R.ok(financialManagementService.getAuditDetail(auditId));
    }

    @Operation(summary = "获取审核列表", description = "分页查询审核列表")
    @GetMapping("/audit")
    public R<PageResp<AuditListResp>> getAuditList(@Valid AuditQuery query) {
        return R.ok(financialManagementService.getAuditList(query));
    }

    @Operation(summary = "获取审核统计", description = "获取审核统计信息")
    @GetMapping("/audit/statistics")
    public R<AuditStatisticsResp> getAuditStatistics(@Parameter(description = "群组ID") @RequestParam Long groupId) {
        return R.ok(financialManagementService.getAuditStatistics(groupId));
    }

    // ==================== 财务预警 ====================

    @Operation(summary = "创建预警规则", description = "创建财务预警规则")
    @Log(value = "创建预警规则")
    @PostMapping("/alert/rule")
    public R<Long> createAlertRule(@Valid @RequestBody AlertRuleCreateReq req) {
        return R.ok(financialManagementService.createAlertRule(req));
    }

    @Operation(summary = "更新预警规则", description = "更新预警规则")
    @Log(value = "更新预警规则")
    @PutMapping("/alert/rule/{ruleId}")
    public R<Void> updateAlertRule(@Parameter(description = "规则ID") @PathVariable Long ruleId,
                                   @Valid @RequestBody AlertRuleUpdateReq req) {
        financialManagementService.updateAlertRule(ruleId, req);
        return R.ok();
    }

    @Operation(summary = "删除预警规则", description = "删除预警规则")
    @Log(value = "删除预警规则")
    @DeleteMapping("/alert/rule/{ruleId}")
    public R<Void> deleteAlertRule(@Parameter(description = "规则ID") @PathVariable Long ruleId) {
        financialManagementService.deleteAlertRule(ruleId);
        return R.ok();
    }

    @Operation(summary = "获取预警规则列表", description = "获取预警规则列表")
    @GetMapping("/alert/rule")
    public R<PageResp<AlertRuleResp>> getAlertRuleList(@Valid AlertRuleQuery query) {
        return R.ok(financialManagementService.getAlertRuleList(query));
    }

    @Operation(summary = "获取预警列表", description = "获取预警记录列表")
    @GetMapping("/alert")
    public R<PageResp<AlertResp>> getAlertList(@Valid AlertQuery query) {
        return R.ok(financialManagementService.getAlertList(query));
    }

    @Operation(summary = "处理预警", description = "处理预警记录")
    @Log(value = "处理预警")
    @PostMapping("/alert/{alertId}/handle")
    public R<Void> handleAlert(@Parameter(description = "预警ID") @PathVariable Long alertId,
                               @Valid @RequestBody AlertHandleReq req) {
        financialManagementService.handleAlert(alertId, req);
        return R.ok();
    }

    @Operation(summary = "检查预警", description = "手动触发预警检查")
    @Log(value = "检查预警")
    @PostMapping("/alert/check")
    public R<AlertCheckResp> checkAlerts(@Parameter(description = "群组ID") @RequestParam Long groupId) {
        return R.ok(financialManagementService.checkAlerts(groupId));
    }

    // ==================== 成本分析 ====================

    @Operation(summary = "成本分析", description = "执行成本分析")
    @PostMapping("/cost/analyze")
    public R<CostAnalysisResp> analyzeCost(@Valid @RequestBody CostAnalysisQuery query) {
        return R.ok(financialManagementService.analyzeCost(query));
    }

    @Operation(summary = "成本趋势分析", description = "获取成本趋势分析")
    @GetMapping("/cost/trend")
    public R<CostTrendResp> getCostTrend(@Valid CostAnalysisQuery query) {
        return R.ok(financialManagementService.getCostTrend(query));
    }

    @Operation(summary = "成本对比分析", description = "获取成本对比分析")
    @GetMapping("/cost/comparison")
    public R<CostComparisonResp> getCostComparison(@Valid CostAnalysisQuery query) {
        return R.ok(financialManagementService.getCostComparison(query));
    }

    @Operation(summary = "成本分解分析", description = "获取成本分解分析")
    @GetMapping("/cost/breakdown")
    public R<CostBreakdownResp> getCostBreakdown(@Valid CostAnalysisQuery query) {
        return R.ok(financialManagementService.getCostBreakdown(query));
    }

    @Operation(summary = "成本效益分析", description = "获取成本效益分析")
    @GetMapping("/cost/benefit")
    public R<CostBenefitResp> getCostBenefit(@Valid CostAnalysisQuery query) {
        return R.ok(financialManagementService.getCostBenefit(query));
    }

    // ==================== 财务报表 ====================

    @Operation(summary = "生成财务报表", description = "生成财务报表")
    @Log(value = "生成财务报表")
    @PostMapping("/report/generate")
    public R<FinancialReportResp> generateFinancialReport(@Valid @RequestBody FinancialReportReq req) {
        return R.ok(financialManagementService.generateFinancialReport(req));
    }

    @Operation(summary = "获取财务报表列表", description = "获取财务报表列表")
    @GetMapping("/report")
    public R<PageResp<FinancialReportListResp>> getFinancialReportList(@Parameter(description = "群组ID") @RequestParam Long groupId) {
        return R.ok(financialManagementService.getFinancialReportList(groupId));
    }

    @Operation(summary = "下载财务报表", description = "下载财务报表文件")
    @GetMapping("/report/{reportId}/download")
    public R<String> downloadFinancialReport(@Parameter(description = "报表ID") @PathVariable Long reportId) {
        return R.ok(financialManagementService.downloadFinancialReport(reportId));
    }

    // ==================== 财务指标 ====================

    @Operation(summary = "获取财务指标", description = "获取财务关键指标")
    @GetMapping("/metrics")
    public R<FinancialMetricsResp> getFinancialMetrics(@Parameter(description = "群组ID") @RequestParam Long groupId) {
        return R.ok(financialManagementService.getFinancialMetrics(groupId));
    }

    @Operation(summary = "获取财务健康度", description = "获取财务健康度评估")
    @GetMapping("/health")
    public R<FinancialHealthResp> getFinancialHealth(@Parameter(description = "群组ID") @RequestParam Long groupId) {
        return R.ok(financialManagementService.getFinancialHealth(groupId));
    }

    @Operation(summary = "获取财务建议", description = "获取财务优化建议")
    @GetMapping("/advice")
    public R<FinancialAdviceResp> getFinancialAdvice(@Parameter(description = "群组ID") @RequestParam Long groupId) {
        return R.ok(financialManagementService.getFinancialAdvice(groupId));
    }

    // ==================== 财务仪表盘 ====================

    @Operation(summary = "获取财务仪表盘", description = "获取财务仪表盘数据")
    @GetMapping("/dashboard")
    public R<FinancialDashboardResp> getFinancialDashboard(@Parameter(description = "群组ID") @RequestParam Long groupId) {
        return R.ok(financialManagementService.getFinancialDashboard(groupId));
    }

    @Operation(summary = "获取财务概览", description = "获取财务概览信息")
    @GetMapping("/overview")
    public R<FinancialOverviewResp> getFinancialOverview(@Parameter(description = "群组ID") @RequestParam Long groupId) {
        return R.ok(financialManagementService.getFinancialOverview(groupId));
    }
}
