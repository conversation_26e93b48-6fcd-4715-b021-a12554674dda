package top.continew.admin.accounting.service;

import com.alicp.jetcache.CacheManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import top.continew.admin.accounting.base.BaseServiceTest;
import top.continew.admin.accounting.enums.CacheTypeEnum;
import top.continew.admin.accounting.enums.CacheStrategyEnum;
import top.continew.admin.accounting.enums.CacheEvictionPolicyEnum;
import top.continew.admin.accounting.mapper.CacheConfigMapper;
import top.continew.admin.accounting.mapper.CacheStatisticsMapper;
import top.continew.admin.accounting.model.entity.CacheConfigDO;
import top.continew.admin.accounting.model.req.CacheConfigReq;
import top.continew.admin.accounting.model.req.CacheQueryReq;
import top.continew.admin.accounting.model.resp.CacheConfigResp;
import top.continew.admin.accounting.model.resp.CacheStatisticsResp;
import top.continew.admin.accounting.service.impl.CacheOptimizationServiceImpl;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 缓存优化服务测试
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@ExtendWith(MockitoExtension.class)
class CacheOptimizationServiceTest extends BaseServiceTest {

    @Mock
    private CacheConfigMapper cacheConfigMapper;

    @Mock
    private CacheStatisticsMapper cacheStatisticsMapper;

    @Mock
    private CacheManager cacheManager;

    @InjectMocks
    private CacheOptimizationServiceImpl cacheOptimizationService;

    private CacheConfigDO testCacheConfig;
    private CacheConfigReq configReq;

    @BeforeEach
    @Override
    protected void beforeEach() {
        super.beforeEach();
        testCacheConfig = createTestCacheConfig();
        configReq = createTestCacheConfigReq();
    }

    private CacheConfigDO createTestCacheConfig() {
        CacheConfigDO config = new CacheConfigDO();
        config.setId(1L);
        config.setConfigName("测试缓存配置");
        config.setConfigCode("TEST_CACHE");
        config.setCacheType(CacheTypeEnum.BOTH);
        config.setCacheStrategy(CacheStrategyEnum.LAZY_LOADING);
        config.setEvictionPolicy(CacheEvictionPolicyEnum.LRU);
        config.setKeyPrefix("TEST:");
        config.setExpireTime(3600L);
        config.setLocalMaxSize(1000L);
        config.setLocalExpireTime(1800L);
        config.setRemoteExpireTime(3600L);
        config.setPenetrationProtect(true);
        config.setAvalancheProtect(true);
        config.setBreakdownProtect(true);
        config.setStatisticsEnabled(true);
        config.setStatus("ENABLE");
        config.setGroupId(1L);
        return config;
    }

    private CacheConfigReq createTestCacheConfigReq() {
        CacheConfigReq req = new CacheConfigReq();
        req.setConfigName("测试缓存配置");
        req.setConfigCode("TEST_CACHE");
        req.setCacheType(CacheTypeEnum.BOTH);
        req.setCacheStrategy(CacheStrategyEnum.LAZY_LOADING);
        req.setEvictionPolicy(CacheEvictionPolicyEnum.LRU);
        req.setKeyPrefix("TEST:");
        req.setExpireTime(3600L);
        req.setLocalMaxSize(1000L);
        req.setLocalExpireTime(1800L);
        req.setRemoteExpireTime(3600L);
        req.setPenetrationProtect(true);
        req.setAvalancheProtect(true);
        req.setBreakdownProtect(true);
        req.setStatisticsEnabled(true);
        req.setGroupId(1L);
        return req;
    }

    @Test
    void testCreateCacheConfig() {
        // Given
        when(cacheConfigMapper.insert(any(CacheConfigDO.class))).thenReturn(1);
        when(cacheConfigMapper.selectById(anyLong())).thenReturn(testCacheConfig);

        // When
        Long configId = cacheOptimizationService.createCacheConfig(configReq);

        // Then
        assertNotNull(configId);
        verify(cacheConfigMapper).insert(any(CacheConfigDO.class));
    }

    @Test
    void testGetCacheConfig() {
        // Given
        when(cacheConfigMapper.selectById(1L)).thenReturn(testCacheConfig);

        // When
        CacheConfigResp result = cacheOptimizationService.getCacheConfig(1L);

        // Then
        assertNotNull(result);
        assertEquals(testCacheConfig.getConfigName(), result.getConfigName());
        assertEquals(testCacheConfig.getCacheType(), result.getCacheType());
        verify(cacheConfigMapper).selectById(1L);
    }

    @Test
    void testUpdateCacheConfig() {
        // Given
        when(cacheConfigMapper.selectById(1L)).thenReturn(testCacheConfig);
        when(cacheConfigMapper.updateById(any(CacheConfigDO.class))).thenReturn(1);

        // When
        assertDoesNotThrow(() -> cacheOptimizationService.updateCacheConfig(1L, configReq));

        // Then
        verify(cacheConfigMapper).selectById(1L);
        verify(cacheConfigMapper).updateById(any(CacheConfigDO.class));
    }

    @Test
    void testDeleteCacheConfig() {
        // Given
        when(cacheConfigMapper.selectById(1L)).thenReturn(testCacheConfig);
        when(cacheConfigMapper.deleteById(1L)).thenReturn(1);

        // When
        assertDoesNotThrow(() -> cacheOptimizationService.deleteCacheConfig(1L));

        // Then
        verify(cacheConfigMapper).selectById(1L);
        verify(cacheConfigMapper).deleteById(1L);
    }

    @Test
    void testPageCacheConfigs() {
        // Given
        CacheQueryReq queryReq = new CacheQueryReq();
        queryReq.setConfigName("测试");
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPage(1);
        pageQuery.setSize(10);

        List<CacheConfigDO> configs = Arrays.asList(testCacheConfig);
        when(cacheConfigMapper.selectPage(any(), any())).thenReturn(configs);
        when(cacheConfigMapper.selectCount(any())).thenReturn(1L);

        // When
        PageResp<CacheConfigResp> result = cacheOptimizationService.pageCacheConfigs(queryReq, pageQuery);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getList().size());
    }

    @Test
    void testApplyCacheConfig() {
        // Given
        when(cacheConfigMapper.selectById(1L)).thenReturn(testCacheConfig);
        when(cacheConfigMapper.updateById(any(CacheConfigDO.class))).thenReturn(1);

        // When
        assertDoesNotThrow(() -> cacheOptimizationService.applyCacheConfig(1L));

        // Then
        verify(cacheConfigMapper).selectById(1L);
        verify(cacheConfigMapper).updateById(any(CacheConfigDO.class));
    }

    @Test
    void testPreloadCache() {
        // Given
        when(cacheConfigMapper.selectById(1L)).thenReturn(testCacheConfig);

        // When
        assertDoesNotThrow(() -> cacheOptimizationService.preloadCache(1L));

        // Then
        verify(cacheConfigMapper).selectById(1L);
    }

    @Test
    void testRefreshCache() {
        // Given
        when(cacheConfigMapper.selectById(1L)).thenReturn(testCacheConfig);

        // When
        assertDoesNotThrow(() -> cacheOptimizationService.refreshCache(1L));

        // Then
        verify(cacheConfigMapper).selectById(1L);
    }

    @Test
    void testClearCache() {
        // Given
        when(cacheConfigMapper.selectById(1L)).thenReturn(testCacheConfig);

        // When
        assertDoesNotThrow(() -> cacheOptimizationService.clearCache(1L));

        // Then
        verify(cacheConfigMapper).selectById(1L);
    }

    @Test
    void testGetCacheStatistics() {
        // Given
        when(cacheStatisticsMapper.selectOverallStatistics(anyLong(), any(), any()))
                .thenReturn(new CacheStatisticsResp.OverallStatistics());

        // When
        CacheStatisticsResp result = cacheOptimizationService.getCacheStatistics(1L);

        // Then
        assertNotNull(result);
        verify(cacheStatisticsMapper).selectOverallStatistics(anyLong(), any(), any());
    }

    @Test
    void testGetCachePerformanceMetrics() {
        // Given
        when(cacheConfigMapper.selectPerformanceMetrics(anyLong(), any(), any()))
                .thenReturn(Arrays.asList());

        // When
        var metrics = cacheOptimizationService.getCachePerformanceMetrics(1L);

        // Then
        assertNotNull(metrics);
        verify(cacheConfigMapper).selectPerformanceMetrics(anyLong(), any(), any());
    }

    @Test
    void testGetHotspotData() {
        // Given
        when(cacheStatisticsMapper.selectHotspotData(anyLong(), any(), any(), anyInt()))
                .thenReturn(Arrays.asList());

        // When
        var hotspots = cacheOptimizationService.getHotspotData(1L, 10);

        // Then
        assertNotNull(hotspots);
        verify(cacheStatisticsMapper).selectHotspotData(anyLong(), any(), any(), anyInt());
    }

    @Test
    void testGetCacheHealthStatus() {
        // Given
        when(cacheConfigMapper.selectHealthStatus(anyLong())).thenReturn(new Object());

        // When
        var healthStatus = cacheOptimizationService.getCacheHealthStatus(1L);

        // Then
        assertNotNull(healthStatus);
        verify(cacheConfigMapper).selectHealthStatus(1L);
    }

    @Test
    void testDetectCacheAnomalies() {
        // Given
        when(cacheConfigMapper.selectAnomalies(anyLong(), any(), any()))
                .thenReturn(Arrays.asList());

        // When
        var anomalies = cacheOptimizationService.detectCacheAnomalies(1L);

        // Then
        assertNotNull(anomalies);
        verify(cacheConfigMapper).selectAnomalies(anyLong(), any(), any());
    }

    @Test
    void testGetCacheOptimizationSuggestions() {
        // Given
        when(cacheConfigMapper.selectOptimizationSuggestions(anyLong()))
                .thenReturn(Arrays.asList());

        // When
        var suggestions = cacheOptimizationService.getCacheOptimizationSuggestions(1L);

        // Then
        assertNotNull(suggestions);
        verify(cacheConfigMapper).selectOptimizationSuggestions(1L);
    }

    @Test
    void testAnalyzeCacheUsagePattern() {
        // Given
        when(cacheStatisticsMapper.selectUsagePattern(anyLong(), any(), any()))
                .thenReturn(new Object());

        // When
        var pattern = cacheOptimizationService.analyzeCacheUsagePattern(1L);

        // Then
        assertNotNull(pattern);
        verify(cacheStatisticsMapper).selectUsagePattern(anyLong(), any(), any());
    }

    @Test
    void testPredictCacheCapacity() {
        // Given
        when(cacheStatisticsMapper.selectCapacityPrediction(anyLong(), anyInt()))
                .thenReturn(new Object());

        // When
        var prediction = cacheOptimizationService.predictCacheCapacity(1L, 30);

        // Then
        assertNotNull(prediction);
        verify(cacheStatisticsMapper).selectCapacityPrediction(1L, 30);
    }

}
