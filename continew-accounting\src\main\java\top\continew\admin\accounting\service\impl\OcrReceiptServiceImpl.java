package top.continew.admin.accounting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.accounting.mapper.OcrTaskMapper;
import top.continew.admin.accounting.model.entity.OcrTask;
import top.continew.admin.accounting.model.query.OcrTaskQuery;
import top.continew.admin.accounting.model.req.OcrReceiptReq;
import top.continew.admin.accounting.model.req.TransactionCreateReq;
import top.continew.admin.accounting.model.resp.OcrReceiptResp;
import top.continew.admin.accounting.service.OcrReceiptService;
import top.continew.admin.accounting.service.TransactionService;
import top.continew.admin.common.model.resp.LabelValueResp;
import top.continew.admin.common.util.helper.LoginHelper;
import top.continew.admin.common.util.validate.CheckUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * OCR收据识别服务实现
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OcrReceiptServiceImpl implements OcrReceiptService {

    private final OcrTaskMapper ocrTaskMapper;
    private final TransactionService transactionService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OcrReceiptResp recognizeReceipt(OcrReceiptReq request) {
        log.info("开始识别收据，引擎：{}", request.getOcrEngine());
        
        // 生成任务编号
        String taskNumber = generateTaskNumber();
        
        // 创建任务记录
        OcrTask task = createOcrTask(request, taskNumber);
        task.setAsyncProcessing(false);
        task.setStatus("PROCESSING");
        task.setRecognitionStartTime(LocalDateTime.now());
        ocrTaskMapper.insert(task);
        
        try {
            // 执行OCR识别
            OcrReceiptResp result = performOcrRecognition(task, request);
            
            // 更新任务状态
            updateTaskAfterRecognition(task, result, "SUCCESS");
            
            // 自动创建账单
            if (Boolean.TRUE.equals(request.getAutoCreateTransaction()) && result.getReceiptInfo() != null) {
                Long transactionId = createTransactionFromRecognition(task, result);
                result.setTransactionCreated(transactionId != null);
                result.setTransactionId(transactionId);
            }
            
            log.info("收据识别完成，任务编号：{}，置信度：{}", taskNumber, result.getConfidence());
            return result;
            
        } catch (Exception e) {
            log.error("收据识别失败，任务编号：{}", taskNumber, e);
            
            // 更新任务状态为失败
            task.setStatus("FAILED");
            task.setErrorMessage(e.getMessage());
            task.setErrorCode("OCR_001");
            task.setRecognitionEndTime(LocalDateTime.now());
            task.setProcessingTime(System.currentTimeMillis() - task.getRecognitionStartTime().atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli());
            ocrTaskMapper.updateById(task);
            
            // 返回失败结果
            OcrReceiptResp errorResult = new OcrReceiptResp();
            errorResult.setTaskId(taskNumber);
            errorResult.setStatus("FAILED");
            errorResult.setErrorMessage(e.getMessage());
            errorResult.setErrorCode("OCR_001");
            errorResult.setOcrEngine(request.getOcrEngine());
            errorResult.setRecognitionTime(LocalDateTime.now());
            return errorResult;
        }
    }

    @Override
    public String recognizeReceiptAsync(OcrReceiptReq request) {
        log.info("开始异步识别收据，引擎：{}", request.getOcrEngine());
        
        // 生成任务编号
        String taskNumber = generateTaskNumber();
        
        // 创建任务记录
        OcrTask task = createOcrTask(request, taskNumber);
        task.setAsyncProcessing(true);
        task.setStatus("PROCESSING");
        task.setRecognitionStartTime(LocalDateTime.now());
        ocrTaskMapper.insert(task);
        
        // TODO: 提交到异步队列处理
        // 这里应该将任务提交到消息队列或线程池进行异步处理
        // 示例：rabbitTemplate.convertAndSend("ocr.queue", task);
        
        log.info("异步识别任务已提交，任务编号：{}", taskNumber);
        return taskNumber;
    }

    @Override
    public List<OcrReceiptResp> batchRecognizeReceipts(List<OcrReceiptReq> requests) {
        log.info("开始批量识别收据，数量：{}", requests.size());
        
        List<OcrReceiptResp> results = new ArrayList<>();
        for (OcrReceiptReq request : requests) {
            try {
                OcrReceiptResp result = recognizeReceipt(request);
                results.add(result);
            } catch (Exception e) {
                log.error("批量识别中的单个任务失败", e);
                
                OcrReceiptResp errorResult = new OcrReceiptResp();
                errorResult.setStatus("FAILED");
                errorResult.setErrorMessage(e.getMessage());
                errorResult.setOcrEngine(request.getOcrEngine());
                results.add(errorResult);
            }
        }
        
        log.info("批量识别完成，成功：{}，失败：{}", 
                results.stream().mapToLong(r -> "SUCCESS".equals(r.getStatus()) ? 1 : 0).sum(),
                results.stream().mapToLong(r -> "FAILED".equals(r.getStatus()) ? 1 : 0).sum());
        
        return results;
    }

    @Override
    public List<String> batchRecognizeReceiptsAsync(List<OcrReceiptReq> requests) {
        log.info("开始批量异步识别收据，数量：{}", requests.size());
        
        List<String> taskNumbers = new ArrayList<>();
        for (OcrReceiptReq request : requests) {
            String taskNumber = recognizeReceiptAsync(request);
            taskNumbers.add(taskNumber);
        }
        
        log.info("批量异步识别任务已提交，任务数量：{}", taskNumbers.size());
        return taskNumbers;
    }

    @Override
    public OcrReceiptResp getTaskStatus(String taskId) {
        OcrTask task = getTaskByNumber(taskId);
        return convertToResponse(task);
    }

    @Override
    public OcrReceiptResp getTaskDetail(String taskId) {
        OcrTask task = getTaskByNumber(taskId);
        OcrReceiptResp response = convertToResponse(task);
        
        // 填充详细信息
        if (StrUtil.isNotBlank(task.getRawResultJson())) {
            response.setRawResult(JSONUtil.parseObj(task.getRawResultJson()));
        }
        
        return response;
    }

    @Override
    public IPage<OcrReceiptResp> pageTasks(OcrTaskQuery query) {
        Page<OcrTask> page = new Page<>(query.getPageNum(), query.getPageSize());
        QueryWrapper<OcrTask> wrapper = buildQueryWrapper(query);
        
        IPage<OcrTask> taskPage = ocrTaskMapper.selectPage(page, wrapper);
        
        return taskPage.convert(this::convertToResponse);
    }

    @Override
    public List<OcrReceiptResp> listTasks(OcrTaskQuery query) {
        QueryWrapper<OcrTask> wrapper = buildQueryWrapper(query);
        List<OcrTask> tasks = ocrTaskMapper.selectList(wrapper);
        
        return tasks.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelTask(String taskId) {
        OcrTask task = getTaskByNumber(taskId);
        CheckUtils.throwIf("PROCESSING".equals(task.getStatus()), "只能取消处理中的任务");
        
        task.setStatus("CANCELLED");
        task.setRecognitionEndTime(LocalDateTime.now());
        task.setUpdatedBy(LoginHelper.getUserId());
        task.setUpdateTime(LocalDateTime.now());
        
        ocrTaskMapper.updateById(task);
        log.info("任务已取消，任务编号：{}", taskId);
    }

    @Override
    public String retryTask(String taskId) {
        OcrTask originalTask = getTaskByNumber(taskId);
        CheckUtils.throwIf(!"FAILED".equals(originalTask.getStatus()), "只能重试失败的任务");
        
        // 创建重试请求
        OcrReceiptReq retryRequest = createRetryRequest(originalTask);
        
        // 执行重试
        return recognizeReceiptAsync(retryRequest);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTask(String taskId) {
        OcrTask task = getTaskByNumber(taskId);
        
        // 软删除
        task.setDeleted(true);
        task.setUpdatedBy(LoginHelper.getUserId());
        task.setUpdateTime(LocalDateTime.now());
        
        ocrTaskMapper.updateById(task);
        log.info("任务已删除，任务编号：{}", taskId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTasks(List<String> taskIds) {
        for (String taskId : taskIds) {
            deleteTask(taskId);
        }
        log.info("批量删除任务完成，数量：{}", taskIds.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTransactionFromTask(String taskId, Long categoryId, List<String> tags, String remark) {
        OcrTask task = getTaskByNumber(taskId);
        CheckUtils.throwIf(!"SUCCESS".equals(task.getStatus()), "只能从成功的识别任务创建账单");
        CheckUtils.throwIf(Boolean.TRUE.equals(task.getTransactionCreated()), "该任务已创建过账单");
        
        // 创建账单请求
        TransactionCreateReq createReq = new TransactionCreateReq();
        createReq.setGroupId(task.getGroupId());
        createReq.setAmount(task.getTotalAmount());
        createReq.setType("EXPENSE"); // 默认为支出
        createReq.setCategoryId(categoryId != null ? categoryId : task.getDefaultCategoryId());
        createReq.setDescription(task.getMerchantName());
        createReq.setTransactionTime(task.getTransactionTime() != null ? task.getTransactionTime() : task.getRecognitionStartTime());
        createReq.setTags(tags);
        createReq.setRemark(remark != null ? remark : task.getDefaultRemark());
        
        // 创建账单
        Long transactionId = transactionService.create(createReq);
        
        // 更新任务状态
        task.setTransactionCreated(true);
        task.setTransactionId(transactionId);
        task.setUpdatedBy(LoginHelper.getUserId());
        task.setUpdateTime(LocalDateTime.now());
        ocrTaskMapper.updateById(task);
        
        log.info("从OCR任务创建账单成功，任务编号：{}，账单ID：{}", taskId, transactionId);
        return transactionId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRecognitionResult(String taskId, Map<String, Object> receiptInfo) {
        OcrTask task = getTaskByNumber(taskId);
        
        // 更新识别结果
        if (receiptInfo.containsKey("merchantName")) {
            task.setMerchantName((String) receiptInfo.get("merchantName"));
        }
        if (receiptInfo.containsKey("totalAmount")) {
            task.setTotalAmount(new BigDecimal(receiptInfo.get("totalAmount").toString()));
        }
        if (receiptInfo.containsKey("transactionTime")) {
            task.setTransactionTime(LocalDateTime.parse(receiptInfo.get("transactionTime").toString()));
        }
        // ... 其他字段更新
        
        task.setUpdatedBy(LoginHelper.getUserId());
        task.setUpdateTime(LocalDateTime.now());
        ocrTaskMapper.updateById(task);
        
        log.info("识别结果已更新，任务编号：{}", taskId);
    }

    @Override
    public void markResultAsCorrect(String taskId) {
        // TODO: 实现标记结果为正确的逻辑
        // 可以用于机器学习模型的训练数据收集
        log.info("标记识别结果为正确，任务编号：{}", taskId);
    }

    @Override
    public void markResultAsIncorrect(String taskId, String feedback) {
        // TODO: 实现标记结果为错误的逻辑
        // 可以用于机器学习模型的训练数据收集
        log.info("标记识别结果为错误，任务编号：{}，反馈：{}", taskId, feedback);
    }

    @Override
    public String exportRecognitionResults(OcrTaskQuery query, String format) {
        // TODO: 实现导出识别结果的逻辑
        log.info("导出识别结果，格式：{}", format);
        return "/exports/ocr_results_" + DateUtil.format(new Date(), "yyyyMMdd_HHmmss") + "." + format.toLowerCase();
    }

    // ==================== 统计分析方法 ====================

    @Override
    public Map<String, Object> getRecognitionStats(Long groupId, Integer days) {
        // TODO: 实现识别统计逻辑
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalTasks", 0);
        stats.put("successTasks", 0);
        stats.put("failedTasks", 0);
        stats.put("successRate", 0.0);
        stats.put("avgProcessingTime", 0L);
        stats.put("avgConfidence", 0.0);
        return stats;
    }

    @Override
    public Map<String, Object> getEnginePerformanceStats(Long groupId, Integer days) {
        // TODO: 实现引擎性能统计逻辑
        Map<String, Object> stats = new HashMap<>();
        stats.put("engineStats", new ArrayList<>());
        return stats;
    }

    @Override
    public Map<String, Object> getAccuracyStats(Long groupId, Integer days) {
        // TODO: 实现准确率统计逻辑
        Map<String, Object> stats = new HashMap<>();
        stats.put("overallAccuracy", 0.0);
        stats.put("fieldAccuracy", new HashMap<>());
        return stats;
    }

    @Override
    public List<Map<String, Object>> getMerchantRecognitionRanking(Long groupId, Integer days, Integer limit) {
        // TODO: 实现商家识别排行逻辑
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getCategoryRecognitionStats(Long groupId, Integer days) {
        // TODO: 实现分类识别统计逻辑
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getRecognitionTrend(Long groupId, Integer days) {
        // TODO: 实现识别趋势逻辑
        return new ArrayList<>();
    }

    @Override
    public Map<String, Object> getErrorAnalysis(Long groupId, Integer days) {
        // TODO: 实现错误分析逻辑
        Map<String, Object> analysis = new HashMap<>();
        analysis.put("errorTypes", new ArrayList<>());
        analysis.put("errorTrend", new ArrayList<>());
        return analysis;
    }

    // ==================== 配置管理方法 ====================

    @Override
    public Map<String, Object> getEngineConfig(String engine) {
        // TODO: 实现获取引擎配置逻辑
        Map<String, Object> config = new HashMap<>();
        config.put("engine", engine);
        config.put("apiKey", "***");
        config.put("endpoint", "");
        config.put("timeout", 30);
        config.put("retryCount", 3);
        return config;
    }

    @Override
    public void updateEngineConfig(String engine, Map<String, Object> config) {
        // TODO: 实现更新引擎配置逻辑
        log.info("更新OCR引擎配置，引擎：{}", engine);
    }

    @Override
    public Map<String, Object> testEngineConnection(String engine) {
        // TODO: 实现测试引擎连接逻辑
        Map<String, Object> result = new HashMap<>();
        result.put("engine", engine);
        result.put("connected", true);
        result.put("responseTime", 500L);
        result.put("message", "连接成功");
        return result;
    }

    @Override
    public List<LabelValueResp<String>> getSupportedEngines() {
        List<LabelValueResp<String>> engines = new ArrayList<>();
        engines.add(new LabelValueResp<>("百度OCR", "BAIDU"));
        engines.add(new LabelValueResp<>("腾讯OCR", "TENCENT"));
        engines.add(new LabelValueResp<>("阿里云OCR", "ALIYUN"));
        engines.add(new LabelValueResp<>("Google Vision", "GOOGLE"));
        engines.add(new LabelValueResp<>("Azure Computer Vision", "AZURE"));
        return engines;
    }

    @Override
    public List<LabelValueResp<String>> getSupportedLanguages(String engine) {
        List<LabelValueResp<String>> languages = new ArrayList<>();
        languages.add(new LabelValueResp<>("中文", "zh-CN"));
        languages.add(new LabelValueResp<>("英文", "en-US"));
        languages.add(new LabelValueResp<>("日文", "ja-JP"));
        languages.add(new LabelValueResp<>("韩文", "ko-KR"));
        return languages;
    }

    @Override
    public List<LabelValueResp<String>> getSupportedRecognitionModes(String engine) {
        List<LabelValueResp<String>> modes = new ArrayList<>();
        modes.add(new LabelValueResp<>("收据识别", "RECEIPT"));
        modes.add(new LabelValueResp<>("发票识别", "INVOICE"));
        modes.add(new LabelValueResp<>("通用识别", "GENERAL"));
        return modes;
    }

    // ==================== 模板管理方法 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createRecognitionTemplate(String templateName, Map<String, Object> config) {
        // TODO: 实现创建识别模板逻辑
        log.info("创建识别模板：{}", templateName);
        return IdUtil.getSnowflakeNextId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRecognitionTemplate(Long templateId, Map<String, Object> config) {
        // TODO: 实现更新识别模板逻辑
        log.info("更新识别模板：{}", templateId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRecognitionTemplate(Long templateId) {
        // TODO: 实现删除识别模板逻辑
        log.info("删除识别模板：{}", templateId);
    }

    @Override
    public List<Map<String, Object>> getRecognitionTemplates(Long groupId) {
        // TODO: 实现获取识别模板列表逻辑
        return new ArrayList<>();
    }

    @Override
    public OcrReceiptReq applyRecognitionTemplate(Long templateId, OcrReceiptReq request) {
        // TODO: 实现应用识别模板逻辑
        log.info("应用识别模板：{}", templateId);
        return request;
    }

    // ==================== 辅助方法 ====================

    @Override
    public Boolean validateImageFormat(String fileName) {
        if (StrUtil.isBlank(fileName)) {
            return false;
        }
        String extension = StrUtil.subAfter(fileName, ".", true).toLowerCase();
        return CollUtil.contains(Arrays.asList("jpg", "jpeg", "png", "bmp", "webp", "tiff"), extension);
    }

    @Override
    public Boolean validateImageSize(Long fileSize) {
        // 限制文件大小为10MB
        return fileSize != null && fileSize > 0 && fileSize <= 10 * 1024 * 1024;
    }

    @Override
    public List<LabelValueResp<String>> getTaskStatusOptions() {
        List<LabelValueResp<String>> options = new ArrayList<>();
        options.add(new LabelValueResp<>("处理中", "PROCESSING"));
        options.add(new LabelValueResp<>("成功", "SUCCESS"));
        options.add(new LabelValueResp<>("失败", "FAILED"));
        options.add(new LabelValueResp<>("超时", "TIMEOUT"));
        options.add(new LabelValueResp<>("已取消", "CANCELLED"));
        return options;
    }

    @Override
    public List<LabelValueResp<String>> getRecognitionModeOptions() {
        return getSupportedRecognitionModes(null);
    }

    @Override
    public List<LabelValueResp<String>> getAccuracyOptions() {
        List<LabelValueResp<String>> options = new ArrayList<>();
        options.add(new LabelValueResp<>("低精度", "LOW"));
        options.add(new LabelValueResp<>("中精度", "MEDIUM"));
        options.add(new LabelValueResp<>("高精度", "HIGH"));
        return options;
    }

    @Override
    public Boolean existsTask(String taskId) {
        QueryWrapper<OcrTask> wrapper = new QueryWrapper<>();
        wrapper.eq("task_number", taskId);
        wrapper.eq("deleted", false);
        return ocrTaskMapper.selectCount(wrapper) > 0;
    }

    @Override
    public String generateTaskNumber() {
        return "OCR" + DateUtil.format(new Date(), "yyyyMMdd") + "_" + IdUtil.fastSimpleUUID().substring(0, 8).toUpperCase();
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 创建OCR任务
     */
    private OcrTask createOcrTask(OcrReceiptReq request, String taskNumber) {
        OcrTask task = new OcrTask();
        task.setGroupId(LoginHelper.getGroupId());
        task.setTaskNumber(taskNumber);
        task.setOcrEngine(request.getOcrEngine());
        task.setRecognitionMode(request.getRecognitionMode());
        task.setLanguage(request.getLanguage());
        task.setAccuracy(request.getAccuracy());
        task.setAutoCreateTransaction(request.getAutoCreateTransaction());
        task.setDefaultCategoryId(request.getDefaultCategoryId());
        task.setDefaultRemark(request.getDefaultRemark());
        task.setEnableSmartCategory(request.getEnableSmartCategory());
        task.setEnableSmartTags(request.getEnableSmartTags());
        task.setConfidenceThreshold(request.getConfidenceThreshold());
        task.setSaveOriginalImage(request.getSaveOriginalImage());
        task.setImageQuality(request.getImageQuality());
        task.setMaxImageSize(request.getMaxImageSize());
        task.setEnableTableRecognition(request.getEnableTableRecognition());
        task.setEnableHandwritingRecognition(request.getEnableHandwritingRecognition());
        task.setTimeoutSeconds(request.getTimeoutSeconds());
        task.setRetryCount(request.getRetryCount());
        task.setCallbackUrl(request.getCallbackUrl());

        if (CollUtil.isNotEmpty(request.getDefaultTags())) {
            task.setDefaultTagsJson(JSONUtil.toJsonStr(request.getDefaultTags()));
        }

        task.setCreatedBy(LoginHelper.getUserId());
        task.setCreateTime(LocalDateTime.now());

        return task;
    }

    /**
     * 执行OCR识别
     */
    private OcrReceiptResp performOcrRecognition(OcrTask task, OcrReceiptReq request) {
        // TODO: 实现实际的OCR识别逻辑
        // 这里应该调用具体的OCR引擎API

        // 模拟识别结果
        OcrReceiptResp response = new OcrReceiptResp();
        response.setTaskId(task.getTaskNumber());
        response.setStatus("SUCCESS");
        response.setProcessingTime(1500L);
        response.setConfidence(0.95);
        response.setOcrEngine(task.getOcrEngine());
        response.setRecognitionTime(LocalDateTime.now());

        // 模拟收据信息
        OcrReceiptResp.ReceiptInfo receiptInfo = new OcrReceiptResp.ReceiptInfo();
        receiptInfo.setMerchantName("星巴克咖啡");
        receiptInfo.setTotalAmount(new BigDecimal("58.50"));
        receiptInfo.setCurrency("CNY");
        receiptInfo.setTransactionTime(LocalDateTime.now().minusHours(1));
        receiptInfo.setRecognizedCategory("餐饮");
        receiptInfo.setRecognizedTags(Arrays.asList("咖啡", "饮品"));

        response.setReceiptInfo(receiptInfo);

        return response;
    }

    /**
     * 识别后更新任务
     */
    private void updateTaskAfterRecognition(OcrTask task, OcrReceiptResp result, String status) {
        task.setStatus(status);
        task.setRecognitionEndTime(LocalDateTime.now());
        task.setProcessingTime(result.getProcessingTime());
        task.setConfidence(result.getConfidence());

        if (result.getReceiptInfo() != null) {
            OcrReceiptResp.ReceiptInfo info = result.getReceiptInfo();
            task.setMerchantName(info.getMerchantName());
            task.setMerchantAddress(info.getMerchantAddress());
            task.setMerchantPhone(info.getMerchantPhone());
            task.setTransactionTime(info.getTransactionTime());
            task.setTotalAmount(info.getTotalAmount());
            task.setTaxAmount(info.getTaxAmount());
            task.setTipAmount(info.getTipAmount());
            task.setDiscountAmount(info.getDiscountAmount());
            task.setCurrency(info.getCurrency());
            task.setPaymentMethod(info.getPaymentMethod());
            task.setCardLastFour(info.getCardLastFour());
            task.setReceiptNumber(info.getReceiptNumber());
            task.setOrderNumber(info.getOrderNumber());
            task.setRecognizedCategory(info.getRecognizedCategory());

            if (CollUtil.isNotEmpty(info.getRecognizedTags())) {
                task.setRecognizedTagsJson(JSONUtil.toJsonStr(info.getRecognizedTags()));
            }
            if (CollUtil.isNotEmpty(info.getItems())) {
                task.setItemsJson(JSONUtil.toJsonStr(info.getItems()));
            }
            if (CollUtil.isNotEmpty(info.getConfidenceDetails())) {
                task.setConfidenceDetailsJson(JSONUtil.toJsonStr(info.getConfidenceDetails()));
            }
        }

        if (result.getRawResult() != null) {
            task.setRawResultJson(JSONUtil.toJsonStr(result.getRawResult()));
        }

        task.setUpdatedBy(LoginHelper.getUserId());
        task.setUpdateTime(LocalDateTime.now());

        ocrTaskMapper.updateById(task);
    }

    /**
     * 从识别结果创建账单
     */
    private Long createTransactionFromRecognition(OcrTask task, OcrReceiptResp result) {
        try {
            OcrReceiptResp.ReceiptInfo info = result.getReceiptInfo();
            if (info == null || info.getTotalAmount() == null) {
                return null;
            }

            TransactionCreateReq createReq = new TransactionCreateReq();
            createReq.setGroupId(task.getGroupId());
            createReq.setAmount(info.getTotalAmount());
            createReq.setType("EXPENSE"); // 默认为支出
            createReq.setCategoryId(task.getDefaultCategoryId());
            createReq.setDescription(info.getMerchantName());
            createReq.setTransactionTime(info.getTransactionTime() != null ? info.getTransactionTime() : LocalDateTime.now());
            createReq.setTags(info.getRecognizedTags());
            createReq.setRemark(task.getDefaultRemark());

            Long transactionId = transactionService.create(createReq);

            // 更新任务状态
            task.setTransactionCreated(true);
            task.setTransactionId(transactionId);

            return transactionId;
        } catch (Exception e) {
            log.error("从OCR识别结果创建账单失败", e);
            return null;
        }
    }

    /**
     * 根据任务编号获取任务
     */
    private OcrTask getTaskByNumber(String taskNumber) {
        QueryWrapper<OcrTask> wrapper = new QueryWrapper<>();
        wrapper.eq("task_number", taskNumber);
        wrapper.eq("deleted", false);

        OcrTask task = ocrTaskMapper.selectOne(wrapper);
        CheckUtils.throwIfNull(task, "任务不存在：{}", taskNumber);

        return task;
    }

    /**
     * 创建重试请求
     */
    private OcrReceiptReq createRetryRequest(OcrTask originalTask) {
        OcrReceiptReq request = new OcrReceiptReq();
        request.setOcrEngine(originalTask.getOcrEngine());
        request.setRecognitionMode(originalTask.getRecognitionMode());
        request.setLanguage(originalTask.getLanguage());
        request.setAccuracy(originalTask.getAccuracy());
        request.setAutoCreateTransaction(originalTask.getAutoCreateTransaction());
        request.setDefaultCategoryId(originalTask.getDefaultCategoryId());
        request.setDefaultRemark(originalTask.getDefaultRemark());
        request.setEnableSmartCategory(originalTask.getEnableSmartCategory());
        request.setEnableSmartTags(originalTask.getEnableSmartTags());
        request.setConfidenceThreshold(originalTask.getConfidenceThreshold());
        request.setSaveOriginalImage(originalTask.getSaveOriginalImage());
        request.setImageQuality(originalTask.getImageQuality());
        request.setMaxImageSize(originalTask.getMaxImageSize());
        request.setEnableTableRecognition(originalTask.getEnableTableRecognition());
        request.setEnableHandwritingRecognition(originalTask.getEnableHandwritingRecognition());
        request.setTimeoutSeconds(originalTask.getTimeoutSeconds());
        request.setRetryCount(originalTask.getRetryCount());
        request.setCallbackUrl(originalTask.getCallbackUrl());

        if (StrUtil.isNotBlank(originalTask.getDefaultTagsJson())) {
            request.setDefaultTags(JSONUtil.toList(originalTask.getDefaultTagsJson(), String.class));
        }

        // 使用原始图片路径
        request.setImageUrl(originalTask.getAccessUrl());

        return request;
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper<OcrTask> buildQueryWrapper(OcrTaskQuery query) {
        QueryWrapper<OcrTask> wrapper = new QueryWrapper<>();
        wrapper.eq("deleted", false);

        if (query.getGroupId() != null) {
            wrapper.eq("group_id", query.getGroupId());
        }
        if (StrUtil.isNotBlank(query.getTaskId())) {
            wrapper.eq("task_number", query.getTaskId());
        }
        if (StrUtil.isNotBlank(query.getStatus())) {
            wrapper.eq("status", query.getStatus());
        }
        if (StrUtil.isNotBlank(query.getOcrEngine())) {
            wrapper.eq("ocr_engine", query.getOcrEngine());
        }
        if (StrUtil.isNotBlank(query.getRecognitionMode())) {
            wrapper.eq("recognition_mode", query.getRecognitionMode());
        }
        if (query.getAutoCreateTransaction() != null) {
            wrapper.eq("auto_create_transaction", query.getAutoCreateTransaction());
        }
        if (query.getTransactionCreated() != null) {
            wrapper.eq("transaction_created", query.getTransactionCreated());
        }
        if (query.getTransactionId() != null) {
            wrapper.eq("transaction_id", query.getTransactionId());
        }
        if (StrUtil.isNotBlank(query.getMerchantName())) {
            wrapper.like("merchant_name", query.getMerchantName());
        }
        if (StrUtil.isNotBlank(query.getRecognizedCategory())) {
            wrapper.eq("recognized_category", query.getRecognizedCategory());
        }
        if (query.getMinConfidence() != null) {
            wrapper.ge("confidence", query.getMinConfidence());
        }
        if (query.getMaxConfidence() != null) {
            wrapper.le("confidence", query.getMaxConfidence());
        }
        if (query.getMinProcessingTime() != null) {
            wrapper.ge("processing_time", query.getMinProcessingTime());
        }
        if (query.getMaxProcessingTime() != null) {
            wrapper.le("processing_time", query.getMaxProcessingTime());
        }
        if (query.getRecognitionTimeStart() != null) {
            wrapper.ge("recognition_start_time", query.getRecognitionTimeStart());
        }
        if (query.getRecognitionTimeEnd() != null) {
            wrapper.le("recognition_end_time", query.getRecognitionTimeEnd());
        }
        if (query.getCreateTimeStart() != null) {
            wrapper.ge("create_time", query.getCreateTimeStart());
        }
        if (query.getCreateTimeEnd() != null) {
            wrapper.le("create_time", query.getCreateTimeEnd());
        }
        if (query.getCreatedBy() != null) {
            wrapper.eq("created_by", query.getCreatedBy());
        }
        if (StrUtil.isNotBlank(query.getOriginalFileName())) {
            wrapper.like("original_file_name", query.getOriginalFileName());
        }
        if (StrUtil.isNotBlank(query.getImageFormat())) {
            wrapper.eq("image_format", query.getImageFormat());
        }
        if (query.getMinFileSize() != null) {
            wrapper.ge("file_size", query.getMinFileSize());
        }
        if (query.getMaxFileSize() != null) {
            wrapper.le("file_size", query.getMaxFileSize());
        }
        if (StrUtil.isNotBlank(query.getErrorCode())) {
            wrapper.eq("error_code", query.getErrorCode());
        }
        if (query.getHasError() != null) {
            if (query.getHasError()) {
                wrapper.isNotNull("error_message");
            } else {
                wrapper.isNull("error_message");
            }
        }
        if (StrUtil.isNotBlank(query.getKeyword())) {
            wrapper.and(w -> w.like("merchant_name", query.getKeyword())
                    .or().like("recognized_category", query.getKeyword())
                    .or().like("original_file_name", query.getKeyword()));
        }
        if (query.getAsyncProcessing() != null) {
            wrapper.eq("async_processing", query.getAsyncProcessing());
        }
        if (StrUtil.isNotBlank(query.getLanguage())) {
            wrapper.eq("language", query.getLanguage());
        }
        if (StrUtil.isNotBlank(query.getAccuracy())) {
            wrapper.eq("accuracy", query.getAccuracy());
        }
        if (query.getEnableSmartCategory() != null) {
            wrapper.eq("enable_smart_category", query.getEnableSmartCategory());
        }
        if (query.getEnableSmartTags() != null) {
            wrapper.eq("enable_smart_tags", query.getEnableSmartTags());
        }
        if (query.getSaveOriginalImage() != null) {
            wrapper.eq("save_original_image", query.getSaveOriginalImage());
        }
        if (query.getEnableTableRecognition() != null) {
            wrapper.eq("enable_table_recognition", query.getEnableTableRecognition());
        }
        if (query.getEnableHandwritingRecognition() != null) {
            wrapper.eq("enable_handwriting_recognition", query.getEnableHandwritingRecognition());
        }

        // 排序
        if (StrUtil.isNotBlank(query.getSortField())) {
            String sortField = query.getSortField();
            boolean isAsc = "ASC".equalsIgnoreCase(query.getSortOrder());

            switch (sortField) {
                case "taskId":
                    wrapper.orderBy(true, isAsc, "task_number");
                    break;
                case "status":
                    wrapper.orderBy(true, isAsc, "status");
                    break;
                case "confidence":
                    wrapper.orderBy(true, isAsc, "confidence");
                    break;
                case "processingTime":
                    wrapper.orderBy(true, isAsc, "processing_time");
                    break;
                case "recognitionTime":
                    wrapper.orderBy(true, isAsc, "recognition_start_time");
                    break;
                case "createTime":
                    wrapper.orderBy(true, isAsc, "create_time");
                    break;
                default:
                    wrapper.orderByDesc("create_time");
                    break;
            }
        } else {
            wrapper.orderByDesc("create_time");
        }

        return wrapper;
    }

    /**
     * 转换为响应对象
     */
    private OcrReceiptResp convertToResponse(OcrTask task) {
        OcrReceiptResp response = new OcrReceiptResp();
        response.setTaskId(task.getTaskNumber());
        response.setStatus(task.getStatus());
        response.setProcessingTime(task.getProcessingTime());
        response.setConfidence(task.getConfidence());
        response.setErrorMessage(task.getErrorMessage());
        response.setErrorCode(task.getErrorCode());
        response.setTransactionCreated(task.getTransactionCreated());
        response.setTransactionId(task.getTransactionId());
        response.setRecognitionTime(task.getRecognitionStartTime());
        response.setOcrEngine(task.getOcrEngine());

        // 收据信息
        if (StrUtil.isNotBlank(task.getMerchantName()) || task.getTotalAmount() != null) {
            OcrReceiptResp.ReceiptInfo receiptInfo = new OcrReceiptResp.ReceiptInfo();
            receiptInfo.setMerchantName(task.getMerchantName());
            receiptInfo.setMerchantAddress(task.getMerchantAddress());
            receiptInfo.setMerchantPhone(task.getMerchantPhone());
            receiptInfo.setTransactionTime(task.getTransactionTime());
            receiptInfo.setTotalAmount(task.getTotalAmount());
            receiptInfo.setTaxAmount(task.getTaxAmount());
            receiptInfo.setTipAmount(task.getTipAmount());
            receiptInfo.setDiscountAmount(task.getDiscountAmount());
            receiptInfo.setCurrency(task.getCurrency());
            receiptInfo.setPaymentMethod(task.getPaymentMethod());
            receiptInfo.setCardLastFour(task.getCardLastFour());
            receiptInfo.setReceiptNumber(task.getReceiptNumber());
            receiptInfo.setOrderNumber(task.getOrderNumber());
            receiptInfo.setRecognizedCategory(task.getRecognizedCategory());

            if (StrUtil.isNotBlank(task.getRecognizedTagsJson())) {
                receiptInfo.setRecognizedTags(JSONUtil.toList(task.getRecognizedTagsJson(), String.class));
            }
            if (StrUtil.isNotBlank(task.getConfidenceDetailsJson())) {
                receiptInfo.setConfidenceDetails(JSONUtil.toBean(task.getConfidenceDetailsJson(), Map.class));
            }

            response.setReceiptInfo(receiptInfo);
        }

        // 图片信息
        if (StrUtil.isNotBlank(task.getOriginalFileName()) || task.getFileSize() != null) {
            OcrReceiptResp.ImageInfo imageInfo = new OcrReceiptResp.ImageInfo();
            imageInfo.setOriginalFileName(task.getOriginalFileName());
            imageInfo.setFileSize(task.getFileSize());
            imageInfo.setWidth(task.getImageWidth());
            imageInfo.setHeight(task.getImageHeight());
            imageInfo.setFormat(task.getImageFormat());
            imageInfo.setStoragePath(task.getStoragePath());
            imageInfo.setAccessUrl(task.getAccessUrl());
            imageInfo.setThumbnailUrl(task.getThumbnailUrl());

            response.setImageInfo(imageInfo);
        }

        return response;
    }
}
