package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 账单统计响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "账单统计响应")
public class TransactionStatisticsResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 总收入
     */
    @Schema(description = "总收入", example = "5000.00")
    private BigDecimal totalIncome;

    /**
     * 总支出
     */
    @Schema(description = "总支出", example = "3000.00")
    private BigDecimal totalExpense;

    /**
     * 净收入（收入-支出）
     */
    @Schema(description = "净收入", example = "2000.00")
    private BigDecimal netIncome;

    /**
     * 交易笔数
     */
    @Schema(description = "交易笔数", example = "150")
    private Integer transactionCount;

    /**
     * 平均交易金额
     */
    @Schema(description = "平均交易金额", example = "53.33")
    private BigDecimal averageAmount;

    /**
     * 按币种统计
     */
    @Schema(description = "按币种统计")
    private Map<String, CurrencyStatistics> currencyStatistics;

    /**
     * 按分类统计
     */
    @Schema(description = "按分类统计")
    private List<CategoryStatistics> categoryStatistics;

    /**
     * 按日期统计
     */
    @Schema(description = "按日期统计")
    private List<DateStatistics> dateStatistics;

    /**
     * 按用户统计
     */
    @Schema(description = "按用户统计")
    private List<UserStatistics> userStatistics;

    /**
     * 币种统计
     */
    @Data
    @Schema(description = "币种统计")
    public static class CurrencyStatistics implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 币种
         */
        @Schema(description = "币种", example = "CNY")
        private String currency;

        /**
         * 总收入
         */
        @Schema(description = "总收入", example = "5000.00")
        private BigDecimal totalIncome;

        /**
         * 总支出
         */
        @Schema(description = "总支出", example = "3000.00")
        private BigDecimal totalExpense;

        /**
         * 净收入
         */
        @Schema(description = "净收入", example = "2000.00")
        private BigDecimal netIncome;

        /**
         * 交易笔数
         */
        @Schema(description = "交易笔数", example = "100")
        private Integer transactionCount;
    }

    /**
     * 分类统计
     */
    @Data
    @Schema(description = "分类统计")
    public static class CategoryStatistics implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 分类
         */
        @Schema(description = "分类", example = "餐饮")
        private String category;

        /**
         * 总金额
         */
        @Schema(description = "总金额", example = "1500.00")
        private BigDecimal totalAmount;

        /**
         * 交易笔数
         */
        @Schema(description = "交易笔数", example = "30")
        private Integer transactionCount;

        /**
         * 占比
         */
        @Schema(description = "占比", example = "25.5")
        private BigDecimal percentage;
    }

    /**
     * 日期统计
     */
    @Data
    @Schema(description = "日期统计")
    public static class DateStatistics implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 日期
         */
        @Schema(description = "日期", example = "2025-01-01")
        private String date;

        /**
         * 收入
         */
        @Schema(description = "收入", example = "200.00")
        private BigDecimal income;

        /**
         * 支出
         */
        @Schema(description = "支出", example = "150.00")
        private BigDecimal expense;

        /**
         * 净收入
         */
        @Schema(description = "净收入", example = "50.00")
        private BigDecimal netIncome;

        /**
         * 交易笔数
         */
        @Schema(description = "交易笔数", example = "5")
        private Integer transactionCount;
    }

    /**
     * 用户统计
     */
    @Data
    @Schema(description = "用户统计")
    public static class UserStatistics implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 用户ID
         */
        @Schema(description = "用户ID", example = "1")
        private Long userId;

        /**
         * 用户姓名
         */
        @Schema(description = "用户姓名", example = "张三")
        private String userName;

        /**
         * 总收入
         */
        @Schema(description = "总收入", example = "2000.00")
        private BigDecimal totalIncome;

        /**
         * 总支出
         */
        @Schema(description = "总支出", example = "1500.00")
        private BigDecimal totalExpense;

        /**
         * 净收入
         */
        @Schema(description = "净收入", example = "500.00")
        private BigDecimal netIncome;

        /**
         * 交易笔数
         */
        @Schema(description = "交易笔数", example = "50")
        private Integer transactionCount;
    }
}
