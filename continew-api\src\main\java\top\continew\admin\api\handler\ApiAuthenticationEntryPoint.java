package top.continew.admin.api.handler;

import cn.hutool.json.JSONUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;
import top.continew.starter.web.model.R;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * API认证入口点
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
public class ApiAuthenticationEntryPoint implements AuthenticationEntryPoint {

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                        AuthenticationException authException) throws IOException {
        
        log.warn("API认证失败: {}, URI: {}", authException.getMessage(), request.getRequestURI());
        
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());

        R<Void> result = R.fail(HttpStatus.UNAUTHORIZED.value(), "认证失败，请检查API密钥");
        response.getWriter().write(JSONUtil.toJsonStr(result));
    }
}
