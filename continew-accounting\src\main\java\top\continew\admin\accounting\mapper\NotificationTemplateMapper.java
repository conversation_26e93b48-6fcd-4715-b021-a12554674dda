package top.continew.admin.accounting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.accounting.model.entity.NotificationTemplateDO;

import java.util.List;
import java.util.Map;

/**
 * 通知模板 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface NotificationTemplateMapper extends BaseMapper<NotificationTemplateDO> {

    // ==================== 模板查询 ====================

    /**
     * 根据代码查询模板
     *
     * @param templateCode 模板代码
     * @return 模板信息
     */
    NotificationTemplateDO selectByCode(@Param("templateCode") String templateCode);

    /**
     * 查询启用的模板列表
     *
     * @param groupId 群组ID
     * @param category 模板分类
     * @return 模板列表
     */
    List<NotificationTemplateDO> selectEnabledTemplates(@Param("groupId") Long groupId, 
                                                       @Param("category") String category);

    /**
     * 分页查询模板
     *
     * @param groupId 群组ID
     * @param category 模板分类
     * @param keyword 关键词
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 模板列表
     */
    List<NotificationTemplateDO> selectTemplatePageList(@Param("groupId") Long groupId,
                                                       @Param("category") String category,
                                                       @Param("keyword") String keyword,
                                                       @Param("offset") Integer offset,
                                                       @Param("limit") Integer limit);

    /**
     * 统计模板数量
     *
     * @param groupId 群组ID
     * @param category 模板分类
     * @param keyword 关键词
     * @return 模板数量
     */
    Integer countTemplates(@Param("groupId") Long groupId,
                          @Param("category") String category,
                          @Param("keyword") String keyword);

    // ==================== 模板分类 ====================

    /**
     * 查询模板分类列表
     *
     * @param groupId 群组ID
     * @return 分类列表
     */
    List<Map<String, Object>> selectTemplateCategories(@Param("groupId") Long groupId);

    /**
     * 检查分类是否存在
     *
     * @param groupId 群组ID
     * @param categoryName 分类名称
     * @return 是否存在
     */
    Boolean existsCategory(@Param("groupId") Long groupId, @Param("categoryName") String categoryName);

    // ==================== 模板统计 ====================

    /**
     * 查询模板使用统计
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 使用统计
     */
    List<Map<String, Object>> selectTemplateUsageStatistics(@Param("groupId") Long groupId,
                                                           @Param("startDate") String startDate,
                                                           @Param("endDate") String endDate);

    /**
     * 查询热门模板
     *
     * @param groupId 群组ID
     * @param limit 限制数量
     * @return 热门模板列表
     */
    List<Map<String, Object>> selectPopularTemplates(@Param("groupId") Long groupId, 
                                                    @Param("limit") Integer limit);

    /**
     * 查询模板性能统计
     *
     * @param templateCode 模板代码
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 性能统计
     */
    Map<String, Object> selectTemplatePerformanceStatistics(@Param("templateCode") String templateCode,
                                                           @Param("startDate") String startDate,
                                                           @Param("endDate") String endDate);

    /**
     * 查询模板渠道使用统计
     *
     * @param templateCode 模板代码
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 渠道使用统计
     */
    List<Map<String, Object>> selectTemplateChannelUsage(@Param("templateCode") String templateCode,
                                                        @Param("startDate") String startDate,
                                                        @Param("endDate") String endDate);

    // ==================== 模板验证 ====================

    /**
     * 检查模板代码是否存在
     *
     * @param templateCode 模板代码
     * @param excludeId 排除的模板ID
     * @return 是否存在
     */
    Boolean existsTemplateCode(@Param("templateCode") String templateCode, 
                              @Param("excludeId") Long excludeId);

    /**
     * 检查模板名称是否存在
     *
     * @param groupId 群组ID
     * @param templateName 模板名称
     * @param excludeId 排除的模板ID
     * @return 是否存在
     */
    Boolean existsTemplateName(@Param("groupId") Long groupId,
                              @Param("templateName") String templateName,
                              @Param("excludeId") Long excludeId);

    // ==================== 模板版本 ====================

    /**
     * 查询模板版本列表
     *
     * @param templateId 模板ID
     * @return 版本列表
     */
    List<Map<String, Object>> selectTemplateVersions(@Param("templateId") Long templateId);

    /**
     * 查询模板版本详情
     *
     * @param versionId 版本ID
     * @return 版本详情
     */
    Map<String, Object> selectTemplateVersionDetail(@Param("versionId") Long versionId);

    /**
     * 删除模板版本
     *
     * @param templateId 模板ID
     * @return 删除数量
     */
    Integer deleteTemplateVersions(@Param("templateId") Long templateId);

    // ==================== 批量操作 ====================

    /**
     * 批量更新模板状态
     *
     * @param templateIds 模板ID列表
     * @param enabled 是否启用
     * @param updateUser 更新用户
     * @return 更新数量
     */
    Integer batchUpdateTemplateStatus(@Param("templateIds") List<Long> templateIds,
                                     @Param("enabled") Boolean enabled,
                                     @Param("updateUser") Long updateUser);

    /**
     * 批量删除模板
     *
     * @param templateIds 模板ID列表
     * @return 删除数量
     */
    Integer batchDeleteTemplates(@Param("templateIds") List<Long> templateIds);

    // ==================== 模板导入导出 ====================

    /**
     * 查询导出模板数据
     *
     * @param templateIds 模板ID列表
     * @return 模板数据
     */
    List<Map<String, Object>> selectExportTemplateData(@Param("templateIds") List<Long> templateIds);

    /**
     * 查询模板依赖关系
     *
     * @param templateId 模板ID
     * @return 依赖关系
     */
    List<Map<String, Object>> selectTemplateDependencies(@Param("templateId") Long templateId);

    // ==================== 模板缓存 ====================

    /**
     * 查询需要缓存的模板
     *
     * @param groupId 群组ID
     * @return 模板列表
     */
    List<NotificationTemplateDO> selectCacheableTemplates(@Param("groupId") Long groupId);

    /**
     * 查询模板最后更新时间
     *
     * @param templateCode 模板代码
     * @return 最后更新时间
     */
    String selectTemplateLastUpdateTime(@Param("templateCode") String templateCode);

    // ==================== 模板监控 ====================

    /**
     * 查询模板健康状态
     *
     * @param groupId 群组ID
     * @return 健康状态
     */
    List<Map<String, Object>> selectTemplateHealthStatus(@Param("groupId") Long groupId);

    /**
     * 查询异常模板
     *
     * @param groupId 群组ID
     * @param hours 小时数
     * @return 异常模板列表
     */
    List<Map<String, Object>> selectAbnormalTemplates(@Param("groupId") Long groupId, 
                                                     @Param("hours") Integer hours);

    /**
     * 查询未使用的模板
     *
     * @param groupId 群组ID
     * @param days 天数
     * @return 未使用的模板列表
     */
    List<Map<String, Object>> selectUnusedTemplates(@Param("groupId") Long groupId, 
                                                   @Param("days") Integer days);

}
