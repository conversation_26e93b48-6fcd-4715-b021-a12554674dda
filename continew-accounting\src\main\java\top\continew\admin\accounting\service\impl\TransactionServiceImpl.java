package top.continew.admin.accounting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.accounting.enums.SplitType;
import top.continew.admin.accounting.enums.TransactionType;
import top.continew.admin.accounting.mapper.TransactionMapper;
import top.continew.admin.accounting.mapper.TransactionSplitMapper;
import top.continew.admin.accounting.model.entity.TransactionDO;
import top.continew.admin.accounting.model.entity.TransactionSplitDO;
import top.continew.admin.accounting.model.query.TransactionQuery;
import top.continew.admin.accounting.model.req.TransactionCreateReq;
import top.continew.admin.accounting.model.req.TransactionUpdateReq;
import top.continew.admin.accounting.model.resp.TransactionDetailResp;
import top.continew.admin.accounting.model.resp.TransactionListResp;
import top.continew.admin.accounting.model.resp.TransactionStatisticsResp;
import top.continew.admin.accounting.event.TransactionCreatedEvent;
import top.continew.admin.accounting.event.TransactionDeletedEvent;
import top.continew.admin.accounting.event.TransactionUpdatedEvent;
import top.continew.admin.accounting.service.GroupService;
import top.continew.admin.accounting.service.TransactionService;
import top.continew.admin.accounting.service.WalletService;
import top.continew.admin.common.base.service.BaseServiceImpl;
import top.continew.starter.core.util.validation.CheckUtils;
import top.continew.starter.security.context.SecurityContextHolder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 账单管理业务实现
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransactionServiceImpl extends BaseServiceImpl<TransactionMapper, TransactionDO, TransactionListResp, TransactionDetailResp, TransactionQuery, TransactionCreateReq> implements TransactionService {

    private final TransactionSplitMapper transactionSplitMapper;
    private final GroupService groupService;
    private final WalletService walletService;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public void beforeCreate(TransactionCreateReq req) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查群组权限
        CheckUtils.throwIf(!groupService.isMember(req.getGroupId(), userId), "您不是该群组成员");
        
        // 检查交易次数限制
        CheckUtils.throwIf(groupService.isTransactionLimitExceeded(req.getGroupId()), "已达到当月交易次数限制");
        
        // 设置默认交易时间
        if (req.getTransactionDate() == null) {
            req.setTransactionDate(LocalDateTime.now());
        }
        
        // 验证分摊信息
        this.validateSplitInfo(req);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(TransactionCreateReq req) {
        // 创建账单
        Long transactionId = super.create(req);

        // 更新钱包余额
        walletService.updateBalance(req.getGroupId(), req.getCurrency(), req.getAmount(), req.getType());

        // 处理分摊信息
        this.handleSplitInfo(transactionId, req.getSplitType(), req.getSplitParticipants(), req.getAmount());

        // 发布交易创建事件
        TransactionDO transaction = super.getById(transactionId);
        if (transaction != null) {
            TransactionCreatedEvent event = new TransactionCreatedEvent(
                    this,
                    transactionId,
                    req.getGroupId(),
                    req.getAmount(),
                    req.getDescription(),
                    req.getCategoryId(),
                    null, // categoryName - 可以从分类服务获取
                    req.getWalletId(),
                    null, // walletName - 可以从钱包服务获取
                    req.getType().name(),
                    transaction.getCreateTime(),
                    transaction.getCreateUser(),
                    req.getTags(),
                    req.getSplitParticipants()
            );
            eventPublisher.publishEvent(event);
        }

        log.info("创建账单成功，ID: {}, 群组: {}, 金额: {}", transactionId, req.getGroupId(), req.getAmount());
        return transactionId;
    }

    @Override
    public void beforeUpdate(TransactionUpdateReq req, Long id) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查操作权限
        CheckUtils.throwIf(!baseMapper.hasPermission(id, userId), "您没有权限修改此账单");
        
        // 验证分摊信息
        if (req.getSplitType() != null || CollUtil.isNotEmpty(req.getSplitParticipants())) {
            this.validateSplitInfo(req);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TransactionUpdateReq req, Long id) {
        TransactionDO transaction = super.getById(id);
        CheckUtils.throwIfNull(transaction, "账单不存在");

        // 保存原始数据用于事件发布和钱包余额更新
        BigDecimal oldAmount = transaction.getAmount();
        String oldDescription = transaction.getDescription();
        Long oldCategoryId = transaction.getCategoryId();
        TransactionType oldType = transaction.getType();
        String oldCurrency = transaction.getCurrency();

        // 更新账单基本信息
        BeanUtil.copyProperties(req, transaction, "id", "groupId", "createUser", "createTime");
        super.updateById(transaction);

        // 更新钱包余额（如果金额、类型或币种发生变化）
        if ((req.getAmount() != null && !req.getAmount().equals(oldAmount)) ||
            (req.getType() != null && !req.getType().equals(oldType)) ||
            (req.getCurrency() != null && !req.getCurrency().equals(oldCurrency))) {

            // 先撤销原交易对钱包的影响
            TransactionType reverseType = oldType == TransactionType.INCOME ? TransactionType.EXPENSE : TransactionType.INCOME;
            walletService.updateBalance(transaction.getGroupId(), oldCurrency, oldAmount, reverseType);

            // 再应用新交易对钱包的影响
            BigDecimal newAmount = req.getAmount() != null ? req.getAmount() : oldAmount;
            TransactionType newType = req.getType() != null ? req.getType() : oldType;
            String newCurrency = req.getCurrency() != null ? req.getCurrency() : oldCurrency;
            walletService.updateBalance(transaction.getGroupId(), newCurrency, newAmount, newType);
        }

        // 处理分摊信息更新
        if (req.getSplitType() != null || CollUtil.isNotEmpty(req.getSplitParticipants())) {
            // 删除原有分摊记录
            transactionSplitMapper.deleteByTransactionId(id);

            // 创建新的分摊记录
            BigDecimal amount = req.getAmount() != null ? req.getAmount() : transaction.getAmount();
            this.handleSplitInfo(id, req.getSplitType(), req.getSplitParticipants(), amount);
        }

        // 发布交易更新事件
        List<String> changeFields = new ArrayList<>();
        if (req.getAmount() != null && !req.getAmount().equals(oldAmount)) {
            changeFields.add("amount");
        }
        if (req.getDescription() != null && !req.getDescription().equals(oldDescription)) {
            changeFields.add("description");
        }
        if (req.getCategoryId() != null && !req.getCategoryId().equals(oldCategoryId)) {
            changeFields.add("categoryId");
        }

        if (!changeFields.isEmpty()) {
            TransactionUpdatedEvent event = new TransactionUpdatedEvent(
                    this,
                    id,
                    transaction.getGroupId(),
                    oldAmount,
                    transaction.getAmount(),
                    oldDescription,
                    transaction.getDescription(),
                    oldCategoryId,
                    transaction.getCategoryId(),
                    LocalDateTime.now(),
                    SecurityContextHolder.getUserId(),
                    changeFields
            );
            eventPublisher.publishEvent(event);
        }

        log.info("更新账单成功，ID: {}", id);
    }

    @Override
    public TransactionStatisticsResp getStatistics(Long groupId, LocalDate startDate, LocalDate endDate) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查群组权限
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组成员");
        
        TransactionStatisticsResp statistics = new TransactionStatisticsResp();
        
        // 基础统计
        TransactionStatisticsResp.CurrencyStatistics basicStats = baseMapper.selectGroupStatistics(groupId, startDate, endDate);
        if (basicStats != null) {
            statistics.setTotalIncome(basicStats.getTotalIncome());
            statistics.setTotalExpense(basicStats.getTotalExpense());
            statistics.setTransactionCount(basicStats.getTransactionCount());
            statistics.setAverageAmount(basicStats.getAverageAmount());
            statistics.setNetIncome(basicStats.getTotalIncome().subtract(basicStats.getTotalExpense()));
        }
        
        // 分类统计
        List<TransactionStatisticsResp.CategoryStatistics> categoryStats = baseMapper.selectCategoryStatistics(groupId, startDate, endDate);
        this.calculateCategoryPercentages(categoryStats, statistics.getTotalExpense());
        statistics.setCategoryStatistics(categoryStats);
        
        // 日期统计
        List<TransactionStatisticsResp.DateStatistics> dateStats = baseMapper.selectDateStatistics(groupId, startDate, endDate);
        this.calculateDateNetIncome(dateStats);
        statistics.setDateStatistics(dateStats);
        
        // 用户统计
        List<TransactionStatisticsResp.UserStatistics> userStats = baseMapper.selectUserStatistics(groupId, startDate, endDate);
        this.calculateUserNetIncome(userStats);
        statistics.setUserStatistics(userStats);
        
        // 币种统计（暂时只支持单币种）
        Map<String, TransactionStatisticsResp.CurrencyStatistics> currencyMap = new HashMap<>();
        currencyMap.put("CNY", basicStats);
        statistics.setCurrencyStatistics(currencyMap);
        
        return statistics;
    }

    @Override
    public List<TransactionListResp> getUserTransactions(Long groupId, Long userId) {
        // 检查群组权限
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组成员");
        
        return baseMapper.selectUserTransactions(groupId, userId);
    }

    @Override
    public BigDecimal calculateUserBalance(Long groupId, Long userId, String currency) {
        // 检查群组权限
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组成员");
        
        return baseMapper.calculateUserBalance(groupId, userId, currency);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTransaction(Long id, Long userId) {
        // 检查操作权限
        CheckUtils.throwIf(!baseMapper.hasPermission(id, userId), "您没有权限删除此账单");

        // 获取交易信息用于事件发布和钱包余额更新
        TransactionDO transaction = super.getById(id);
        CheckUtils.throwIfNull(transaction, "账单不存在");

        // 撤销交易对钱包余额的影响
        TransactionType reverseType = transaction.getType() == TransactionType.INCOME ? TransactionType.EXPENSE : TransactionType.INCOME;
        walletService.updateBalance(transaction.getGroupId(), transaction.getCurrency(), transaction.getAmount(), reverseType);

        // 删除分摊记录
        transactionSplitMapper.deleteByTransactionId(id);

        // 软删除账单
        super.removeById(id);

        // 发布交易删除事件
        TransactionDeletedEvent event = new TransactionDeletedEvent(
                this,
                id,
                transaction.getGroupId(),
                transaction.getAmount(),
                transaction.getDescription(),
                transaction.getCategoryId(),
                null, // categoryName - 可以从分类服务获取
                transaction.getWalletId(),
                null, // walletName - 可以从钱包服务获取
                transaction.getType().name(),
                LocalDateTime.now(),
                userId,
                "用户删除"
        );
        eventPublisher.publishEvent(event);

        log.info("删除账单成功，ID: {}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResult batchImport(Long groupId, List<TransactionCreateReq> transactions, Long userId) {
        // 检查群组权限
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组成员");
        
        ImportResult result = new ImportResult();
        result.setTotalCount(transactions.size());
        result.setErrors(new ArrayList<>());
        
        int successCount = 0;
        for (int i = 0; i < transactions.size(); i++) {
            try {
                TransactionCreateReq req = transactions.get(i);
                req.setGroupId(groupId);
                this.create(req);
                successCount++;
            } catch (Exception e) {
                result.getErrors().add(String.format("第%d行导入失败: %s", i + 1, e.getMessage()));
                log.warn("批量导入账单失败，行号: {}, 错误: {}", i + 1, e.getMessage());
            }
        }
        
        result.setSuccessCount(successCount);
        result.setFailureCount(transactions.size() - successCount);
        
        log.info("批量导入账单完成，总数: {}, 成功: {}, 失败: {}", 
                result.getTotalCount(), result.getSuccessCount(), result.getFailureCount());
        
        return result;
    }

    /**
     * 验证分摊信息
     */
    private void validateSplitInfo(Object req) {
        SplitType splitType = null;
        List<TransactionCreateReq.SplitParticipant> splitParticipants = null;
        BigDecimal amount = null;
        
        if (req instanceof TransactionCreateReq createReq) {
            splitType = createReq.getSplitType();
            splitParticipants = createReq.getSplitParticipants();
            amount = createReq.getAmount();
        } else if (req instanceof TransactionUpdateReq updateReq) {
            splitType = updateReq.getSplitType();
            splitParticipants = updateReq.getSplitParticipants();
            // amount 在更新时可能为空，需要从数据库获取
        }
        
        if (splitType == null || CollUtil.isEmpty(splitParticipants)) {
            return;
        }
        
        CheckUtils.throwIf(splitParticipants.size() < 2, "分摊至少需要2个参与者");
        
        if (splitType == SplitType.FIXED_AMOUNT) {
            // 固定金额分摊：验证总金额
            BigDecimal totalSplitAmount = splitParticipants.stream()
                .map(TransactionCreateReq.SplitParticipant::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            if (amount != null) {
                CheckUtils.throwIf(totalSplitAmount.compareTo(amount) != 0, "分摊总金额与账单金额不匹配");
            }
        } else if (splitType == SplitType.PERCENTAGE) {
            // 百分比分摊：验证总比例
            BigDecimal totalPercentage = splitParticipants.stream()
                .map(TransactionCreateReq.SplitParticipant::getPercentage)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            CheckUtils.throwIf(totalPercentage.compareTo(new BigDecimal("100")) != 0, "分摊比例总和必须为100%");
        }
    }

    /**
     * 处理分摊信息
     */
    private void handleSplitInfo(Long transactionId, SplitType splitType, 
                                List<TransactionCreateReq.SplitParticipant> splitParticipants, 
                                BigDecimal totalAmount) {
        if (splitType == null || CollUtil.isEmpty(splitParticipants)) {
            return;
        }
        
        List<TransactionSplitDO> splitRecords = new ArrayList<>();
        
        if (splitType == SplitType.EQUAL) {
            // 平均分摊
            BigDecimal splitAmount = totalAmount.divide(new BigDecimal(splitParticipants.size()), 2, RoundingMode.HALF_UP);
            for (TransactionCreateReq.SplitParticipant participant : splitParticipants) {
                TransactionSplitDO split = new TransactionSplitDO();
                split.setTransactionId(transactionId);
                split.setUserId(participant.getUserId());
                split.setAmount(splitAmount);
                split.setPercentage(new BigDecimal("100").divide(new BigDecimal(splitParticipants.size()), 2, RoundingMode.HALF_UP));
                split.setSettled(false);
                split.setStatus(1);
                splitRecords.add(split);
            }
        } else if (splitType == SplitType.FIXED_AMOUNT) {
            // 固定金额分摊
            for (TransactionCreateReq.SplitParticipant participant : splitParticipants) {
                TransactionSplitDO split = new TransactionSplitDO();
                split.setTransactionId(transactionId);
                split.setUserId(participant.getUserId());
                split.setAmount(participant.getAmount());
                split.setPercentage(participant.getAmount().multiply(new BigDecimal("100")).divide(totalAmount, 2, RoundingMode.HALF_UP));
                split.setSettled(false);
                split.setStatus(1);
                splitRecords.add(split);
            }
        } else if (splitType == SplitType.PERCENTAGE) {
            // 百分比分摊
            for (TransactionCreateReq.SplitParticipant participant : splitParticipants) {
                TransactionSplitDO split = new TransactionSplitDO();
                split.setTransactionId(transactionId);
                split.setUserId(participant.getUserId());
                split.setPercentage(participant.getPercentage());
                split.setAmount(totalAmount.multiply(participant.getPercentage()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
                split.setSettled(false);
                split.setStatus(1);
                splitRecords.add(split);
            }
        }
        
        // 批量保存分摊记录
        if (CollUtil.isNotEmpty(splitRecords)) {
            for (TransactionSplitDO split : splitRecords) {
                transactionSplitMapper.insert(split);
            }
        }
    }

    /**
     * 计算分类统计百分比
     */
    private void calculateCategoryPercentages(List<TransactionStatisticsResp.CategoryStatistics> categoryStats, BigDecimal totalExpense) {
        if (CollUtil.isEmpty(categoryStats) || totalExpense.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        
        for (TransactionStatisticsResp.CategoryStatistics stat : categoryStats) {
            BigDecimal percentage = stat.getTotalAmount()
                .multiply(new BigDecimal("100"))
                .divide(totalExpense, 2, RoundingMode.HALF_UP);
            stat.setPercentage(percentage);
        }
    }

    /**
     * 计算日期统计净收入
     */
    private void calculateDateNetIncome(List<TransactionStatisticsResp.DateStatistics> dateStats) {
        if (CollUtil.isEmpty(dateStats)) {
            return;
        }
        
        for (TransactionStatisticsResp.DateStatistics stat : dateStats) {
            stat.setNetIncome(stat.getIncome().subtract(stat.getExpense()));
        }
    }

    /**
     * 计算用户统计净收入
     */
    private void calculateUserNetIncome(List<TransactionStatisticsResp.UserStatistics> userStats) {
        if (CollUtil.isEmpty(userStats)) {
            return;
        }
        
        for (TransactionStatisticsResp.UserStatistics stat : userStats) {
            stat.setNetIncome(stat.getTotalIncome().subtract(stat.getTotalExpense()));
        }
    }
}
