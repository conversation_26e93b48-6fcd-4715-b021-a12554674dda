package top.continew.admin.bot.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.bot.model.entity.CommandHistoryDO;
import top.continew.starter.data.mybatis.plus.base.BaseMapper;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 命令历史记录 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface CommandHistoryMapper extends BaseMapper<CommandHistoryDO> {

    /**
     * 查询用户命令历史
     */
    List<CommandHistoryDO> selectUserHistory(@Param("userId") Long userId, 
                                           @Param("groupId") Long groupId, 
                                           @Param("limit") int limit);

    /**
     * 查询群组命令历史
     */
    List<CommandHistoryDO> selectGroupHistory(@Param("groupId") Long groupId, 
                                            @Param("limit") int limit);

    /**
     * 查询命令统计信息
     */
    Map<String, Object> selectCommandStatistics(@Param("userId") Long userId, 
                                               @Param("groupId") Long groupId,
                                               @Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime);

    /**
     * 查询最常用的命令
     */
    List<String> selectMostUsedCommands(@Param("userId") Long userId, 
                                      @Param("groupId") Long groupId, 
                                      @Param("limit") int limit);

    /**
     * 查询失败的命令
     */
    List<CommandHistoryDO> selectFailedCommands(@Param("userId") Long userId, 
                                              @Param("groupId") Long groupId, 
                                              @Param("limit") int limit);

    /**
     * 删除过期历史记录
     */
    int deleteExpiredHistory(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 按时间范围查询历史记录
     */
    List<CommandHistoryDO> selectHistoryByTimeRange(@Param("userId") Long userId, 
                                                  @Param("groupId") Long groupId,
                                                  @Param("startTime") LocalDateTime startTime,
                                                  @Param("endTime") LocalDateTime endTime);
}
