package top.continew.admin.bot.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 命令执行结果
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "命令执行结果")
public class CommandExecutionResult {

    /**
     * 是否成功
     */
    @Schema(description = "是否成功")
    private Boolean success;

    /**
     * 结果消息
     */
    @Schema(description = "结果消息")
    private String message;

    /**
     * 错误代码
     */
    @Schema(description = "错误代码")
    private String errorCode;

    /**
     * 执行时间
     */
    @Schema(description = "执行时间")
    private LocalDateTime executionTime;

    /**
     * 执行耗时（毫秒）
     */
    @Schema(description = "执行耗时（毫秒）")
    private Long executionDuration;

    /**
     * 关联的业务ID（如交易ID）
     */
    @Schema(description = "关联的业务ID")
    private Long businessId;

    /**
     * 原始命令
     */
    @Schema(description = "原始命令")
    private String originalCommand;

    /**
     * 解析后的命令
     */
    @Schema(description = "解析后的命令")
    private ParsedCommand parsedCommand;

    /**
     * 额外数据
     */
    @Schema(description = "额外数据")
    private Object data;

    /**
     * 创建成功结果
     */
    public static CommandExecutionResult success(String message, Long businessId) {
        CommandExecutionResult result = new CommandExecutionResult();
        result.setSuccess(true);
        result.setMessage(message);
        result.setBusinessId(businessId);
        result.setExecutionTime(LocalDateTime.now());
        return result;
    }

    /**
     * 创建成功结果（带数据）
     */
    public static CommandExecutionResult success(String message, Long businessId, Object data) {
        CommandExecutionResult result = success(message, businessId);
        result.setData(data);
        return result;
    }

    /**
     * 创建失败结果
     */
    public static CommandExecutionResult failure(String message) {
        CommandExecutionResult result = new CommandExecutionResult();
        result.setSuccess(false);
        result.setMessage(message);
        result.setExecutionTime(LocalDateTime.now());
        return result;
    }

    /**
     * 创建失败结果（带错误代码）
     */
    public static CommandExecutionResult failure(String message, String errorCode) {
        CommandExecutionResult result = failure(message);
        result.setErrorCode(errorCode);
        return result;
    }

    /**
     * 设置执行耗时
     */
    public CommandExecutionResult withDuration(long startTime) {
        this.executionDuration = System.currentTimeMillis() - startTime;
        return this;
    }

    /**
     * 设置原始命令
     */
    public CommandExecutionResult withOriginalCommand(String command) {
        this.originalCommand = command;
        return this;
    }

    /**
     * 设置解析后的命令
     */
    public CommandExecutionResult withParsedCommand(ParsedCommand command) {
        this.parsedCommand = command;
        return this;
    }
}
