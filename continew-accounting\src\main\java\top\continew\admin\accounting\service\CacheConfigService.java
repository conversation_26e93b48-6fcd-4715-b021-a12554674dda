package top.continew.admin.accounting.service;

import top.continew.admin.accounting.model.entity.CacheConfigDO;
import top.continew.admin.accounting.model.req.CacheConfigReq;
import top.continew.admin.accounting.model.req.CacheQueryReq;
import top.continew.starter.extension.crud.service.BaseService;

/**
 * 缓存配置服务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface CacheConfigService extends BaseService<CacheConfigDO, Long, CacheQueryReq, CacheConfigReq> {

    /**
     * 根据配置代码获取缓存配置
     *
     * @param configCode 配置代码
     * @return 缓存配置
     */
    CacheConfigDO getByConfigCode(String configCode);

    /**
     * 根据配置代码和群组ID获取缓存配置
     *
     * @param configCode 配置代码
     * @param groupId    群组ID
     * @return 缓存配置
     */
    CacheConfigDO getByConfigCodeAndGroupId(String configCode, Long groupId);

    /**
     * 检查配置代码是否存在
     *
     * @param configCode 配置代码
     * @param excludeId  排除的ID
     * @param groupId    群组ID
     * @return 是否存在
     */
    Boolean existsByConfigCode(String configCode, Long excludeId, Long groupId);

    /**
     * 应用缓存配置
     *
     * @param configId 配置ID
     * @return 是否成功
     */
    Boolean applyCacheConfig(Long configId);

    /**
     * 启用缓存配置
     *
     * @param configId 配置ID
     * @return 是否成功
     */
    Boolean enableCacheConfig(Long configId);

    /**
     * 禁用缓存配置
     *
     * @param configId 配置ID
     * @return 是否成功
     */
    Boolean disableCacheConfig(Long configId);

    /**
     * 复制缓存配置
     *
     * @param configId 配置ID
     * @param newName  新配置名称
     * @param newCode  新配置代码
     * @return 新配置ID
     */
    Long copyCacheConfig(Long configId, String newName, String newCode);

    /**
     * 验证缓存配置
     *
     * @param configId 配置ID
     * @return 验证结果
     */
    Boolean validateCacheConfig(Long configId);

    /**
     * 测试缓存配置
     *
     * @param configId 配置ID
     * @return 测试结果
     */
    Boolean testCacheConfig(Long configId);

    /**
     * 重置缓存配置统计
     *
     * @param configId 配置ID
     * @return 是否成功
     */
    Boolean resetCacheConfigStatistics(Long configId);

}
