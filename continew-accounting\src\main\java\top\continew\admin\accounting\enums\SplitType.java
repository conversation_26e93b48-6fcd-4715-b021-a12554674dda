package top.continew.admin.accounting.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.base.IBaseEnum;

/**
 * 分摊类型枚举
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Getter
@RequiredArgsConstructor
public enum SplitType implements IBaseEnum<String> {

    /**
     * 平均分摊
     */
    EQUAL("EQUAL", "平均分摊"),

    /**
     * 按比例分摊
     */
    RATIO("RATIO", "按比例分摊"),

    /**
     * 按金额分摊
     */
    AMOUNT("AMOUNT", "按金额分摊"),

    /**
     * 自定义分摊
     */
    CUSTOM("CUSTOM", "自定义分摊");

    private final String value;
    private final String description;
}
