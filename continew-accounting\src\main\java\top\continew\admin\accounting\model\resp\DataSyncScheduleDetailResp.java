package top.continew.admin.accounting.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 数据同步计划详情响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "数据同步计划详情响应")
public class DataSyncScheduleDetailResp implements Serializable {

    /**
     * ID
     */
    @Schema(description = "ID", example = "1")
    private Long id;

    /**
     * 配置ID
     */
    @Schema(description = "配置ID", example = "1")
    private Long configId;

    /**
     * 配置名称
     */
    @Schema(description = "配置名称", example = "用户数据同步")
    private String configName;

    /**
     * 配置描述
     */
    @Schema(description = "配置描述", example = "同步用户数据到外部系统")
    private String configDescription;

    /**
     * 计划名称
     */
    @Schema(description = "计划名称", example = "每日同步计划")
    private String scheduleName;

    /**
     * 计划类型
     */
    @Schema(description = "计划类型", example = "CRON")
    private String scheduleType;

    /**
     * Cron表达式
     */
    @Schema(description = "Cron表达式", example = "0 0 2 * * ?")
    private String cronExpression;

    /**
     * 间隔秒数
     */
    @Schema(description = "间隔秒数", example = "3600")
    private Integer intervalSeconds;

    /**
     * 计划执行时间(一次性)
     */
    @Schema(description = "计划执行时间(一次性)", example = "2025-01-01 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime scheduledTime;

    /**
     * 时区
     */
    @Schema(description = "时区", example = "Asia/Shanghai")
    private String timezone;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    /**
     * 最大重试次数
     */
    @Schema(description = "最大重试次数", example = "3")
    private Integer maxRetryCount;

    /**
     * 重试间隔秒数
     */
    @Schema(description = "重试间隔秒数", example = "300")
    private Integer retryIntervalSeconds;

    /**
     * 超时时间(秒)
     */
    @Schema(description = "超时时间(秒)", example = "1800")
    private Integer timeoutSeconds;

    /**
     * 最后执行时间
     */
    @Schema(description = "最后执行时间", example = "2025-01-01 02:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastExecutionTime;

    /**
     * 下次执行时间
     */
    @Schema(description = "下次执行时间", example = "2025-01-02 02:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime nextExecutionTime;

    /**
     * 执行次数
     */
    @Schema(description = "执行次数", example = "10")
    private Integer executionCount;

    /**
     * 成功次数
     */
    @Schema(description = "成功次数", example = "8")
    private Integer successCount;

    /**
     * 失败次数
     */
    @Schema(description = "失败次数", example = "2")
    private Integer failureCount;

    /**
     * 成功率
     */
    @Schema(description = "成功率", example = "80.0")
    private Double successRate;

    /**
     * 最后执行状态
     */
    @Schema(description = "最后执行状态", example = "SUCCESS")
    private String lastExecutionStatus;

    /**
     * 最后错误信息
     */
    @Schema(description = "最后错误信息", example = "连接超时")
    private String lastErrorMessage;

    /**
     * 健康状态
     */
    @Schema(description = "健康状态", example = "HEALTHY")
    private String healthStatus;

    /**
     * 下次执行时间预览
     */
    @Schema(description = "下次执行时间预览")
    private List<LocalDateTime> nextExecutionPreview;

    /**
     * 执行统计
     */
    @Schema(description = "执行统计")
    private Map<String, Object> executionStatistics;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-01-01 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2025-01-01 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人", example = "1")
    private Long createBy;

    /**
     * 更新人
     */
    @Schema(description = "更新人", example = "1")
    private Long updateBy;

}
