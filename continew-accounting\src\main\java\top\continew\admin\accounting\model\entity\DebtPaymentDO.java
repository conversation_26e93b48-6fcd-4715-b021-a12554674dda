package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.data.mybatis.base.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 债务还款记录实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("acc_debt_payment")
@Schema(description = "债务还款记录")
public class DebtPaymentDO extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 债务ID
     */
    @Schema(description = "债务ID")
    private Long debtId;

    /**
     * 还款金额
     */
    @Schema(description = "还款金额")
    private BigDecimal amount;

    /**
     * 币种
     */
    @Schema(description = "币种")
    private String currency;

    /**
     * 还款方式
     */
    @Schema(description = "还款方式")
    private String paymentMethod;

    /**
     * 还款时间
     */
    @Schema(description = "还款时间")
    private LocalDateTime paymentDate;

    /**
     * 关联交易ID
     */
    @Schema(description = "关联交易ID")
    private Long transactionId;

    /**
     * 还款类型
     */
    @Schema(description = "还款类型")
    private String paymentType;

    /**
     * 利息金额
     */
    @Schema(description = "利息金额")
    private BigDecimal interestAmount;

    /**
     * 本金金额
     */
    @Schema(description = "本金金额")
    private BigDecimal principalAmount;

    /**
     * 手续费
     */
    @Schema(description = "手续费")
    private BigDecimal feeAmount;

    /**
     * 还款状态
     */
    @Schema(description = "还款状态")
    private String status;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 确认人ID
     */
    @Schema(description = "确认人ID")
    private Long confirmedBy;

    /**
     * 确认时间
     */
    @Schema(description = "确认时间")
    private LocalDateTime confirmedAt;
}
