package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * OCR收据识别请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "OCR收据识别请求")
public class OcrReceiptReq {

    /**
     * 图片文件
     */
    @Schema(description = "图片文件", required = true)
    @NotNull(message = "图片文件不能为空")
    private MultipartFile imageFile;

    /**
     * 图片URL（与imageFile二选一）
     */
    @Schema(description = "图片URL", example = "https://example.com/receipt.jpg")
    private String imageUrl;

    /**
     * 图片Base64编码（与imageFile二选一）
     */
    @Schema(description = "图片Base64编码")
    private String imageBase64;

    /**
     * OCR引擎类型
     */
    @Schema(description = "OCR引擎类型", example = "BAIDU", allowableValues = {"BAIDU", "TENCENT", "ALIYUN", "GOOGLE", "AZURE"})
    private String ocrEngine = "BAIDU";

    /**
     * 识别语言
     */
    @Schema(description = "识别语言", example = "zh-CN", allowableValues = {"zh-CN", "en-US", "ja-JP", "ko-KR"})
    private String language = "zh-CN";

    /**
     * 识别精度
     */
    @Schema(description = "识别精度", example = "HIGH", allowableValues = {"LOW", "MEDIUM", "HIGH"})
    private String accuracy = "HIGH";

    /**
     * 是否自动创建账单
     */
    @Schema(description = "是否自动创建账单", example = "true")
    private Boolean autoCreateTransaction = true;

    /**
     * 默认分类ID
     */
    @Schema(description = "默认分类ID", example = "1")
    private Long defaultCategoryId;

    /**
     * 默认标签
     */
    @Schema(description = "默认标签")
    @Size(max = 10, message = "标签数量不能超过10个")
    private List<String> defaultTags;

    /**
     * 默认备注
     */
    @Schema(description = "默认备注", example = "OCR自动识别")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String defaultRemark;

    /**
     * 是否启用智能分类
     */
    @Schema(description = "是否启用智能分类", example = "true")
    private Boolean enableSmartCategory = true;

    /**
     * 是否启用智能标签
     */
    @Schema(description = "是否启用智能标签", example = "true")
    private Boolean enableSmartTags = true;

    /**
     * 置信度阈值
     */
    @Schema(description = "置信度阈值", example = "0.8")
    private Double confidenceThreshold = 0.8;

    /**
     * 是否保存原始图片
     */
    @Schema(description = "是否保存原始图片", example = "true")
    private Boolean saveOriginalImage = true;

    /**
     * 图片压缩质量
     */
    @Schema(description = "图片压缩质量", example = "80")
    private Integer imageQuality = 80;

    /**
     * 最大图片尺寸
     */
    @Schema(description = "最大图片尺寸", example = "2048")
    private Integer maxImageSize = 2048;

    /**
     * 识别模式
     */
    @Schema(description = "识别模式", example = "RECEIPT", allowableValues = {"RECEIPT", "INVOICE", "GENERAL"})
    private String recognitionMode = "RECEIPT";

    /**
     * 是否启用表格识别
     */
    @Schema(description = "是否启用表格识别", example = "false")
    private Boolean enableTableRecognition = false;

    /**
     * 是否启用手写识别
     */
    @Schema(description = "是否启用手写识别", example = "false")
    private Boolean enableHandwritingRecognition = false;

    /**
     * 超时时间（秒）
     */
    @Schema(description = "超时时间（秒）", example = "30")
    private Integer timeoutSeconds = 30;

    /**
     * 重试次数
     */
    @Schema(description = "重试次数", example = "3")
    private Integer retryCount = 3;

    /**
     * 回调URL
     */
    @Schema(description = "回调URL", example = "https://example.com/callback")
    private String callbackUrl;

    /**
     * 异步处理
     */
    @Schema(description = "是否异步处理", example = "false")
    private Boolean asyncProcessing = false;

    /**
     * 扩展参数
     */
    @Schema(description = "扩展参数")
    private String extraParams;
}
