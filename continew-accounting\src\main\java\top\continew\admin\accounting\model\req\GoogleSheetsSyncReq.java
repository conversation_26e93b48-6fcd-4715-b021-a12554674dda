package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Google Sheets同步请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "Google Sheets同步请求")
public class GoogleSheetsSyncReq {

    /**
     * 配置ID
     */
    @Schema(description = "配置ID", example = "1")
    @NotNull(message = "配置ID不能为空")
    private Long configId;

    /**
     * 同步类型
     */
    @Schema(description = "同步类型", example = "FULL", allowableValues = {"FULL", "INCREMENTAL", "CUSTOM"})
    @NotBlank(message = "同步类型不能为空")
    private String syncType;

    /**
     * 同步方向
     */
    @Schema(description = "同步方向", example = "TO_SHEETS", allowableValues = {"TO_SHEETS", "FROM_SHEETS", "BIDIRECTIONAL"})
    private String syncDirection;

    /**
     * 是否强制同步
     */
    @Schema(description = "是否强制同步", example = "false")
    private Boolean forceSync = false;

    /**
     * 是否异步执行
     */
    @Schema(description = "是否异步执行", example = "true")
    private Boolean asyncExecution = true;

    /**
     * 同步范围
     */
    @Schema(description = "同步范围")
    private SyncScope syncScope;

    /**
     * 同步选项
     */
    @Schema(description = "同步选项")
    private SyncOptions syncOptions;

    /**
     * 回调配置
     */
    @Schema(description = "回调配置")
    private CallbackConfig callbackConfig;

    /**
     * 同步范围
     */
    @Data
    @Schema(description = "同步范围")
    public static class SyncScope {

        /**
         * 指定账单ID列表
         */
        @Schema(description = "指定账单ID列表")
        private List<Long> transactionIds;

        /**
         * 日期范围
         */
        @Schema(description = "日期范围")
        private DateRange dateRange;

        /**
         * 分类过滤
         */
        @Schema(description = "分类过滤")
        private List<Long> categoryIds;

        /**
         * 标签过滤
         */
        @Schema(description = "标签过滤")
        private List<String> tags;

        /**
         * 账单类型过滤
         */
        @Schema(description = "账单类型过滤", allowableValues = {"INCOME", "EXPENSE"})
        private List<String> transactionTypes;

        /**
         * 最大记录数
         */
        @Schema(description = "最大记录数", example = "1000")
        private Integer maxRecords;

        /**
         * 自定义查询条件
         */
        @Schema(description = "自定义查询条件")
        private String customQuery;
    }

    /**
     * 日期范围
     */
    @Data
    @Schema(description = "日期范围")
    public static class DateRange {

        /**
         * 开始时间
         */
        @Schema(description = "开始时间", example = "2025-01-01T00:00:00")
        private LocalDateTime startTime;

        /**
         * 结束时间
         */
        @Schema(description = "结束时间", example = "2025-01-31T23:59:59")
        private LocalDateTime endTime;

        /**
         * 相对时间类型
         */
        @Schema(description = "相对时间类型", example = "LAST_30_DAYS", allowableValues = {
                "TODAY", "YESTERDAY", "LAST_7_DAYS", "LAST_30_DAYS", "THIS_MONTH", "LAST_MONTH", "THIS_YEAR", "LAST_YEAR"
        })
        private String relativeTimeType;
    }

    /**
     * 同步选项
     */
    @Data
    @Schema(description = "同步选项")
    public static class SyncOptions {

        /**
         * 批量大小
         */
        @Schema(description = "批量大小", example = "100")
        private Integer batchSize = 100;

        /**
         * 超时时间（秒）
         */
        @Schema(description = "超时时间（秒）", example = "300")
        private Integer timeoutSeconds = 300;

        /**
         * 重试次数
         */
        @Schema(description = "重试次数", example = "3")
        private Integer retryCount = 3;

        /**
         * 重试间隔（秒）
         */
        @Schema(description = "重试间隔（秒）", example = "5")
        private Integer retryIntervalSeconds = 5;

        /**
         * 冲突解决策略
         */
        @Schema(description = "冲突解决策略", example = "LOCAL_WINS", allowableValues = {
                "LOCAL_WINS", "REMOTE_WINS", "MERGE", "SKIP", "MANUAL"
        })
        private String conflictResolution = "LOCAL_WINS";

        /**
         * 是否跳过验证
         */
        @Schema(description = "是否跳过验证", example = "false")
        private Boolean skipValidation = false;

        /**
         * 是否创建备份
         */
        @Schema(description = "是否创建备份", example = "true")
        private Boolean createBackup = true;

        /**
         * 是否清空目标数据
         */
        @Schema(description = "是否清空目标数据", example = "false")
        private Boolean clearTarget = false;

        /**
         * 是否保留格式
         */
        @Schema(description = "是否保留格式", example = "true")
        private Boolean preserveFormat = true;

        /**
         * 自定义参数
         */
        @Schema(description = "自定义参数")
        private Map<String, Object> customParams;
    }

    /**
     * 回调配置
     */
    @Data
    @Schema(description = "回调配置")
    public static class CallbackConfig {

        /**
         * 回调URL
         */
        @Schema(description = "回调URL", example = "https://api.example.com/webhook/sync-callback")
        private String callbackUrl;

        /**
         * 回调方法
         */
        @Schema(description = "回调方法", example = "POST", allowableValues = {"GET", "POST", "PUT"})
        private String callbackMethod = "POST";

        /**
         * 回调头信息
         */
        @Schema(description = "回调头信息")
        private Map<String, String> callbackHeaders;

        /**
         * 回调超时时间（秒）
         */
        @Schema(description = "回调超时时间（秒）", example = "30")
        private Integer callbackTimeoutSeconds = 30;

        /**
         * 回调重试次数
         */
        @Schema(description = "回调重试次数", example = "3")
        private Integer callbackRetryCount = 3;

        /**
         * 是否包含详细结果
         */
        @Schema(description = "是否包含详细结果", example = "true")
        private Boolean includeDetailedResult = true;

        /**
         * 回调条件
         */
        @Schema(description = "回调条件", allowableValues = {"ALWAYS", "SUCCESS_ONLY", "FAILURE_ONLY"})
        private String callbackCondition = "ALWAYS";
    }
}
