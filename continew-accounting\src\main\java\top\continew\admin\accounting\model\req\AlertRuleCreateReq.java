package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 财务预警规则创建请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "财务预警规则创建请求")
public class AlertRuleCreateReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    @NotNull(message = "群组ID不能为空")
    private Long groupId;

    /**
     * 规则名称
     */
    @Schema(description = "规则名称", example = "餐饮支出预警")
    @NotBlank(message = "规则名称不能为空")
    @Size(max = 100, message = "规则名称长度不能超过100个字符")
    private String ruleName;

    /**
     * 规则描述
     */
    @Schema(description = "规则描述", example = "当餐饮支出超过预算80%时触发预警")
    @Size(max = 500, message = "规则描述长度不能超过500个字符")
    private String ruleDescription;

    /**
     * 预警类型
     */
    @Schema(description = "预警类型", example = "BUDGET_THRESHOLD", allowableValues = {"BUDGET_THRESHOLD", "AMOUNT_LIMIT", "FREQUENCY_LIMIT", "ANOMALY_DETECTION", "TREND_ANALYSIS", "CUSTOM_RULE"})
    @NotBlank(message = "预警类型不能为空")
    private String alertType;

    /**
     * 预警级别
     */
    @Schema(description = "预警级别", example = "WARNING", allowableValues = {"INFO", "WARNING", "ERROR", "CRITICAL"})
    @NotBlank(message = "预警级别不能为空")
    private String alertLevel;

    /**
     * 监控范围
     */
    @Schema(description = "监控范围")
    @NotNull(message = "监控范围不能为空")
    private MonitoringScope monitoringScope;

    /**
     * 触发条件
     */
    @Schema(description = "触发条件")
    @NotNull(message = "触发条件不能为空")
    private TriggerConditions triggerConditions;

    /**
     * 通知设置
     */
    @Schema(description = "通知设置")
    @NotNull(message = "通知设置不能为空")
    private NotificationSettings notificationSettings;

    /**
     * 执行设置
     */
    @Schema(description = "执行设置")
    private ExecutionSettings executionSettings;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled = true;

    /**
     * 优先级
     */
    @Schema(description = "优先级", example = "NORMAL", allowableValues = {"LOW", "NORMAL", "HIGH", "URGENT"})
    private String priority = "NORMAL";

    /**
     * 标签列表
     */
    @Schema(description = "标签列表", example = "[\"预算管理\", \"支出控制\"]")
    private List<String> tags;

    /**
     * 扩展属性
     */
    @Schema(description = "扩展属性")
    private Map<String, Object> attributes;

    /**
     * 监控范围
     */
    @Data
    @Schema(description = "监控范围")
    public static class MonitoringScope implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 范围类型
         */
        @Schema(description = "范围类型", example = "CATEGORY", allowableValues = {"ALL", "CATEGORY", "TAG", "MEMBER", "WALLET", "CUSTOM"})
        @NotBlank(message = "范围类型不能为空")
        private String scopeType;

        /**
         * 目标ID列表
         */
        @Schema(description = "目标ID列表", example = "[1, 2, 3]")
        private List<Long> targetIds;

        /**
         * 目标名称列表
         */
        @Schema(description = "目标名称列表", example = "[\"餐饮\", \"交通\"]")
        private List<String> targetNames;

        /**
         * 包含子分类
         */
        @Schema(description = "包含子分类", example = "true")
        private Boolean includeSubCategories = true;

        /**
         * 时间范围
         */
        @Schema(description = "时间范围")
        private TimeRange timeRange;

        /**
         * 金额范围
         */
        @Schema(description = "金额范围")
        private AmountRange amountRange;

        /**
         * 自定义过滤条件
         */
        @Schema(description = "自定义过滤条件")
        private Map<String, Object> customFilters;

        @Data
        @Schema(description = "时间范围")
        public static class TimeRange implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 时间类型
             */
            @Schema(description = "时间类型", example = "RELATIVE", allowableValues = {"RELATIVE", "ABSOLUTE", "ROLLING"})
            private String timeType = "RELATIVE";

            /**
             * 相对时间单位
             */
            @Schema(description = "相对时间单位", example = "MONTH", allowableValues = {"DAY", "WEEK", "MONTH", "QUARTER", "YEAR"})
            private String relativeUnit;

            /**
             * 相对时间值
             */
            @Schema(description = "相对时间值", example = "1")
            private Integer relativeValue;

            /**
             * 开始日期
             */
            @Schema(description = "开始日期", example = "2025-01-01")
            private String startDate;

            /**
             * 结束日期
             */
            @Schema(description = "结束日期", example = "2025-01-31")
            private String endDate;

            /**
             * 滚动窗口大小（天）
             */
            @Schema(description = "滚动窗口大小（天）", example = "30")
            private Integer rollingWindowDays;
        }

        @Data
        @Schema(description = "金额范围")
        public static class AmountRange implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 最小金额
             */
            @Schema(description = "最小金额", example = "0.00")
            private BigDecimal minAmount;

            /**
             * 最大金额
             */
            @Schema(description = "最大金额", example = "10000.00")
            private BigDecimal maxAmount;

            /**
             * 币种
             */
            @Schema(description = "币种", example = "CNY")
            private String currency = "CNY";
        }
    }

    /**
     * 触发条件
     */
    @Data
    @Schema(description = "触发条件")
    public static class TriggerConditions implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 条件类型
         */
        @Schema(description = "条件类型", example = "THRESHOLD", allowableValues = {"THRESHOLD", "PERCENTAGE", "TREND", "ANOMALY", "FREQUENCY", "CUSTOM"})
        @NotBlank(message = "条件类型不能为空")
        private String conditionType;

        /**
         * 阈值设置
         */
        @Schema(description = "阈值设置")
        private ThresholdSettings thresholdSettings;

        /**
         * 百分比设置
         */
        @Schema(description = "百分比设置")
        private PercentageSettings percentageSettings;

        /**
         * 趋势设置
         */
        @Schema(description = "趋势设置")
        private TrendSettings trendSettings;

        /**
         * 异常检测设置
         */
        @Schema(description = "异常检测设置")
        private AnomalySettings anomalySettings;

        /**
         * 频率设置
         */
        @Schema(description = "频率设置")
        private FrequencySettings frequencySettings;

        /**
         * 自定义条件表达式
         */
        @Schema(description = "自定义条件表达式", example = "amount > 1000 && category == 'dining'")
        private String customExpression;

        /**
         * 条件组合逻辑
         */
        @Schema(description = "条件组合逻辑", example = "AND", allowableValues = {"AND", "OR", "NOT"})
        private String logicOperator = "AND";

        @Data
        @Schema(description = "阈值设置")
        public static class ThresholdSettings implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 阈值
             */
            @Schema(description = "阈值", example = "1000.00")
            @NotNull(message = "阈值不能为空")
            private BigDecimal threshold;

            /**
             * 比较操作符
             */
            @Schema(description = "比较操作符", example = "GREATER_THAN", allowableValues = {"GREATER_THAN", "GREATER_EQUAL", "LESS_THAN", "LESS_EQUAL", "EQUAL", "NOT_EQUAL"})
            private String operator = "GREATER_THAN";

            /**
             * 统计方式
             */
            @Schema(description = "统计方式", example = "SUM", allowableValues = {"SUM", "COUNT", "AVERAGE", "MAX", "MIN"})
            private String aggregation = "SUM";

            /**
             * 币种
             */
            @Schema(description = "币种", example = "CNY")
            private String currency = "CNY";
        }

        @Data
        @Schema(description = "百分比设置")
        public static class PercentageSettings implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 百分比阈值
             */
            @Schema(description = "百分比阈值", example = "0.80")
            @NotNull(message = "百分比阈值不能为空")
            @DecimalMin(value = "0.0", message = "百分比阈值不能小于0")
            @DecimalMax(value = "1.0", message = "百分比阈值不能大于1")
            private BigDecimal percentage;

            /**
             * 基准类型
             */
            @Schema(description = "基准类型", example = "BUDGET", allowableValues = {"BUDGET", "LAST_PERIOD", "AVERAGE", "CUSTOM"})
            private String baselineType = "BUDGET";

            /**
             * 基准值
             */
            @Schema(description = "基准值", example = "5000.00")
            private BigDecimal baselineValue;

            /**
             * 基准期间
             */
            @Schema(description = "基准期间", example = "LAST_MONTH")
            private String baselinePeriod;
        }

        @Data
        @Schema(description = "趋势设置")
        public static class TrendSettings implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 趋势类型
             */
            @Schema(description = "趋势类型", example = "INCREASING", allowableValues = {"INCREASING", "DECREASING", "STABLE", "VOLATILE"})
            private String trendType;

            /**
             * 趋势强度阈值
             */
            @Schema(description = "趋势强度阈值", example = "0.20")
            private BigDecimal intensityThreshold;

            /**
             * 观察期间（天）
             */
            @Schema(description = "观察期间（天）", example = "30")
            private Integer observationDays = 30;

            /**
             * 最小数据点数
             */
            @Schema(description = "最小数据点数", example = "5")
            private Integer minDataPoints = 5;
        }

        @Data
        @Schema(description = "异常检测设置")
        public static class AnomalySettings implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 检测算法
             */
            @Schema(description = "检测算法", example = "STATISTICAL", allowableValues = {"STATISTICAL", "ISOLATION_FOREST", "Z_SCORE", "IQR"})
            private String algorithm = "STATISTICAL";

            /**
             * 敏感度
             */
            @Schema(description = "敏感度", example = "MEDIUM", allowableValues = {"LOW", "MEDIUM", "HIGH"})
            private String sensitivity = "MEDIUM";

            /**
             * 历史数据天数
             */
            @Schema(description = "历史数据天数", example = "90")
            private Integer historicalDays = 90;

            /**
             * 置信度
             */
            @Schema(description = "置信度", example = "0.95")
            private BigDecimal confidence = new BigDecimal("0.95");
        }

        @Data
        @Schema(description = "频率设置")
        public static class FrequencySettings implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 频率阈值
             */
            @Schema(description = "频率阈值", example = "10")
            private Integer frequencyThreshold;

            /**
             * 时间窗口（小时）
             */
            @Schema(description = "时间窗口（小时）", example = "24")
            private Integer timeWindowHours = 24;

            /**
             * 频率类型
             */
            @Schema(description = "频率类型", example = "TRANSACTION_COUNT", allowableValues = {"TRANSACTION_COUNT", "AMOUNT_SUM", "UNIQUE_CATEGORIES"})
            private String frequencyType = "TRANSACTION_COUNT";
        }
    }

    /**
     * 通知设置
     */
    @Data
    @Schema(description = "通知设置")
    public static class NotificationSettings implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 通知方式
         */
        @Schema(description = "通知方式", example = "[\"EMAIL\", \"SMS\", \"PUSH\"]")
        @NotEmpty(message = "通知方式不能为空")
        private List<String> notificationMethods;

        /**
         * 接收人ID列表
         */
        @Schema(description = "接收人ID列表", example = "[1, 2, 3]")
        @NotEmpty(message = "接收人ID列表不能为空")
        private List<Long> recipientIds;

        /**
         * 通知模板
         */
        @Schema(description = "通知模板", example = "BUDGET_ALERT")
        private String notificationTemplate;

        /**
         * 自定义消息
         */
        @Schema(description = "自定义消息", example = "您的餐饮支出已超过预算的80%")
        @Size(max = 500, message = "自定义消息长度不能超过500个字符")
        private String customMessage;

        /**
         * 通知频率限制
         */
        @Schema(description = "通知频率限制")
        private FrequencyLimit frequencyLimit;

        /**
         * 静默时间段
         */
        @Schema(description = "静默时间段")
        private List<SilentPeriod> silentPeriods;

        @Data
        @Schema(description = "频率限制")
        public static class FrequencyLimit implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 最大通知次数
             */
            @Schema(description = "最大通知次数", example = "3")
            private Integer maxNotifications = 3;

            /**
             * 时间窗口（小时）
             */
            @Schema(description = "时间窗口（小时）", example = "24")
            private Integer timeWindowHours = 24;

            /**
             * 冷却期（分钟）
             */
            @Schema(description = "冷却期（分钟）", example = "60")
            private Integer cooldownMinutes = 60;
        }

        @Data
        @Schema(description = "静默时间段")
        public static class SilentPeriod implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 开始时间
             */
            @Schema(description = "开始时间", example = "22:00")
            private String startTime;

            /**
             * 结束时间
             */
            @Schema(description = "结束时间", example = "08:00")
            private String endTime;

            /**
             * 星期几
             */
            @Schema(description = "星期几", example = "[1, 2, 3, 4, 5]")
            private List<Integer> daysOfWeek;

            /**
             * 时区
             */
            @Schema(description = "时区", example = "Asia/Shanghai")
            private String timezone = "Asia/Shanghai";
        }
    }

    /**
     * 执行设置
     */
    @Data
    @Schema(description = "执行设置")
    public static class ExecutionSettings implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 检查频率
         */
        @Schema(description = "检查频率", example = "REAL_TIME", allowableValues = {"REAL_TIME", "HOURLY", "DAILY", "WEEKLY", "MONTHLY", "CUSTOM"})
        private String checkFrequency = "REAL_TIME";

        /**
         * 自定义Cron表达式
         */
        @Schema(description = "自定义Cron表达式", example = "0 0 9 * * ?")
        private String cronExpression;

        /**
         * 生效时间
         */
        @Schema(description = "生效时间", example = "2025-01-01 00:00:00")
        private String effectiveTime;

        /**
         * 失效时间
         */
        @Schema(description = "失效时间", example = "2025-12-31 23:59:59")
        private String expiryTime;

        /**
         * 最大执行次数
         */
        @Schema(description = "最大执行次数", example = "100")
        private Integer maxExecutions;

        /**
         * 超时时间（秒）
         */
        @Schema(description = "超时时间（秒）", example = "30")
        private Integer timeoutSeconds = 30;

        /**
         * 重试次数
         */
        @Schema(description = "重试次数", example = "3")
        private Integer retryCount = 3;

        /**
         * 重试间隔（秒）
         */
        @Schema(description = "重试间隔（秒）", example = "60")
        private Integer retryIntervalSeconds = 60;
    }
}
