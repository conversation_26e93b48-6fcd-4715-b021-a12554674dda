package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 钱包更新请求参数
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "钱包更新请求参数")
public class WalletUpdateReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 余额（仅管理员可修改）
     */
    @Schema(description = "余额", example = "1000.00")
    @DecimalMin(value = "0.00", message = "余额不能为负数")
    @Digits(integer = 15, fraction = 2, message = "余额格式不正确")
    private BigDecimal balance;

    /**
     * 冻结金额（仅管理员可修改）
     */
    @Schema(description = "冻结金额", example = "100.00")
    @DecimalMin(value = "0.00", message = "冻结金额不能为负数")
    @Digits(integer = 15, fraction = 2, message = "冻结金额格式不正确")
    private BigDecimal frozenAmount;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "管理员调整余额")
    @Size(max = 200, message = "备注长度不能超过200个字符")
    private String remark;
}
