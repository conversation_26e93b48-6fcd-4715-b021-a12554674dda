<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.accounting.mapper.CacheConfigMapper">

    <!-- 获取缓存配置统计 -->
    <select id="getCacheConfigStatistics" resultType="map">
        SELECT 
            COUNT(*) as totalConfigs,
            COUNT(CASE WHEN status = 'ENABLE' THEN 1 END) as enabledConfigs,
            COUNT(CASE WHEN status = 'DISABLE' THEN 1 END) as disabledConfigs,
            COUNT(CASE WHEN cache_type = 'LOCAL' THEN 1 END) as localCacheConfigs,
            COUNT(CASE WHEN cache_type = 'REMOTE' THEN 1 END) as remoteCacheConfigs,
            COUNT(CASE WHEN cache_type = 'BOTH' THEN 1 END) as bothCacheConfigs,
            AVG(applied_count) as avgAppliedCount,
            MAX(applied_count) as maxAppliedCount,
            COUNT(CASE WHEN statistics_enabled = 1 THEN 1 END) as statisticsEnabledConfigs,
            COUNT(CASE WHEN monitor_enabled = 1 THEN 1 END) as monitorEnabledConfigs,
            COUNT(CASE WHEN hotspot_detection = 1 THEN 1 END) as hotspotDetectionConfigs
        FROM acc_cache_config 
        WHERE deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
    </select>

    <!-- 获取缓存类型分布统计 -->
    <select id="getCacheTypeDistribution" resultType="map">
        SELECT 
            cache_type as cacheType,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM acc_cache_config WHERE deleted = 0 
                <if test="groupId != null">AND group_id = #{groupId}</if>), 2) as percentage
        FROM acc_cache_config 
        WHERE deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        GROUP BY cache_type
        ORDER BY count DESC
    </select>

    <!-- 获取缓存策略分布统计 -->
    <select id="getCacheStrategyDistribution" resultType="map">
        SELECT 
            cache_strategy as cacheStrategy,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM acc_cache_config WHERE deleted = 0 
                <if test="groupId != null">AND group_id = #{groupId}</if>), 2) as percentage
        FROM acc_cache_config 
        WHERE deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        GROUP BY cache_strategy
        ORDER BY count DESC
    </select>

    <!-- 获取缓存配置使用频率统计 -->
    <select id="getCacheConfigUsageFrequency" resultType="map">
        SELECT 
            id,
            config_name as configName,
            config_code as configCode,
            cache_type as cacheType,
            applied_count as appliedCount,
            last_applied_time as lastAppliedTime,
            status
        FROM acc_cache_config 
        WHERE deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        ORDER BY applied_count DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 获取缓存配置性能指标 -->
    <select id="getCacheConfigPerformanceMetrics" resultType="map">
        SELECT 
            DATE_FORMAT(create_time, '%Y-%m-%d %H:00:00') as timestamp,
            COUNT(*) as configCount,
            AVG(applied_count) as avgAppliedCount,
            SUM(CASE WHEN status = 'ENABLE' THEN 1 ELSE 0 END) as enabledCount,
            SUM(CASE WHEN status = 'DISABLE' THEN 1 ELSE 0 END) as disabledCount
        FROM acc_cache_config 
        WHERE deleted = 0
        <if test="configId != null">
            AND id = #{configId}
        </if>
        <if test="hours != null">
            AND create_time >= DATE_SUB(NOW(), INTERVAL #{hours} HOUR)
        </if>
        GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d %H:00:00')
        ORDER BY timestamp DESC
    </select>

    <!-- 获取缓存配置健康状态 -->
    <select id="getCacheConfigHealthStatus" resultType="map">
        SELECT 
            id,
            config_name as configName,
            config_code as configCode,
            cache_type as cacheType,
            status,
            CASE 
                WHEN status = 'ENABLE' AND applied_count > 0 THEN 'HEALTHY'
                WHEN status = 'ENABLE' AND applied_count = 0 THEN 'WARNING'
                WHEN status = 'DISABLE' THEN 'INACTIVE'
                ELSE 'UNKNOWN'
            END as healthStatus,
            applied_count as appliedCount,
            last_applied_time as lastAppliedTime,
            TIMESTAMPDIFF(HOUR, last_applied_time, NOW()) as hoursSinceLastApplied
        FROM acc_cache_config 
        WHERE deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        ORDER BY 
            CASE 
                WHEN status = 'ENABLE' AND applied_count > 0 THEN 1
                WHEN status = 'ENABLE' AND applied_count = 0 THEN 2
                WHEN status = 'DISABLE' THEN 3
                ELSE 4
            END,
            applied_count DESC
    </select>

    <!-- 检测缓存配置异常 -->
    <select id="detectCacheConfigAnomalies" resultType="map">
        SELECT 
            id,
            config_name as configName,
            config_code as configCode,
            'UNUSED_CONFIG' as anomalyType,
            '配置已启用但从未被应用' as description,
            'WARNING' as severity,
            create_time as detectedTime
        FROM acc_cache_config 
        WHERE deleted = 0 
            AND status = 'ENABLE' 
            AND applied_count = 0
            AND TIMESTAMPDIFF(DAY, create_time, NOW()) > 7
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        
        UNION ALL
        
        SELECT 
            id,
            config_name as configName,
            config_code as configCode,
            'STALE_CONFIG' as anomalyType,
            '配置长时间未被应用' as description,
            'INFO' as severity,
            last_applied_time as detectedTime
        FROM acc_cache_config 
        WHERE deleted = 0 
            AND status = 'ENABLE' 
            AND last_applied_time IS NOT NULL
            AND TIMESTAMPDIFF(DAY, last_applied_time, NOW()) > 30
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        
        ORDER BY severity DESC, detectedTime DESC
    </select>

    <!-- 获取缓存配置优化建议 -->
    <select id="getCacheConfigOptimizationSuggestions" resultType="map">
        SELECT 
            id,
            config_name as configName,
            config_code as configCode,
            'ENABLE_STATISTICS' as suggestionType,
            '启用统计功能' as title,
            '建议启用统计功能以便监控缓存性能' as description,
            'MEDIUM' as priority
        FROM acc_cache_config 
        WHERE deleted = 0 
            AND status = 'ENABLE'
            AND statistics_enabled = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        
        UNION ALL
        
        SELECT 
            id,
            config_name as configName,
            config_code as configCode,
            'ENABLE_MONITORING' as suggestionType,
            '启用监控功能' as title,
            '建议启用监控功能以便及时发现问题' as description,
            'HIGH' as priority
        FROM acc_cache_config 
        WHERE deleted = 0 
            AND status = 'ENABLE'
            AND monitor_enabled = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        
        UNION ALL
        
        SELECT 
            id,
            config_name as configName,
            config_code as configCode,
            'ENABLE_HOTSPOT_DETECTION' as suggestionType,
            '启用热点数据识别' as title,
            '建议启用热点数据识别以优化缓存策略' as description,
            'LOW' as priority
        FROM acc_cache_config 
        WHERE deleted = 0 
            AND status = 'ENABLE'
            AND hotspot_detection = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        
        ORDER BY 
            CASE priority 
                WHEN 'HIGH' THEN 1 
                WHEN 'MEDIUM' THEN 2 
                WHEN 'LOW' THEN 3 
                ELSE 4 
            END
    </select>

    <!-- 批量更新缓存配置状态 -->
    <update id="batchUpdateStatus">
        UPDATE acc_cache_config 
        SET status = #{status}, update_time = NOW()
        WHERE id IN 
        <foreach collection="configIds" item="configId" open="(" separator="," close=")">
            #{configId}
        </foreach>
        AND deleted = 0
    </update>

    <!-- 批量更新应用统计 -->
    <update id="batchUpdateAppliedStatistics">
        UPDATE acc_cache_config 
        SET applied_count = applied_count + 1, 
            last_applied_time = NOW(),
            update_time = NOW()
        WHERE id IN 
        <foreach collection="configIds" item="configId" open="(" separator="," close=")">
            #{configId}
        </foreach>
        AND deleted = 0
    </update>

    <!-- 清理过期的缓存配置 -->
    <update id="cleanupExpiredConfigs">
        UPDATE acc_cache_config 
        SET deleted = 1, update_time = NOW()
        WHERE status = 'DISABLE' 
            AND last_applied_time IS NULL
            AND create_time &lt; #{expireTime}
            AND deleted = 0
    </update>

    <!-- 获取缓存配置容量预测 -->
    <select id="getCacheConfigCapacityPrediction" resultType="map">
        SELECT 
            id,
            config_name as configName,
            config_code as configCode,
            local_max_size as currentCapacity,
            COALESCE(local_max_size * 0.8, 1000) as currentUsage,
            ROUND(COALESCE(local_max_size * 0.8, 1000) / COALESCE(local_max_size, 1000) * 100, 2) as currentUtilization,
            ROUND(COALESCE(local_max_size * 0.8, 1000) * 1.15, 0) as predictedCapacityNeeded,
            ROUND(COALESCE(local_max_size * 0.8, 1000) * 1.25, 0) as recommendedCapacity,
            #{days} as predictionPeriod
        FROM acc_cache_config 
        WHERE id = #{configId} AND deleted = 0
    </select>

    <!-- 获取缓存配置使用模式分析 -->
    <select id="getCacheConfigUsagePattern" resultType="map">
        SELECT 
            id,
            config_name as configName,
            config_code as configCode,
            applied_count as totalApplications,
            ROUND(applied_count / GREATEST(TIMESTAMPDIFF(DAY, create_time, NOW()), 1), 2) as avgDailyApplications,
            CASE 
                WHEN applied_count > 100 THEN 'HIGH_FREQUENCY'
                WHEN applied_count > 10 THEN 'MEDIUM_FREQUENCY'
                WHEN applied_count > 0 THEN 'LOW_FREQUENCY'
                ELSE 'UNUSED'
            END as usagePattern,
            TIMESTAMPDIFF(DAY, create_time, NOW()) as configAge,
            last_applied_time as lastUsedTime,
            #{days} as analysisPeriod
        FROM acc_cache_config 
        WHERE id = #{configId} AND deleted = 0
    </select>

    <!-- 获取缓存配置热点数据 -->
    <select id="getCacheConfigHotspotData" resultType="map">
        SELECT
            config_code as cacheKey,
            applied_count as accessCount,
            applied_count as hitCount,
            ROUND(applied_count / GREATEST(applied_count, 1) * 100, 2) as hitRate,
            last_applied_time as lastAccessTime,
            CHAR_LENGTH(COALESCE(extend_config, '{}')) as dataSize
        FROM acc_cache_config
        WHERE id = #{configId} AND deleted = 0
        ORDER BY applied_count DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 获取缓存配置错误统计 -->
    <select id="getCacheConfigErrorStatistics" resultType="map">
        SELECT
            DATE_FORMAT(update_time, '%Y-%m-%d %H:00:00') as timestamp,
            COUNT(CASE WHEN status = 'ERROR' THEN 1 END) as errorCount,
            COUNT(CASE WHEN status = 'DISABLE' THEN 1 END) as disableCount,
            COUNT(*) as totalCount,
            ROUND(COUNT(CASE WHEN status = 'ERROR' THEN 1 END) * 100.0 / COUNT(*), 2) as errorRate
        FROM acc_cache_config
        WHERE id = #{configId} AND deleted = 0
        <if test="hours != null">
            AND update_time >= DATE_SUB(NOW(), INTERVAL #{hours} HOUR)
        </if>
        GROUP BY DATE_FORMAT(update_time, '%Y-%m-%d %H:00:00')
        ORDER BY timestamp DESC
    </select>

    <!-- 获取缓存配置性能趋势 -->
    <select id="getCacheConfigPerformanceTrend" resultType="map">
        SELECT
            DATE_FORMAT(update_time, '%Y-%m-%d') as date,
            AVG(applied_count) as avgAppliedCount,
            MAX(applied_count) as maxAppliedCount,
            MIN(applied_count) as minAppliedCount,
            COUNT(CASE WHEN status = 'ENABLE' THEN 1 END) as enabledCount,
            COUNT(*) as totalCount
        FROM acc_cache_config
        WHERE id = #{configId} AND deleted = 0
        <if test="days != null">
            AND update_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        </if>
        GROUP BY DATE_FORMAT(update_time, '%Y-%m-%d')
        ORDER BY date DESC
    </select>

    <!-- 验证缓存配置完整性 -->
    <select id="validateCacheConfigIntegrity" resultType="map">
        SELECT
            id,
            config_name as configName,
            config_code as configCode,
            CASE
                WHEN config_name IS NULL OR config_name = '' THEN 0
                WHEN config_code IS NULL OR config_code = '' THEN 0
                WHEN cache_type IS NULL THEN 0
                WHEN cache_strategy IS NULL THEN 0
                ELSE 1
            END as isValid,
            CASE
                WHEN config_name IS NULL OR config_name = '' THEN '配置名称不能为空'
                WHEN config_code IS NULL OR config_code = '' THEN '配置代码不能为空'
                WHEN cache_type IS NULL THEN '缓存类型不能为空'
                WHEN cache_strategy IS NULL THEN '缓存策略不能为空'
                ELSE '配置完整'
            END as validationMessage
        FROM acc_cache_config
        WHERE id = #{configId} AND deleted = 0
    </select>

    <!-- 获取缓存配置依赖关系 -->
    <select id="getCacheConfigDependencies" resultType="map">
        SELECT
            c1.id as configId,
            c1.config_name as configName,
            c2.id as dependentConfigId,
            c2.config_name as dependentConfigName,
            'CACHE_STRATEGY' as dependencyType,
            '缓存策略依赖' as description
        FROM acc_cache_config c1
        JOIN acc_cache_config c2 ON c1.cache_strategy = c2.cache_strategy AND c1.id != c2.id
        WHERE c1.id = #{configId} AND c1.deleted = 0 AND c2.deleted = 0

        UNION ALL

        SELECT
            c1.id as configId,
            c1.config_name as configName,
            c2.id as dependentConfigId,
            c2.config_name as dependentConfigName,
            'KEY_PREFIX' as dependencyType,
            '键前缀依赖' as description
        FROM acc_cache_config c1
        JOIN acc_cache_config c2 ON c1.key_prefix = c2.key_prefix AND c1.id != c2.id
        WHERE c1.id = #{configId} AND c1.deleted = 0 AND c2.deleted = 0
    </select>

    <!-- 获取缓存配置影响分析 -->
    <select id="getCacheConfigImpactAnalysis" resultType="map">
        SELECT
            id,
            config_name as configName,
            config_code as configCode,
            applied_count as totalApplications,
            CASE
                WHEN applied_count > 1000 THEN 'HIGH'
                WHEN applied_count > 100 THEN 'MEDIUM'
                WHEN applied_count > 0 THEN 'LOW'
                ELSE 'NONE'
            END as impactLevel,
            CASE
                WHEN status = 'ENABLE' THEN '启用状态，影响正在进行'
                WHEN status = 'DISABLE' THEN '禁用状态，无当前影响'
                ELSE '未知状态'
            END as currentImpact,
            TIMESTAMPDIFF(DAY, create_time, NOW()) as configAge,
            last_applied_time as lastImpactTime
        FROM acc_cache_config
        WHERE id = #{configId} AND deleted = 0
    </select>

    <!-- 获取缓存配置版本历史 -->
    <select id="getCacheConfigVersionHistory" resultType="map">
        SELECT
            id,
            config_name as configName,
            config_code as configCode,
            status,
            applied_count as appliedCount,
            create_time as createdTime,
            update_time as updatedTime,
            'CURRENT' as version,
            '当前版本' as description
        FROM acc_cache_config
        WHERE id = #{configId} AND deleted = 0
        ORDER BY update_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 计算缓存配置相似度 -->
    <select id="calculateCacheConfigSimilarity" resultType="decimal">
        SELECT
            ROUND(
                (CASE WHEN c1.cache_type = c2.cache_type THEN 0.3 ELSE 0 END +
                 CASE WHEN c1.cache_strategy = c2.cache_strategy THEN 0.3 ELSE 0 END +
                 CASE WHEN c1.eviction_policy = c2.eviction_policy THEN 0.2 ELSE 0 END +
                 CASE WHEN c1.key_prefix = c2.key_prefix THEN 0.1 ELSE 0 END +
                 CASE WHEN ABS(COALESCE(c1.expire_time, 0) - COALESCE(c2.expire_time, 0)) &lt; 300 THEN 0.1 ELSE 0 END), 2
            ) as similarity
        FROM acc_cache_config c1, acc_cache_config c2
        WHERE c1.id = #{configId1} AND c2.id = #{configId2}
            AND c1.deleted = 0 AND c2.deleted = 0
    </select>

    <!-- 获取缓存配置推荐 -->
    <select id="getCacheConfigRecommendations" resultType="map">
        SELECT
            id,
            config_name as configName,
            config_code as configCode,
            cache_type as cacheType,
            cache_strategy as cacheStrategy,
            applied_count as appliedCount,
            CASE
                WHEN applied_count > 100 AND status = 'ENABLE' THEN 'PROVEN_EFFECTIVE'
                WHEN cache_type = 'BOTH' THEN 'PERFORMANCE_OPTIMIZED'
                WHEN statistics_enabled = 1 AND monitor_enabled = 1 THEN 'WELL_MONITORED'
                ELSE 'STANDARD'
            END as recommendationReason,
            ROUND(applied_count / GREATEST(TIMESTAMPDIFF(DAY, create_time, NOW()), 1), 2) as usageScore
        FROM acc_cache_config
        WHERE deleted = 0 AND status = 'ENABLE'
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        ORDER BY usageScore DESC, applied_count DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

</mapper>
