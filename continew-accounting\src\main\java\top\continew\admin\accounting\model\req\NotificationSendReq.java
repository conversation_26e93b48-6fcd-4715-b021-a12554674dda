package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 发送通知请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "发送通知请求")
public class NotificationSendReq {

    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    @Schema(description = "通知标题", example = "账单提醒")
    @NotBlank(message = "通知标题不能为空")
    private String title;

    @Schema(description = "通知内容", example = "您有一笔新的账单需要确认")
    @NotBlank(message = "通知内容不能为空")
    private String content;

    @Schema(description = "通知类型", example = "TRANSACTION")
    @NotBlank(message = "通知类型不能为空")
    private String notificationType;

    @Schema(description = "优先级", example = "NORMAL")
    private String priority = "NORMAL";

    @Schema(description = "发送渠道列表", example = "[\"SYSTEM_MESSAGE\", \"EMAIL\"]")
    @NotEmpty(message = "发送渠道不能为空")
    private List<String> channels;

    @Schema(description = "目标用户列表", example = "[1, 2, 3]")
    private List<Long> targetUsers;

    @Schema(description = "目标群组列表", example = "[1, 2]")
    private List<Long> targetGroups;

    @Schema(description = "目标角色列表", example = "[\"OWNER\", \"ACCOUNTANT\"]")
    private List<String> targetRoles;

    @Schema(description = "计划发送时间", example = "2025-01-01T10:00:00")
    private LocalDateTime scheduledTime;

    @Schema(description = "过期时间", example = "2025-01-02T10:00:00")
    private LocalDateTime expireTime;

    @Schema(description = "最大重试次数", example = "3")
    private Integer maxRetryCount = 3;

    @Schema(description = "重试间隔（秒）", example = "300")
    private Integer retryInterval = 300;

    @Schema(description = "模板ID", example = "1")
    private Long templateId;

    @Schema(description = "模板参数")
    private Map<String, Object> templateParams;

    @Schema(description = "附件列表")
    private List<String> attachments;

    @Schema(description = "扩展属性")
    private Map<String, Object> extraData;

    @Schema(description = "备注")
    private String remark;

}
