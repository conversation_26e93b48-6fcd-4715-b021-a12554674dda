package top.continew.admin.accounting.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 使用量统计响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "使用量统计响应")
public class UsageStatisticsResp {

    /**
     * ID
     */
    @Schema(description = "ID", example = "1")
    private Long id;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 群组名称
     */
    @Schema(description = "群组名称", example = "我的群组")
    private String groupName;

    /**
     * 统计日期
     */
    @Schema(description = "统计日期", example = "2025-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate statDate;

    /**
     * 交易数量
     */
    @Schema(description = "交易数量", example = "25")
    private Integer transactionCount;

    /**
     * OCR识别次数
     */
    @Schema(description = "OCR识别次数", example = "5")
    private Integer ocrCount;

    /**
     * API调用次数
     */
    @Schema(description = "API调用次数", example = "150")
    private Integer apiCalls;

    /**
     * 存储使用量(字节)
     */
    @Schema(description = "存储使用量(字节)", example = "1048576")
    private Long storageUsed;

    /**
     * 存储使用量(MB)
     */
    @Schema(description = "存储使用量(MB)", example = "1.0")
    private BigDecimal storageUsedMB;

    /**
     * 导出次数
     */
    @Schema(description = "导出次数", example = "3")
    private Integer exportCount;

    /**
     * Webhook调用次数
     */
    @Schema(description = "Webhook调用次数", example = "10")
    private Integer webhookCalls;

    /**
     * 活跃用户数
     */
    @Schema(description = "活跃用户数", example = "8")
    private Integer activeUsers;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-01-01 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2025-01-01 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
