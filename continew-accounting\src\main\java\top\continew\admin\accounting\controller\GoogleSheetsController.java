package top.continew.admin.accounting.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.accounting.model.query.GoogleSheetsConfigQuery;
import top.continew.admin.accounting.model.query.GoogleSheetsSyncLogQuery;
import top.continew.admin.accounting.model.req.GoogleSheetsConfigReq;
import top.continew.admin.accounting.model.req.GoogleSheetsConfigUpdateReq;
import top.continew.admin.accounting.model.req.GoogleSheetsSyncReq;
import top.continew.admin.accounting.model.resp.GoogleSheetsConfigResp;
import top.continew.admin.accounting.model.resp.GoogleSheetsSyncLogResp;
import top.continew.admin.accounting.model.resp.GoogleSheetsSyncResp;
import top.continew.admin.accounting.service.GoogleSheetsService;
import top.continew.admin.common.model.resp.LabelValueResp;
import top.continew.admin.common.model.resp.R;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * Google Sheets集成控制器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "Google Sheets集成 API")
@RestController
@RequestMapping("/accounting/google-sheets")
@RequiredArgsConstructor
@Validated
public class GoogleSheetsController {

    private final GoogleSheetsService googleSheetsService;

    // ==================== 配置管理 ====================

    @Operation(summary = "创建Google Sheets配置", description = "创建新的Google Sheets同步配置")
    @SaCheckPermission("accounting:google-sheets:create")
    @PostMapping("/configs")
    public R<Long> createConfig(@Valid @RequestBody GoogleSheetsConfigReq req) {
        Long configId = googleSheetsService.createConfig(req);
        return R.ok(configId);
    }

    @Operation(summary = "更新Google Sheets配置", description = "更新指定的Google Sheets同步配置")
    @SaCheckPermission("accounting:google-sheets:update")
    @PutMapping("/configs/{configId}")
    public R<Boolean> updateConfig(@Parameter(description = "配置ID") @PathVariable Long configId,
                                   @Valid @RequestBody GoogleSheetsConfigUpdateReq req) {
        Boolean result = googleSheetsService.updateConfig(configId, req);
        return R.ok(result);
    }

    @Operation(summary = "删除Google Sheets配置", description = "删除指定的Google Sheets同步配置")
    @SaCheckPermission("accounting:google-sheets:delete")
    @DeleteMapping("/configs/{configId}")
    public R<Boolean> deleteConfig(@Parameter(description = "配置ID") @PathVariable Long configId) {
        Boolean result = googleSheetsService.deleteConfig(configId);
        return R.ok(result);
    }

    @Operation(summary = "批量删除Google Sheets配置", description = "批量删除Google Sheets同步配置")
    @SaCheckPermission("accounting:google-sheets:delete")
    @DeleteMapping("/configs")
    public R<Boolean> deleteConfigs(@Parameter(description = "配置ID列表") @RequestBody @NotEmpty List<Long> configIds) {
        Boolean result = googleSheetsService.deleteConfigs(configIds);
        return R.ok(result);
    }

    @Operation(summary = "获取Google Sheets配置详情", description = "获取指定Google Sheets配置的详细信息")
    @SaCheckPermission("accounting:google-sheets:detail")
    @GetMapping("/configs/{configId}")
    public R<GoogleSheetsConfigResp> getConfigDetail(@Parameter(description = "配置ID") @PathVariable Long configId) {
        GoogleSheetsConfigResp result = googleSheetsService.getConfigDetail(configId);
        return R.ok(result);
    }

    @Operation(summary = "分页查询Google Sheets配置", description = "分页查询Google Sheets同步配置列表")
    @SaCheckPermission("accounting:google-sheets:page")
    @GetMapping("/configs")
    public R<IPage<GoogleSheetsConfigResp>> pageConfigs(@Valid GoogleSheetsConfigQuery query) {
        IPage<GoogleSheetsConfigResp> result = googleSheetsService.pageConfigs(query);
        return R.ok(result);
    }

    @Operation(summary = "获取配置选项列表", description = "获取可用的Google Sheets配置选项")
    @SaCheckPermission("accounting:google-sheets:list")
    @GetMapping("/configs/options")
    public R<List<LabelValueResp<Long>>> listConfigOptions(@Parameter(description = "群组ID") @RequestParam Long groupId) {
        List<LabelValueResp<Long>> result = googleSheetsService.listConfigOptions(groupId);
        return R.ok(result);
    }

    // ==================== 配置操作 ====================

    @Operation(summary = "启用配置", description = "启用指定的Google Sheets配置")
    @SaCheckPermission("accounting:google-sheets:update")
    @PostMapping("/configs/{configId}/enable")
    public R<Boolean> enableConfig(@Parameter(description = "配置ID") @PathVariable Long configId) {
        Boolean result = googleSheetsService.enableConfig(configId);
        return R.ok(result);
    }

    @Operation(summary = "禁用配置", description = "禁用指定的Google Sheets配置")
    @SaCheckPermission("accounting:google-sheets:update")
    @PostMapping("/configs/{configId}/disable")
    public R<Boolean> disableConfig(@Parameter(description = "配置ID") @PathVariable Long configId) {
        Boolean result = googleSheetsService.disableConfig(configId);
        return R.ok(result);
    }

    @Operation(summary = "复制配置", description = "复制指定的Google Sheets配置")
    @SaCheckPermission("accounting:google-sheets:create")
    @PostMapping("/configs/{configId}/copy")
    public R<Long> copyConfig(@Parameter(description = "配置ID") @PathVariable Long configId,
                              @Parameter(description = "新配置名称") @RequestParam String newName) {
        Long result = googleSheetsService.copyConfig(configId, newName);
        return R.ok(result);
    }

    @Operation(summary = "导出配置", description = "导出Google Sheets配置")
    @SaCheckPermission("accounting:google-sheets:export")
    @PostMapping("/configs/export")
    public R<String> exportConfigs(@Parameter(description = "配置ID列表") @RequestBody @NotEmpty List<Long> configIds) {
        String result = googleSheetsService.exportConfigs(configIds);
        return R.ok(result);
    }

    @Operation(summary = "导入配置", description = "导入Google Sheets配置")
    @SaCheckPermission("accounting:google-sheets:import")
    @PostMapping("/configs/import")
    public R<Map<String, Object>> importConfigs(@Parameter(description = "文件路径") @RequestParam String filePath,
                                                @Parameter(description = "群组ID") @RequestParam Long groupId) {
        Map<String, Object> result = googleSheetsService.importConfigs(filePath, groupId);
        return R.ok(result);
    }

    // ==================== 认证管理 ====================

    @Operation(summary = "验证Google认证", description = "验证Google Sheets API认证状态")
    @SaCheckPermission("accounting:google-sheets:auth")
    @PostMapping("/configs/{configId}/auth/validate")
    public R<Map<String, Object>> validateAuth(@Parameter(description = "配置ID") @PathVariable Long configId) {
        Map<String, Object> result = googleSheetsService.validateAuth(configId);
        return R.ok(result);
    }

    @Operation(summary = "刷新认证令牌", description = "刷新Google API认证令牌")
    @SaCheckPermission("accounting:google-sheets:auth")
    @PostMapping("/configs/{configId}/auth/refresh")
    public R<Boolean> refreshAuthToken(@Parameter(description = "配置ID") @PathVariable Long configId) {
        Boolean result = googleSheetsService.refreshAuthToken(configId);
        return R.ok(result);
    }

    @Operation(summary = "获取OAuth2授权URL", description = "获取Google OAuth2授权链接")
    @SaCheckPermission("accounting:google-sheets:auth")
    @GetMapping("/configs/{configId}/auth/oauth2-url")
    public R<String> getOAuth2AuthUrl(@Parameter(description = "配置ID") @PathVariable Long configId) {
        String result = googleSheetsService.getOAuth2AuthUrl(configId);
        return R.ok(result);
    }

    @Operation(summary = "处理OAuth2回调", description = "处理Google OAuth2授权回调")
    @SaCheckPermission("accounting:google-sheets:auth")
    @PostMapping("/configs/{configId}/auth/oauth2-callback")
    public R<Boolean> handleOAuth2Callback(@Parameter(description = "配置ID") @PathVariable Long configId,
                                           @Parameter(description = "授权码") @RequestParam String code) {
        Boolean result = googleSheetsService.handleOAuth2Callback(configId, code);
        return R.ok(result);
    }

    // ==================== 表格操作 ====================

    @Operation(summary = "获取表格信息", description = "获取Google Sheets表格基本信息")
    @SaCheckPermission("accounting:google-sheets:read")
    @GetMapping("/configs/{configId}/spreadsheet/info")
    public R<Map<String, Object>> getSpreadsheetInfo(@Parameter(description = "配置ID") @PathVariable Long configId) {
        Map<String, Object> result = googleSheetsService.getSpreadsheetInfo(configId);
        return R.ok(result);
    }

    @Operation(summary = "获取工作表列表", description = "获取Google Sheets中的工作表列表")
    @SaCheckPermission("accounting:google-sheets:read")
    @GetMapping("/configs/{configId}/spreadsheet/sheets")
    public R<List<Map<String, Object>>> getSheetList(@Parameter(description = "配置ID") @PathVariable Long configId) {
        List<Map<String, Object>> result = googleSheetsService.getSheetList(configId);
        return R.ok(result);
    }

    @Operation(summary = "创建工作表", description = "在Google Sheets中创建新的工作表")
    @SaCheckPermission("accounting:google-sheets:update")
    @PostMapping("/configs/{configId}/spreadsheet/sheets")
    public R<Boolean> createSheet(@Parameter(description = "配置ID") @PathVariable Long configId,
                                  @Parameter(description = "工作表名称") @RequestParam String sheetName) {
        Boolean result = googleSheetsService.createSheet(configId, sheetName);
        return R.ok(result);
    }

    @Operation(summary = "删除工作表", description = "删除Google Sheets中的工作表")
    @SaCheckPermission("accounting:google-sheets:update")
    @DeleteMapping("/configs/{configId}/spreadsheet/sheets/{sheetName}")
    public R<Boolean> deleteSheet(@Parameter(description = "配置ID") @PathVariable Long configId,
                                  @Parameter(description = "工作表名称") @PathVariable String sheetName) {
        Boolean result = googleSheetsService.deleteSheet(configId, sheetName);
        return R.ok(result);
    }

    @Operation(summary = "预览表格数据", description = "预览Google Sheets中的数据")
    @SaCheckPermission("accounting:google-sheets:read")
    @GetMapping("/configs/{configId}/spreadsheet/preview")
    public R<Map<String, Object>> previewData(@Parameter(description = "配置ID") @PathVariable Long configId,
                                              @Parameter(description = "限制行数") @RequestParam(defaultValue = "10") Integer limit) {
        Map<String, Object> result = googleSheetsService.previewData(configId, limit);
        return R.ok(result);
    }

    // ==================== 同步操作 ====================

    @Operation(summary = "执行同步", description = "执行Google Sheets数据同步")
    @SaCheckPermission("accounting:google-sheets:sync")
    @PostMapping("/sync")
    public R<GoogleSheetsSyncResp> executeSync(@Valid @RequestBody GoogleSheetsSyncReq req) {
        GoogleSheetsSyncResp result = googleSheetsService.executeSync(req);
        return R.ok(result);
    }

    @Operation(summary = "异步执行同步", description = "异步执行Google Sheets数据同步")
    @SaCheckPermission("accounting:google-sheets:sync")
    @PostMapping("/sync/async")
    public R<String> executeSyncAsync(@Valid @RequestBody GoogleSheetsSyncReq req) {
        String result = googleSheetsService.executeSyncAsync(req);
        return R.ok(result);
    }

    @Operation(summary = "停止同步", description = "停止正在进行的同步任务")
    @SaCheckPermission("accounting:google-sheets:sync")
    @PostMapping("/sync/{syncId}/stop")
    public R<Boolean> stopSync(@Parameter(description = "同步ID") @PathVariable String syncId) {
        Boolean result = googleSheetsService.stopSync(syncId);
        return R.ok(result);
    }

    @Operation(summary = "重试同步", description = "重试失败的同步任务")
    @SaCheckPermission("accounting:google-sheets:sync")
    @PostMapping("/sync/{syncId}/retry")
    public R<GoogleSheetsSyncResp> retrySync(@Parameter(description = "同步ID") @PathVariable String syncId) {
        GoogleSheetsSyncResp result = googleSheetsService.retrySync(syncId);
        return R.ok(result);
    }

    @Operation(summary = "获取同步状态", description = "获取同步任务的当前状态")
    @SaCheckPermission("accounting:google-sheets:read")
    @GetMapping("/sync/{syncId}/status")
    public R<Map<String, Object>> getSyncStatus(@Parameter(description = "同步ID") @PathVariable String syncId) {
        Map<String, Object> result = googleSheetsService.getSyncStatus(syncId);
        return R.ok(result);
    }

    @Operation(summary = "获取同步进度", description = "获取同步任务的执行进度")
    @SaCheckPermission("accounting:google-sheets:read")
    @GetMapping("/sync/{syncId}/progress")
    public R<Map<String, Object>> getSyncProgress(@Parameter(description = "同步ID") @PathVariable String syncId) {
        Map<String, Object> result = googleSheetsService.getSyncProgress(syncId);
        return R.ok(result);
    }

    // ==================== 同步日志 ====================

    @Operation(summary = "分页查询同步日志", description = "分页查询Google Sheets同步日志")
    @SaCheckPermission("accounting:google-sheets:log")
    @GetMapping("/sync/logs")
    public R<IPage<GoogleSheetsSyncLogResp>> pageSyncLogs(@Valid GoogleSheetsSyncLogQuery query) {
        IPage<GoogleSheetsSyncLogResp> result = googleSheetsService.pageSyncLogs(query);
        return R.ok(result);
    }

    @Operation(summary = "获取同步日志详情", description = "获取指定同步日志的详细信息")
    @SaCheckPermission("accounting:google-sheets:log")
    @GetMapping("/sync/logs/{logId}")
    public R<GoogleSheetsSyncLogResp> getSyncLogDetail(@Parameter(description = "日志ID") @PathVariable Long logId) {
        GoogleSheetsSyncLogResp result = googleSheetsService.getSyncLogDetail(logId);
        return R.ok(result);
    }

    @Operation(summary = "删除同步日志", description = "删除指定的同步日志")
    @SaCheckPermission("accounting:google-sheets:log:delete")
    @DeleteMapping("/sync/logs/{logId}")
    public R<Boolean> deleteSyncLog(@Parameter(description = "日志ID") @PathVariable Long logId) {
        Boolean result = googleSheetsService.deleteSyncLog(logId);
        return R.ok(result);
    }

    @Operation(summary = "批量删除同步日志", description = "批量删除同步日志")
    @SaCheckPermission("accounting:google-sheets:log:delete")
    @DeleteMapping("/sync/logs")
    public R<Boolean> deleteSyncLogs(@Parameter(description = "日志ID列表") @RequestBody @NotEmpty List<Long> logIds) {
        Boolean result = googleSheetsService.deleteSyncLogs(logIds);
        return R.ok(result);
    }

    @Operation(summary = "清理过期日志", description = "清理指定天数之前的过期日志")
    @SaCheckPermission("accounting:google-sheets:log:cleanup")
    @PostMapping("/sync/logs/cleanup")
    public R<Integer> cleanupExpiredLogs(@Parameter(description = "保留天数") @RequestParam @NotNull Integer retentionDays) {
        Integer result = googleSheetsService.cleanupExpiredLogs(retentionDays);
        return R.ok(result);
    }

    // ==================== 备份管理 ====================

    @Operation(summary = "创建备份", description = "创建Google Sheets数据备份")
    @SaCheckPermission("accounting:google-sheets:backup")
    @PostMapping("/configs/{configId}/backup")
    public R<String> createBackup(@Parameter(description = "配置ID") @PathVariable Long configId,
                                  @Parameter(description = "备份类型") @RequestParam String backupType) {
        String result = googleSheetsService.createBackup(configId, backupType);
        return R.ok(result);
    }

    @Operation(summary = "恢复备份", description = "从备份恢复Google Sheets数据")
    @SaCheckPermission("accounting:google-sheets:backup")
    @PostMapping("/backup/{backupId}/restore")
    public R<Boolean> restoreBackup(@Parameter(description = "备份ID") @PathVariable String backupId) {
        Boolean result = googleSheetsService.restoreBackup(backupId);
        return R.ok(result);
    }

    @Operation(summary = "删除备份", description = "删除指定的备份文件")
    @SaCheckPermission("accounting:google-sheets:backup")
    @DeleteMapping("/backup/{backupId}")
    public R<Boolean> deleteBackup(@Parameter(description = "备份ID") @PathVariable String backupId) {
        Boolean result = googleSheetsService.deleteBackup(backupId);
        return R.ok(result);
    }

    @Operation(summary = "获取备份列表", description = "获取指定配置的备份列表")
    @SaCheckPermission("accounting:google-sheets:backup")
    @GetMapping("/configs/{configId}/backups")
    public R<List<Map<String, Object>>> listBackups(@Parameter(description = "配置ID") @PathVariable Long configId) {
        List<Map<String, Object>> result = googleSheetsService.listBackups(configId);
        return R.ok(result);
    }

    @Operation(summary = "清理过期备份", description = "清理指定天数之前的过期备份")
    @SaCheckPermission("accounting:google-sheets:backup")
    @PostMapping("/configs/{configId}/backups/cleanup")
    public R<Integer> cleanupExpiredBackups(@Parameter(description = "配置ID") @PathVariable Long configId,
                                           @Parameter(description = "保留天数") @RequestParam @NotNull Integer retentionDays) {
        Integer result = googleSheetsService.cleanupExpiredBackups(configId, retentionDays);
        return R.ok(result);
    }

    // ==================== 统计分析 ====================

    @Operation(summary = "获取配置统计", description = "获取Google Sheets配置统计信息")
    @SaCheckPermission("accounting:google-sheets:stats")
    @GetMapping("/stats/configs")
    public R<Map<String, Object>> getConfigStatistics(@Parameter(description = "群组ID") @RequestParam Long groupId) {
        Map<String, Object> result = googleSheetsService.getConfigStatistics(groupId);
        return R.ok(result);
    }

    @Operation(summary = "获取同步统计", description = "获取指定配置的同步统计信息")
    @SaCheckPermission("accounting:google-sheets:stats")
    @GetMapping("/stats/sync")
    public R<Map<String, Object>> getSyncStatistics(@Parameter(description = "配置ID") @RequestParam Long configId,
                                                    @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days) {
        Map<String, Object> result = googleSheetsService.getSyncStatistics(configId, days);
        return R.ok(result);
    }

    @Operation(summary = "获取性能统计", description = "获取指定配置的性能统计信息")
    @SaCheckPermission("accounting:google-sheets:stats")
    @GetMapping("/stats/performance")
    public R<Map<String, Object>> getPerformanceStatistics(@Parameter(description = "配置ID") @RequestParam Long configId,
                                                           @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days) {
        Map<String, Object> result = googleSheetsService.getPerformanceStatistics(configId, days);
        return R.ok(result);
    }

    @Operation(summary = "获取错误统计", description = "获取指定配置的错误统计信息")
    @SaCheckPermission("accounting:google-sheets:stats")
    @GetMapping("/stats/errors")
    public R<Map<String, Object>> getErrorStatistics(@Parameter(description = "配置ID") @RequestParam Long configId,
                                                     @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days) {
        Map<String, Object> result = googleSheetsService.getErrorStatistics(configId, days);
        return R.ok(result);
    }

    @Operation(summary = "获取使用趋势", description = "获取群组的Google Sheets使用趋势")
    @SaCheckPermission("accounting:google-sheets:stats")
    @GetMapping("/stats/trends")
    public R<Map<String, Object>> getUsageTrends(@Parameter(description = "群组ID") @RequestParam Long groupId,
                                                 @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days) {
        Map<String, Object> result = googleSheetsService.getUsageTrends(groupId, days);
        return R.ok(result);
    }

    // ==================== 健康检查 ====================

    @Operation(summary = "检查配置健康状态", description = "检查指定配置的健康状态")
    @SaCheckPermission("accounting:google-sheets:health")
    @GetMapping("/configs/{configId}/health")
    public R<Map<String, Object>> checkConfigHealth(@Parameter(description = "配置ID") @PathVariable Long configId) {
        Map<String, Object> result = googleSheetsService.checkConfigHealth(configId);
        return R.ok(result);
    }

    @Operation(summary = "检查所有配置健康状态", description = "检查群组下所有配置的健康状态")
    @SaCheckPermission("accounting:google-sheets:health")
    @GetMapping("/configs/health")
    public R<List<Map<String, Object>>> checkAllConfigsHealth(@Parameter(description = "群组ID") @RequestParam Long groupId) {
        List<Map<String, Object>> result = googleSheetsService.checkAllConfigsHealth(groupId);
        return R.ok(result);
    }

    @Operation(summary = "修复配置问题", description = "尝试修复指定配置的问题")
    @SaCheckPermission("accounting:google-sheets:repair")
    @PostMapping("/configs/{configId}/repair")
    public R<Map<String, Object>> repairConfig(@Parameter(description = "配置ID") @PathVariable Long configId) {
        Map<String, Object> result = googleSheetsService.repairConfig(configId);
        return R.ok(result);
    }

    // ==================== 工具方法 ====================

    @Operation(summary = "测试连接", description = "测试Google Sheets API连接")
    @SaCheckPermission("accounting:google-sheets:test")
    @PostMapping("/configs/{configId}/test")
    public R<Map<String, Object>> testConnection(@Parameter(description = "配置ID") @PathVariable Long configId) {
        Map<String, Object> result = googleSheetsService.testConnection(configId);
        return R.ok(result);
    }

    @Operation(summary = "验证字段映射", description = "验证字段映射配置的正确性")
    @SaCheckPermission("accounting:google-sheets:validate")
    @PostMapping("/configs/{configId}/validate-mapping")
    public R<Map<String, Object>> validateFieldMapping(@Parameter(description = "配置ID") @PathVariable Long configId) {
        Map<String, Object> result = googleSheetsService.validateFieldMapping(configId);
        return R.ok(result);
    }

    @Operation(summary = "获取字段建议", description = "获取字段映射建议")
    @SaCheckPermission("accounting:google-sheets:read")
    @GetMapping("/configs/{configId}/field-suggestions")
    public R<List<Map<String, Object>>> getFieldSuggestions(@Parameter(description = "配置ID") @PathVariable Long configId) {
        List<Map<String, Object>> result = googleSheetsService.getFieldSuggestions(configId);
        return R.ok(result);
    }

    @Operation(summary = "生成同步报告", description = "生成指定同步任务的详细报告")
    @SaCheckPermission("accounting:google-sheets:report")
    @PostMapping("/sync/{syncId}/report")
    public R<String> generateSyncReport(@Parameter(description = "同步ID") @PathVariable String syncId) {
        String result = googleSheetsService.generateSyncReport(syncId);
        return R.ok(result);
    }

    @Operation(summary = "获取配置模板", description = "获取预定义的配置模板")
    @SaCheckPermission("accounting:google-sheets:read")
    @GetMapping("/templates/{templateType}")
    public R<Map<String, Object>> getConfigTemplate(@Parameter(description = "模板类型") @PathVariable String templateType) {
        Map<String, Object> result = googleSheetsService.getConfigTemplate(templateType);
        return R.ok(result);
    }
}
