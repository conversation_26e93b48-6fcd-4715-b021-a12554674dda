package top.continew.admin.bot.model.entity.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 机器人设置
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "机器人设置")
public class BotSettings {

    /**
     * 命令前缀
     */
    @Schema(description = "命令前缀")
    private String commandPrefix = "/";

    /**
     * 是否启用自动回复
     */
    @Schema(description = "是否启用自动回复")
    private Boolean autoReply = true;

    /**
     * 是否启用命令提示
     */
    @Schema(description = "是否启用命令提示")
    private Boolean commandHints = true;

    /**
     * 是否启用错误提示
     */
    @Schema(description = "是否启用错误提示")
    private Boolean errorMessages = true;

    /**
     * 语言设置
     */
    @Schema(description = "语言设置")
    private String language = "zh-CN";

    /**
     * 时区设置
     */
    @Schema(description = "时区设置")
    private String timezone = "Asia/Shanghai";

    /**
     * 允许的用户列表 (空表示允许所有用户)
     */
    @Schema(description = "允许的用户列表")
    private List<String> allowedUsers;

    /**
     * 管理员用户列表
     */
    @Schema(description = "管理员用户列表")
    private List<String> adminUsers;

    /**
     * 命令别名映射
     */
    @Schema(description = "命令别名映射")
    private Map<String, String> commandAliases;

    /**
     * 自定义回复消息
     */
    @Schema(description = "自定义回复消息")
    private Map<String, String> customMessages;

    /**
     * 通知设置
     */
    @Schema(description = "通知设置")
    private NotificationSettings notifications;

    /**
     * 安全设置
     */
    @Schema(description = "安全设置")
    private SecuritySettings security;

    /**
     * 通知设置
     */
    @Data
    @Schema(description = "通知设置")
    public static class NotificationSettings {
        /**
         * 是否启用交易通知
         */
        @Schema(description = "是否启用交易通知")
        private Boolean transactionNotifications = true;

        /**
         * 是否启用错误通知
         */
        @Schema(description = "是否启用错误通知")
        private Boolean errorNotifications = true;

        /**
         * 是否启用系统通知
         */
        @Schema(description = "是否启用系统通知")
        private Boolean systemNotifications = true;

        /**
         * 通知频率限制 (秒)
         */
        @Schema(description = "通知频率限制")
        private Integer notificationCooldown = 5;
    }

    /**
     * 安全设置
     */
    @Data
    @Schema(description = "安全设置")
    public static class SecuritySettings {
        /**
         * 是否启用速率限制
         */
        @Schema(description = "是否启用速率限制")
        private Boolean rateLimitEnabled = true;

        /**
         * 每分钟最大请求数
         */
        @Schema(description = "每分钟最大请求数")
        private Integer maxRequestsPerMinute = 30;

        /**
         * 是否启用IP白名单
         */
        @Schema(description = "是否启用IP白名单")
        private Boolean ipWhitelistEnabled = false;

        /**
         * IP白名单
         */
        @Schema(description = "IP白名单")
        private List<String> ipWhitelist;

        /**
         * 是否记录所有消息
         */
        @Schema(description = "是否记录所有消息")
        private Boolean logAllMessages = false;

        /**
         * 敏感词过滤
         */
        @Schema(description = "敏感词过滤")
        private List<String> bannedWords;
    }
}
