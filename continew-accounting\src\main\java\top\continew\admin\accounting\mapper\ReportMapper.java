package top.continew.admin.accounting.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.accounting.model.query.ReportQuery;
import top.continew.admin.accounting.model.resp.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 报表统计 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface ReportMapper {

    /**
     * 查询报表总览数据
     *
     * @param query 查询条件
     * @return 总览数据
     */
    ReportOverviewResp selectReportOverview(@Param("query") ReportQuery query);

    /**
     * 查询趋势数据
     *
     * @param query 查询条件
     * @return 趋势数据列表
     */
    List<ReportTrendResp> selectReportTrend(@Param("query") ReportQuery query);

    /**
     * 查询分类统计数据
     *
     * @param query 查询条件
     * @return 分类统计列表
     */
    List<ReportCategoryResp> selectReportCategory(@Param("query") ReportQuery query);

    /**
     * 查询钱包统计数据
     *
     * @param query 查询条件
     * @return 钱包统计列表
     */
    List<ReportWalletResp> selectReportWallet(@Param("query") ReportQuery query);

    /**
     * 查询成员统计数据
     *
     * @param query 查询条件
     * @return 成员统计列表
     */
    List<ReportMemberResp> selectReportMember(@Param("query") ReportQuery query);

    /**
     * 查询标签统计数据
     *
     * @param query 查询条件
     * @return 标签统计列表
     */
    List<ReportTagResp> selectReportTag(@Param("query") ReportQuery query);

    /**
     * 查询收支对比数据
     *
     * @param groupId   群组ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 收支对比数据
     */
    IncomeExpenseCompareResp selectIncomeExpenseCompare(@Param("groupId") Long groupId,
                                                       @Param("startDate") LocalDate startDate,
                                                       @Param("endDate") LocalDate endDate);

    /**
     * 查询月度统计数据
     *
     * @param groupId 群组ID
     * @param year    年份
     * @return 月度统计列表
     */
    List<MonthlyStatResp> selectMonthlyStats(@Param("groupId") Long groupId, @Param("year") Integer year);

    /**
     * 查询年度统计数据
     *
     * @param groupId   群组ID
     * @param startYear 开始年份
     * @param endYear   结束年份
     * @return 年度统计列表
     */
    List<YearlyStatResp> selectYearlyStats(@Param("groupId") Long groupId,
                                          @Param("startYear") Integer startYear,
                                          @Param("endYear") Integer endYear);

    /**
     * 查询预算执行情况
     *
     * @param groupId 群组ID
     * @param month   月份（格式：2025-01）
     * @return 预算执行列表
     */
    List<BudgetExecutionResp> selectBudgetExecution(@Param("groupId") Long groupId, @Param("month") String month);

    /**
     * 查询交易频率统计
     *
     * @param query 查询条件
     * @return 交易频率统计
     */
    TransactionFrequencyResp selectTransactionFrequency(@Param("query") ReportQuery query);

    /**
     * 查询时段分析数据
     *
     * @param query 查询条件
     * @return 时段分析列表
     */
    List<TimeSlotAnalysisResp> selectTimeSlotAnalysis(@Param("query") ReportQuery query);

    /**
     * 查询上期对比数据
     *
     * @param groupId        群组ID
     * @param currentStart   当前期间开始日期
     * @param currentEnd     当前期间结束日期
     * @param previousStart  上期开始日期
     * @param previousEnd    上期结束日期
     * @return 上期对比数据
     */
    PeriodCompareResp selectPeriodCompare(@Param("groupId") Long groupId,
                                         @Param("currentStart") LocalDate currentStart,
                                         @Param("currentEnd") LocalDate currentEnd,
                                         @Param("previousStart") LocalDate previousStart,
                                         @Param("previousEnd") LocalDate previousEnd);

    /**
     * 内部类：收支对比响应
     */
    class IncomeExpenseCompareResp {
        private BigDecimal totalIncome;
        private BigDecimal totalExpense;
        private BigDecimal netIncome;
        private Integer incomeCount;
        private Integer expenseCount;
        private BigDecimal incomeGrowthRate;
        private BigDecimal expenseGrowthRate;

        // Getters and Setters
        public BigDecimal getTotalIncome() { return totalIncome; }
        public void setTotalIncome(BigDecimal totalIncome) { this.totalIncome = totalIncome; }
        public BigDecimal getTotalExpense() { return totalExpense; }
        public void setTotalExpense(BigDecimal totalExpense) { this.totalExpense = totalExpense; }
        public BigDecimal getNetIncome() { return netIncome; }
        public void setNetIncome(BigDecimal netIncome) { this.netIncome = netIncome; }
        public Integer getIncomeCount() { return incomeCount; }
        public void setIncomeCount(Integer incomeCount) { this.incomeCount = incomeCount; }
        public Integer getExpenseCount() { return expenseCount; }
        public void setExpenseCount(Integer expenseCount) { this.expenseCount = expenseCount; }
        public BigDecimal getIncomeGrowthRate() { return incomeGrowthRate; }
        public void setIncomeGrowthRate(BigDecimal incomeGrowthRate) { this.incomeGrowthRate = incomeGrowthRate; }
        public BigDecimal getExpenseGrowthRate() { return expenseGrowthRate; }
        public void setExpenseGrowthRate(BigDecimal expenseGrowthRate) { this.expenseGrowthRate = expenseGrowthRate; }
    }

    /**
     * 内部类：月度统计响应
     */
    class MonthlyStatResp {
        private String month;
        private BigDecimal incomeAmount;
        private BigDecimal expenseAmount;
        private BigDecimal netAmount;
        private Integer transactionCount;

        // Getters and Setters
        public String getMonth() { return month; }
        public void setMonth(String month) { this.month = month; }
        public BigDecimal getIncomeAmount() { return incomeAmount; }
        public void setIncomeAmount(BigDecimal incomeAmount) { this.incomeAmount = incomeAmount; }
        public BigDecimal getExpenseAmount() { return expenseAmount; }
        public void setExpenseAmount(BigDecimal expenseAmount) { this.expenseAmount = expenseAmount; }
        public BigDecimal getNetAmount() { return netAmount; }
        public void setNetAmount(BigDecimal netAmount) { this.netAmount = netAmount; }
        public Integer getTransactionCount() { return transactionCount; }
        public void setTransactionCount(Integer transactionCount) { this.transactionCount = transactionCount; }
    }

    /**
     * 内部类：年度统计响应
     */
    class YearlyStatResp {
        private Integer year;
        private BigDecimal incomeAmount;
        private BigDecimal expenseAmount;
        private BigDecimal netAmount;
        private Integer transactionCount;

        // Getters and Setters
        public Integer getYear() { return year; }
        public void setYear(Integer year) { this.year = year; }
        public BigDecimal getIncomeAmount() { return incomeAmount; }
        public void setIncomeAmount(BigDecimal incomeAmount) { this.incomeAmount = incomeAmount; }
        public BigDecimal getExpenseAmount() { return expenseAmount; }
        public void setExpenseAmount(BigDecimal expenseAmount) { this.expenseAmount = expenseAmount; }
        public BigDecimal getNetAmount() { return netAmount; }
        public void setNetAmount(BigDecimal netAmount) { this.netAmount = netAmount; }
        public Integer getTransactionCount() { return transactionCount; }
        public void setTransactionCount(Integer transactionCount) { this.transactionCount = transactionCount; }
    }

    /**
     * 内部类：预算执行响应
     */
    class BudgetExecutionResp {
        private Long categoryId;
        private String categoryName;
        private BigDecimal budgetAmount;
        private BigDecimal actualAmount;
        private BigDecimal remainingAmount;
        private BigDecimal executionRate;

        // Getters and Setters
        public Long getCategoryId() { return categoryId; }
        public void setCategoryId(Long categoryId) { this.categoryId = categoryId; }
        public String getCategoryName() { return categoryName; }
        public void setCategoryName(String categoryName) { this.categoryName = categoryName; }
        public BigDecimal getBudgetAmount() { return budgetAmount; }
        public void setBudgetAmount(BigDecimal budgetAmount) { this.budgetAmount = budgetAmount; }
        public BigDecimal getActualAmount() { return actualAmount; }
        public void setActualAmount(BigDecimal actualAmount) { this.actualAmount = actualAmount; }
        public BigDecimal getRemainingAmount() { return remainingAmount; }
        public void setRemainingAmount(BigDecimal remainingAmount) { this.remainingAmount = remainingAmount; }
        public BigDecimal getExecutionRate() { return executionRate; }
        public void setExecutionRate(BigDecimal executionRate) { this.executionRate = executionRate; }
    }

    /**
     * 内部类：交易频率响应
     */
    class TransactionFrequencyResp {
        private BigDecimal avgDailyTransactions;
        private Integer maxDailyTransactions;
        private Integer minDailyTransactions;
        private Integer activeDays;
        private Integer totalDays;

        // Getters and Setters
        public BigDecimal getAvgDailyTransactions() { return avgDailyTransactions; }
        public void setAvgDailyTransactions(BigDecimal avgDailyTransactions) { this.avgDailyTransactions = avgDailyTransactions; }
        public Integer getMaxDailyTransactions() { return maxDailyTransactions; }
        public void setMaxDailyTransactions(Integer maxDailyTransactions) { this.maxDailyTransactions = maxDailyTransactions; }
        public Integer getMinDailyTransactions() { return minDailyTransactions; }
        public void setMinDailyTransactions(Integer minDailyTransactions) { this.minDailyTransactions = minDailyTransactions; }
        public Integer getActiveDays() { return activeDays; }
        public void setActiveDays(Integer activeDays) { this.activeDays = activeDays; }
        public Integer getTotalDays() { return totalDays; }
        public void setTotalDays(Integer totalDays) { this.totalDays = totalDays; }
    }

    /**
     * 内部类：时段分析响应
     */
    class TimeSlotAnalysisResp {
        private String timeSlot;
        private BigDecimal totalAmount;
        private Integer transactionCount;
        private BigDecimal avgAmount;

        // Getters and Setters
        public String getTimeSlot() { return timeSlot; }
        public void setTimeSlot(String timeSlot) { this.timeSlot = timeSlot; }
        public BigDecimal getTotalAmount() { return totalAmount; }
        public void setTotalAmount(BigDecimal totalAmount) { this.totalAmount = totalAmount; }
        public Integer getTransactionCount() { return transactionCount; }
        public void setTransactionCount(Integer transactionCount) { this.transactionCount = transactionCount; }
        public BigDecimal getAvgAmount() { return avgAmount; }
        public void setAvgAmount(BigDecimal avgAmount) { this.avgAmount = avgAmount; }
    }

    /**
     * 内部类：期间对比响应
     */
    class PeriodCompareResp {
        private BigDecimal currentIncome;
        private BigDecimal currentExpense;
        private BigDecimal previousIncome;
        private BigDecimal previousExpense;
        private BigDecimal incomeGrowthRate;
        private BigDecimal expenseGrowthRate;

        // Getters and Setters
        public BigDecimal getCurrentIncome() { return currentIncome; }
        public void setCurrentIncome(BigDecimal currentIncome) { this.currentIncome = currentIncome; }
        public BigDecimal getCurrentExpense() { return currentExpense; }
        public void setCurrentExpense(BigDecimal currentExpense) { this.currentExpense = currentExpense; }
        public BigDecimal getPreviousIncome() { return previousIncome; }
        public void setPreviousIncome(BigDecimal previousIncome) { this.previousIncome = previousIncome; }
        public BigDecimal getPreviousExpense() { return previousExpense; }
        public void setPreviousExpense(BigDecimal previousExpense) { this.previousExpense = previousExpense; }
        public BigDecimal getIncomeGrowthRate() { return incomeGrowthRate; }
        public void setIncomeGrowthRate(BigDecimal incomeGrowthRate) { this.incomeGrowthRate = incomeGrowthRate; }
        public BigDecimal getExpenseGrowthRate() { return expenseGrowthRate; }
        public void setExpenseGrowthRate(BigDecimal expenseGrowthRate) { this.expenseGrowthRate = expenseGrowthRate; }
    }
}
