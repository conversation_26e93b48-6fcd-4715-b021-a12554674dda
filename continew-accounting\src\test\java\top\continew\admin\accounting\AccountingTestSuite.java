package top.continew.admin.accounting;

import org.junit.platform.suite.api.IncludeClassNamePatterns;
import org.junit.platform.suite.api.SelectPackages;
import org.junit.platform.suite.api.Suite;
import org.junit.platform.suite.api.SuiteDisplayName;

/**
 * 记账模块测试套件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Suite
@SuiteDisplayName("记账模块测试套件")
@SelectPackages({
    "top.continew.admin.accounting.service",
    "top.continew.admin.accounting.controller",
    "top.continew.admin.accounting.integration"
})
@IncludeClassNamePatterns(".*Test")
public class AccountingTestSuite {
    // 测试套件类，用于组织和运行所有测试
}
