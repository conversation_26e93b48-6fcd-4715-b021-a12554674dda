package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

/**
 * 数据同步配置更新请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "数据同步配置更新请求")
public class DataSyncConfigUpdateReq {

    /**
     * 配置名称
     */
    @Schema(description = "配置名称", example = "交易数据同步到Google Sheets")
    private String configName;

    /**
     * 配置描述
     */
    @Schema(description = "配置描述", example = "将交易数据实时同步到Google Sheets进行分析")
    private String configDescription;

    /**
     * 同步方向
     */
    @Schema(description = "同步方向", example = "TO_TARGET", allowableValues = {
            "TO_TARGET", "FROM_SOURCE", "BIDIRECTIONAL"
    })
    private String syncDirection;

    /**
     * 同步模式
     */
    @Schema(description = "同步模式", example = "INCREMENTAL", allowableValues = {
            "FULL", "INCREMENTAL", "CUSTOM"
    })
    private String syncMode;

    /**
     * 源配置
     */
    @Schema(description = "源配置")
    private Map<String, Object> sourceConfig;

    /**
     * 目标配置
     */
    @Schema(description = "目标配置")
    private Map<String, Object> targetConfig;

    /**
     * 字段映射
     */
    @Schema(description = "字段映射")
    private Map<String, Object> fieldMapping;

    /**
     * 过滤条件
     */
    @Schema(description = "过滤条件")
    private Map<String, Object> filterCondition;

    /**
     * 转换规则
     */
    @Schema(description = "转换规则")
    private Map<String, Object> transformRules;

    /**
     * 冲突解决策略
     */
    @Schema(description = "冲突解决策略", example = "LOCAL_WINS", allowableValues = {
            "LOCAL_WINS", "REMOTE_WINS", "MERGE", "SKIP", "MANUAL"
    })
    private String conflictResolution;

    /**
     * 同步频率
     */
    @Schema(description = "同步频率", example = "HOURLY", allowableValues = {
            "MANUAL", "REAL_TIME", "MINUTELY", "HOURLY", "DAILY", "WEEKLY", "MONTHLY", "CUSTOM"
    })
    private String syncFrequency;

    /**
     * 同步设置
     */
    @Schema(description = "同步设置")
    private Map<String, Object> syncSettings;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    /**
     * 最大重试次数
     */
    @Schema(description = "最大重试次数", example = "3")
    private Integer maxRetryCount;

    /**
     * 自定义标签
     */
    @Schema(description = "自定义标签")
    private Map<String, Object> customTags;

    /**
     * 排序
     */
    @Schema(description = "排序", example = "1")
    private Integer sortOrder;
}
