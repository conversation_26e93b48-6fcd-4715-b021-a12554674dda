package top.continew.admin.accounting.model.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 报表成员统计响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "报表成员统计响应")
public class ReportMemberResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 成员ID
     */
    @Schema(description = "成员ID", example = "1")
    private Long memberId;

    /**
     * 成员姓名
     */
    @Schema(description = "成员姓名", example = "张三")
    @ExcelProperty("成员姓名")
    private String memberName;

    /**
     * 成员头像
     */
    @Schema(description = "成员头像", example = "https://example.com/avatar.jpg")
    private String memberAvatar;

    /**
     * 角色
     */
    @Schema(description = "角色", example = "MEMBER")
    @ExcelProperty("角色")
    private String role;

    /**
     * 记录的收入金额
     */
    @Schema(description = "记录的收入金额", example = "3000.00")
    @ExcelProperty("记录收入金额")
    private BigDecimal recordedIncomeAmount;

    /**
     * 记录的支出金额
     */
    @Schema(description = "记录的支出金额", example = "2000.00")
    @ExcelProperty("记录支出金额")
    private BigDecimal recordedExpenseAmount;

    /**
     * 记录的交易笔数
     */
    @Schema(description = "记录的交易笔数", example = "30")
    @ExcelProperty("记录交易笔数")
    private Integer recordedTransactionCount;

    /**
     * 参与的收入金额（作为参与者）
     */
    @Schema(description = "参与的收入金额", example = "1500.00")
    private BigDecimal participatedIncomeAmount;

    /**
     * 参与的支出金额（作为参与者）
     */
    @Schema(description = "参与的支出金额", example = "1200.00")
    private BigDecimal participatedExpenseAmount;

    /**
     * 参与的交易笔数
     */
    @Schema(description = "参与的交易笔数", example = "20")
    private Integer participatedTransactionCount;

    /**
     * 总贡献金额（记录+参与）
     */
    @Schema(description = "总贡献金额", example = "4500.00")
    private BigDecimal totalContributionAmount;

    /**
     * 活跃天数
     */
    @Schema(description = "活跃天数", example = "25")
    private Integer activeDays;

    /**
     * 最后活跃时间
     */
    @Schema(description = "最后活跃时间", example = "2025-01-01T12:00:00")
    private java.time.LocalDateTime lastActiveTime;

    /**
     * 占比（百分比）
     */
    @Schema(description = "占比", example = "18.5")
    private BigDecimal percentage;
}
