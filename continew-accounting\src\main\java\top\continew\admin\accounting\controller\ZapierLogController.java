package top.continew.admin.accounting.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.accounting.model.query.ZapierLogQuery;
import top.continew.admin.accounting.model.resp.ZapierLogResp;
import top.continew.admin.accounting.service.ZapierLogService;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.controller.BaseController;
import top.continew.starter.core.util.response.Response;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Zapier日志管理
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "Zapier日志管理")
@RestController
@RequiredArgsConstructor
@Validated
@CrudRequestMapping(value = "/accounting/zapier/log", api = {CrudRequestMapping.Api.PAGE, CrudRequestMapping.Api.GET, CrudRequestMapping.Api.DELETE})
public class ZapierLogController extends BaseController<ZapierLogService, ZapierLogResp, ZapierLogResp, ZapierLogQuery, Object, Object> {

    @Operation(summary = "查询配置的执行日志", description = "查询指定配置的执行日志")
    @GetMapping("/config/{configId}")
    public Response<List<ZapierLogResp>> listByConfigId(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long configId,
            @Parameter(description = "限制数量", example = "50") @RequestParam(defaultValue = "50") Integer limit) {
        return Response.success(baseService.listByConfigId(configId, limit));
    }

    @Operation(summary = "查询群组的执行日志", description = "查询指定群组的执行日志")
    @GetMapping("/group/{groupId}")
    public Response<List<ZapierLogResp>> listByGroupId(
            @Parameter(description = "群组ID", example = "1") @PathVariable Long groupId,
            @Parameter(description = "限制数量", example = "50") @RequestParam(defaultValue = "50") Integer limit) {
        return Response.success(baseService.listByGroupId(groupId, limit));
    }

    @Operation(summary = "查询失败的执行日志", description = "查询指定配置在指定时间内的失败日志")
    @GetMapping("/failed/{configId}")
    public Response<List<ZapierLogResp>> listFailedLogs(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long configId,
            @Parameter(description = "小时数", example = "24") @RequestParam(defaultValue = "24") Integer hours) {
        return Response.success(baseService.listFailedLogs(configId, hours));
    }

    @Operation(summary = "查询执行统计", description = "查询指定配置在指定时间范围内的执行统计")
    @GetMapping("/stats/{configId}")
    public Response<Map<String, Object>> getExecutionStats(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long configId,
            @Parameter(description = "开始时间") @RequestParam LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam LocalDateTime endTime) {
        return Response.success(baseService.getExecutionStats(configId, startTime, endTime));
    }

    @Operation(summary = "查询群组执行统计", description = "查询指定群组在指定时间范围内的执行统计")
    @GetMapping("/stats/group/{groupId}")
    public Response<Map<String, Object>> getGroupExecutionStats(
            @Parameter(description = "群组ID", example = "1") @PathVariable Long groupId,
            @Parameter(description = "开始时间") @RequestParam LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam LocalDateTime endTime) {
        return Response.success(baseService.getGroupExecutionStats(groupId, startTime, endTime));
    }

    @Operation(summary = "查询执行趋势", description = "查询指定配置在指定时间范围内的执行趋势")
    @GetMapping("/trend/{configId}")
    public Response<List<Map<String, Object>>> getExecutionTrend(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long configId,
            @Parameter(description = "开始时间") @RequestParam LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam LocalDateTime endTime,
            @Parameter(description = "时间间隔", example = "hour") @RequestParam(defaultValue = "hour") String interval) {
        return Response.success(baseService.getExecutionTrend(configId, startTime, endTime, interval));
    }

    @Operation(summary = "查询错误分布统计", description = "查询指定配置在指定时间范围内的错误分布统计")
    @GetMapping("/error-distribution/{configId}")
    public Response<List<Map<String, Object>>> getErrorDistribution(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long configId,
            @Parameter(description = "开始时间") @RequestParam LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam LocalDateTime endTime) {
        return Response.success(baseService.getErrorDistribution(configId, startTime, endTime));
    }

    @Operation(summary = "查询性能统计", description = "查询指定配置在指定时间范围内的性能统计")
    @GetMapping("/performance/{configId}")
    public Response<Map<String, Object>> getPerformanceStats(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long configId,
            @Parameter(description = "开始时间") @RequestParam LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam LocalDateTime endTime) {
        return Response.success(baseService.getPerformanceStats(configId, startTime, endTime));
    }

    @Operation(summary = "查询慢执行日志", description = "查询指定配置的慢执行日志")
    @GetMapping("/slow/{configId}")
    public Response<List<ZapierLogResp>> listSlowExecutionLogs(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long configId,
            @Parameter(description = "执行时间阈值（毫秒）", example = "5000") @RequestParam(defaultValue = "5000") Long thresholdMs,
            @Parameter(description = "小时数", example = "24") @RequestParam(defaultValue = "24") Integer hours) {
        return Response.success(baseService.listSlowExecutionLogs(configId, thresholdMs, hours));
    }

    @Operation(summary = "查询重试日志", description = "查询指定原始日志的重试日志")
    @GetMapping("/retry/{originalLogId}")
    public Response<List<ZapierLogResp>> listRetryLogs(
            @Parameter(description = "原始日志ID", example = "1") @PathVariable Long originalLogId) {
        return Response.success(baseService.listRetryLogs(originalLogId));
    }

    @Operation(summary = "查询业务相关日志", description = "查询指定业务的相关日志")
    @GetMapping("/business")
    public Response<List<ZapierLogResp>> listByBusiness(
            @Parameter(description = "业务类型", example = "TRANSACTION") @RequestParam String businessType,
            @Parameter(description = "业务ID", example = "123") @RequestParam Long businessId) {
        return Response.success(baseService.listByBusiness(businessType, businessId));
    }

    @Operation(summary = "删除过期日志", description = "删除指定天数之前的过期日志")
    @DeleteMapping("/expired")
    public Response<Integer> deleteExpiredLogs(
            @Parameter(description = "保留天数", example = "30") @RequestParam Integer days) {
        return Response.success(baseService.deleteExpiredLogs(days));
    }

    @Operation(summary = "删除配置的所有日志", description = "删除指定配置的所有日志")
    @DeleteMapping("/config/{configId}")
    public Response<Integer> deleteByConfigId(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long configId) {
        return Response.success(baseService.deleteByConfigId(configId));
    }

    @Operation(summary = "删除群组的所有日志", description = "删除指定群组的所有日志")
    @DeleteMapping("/group/{groupId}")
    public Response<Integer> deleteByGroupId(
            @Parameter(description = "群组ID", example = "1") @PathVariable Long groupId) {
        return Response.success(baseService.deleteByGroupId(groupId));
    }

    @Operation(summary = "查询最近执行日志", description = "查询指定配置的最近执行日志")
    @GetMapping("/recent/{configId}")
    public Response<List<ZapierLogResp>> listRecentLogs(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long configId,
            @Parameter(description = "限制数量", example = "20") @RequestParam(defaultValue = "20") Integer limit) {
        return Response.success(baseService.listRecentLogs(configId, limit));
    }

    @Operation(summary = "查询异常日志", description = "查询指定群组在指定时间内的异常日志")
    @GetMapping("/anomaly/{groupId}")
    public Response<List<ZapierLogResp>> listAnomalyLogs(
            @Parameter(description = "群组ID", example = "1") @PathVariable Long groupId,
            @Parameter(description = "小时数", example = "24") @RequestParam(defaultValue = "24") Integer hours) {
        return Response.success(baseService.listAnomalyLogs(groupId, hours));
    }

    @Operation(summary = "查询HTTP状态码分布", description = "查询指定配置在指定时间范围内的HTTP状态码分布")
    @GetMapping("/http-status/{configId}")
    public Response<List<Map<String, Object>>> getHttpStatusDistribution(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long configId,
            @Parameter(description = "开始时间") @RequestParam LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam LocalDateTime endTime) {
        return Response.success(baseService.getHttpStatusDistribution(configId, startTime, endTime));
    }

    @Operation(summary = "查询数据传输统计", description = "查询指定配置在指定时间范围内的数据传输统计")
    @GetMapping("/data-transfer/{configId}")
    public Response<Map<String, Object>> getDataTransferStats(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long configId,
            @Parameter(description = "开始时间") @RequestParam LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam LocalDateTime endTime) {
        return Response.success(baseService.getDataTransferStats(configId, startTime, endTime));
    }

    @Operation(summary = "查询执行时间分布", description = "查询指定配置在指定时间范围内的执行时间分布")
    @GetMapping("/execution-time/{configId}")
    public Response<List<Map<String, Object>>> getExecutionTimeDistribution(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long configId,
            @Parameter(description = "开始时间") @RequestParam LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam LocalDateTime endTime) {
        return Response.success(baseService.getExecutionTimeDistribution(configId, startTime, endTime));
    }

    @Operation(summary = "导出日志", description = "导出符合条件的日志")
    @PostMapping("/export")
    public Response<Map<String, Object>> exportLogs(
            @Parameter(description = "查询条件") @RequestBody ZapierLogQuery query) {
        return Response.success(baseService.exportLogs(query));
    }

    @Operation(summary = "清理日志", description = "清理指定配置的日志")
    @DeleteMapping("/cleanup/{configId}")
    public Response<Integer> cleanupLogs(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long configId,
            @Parameter(description = "保留天数", example = "30") @RequestParam Integer days) {
        return Response.success(baseService.cleanupLogs(configId, days));
    }

    @Operation(summary = "获取日志统计概览", description = "获取指定群组的日志统计概览")
    @GetMapping("/overview/{groupId}")
    public Response<Map<String, Object>> getLogOverview(
            @Parameter(description = "群组ID", example = "1") @PathVariable Long groupId) {
        return Response.success(baseService.getLogOverview(groupId));
    }

    @Operation(summary = "获取日志详情", description = "获取指定日志的详细信息（包含配置信息）")
    @GetMapping("/detail/{id}")
    public Response<Map<String, Object>> getLogDetail(
            @Parameter(description = "日志ID", example = "1") @PathVariable Long id) {
        return Response.success(baseService.getLogDetail(id));
    }
}
