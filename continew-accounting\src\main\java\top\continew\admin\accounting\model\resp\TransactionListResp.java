package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.accounting.enums.SplitType;
import top.continew.admin.accounting.enums.TransactionType;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 账单列表响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "账单列表响应")
public class TransactionListResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Schema(description = "ID", example = "1")
    private Long id;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 群组名称
     */
    @Schema(description = "群组名称", example = "我的记账群")
    private String groupName;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型", example = "EXPENSE")
    private TransactionType type;

    /**
     * 金额
     */
    @Schema(description = "金额", example = "100.50")
    private BigDecimal amount;

    /**
     * 币种
     */
    @Schema(description = "币种", example = "CNY")
    private String currency;

    /**
     * 描述
     */
    @Schema(description = "描述", example = "午餐费用")
    private String description;

    /**
     * 分类
     */
    @Schema(description = "分类", example = "餐饮")
    private String category;

    /**
     * 标签
     */
    @Schema(description = "标签", example = "工作,午餐")
    private String tags;

    /**
     * 交易时间
     */
    @Schema(description = "交易时间", example = "2025-01-01T12:00:00")
    private LocalDateTime transactionDate;

    /**
     * 分摊类型
     */
    @Schema(description = "分摊类型", example = "EQUAL")
    private SplitType splitType;

    /**
     * 是否有分摊
     */
    @Schema(description = "是否有分摊", example = "true")
    private Boolean hasSplit;

    /**
     * 分摊参与者数量
     */
    @Schema(description = "分摊参与者数量", example = "3")
    private Integer splitParticipantCount;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID", example = "1")
    private Long createUser;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名", example = "张三")
    private String createUserName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-01-01T12:00:00")
    private LocalDateTime createTime;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "1")
    private Integer status;
}
