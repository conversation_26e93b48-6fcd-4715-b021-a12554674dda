package top.continew.admin.accounting.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.accounting.enums.TransactionType;
import top.continew.starter.extension.crud.model.query.SortQuery;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 分类查询条件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "分类查询条件")
public class CategoryQuery extends SortQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 分类名称
     */
    @Schema(description = "分类名称", example = "餐饮")
    private String name;

    /**
     * 父分类ID
     */
    @Schema(description = "父分类ID", example = "1")
    private Long parentId;

    /**
     * 分类类型
     */
    @Schema(description = "分类类型", example = "EXPENSE")
    private TransactionType type;

    /**
     * 是否为系统分类
     */
    @Schema(description = "是否为系统分类", example = "false")
    private Boolean isSystem;

    /**
     * 是否为默认分类
     */
    @Schema(description = "是否为默认分类", example = "false")
    private Boolean isDefault;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "1")
    private Integer status;

    /**
     * 排除的分类ID列表
     */
    @Schema(description = "排除的分类ID列表")
    private List<Long> excludeCategoryIds;

    /**
     * 关键词
     */
    @Schema(description = "关键词", example = "餐饮")
    private String keyword;
}
