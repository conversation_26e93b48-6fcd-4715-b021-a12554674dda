package top.continew.admin.accounting.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 缓存淘汰策略枚举
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Getter
@RequiredArgsConstructor
public enum CacheEvictionPolicyEnum {

    /**
     * 最近最少使用
     */
    LRU("LRU", "最近最少使用", "淘汰最近最少使用的缓存项"),

    /**
     * 最近最少频率使用
     */
    LFU("LFU", "最近最少频率使用", "淘汰使用频率最低的缓存项"),

    /**
     * 先进先出
     */
    FIFO("FIFO", "先进先出", "淘汰最早进入缓存的项"),

    /**
     * 随机淘汰
     */
    RANDOM("RANDOM", "随机淘汰", "随机选择缓存项进行淘汰"),

    /**
     * 基于时间的淘汰
     */
    TTL("TTL", "基于时间的淘汰", "根据过期时间淘汰缓存项"),

    /**
     * 基于大小的淘汰
     */
    SIZE("SIZE", "基于大小的淘汰", "根据缓存大小限制淘汰缓存项"),

    /**
     * 基于权重的淘汰
     */
    WEIGHT("WEIGHT", "基于权重的淘汰", "根据缓存项权重进行淘汰"),

    /**
     * 不淘汰
     */
    NONE("NONE", "不淘汰", "不进行缓存淘汰");

    /**
     * 策略代码
     */
    private final String code;

    /**
     * 策略名称
     */
    private final String name;

    /**
     * 策略描述
     */
    private final String description;

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static CacheEvictionPolicyEnum getByCode(String code) {
        for (CacheEvictionPolicyEnum policy : values()) {
            if (policy.getCode().equals(code)) {
                return policy;
            }
        }
        return LRU;
    }

    /**
     * 是否为基于使用的策略
     *
     * @return 是否为基于使用的策略
     */
    public boolean isUsageBased() {
        return this == LRU || this == LFU;
    }

    /**
     * 是否为基于时间的策略
     *
     * @return 是否为基于时间的策略
     */
    public boolean isTimeBased() {
        return this == FIFO || this == TTL;
    }

    /**
     * 是否为基于空间的策略
     *
     * @return 是否为基于空间的策略
     */
    public boolean isSpaceBased() {
        return this == SIZE || this == WEIGHT;
    }

    /**
     * 是否需要统计信息
     *
     * @return 是否需要统计信息
     */
    public boolean needsStatistics() {
        return this == LRU || this == LFU || this == WEIGHT;
    }

    /**
     * 是否为确定性策略
     *
     * @return 是否为确定性策略
     */
    public boolean isDeterministic() {
        return this != RANDOM;
    }

}
