package top.continew.admin.accounting.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import top.continew.admin.accounting.model.entity.RuleEngine;
import top.continew.admin.accounting.model.query.RuleEngineQuery;
import top.continew.admin.accounting.model.req.RuleEngineCreateReq;
import top.continew.admin.accounting.model.req.RuleEngineUpdateReq;
import top.continew.admin.accounting.model.req.RuleExecutionReq;
import top.continew.admin.accounting.model.resp.RuleEngineDetailResp;
import top.continew.admin.accounting.model.resp.RuleEngineResp;
import top.continew.admin.accounting.model.resp.RuleExecutionResp;
import top.continew.admin.common.model.resp.LabelValueResp;

import java.util.List;
import java.util.Map;

/**
 * 规则引擎服务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface RuleEngineService {

    // ==================== 规则管理 ====================

    /**
     * 创建规则
     *
     * @param createReq 创建请求
     * @return 规则ID
     */
    Long createRule(RuleEngineCreateReq createReq);

    /**
     * 更新规则
     *
     * @param ruleId    规则ID
     * @param updateReq 更新请求
     */
    void updateRule(Long ruleId, RuleEngineUpdateReq updateReq);

    /**
     * 删除规则
     *
     * @param ruleId 规则ID
     */
    void deleteRule(Long ruleId);

    /**
     * 批量删除规则
     *
     * @param ruleIds 规则ID列表
     */
    void deleteRules(List<Long> ruleIds);

    /**
     * 获取规则详情
     *
     * @param ruleId 规则ID
     * @return 规则详情
     */
    RuleEngineDetailResp getRuleDetail(Long ruleId);

    /**
     * 分页查询规则
     *
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<RuleEngineResp> pageRules(RuleEngineQuery query);

    /**
     * 列表查询规则
     *
     * @param query 查询条件
     * @return 规则列表
     */
    List<RuleEngineResp> listRules(RuleEngineQuery query);

    /**
     * 复制规则
     *
     * @param ruleId   源规则ID
     * @param ruleName 新规则名称
     * @return 新规则ID
     */
    Long copyRule(Long ruleId, String ruleName);

    /**
     * 启用/禁用规则
     *
     * @param ruleId  规则ID
     * @param enabled 是否启用
     */
    void toggleRuleStatus(Long ruleId, Boolean enabled);

    /**
     * 发布规则
     *
     * @param ruleId 规则ID
     */
    void publishRule(Long ruleId);

    /**
     * 取消发布规则
     *
     * @param ruleId 规则ID
     */
    void unpublishRule(Long ruleId);

    // ==================== 规则版本管理 ====================

    /**
     * 创建规则版本
     *
     * @param ruleId      规则ID
     * @param versionNote 版本说明
     * @return 版本号
     */
    String createRuleVersion(Long ruleId, String versionNote);

    /**
     * 回滚到指定版本
     *
     * @param ruleId  规则ID
     * @param version 版本号
     */
    void rollbackToVersion(Long ruleId, String version);

    /**
     * 获取规则版本历史
     *
     * @param ruleId 规则ID
     * @return 版本历史列表
     */
    List<Map<String, Object>> getRuleVersionHistory(Long ruleId);

    /**
     * 比较规则版本
     *
     * @param ruleId      规则ID
     * @param version1    版本1
     * @param version2    版本2
     * @return 版本比较结果
     */
    Map<String, Object> compareRuleVersions(Long ruleId, String version1, String version2);

    // ==================== 规则执行 ====================

    /**
     * 执行规则
     *
     * @param executionReq 执行请求
     * @return 执行结果
     */
    RuleExecutionResp executeRules(RuleExecutionReq executionReq);

    /**
     * 异步执行规则
     *
     * @param executionReq 执行请求
     * @return 执行ID
     */
    Long executeRulesAsync(RuleExecutionReq executionReq);

    /**
     * 获取执行状态
     *
     * @param executionId 执行ID
     * @return 执行状态
     */
    RuleExecutionResp getExecutionStatus(Long executionId);

    /**
     * 取消执行
     *
     * @param executionId 执行ID
     */
    void cancelExecution(Long executionId);

    /**
     * 重新执行
     *
     * @param executionId 原执行ID
     * @return 新执行ID
     */
    Long reExecute(Long executionId);

    /**
     * 测试规则
     *
     * @param ruleId    规则ID
     * @param testData  测试数据
     * @return 测试结果
     */
    Map<String, Object> testRule(Long ruleId, Map<String, Object> testData);

    /**
     * 验证规则配置
     *
     * @param ruleId 规则ID
     * @return 验证结果
     */
    Map<String, Object> validateRuleConfig(Long ruleId);

    // ==================== 规则调度 ====================

    /**
     * 启动规则调度
     *
     * @param ruleId 规则ID
     */
    void startRuleSchedule(Long ruleId);

    /**
     * 停止规则调度
     *
     * @param ruleId 规则ID
     */
    void stopRuleSchedule(Long ruleId);

    /**
     * 暂停规则调度
     *
     * @param ruleId 规则ID
     */
    void pauseRuleSchedule(Long ruleId);

    /**
     * 恢复规则调度
     *
     * @param ruleId 规则ID
     */
    void resumeRuleSchedule(Long ruleId);

    /**
     * 立即执行调度任务
     *
     * @param ruleId 规则ID
     * @return 执行ID
     */
    Long triggerScheduleNow(Long ruleId);

    /**
     * 获取下次执行时间
     *
     * @param ruleId 规则ID
     * @param count  获取数量
     * @return 执行时间列表
     */
    List<String> getNextExecutionTimes(Long ruleId, Integer count);

    // ==================== 统计分析 ====================

    /**
     * 获取规则执行统计
     *
     * @param ruleId 规则ID
     * @param days   统计天数
     * @return 执行统计
     */
    Map<String, Object> getRuleExecutionStats(Long ruleId, Integer days);

    /**
     * 获取规则性能统计
     *
     * @param ruleId 规则ID
     * @param days   统计天数
     * @return 性能统计
     */
    Map<String, Object> getRulePerformanceStats(Long ruleId, Integer days);

    /**
     * 获取规则执行趋势
     *
     * @param ruleId 规则ID
     * @param days   统计天数
     * @return 执行趋势
     */
    List<Map<String, Object>> getRuleExecutionTrend(Long ruleId, Integer days);

    /**
     * 获取热门规则排行
     *
     * @param groupId 群组ID
     * @param days    统计天数
     * @param limit   返回数量
     * @return 热门规则列表
     */
    List<Map<String, Object>> getPopularRules(Long groupId, Integer days, Integer limit);

    /**
     * 获取规则类型统计
     *
     * @param groupId 群组ID
     * @return 类型统计
     */
    List<Map<String, Object>> getRuleTypeStats(Long groupId);

    /**
     * 获取规则执行历史
     *
     * @param ruleId 规则ID
     * @param limit  返回数量
     * @return 执行历史
     */
    List<Map<String, Object>> getRuleExecutionHistory(Long ruleId, Integer limit);

    /**
     * 获取群组规则统计
     *
     * @param groupId 群组ID
     * @return 群组统计
     */
    Map<String, Object> getGroupRuleStats(Long groupId);

    /**
     * 获取系统规则统计
     *
     * @return 系统统计
     */
    Map<String, Object> getSystemRuleStats();

    // ==================== 规则依赖和冲突 ====================

    /**
     * 分析规则依赖
     *
     * @param ruleId 规则ID
     * @return 依赖分析结果
     */
    Map<String, Object> analyzeRuleDependencies(Long ruleId);

    /**
     * 检测规则冲突
     *
     * @param ruleId 规则ID
     * @return 冲突检测结果
     */
    List<Map<String, Object>> detectRuleConflicts(Long ruleId);

    /**
     * 获取规则影响分析
     *
     * @param ruleId 规则ID
     * @return 影响分析结果
     */
    Map<String, Object> getRuleImpactAnalysis(Long ruleId);

    // ==================== 规则模板和预设 ====================

    /**
     * 获取规则模板列表
     *
     * @param ruleType 规则类型
     * @return 模板列表
     */
    List<Map<String, Object>> getRuleTemplates(String ruleType);

    /**
     * 从模板创建规则
     *
     * @param templateId   模板ID
     * @param ruleName     规则名称
     * @param customParams 自定义参数
     * @return 规则ID
     */
    Long createRuleFromTemplate(Long templateId, String ruleName, Map<String, Object> customParams);

    /**
     * 保存为模板
     *
     * @param ruleId       规则ID
     * @param templateName 模板名称
     * @return 模板ID
     */
    Long saveAsTemplate(Long ruleId, String templateName);

    // ==================== 辅助方法 ====================

    /**
     * 获取规则类型选项
     *
     * @return 类型选项列表
     */
    List<LabelValueResp<String>> getRuleTypeOptions();

    /**
     * 获取触发类型选项
     *
     * @return 触发类型选项列表
     */
    List<LabelValueResp<String>> getTriggerTypeOptions();

    /**
     * 获取事件类型选项
     *
     * @param triggerType 触发类型
     * @return 事件类型选项列表
     */
    List<LabelValueResp<String>> getEventTypeOptions(String triggerType);

    /**
     * 获取动作类型选项
     *
     * @param ruleType 规则类型
     * @return 动作类型选项列表
     */
    List<LabelValueResp<String>> getActionTypeOptions(String ruleType);

    /**
     * 获取操作符选项
     *
     * @param dataType 数据类型
     * @return 操作符选项列表
     */
    List<LabelValueResp<String>> getOperatorOptions(String dataType);

    /**
     * 获取字段选项
     *
     * @param entityType 实体类型
     * @return 字段选项列表
     */
    List<LabelValueResp<String>> getFieldOptions(String entityType);

    /**
     * 检查规则名称是否存在
     *
     * @param ruleName  规则名称
     * @param groupId   群组ID
     * @param excludeId 排除的规则ID
     * @return 是否存在
     */
    Boolean existsByName(String ruleName, Long groupId, Long excludeId);
}
