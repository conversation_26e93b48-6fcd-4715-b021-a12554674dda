package top.continew.admin.bot.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.admin.accounting.enums.PlatformType;
import top.continew.admin.accounting.enums.TransactionType;
import top.continew.starter.data.mybatis.plus.base.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 命令历史记录实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bot_command_history")
@Schema(description = "命令历史记录")
public class CommandHistoryDO extends BaseEntity {

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID")
    private Long groupId;

    /**
     * 平台类型
     */
    @Schema(description = "平台类型")
    private PlatformType platform;

    /**
     * 原始命令
     */
    @Schema(description = "原始命令")
    private String originalCommand;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型")
    private TransactionType transactionType;

    /**
     * 金额
     */
    @Schema(description = "金额")
    private BigDecimal amount;

    /**
     * 币种
     */
    @Schema(description = "币种")
    private String currency;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 分类
     */
    @Schema(description = "分类")
    private String category;

    /**
     * 是否执行成功
     */
    @Schema(description = "是否执行成功")
    private Boolean success;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 执行时间
     */
    @Schema(description = "执行时间")
    private LocalDateTime executionTime;

    /**
     * 执行耗时（毫秒）
     */
    @Schema(description = "执行耗时（毫秒）")
    private Long executionDuration;

    /**
     * 关联的业务ID
     */
    @Schema(description = "关联的业务ID")
    private Long businessId;

    /**
     * 置信度分数
     */
    @Schema(description = "置信度分数")
    private Double confidenceScore;
}
