/*
 * Copyright (c) 2022-present <PERSON>7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.tenant.model.resp;

import cn.idev.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;

/**
 * 租户详情响应参数
 *
 * <AUTHOR>
 * <AUTHOR>
 * @since 2024/11/26 17:20
 */
@Data
@Schema(description = "租户详情响应参数")
public class TenantDetailResp extends TenantResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 租户管理员
     */
    @Schema(description = "租户管理员", example = "666")
    @ExcelProperty(value = "租户管理员", order = 11)
    private Long adminUser;
}