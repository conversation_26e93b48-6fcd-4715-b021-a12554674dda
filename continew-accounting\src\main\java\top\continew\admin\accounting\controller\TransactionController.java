package top.continew.admin.accounting.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.accounting.model.query.TransactionQuery;
import top.continew.admin.accounting.model.req.TransactionCreateReq;
import top.continew.admin.accounting.model.req.TransactionUpdateReq;
import top.continew.admin.accounting.model.resp.TransactionDetailResp;
import top.continew.admin.accounting.model.resp.TransactionListResp;
import top.continew.admin.accounting.model.resp.TransactionStatisticsResp;
import top.continew.admin.accounting.service.TransactionService;
import top.continew.admin.common.base.controller.BaseController;
import top.continew.starter.core.util.validate.ValidationUtils;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;
import top.continew.starter.security.context.SecurityContextHolder;
import top.continew.starter.web.model.R;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 账单管理 API
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "账单管理 API")
@RestController
@RequiredArgsConstructor
@RequestMapping("/accounting/transaction")
@Validated
public class TransactionController extends BaseController<TransactionService, TransactionListResp, TransactionDetailResp, TransactionQuery, TransactionCreateReq> {

    /**
     * 启用标准 CRUD API
     */
    @CrudRequestMapping(value = "", api = {Api.PAGE, Api.LIST, Api.GET, Api.CREATE, Api.UPDATE, Api.BATCH_DELETE, Api.EXPORT, Api.DICT})
    @SaCheckPermission("accounting:transaction")
    public void crud() {
        // 标准 CRUD 操作由 @CrudRequestMapping 自动生成
    }

    /**
     * 更新账单
     */
    @Operation(summary = "更新账单", description = "更新账单信息")
    @PutMapping("/{id}")
    @SaCheckPermission("accounting:transaction:update")
    public R<Void> update(@Parameter(description = "ID", example = "1") @PathVariable Long id,
                         @Validated @RequestBody TransactionUpdateReq req) {
        baseService.update(req, id);
        return R.ok();
    }

    /**
     * 获取群组账单统计
     */
    @Operation(summary = "获取群组账单统计", description = "获取指定群组和时间范围的账单统计信息")
    @GetMapping("/statistics")
    @SaCheckPermission("accounting:transaction:statistics")
    public R<TransactionStatisticsResp> getStatistics(
            @Parameter(description = "群组ID", example = "1") @RequestParam @NotNull Long groupId,
            @Parameter(description = "开始日期", example = "2025-01-01") @RequestParam LocalDate startDate,
            @Parameter(description = "结束日期", example = "2025-01-31") @RequestParam LocalDate endDate) {
        
        ValidationUtils.throwIf(startDate.isAfter(endDate), "开始日期不能晚于结束日期");
        
        TransactionStatisticsResp statistics = baseService.getStatistics(groupId, startDate, endDate);
        return R.ok(statistics);
    }

    /**
     * 获取用户账单列表
     */
    @Operation(summary = "获取用户账单列表", description = "获取用户在指定群组中的账单列表")
    @GetMapping("/user")
    @SaCheckPermission("accounting:transaction:list")
    public R<List<TransactionListResp>> getUserTransactions(
            @Parameter(description = "群组ID", example = "1") @RequestParam @NotNull Long groupId,
            @Parameter(description = "用户ID", example = "1") @RequestParam(required = false) Long userId) {
        
        // 如果未指定用户ID，则查询当前用户的账单
        Long targetUserId = userId != null ? userId : SecurityContextHolder.getUserId();
        
        List<TransactionListResp> transactions = baseService.getUserTransactions(groupId, targetUserId);
        return R.ok(transactions);
    }

    /**
     * 计算用户余额
     */
    @Operation(summary = "计算用户余额", description = "计算用户在指定群组中的余额")
    @GetMapping("/balance")
    @SaCheckPermission("accounting:transaction:balance")
    public R<BigDecimal> calculateUserBalance(
            @Parameter(description = "群组ID", example = "1") @RequestParam @NotNull Long groupId,
            @Parameter(description = "币种", example = "CNY") @RequestParam(defaultValue = "CNY") String currency,
            @Parameter(description = "用户ID", example = "1") @RequestParam(required = false) Long userId) {
        
        // 如果未指定用户ID，则查询当前用户的余额
        Long targetUserId = userId != null ? userId : SecurityContextHolder.getUserId();
        
        BigDecimal balance = baseService.calculateUserBalance(groupId, targetUserId, currency);
        return R.ok(balance);
    }

    /**
     * 删除账单
     */
    @Operation(summary = "删除账单", description = "删除指定账单")
    @DeleteMapping("/{id}")
    @SaCheckPermission("accounting:transaction:delete")
    public R<Void> deleteTransaction(@Parameter(description = "账单ID", example = "1") @PathVariable Long id) {
        Long userId = SecurityContextHolder.getUserId();
        baseService.deleteTransaction(id, userId);
        return R.ok();
    }

    /**
     * 批量导入账单
     */
    @Operation(summary = "批量导入账单", description = "批量导入账单数据")
    @PostMapping("/import")
    @SaCheckPermission("accounting:transaction:import")
    public R<TransactionService.ImportResult> batchImport(
            @Parameter(description = "群组ID", example = "1") @RequestParam @NotNull Long groupId,
            @Valid @RequestBody List<TransactionCreateReq> transactions) {
        
        Long userId = SecurityContextHolder.getUserId();
        TransactionService.ImportResult result = baseService.batchImport(groupId, transactions, userId);
        return R.ok(result);
    }
}
