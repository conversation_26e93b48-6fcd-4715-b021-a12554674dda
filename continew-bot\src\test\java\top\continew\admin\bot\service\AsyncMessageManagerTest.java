package top.continew.admin.bot.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import top.continew.admin.accounting.enums.PlatformType;
import top.continew.admin.bot.common.BotMessageQueue;
import top.continew.admin.bot.model.dto.BotMessage;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * 异步消息管理器测试
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@ExtendWith(MockitoExtension.class)
class AsyncMessageManagerTest {

    @Mock
    private RabbitTemplate rabbitTemplate;

    @Mock
    private BotMessageQueue botMessageQueue;

    @InjectMocks
    private AsyncMessageManager asyncMessageManager;

    private BotMessage testMessage;

    @BeforeEach
    void setUp() {
        testMessage = BotMessage.createTextMessage(
            PlatformType.TELEGRAM,
            "123456",
            1L,
            "test_user",
            "测试消息"
        );
    }

    @Test
    void testSendMessageAsync() {
        // Given
        doNothing().when(botMessageQueue).sendMessage(any(BotMessage.class));

        // When
        CompletableFuture<Boolean> future = asyncMessageManager.sendMessageAsync(testMessage);

        // Then
        assertNotNull(future);
        assertTrue(future.join());
        assertNotNull(testMessage.getMessageId());
        assertNotNull(testMessage.getTimestamp());
        assertEquals(BotMessage.ProcessStatus.PENDING, testMessage.getProcessStatus());
        verify(botMessageQueue).sendMessage(testMessage);
    }

    @Test
    void testSendNotificationAsync() {
        // Given
        BotMessage notification = BotMessage.createNotificationMessage(
            PlatformType.TELEGRAM,
            "123456",
            "测试通知"
        );
        doNothing().when(botMessageQueue).sendNotification(any(BotMessage.class));

        // When
        CompletableFuture<Boolean> future = asyncMessageManager.sendNotificationAsync(notification);

        // Then
        assertNotNull(future);
        assertTrue(future.join());
        assertNotNull(notification.getMessageId());
        verify(botMessageQueue).sendNotification(notification);
    }

    @Test
    void testSendCommandAsync() {
        // Given
        BotMessage command = BotMessage.createCommandMessage(
            PlatformType.TELEGRAM,
            "123456",
            1L,
            "test_user",
            "/help"
        );
        doNothing().when(botMessageQueue).sendCommand(any(BotMessage.class));

        // When
        CompletableFuture<Boolean> future = asyncMessageManager.sendCommandAsync(command);

        // Then
        assertNotNull(future);
        assertTrue(future.join());
        assertNotNull(command.getMessageId());
        verify(botMessageQueue).sendCommand(command);
    }

    @Test
    void testSendBulkMessagesAsync() {
        // Given
        List<BotMessage> messages = List.of(
            BotMessage.createTextMessage(PlatformType.TELEGRAM, "123", 1L, "user1", "消息1"),
            BotMessage.createTextMessage(PlatformType.TELEGRAM, "456", 2L, "user2", "消息2"),
            BotMessage.createTextMessage(PlatformType.TELEGRAM, "789", 3L, "user3", "消息3")
        );
        doNothing().when(botMessageQueue).sendMessage(any(BotMessage.class));

        // When
        CompletableFuture<List<Boolean>> future = asyncMessageManager.sendBulkMessagesAsync(messages);

        // Then
        assertNotNull(future);
        List<Boolean> results = future.join();
        assertEquals(3, results.size());
        assertTrue(results.stream().allMatch(result -> result));
        verify(botMessageQueue, times(3)).sendMessage(any(BotMessage.class));
    }

    @Test
    void testSendMessageAsyncWithException() {
        // Given
        doThrow(new RuntimeException("发送失败")).when(botMessageQueue).sendMessage(any(BotMessage.class));

        // When
        CompletableFuture<Boolean> future = asyncMessageManager.sendMessageAsync(testMessage);

        // Then
        assertNotNull(future);
        assertFalse(future.join());
    }

    @Test
    void testSendDelayedMessage() {
        // Given
        doNothing().when(botMessageQueue).sendMessage(any(BotMessage.class));

        // When
        asyncMessageManager.sendDelayedMessage(testMessage, 1);

        // Then
        // 延迟消息测试比较复杂，这里只验证方法不抛异常
        assertNotNull(testMessage);
    }

    @Test
    void testGetMessageStatistics() {
        // When
        Map<String, Object> statistics = asyncMessageManager.getMessageStatistics();

        // Then
        assertNotNull(statistics);
        assertTrue(statistics.containsKey("totalMessages"));
        assertTrue(statistics.containsKey("successMessages"));
        assertTrue(statistics.containsKey("failedMessages"));
        assertTrue(statistics.containsKey("retryQueueSize"));
        assertTrue(statistics.containsKey("successRate"));
    }

    @Test
    void testMarkMessageSuccess() {
        // Given
        String messageId = "test_message_id";

        // When
        asyncMessageManager.markMessageSuccess(messageId);

        // Then
        Map<String, Object> statistics = asyncMessageManager.getMessageStatistics();
        assertEquals(1L, statistics.get("successMessages"));
    }

    @Test
    void testMarkMessageFailed() {
        // Given
        String messageId = "test_message_id";
        String errorMessage = "处理失败";

        // When
        asyncMessageManager.markMessageFailed(messageId, errorMessage);

        // Then
        Map<String, Object> statistics = asyncMessageManager.getMessageStatistics();
        assertEquals(1L, statistics.get("failedMessages"));
    }

    @Test
    void testResetStatistics() {
        // Given
        asyncMessageManager.markMessageSuccess("test1");
        asyncMessageManager.markMessageFailed("test2", "error");

        // When
        asyncMessageManager.resetStatistics();

        // Then
        Map<String, Object> statistics = asyncMessageManager.getMessageStatistics();
        assertEquals(0L, statistics.get("totalMessages"));
        assertEquals(0L, statistics.get("successMessages"));
        assertEquals(0L, statistics.get("failedMessages"));
    }

    @Test
    void testGetRetryQueueStatus() {
        // When
        Map<String, Object> status = asyncMessageManager.getRetryQueueStatus();

        // Then
        assertNotNull(status);
        assertTrue(status.containsKey("queueSize"));
        assertTrue(status.containsKey("messages"));
    }

    @Test
    void testMessageRetry() {
        // Given
        testMessage.setMaxRetries(3);
        doThrow(new RuntimeException("发送失败")).when(botMessageQueue).sendMessage(any(BotMessage.class));

        // When
        CompletableFuture<Boolean> future = asyncMessageManager.sendMessageAsync(testMessage);

        // Then
        assertFalse(future.join());
        assertTrue(testMessage.canRetry());
    }

    @Test
    void testMessageCannotRetry() {
        // Given
        testMessage.setMaxRetries(0);

        // When
        boolean canRetry = testMessage.canRetry();

        // Then
        assertFalse(canRetry);
    }

    @Test
    void testMessageIncrementRetryCount() {
        // Given
        int initialRetryCount = testMessage.getRetryCount();

        // When
        testMessage.incrementRetryCount();

        // Then
        assertEquals(initialRetryCount + 1, testMessage.getRetryCount());
    }

    @Test
    void testMessageMarkAsSuccess() {
        // Given
        testMessage.setProcessStatus(BotMessage.ProcessStatus.PROCESSING);

        // When
        testMessage.markAsSuccess();

        // Then
        assertEquals(BotMessage.ProcessStatus.SUCCESS, testMessage.getProcessStatus());
        assertNull(testMessage.getErrorMessage());
    }

    @Test
    void testConcurrentMessageSending() throws InterruptedException {
        // Given
        doNothing().when(botMessageQueue).sendMessage(any(BotMessage.class));
        int messageCount = 100;

        // When
        List<CompletableFuture<Boolean>> futures = new java.util.ArrayList<>();
        for (int i = 0; i < messageCount; i++) {
            BotMessage message = BotMessage.createTextMessage(
                PlatformType.TELEGRAM,
                "chat_" + i,
                (long) i,
                "user_" + i,
                "消息_" + i
            );
            futures.add(asyncMessageManager.sendMessageAsync(message));
        }

        // Then
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        long successCount = futures.stream().mapToLong(f -> f.join() ? 1 : 0).sum();
        assertEquals(messageCount, successCount);
    }

    @Test
    void testMessageIdGeneration() {
        // When
        asyncMessageManager.sendMessageAsync(testMessage);

        // Then
        assertNotNull(testMessage.getMessageId());
        assertTrue(testMessage.getMessageId().startsWith("msg_"));
    }

    @Test
    void testPlatformStatistics() {
        // Given
        doNothing().when(botMessageQueue).sendMessage(any(BotMessage.class));
        
        BotMessage telegramMessage = BotMessage.createTextMessage(PlatformType.TELEGRAM, "123", 1L, "user", "test");
        BotMessage discordMessage = BotMessage.createTextMessage(PlatformType.DISCORD, "456", 2L, "user", "test");

        // When
        asyncMessageManager.sendMessageAsync(telegramMessage).join();
        asyncMessageManager.sendMessageAsync(discordMessage).join();

        // Then
        Map<String, Object> statistics = asyncMessageManager.getMessageStatistics();
        @SuppressWarnings("unchecked")
        Map<String, Object> platformStats = (Map<String, Object>) statistics.get("platformStats");
        assertNotNull(platformStats);
    }
}
