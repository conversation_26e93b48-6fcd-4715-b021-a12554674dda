package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;
import top.continew.admin.accounting.model.entity.settings.GroupSettings;

/**
 * 群组更新请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "群组更新请求")
public class GroupUpdateReq {

    /**
     * 群组名称
     */
    @Schema(description = "群组名称")
    @Size(max = 100, message = "群组名称长度不能超过100个字符")
    private String name;

    /**
     * 群组描述
     */
    @Schema(description = "群组描述")
    @Size(max = 500, message = "群组描述长度不能超过500个字符")
    private String description;

    /**
     * 默认币种
     */
    @Schema(description = "默认币种")
    @Size(max = 10, message = "默认币种长度不能超过10个字符")
    private String defaultCurrency;

    /**
     * 时区
     */
    @Schema(description = "时区")
    @Size(max = 50, message = "时区长度不能超过50个字符")
    private String timezone;

    /**
     * 群组设置
     */
    @Schema(description = "群组设置")
    private GroupSettings settings;
}
