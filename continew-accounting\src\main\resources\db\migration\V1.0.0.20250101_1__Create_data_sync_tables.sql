-- 数据同步配置表
CREATE TABLE `acc_data_sync_config` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `group_id` BIGINT NOT NULL COMMENT '群组ID',
    `config_name` VARCHAR(100) NOT NULL COMMENT '配置名称',
    `config_description` VARCHAR(500) DEFAULT NULL COMMENT '配置描述',
    `source_type` VARCHAR(50) NOT NULL COMMENT '数据源类型(DATABASE/GOOGLE_SHEETS/API/WEBHOOK)',
    `target_type` VARCHAR(50) NOT NULL COMMENT '目标类型(DATABASE/GOOGLE_SHEETS/API/WEBHOOK)',
    `sync_direction` VARCHAR(20) NOT NULL DEFAULT 'UNIDIRECTIONAL' COMMENT '同步方向(UNIDIRECTIONAL/BIDIRECTIONAL)',
    `sync_mode` VARCHAR(20) NOT NULL DEFAULT 'FULL' COMMENT '同步模式(FULL/INCREMENTAL)',
    `data_type` VARCHAR(50) NOT NULL COMMENT '数据类型(TRANSACTION/GROUP/USER/CATEGORY/TAG)',
    `source_config_json` LONGTEXT NOT NULL COMMENT '源配置JSON',
    `target_config_json` LONGTEXT NOT NULL COMMENT '目标配置JSON',
    `field_mapping_json` LONGTEXT DEFAULT NULL COMMENT '字段映射JSON',
    `filter_condition_json` LONGTEXT DEFAULT NULL COMMENT '过滤条件JSON',
    `transform_rules_json` LONGTEXT DEFAULT NULL COMMENT '转换规则JSON',
    `conflict_resolution` VARCHAR(20) NOT NULL DEFAULT 'SOURCE_WINS' COMMENT '冲突解决策略(SOURCE_WINS/TARGET_WINS/TIMESTAMP_WINS/MERGE_FIELDS/MANUAL_REVIEW/CUSTOM_RULES)',
    `sync_frequency` VARCHAR(20) DEFAULT NULL COMMENT '同步频率(MANUAL/REAL_TIME/HOURLY/DAILY/WEEKLY/MONTHLY)',
    `sync_settings_json` LONGTEXT DEFAULT NULL COMMENT '同步设置JSON',
    `status` VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态(ACTIVE/INACTIVE/ERROR)',
    `last_sync_time` DATETIME DEFAULT NULL COMMENT '最后同步时间',
    `next_sync_time` DATETIME DEFAULT NULL COMMENT '下次同步时间',
    `total_sync_count` INT NOT NULL DEFAULT 0 COMMENT '总同步次数',
    `successful_sync_count` INT NOT NULL DEFAULT 0 COMMENT '成功同步次数',
    `failed_sync_count` INT NOT NULL DEFAULT 0 COMMENT '失败同步次数',
    `avg_sync_duration` BIGINT DEFAULT NULL COMMENT '平均同步时长(毫秒)',
    `last_error_message` TEXT DEFAULT NULL COMMENT '最后错误信息',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT NOT NULL COMMENT '创建人',
    `update_by` BIGINT NOT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_group_config_name` (`group_id`, `config_name`),
    KEY `idx_group_id` (`group_id`),
    KEY `idx_source_type` (`source_type`),
    KEY `idx_target_type` (`target_type`),
    KEY `idx_sync_mode` (`sync_mode`),
    KEY `idx_data_type` (`data_type`),
    KEY `idx_status` (`status`),
    KEY `idx_sync_frequency` (`sync_frequency`),
    KEY `idx_last_sync_time` (`last_sync_time`),
    KEY `idx_next_sync_time` (`next_sync_time`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_update_time` (`update_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据同步配置表';

-- 数据同步日志表
CREATE TABLE `acc_data_sync_log` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `config_id` BIGINT NOT NULL COMMENT '配置ID',
    `sync_id` VARCHAR(100) NOT NULL COMMENT '同步ID(UUID)',
    `sync_type` VARCHAR(20) NOT NULL COMMENT '同步类型(FULL/INCREMENTAL/BIDIRECTIONAL)',
    `status` VARCHAR(20) NOT NULL COMMENT '状态(RUNNING/COMPLETED/FAILED/STOPPED)',
    `start_time` DATETIME NOT NULL COMMENT '开始时间',
    `end_time` DATETIME DEFAULT NULL COMMENT '结束时间',
    `duration` BIGINT DEFAULT NULL COMMENT '执行时长(毫秒)',
    `total_count` INT NOT NULL DEFAULT 0 COMMENT '总记录数',
    `success_count` INT NOT NULL DEFAULT 0 COMMENT '成功记录数',
    `failed_count` INT NOT NULL DEFAULT 0 COMMENT '失败记录数',
    `conflict_count` INT NOT NULL DEFAULT 0 COMMENT '冲突记录数',
    `data_size` BIGINT DEFAULT NULL COMMENT '数据大小(字节)',
    `throughput` DECIMAL(10,2) DEFAULT NULL COMMENT '吞吐量(记录/秒)',
    `error_message` TEXT DEFAULT NULL COMMENT '错误信息',
    `error_details` LONGTEXT DEFAULT NULL COMMENT '错误详情JSON',
    `sync_details` LONGTEXT DEFAULT NULL COMMENT '同步详情JSON',
    `performance_metrics` LONGTEXT DEFAULT NULL COMMENT '性能指标JSON',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` BIGINT NOT NULL COMMENT '创建人',
    PRIMARY KEY (`id`),
    KEY `idx_config_id` (`config_id`),
    KEY `idx_sync_id` (`sync_id`),
    KEY `idx_sync_type` (`sync_type`),
    KEY `idx_status` (`status`),
    KEY `idx_start_time` (`start_time`),
    KEY `idx_end_time` (`end_time`),
    KEY `idx_duration` (`duration`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_config_start_time` (`config_id`, `start_time`),
    KEY `idx_config_status` (`config_id`, `status`),
    CONSTRAINT `fk_data_sync_log_config` FOREIGN KEY (`config_id`) REFERENCES `acc_data_sync_config` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据同步日志表';

-- 数据同步冲突表
CREATE TABLE `acc_data_sync_conflict` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `config_id` BIGINT NOT NULL COMMENT '配置ID',
    `sync_id` VARCHAR(100) NOT NULL COMMENT '同步ID',
    `conflict_id` VARCHAR(100) NOT NULL COMMENT '冲突ID(UUID)',
    `record_id` VARCHAR(100) NOT NULL COMMENT '记录ID',
    `conflict_type` VARCHAR(20) NOT NULL COMMENT '冲突类型(UPDATE_CONFLICT/DELETE_CONFLICT/DUPLICATE_KEY)',
    `source_data` LONGTEXT NOT NULL COMMENT '源数据JSON',
    `target_data` LONGTEXT NOT NULL COMMENT '目标数据JSON',
    `conflict_fields` JSON DEFAULT NULL COMMENT '冲突字段列表',
    `resolution_strategy` VARCHAR(20) DEFAULT NULL COMMENT '解决策略',
    `resolved_data` LONGTEXT DEFAULT NULL COMMENT '解决后数据JSON',
    `status` VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '状态(PENDING/RESOLVED/IGNORED)',
    `resolution_time` DATETIME DEFAULT NULL COMMENT '解决时间',
    `resolution_by` BIGINT DEFAULT NULL COMMENT '解决人',
    `resolution_notes` TEXT DEFAULT NULL COMMENT '解决备注',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` BIGINT NOT NULL COMMENT '创建人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_conflict_id` (`conflict_id`),
    KEY `idx_config_id` (`config_id`),
    KEY `idx_sync_id` (`sync_id`),
    KEY `idx_record_id` (`record_id`),
    KEY `idx_conflict_type` (`conflict_type`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_resolution_time` (`resolution_time`),
    CONSTRAINT `fk_data_sync_conflict_config` FOREIGN KEY (`config_id`) REFERENCES `acc_data_sync_config` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据同步冲突表';

-- 数据同步进度表
CREATE TABLE `acc_data_sync_progress` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `config_id` BIGINT NOT NULL COMMENT '配置ID',
    `sync_id` VARCHAR(100) NOT NULL COMMENT '同步ID',
    `stage` VARCHAR(50) NOT NULL COMMENT '阶段(PREPARING/READING/TRANSFORMING/WRITING/COMPLETING)',
    `stage_name` VARCHAR(100) NOT NULL COMMENT '阶段名称',
    `total_steps` INT NOT NULL DEFAULT 0 COMMENT '总步骤数',
    `completed_steps` INT NOT NULL DEFAULT 0 COMMENT '已完成步骤数',
    `current_step` VARCHAR(200) DEFAULT NULL COMMENT '当前步骤',
    `progress_percentage` DECIMAL(5,2) NOT NULL DEFAULT 0.00 COMMENT '进度百分比',
    `estimated_remaining_time` BIGINT DEFAULT NULL COMMENT '预计剩余时间(毫秒)',
    `throughput` DECIMAL(10,2) DEFAULT NULL COMMENT '当前吞吐量(记录/秒)',
    `error_count` INT NOT NULL DEFAULT 0 COMMENT '错误数量',
    `warning_count` INT NOT NULL DEFAULT 0 COMMENT '警告数量',
    `last_update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_sync_stage` (`sync_id`, `stage`),
    KEY `idx_config_id` (`config_id`),
    KEY `idx_sync_id` (`sync_id`),
    KEY `idx_stage` (`stage`),
    KEY `idx_progress_percentage` (`progress_percentage`),
    KEY `idx_last_update_time` (`last_update_time`),
    CONSTRAINT `fk_data_sync_progress_config` FOREIGN KEY (`config_id`) REFERENCES `acc_data_sync_config` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据同步进度表';

-- 数据同步计划表
CREATE TABLE `acc_data_sync_schedule` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `config_id` BIGINT NOT NULL COMMENT '配置ID',
    `schedule_name` VARCHAR(100) NOT NULL COMMENT '计划名称',
    `schedule_type` VARCHAR(20) NOT NULL COMMENT '计划类型(CRON/INTERVAL/ONE_TIME)',
    `cron_expression` VARCHAR(100) DEFAULT NULL COMMENT 'Cron表达式',
    `interval_seconds` INT DEFAULT NULL COMMENT '间隔秒数',
    `scheduled_time` DATETIME DEFAULT NULL COMMENT '计划执行时间(一次性)',
    `timezone` VARCHAR(50) NOT NULL DEFAULT 'Asia/Shanghai' COMMENT '时区',
    `enabled` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    `max_retry_count` INT NOT NULL DEFAULT 3 COMMENT '最大重试次数',
    `retry_interval_seconds` INT NOT NULL DEFAULT 300 COMMENT '重试间隔秒数',
    `timeout_seconds` INT DEFAULT NULL COMMENT '超时时间(秒)',
    `last_execution_time` DATETIME DEFAULT NULL COMMENT '最后执行时间',
    `next_execution_time` DATETIME DEFAULT NULL COMMENT '下次执行时间',
    `execution_count` INT NOT NULL DEFAULT 0 COMMENT '执行次数',
    `success_count` INT NOT NULL DEFAULT 0 COMMENT '成功次数',
    `failure_count` INT NOT NULL DEFAULT 0 COMMENT '失败次数',
    `last_execution_status` VARCHAR(20) DEFAULT NULL COMMENT '最后执行状态',
    `last_error_message` TEXT DEFAULT NULL COMMENT '最后错误信息',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT NOT NULL COMMENT '创建人',
    `update_by` BIGINT NOT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY `idx_config_id` (`config_id`),
    KEY `idx_schedule_type` (`schedule_type`),
    KEY `idx_enabled` (`enabled`),
    KEY `idx_next_execution_time` (`next_execution_time`),
    KEY `idx_last_execution_time` (`last_execution_time`),
    KEY `idx_create_time` (`create_time`),
    CONSTRAINT `fk_data_sync_schedule_config` FOREIGN KEY (`config_id`) REFERENCES `acc_data_sync_config` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据同步计划表';

-- 数据同步字段映射表
CREATE TABLE `acc_data_sync_field_mapping` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `config_id` BIGINT NOT NULL COMMENT '配置ID',
    `source_field` VARCHAR(100) NOT NULL COMMENT '源字段名',
    `target_field` VARCHAR(100) NOT NULL COMMENT '目标字段名',
    `field_type` VARCHAR(50) NOT NULL COMMENT '字段类型',
    `is_key_field` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否为关键字段',
    `is_required` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否必填',
    `default_value` TEXT DEFAULT NULL COMMENT '默认值',
    `transform_rule` TEXT DEFAULT NULL COMMENT '转换规则',
    `validation_rule` TEXT DEFAULT NULL COMMENT '验证规则',
    `description` VARCHAR(500) DEFAULT NULL COMMENT '字段描述',
    `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序顺序',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_config_source_field` (`config_id`, `source_field`),
    KEY `idx_config_id` (`config_id`),
    KEY `idx_source_field` (`source_field`),
    KEY `idx_target_field` (`target_field`),
    KEY `idx_field_type` (`field_type`),
    KEY `idx_is_key_field` (`is_key_field`),
    KEY `idx_sort_order` (`sort_order`),
    CONSTRAINT `fk_data_sync_field_mapping_config` FOREIGN KEY (`config_id`) REFERENCES `acc_data_sync_config` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据同步字段映射表';

-- 创建视图：同步配置概览
CREATE VIEW `v_data_sync_config_overview` AS
SELECT 
    c.id,
    c.group_id,
    c.config_name,
    c.config_description,
    c.source_type,
    c.target_type,
    c.sync_direction,
    c.sync_mode,
    c.data_type,
    c.status,
    c.sync_frequency,
    c.last_sync_time,
    c.next_sync_time,
    c.total_sync_count,
    c.successful_sync_count,
    c.failed_sync_count,
    CASE 
        WHEN c.total_sync_count > 0 THEN ROUND((c.successful_sync_count * 100.0 / c.total_sync_count), 2)
        ELSE 0 
    END as success_rate,
    c.avg_sync_duration,
    c.last_error_message,
    c.create_time,
    c.update_time,
    -- 最近同步状态
    (SELECT status FROM acc_data_sync_log l WHERE l.config_id = c.id ORDER BY l.start_time DESC LIMIT 1) as last_sync_status,
    -- 是否有待解决冲突
    (SELECT COUNT(*) FROM acc_data_sync_conflict cf WHERE cf.config_id = c.id AND cf.status = 'PENDING') as pending_conflicts,
    -- 健康状态
    CASE 
        WHEN c.status = 'ERROR' THEN 'CRITICAL'
        WHEN c.failed_sync_count > 0 AND c.total_sync_count > 0 
             AND (c.failed_sync_count * 100.0 / c.total_sync_count) > 20 THEN 'WARNING'
        WHEN c.last_sync_time IS NULL OR c.last_sync_time < DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 'WARNING'
        ELSE 'HEALTHY'
    END as health_status
FROM acc_data_sync_config c;

-- 创建视图：同步统计汇总
CREATE VIEW `v_data_sync_statistics` AS
SELECT 
    DATE(l.start_time) as sync_date,
    l.config_id,
    c.config_name,
    c.source_type,
    c.target_type,
    c.sync_mode,
    COUNT(*) as total_syncs,
    COUNT(CASE WHEN l.status = 'COMPLETED' THEN 1 END) as completed_syncs,
    COUNT(CASE WHEN l.status = 'FAILED' THEN 1 END) as failed_syncs,
    SUM(l.total_count) as total_records,
    SUM(l.success_count) as success_records,
    SUM(l.failed_count) as failed_records,
    SUM(l.conflict_count) as conflict_records,
    AVG(l.duration) as avg_duration,
    MAX(l.duration) as max_duration,
    MIN(l.duration) as min_duration,
    AVG(l.throughput) as avg_throughput,
    SUM(l.data_size) as total_data_size
FROM acc_data_sync_log l
JOIN acc_data_sync_config c ON l.config_id = c.id
WHERE l.start_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(l.start_time), l.config_id, c.config_name, c.source_type, c.target_type, c.sync_mode;

