package top.continew.admin.accounting.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.extension.crud.model.query.PageQuery;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * Zapier配置查询条件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "Zapier配置查询条件")
public class ZapierConfigQuery extends PageQuery {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 配置名称
     */
    @Schema(description = "配置名称", example = "交易同步")
    private String name;

    /**
     * 触发器类型
     */
    @Schema(description = "触发器类型", example = "TRANSACTION_CREATED")
    private String triggerType;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "ACTIVE")
    private String status;

    /**
     * 标签
     */
    @Schema(description = "标签", example = "自动化")
    private String tag;

    /**
     * 创建时间开始
     */
    @Schema(description = "创建时间开始", example = "2025-01-01T00:00:00")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    @Schema(description = "创建时间结束", example = "2025-01-31T23:59:59")
    private LocalDateTime createTimeEnd;

    /**
     * 最后触发时间开始
     */
    @Schema(description = "最后触发时间开始", example = "2025-01-01T00:00:00")
    private LocalDateTime lastTriggeredStart;

    /**
     * 最后触发时间结束
     */
    @Schema(description = "最后触发时间结束", example = "2025-01-31T23:59:59")
    private LocalDateTime lastTriggeredEnd;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID", example = "1")
    private Long createUserId;
}
