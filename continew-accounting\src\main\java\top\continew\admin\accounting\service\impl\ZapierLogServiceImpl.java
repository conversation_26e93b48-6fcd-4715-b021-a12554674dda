package top.continew.admin.accounting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.accounting.mapper.ZapierLogMapper;
import top.continew.admin.accounting.model.entity.ZapierLogDO;
import top.continew.admin.accounting.model.query.ZapierLogQuery;
import top.continew.admin.accounting.model.resp.ZapierLogResp;
import top.continew.admin.accounting.service.ZapierLogService;
import top.continew.starter.extension.crud.service.impl.BaseServiceImpl;
import top.continew.starter.core.util.validate.CheckUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Zapier日志服务实现
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ZapierLogServiceImpl extends BaseServiceImpl<ZapierLogMapper, ZapierLogDO, ZapierLogResp, ZapierLogResp, ZapierLogQuery, ZapierLogDO, ZapierLogDO> implements ZapierLogService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long recordLog(Long configId, Long groupId, String triggerType, String eventType,
                         Long businessId, String businessType, Map<String, Object> requestData,
                         Map<String, Object> responseData, Integer httpStatus, String status,
                         Long executionTime, String errorMessage, String errorCode) {
        
        ZapierLogDO log = new ZapierLogDO();
        log.setConfigId(configId);
        log.setGroupId(groupId);
        log.setTriggerType(triggerType);
        log.setEventType(eventType);
        log.setBusinessId(businessId);
        log.setBusinessType(businessType);
        log.setRequestData(requestData != null ? JSONUtil.toJsonStr(requestData) : null);
        log.setResponseData(responseData != null ? JSONUtil.toJsonStr(responseData) : null);
        log.setHttpStatus(httpStatus);
        log.setStatus(status);
        log.setExecutedAt(LocalDateTime.now());
        log.setExecutionTime(executionTime);
        log.setErrorMessage(errorMessage);
        log.setErrorCode(errorCode);
        log.setRetryCount(0);
        log.setIsRetry(false);
        
        // 计算请求和响应大小
        if (requestData != null) {
            log.setRequestSize((long) JSONUtil.toJsonStr(requestData).getBytes().length);
        }
        if (responseData != null) {
            log.setResponseSize((long) JSONUtil.toJsonStr(responseData).getBytes().length);
        }
        
        super.save(log);
        
        log.debug("记录Zapier执行日志: configId={}, status={}, executionTime={}ms", 
                configId, status, executionTime);
        
        return log.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long recordRetryLog(Long originalLogId, Long configId, Long groupId, String triggerType,
                              String eventType, Long businessId, String businessType,
                              Map<String, Object> requestData, Map<String, Object> responseData,
                              Integer httpStatus, String status, Long executionTime,
                              String errorMessage, String errorCode, Integer retryCount) {
        
        ZapierLogDO log = new ZapierLogDO();
        log.setConfigId(configId);
        log.setGroupId(groupId);
        log.setTriggerType(triggerType);
        log.setEventType(eventType);
        log.setBusinessId(businessId);
        log.setBusinessType(businessType);
        log.setRequestData(requestData != null ? JSONUtil.toJsonStr(requestData) : null);
        log.setResponseData(responseData != null ? JSONUtil.toJsonStr(responseData) : null);
        log.setHttpStatus(httpStatus);
        log.setStatus(status);
        log.setExecutedAt(LocalDateTime.now());
        log.setExecutionTime(executionTime);
        log.setErrorMessage(errorMessage);
        log.setErrorCode(errorCode);
        log.setRetryCount(retryCount);
        log.setIsRetry(true);
        log.setOriginalLogId(originalLogId);
        
        // 计算请求和响应大小
        if (requestData != null) {
            log.setRequestSize((long) JSONUtil.toJsonStr(requestData).getBytes().length);
        }
        if (responseData != null) {
            log.setResponseSize((long) JSONUtil.toJsonStr(responseData).getBytes().length);
        }
        
        super.save(log);
        
        log.info("记录Zapier重试日志: originalLogId={}, configId={}, retryCount={}, status={}", 
                originalLogId, configId, retryCount, status);
        
        return log.getId();
    }

    @Override
    public List<ZapierLogResp> listByConfigId(Long configId, Integer limit) {
        CheckUtils.throwIfNull(configId, "配置ID不能为空");
        
        List<ZapierLogDO> logs = baseMapper.selectByConfigId(configId, limit);
        return logs.stream()
                .map(this::convertToResp)
                .collect(Collectors.toList());
    }

    @Override
    public List<ZapierLogResp> listByGroupId(Long groupId, Integer limit) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        
        List<ZapierLogDO> logs = baseMapper.selectByGroupId(groupId, limit);
        return logs.stream()
                .map(this::convertToResp)
                .collect(Collectors.toList());
    }

    @Override
    public List<ZapierLogResp> listFailedLogs(Long configId, Integer hours) {
        List<ZapierLogDO> logs = baseMapper.selectFailedLogs(configId, hours);
        return logs.stream()
                .map(this::convertToResp)
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> getExecutionStats(Long configId, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.selectExecutionStats(configId, startTime, endTime);
    }

    @Override
    public Map<String, Object> getGroupExecutionStats(Long groupId, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.selectGroupExecutionStats(groupId, startTime, endTime);
    }

    @Override
    public List<Map<String, Object>> getExecutionTrend(Long configId, LocalDateTime startTime, LocalDateTime endTime, String interval) {
        return baseMapper.selectExecutionTrend(configId, startTime, endTime, interval);
    }

    @Override
    public List<Map<String, Object>> getErrorDistribution(Long configId, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.selectErrorDistribution(configId, startTime, endTime);
    }

    @Override
    public Map<String, Object> getPerformanceStats(Long configId, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.selectPerformanceStats(configId, startTime, endTime);
    }

    @Override
    public List<ZapierLogResp> listSlowExecutionLogs(Long configId, Long thresholdMs, Integer hours) {
        List<ZapierLogDO> logs = baseMapper.selectSlowExecutionLogs(configId, thresholdMs, hours);
        return logs.stream()
                .map(this::convertToResp)
                .collect(Collectors.toList());
    }

    @Override
    public List<ZapierLogResp> listRetryLogs(Long originalLogId) {
        List<ZapierLogDO> logs = baseMapper.selectRetryLogs(originalLogId);
        return logs.stream()
                .map(this::convertToResp)
                .collect(Collectors.toList());
    }

    @Override
    public List<ZapierLogResp> listByBusiness(String businessType, Long businessId) {
        List<ZapierLogDO> logs = baseMapper.selectByBusiness(businessType, businessId);
        return logs.stream()
                .map(this::convertToResp)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteExpiredLogs(Integer days) {
        CheckUtils.throwIfNull(days, "保留天数不能为空");
        CheckUtils.throwIf(days < 1, "保留天数必须大于0");
        
        int result = baseMapper.deleteExpiredLogs(days);
        
        log.info("删除过期Zapier日志: days={}, count={}", days, result);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteByConfigId(Long configId) {
        CheckUtils.throwIfNull(configId, "配置ID不能为空");
        
        int result = baseMapper.deleteByConfigId(configId);
        
        log.info("删除配置的所有Zapier日志: configId={}, count={}", configId, result);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteByGroupId(Long groupId) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        
        int result = baseMapper.deleteByGroupId(groupId);
        
        log.info("删除群组的所有Zapier日志: groupId={}, count={}", groupId, result);
        return result;
    }

    @Override
    public List<ZapierLogResp> listRecentLogs(Long configId, Integer limit) {
        List<ZapierLogDO> logs = baseMapper.selectRecentLogs(configId, limit);
        return logs.stream()
                .map(this::convertToResp)
                .collect(Collectors.toList());
    }

    @Override
    public List<ZapierLogResp> listAnomalyLogs(Long groupId, Integer hours) {
        List<ZapierLogDO> logs = baseMapper.selectAnomalyLogs(groupId, hours);
        return logs.stream()
                .map(this::convertToResp)
                .collect(Collectors.toList());
    }

    @Override
    public List<Map<String, Object>> getHttpStatusDistribution(Long configId, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.selectHttpStatusDistribution(configId, startTime, endTime);
    }

    @Override
    public Map<String, Object> getDataTransferStats(Long configId, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.selectDataTransferStats(configId, startTime, endTime);
    }

    @Override
    public List<Map<String, Object>> getExecutionTimeDistribution(Long configId, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.selectExecutionTimeDistribution(configId, startTime, endTime);
    }

    @Override
    public Map<String, Object> exportLogs(ZapierLogQuery query) {
        // TODO: 实现日志导出逻辑
        Map<String, Object> result = new HashMap<>();
        result.put("message", "日志导出功能待实现");
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer cleanupLogs(Long configId, Integer days) {
        CheckUtils.throwIfNull(configId, "配置ID不能为空");
        CheckUtils.throwIfNull(days, "保留天数不能为空");
        CheckUtils.throwIf(days < 1, "保留天数必须大于0");

        // 删除指定配置的过期日志
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(days);

        // 先查询要删除的日志数量
        Long count = baseMapper.selectList(
                new QueryWrapper<ZapierLogDO>()
                        .eq("config_id", configId)
                        .lt("executed_at", cutoffTime)
        ).stream().count();

        if (count > 0) {
            // 执行删除
            int deleted = baseMapper.delete(
                    new QueryWrapper<ZapierLogDO>()
                            .eq("config_id", configId)
                            .lt("executed_at", cutoffTime)
            );

            log.info("清理配置日志完成: configId={}, days={}, deletedCount={}", configId, days, deleted);
            return deleted;
        }

        return 0;
    }

    @Override
    public Map<String, Object> getLogOverview(Long groupId) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusDays(30);
        
        return getGroupExecutionStats(groupId, startTime, endTime);
    }

    @Override
    public Map<String, Object> getLogDetail(Long id) {
        CheckUtils.throwIfNull(id, "日志ID不能为空");
        
        return baseMapper.selectLogDetail(id);
    }

    /**
     * 转换为响应对象
     */
    private ZapierLogResp convertToResp(ZapierLogDO log) {
        ZapierLogResp resp = BeanUtil.copyProperties(log, ZapierLogResp.class);
        
        // 设置触发器类型名称
        resp.setTriggerTypeName(getTriggerTypeName(log.getTriggerType()));
        
        // 设置事件类型名称
        resp.setEventTypeName(getEventTypeName(log.getEventType()));
        
        // 设置业务类型名称
        resp.setBusinessTypeName(getBusinessTypeName(log.getBusinessType()));
        
        // 设置执行状态名称
        resp.setStatusName(getStatusName(log.getStatus()));
        
        // 设置HTTP状态描述
        if (log.getHttpStatus() != null) {
            resp.setHttpStatusText(getHttpStatusText(log.getHttpStatus()));
        }
        
        // 设置执行耗时描述
        if (log.getExecutionTime() != null) {
            resp.setExecutionTimeText(formatExecutionTime(log.getExecutionTime()));
        }
        
        // 设置请求大小描述
        if (log.getRequestSize() != null) {
            resp.setRequestSizeText(formatDataSize(log.getRequestSize()));
        }
        
        // 设置响应大小描述
        if (log.getResponseSize() != null) {
            resp.setResponseSizeText(formatDataSize(log.getResponseSize()));
        }
        
        // 解析JSON字段
        if (StrUtil.isNotBlank(log.getRequestData())) {
            try {
                resp.setRequestData(JSONUtil.toBean(log.getRequestData(), Map.class));
            } catch (Exception e) {
                log.warn("解析请求数据失败: logId={}, error={}", log.getId(), e.getMessage());
            }
        }
        
        if (StrUtil.isNotBlank(log.getResponseData())) {
            try {
                resp.setResponseData(JSONUtil.toBean(log.getResponseData(), Map.class));
            } catch (Exception e) {
                log.warn("解析响应数据失败: logId={}, error={}", log.getId(), e.getMessage());
            }
        }
        
        if (StrUtil.isNotBlank(log.getRequestHeaders())) {
            try {
                resp.setRequestHeaders(JSONUtil.toBean(log.getRequestHeaders(), Map.class));
            } catch (Exception e) {
                log.warn("解析请求头失败: logId={}, error={}", log.getId(), e.getMessage());
            }
        }
        
        if (StrUtil.isNotBlank(log.getResponseHeaders())) {
            try {
                resp.setResponseHeaders(JSONUtil.toBean(log.getResponseHeaders(), Map.class));
            } catch (Exception e) {
                log.warn("解析响应头失败: logId={}, error={}", log.getId(), e.getMessage());
            }
        }
        
        return resp;
    }

    /**
     * 获取触发器类型名称
     */
    private String getTriggerTypeName(String triggerType) {
        Map<String, String> typeNames = Map.of(
                "TRANSACTION_CREATED", "交易创建",
                "TRANSACTION_UPDATED", "交易更新",
                "TRANSACTION_DELETED", "交易删除",
                "WALLET_UPDATED", "钱包更新",
                "REPORT_GENERATED", "报表生成"
        );
        return typeNames.getOrDefault(triggerType, triggerType);
    }

    /**
     * 获取事件类型名称
     */
    private String getEventTypeName(String eventType) {
        Map<String, String> typeNames = Map.of(
                "CREATE", "创建",
                "UPDATE", "更新",
                "DELETE", "删除",
                "SYNC", "同步"
        );
        return typeNames.getOrDefault(eventType, eventType);
    }

    /**
     * 获取业务类型名称
     */
    private String getBusinessTypeName(String businessType) {
        Map<String, String> typeNames = Map.of(
                "TRANSACTION", "交易",
                "WALLET", "钱包",
                "REPORT", "报表",
                "USER", "用户",
                "GROUP", "群组"
        );
        return typeNames.getOrDefault(businessType, businessType);
    }

    /**
     * 获取状态名称
     */
    private String getStatusName(String status) {
        Map<String, String> statusNames = Map.of(
                "SUCCESS", "成功",
                "FAILED", "失败",
                "TIMEOUT", "超时",
                "RETRY", "重试中"
        );
        return statusNames.getOrDefault(status, status);
    }

    /**
     * 获取HTTP状态描述
     */
    private String getHttpStatusText(Integer httpStatus) {
        Map<Integer, String> statusTexts = Map.of(
                200, "OK",
                201, "Created",
                400, "Bad Request",
                401, "Unauthorized",
                403, "Forbidden",
                404, "Not Found",
                500, "Internal Server Error",
                502, "Bad Gateway",
                503, "Service Unavailable",
                504, "Gateway Timeout"
        );
        return statusTexts.getOrDefault(httpStatus, "Unknown");
    }

    /**
     * 格式化执行耗时
     */
    private String formatExecutionTime(Long executionTime) {
        if (executionTime < 1000) {
            return executionTime + "ms";
        } else if (executionTime < 60000) {
            return String.format("%.1fs", executionTime / 1000.0);
        } else {
            return String.format("%.1fm", executionTime / 60000.0);
        }
    }

    /**
     * 格式化数据大小
     */
    private String formatDataSize(Long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        }
    }
}
