package top.continew.admin.bot.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.bot.model.dto.BotMessage;
import top.continew.admin.bot.service.AsyncMessageManager;
import top.continew.admin.bot.service.MessageMonitorService;
import top.continew.starter.web.model.R;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 异步消息处理控制器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "异步消息处理 API")
@RestController
@RequestMapping("/api/v1/bot/async")
@RequiredArgsConstructor
@Validated
public class AsyncMessageController {

    private final AsyncMessageManager asyncMessageManager;
    private final MessageMonitorService messageMonitorService;

    @Operation(summary = "异步发送消息", description = "异步发送机器人消息")
    @PostMapping("/message")
    public R<String> sendMessageAsync(@RequestBody @Valid BotMessage message) {
        CompletableFuture<Boolean> future = asyncMessageManager.sendMessageAsync(message);
        return R.ok("消息已提交异步处理，消息ID: " + message.getMessageId());
    }

    @Operation(summary = "异步发送通知", description = "异步发送机器人通知")
    @PostMapping("/notification")
    public R<String> sendNotificationAsync(@RequestBody @Valid BotMessage notification) {
        CompletableFuture<Boolean> future = asyncMessageManager.sendNotificationAsync(notification);
        return R.ok("通知已提交异步处理，消息ID: " + notification.getMessageId());
    }

    @Operation(summary = "异步发送命令", description = "异步发送机器人命令")
    @PostMapping("/command")
    public R<String> sendCommandAsync(@RequestBody @Valid BotMessage command) {
        CompletableFuture<Boolean> future = asyncMessageManager.sendCommandAsync(command);
        return R.ok("命令已提交异步处理，消息ID: " + command.getMessageId());
    }

    @Operation(summary = "批量异步发送消息", description = "批量异步发送机器人消息")
    @PostMapping("/messages/batch")
    public R<String> sendBulkMessagesAsync(@RequestBody @Valid List<BotMessage> messages) {
        CompletableFuture<List<Boolean>> future = asyncMessageManager.sendBulkMessagesAsync(messages);
        return R.ok("批量消息已提交异步处理，消息数量: " + messages.size());
    }

    @Operation(summary = "发送延迟消息", description = "发送延迟执行的消息")
    @PostMapping("/message/delayed")
    public R<String> sendDelayedMessage(
        @RequestBody @Valid BotMessage message,
        @Parameter(description = "延迟秒数") @RequestParam long delaySeconds
    ) {
        asyncMessageManager.sendDelayedMessage(message, delaySeconds);
        return R.ok("延迟消息已设置，将在 " + delaySeconds + " 秒后发送");
    }

    @Operation(summary = "获取消息统计", description = "获取异步消息处理统计信息")
    @GetMapping("/statistics")
    public R<Map<String, Object>> getMessageStatistics() {
        Map<String, Object> statistics = asyncMessageManager.getMessageStatistics();
        return R.ok(statistics);
    }

    @Operation(summary = "获取重试队列状态", description = "获取消息重试队列状态")
    @GetMapping("/retry-queue")
    public R<Map<String, Object>> getRetryQueueStatus() {
        Map<String, Object> status = asyncMessageManager.getRetryQueueStatus();
        return R.ok(status);
    }

    @Operation(summary = "重置统计信息", description = "重置消息处理统计信息")
    @PostMapping("/statistics/reset")
    public R<String> resetStatistics() {
        asyncMessageManager.resetStatistics();
        return R.ok("统计信息已重置");
    }

    @Operation(summary = "标记消息成功", description = "手动标记消息处理成功")
    @PostMapping("/message/{messageId}/success")
    public R<String> markMessageSuccess(@PathVariable String messageId) {
        asyncMessageManager.markMessageSuccess(messageId);
        return R.ok("消息已标记为成功");
    }

    @Operation(summary = "标记消息失败", description = "手动标记消息处理失败")
    @PostMapping("/message/{messageId}/failed")
    public R<String> markMessageFailed(
        @PathVariable String messageId,
        @RequestParam String errorMessage
    ) {
        asyncMessageManager.markMessageFailed(messageId, errorMessage);
        return R.ok("消息已标记为失败");
    }

    @Operation(summary = "获取实时监控数据", description = "获取消息队列实时监控数据")
    @GetMapping("/monitor/metrics")
    public R<Map<String, Object>> getRealTimeMetrics() {
        Map<String, Object> metrics = messageMonitorService.getRealTimeMetrics();
        return R.ok(metrics);
    }

    @Operation(summary = "获取队列详细信息", description = "获取指定队列的详细信息")
    @GetMapping("/monitor/queue/{queueName}")
    public R<Map<String, Object>> getQueueDetails(@PathVariable String queueName) {
        Map<String, Object> details = messageMonitorService.getQueueDetails(queueName);
        return R.ok(details);
    }

    @Operation(summary = "执行健康检查", description = "手动执行消息队列健康检查")
    @PostMapping("/monitor/health-check")
    public R<Map<String, Object>> performHealthCheck() {
        Map<String, Object> result = messageMonitorService.performManualHealthCheck();
        return R.ok(result);
    }

    @Operation(summary = "获取告警信息", description = "获取当前系统告警信息")
    @GetMapping("/monitor/alerts")
    public R<Map<String, Object>> getAlerts() {
        Map<String, Object> alerts = messageMonitorService.getAlerts();
        return R.ok(alerts);
    }

    @Operation(summary = "获取队列监控概览", description = "获取所有队列的监控概览")
    @GetMapping("/monitor/overview")
    public R<Map<String, Object>> getMonitoringOverview() {
        Map<String, Object> overview = Map.of(
            "metrics", messageMonitorService.getRealTimeMetrics(),
            "alerts", messageMonitorService.getAlerts(),
            "statistics", asyncMessageManager.getMessageStatistics(),
            "retryQueue", asyncMessageManager.getRetryQueueStatus()
        );
        return R.ok(overview);
    }

    @Operation(summary = "清理重试队列", description = "清理过期的重试消息")
    @PostMapping("/retry-queue/cleanup")
    public R<String> cleanupRetryQueue() {
        // 这个方法由定时任务自动执行，这里提供手动触发
        return R.ok("重试队列清理已触发");
    }

    @Operation(summary = "暂停消息处理", description = "暂停异步消息处理")
    @PostMapping("/pause")
    public R<String> pauseMessageProcessing() {
        // TODO: 实现暂停逻辑
        return R.ok("消息处理已暂停");
    }

    @Operation(summary = "恢复消息处理", description = "恢复异步消息处理")
    @PostMapping("/resume")
    public R<String> resumeMessageProcessing() {
        // TODO: 实现恢复逻辑
        return R.ok("消息处理已恢复");
    }

    @Operation(summary = "获取处理状态", description = "获取消息处理器状态")
    @GetMapping("/status")
    public R<Map<String, Object>> getProcessingStatus() {
        Map<String, Object> status = Map.of(
            "isRunning", true, // TODO: 实现实际状态检查
            "lastActivity", System.currentTimeMillis(),
            "version", "1.0.0"
        );
        return R.ok(status);
    }

    @Operation(summary = "测试消息发送", description = "发送测试消息验证系统功能")
    @PostMapping("/test")
    public R<String> sendTestMessage(
        @Parameter(description = "平台类型") @RequestParam String platform,
        @Parameter(description = "聊天ID") @RequestParam String chatId,
        @Parameter(description = "测试内容") @RequestParam(defaultValue = "这是一条测试消息") String content
    ) {
        try {
            BotMessage testMessage = BotMessage.createTextMessage(
                top.continew.admin.accounting.enums.PlatformType.valueOf(platform.toUpperCase()),
                chatId,
                1L, // 测试用户ID
                "test_user",
                content
            );
            
            asyncMessageManager.sendMessageAsync(testMessage);
            return R.ok("测试消息已发送，消息ID: " + testMessage.getMessageId());
            
        } catch (Exception e) {
            return R.fail("发送测试消息失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取消息处理历史", description = "获取最近的消息处理历史")
    @GetMapping("/history")
    public R<Map<String, Object>> getProcessingHistory(
        @Parameter(description = "限制数量") @RequestParam(defaultValue = "100") int limit
    ) {
        // TODO: 实现消息处理历史查询
        Map<String, Object> history = Map.of(
            "total", 0,
            "messages", List.of(),
            "limit", limit
        );
        return R.ok(history);
    }

    @Operation(summary = "导出监控数据", description = "导出消息队列监控数据")
    @GetMapping("/monitor/export")
    public R<String> exportMonitoringData(
        @Parameter(description = "导出格式") @RequestParam(defaultValue = "json") String format
    ) {
        // TODO: 实现监控数据导出
        return R.ok("监控数据导出功能开发中");
    }
}
