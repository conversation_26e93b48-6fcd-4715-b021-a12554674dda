# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

ContiNew Admin is a high-quality multi-tenant backend management system framework based on Spring Boot 3 (Java 17) and modern technologies. The project uses a modular architecture with separate modules for different business domains.

## Build and Development Commands

### Maven Commands
- **Build the project**: `mvn clean compile`
- **Run tests**: `mvn test`
- **Package application**: `mvn clean package`
- **Skip tests during build**: `mvn clean package -DskipTests`
- **Format code**: `mvn compile` (Spotless plugin runs during compile phase)
- **Generate test coverage**: `mvn jacoco:report`

### Running the Application
- **Main application**: Run `ContiNewAdminApplication.java` in `continew-server` module
- **Schedule server**: Run `ScheduleServerApplication.java` in `continew-extension-schedule-server` module
- **Default port**: 8000 (configurable in `application-dev.yml`)

### Test Commands
For comprehensive testing (especially accounting module):
- Windows: `continew-accounting/run-tests.bat`
- Linux/Mac: `continew-accounting/run-tests.sh`

## Architecture and Module Structure

### Core Modules
- **continew-server**: Main application module (entry point, configuration, deployment)
- **continew-system**: System management (users, roles, menus, departments, auth)
- **continew-common**: Common utilities, base classes, shared configurations
- **continew-plugin**: Plugin modules (generator, tenant, schedule, open API)
- **continew-extension**: Extension modules (schedule server)

### Business Modules
- **continew-accounting**: Financial accounting and transaction management
- **continew-bot**: Multi-platform bot integration (Telegram, Discord, WeChat)
- **continew-api**: API management and access control

### Key Architecture Patterns
1. **CRUD Base Classes**: All controllers extend `BaseController` with generic CRUD operations
2. **Multi-tenant Support**: Tenant isolation at row level with automatic filtering
3. **Modular Design**: Business features organized in separate modules
4. **Convention over Configuration**: Heavy use of annotations and auto-configuration

## Configuration

### Database Configuration
- Primary database: MySQL 8.0+ (configured in `application-dev.yml`)
- Connection pool: HikariCP
- ORM: MyBatis Plus with Liquibase for schema migration
- Redis for caching and session storage

### Key Configuration Files
- `continew-server/src/main/resources/config/application.yml`: Base configuration
- `continew-server/src/main/resources/config/application-dev.yml`: Development settings
- `continew-server/src/main/resources/config/application-prod.yml`: Production settings
- `lombok.config`: Global Lombok configuration with inheritance rules

### Environment Variables
- `DB_HOST`, `DB_PORT`, `DB_USER`, `DB_PWD`, `DB_NAME`: Database connection
- `REDIS_HOST`, `REDIS_PORT`, `REDIS_PWD`, `REDIS_DB`: Redis connection

## Code Style and Standards

### Code Quality
- **Alibaba Java Coding Guidelines**: Strictly followed
- **Comment Coverage**: >45% required
- **Spotless Code Formatting**: Auto-applied during compilation
- **Lombok Configuration**: Inheritance-aware with disabled risky annotations

### Key Conventions
1. Package structure: `top.continew.admin.{module}.{layer}`
2. Controller naming: `{Entity}Controller`
3. Service interfaces: `{Entity}Service` with `{Entity}ServiceImpl`
4. Entity inheritance: Use `@EqualsAndHashCode(callSuper = true)` and `@ToString(callSuper = true)`

## Development Workflow

### Making Changes
1. Code changes trigger automatic formatting during `mvn compile`
2. Use CRUD annotations for standard operations: `@CrudRequestMapping`
3. Follow the existing module structure and naming conventions
4. All new features should include proper API documentation with Knife4j

### Testing Strategy
- Unit tests in each module's `src/test/java`
- Integration tests for complex business logic
- Test coverage reports generated with JaCoCo
- Comprehensive test scripts available for critical modules

### Common Tasks
- **Add new CRUD entity**: Extend `BaseController`, use `@CrudRequestMapping`
- **Database changes**: Add Liquibase changesets in `db/changelog`
- **New business module**: Follow existing module structure patterns
- **API documentation**: Use OpenAPI 3 annotations, available at `/doc.html`

## Technology Stack Integration

### Core Technologies
- **Spring Boot 3.3.12** with Java 17
- **Sa-Token + JWT** for authentication
- **MyBatis Plus 3.5.12** for database operations
- **Redisson 3.49.0** for distributed operations
- **JetCache 2.7.8** for caching

### Business Libraries
- **ContiNew Starter 2.13.4**: Custom starter with enterprise optimizations
- **Snail Job 1.5.0**: Distributed task scheduling
- **JustAuth 1.16.7**: Third-party login integration
- **Fast Excel 1.2.0**: Excel processing
- **Crane4j 2.9.0**: Data filling framework

## Important Notes

- **Multi-tenant**: All database operations automatically include tenant filtering
- **Security**: Built-in XSS filtering, field encryption, JSON desensitization
- **Performance**: P6Spy for SQL monitoring in development
- **Caching**: Two-level caching with local (Caffeine) and remote (Redis)
- **Logging**: TLog for request tracing, comprehensive access logging