package top.continew.admin.accounting.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.starter.extension.crud.model.query.SortQuery;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 钱包查询条件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "钱包查询条件")
public class WalletQuery extends SortQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 币种
     */
    @Schema(description = "币种", example = "CNY")
    private String currency;

    /**
     * 最小余额
     */
    @Schema(description = "最小余额", example = "0")
    private BigDecimal minBalance;

    /**
     * 最大余额
     */
    @Schema(description = "最大余额", example = "10000")
    private BigDecimal maxBalance;

    /**
     * 是否有冻结金额
     */
    @Schema(description = "是否有冻结金额", example = "true")
    private Boolean hasFrozenAmount;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "1")
    private Integer status;
}
