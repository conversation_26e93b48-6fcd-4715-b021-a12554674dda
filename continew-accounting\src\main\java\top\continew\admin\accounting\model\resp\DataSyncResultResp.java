package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 数据同步结果响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "数据同步结果响应")
public class DataSyncResultResp {

    /**
     * 同步ID
     */
    @Schema(description = "同步ID", example = "sync_20250101_120000_001")
    private String syncId;

    /**
     * 配置ID
     */
    @Schema(description = "配置ID", example = "1")
    private Long configId;

    /**
     * 同步状态
     */
    @Schema(description = "同步状态", example = "SUCCESS", allowableValues = {
            "PENDING", "RUNNING", "SUCCESS", "FAILED", "PARTIAL", "CANCELLED"
    })
    private String status;

    /**
     * 同步类型
     */
    @Schema(description = "同步类型", example = "INCREMENTAL")
    private String syncType;

    /**
     * 同步方向
     */
    @Schema(description = "同步方向", example = "TO_TARGET")
    private String syncDirection;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2025-01-01 12:00:00")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", example = "2025-01-01 12:05:30")
    private LocalDateTime endTime;

    /**
     * 执行时长（毫秒）
     */
    @Schema(description = "执行时长（毫秒）", example = "330000")
    private Long durationMs;

    /**
     * 处理记录数
     */
    @Schema(description = "处理记录数", example = "1000")
    private Integer recordsProcessed;

    /**
     * 成功记录数
     */
    @Schema(description = "成功记录数", example = "980")
    private Integer recordsSuccess;

    /**
     * 失败记录数
     */
    @Schema(description = "失败记录数", example = "15")
    private Integer recordsFailed;

    /**
     * 跳过记录数
     */
    @Schema(description = "跳过记录数", example = "5")
    private Integer recordsSkipped;

    /**
     * 冲突记录数
     */
    @Schema(description = "冲突记录数", example = "3")
    private Integer recordsConflict;

    /**
     * 成功率
     */
    @Schema(description = "成功率", example = "98.0")
    private Double successRate;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 错误代码
     */
    @Schema(description = "错误代码")
    private String errorCode;

    /**
     * 错误详情
     */
    @Schema(description = "错误详情")
    private List<ErrorDetail> errorDetails;

    /**
     * 同步详情
     */
    @Schema(description = "同步详情")
    private Map<String, Object> syncDetails;

    /**
     * 冲突详情
     */
    @Schema(description = "冲突详情")
    private List<ConflictDetail> conflictDetails;

    /**
     * 性能指标
     */
    @Schema(description = "性能指标")
    private PerformanceMetrics performanceMetrics;

    /**
     * 是否重试
     */
    @Schema(description = "是否重试", example = "false")
    private Boolean isRetry;

    /**
     * 重试次数
     */
    @Schema(description = "重试次数", example = "0")
    private Integer retryCount;

    /**
     * 触发方式
     */
    @Schema(description = "触发方式", example = "MANUAL")
    private String triggerType;

    /**
     * 错误详情
     */
    @Data
    @Schema(description = "错误详情")
    public static class ErrorDetail {

        /**
         * 记录ID
         */
        @Schema(description = "记录ID", example = "123")
        private String recordId;

        /**
         * 错误类型
         */
        @Schema(description = "错误类型", example = "VALIDATION_ERROR")
        private String errorType;

        /**
         * 错误消息
         */
        @Schema(description = "错误消息", example = "字段格式不正确")
        private String errorMessage;

        /**
         * 错误字段
         */
        @Schema(description = "错误字段", example = "amount")
        private String errorField;

        /**
         * 错误值
         */
        @Schema(description = "错误值", example = "invalid_amount")
        private String errorValue;
    }

    /**
     * 冲突详情
     */
    @Data
    @Schema(description = "冲突详情")
    public static class ConflictDetail {

        /**
         * 记录ID
         */
        @Schema(description = "记录ID", example = "123")
        private String recordId;

        /**
         * 冲突类型
         */
        @Schema(description = "冲突类型", example = "DATA_CONFLICT")
        private String conflictType;

        /**
         * 冲突字段
         */
        @Schema(description = "冲突字段", example = "amount")
        private String conflictField;

        /**
         * 本地值
         */
        @Schema(description = "本地值", example = "100.00")
        private String localValue;

        /**
         * 远程值
         */
        @Schema(description = "远程值", example = "120.00")
        private String remoteValue;

        /**
         * 解决方式
         */
        @Schema(description = "解决方式", example = "LOCAL_WINS")
        private String resolutionType;

        /**
         * 最终值
         */
        @Schema(description = "最终值", example = "100.00")
        private String finalValue;
    }

    /**
     * 性能指标
     */
    @Data
    @Schema(description = "性能指标")
    public static class PerformanceMetrics {

        /**
         * 平均处理速度（记录/秒）
         */
        @Schema(description = "平均处理速度（记录/秒）", example = "50.5")
        private Double avgProcessingSpeed;

        /**
         * 峰值处理速度（记录/秒）
         */
        @Schema(description = "峰值处理速度（记录/秒）", example = "80.2")
        private Double peakProcessingSpeed;

        /**
         * 内存使用量（MB）
         */
        @Schema(description = "内存使用量（MB）", example = "256")
        private Long memoryUsageMb;

        /**
         * 网络传输量（MB）
         */
        @Schema(description = "网络传输量（MB）", example = "12.5")
        private Double networkTransferMb;

        /**
         * 数据库查询次数
         */
        @Schema(description = "数据库查询次数", example = "150")
        private Integer dbQueryCount;

        /**
         * API调用次数
         */
        @Schema(description = "API调用次数", example = "25")
        private Integer apiCallCount;
    }
}
