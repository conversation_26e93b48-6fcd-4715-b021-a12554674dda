<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.accounting.mapper.CacheStatisticsMapper">

    <!-- 获取总体缓存统计 -->
    <select id="getOverallCacheStatistics" resultType="map">
        SELECT 
            COUNT(*) as totalCaches,
            AVG(hit_rate) as avgHitRate,
            AVG(avg_load_time) as avgLoadTime,
            SUM(hit_count) as totalHitCount,
            SUM(miss_count) as totalMissCount,
            SUM(load_count) as totalLoadCount,
            SUM(eviction_count) as totalEvictionCount,
            AVG(qps) as avgQps,
            AVG(tps) as avgTps,
            MAX(peak_qps) as maxQps,
            AVG(memory_usage) as avgMemoryUsage,
            SUM(network_in_bytes) as totalNetworkIn,
            SUM(network_out_bytes) as totalNetworkOut
        FROM acc_cache_statistics 
        WHERE deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        AND statistic_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    </select>

    <!-- 获取缓存类型统计 -->
    <select id="getCacheTypeStatistics" resultType="map">
        SELECT 
            cache_type as cacheType,
            COUNT(*) as count,
            AVG(hit_rate) as avgHitRate,
            AVG(avg_load_time) as avgLoadTime,
            SUM(hit_count) as totalHitCount,
            SUM(miss_count) as totalMissCount,
            AVG(qps) as avgQps,
            AVG(memory_usage) as avgMemoryUsage
        FROM acc_cache_statistics 
        WHERE deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        AND statistic_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        GROUP BY cache_type
        ORDER BY count DESC
    </select>

    <!-- 获取热点数据统计 -->
    <select id="getHotspotStatistics" resultType="map">
        SELECT 
            cache_key as cacheKey,
            SUM(hit_count) as totalHitCount,
            AVG(hit_rate) as avgHitRate,
            MAX(statistic_time) as lastAccessTime,
            COUNT(*) as recordCount,
            AVG(avg_load_time) as avgLoadTime
        FROM acc_cache_statistics 
        WHERE deleted = 0
        <if test="cacheName != null">
            AND cache_name = #{cacheName}
        </if>
        AND statistic_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        GROUP BY cache_key
        ORDER BY totalHitCount DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 获取性能趋势统计 -->
    <select id="getPerformanceTrendStatistics" resultType="map">
        SELECT 
            DATE_FORMAT(statistic_time, '%Y-%m-%d %H:00:00') as timestamp,
            AVG(hit_rate) as avgHitRate,
            AVG(avg_load_time) as avgLoadTime,
            AVG(qps) as avgQps,
            AVG(tps) as avgTps,
            SUM(hit_count) as totalHitCount,
            SUM(miss_count) as totalMissCount,
            AVG(memory_usage) as avgMemoryUsage
        FROM acc_cache_statistics 
        WHERE deleted = 0
        <if test="cacheName != null">
            AND cache_name = #{cacheName}
        </if>
        <if test="hours != null">
            AND statistic_time >= DATE_SUB(NOW(), INTERVAL #{hours} HOUR)
        </if>
        GROUP BY DATE_FORMAT(statistic_time, '%Y-%m-%d %H:00:00')
        ORDER BY timestamp DESC
    </select>

    <!-- 获取错误统计 -->
    <select id="getErrorStatistics" resultType="map">
        SELECT 
            DATE_FORMAT(statistic_time, '%Y-%m-%d %H:00:00') as timestamp,
            SUM(error_count) as totalErrorCount,
            SUM(timeout_count) as totalTimeoutCount,
            SUM(exception_count) as totalExceptionCount,
            AVG(error_rate) as avgErrorRate,
            COUNT(*) as recordCount
        FROM acc_cache_statistics 
        WHERE deleted = 0
        <if test="cacheName != null">
            AND cache_name = #{cacheName}
        </if>
        <if test="hours != null">
            AND statistic_time >= DATE_SUB(NOW(), INTERVAL #{hours} HOUR)
        </if>
        AND (error_count > 0 OR timeout_count > 0 OR exception_count > 0)
        GROUP BY DATE_FORMAT(statistic_time, '%Y-%m-%d %H:00:00')
        ORDER BY timestamp DESC
    </select>

    <!-- 获取缓存性能指标 -->
    <select id="getCachePerformanceMetrics" resultType="map">
        SELECT 
            cache_name as cacheName,
            cache_type as cacheType,
            AVG(hit_rate) as avgHitRate,
            MIN(hit_rate) as minHitRate,
            MAX(hit_rate) as maxHitRate,
            AVG(avg_load_time) as avgLoadTime,
            MIN(min_load_time) as minLoadTime,
            MAX(max_load_time) as maxLoadTime,
            AVG(qps) as avgQps,
            MAX(peak_qps) as maxQps,
            AVG(tps) as avgTps,
            MAX(peak_tps) as maxTps,
            AVG(memory_usage) as avgMemoryUsage,
            MAX(memory_usage) as maxMemoryUsage,
            SUM(hit_count) as totalHitCount,
            SUM(miss_count) as totalMissCount,
            SUM(load_count) as totalLoadCount,
            SUM(eviction_count) as totalEvictionCount
        FROM acc_cache_statistics 
        WHERE deleted = 0
        <if test="cacheName != null">
            AND cache_name = #{cacheName}
        </if>
        <if test="hours != null">
            AND statistic_time >= DATE_SUB(NOW(), INTERVAL #{hours} HOUR)
        </if>
        GROUP BY cache_name, cache_type
        ORDER BY avgHitRate DESC
    </select>

    <!-- 获取缓存健康状态 -->
    <select id="getCacheHealthStatus" resultType="map">
        SELECT 
            COUNT(*) as totalCaches,
            COUNT(CASE WHEN hit_rate >= 0.8 THEN 1 END) as healthyCaches,
            COUNT(CASE WHEN hit_rate >= 0.6 AND hit_rate < 0.8 THEN 1 END) as warningCaches,
            COUNT(CASE WHEN hit_rate < 0.6 THEN 1 END) as criticalCaches,
            AVG(hit_rate) as overallHitRate,
            AVG(avg_load_time) as overallAvgLoadTime,
            AVG(error_rate) as overallErrorRate,
            CASE 
                WHEN AVG(hit_rate) >= 0.8 AND AVG(error_rate) <= 0.01 THEN 'HEALTHY'
                WHEN AVG(hit_rate) >= 0.6 AND AVG(error_rate) <= 0.05 THEN 'WARNING'
                ELSE 'CRITICAL'
            END as overallHealthStatus
        FROM acc_cache_statistics 
        WHERE deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        AND statistic_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
    </select>

    <!-- 检测缓存异常 -->
    <select id="detectCacheAnomalies" resultType="map">
        SELECT 
            cache_name as cacheName,
            cache_type as cacheType,
            'LOW_HIT_RATE' as anomalyType,
            CONCAT('命中率过低: ', ROUND(AVG(hit_rate) * 100, 2), '%') as description,
            'WARNING' as severity,
            MAX(statistic_time) as detectedTime
        FROM acc_cache_statistics 
        WHERE deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        AND statistic_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        GROUP BY cache_name, cache_type
        HAVING AVG(hit_rate) < 0.6
        
        UNION ALL
        
        SELECT 
            cache_name as cacheName,
            cache_type as cacheType,
            'HIGH_ERROR_RATE' as anomalyType,
            CONCAT('错误率过高: ', ROUND(AVG(error_rate) * 100, 2), '%') as description,
            'CRITICAL' as severity,
            MAX(statistic_time) as detectedTime
        FROM acc_cache_statistics 
        WHERE deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        AND statistic_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        GROUP BY cache_name, cache_type
        HAVING AVG(error_rate) > 0.05
        
        UNION ALL
        
        SELECT 
            cache_name as cacheName,
            cache_type as cacheType,
            'HIGH_LOAD_TIME' as anomalyType,
            CONCAT('加载时间过长: ', ROUND(AVG(avg_load_time), 2), 'ms') as description,
            'WARNING' as severity,
            MAX(statistic_time) as detectedTime
        FROM acc_cache_statistics 
        WHERE deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        AND statistic_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        GROUP BY cache_name, cache_type
        HAVING AVG(avg_load_time) > 1000
        
        ORDER BY severity DESC, detectedTime DESC
    </select>

    <!-- 获取缓存使用模式分析 -->
    <select id="getCacheUsagePattern" resultType="map">
        SELECT 
            cache_name as cacheName,
            AVG(qps) as avgQps,
            MAX(peak_qps) as maxQps,
            MIN(qps) as minQps,
            STDDEV(qps) as qpsVariance,
            AVG(hit_rate) as avgHitRate,
            STDDEV(hit_rate) as hitRateVariance,
            COUNT(*) as totalRecords,
            MIN(statistic_time) as startTime,
            MAX(statistic_time) as endTime,
            CASE 
                WHEN STDDEV(qps) / AVG(qps) < 0.3 THEN 'STABLE'
                WHEN STDDEV(qps) / AVG(qps) < 0.6 THEN 'MODERATE'
                ELSE 'VOLATILE'
            END as usagePattern,
            #{days} as analysisPeriod
        FROM acc_cache_statistics 
        WHERE deleted = 0
        <if test="cacheName != null">
            AND cache_name = #{cacheName}
        </if>
        <if test="days != null">
            AND statistic_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        </if>
        GROUP BY cache_name
    </select>

    <!-- 获取缓存容量预测 -->
    <select id="getCacheCapacityPrediction" resultType="map">
        SELECT 
            cache_name as cacheName,
            AVG(memory_usage) as currentMemoryUsage,
            MAX(memory_usage) as maxMemoryUsage,
            MIN(memory_usage) as minMemoryUsage,
            STDDEV(memory_usage) as memoryVariance,
            COUNT(*) as recordCount,
            ROUND(AVG(memory_usage) * 1.15, 2) as predictedMemoryUsage,
            ROUND(AVG(memory_usage) * 1.25, 2) as recommendedCapacity,
            #{days} as predictionPeriod,
            CASE 
                WHEN AVG(memory_usage) > 80 THEN 'CAPACITY_WARNING'
                WHEN AVG(memory_usage) > 90 THEN 'CAPACITY_CRITICAL'
                ELSE 'CAPACITY_NORMAL'
            END as capacityStatus
        FROM acc_cache_statistics 
        WHERE deleted = 0
        <if test="cacheName != null">
            AND cache_name = #{cacheName}
        </if>
        <if test="days != null">
            AND statistic_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        </if>
        GROUP BY cache_name
    </select>

    <!-- 获取缓存优化建议 -->
    <select id="getCacheOptimizationSuggestions" resultType="map">
        SELECT 
            cache_name as cacheName,
            'INCREASE_CACHE_SIZE' as suggestionType,
            '增加缓存大小' as title,
            CONCAT('当前命中率 ', ROUND(AVG(hit_rate) * 100, 2), '%，建议增加缓存大小') as description,
            'HIGH' as priority
        FROM acc_cache_statistics 
        WHERE deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        AND statistic_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        GROUP BY cache_name
        HAVING AVG(hit_rate) < 0.7
        
        UNION ALL
        
        SELECT 
            cache_name as cacheName,
            'OPTIMIZE_EVICTION_POLICY' as suggestionType,
            '优化淘汰策略' as title,
            CONCAT('淘汰次数过多: ', SUM(eviction_count), '次') as description,
            'MEDIUM' as priority
        FROM acc_cache_statistics 
        WHERE deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        AND statistic_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        GROUP BY cache_name
        HAVING SUM(eviction_count) > 1000
        
        ORDER BY 
            CASE priority 
                WHEN 'HIGH' THEN 1 
                WHEN 'MEDIUM' THEN 2 
                WHEN 'LOW' THEN 3 
                ELSE 4 
            END
    </select>

    <!-- 批量插入缓存统计数据 -->
    <insert id="batchInsertStatistics">
        INSERT INTO acc_cache_statistics (
            cache_name, cache_type, cache_key, hit_count, miss_count, load_count, 
            eviction_count, hit_rate, avg_load_time, min_load_time, max_load_time,
            qps, tps, peak_qps, peak_tps, memory_usage, network_in_bytes, network_out_bytes,
            error_count, timeout_count, exception_count, error_rate, statistic_time,
            group_id, create_time
        ) VALUES
        <foreach collection="statistics" item="stat" separator=",">
            (
                #{stat.cacheName}, #{stat.cacheType}, #{stat.cacheKey}, #{stat.hitCount}, 
                #{stat.missCount}, #{stat.loadCount}, #{stat.evictionCount}, #{stat.hitRate},
                #{stat.avgLoadTime}, #{stat.minLoadTime}, #{stat.maxLoadTime}, #{stat.qps},
                #{stat.tps}, #{stat.peakQps}, #{stat.peakTps}, #{stat.memoryUsage},
                #{stat.networkInBytes}, #{stat.networkOutBytes}, #{stat.errorCount},
                #{stat.timeoutCount}, #{stat.exceptionCount}, #{stat.errorRate},
                #{stat.statisticTime}, #{stat.groupId}, NOW()
            )
        </foreach>
    </insert>

    <!-- 清理过期的统计数据 -->
    <delete id="cleanupExpiredStatistics">
        DELETE FROM acc_cache_statistics 
        WHERE statistic_time &lt; #{expireTime}
    </delete>

</mapper>
