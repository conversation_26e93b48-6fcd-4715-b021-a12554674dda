package top.continew.admin.accounting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.common.base.service.BaseServiceImpl;
import top.continew.admin.accounting.enums.GroupRole;
import top.continew.admin.accounting.enums.PlatformType;
import top.continew.admin.accounting.mapper.GroupMapper;
import top.continew.admin.accounting.mapper.GroupMemberMapper;
import top.continew.admin.accounting.model.entity.GroupDO;
import top.continew.admin.accounting.model.entity.GroupMemberDO;
import top.continew.admin.accounting.model.query.GroupQuery;
import top.continew.admin.accounting.model.req.GroupCreateReq;
import top.continew.admin.accounting.model.req.GroupUpdateReq;
import top.continew.admin.accounting.model.resp.GroupDetailResp;
import top.continew.admin.accounting.model.resp.GroupListResp;
import top.continew.admin.accounting.service.GroupService;
import top.continew.starter.core.util.validate.CheckUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 群组管理业务实现
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Service
@RequiredArgsConstructor
public class GroupServiceImpl extends BaseServiceImpl<GroupMapper, GroupDO, GroupListResp, GroupDetailResp, GroupQuery, GroupCreateReq> implements GroupService {

    private final GroupMemberMapper groupMemberMapper;

    @Override
    public void beforeCreate(GroupCreateReq req) {
        this.checkNameRepeat(req.getName(), null);
        this.checkPlatformGroupIdRepeat(req.getPlatform(), req.getPlatformGroupId(), null);
    }

    @Override
    public void beforeUpdate(GroupCreateReq req, Long id) {
        this.checkNameRepeat(req.getName(), id);
        GroupDO oldGroup = super.getById(id);
        CheckUtils.throwIfNotEqual(req.getPlatform(), oldGroup.getPlatform(), "不允许修改平台类型");
        CheckUtils.throwIfNotEqual(req.getPlatformGroupId(), oldGroup.getPlatformGroupId(), "不允许修改平台群组ID");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(GroupUpdateReq req, Long id) {
        GroupDO group = super.getById(id);
        CheckUtils.throwIfNull(group, "群组不存在");
        
        // 检查名称重复
        if (StrUtil.isNotBlank(req.getName()) && !req.getName().equals(group.getName())) {
            this.checkNameRepeat(req.getName(), id);
        }
        
        // 更新群组信息
        GroupDO updateGroup = new GroupDO();
        updateGroup.setId(id);
        BeanUtil.copyProperties(req, updateGroup, "id");
        super.updateById(updateGroup);
    }

    @Override
    public GroupDO getByPlatformInfo(PlatformType platform, String platformGroupId) {
        return baseMapper.lambdaQuery()
            .eq(GroupDO::getPlatform, platform)
            .eq(GroupDO::getPlatformGroupId, platformGroupId)
            .one();
    }

    @Override
    public List<GroupListResp> getUserGroups(Long userId) {
        return baseMapper.selectUserGroups(userId);
    }

    @Override
    public boolean isMember(Long groupId, Long userId) {
        return groupMemberMapper.lambdaQuery()
            .eq(GroupMemberDO::getGroupId, groupId)
            .eq(GroupMemberDO::getUserId, userId)
            .eq(GroupMemberDO::getStatus, 1)
            .exists();
    }

    @Override
    public boolean isAdmin(Long groupId, Long userId) {
        GroupMemberDO member = groupMemberMapper.lambdaQuery()
            .eq(GroupMemberDO::getGroupId, groupId)
            .eq(GroupMemberDO::getUserId, userId)
            .eq(GroupMemberDO::getStatus, 1)
            .one();
        
        if (member == null) {
            return false;
        }
        
        GroupRole role = member.getRole();
        return role != null && role.canManageGroup();
    }

    @Override
    public boolean isTransactionLimitExceeded(Long groupId) {
        GroupDO group = super.getById(groupId);
        if (group == null || group.getSubscriptionPlan() == null) {
            return true;
        }
        
        int currentCount = baseMapper.selectCurrentMonthTransactionCount(groupId);
        int limit = group.getSubscriptionPlan().getTransactionLimit();
        
        return limit > 0 && currentCount >= limit;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addMember(Long groupId, Long userId, String platformUserId, String nickname, Long inviterId) {
        // 检查是否已是成员
        boolean exists = groupMemberMapper.lambdaQuery()
            .eq(GroupMemberDO::getGroupId, groupId)
            .eq(GroupMemberDO::getUserId, userId)
            .exists();
        
        if (exists) {
            // 如果已存在但状态为禁用，则重新激活
            groupMemberMapper.lambdaUpdate()
                .eq(GroupMemberDO::getGroupId, groupId)
                .eq(GroupMemberDO::getUserId, userId)
                .set(GroupMemberDO::getStatus, 1)
                .set(GroupMemberDO::getJoinTime, LocalDateTime.now())
                .update();
        } else {
            // 新增成员
            GroupMemberDO member = new GroupMemberDO();
            member.setGroupId(groupId);
            member.setUserId(userId);
            member.setPlatformUserId(platformUserId);
            member.setNickname(nickname);
            member.setRole(GroupRole.MEMBER);
            member.setStatus(1);
            member.setJoinTime(LocalDateTime.now());
            member.setInviterId(inviterId);
            groupMemberMapper.insert(member);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeMember(Long groupId, Long userId) {
        groupMemberMapper.lambdaUpdate()
            .eq(GroupMemberDO::getGroupId, groupId)
            .eq(GroupMemberDO::getUserId, userId)
            .set(GroupMemberDO::getStatus, 0)
            .update();
    }

    @Override
    protected QueryWrapper<GroupDO> buildQueryWrapper(GroupQuery query) {
        QueryWrapper<GroupDO> queryWrapper = super.buildQueryWrapper(query);
        queryWrapper.like(StrUtil.isNotBlank(query.getName()), "name", query.getName())
            .eq(ObjectUtil.isNotNull(query.getPlatform()), "platform", query.getPlatform())
            .eq(StrUtil.isNotBlank(query.getPlatformGroupId()), "platform_group_id", query.getPlatformGroupId())
            .eq(ObjectUtil.isNotNull(query.getSubscriptionPlan()), "subscription_plan", query.getSubscriptionPlan())
            .eq(ObjectUtil.isNotNull(query.getStatus()), "status", query.getStatus())
            .eq(ObjectUtil.isNotNull(query.getCreateUser()), "create_user", query.getCreateUser());
        return queryWrapper;
    }

    /**
     * 检查名称是否重复
     *
     * @param name 名称
     * @param id   ID
     */
    private void checkNameRepeat(String name, Long id) {
        CheckUtils.throwIf(baseMapper.lambdaQuery()
            .eq(GroupDO::getName, name)
            .ne(id != null, GroupDO::getId, id)
            .exists(), "名称为 [{}] 的群组已存在", name);
    }

    /**
     * 检查平台群组ID是否重复
     *
     * @param platform        平台类型
     * @param platformGroupId 平台群组ID
     * @param id              ID
     */
    private void checkPlatformGroupIdRepeat(PlatformType platform, String platformGroupId, Long id) {
        CheckUtils.throwIf(baseMapper.lambdaQuery()
            .eq(GroupDO::getPlatform, platform)
            .eq(GroupDO::getPlatformGroupId, platformGroupId)
            .ne(id != null, GroupDO::getId, id)
            .exists(), "平台群组ID为 [{}] 的群组已存在", platformGroupId);
    }
}
