package top.continew.admin.accounting.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.accounting.model.entity.SubscriptionDO;
import top.continew.admin.accounting.model.query.SubscriptionQuery;
import top.continew.admin.accounting.model.resp.SubscriptionDetailResp;
import top.continew.admin.accounting.model.resp.SubscriptionListResp;
import top.continew.starter.extension.crud.mapper.BaseMapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 订阅记录 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface SubscriptionMapper extends BaseMapper<SubscriptionDO> {

    /**
     * 分页查询订阅列表
     */
    IPage<SubscriptionListResp> selectSubscriptionPage(Page<SubscriptionListResp> page, @Param("query") SubscriptionQuery query);

    /**
     * 查询订阅详情
     */
    SubscriptionDetailResp selectSubscriptionDetail(@Param("id") Long id);

    /**
     * 根据群组ID查询当前有效订阅
     */
    SubscriptionDO selectActiveByGroupId(@Param("groupId") Long groupId);

    /**
     * 查询即将过期的订阅
     */
    List<SubscriptionDO> selectExpiringSubscriptions(@Param("days") Integer days);

    /**
     * 查询已过期的订阅
     */
    List<SubscriptionDO> selectExpiredSubscriptions();

    /**
     * 更新订阅状态
     */
    int updateStatus(@Param("id") Long id, @Param("status") String status, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新自动续费状态
     */
    int updateAutoRenew(@Param("id") Long id, @Param("autoRenew") Boolean autoRenew, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 取消订阅
     */
    int cancelSubscription(@Param("id") Long id, @Param("reason") String reason, @Param("cancelTime") LocalDateTime cancelTime);

    /**
     * 续费订阅
     */
    int renewSubscription(@Param("id") Long id, @Param("endDate") LocalDateTime endDate, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 查询用户订阅历史
     */
    List<SubscriptionListResp> selectUserSubscriptionHistory(@Param("userId") Long userId);

    /**
     * 查询群组订阅历史
     */
    List<SubscriptionListResp> selectGroupSubscriptionHistory(@Param("groupId") Long groupId);

    /**
     * 统计订阅数量
     */
    Long countSubscriptions(@Param("status") String status, @Param("planId") Long planId);

    /**
     * 统计收入
     */
    java.math.BigDecimal sumRevenue(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 查询订阅趋势
     */
    List<SubscriptionTrendResp> selectSubscriptionTrend(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 订阅趋势响应
     */
    class SubscriptionTrendResp {
        private String date;
        private Long newSubscriptions;
        private Long cancelledSubscriptions;
        private java.math.BigDecimal revenue;
        
        // getters and setters
        public String getDate() { return date; }
        public void setDate(String date) { this.date = date; }
        public Long getNewSubscriptions() { return newSubscriptions; }
        public void setNewSubscriptions(Long newSubscriptions) { this.newSubscriptions = newSubscriptions; }
        public Long getCancelledSubscriptions() { return cancelledSubscriptions; }
        public void setCancelledSubscriptions(Long cancelledSubscriptions) { this.cancelledSubscriptions = cancelledSubscriptions; }
        public java.math.BigDecimal getRevenue() { return revenue; }
        public void setRevenue(java.math.BigDecimal revenue) { this.revenue = revenue; }
    }
}
