package top.continew.admin.accounting.service.sync;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.continew.admin.accounting.model.entity.DataSyncConfigDO;
import top.continew.starter.core.exception.BusinessException;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 增量同步管理器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class IncrementalSyncManager {

    private final ConflictResolver conflictResolver;
    private final DataTransformEngine dataTransformEngine;

    /**
     * 执行增量同步
     *
     * @param config 同步配置
     * @param sourceAdapter 源适配器
     * @param targetAdapter 目标适配器
     * @param sourceConfig 源配置
     * @param targetConfig 目标配置
     * @param fieldMapping 字段映射
     * @param lastSyncTime 上次同步时间
     * @param progress 同步进度
     * @return 同步结果
     */
    public Map<String, Object> executeIncrementalSync(DataSyncConfigDO config,
                                                      DataSourceAdapter sourceAdapter,
                                                      DataSourceAdapter targetAdapter,
                                                      Map<String, Object> sourceConfig,
                                                      Map<String, Object> targetConfig,
                                                      Map<String, Object> fieldMapping,
                                                      Map<String, Object> transformRules,
                                                      LocalDateTime lastSyncTime,
                                                      SyncEngine.SyncProgress progress) {
        log.info("执行增量同步: configId={}, lastSyncTime={}", config.getId(), lastSyncTime);
        
        Map<String, Object> result = new HashMap<>();
        int totalCount = 0;
        int successCount = 0;
        int failedCount = 0;
        int conflictCount = 0;
        List<String> errors = new ArrayList<>();
        List<Map<String, Object>> conflicts = new ArrayList<>();
        
        try {
            // 检测源数据变更
            Map<String, Object> changeDetection = sourceAdapter.detectChanges(sourceConfig, lastSyncTime);
            Boolean hasChanges = (Boolean) changeDetection.get("hasChanges");
            
            if (hasChanges == null || !hasChanges) {
                log.info("源数据无变更，跳过增量同步: configId={}", config.getId());
                result.put("syncType", "INCREMENTAL");
                result.put("totalCount", 0);
                result.put("successCount", 0);
                result.put("failedCount", 0);
                result.put("conflictCount", 0);
                result.put("message", "无数据变更");
                return result;
            }
            
            // 读取增量数据
            List<Map<String, Object>> incrementalData = sourceAdapter.readData(
                    sourceConfig, fieldMapping, null, lastSyncTime, null);
            
            if (CollUtil.isEmpty(incrementalData)) {
                log.info("增量数据为空，跳过同步: configId={}", config.getId());
                result.put("syncType", "INCREMENTAL");
                result.put("totalCount", 0);
                result.put("successCount", 0);
                result.put("failedCount", 0);
                result.put("conflictCount", 0);
                result.put("message", "增量数据为空");
                return result;
            }
            
            totalCount = incrementalData.size();
            progress.setTotalCount(totalCount);
            
            // 分析数据变更类型
            Map<String, List<Map<String, Object>>> changesByType = analyzeChanges(incrementalData, config);
            
            // 处理新增数据
            if (changesByType.containsKey("CREATE")) {
                Map<String, Object> createResult = processCreateChanges(
                        targetAdapter, targetConfig, fieldMapping, transformRules, changesByType.get("CREATE"), progress);
                updateCounts(createResult, result);
            }

            // 处理更新数据
            if (changesByType.containsKey("UPDATE")) {
                Map<String, Object> updateResult = processUpdateChanges(
                        targetAdapter, targetConfig, fieldMapping, transformRules, changesByType.get("UPDATE"), config, progress);
                updateCounts(updateResult, result);

                // 处理冲突
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> updateConflicts = (List<Map<String, Object>>) updateResult.get("conflicts");
                if (CollUtil.isNotEmpty(updateConflicts)) {
                    conflicts.addAll(updateConflicts);
                    conflictCount += updateConflicts.size();
                }
            }

            // 处理删除数据
            if (changesByType.containsKey("DELETE")) {
                Map<String, Object> deleteResult = processDeleteChanges(
                        targetAdapter, targetConfig, fieldMapping, transformRules, changesByType.get("DELETE"), progress);
                updateCounts(deleteResult, result);
            }
            
            // 计算总计数
            successCount = (Integer) result.getOrDefault("successCount", 0);
            failedCount = (Integer) result.getOrDefault("failedCount", 0);
            @SuppressWarnings("unchecked")
            List<String> allErrors = (List<String>) result.getOrDefault("errors", new ArrayList<>());
            errors.addAll(allErrors);
            
        } catch (Exception e) {
            log.error("增量同步执行失败: configId={}, error={}", config.getId(), e.getMessage(), e);
            errors.add("增量同步执行失败: " + e.getMessage());
            failedCount = totalCount;
        }
        
        result.put("syncType", "INCREMENTAL");
        result.put("totalCount", totalCount);
        result.put("successCount", successCount);
        result.put("failedCount", failedCount);
        result.put("conflictCount", conflictCount);
        result.put("conflicts", conflicts);
        result.put("errors", errors);
        
        return result;
    }

    /**
     * 分析数据变更类型
     */
    private Map<String, List<Map<String, Object>>> analyzeChanges(List<Map<String, Object>> data, DataSyncConfigDO config) {
        Map<String, List<Map<String, Object>>> changesByType = new HashMap<>();
        
        String changeTypeField = getChangeTypeField(config);
        
        for (Map<String, Object> record : data) {
            String changeType = determineChangeType(record, changeTypeField);
            
            changesByType.computeIfAbsent(changeType, k -> new ArrayList<>()).add(record);
        }
        
        log.debug("数据变更分析完成: {}", changesByType.keySet());
        return changesByType;
    }

    /**
     * 确定变更类型
     */
    private String determineChangeType(Map<String, Object> record, String changeTypeField) {
        if (StrUtil.isNotBlank(changeTypeField) && record.containsKey(changeTypeField)) {
            String changeType = (String) record.get(changeTypeField);
            if (StrUtil.isNotBlank(changeType)) {
                return changeType.toUpperCase();
            }
        }
        
        // 默认逻辑：根据记录状态判断
        Object deletedFlag = record.get("deleted");
        Object isDeleted = record.get("isDeleted");
        
        if ((deletedFlag != null && Boolean.TRUE.equals(deletedFlag)) ||
            (isDeleted != null && Boolean.TRUE.equals(isDeleted))) {
            return "DELETE";
        }
        
        // 如果有ID且不为空，认为是更新；否则是新增
        Object id = record.get("id");
        if (id != null && StrUtil.isNotBlank(id.toString())) {
            return "UPDATE";
        } else {
            return "CREATE";
        }
    }

    /**
     * 处理新增变更
     */
    private Map<String, Object> processCreateChanges(DataSourceAdapter targetAdapter,
                                                     Map<String, Object> targetConfig,
                                                     Map<String, Object> fieldMapping,
                                                     Map<String, Object> transformRules,
                                                     List<Map<String, Object>> createData,
                                                     SyncEngine.SyncProgress progress) {
        log.info("处理新增数据: count={}", createData.size());

        // 数据转换
        List<Map<String, Object>> transformedData = dataTransformEngine.transformData(
                createData, fieldMapping, transformRules);

        Map<String, Object> result = targetAdapter.writeData(targetConfig, fieldMapping, transformedData, "CREATE");
        
        // 更新进度
        Integer successCount = (Integer) result.get("successCount");
        Integer failedCount = (Integer) result.get("failedCount");
        
        if (successCount != null) {
            progress.setSuccessCount(progress.getSuccessCount() + successCount);
        }
        if (failedCount != null) {
            progress.setFailedCount(progress.getFailedCount() + failedCount);
        }
        progress.setProcessedCount(progress.getProcessedCount() + createData.size());
        
        return result;
    }

    /**
     * 处理更新变更
     */
    private Map<String, Object> processUpdateChanges(DataSourceAdapter targetAdapter,
                                                     Map<String, Object> targetConfig,
                                                     Map<String, Object> fieldMapping,
                                                     Map<String, Object> transformRules,
                                                     List<Map<String, Object>> updateData,
                                                     DataSyncConfigDO config,
                                                     SyncEngine.SyncProgress progress) {
        log.info("处理更新数据: count={}", updateData.size());
        
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failedCount = 0;
        List<String> errors = new ArrayList<>();
        List<Map<String, Object>> conflicts = new ArrayList<>();
        
        for (Map<String, Object> record : updateData) {
            try {
                // 检查是否存在冲突
                Map<String, Object> conflictCheck = checkUpdateConflict(targetAdapter, targetConfig, record, config);
                
                if (Boolean.TRUE.equals(conflictCheck.get("hasConflict"))) {
                    // 处理冲突
                    Map<String, Object> conflictResolution = conflictResolver.resolveConflict(
                            record, conflictCheck, config.getConflictResolution());
                    
                    if ("RESOLVED".equals(conflictResolution.get("status"))) {
                        // 冲突已解决，继续更新
                        @SuppressWarnings("unchecked")
                        Map<String, Object> resolvedRecord = (Map<String, Object>) conflictResolution.get("resolvedData");

                        // 数据转换
                        List<Map<String, Object>> transformedData = dataTransformEngine.transformData(
                                Arrays.asList(resolvedRecord), fieldMapping, transformRules);

                        Map<String, Object> writeResult = targetAdapter.writeData(
                                targetConfig, fieldMapping, transformedData, "UPDATE");
                        
                        if (Boolean.TRUE.equals(writeResult.get("success"))) {
                            successCount++;
                        } else {
                            failedCount++;
                            errors.add("更新失败: " + writeResult.get("error"));
                        }
                    } else {
                        // 冲突未解决，记录冲突
                        conflicts.add(conflictResolution);
                        failedCount++;
                    }
                } else {
                    // 无冲突，直接更新
                    // 数据转换
                    List<Map<String, Object>> transformedData = dataTransformEngine.transformData(
                            Arrays.asList(record), fieldMapping, transformRules);

                    Map<String, Object> writeResult = targetAdapter.writeData(
                            targetConfig, fieldMapping, transformedData, "UPDATE");
                    
                    if (Boolean.TRUE.equals(writeResult.get("success"))) {
                        successCount++;
                    } else {
                        failedCount++;
                        errors.add("更新失败: " + writeResult.get("error"));
                    }
                }
                
            } catch (Exception e) {
                failedCount++;
                errors.add("处理更新记录失败: " + e.getMessage());
                log.error("处理更新记录失败: record={}, error={}", record, e.getMessage());
            }
        }
        
        // 更新进度
        progress.setSuccessCount(progress.getSuccessCount() + successCount);
        progress.setFailedCount(progress.getFailedCount() + failedCount);
        progress.setProcessedCount(progress.getProcessedCount() + updateData.size());
        
        result.put("successCount", successCount);
        result.put("failedCount", failedCount);
        result.put("errors", errors);
        result.put("conflicts", conflicts);
        
        return result;
    }

    /**
     * 处理删除变更
     */
    private Map<String, Object> processDeleteChanges(DataSourceAdapter targetAdapter,
                                                     Map<String, Object> targetConfig,
                                                     Map<String, Object> fieldMapping,
                                                     Map<String, Object> transformRules,
                                                     List<Map<String, Object>> deleteData,
                                                     SyncEngine.SyncProgress progress) {
        log.info("处理删除数据: count={}", deleteData.size());

        // 数据转换
        List<Map<String, Object>> transformedData = dataTransformEngine.transformData(
                deleteData, fieldMapping, transformRules);

        Map<String, Object> result = targetAdapter.writeData(targetConfig, fieldMapping, transformedData, "DELETE");
        
        // 更新进度
        Integer successCount = (Integer) result.get("successCount");
        Integer failedCount = (Integer) result.get("failedCount");
        
        if (successCount != null) {
            progress.setSuccessCount(progress.getSuccessCount() + successCount);
        }
        if (failedCount != null) {
            progress.setFailedCount(progress.getFailedCount() + failedCount);
        }
        progress.setProcessedCount(progress.getProcessedCount() + deleteData.size());
        
        return result;
    }

    /**
     * 检查更新冲突
     */
    private Map<String, Object> checkUpdateConflict(DataSourceAdapter targetAdapter,
                                                    Map<String, Object> targetConfig,
                                                    Map<String, Object> sourceRecord,
                                                    DataSyncConfigDO config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取目标记录的主键
            String primaryKey = getPrimaryKey(config);
            Object keyValue = sourceRecord.get(primaryKey);
            
            if (keyValue == null) {
                result.put("hasConflict", false);
                return result;
            }
            
            // 查询目标记录
            Map<String, Object> filter = new HashMap<>();
            filter.put(primaryKey, keyValue);
            
            List<Map<String, Object>> targetRecords = targetAdapter.readData(
                    targetConfig, null, filter, null, 1);
            
            if (CollUtil.isEmpty(targetRecords)) {
                result.put("hasConflict", false);
                return result;
            }
            
            Map<String, Object> targetRecord = targetRecords.get(0);
            
            // 检查时间戳冲突
            String timestampField = getTimestampField(config);
            if (StrUtil.isNotBlank(timestampField)) {
                Object sourceTimestamp = sourceRecord.get(timestampField);
                Object targetTimestamp = targetRecord.get(timestampField);
                
                if (sourceTimestamp != null && targetTimestamp != null) {
                    // 比较时间戳
                    if (compareTimestamps(sourceTimestamp, targetTimestamp) < 0) {
                        result.put("hasConflict", true);
                        result.put("conflictType", "TIMESTAMP");
                        result.put("sourceRecord", sourceRecord);
                        result.put("targetRecord", targetRecord);
                        return result;
                    }
                }
            }
            
            // 检查字段值冲突
            List<String> conflictFields = findConflictFields(sourceRecord, targetRecord, config);
            if (CollUtil.isNotEmpty(conflictFields)) {
                result.put("hasConflict", true);
                result.put("conflictType", "FIELD_VALUE");
                result.put("conflictFields", conflictFields);
                result.put("sourceRecord", sourceRecord);
                result.put("targetRecord", targetRecord);
                return result;
            }
            
            result.put("hasConflict", false);
            
        } catch (Exception e) {
            log.error("检查更新冲突失败: {}", e.getMessage(), e);
            result.put("hasConflict", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 比较时间戳
     */
    private int compareTimestamps(Object timestamp1, Object timestamp2) {
        // 简化实现，实际应该根据时间戳类型进行比较
        if (timestamp1 instanceof LocalDateTime && timestamp2 instanceof LocalDateTime) {
            return ((LocalDateTime) timestamp1).compareTo((LocalDateTime) timestamp2);
        }
        
        return timestamp1.toString().compareTo(timestamp2.toString());
    }

    /**
     * 查找冲突字段
     */
    private List<String> findConflictFields(Map<String, Object> sourceRecord,
                                           Map<String, Object> targetRecord,
                                           DataSyncConfigDO config) {
        List<String> conflictFields = new ArrayList<>();
        
        // 获取需要检查的字段列表
        Set<String> fieldsToCheck = getFieldsToCheck(config);
        
        for (String field : fieldsToCheck) {
            Object sourceValue = sourceRecord.get(field);
            Object targetValue = targetRecord.get(field);
            
            if (!Objects.equals(sourceValue, targetValue)) {
                conflictFields.add(field);
            }
        }
        
        return conflictFields;
    }

    /**
     * 更新计数
     */
    private void updateCounts(Map<String, Object> operationResult, Map<String, Object> totalResult) {
        Integer successCount = (Integer) operationResult.get("successCount");
        Integer failedCount = (Integer) operationResult.get("failedCount");
        @SuppressWarnings("unchecked")
        List<String> errors = (List<String>) operationResult.get("errors");
        
        if (successCount != null) {
            totalResult.put("successCount", (Integer) totalResult.getOrDefault("successCount", 0) + successCount);
        }
        if (failedCount != null) {
            totalResult.put("failedCount", (Integer) totalResult.getOrDefault("failedCount", 0) + failedCount);
        }
        if (CollUtil.isNotEmpty(errors)) {
            @SuppressWarnings("unchecked")
            List<String> totalErrors = (List<String>) totalResult.getOrDefault("errors", new ArrayList<>());
            totalErrors.addAll(errors);
            totalResult.put("errors", totalErrors);
        }
    }

    /**
     * 获取变更类型字段
     */
    private String getChangeTypeField(DataSyncConfigDO config) {
        // 从配置中获取变更类型字段，默认为 "changeType"
        return "changeType";
    }

    /**
     * 获取主键字段
     */
    private String getPrimaryKey(DataSyncConfigDO config) {
        // 从配置中获取主键字段，默认为 "id"
        return "id";
    }

    /**
     * 获取时间戳字段
     */
    private String getTimestampField(DataSyncConfigDO config) {
        // 从配置中获取时间戳字段，默认为 "updateTime"
        return "updateTime";
    }

    /**
     * 获取需要检查的字段列表
     */
    private Set<String> getFieldsToCheck(DataSyncConfigDO config) {
        // 从配置中获取需要检查冲突的字段列表
        // 这里简化处理，实际应该从配置中读取
        return new HashSet<>(Arrays.asList("name", "value", "status", "description"));
    }
}
