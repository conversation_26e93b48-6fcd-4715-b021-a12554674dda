package top.continew.admin.accounting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import top.continew.admin.accounting.model.entity.TransactionDO;
import top.continew.admin.accounting.model.resp.TransactionListResp;
import top.continew.admin.accounting.model.resp.TransactionStatisticsResp;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 账单 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface TransactionMapper extends BaseMapper<TransactionDO> {

    /**
     * 获取用户在群组中的账单列表
     *
     * @param groupId 群组ID
     * @param userId  用户ID
     * @return 账单列表
     */
    @Select("""
        SELECT t.*, g.name as group_name, u.nickname as create_user_name,
               CASE WHEN ts.transaction_id IS NOT NULL THEN true ELSE false END as has_split,
               COALESCE(split_count.cnt, 0) as split_participant_count
        FROM acc_transaction t
        LEFT JOIN acc_group g ON t.group_id = g.id
        LEFT JOIN sys_user u ON t.create_user = u.id
        LEFT JOIN (SELECT DISTINCT transaction_id FROM acc_transaction_split) ts ON t.id = ts.transaction_id
        LEFT JOIN (SELECT transaction_id, COUNT(*) as cnt FROM acc_transaction_split GROUP BY transaction_id) split_count ON t.id = split_count.transaction_id
        WHERE t.group_id = #{groupId} AND t.create_user = #{userId} AND t.status = 1
        ORDER BY t.transaction_date DESC, t.create_time DESC
        """)
    List<TransactionListResp> selectUserTransactions(@Param("groupId") Long groupId, @Param("userId") Long userId);

    /**
     * 计算用户在群组中的余额
     *
     * @param groupId  群组ID
     * @param userId   用户ID
     * @param currency 币种
     * @return 余额
     */
    @Select("""
        SELECT COALESCE(
            SUM(CASE WHEN type = 'INCOME' THEN amount ELSE -amount END), 0
        ) as balance
        FROM acc_transaction 
        WHERE group_id = #{groupId} AND create_user = #{userId} 
        AND currency = #{currency} AND status = 1
        """)
    BigDecimal calculateUserBalance(@Param("groupId") Long groupId, @Param("userId") Long userId, @Param("currency") String currency);

    /**
     * 获取群组统计信息
     *
     * @param groupId   群组ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 统计信息
     */
    @Select("""
        SELECT 
            COALESCE(SUM(CASE WHEN type = 'INCOME' THEN amount ELSE 0 END), 0) as total_income,
            COALESCE(SUM(CASE WHEN type = 'EXPENSE' THEN amount ELSE 0 END), 0) as total_expense,
            COUNT(*) as transaction_count,
            COALESCE(AVG(amount), 0) as average_amount
        FROM acc_transaction 
        WHERE group_id = #{groupId} AND status = 1
        AND DATE(transaction_date) BETWEEN #{startDate} AND #{endDate}
        """)
    TransactionStatisticsResp.CurrencyStatistics selectGroupStatistics(
        @Param("groupId") Long groupId, 
        @Param("startDate") LocalDate startDate, 
        @Param("endDate") LocalDate endDate
    );

    /**
     * 获取分类统计
     *
     * @param groupId   群组ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 分类统计列表
     */
    @Select("""
        SELECT 
            category,
            SUM(amount) as total_amount,
            COUNT(*) as transaction_count
        FROM acc_transaction 
        WHERE group_id = #{groupId} AND status = 1
        AND DATE(transaction_date) BETWEEN #{startDate} AND #{endDate}
        AND category IS NOT NULL AND category != ''
        GROUP BY category
        ORDER BY total_amount DESC
        """)
    List<TransactionStatisticsResp.CategoryStatistics> selectCategoryStatistics(
        @Param("groupId") Long groupId, 
        @Param("startDate") LocalDate startDate, 
        @Param("endDate") LocalDate endDate
    );

    /**
     * 获取日期统计
     *
     * @param groupId   群组ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 日期统计列表
     */
    @Select("""
        SELECT 
            DATE(transaction_date) as date,
            COALESCE(SUM(CASE WHEN type = 'INCOME' THEN amount ELSE 0 END), 0) as income,
            COALESCE(SUM(CASE WHEN type = 'EXPENSE' THEN amount ELSE 0 END), 0) as expense,
            COUNT(*) as transaction_count
        FROM acc_transaction 
        WHERE group_id = #{groupId} AND status = 1
        AND DATE(transaction_date) BETWEEN #{startDate} AND #{endDate}
        GROUP BY DATE(transaction_date)
        ORDER BY date
        """)
    List<TransactionStatisticsResp.DateStatistics> selectDateStatistics(
        @Param("groupId") Long groupId, 
        @Param("startDate") LocalDate startDate, 
        @Param("endDate") LocalDate endDate
    );

    /**
     * 获取用户统计
     *
     * @param groupId   群组ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 用户统计列表
     */
    @Select("""
        SELECT 
            t.create_user as user_id,
            u.nickname as user_name,
            COALESCE(SUM(CASE WHEN t.type = 'INCOME' THEN t.amount ELSE 0 END), 0) as total_income,
            COALESCE(SUM(CASE WHEN t.type = 'EXPENSE' THEN t.amount ELSE 0 END), 0) as total_expense,
            COUNT(*) as transaction_count
        FROM acc_transaction t
        LEFT JOIN sys_user u ON t.create_user = u.id
        WHERE t.group_id = #{groupId} AND t.status = 1
        AND DATE(t.transaction_date) BETWEEN #{startDate} AND #{endDate}
        GROUP BY t.create_user, u.nickname
        ORDER BY total_expense DESC
        """)
    List<TransactionStatisticsResp.UserStatistics> selectUserStatistics(
        @Param("groupId") Long groupId, 
        @Param("startDate") LocalDate startDate, 
        @Param("endDate") LocalDate endDate
    );

    /**
     * 检查用户是否有权限操作账单
     *
     * @param transactionId 账单ID
     * @param userId        用户ID
     * @return 是否有权限
     */
    @Select("""
        SELECT COUNT(*) > 0 FROM acc_transaction t
        INNER JOIN acc_group_member gm ON t.group_id = gm.group_id
        WHERE t.id = #{transactionId} AND gm.user_id = #{userId} 
        AND t.status = 1 AND gm.status = 1
        AND (t.create_user = #{userId} OR gm.role IN ('OWNER', 'ACCOUNTANT'))
        """)
    boolean hasPermission(@Param("transactionId") Long transactionId, @Param("userId") Long userId);
}
