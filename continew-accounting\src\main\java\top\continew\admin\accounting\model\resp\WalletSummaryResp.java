package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 钱包汇总响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "钱包汇总响应")
public class WalletSummaryResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 群组名称
     */
    @Schema(description = "群组名称", example = "我的记账群")
    private String groupName;

    /**
     * 钱包总数
     */
    @Schema(description = "钱包总数", example = "3")
    private Integer totalWallets;

    /**
     * 总余额（按币种分组）
     */
    @Schema(description = "总余额（按币种分组）")
    private Map<String, BigDecimal> totalBalances;

    /**
     * 总冻结金额（按币种分组）
     */
    @Schema(description = "总冻结金额（按币种分组）")
    private Map<String, BigDecimal> totalFrozenAmounts;

    /**
     * 总可用余额（按币种分组）
     */
    @Schema(description = "总可用余额（按币种分组）")
    private Map<String, BigDecimal> totalAvailableBalances;

    /**
     * 钱包详情列表
     */
    @Schema(description = "钱包详情列表")
    private List<WalletSummaryItem> wallets;

    /**
     * 钱包汇总项
     */
    @Data
    @Schema(description = "钱包汇总项")
    public static class WalletSummaryItem implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 钱包ID
         */
        @Schema(description = "钱包ID", example = "1")
        private Long id;

        /**
         * 币种
         */
        @Schema(description = "币种", example = "CNY")
        private String currency;

        /**
         * 余额
         */
        @Schema(description = "余额", example = "1000.00")
        private BigDecimal balance;

        /**
         * 冻结金额
         */
        @Schema(description = "冻结金额", example = "100.00")
        private BigDecimal frozenAmount;

        /**
         * 可用余额
         */
        @Schema(description = "可用余额", example = "900.00")
        private BigDecimal availableBalance;

        /**
         * 最后更新时间
         */
        @Schema(description = "最后更新时间", example = "2025-01-01T12:00:00")
        private java.time.LocalDateTime lastUpdateTime;
    }
}
