package top.continew.admin.accounting.controller;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.accounting.model.query.DataSyncConfigQuery;
import top.continew.admin.accounting.model.query.DataSyncLogQuery;
import top.continew.admin.accounting.model.req.DataSyncConfigCreateReq;
import top.continew.admin.accounting.model.req.DataSyncConfigUpdateReq;
import top.continew.admin.accounting.model.req.DataSyncExecuteReq;
import top.continew.admin.accounting.model.resp.DataSyncConfigDetailResp;
import top.continew.admin.accounting.model.resp.DataSyncConfigListResp;
import top.continew.admin.accounting.model.resp.DataSyncLogResp;
import top.continew.admin.accounting.model.resp.DataSyncResultResp;
import top.continew.admin.accounting.service.DataSyncService;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.controller.BaseController;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.web.model.R;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 数据同步管理 API
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "数据同步管理 API")
@RestController
@RequiredArgsConstructor
@Validated
@CrudRequestMapping(value = "/accounting/data-sync", api = "数据同步配置")
public class DataSyncController extends BaseController<DataSyncService, DataSyncConfigListResp, DataSyncConfigDetailResp, DataSyncConfigQuery, DataSyncConfigCreateReq, DataSyncConfigUpdateReq> {

    private final DataSyncService dataSyncService;

    // ==================== 同步执行 ====================

    @Operation(summary = "执行同步", description = "执行数据同步操作")
    @PostMapping("/{id}/execute")
    public R<DataSyncResultResp> executeSync(
            @Parameter(description = "配置ID") @PathVariable Long id,
            @Parameter(description = "同步执行请求") @RequestBody @Valid DataSyncExecuteReq req) {
        DataSyncResultResp result = dataSyncService.executeSync(id, req);
        return R.ok(result);
    }

    @Operation(summary = "异步执行同步", description = "异步执行数据同步操作")
    @PostMapping("/{id}/execute-async")
    public R<String> executeSyncAsync(
            @Parameter(description = "配置ID") @PathVariable Long id,
            @Parameter(description = "同步执行请求") @RequestBody @Valid DataSyncExecuteReq req) {
        CompletableFuture<DataSyncResultResp> future = dataSyncService.executeSyncAsync(id, req);
        return R.ok("同步任务已启动，任务ID: " + future.toString());
    }

    @Operation(summary = "停止同步", description = "停止正在执行的同步任务")
    @PostMapping("/{id}/stop")
    public R<Void> stopSync(@Parameter(description = "配置ID") @PathVariable Long id) {
        dataSyncService.stopSync(id);
        return R.ok();
    }

    @Operation(summary = "获取同步进度", description = "获取同步任务执行进度")
    @GetMapping("/{id}/progress")
    public R<Map<String, Object>> getSyncProgress(@Parameter(description = "配置ID") @PathVariable Long id) {
        Map<String, Object> progress = dataSyncService.getSyncProgress(id);
        return R.ok(progress);
    }

    // ==================== 增量同步 ====================

    @Operation(summary = "执行增量同步", description = "执行增量数据同步")
    @PostMapping("/{id}/incremental")
    public R<DataSyncResultResp> executeIncrementalSync(
            @Parameter(description = "配置ID") @PathVariable Long id,
            @Parameter(description = "同步执行请求") @RequestBody @Valid DataSyncExecuteReq req) {
        DataSyncResultResp result = dataSyncService.executeIncrementalSync(id, req);
        return R.ok(result);
    }

    @Operation(summary = "获取增量同步状态", description = "获取增量同步状态信息")
    @GetMapping("/{id}/incremental/status")
    public R<Map<String, Object>> getIncrementalSyncStatus(@Parameter(description = "配置ID") @PathVariable Long id) {
        Map<String, Object> status = dataSyncService.getIncrementalSyncStatus(id);
        return R.ok(status);
    }

    // ==================== 冲突管理 ====================

    @Operation(summary = "获取冲突列表", description = "获取数据同步冲突列表")
    @GetMapping("/{id}/conflicts")
    public R<List<Map<String, Object>>> getConflicts(@Parameter(description = "配置ID") @PathVariable Long id) {
        List<Map<String, Object>> conflicts = dataSyncService.getConflicts(id);
        return R.ok(conflicts);
    }

    @Operation(summary = "解决冲突", description = "解决数据同步冲突")
    @PostMapping("/{id}/conflicts/{conflictId}/resolve")
    public R<Void> resolveConflict(
            @Parameter(description = "配置ID") @PathVariable Long id,
            @Parameter(description = "冲突ID") @PathVariable String conflictId,
            @Parameter(description = "解决方案") @RequestBody Map<String, Object> resolution) {
        dataSyncService.resolveConflict(id, conflictId, resolution);
        return R.ok();
    }

    @Operation(summary = "批量解决冲突", description = "批量解决数据同步冲突")
    @PostMapping("/{id}/conflicts/batch-resolve")
    public R<Void> batchResolveConflicts(
            @Parameter(description = "配置ID") @PathVariable Long id,
            @Parameter(description = "冲突解决列表") @RequestBody List<Map<String, Object>> resolutions) {
        dataSyncService.batchResolveConflicts(id, resolutions);
        return R.ok();
    }

    // ==================== 字段映射 ====================

    @Operation(summary = "获取字段映射建议", description = "获取字段映射建议")
    @GetMapping("/{id}/field-mapping/suggestions")
    public R<List<Map<String, Object>>> getFieldMappingSuggestions(@Parameter(description = "配置ID") @PathVariable Long id) {
        List<Map<String, Object>> suggestions = dataSyncService.getFieldMappingSuggestions(id);
        return R.ok(suggestions);
    }

    @Operation(summary = "验证字段映射", description = "验证字段映射配置")
    @PostMapping("/{id}/field-mapping/validate")
    public R<Map<String, Object>> validateFieldMapping(
            @Parameter(description = "配置ID") @PathVariable Long id,
            @Parameter(description = "字段映射") @RequestBody Map<String, Object> fieldMapping) {
        Map<String, Object> validation = dataSyncService.validateFieldMapping(id, fieldMapping);
        return R.ok(validation);
    }

    // ==================== 同步日志 ====================

    @Operation(summary = "分页查询同步日志", description = "分页查询同步日志")
    @GetMapping("/{id}/logs")
    public R<PageResp<DataSyncLogResp>> pageLogs(
            @Parameter(description = "配置ID") @PathVariable Long id,
            @Parameter(description = "查询条件") DataSyncLogQuery query,
            @Parameter(description = "分页参数") PageQuery pageQuery) {
        query.setConfigId(id);
        PageResp<DataSyncLogResp> page = dataSyncService.pageLogs(query, pageQuery);
        return R.ok(page);
    }

    @Operation(summary = "获取日志详情", description = "获取同步日志详情")
    @GetMapping("/logs/{logId}")
    public R<DataSyncLogResp> getLogDetail(@Parameter(description = "日志ID") @PathVariable Long logId) {
        DataSyncLogResp log = dataSyncService.getLogDetail(logId);
        return R.ok(log);
    }

    @Operation(summary = "清理日志", description = "清理过期的同步日志")
    @DeleteMapping("/{id}/logs/cleanup")
    public R<Integer> cleanupLogs(
            @Parameter(description = "配置ID") @PathVariable Long id,
            @Parameter(description = "保留天数") @RequestParam(defaultValue = "30") Integer retentionDays) {
        Integer deletedCount = dataSyncService.cleanupLogs(id, retentionDays);
        return R.ok(deletedCount);
    }

    // ==================== 监控与统计 ====================

    @Operation(summary = "获取配置统计", description = "获取配置统计信息")
    @GetMapping("/statistics/config")
    public R<Map<String, Object>> getConfigStatistics(
            @Parameter(description = "群组ID") @RequestParam(required = false) Long groupId) {
        Map<String, Object> statistics = dataSyncService.getConfigStatistics(groupId);
        return R.ok(statistics);
    }

    @Operation(summary = "获取同步统计", description = "获取同步统计信息")
    @GetMapping("/{id}/statistics/sync")
    public R<Map<String, Object>> getSyncStatistics(
            @Parameter(description = "配置ID") @PathVariable Long id,
            @Parameter(description = "开始日期") @RequestParam(required = false) String startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) String endDate) {
        Map<String, Object> statistics = dataSyncService.getSyncStatistics(id, startDate, endDate);
        return R.ok(statistics);
    }

    @Operation(summary = "获取性能分析", description = "获取性能分析数据")
    @GetMapping("/statistics/performance")
    public R<List<Map<String, Object>>> getPerformanceAnalysis(
            @Parameter(description = "群组ID") @RequestParam(required = false) Long groupId,
            @Parameter(description = "状态") @RequestParam(required = false) String status,
            @Parameter(description = "源类型") @RequestParam(required = false) String sourceType,
            @Parameter(description = "目标类型") @RequestParam(required = false) String targetType,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "lastSyncTime") String orderBy) {
        List<Map<String, Object>> analysis = dataSyncService.getPerformanceAnalysis(groupId, status, sourceType, targetType, orderBy);
        return R.ok(analysis);
    }

    @Operation(summary = "获取健康监控", description = "获取健康监控数据")
    @GetMapping("/monitoring/health")
    public R<List<Map<String, Object>>> getHealthMonitoring(
            @Parameter(description = "群组ID") @RequestParam(required = false) Long groupId,
            @Parameter(description = "健康状态") @RequestParam(required = false) String healthStatus) {
        List<Map<String, Object>> monitoring = dataSyncService.getHealthMonitoring(groupId, healthStatus);
        return R.ok(monitoring);
    }

    @Operation(summary = "获取同步趋势", description = "获取同步趋势数据")
    @GetMapping("/statistics/trends")
    public R<List<Map<String, Object>>> getSyncTrends(
            @Parameter(description = "群组ID") @RequestParam(required = false) Long groupId,
            @Parameter(description = "开始日期") @RequestParam(required = false) String startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) String endDate,
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "30") Integer limit) {
        List<Map<String, Object>> trends = dataSyncService.getSyncTrends(groupId, startDate, endDate, limit);
        return R.ok(trends);
    }

    // ==================== 配置管理 ====================

    @Operation(summary = "测试连接", description = "测试数据源连接")
    @PostMapping("/test-connection")
    public R<Map<String, Object>> testConnection(
            @Parameter(description = "数据源类型") @RequestParam String sourceType,
            @Parameter(description = "连接配置") @RequestBody Map<String, Object> config) {
        Map<String, Object> result = dataSyncService.testConnection(sourceType, config);
        return R.ok(result);
    }

    @Operation(summary = "获取配置模板", description = "获取配置模板")
    @GetMapping("/templates")
    public R<List<Map<String, Object>>> getConfigTemplates(
            @Parameter(description = "群组ID") @RequestParam(required = false) Long groupId,
            @Parameter(description = "最小使用次数") @RequestParam(defaultValue = "3") Integer minUsageCount) {
        List<Map<String, Object>> templates = dataSyncService.getConfigTemplates(groupId, minUsageCount);
        return R.ok(templates);
    }

    @Operation(summary = "复制配置", description = "复制现有配置")
    @PostMapping("/{id}/copy")
    public R<DataSyncConfigDetailResp> copyConfig(
            @Parameter(description = "配置ID") @PathVariable Long id,
            @Parameter(description = "新配置名称") @RequestParam String newName) {
        DataSyncConfigDetailResp config = dataSyncService.copyConfig(id, newName);
        return R.ok(config);
    }

    @Operation(summary = "批量操作", description = "批量启用/禁用配置")
    @PostMapping("/batch-operation")
    public R<Void> batchOperation(
            @Parameter(description = "配置ID列表") @RequestParam List<Long> configIds,
            @Parameter(description = "操作类型") @RequestParam String operation) {
        dataSyncService.batchOperation(configIds, operation);
        return R.ok();
    }

    // ==================== 数据源管理 ====================

    @Operation(summary = "获取数据源类型", description = "获取支持的数据源类型列表")
    @GetMapping("/data-source-types")
    public R<List<Map<String, Object>>> getDataSourceTypes() {
        List<Map<String, Object>> types = dataSyncService.getDataSourceTypes();
        return R.ok(types);
    }

    @Operation(summary = "获取数据源结构", description = "获取数据源结构信息")
    @PostMapping("/data-source/structure")
    public R<Map<String, Object>> getDataSourceStructure(
            @Parameter(description = "数据源类型") @RequestParam String sourceType,
            @Parameter(description = "连接配置") @RequestBody Map<String, Object> config) {
        Map<String, Object> structure = dataSyncService.getDataSourceStructure(sourceType, config);
        return R.ok(structure);
    }

    @Operation(summary = "预览数据", description = "预览数据源数据")
    @PostMapping("/data-source/preview")
    public R<List<Map<String, Object>>> previewData(
            @Parameter(description = "数据源类型") @RequestParam String sourceType,
            @Parameter(description = "连接配置") @RequestBody Map<String, Object> config,
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "10") Integer limit) {
        List<Map<String, Object>> data = dataSyncService.previewData(sourceType, config, limit);
        return R.ok(data);
    }

}
