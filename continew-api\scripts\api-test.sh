#!/bin/bash

# ContiNew Admin 群组记账机器人 API 测试脚本
# 使用方法: ./api-test.sh [BASE_URL] [API_KEY] [API_SECRET]

# 默认配置
BASE_URL=${1:-"http://localhost:8080"}
API_KEY=${2:-""}
API_SECRET=${3:-""}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# API 调用函数
api_call() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    log_info "测试: $description"
    log_info "请求: $method $endpoint"
    
    local curl_cmd="curl -s -w '\n%{http_code}' -X $method"
    
    # 添加认证头
    if [ -n "$API_KEY" ]; then
        curl_cmd="$curl_cmd -H 'X-API-Key: $API_KEY'"
    fi
    
    if [ -n "$API_SECRET" ]; then
        curl_cmd="$curl_cmd -H 'X-API-Secret: $API_SECRET'"
    fi
    
    # 添加Content-Type
    if [ "$method" = "POST" ] || [ "$method" = "PUT" ]; then
        curl_cmd="$curl_cmd -H 'Content-Type: application/json'"
    fi
    
    # 添加请求体
    if [ -n "$data" ]; then
        curl_cmd="$curl_cmd -d '$data'"
    fi
    
    # 执行请求
    local response=$(eval "$curl_cmd '$BASE_URL$endpoint'")
    local http_code=$(echo "$response" | tail -n1)
    local body=$(echo "$response" | head -n -1)
    
    # 检查响应
    if [ "$http_code" -ge 200 ] && [ "$http_code" -lt 300 ]; then
        log_success "HTTP $http_code - 请求成功"
        echo "$body" | jq . 2>/dev/null || echo "$body"
    else
        log_error "HTTP $http_code - 请求失败"
        echo "$body" | jq . 2>/dev/null || echo "$body"
    fi
    
    echo "----------------------------------------"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v curl &> /dev/null; then
        log_error "curl 未安装，请先安装 curl"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_warning "jq 未安装，JSON 响应将不会格式化"
    fi
    
    log_success "依赖检查完成"
    echo "----------------------------------------"
}

# 测试连接
test_connection() {
    log_info "测试 API 连接..."
    api_call "GET" "/api/v1/auth/test" "" "测试连接"
}

# 测试认证管理
test_auth() {
    log_info "测试认证管理..."
    
    # 获取权限范围
    api_call "GET" "/api/v1/auth/scopes" "" "获取权限范围"
    
    # 创建 API 密钥（需要登录）
    local api_key_data='{
        "appName": "测试应用",
        "description": "API 测试用密钥",
        "scopes": ["groups:read", "transactions:read"],
        "rateLimit": 100
    }'
    api_call "POST" "/api/v1/auth/api-keys" "$api_key_data" "创建API密钥"
}

# 测试群组管理
test_groups() {
    log_info "测试群组管理..."
    
    # 获取群组列表
    api_call "GET" "/api/v1/groups" "" "获取群组列表"
    
    # 创建群组
    local group_data='{
        "name": "测试群组",
        "description": "API测试用群组",
        "currency": "CNY",
        "settings": {
            "isPrivate": false,
            "allowInvite": true,
            "autoApprove": true
        }
    }'
    api_call "POST" "/api/v1/groups" "$group_data" "创建群组"
    
    # 获取群组详情（假设群组ID为1）
    api_call "GET" "/api/v1/groups/1" "" "获取群组详情"
    
    # 获取群组统计
    api_call "GET" "/api/v1/groups/1/statistics" "" "获取群组统计"
}

# 测试交易管理
test_transactions() {
    log_info "测试交易管理..."
    
    # 获取交易列表
    api_call "GET" "/api/v1/transactions?groupId=1" "" "获取交易列表"
    
    # 创建交易
    local transaction_data='{
        "groupId": 1,
        "type": "EXPENSE",
        "amount": 100.50,
        "description": "API测试交易",
        "categoryId": 1,
        "walletId": 1,
        "tags": ["测试", "API"]
    }'
    api_call "POST" "/api/v1/transactions" "$transaction_data" "创建交易"
    
    # 获取交易统计
    api_call "GET" "/api/v1/transactions/statistics?groupId=1" "" "获取交易统计"
    
    # 搜索交易
    api_call "GET" "/api/v1/transactions/search?groupId=1&keyword=测试" "" "搜索交易"
}

# 测试批量操作
test_batch_operations() {
    log_info "测试批量操作..."
    
    # 批量创建交易
    local batch_data='[
        {
            "groupId": 1,
            "type": "EXPENSE",
            "amount": 50.00,
            "description": "批量测试1",
            "categoryId": 1,
            "walletId": 1
        },
        {
            "groupId": 1,
            "type": "INCOME",
            "amount": 200.00,
            "description": "批量测试2",
            "categoryId": 2,
            "walletId": 1
        }
    ]'
    api_call "POST" "/api/v1/transactions/batch" "$batch_data" "批量创建交易"
}

# 测试错误处理
test_error_handling() {
    log_info "测试错误处理..."
    
    # 测试无效的群组ID
    api_call "GET" "/api/v1/groups/99999" "" "获取不存在的群组"
    
    # 测试无效的请求数据
    local invalid_data='{"invalid": "data"}'
    api_call "POST" "/api/v1/transactions" "$invalid_data" "创建无效交易"
}

# 主函数
main() {
    echo "========================================"
    echo "ContiNew Admin API 测试脚本"
    echo "========================================"
    echo "Base URL: $BASE_URL"
    echo "API Key: ${API_KEY:0:10}..."
    echo "========================================"
    
    check_dependencies
    test_connection
    
    if [ -n "$API_KEY" ]; then
        test_auth
        test_groups
        test_transactions
        test_batch_operations
        test_error_handling
    else
        log_warning "未提供 API 密钥，跳过需要认证的测试"
    fi
    
    echo "========================================"
    log_info "测试完成"
    echo "========================================"
}

# 显示帮助信息
show_help() {
    echo "ContiNew Admin API 测试脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [BASE_URL] [API_KEY] [API_SECRET]"
    echo ""
    echo "参数:"
    echo "  BASE_URL    API 基础URL (默认: http://localhost:8080)"
    echo "  API_KEY     API 密钥"
    echo "  API_SECRET  API 密钥"
    echo ""
    echo "示例:"
    echo "  $0"
    echo "  $0 https://api.continew.top"
    echo "  $0 https://api.continew.top your_api_key your_api_secret"
    echo ""
    echo "环境变量:"
    echo "  CONTINEW_API_URL     API 基础URL"
    echo "  CONTINEW_API_KEY     API 密钥"
    echo "  CONTINEW_API_SECRET  API 密钥"
}

# 检查参数
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

# 从环境变量获取配置
if [ -z "$BASE_URL" ] && [ -n "$CONTINEW_API_URL" ]; then
    BASE_URL="$CONTINEW_API_URL"
fi

if [ -z "$API_KEY" ] && [ -n "$CONTINEW_API_KEY" ]; then
    API_KEY="$CONTINEW_API_KEY"
fi

if [ -z "$API_SECRET" ] && [ -n "$CONTINEW_API_SECRET" ]; then
    API_SECRET="$CONTINEW_API_SECRET"
fi

# 运行测试
main
