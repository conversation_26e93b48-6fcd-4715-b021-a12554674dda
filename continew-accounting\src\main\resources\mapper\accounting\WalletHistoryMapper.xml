<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.accounting.mapper.WalletHistoryMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="top.continew.admin.accounting.model.entity.WalletHistoryDO">
        <id column="id" property="id" />
        <result column="group_id" property="groupId" />
        <result column="wallet_id" property="walletId" />
        <result column="currency" property="currency" />
        <result column="operation_type" property="operationType" />
        <result column="amount" property="amount" />
        <result column="balance_before" property="balanceBefore" />
        <result column="balance_after" property="balanceAfter" />
        <result column="frozen_amount_before" property="frozenAmountBefore" />
        <result column="frozen_amount_after" property="frozenAmountAfter" />
        <result column="description" property="description" />
        <result column="operator_id" property="operatorId" />
        <result column="operate_time" property="operateTime" />
        <result column="business_id" property="businessId" />
        <result column="business_type" property="businessType" />
        <result column="remark" property="remark" />
        <result column="ip_address" property="ipAddress" />
        <result column="user_agent" property="userAgent" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_time" property="createTime" />
        <result column="update_user_id" property="updateUserId" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 查询钱包历史记录 -->
    <select id="selectWalletHistory" resultMap="BaseResultMap">
        SELECT * FROM acc_wallet_history
        WHERE group_id = #{groupId}
        <if test="currency != null and currency != ''">
            AND currency = #{currency}
        </if>
        ORDER BY operate_time DESC
        LIMIT #{limit}
    </select>

    <!-- 查询钱包历史记录（带操作人信息） -->
    <select id="selectWalletHistoryWithOperator" resultType="map">
        SELECT 
            h.*,
            u.nickname as operator_name,
            u.username as operator_username
        FROM acc_wallet_history h
        LEFT JOIN sys_user u ON h.operator_id = u.id
        WHERE h.group_id = #{groupId}
        <if test="currency != null and currency != ''">
            AND h.currency = #{currency}
        </if>
        ORDER BY h.operate_time DESC
        LIMIT #{limit}
    </select>

    <!-- 查询指定时间范围内的钱包历史记录 -->
    <select id="selectWalletHistoryByTimeRange" resultMap="BaseResultMap">
        SELECT * FROM acc_wallet_history
        WHERE group_id = #{groupId}
        <if test="currency != null and currency != ''">
            AND currency = #{currency}
        </if>
        AND operate_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY operate_time DESC
    </select>

    <!-- 查询指定操作类型的历史记录 -->
    <select id="selectWalletHistoryByOperationType" resultMap="BaseResultMap">
        SELECT * FROM acc_wallet_history
        WHERE group_id = #{groupId}
        <if test="currency != null and currency != ''">
            AND currency = #{currency}
        </if>
        AND operation_type = #{operationType}
        ORDER BY operate_time DESC
        LIMIT #{limit}
    </select>

    <!-- 查询指定业务的历史记录 -->
    <select id="selectWalletHistoryByBusiness" resultMap="BaseResultMap">
        SELECT * FROM acc_wallet_history
        WHERE business_id = #{businessId}
        AND business_type = #{businessType}
        ORDER BY operate_time DESC
    </select>

    <!-- 统计钱包操作次数 -->
    <select id="countWalletOperations" resultType="map">
        SELECT 
            COUNT(*) as total_operations,
            COUNT(CASE WHEN operation_type = 'INCOME' THEN 1 END) as income_count,
            COUNT(CASE WHEN operation_type = 'EXPENSE' THEN 1 END) as expense_count,
            COUNT(CASE WHEN operation_type = 'TRANSFER_IN' THEN 1 END) as transfer_in_count,
            COUNT(CASE WHEN operation_type = 'TRANSFER_OUT' THEN 1 END) as transfer_out_count,
            COUNT(CASE WHEN operation_type = 'FREEZE' THEN 1 END) as freeze_count,
            COUNT(CASE WHEN operation_type = 'UNFREEZE' THEN 1 END) as unfreeze_count,
            COUNT(CASE WHEN operation_type = 'RESET' THEN 1 END) as reset_count,
            COALESCE(SUM(CASE WHEN operation_type = 'INCOME' THEN amount END), 0) as total_income,
            COALESCE(SUM(CASE WHEN operation_type = 'EXPENSE' THEN amount END), 0) as total_expense,
            COUNT(DISTINCT operator_id) as unique_operators
        FROM acc_wallet_history
        WHERE group_id = #{groupId}
        <if test="currency != null and currency != ''">
            AND currency = #{currency}
        </if>
        AND operate_time BETWEEN #{startTime} AND #{endTime}
    </select>

    <!-- 查询钱包余额变化趋势 -->
    <select id="selectBalanceTrend" resultType="map">
        SELECT 
            DATE(operate_time) as date,
            MIN(balance_before) as min_balance,
            MAX(balance_after) as max_balance,
            COUNT(*) as operation_count,
            COALESCE(SUM(CASE WHEN operation_type = 'INCOME' THEN amount END), 0) as daily_income,
            COALESCE(SUM(CASE WHEN operation_type = 'EXPENSE' THEN amount END), 0) as daily_expense
        FROM acc_wallet_history
        WHERE group_id = #{groupId}
        <if test="currency != null and currency != ''">
            AND currency = #{currency}
        </if>
        AND operate_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY DATE(operate_time)
        ORDER BY date ASC
    </select>

    <!-- 删除过期的历史记录 -->
    <delete id="deleteExpiredHistory">
        DELETE FROM acc_wallet_history
        WHERE operate_time &lt; DATE_SUB(NOW(), INTERVAL #{retentionDays} DAY)
    </delete>

    <!-- 删除指定群组的历史记录 -->
    <delete id="deleteHistoryByGroupId">
        DELETE FROM acc_wallet_history
        WHERE group_id = #{groupId}
    </delete>

    <!-- 删除指定钱包的历史记录 -->
    <delete id="deleteHistoryByWalletId">
        DELETE FROM acc_wallet_history
        WHERE wallet_id = #{walletId}
    </delete>

    <!-- 查询最近的历史记录 -->
    <select id="selectLatestHistory" resultMap="BaseResultMap">
        SELECT * FROM acc_wallet_history
        WHERE group_id = #{groupId}
        <if test="currency != null and currency != ''">
            AND currency = #{currency}
        </if>
        ORDER BY operate_time DESC
        LIMIT 1
    </select>

    <!-- 批量插入历史记录 -->
    <insert id="batchInsertHistory">
        INSERT INTO acc_wallet_history (
            group_id, wallet_id, currency, operation_type, amount,
            balance_before, balance_after, frozen_amount_before, frozen_amount_after,
            description, operator_id, operate_time, business_id, business_type,
            remark, ip_address, user_agent, create_user_id, create_time
        ) VALUES
        <foreach collection="historyList" item="item" separator=",">
            (
                #{item.groupId}, #{item.walletId}, #{item.currency}, #{item.operationType}, #{item.amount},
                #{item.balanceBefore}, #{item.balanceAfter}, #{item.frozenAmountBefore}, #{item.frozenAmountAfter},
                #{item.description}, #{item.operatorId}, #{item.operateTime}, #{item.businessId}, #{item.businessType},
                #{item.remark}, #{item.ipAddress}, #{item.userAgent}, #{item.createUserId}, #{item.createTime}
            )
        </foreach>
    </insert>

    <!-- 查询操作人的历史记录 -->
    <select id="selectHistoryByOperator" resultMap="BaseResultMap">
        SELECT * FROM acc_wallet_history
        WHERE operator_id = #{operatorId}
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        ORDER BY operate_time DESC
        LIMIT #{limit}
    </select>

    <!-- 查询异常操作记录 -->
    <select id="selectAbnormalOperations" resultMap="BaseResultMap">
        SELECT * FROM acc_wallet_history
        WHERE group_id = #{groupId}
        AND operate_time BETWEEN #{startTime} AND #{endTime}
        AND (
            -- 大额操作（超过10000）
            ABS(amount) > 10000
            -- 或者短时间内频繁操作（同一操作人5分钟内超过10次操作）
            OR operator_id IN (
                SELECT operator_id 
                FROM acc_wallet_history h2 
                WHERE h2.group_id = #{groupId}
                AND h2.operate_time BETWEEN DATE_SUB(acc_wallet_history.operate_time, INTERVAL 5 MINUTE) 
                                        AND acc_wallet_history.operate_time
                GROUP BY h2.operator_id 
                HAVING COUNT(*) > 10
            )
            -- 或者余额异常变化（变化幅度超过50%）
            OR (
                balance_before > 0 
                AND ABS(balance_after - balance_before) / balance_before > 0.5
            )
        )
        ORDER BY operate_time DESC
    </select>

    <!-- 查询钱包历史记录统计信息 -->
    <select id="selectHistoryStatistics" resultType="map">
        SELECT 
            COUNT(*) as total_records,
            COUNT(DISTINCT operator_id) as unique_operators,
            COUNT(DISTINCT DATE(operate_time)) as active_days,
            MIN(operate_time) as first_operation,
            MAX(operate_time) as last_operation,
            COALESCE(SUM(CASE WHEN amount > 0 THEN amount END), 0) as total_positive_amount,
            COALESCE(SUM(CASE WHEN amount &lt; 0 THEN ABS(amount) END), 0) as total_negative_amount,
            AVG(CASE WHEN amount > 0 THEN amount END) as avg_positive_amount,
            AVG(CASE WHEN amount &lt; 0 THEN ABS(amount) END) as avg_negative_amount,
            MAX(CASE WHEN amount > 0 THEN amount END) as max_positive_amount,
            MAX(CASE WHEN amount &lt; 0 THEN ABS(amount) END) as max_negative_amount
        FROM acc_wallet_history
        WHERE group_id = #{groupId}
        <if test="currency != null and currency != ''">
            AND currency = #{currency}
        </if>
        <if test="startTime != null and endTime != null">
            AND operate_time BETWEEN #{startTime} AND #{endTime}
        </if>
    </select>

    <!-- 查询操作类型分布 -->
    <select id="selectOperationTypeDistribution" resultType="map">
        SELECT 
            operation_type,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (
                SELECT COUNT(*) FROM acc_wallet_history 
                WHERE group_id = #{groupId}
                <if test="currency != null and currency != ''">
                    AND currency = #{currency}
                </if>
            ), 2) as percentage,
            COALESCE(SUM(ABS(amount)), 0) as total_amount,
            AVG(ABS(amount)) as avg_amount
        FROM acc_wallet_history
        WHERE group_id = #{groupId}
        <if test="currency != null and currency != ''">
            AND currency = #{currency}
        </if>
        GROUP BY operation_type
        ORDER BY count DESC
    </select>

    <!-- 查询活跃操作人排行 -->
    <select id="selectActiveOperators" resultType="map">
        SELECT 
            h.operator_id,
            u.nickname as operator_name,
            u.username as operator_username,
            COUNT(*) as operation_count,
            COALESCE(SUM(CASE WHEN h.amount > 0 THEN h.amount END), 0) as total_positive_amount,
            COALESCE(SUM(CASE WHEN h.amount &lt; 0 THEN ABS(h.amount) END), 0) as total_negative_amount,
            MIN(h.operate_time) as first_operation,
            MAX(h.operate_time) as last_operation
        FROM acc_wallet_history h
        LEFT JOIN sys_user u ON h.operator_id = u.id
        WHERE h.group_id = #{groupId}
        <if test="currency != null and currency != ''">
            AND h.currency = #{currency}
        </if>
        <if test="startTime != null and endTime != null">
            AND h.operate_time BETWEEN #{startTime} AND #{endTime}
        </if>
        GROUP BY h.operator_id, u.nickname, u.username
        ORDER BY operation_count DESC
        LIMIT #{limit}
    </select>

    <!-- 查询每日操作统计 -->
    <select id="selectDailyOperationStats" resultType="map">
        SELECT 
            DATE(operate_time) as operation_date,
            COUNT(*) as total_operations,
            COUNT(DISTINCT operator_id) as unique_operators,
            COALESCE(SUM(CASE WHEN amount > 0 THEN amount END), 0) as daily_income,
            COALESCE(SUM(CASE WHEN amount &lt; 0 THEN ABS(amount) END), 0) as daily_expense,
            MIN(balance_after) as min_balance,
            MAX(balance_after) as max_balance
        FROM acc_wallet_history
        WHERE group_id = #{groupId}
        <if test="currency != null and currency != ''">
            AND currency = #{currency}
        </if>
        AND operate_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY DATE(operate_time)
        ORDER BY operation_date DESC
    </select>

</mapper>
