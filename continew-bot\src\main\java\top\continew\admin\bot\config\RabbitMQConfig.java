package top.continew.admin.bot.config;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import top.continew.admin.bot.common.BotMessageQueue;

/**
 * RabbitMQ配置
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Configuration
public class RabbitMQConfig {

    /**
     * 消息转换器
     */
    @Bean
    public MessageConverter messageConverter() {
        return new Jackson2JsonMessageConverter();
    }

    /**
     * RabbitTemplate配置
     */
    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setMessageConverter(messageConverter());
        template.setMandatory(true);
        template.setConfirmCallback((correlationData, ack, cause) -> {
            if (!ack) {
                System.err.println("消息发送失败: " + cause);
            }
        });
        template.setReturnsCallback(returned -> {
            System.err.println("消息被退回: " + returned.getMessage());
        });
        return template;
    }

    /**
     * 监听器容器工厂
     */
    @Bean
    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory(ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setMessageConverter(messageConverter());
        factory.setConcurrentConsumers(3);
        factory.setMaxConcurrentConsumers(10);
        factory.setPrefetchCount(1);
        return factory;
    }

    /**
     * 机器人交换机
     */
    @Bean
    public TopicExchange botExchange() {
        return ExchangeBuilder
                .topicExchange(BotMessageQueue.EXCHANGE_BOT)
                .durable(true)
                .build();
    }

    /**
     * 机器人消息队列
     */
    @Bean
    public Queue botMessageQueue() {
        return QueueBuilder
                .durable(BotMessageQueue.QUEUE_BOT_MESSAGE)
                .withArgument("x-message-ttl", 300000) // 5分钟TTL
                .withArgument("x-max-length", 10000) // 最大长度
                .build();
    }

    /**
     * 机器人通知队列
     */
    @Bean
    public Queue botNotificationQueue() {
        return QueueBuilder
                .durable(BotMessageQueue.QUEUE_BOT_NOTIFICATION)
                .withArgument("x-message-ttl", 600000) // 10分钟TTL
                .withArgument("x-max-length", 5000) // 最大长度
                .build();
    }

    /**
     * 机器人命令队列
     */
    @Bean
    public Queue botCommandQueue() {
        return QueueBuilder
                .durable(BotMessageQueue.QUEUE_BOT_COMMAND)
                .withArgument("x-message-ttl", 180000) // 3分钟TTL
                .withArgument("x-max-length", 20000) // 最大长度
                .build();
    }

    /**
     * 绑定消息队列到交换机
     */
    @Bean
    public Binding botMessageBinding() {
        return BindingBuilder
                .bind(botMessageQueue())
                .to(botExchange())
                .with(BotMessageQueue.ROUTING_KEY_MESSAGE);
    }

    /**
     * 绑定通知队列到交换机
     */
    @Bean
    public Binding botNotificationBinding() {
        return BindingBuilder
                .bind(botNotificationQueue())
                .to(botExchange())
                .with(BotMessageQueue.ROUTING_KEY_NOTIFICATION);
    }

    /**
     * 绑定命令队列到交换机
     */
    @Bean
    public Binding botCommandBinding() {
        return BindingBuilder
                .bind(botCommandQueue())
                .to(botExchange())
                .with(BotMessageQueue.ROUTING_KEY_COMMAND);
    }

    /**
     * 死信交换机
     */
    @Bean
    public DirectExchange deadLetterExchange() {
        return ExchangeBuilder
                .directExchange("bot.dlx")
                .durable(true)
                .build();
    }

    /**
     * 死信队列
     */
    @Bean
    public Queue deadLetterQueue() {
        return QueueBuilder
                .durable("bot.dlq")
                .build();
    }

    /**
     * 绑定死信队列
     */
    @Bean
    public Binding deadLetterBinding() {
        return BindingBuilder
                .bind(deadLetterQueue())
                .to(deadLetterExchange())
                .with("dead");
    }
}
