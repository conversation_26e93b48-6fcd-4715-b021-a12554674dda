package top.continew.admin.accounting.service.sync.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.continew.admin.accounting.service.sync.DataSourceAdapter;
import top.continew.starter.core.exception.BusinessException;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * API适配器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ApiAdapter implements DataSourceAdapter {

    @Override
    public String getAdapterType() {
        return "API";
    }

    @Override
    public Map<String, Object> testConnection(Map<String, Object> config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String url = (String) config.get("url");
            String method = (String) config.getOrDefault("method", "GET");
            
            if (StrUtil.isBlank(url)) {
                throw new BusinessException("API地址不能为空");
            }
            
            // 构建测试请求
            HttpRequest request = buildHttpRequest(url, method, config, null);
            
            // 发送请求
            HttpResponse response = request.execute();
            
            if (response.isOk()) {
                result.put("success", true);
                result.put("message", "API连接测试成功");
                result.put("statusCode", response.getStatus());
                result.put("responseTime", response.getTime());
            } else {
                result.put("success", false);
                result.put("message", "API连接测试失败: HTTP " + response.getStatus());
                result.put("statusCode", response.getStatus());
            }
            
            result.put("testTime", LocalDateTime.now());
            
        } catch (Exception e) {
            log.error("API连接测试失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "API连接测试失败: " + e.getMessage());
            result.put("testTime", LocalDateTime.now());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> validateConfig(Map<String, Object> config) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        
        String url = (String) config.get("url");
        String method = (String) config.get("method");
        
        if (StrUtil.isBlank(url)) {
            errors.add("API地址不能为空");
        } else if (!isValidUrl(url)) {
            errors.add("API地址格式不正确");
        }
        
        if (StrUtil.isBlank(method)) {
            errors.add("请求方法不能为空");
        } else if (!isValidHttpMethod(method)) {
            errors.add("不支持的请求方法: " + method);
        }
        
        result.put("valid", errors.isEmpty());
        result.put("errors", errors);
        result.put("validateTime", LocalDateTime.now());
        
        return result;
    }

    @Override
    public Map<String, Object> getDataStructure(Map<String, Object> config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String url = (String) config.get("url");
            String method = (String) config.getOrDefault("method", "GET");
            
            // 发送请求获取样本数据
            HttpRequest request = buildHttpRequest(url, method, config, null);
            HttpResponse response = request.execute();
            
            if (response.isOk()) {
                String responseBody = response.body();
                
                // 尝试解析JSON响应
                if (JSONUtil.isTypeJSON(responseBody)) {
                    Object jsonData = JSONUtil.parse(responseBody);
                    
                    if (jsonData instanceof List) {
                        @SuppressWarnings("unchecked")
                        List<Map<String, Object>> dataList = (List<Map<String, Object>>) jsonData;
                        if (!dataList.isEmpty()) {
                            Map<String, Object> sampleRecord = dataList.get(0);
                            result.put("fields", analyzeFields(sampleRecord));
                        }
                    } else if (jsonData instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> dataMap = (Map<String, Object>) jsonData;
                        
                        // 检查是否有data字段包含数组
                        if (dataMap.containsKey("data") && dataMap.get("data") instanceof List) {
                            @SuppressWarnings("unchecked")
                            List<Map<String, Object>> dataList = (List<Map<String, Object>>) dataMap.get("data");
                            if (!dataList.isEmpty()) {
                                Map<String, Object> sampleRecord = dataList.get(0);
                                result.put("fields", analyzeFields(sampleRecord));
                            }
                        } else {
                            result.put("fields", analyzeFields(dataMap));
                        }
                    }
                }
                
                result.put("success", true);
                result.put("responseFormat", "JSON");
                result.put("sampleResponse", responseBody.length() > 1000 ? responseBody.substring(0, 1000) + "..." : responseBody);
                
            } else {
                result.put("success", false);
                result.put("error", "API请求失败: HTTP " + response.getStatus());
            }
            
        } catch (Exception e) {
            log.error("获取API数据结构失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    @Override
    public List<Map<String, Object>> readData(Map<String, Object> config,
                                               Map<String, Object> fieldMapping,
                                               Map<String, Object> filterCondition,
                                               LocalDateTime lastSyncTime,
                                               Integer batchSize) {
        try {
            String url = (String) config.get("url");
            String method = (String) config.getOrDefault("method", "GET");
            
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            if (filterCondition != null) {
                params.putAll(filterCondition);
            }
            
            // 添加增量同步参数
            if (lastSyncTime != null) {
                String timestampParam = (String) config.getOrDefault("timestampParam", "lastModified");
                params.put(timestampParam, lastSyncTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            }
            
            // 添加分页参数
            if (batchSize != null) {
                String limitParam = (String) config.getOrDefault("limitParam", "limit");
                params.put(limitParam, batchSize);
            }
            
            // 发送请求
            HttpRequest request = buildHttpRequest(url, method, config, params);
            HttpResponse response = request.execute();
            
            if (!response.isOk()) {
                throw new BusinessException("API请求失败: HTTP " + response.getStatus());
            }
            
            String responseBody = response.body();
            List<Map<String, Object>> data = parseResponseData(responseBody, config);
            
            // 应用字段映射
            if (fieldMapping != null && !fieldMapping.isEmpty()) {
                data = applyFieldMapping(data, fieldMapping);
            }
            
            log.info("API读取数据成功: url={}, count={}", url, data.size());
            return data;
            
        } catch (Exception e) {
            log.error("API读取数据失败: {}", e.getMessage(), e);
            throw new BusinessException("API读取数据失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> writeData(Map<String, Object> config,
                                         Map<String, Object> fieldMapping,
                                         List<Map<String, Object>> data,
                                         String operationType) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String url = (String) config.get("url");
            String method = getWriteMethod(operationType, config);
            
            int successCount = 0;
            int failedCount = 0;
            List<String> errors = new ArrayList<>();
            
            // 应用字段映射
            if (fieldMapping != null && !fieldMapping.isEmpty()) {
                data = reverseFieldMapping(data, fieldMapping);
            }
            
            // 检查是否支持批量操作
            Boolean supportBatch = (Boolean) config.getOrDefault("supportBatch", false);
            
            if (supportBatch) {
                // 批量写入
                try {
                    Map<String, Object> batchResult = writeBatchData(url, method, config, data, operationType);
                    successCount = (Integer) batchResult.getOrDefault("successCount", 0);
                    failedCount = (Integer) batchResult.getOrDefault("failedCount", 0);
                    @SuppressWarnings("unchecked")
                    List<String> batchErrors = (List<String>) batchResult.getOrDefault("errors", new ArrayList<>());
                    errors.addAll(batchErrors);
                } catch (Exception e) {
                    failedCount = data.size();
                    errors.add("批量写入失败: " + e.getMessage());
                }
            } else {
                // 逐条写入
                for (Map<String, Object> record : data) {
                    try {
                        writeRecord(url, method, config, record, operationType);
                        successCount++;
                    } catch (Exception e) {
                        failedCount++;
                        errors.add("记录写入失败: " + e.getMessage());
                        log.error("记录写入失败: record={}, error={}", record, e.getMessage());
                    }
                }
            }
            
            result.put("success", true);
            result.put("totalCount", data.size());
            result.put("successCount", successCount);
            result.put("failedCount", failedCount);
            result.put("errors", errors);
            
            log.info("API写入数据完成: url={}, total={}, success={}, failed={}", 
                    url, data.size(), successCount, failedCount);
            
        } catch (Exception e) {
            log.error("API写入数据失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> detectChanges(Map<String, Object> config, LocalDateTime lastSyncTime) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String url = (String) config.get("url");
            String method = (String) config.getOrDefault("method", "GET");
            
            // 构建变更检测请求
            Map<String, Object> params = new HashMap<>();
            if (lastSyncTime != null) {
                String timestampParam = (String) config.getOrDefault("timestampParam", "lastModified");
                params.put(timestampParam, lastSyncTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            }
            
            // 只获取数量，不获取具体数据
            String countParam = (String) config.getOrDefault("countParam", "count");
            params.put(countParam, "true");
            
            HttpRequest request = buildHttpRequest(url, method, config, params);
            HttpResponse response = request.execute();
            
            if (response.isOk()) {
                String responseBody = response.body();
                
                // 尝试解析变更数量
                Integer changeCount = parseChangeCount(responseBody, config);
                
                result.put("hasChanges", changeCount != null && changeCount > 0);
                result.put("changeCount", changeCount != null ? changeCount : 0);
                result.put("lastCheckTime", LocalDateTime.now());
            } else {
                result.put("hasChanges", false);
                result.put("error", "API请求失败: HTTP " + response.getStatus());
            }
            
        } catch (Exception e) {
            log.error("检测API数据变更失败: {}", e.getMessage(), e);
            result.put("hasChanges", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    @Override
    public Long getDataCount(Map<String, Object> config, Map<String, Object> filterCondition) {
        try {
            String url = (String) config.get("url");
            String method = (String) config.getOrDefault("method", "GET");
            
            Map<String, Object> params = new HashMap<>();
            if (filterCondition != null) {
                params.putAll(filterCondition);
            }
            
            // 添加计数参数
            String countParam = (String) config.getOrDefault("countParam", "count");
            params.put(countParam, "true");
            
            HttpRequest request = buildHttpRequest(url, method, config, params);
            HttpResponse response = request.execute();
            
            if (response.isOk()) {
                String responseBody = response.body();
                Integer count = parseChangeCount(responseBody, config);
                return count != null ? count.longValue() : 0L;
            }
            
            return 0L;
            
        } catch (Exception e) {
            log.error("获取API数据总数失败: {}", e.getMessage(), e);
            return 0L;
        }
    }

    @Override
    public Map<String, Object> suggestFieldMapping(Map<String, Object> sourceConfig, Map<String, Object> targetConfig) {
        Map<String, Object> mapping = new HashMap<>();
        
        try {
            Map<String, Object> sourceStructure = getDataStructure(sourceConfig);
            Map<String, Object> targetStructure = getDataStructure(targetConfig);
            
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> sourceFields = (List<Map<String, Object>>) sourceStructure.get("fields");
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> targetFields = (List<Map<String, Object>>) targetStructure.get("fields");
            
            if (sourceFields != null && targetFields != null) {
                Map<String, String> suggestions = new HashMap<>();
                
                for (Map<String, Object> sourceField : sourceFields) {
                    String sourceFieldName = (String) sourceField.get("name");
                    
                    for (Map<String, Object> targetField : targetFields) {
                        String targetFieldName = (String) targetField.get("name");
                        
                        if (sourceFieldName.equals(targetFieldName)) {
                            suggestions.put(sourceFieldName, targetFieldName);
                            break;
                        }
                        
                        if (sourceFieldName.toLowerCase().equals(targetFieldName.toLowerCase())) {
                            suggestions.put(sourceFieldName, targetFieldName);
                            break;
                        }
                    }
                }
                
                mapping.put("suggestions", suggestions);
            }
            
        } catch (Exception e) {
            log.error("生成API字段映射建议失败: {}", e.getMessage(), e);
            mapping.put("error", e.getMessage());
        }
        
        return mapping;
    }

    @Override
    public List<Map<String, Object>> executeCustomQuery(Map<String, Object> config, String query, Map<String, Object> params) {
        try {
            String url = (String) config.get("url");
            String method = (String) config.getOrDefault("method", "GET");
            
            // 将查询参数添加到请求中
            Map<String, Object> requestParams = new HashMap<>();
            if (params != null) {
                requestParams.putAll(params);
            }
            requestParams.put("query", query);
            
            HttpRequest request = buildHttpRequest(url, method, config, requestParams);
            HttpResponse response = request.execute();
            
            if (response.isOk()) {
                String responseBody = response.body();
                return parseResponseData(responseBody, config);
            } else {
                throw new BusinessException("API查询失败: HTTP " + response.getStatus());
            }
            
        } catch (Exception e) {
            log.error("执行API自定义查询失败: query={}, error={}", query, e.getMessage(), e);
            throw new BusinessException("执行API自定义查询失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getSyncProgress(Map<String, Object> config, String syncId) {
        Map<String, Object> progress = new HashMap<>();
        progress.put("syncId", syncId);
        progress.put("progress", 100);
        progress.put("message", "API同步完成");
        return progress;
    }

    @Override
    public void cleanup(Map<String, Object> config, String syncId) {
        log.debug("API适配器清理完成: syncId={}", syncId);
    }

    /**
     * 构建HTTP请求
     */
    private HttpRequest buildHttpRequest(String url, String method, Map<String, Object> config, Map<String, Object> params) {
        HttpRequest request;
        
        switch (method.toUpperCase()) {
            case "GET":
                request = HttpUtil.createGet(url);
                if (params != null && !params.isEmpty()) {
                    request.form(params);
                }
                break;
            case "POST":
                request = HttpUtil.createPost(url);
                if (params != null && !params.isEmpty()) {
                    request.body(JSONUtil.toJsonStr(params));
                    request.contentType("application/json");
                }
                break;
            case "PUT":
                request = HttpUtil.createRequest(cn.hutool.http.Method.PUT, url);
                if (params != null && !params.isEmpty()) {
                    request.body(JSONUtil.toJsonStr(params));
                    request.contentType("application/json");
                }
                break;
            case "DELETE":
                request = HttpUtil.createRequest(cn.hutool.http.Method.DELETE, url);
                if (params != null && !params.isEmpty()) {
                    request.form(params);
                }
                break;
            default:
                throw new BusinessException("不支持的HTTP方法: " + method);
        }
        
        // 添加认证头
        @SuppressWarnings("unchecked")
        Map<String, String> headers = (Map<String, String>) config.get("headers");
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                request.header(entry.getKey(), entry.getValue());
            }
        }
        
        // 设置超时时间
        Integer timeout = (Integer) config.getOrDefault("timeout", 30000);
        request.timeout(timeout);
        
        return request;
    }

    /**
     * 验证URL格式
     */
    private boolean isValidUrl(String url) {
        return StrUtil.isNotBlank(url) && (url.startsWith("http://") || url.startsWith("https://"));
    }

    /**
     * 验证HTTP方法
     */
    private boolean isValidHttpMethod(String method) {
        return Arrays.asList("GET", "POST", "PUT", "DELETE", "PATCH").contains(method.toUpperCase());
    }

    /**
     * 分析字段结构
     */
    private List<Map<String, Object>> analyzeFields(Map<String, Object> sampleRecord) {
        List<Map<String, Object>> fields = new ArrayList<>();
        
        for (Map.Entry<String, Object> entry : sampleRecord.entrySet()) {
            Map<String, Object> field = new HashMap<>();
            field.put("name", entry.getKey());
            field.put("type", getFieldType(entry.getValue()));
            field.put("sample", entry.getValue());
            fields.add(field);
        }
        
        return fields;
    }

    /**
     * 获取字段类型
     */
    private String getFieldType(Object value) {
        if (value == null) {
            return "NULL";
        } else if (value instanceof String) {
            return "STRING";
        } else if (value instanceof Number) {
            return "NUMBER";
        } else if (value instanceof Boolean) {
            return "BOOLEAN";
        } else if (value instanceof List) {
            return "ARRAY";
        } else if (value instanceof Map) {
            return "OBJECT";
        } else {
            return "UNKNOWN";
        }
    }

    /**
     * 解析响应数据
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> parseResponseData(String responseBody, Map<String, Object> config) {
        if (!JSONUtil.isTypeJSON(responseBody)) {
            throw new BusinessException("API响应不是有效的JSON格式");
        }
        
        Object jsonData = JSONUtil.parse(responseBody);
        
        if (jsonData instanceof List) {
            return (List<Map<String, Object>>) jsonData;
        } else if (jsonData instanceof Map) {
            Map<String, Object> dataMap = (Map<String, Object>) jsonData;
            
            // 检查配置的数据路径
            String dataPath = (String) config.getOrDefault("dataPath", "data");
            if (dataMap.containsKey(dataPath) && dataMap.get(dataPath) instanceof List) {
                return (List<Map<String, Object>>) dataMap.get(dataPath);
            } else {
                // 将单个对象包装成列表
                return Arrays.asList(dataMap);
            }
        } else {
            throw new BusinessException("无法解析API响应数据格式");
        }
    }

    /**
     * 解析变更数量
     */
    private Integer parseChangeCount(String responseBody, Map<String, Object> config) {
        try {
            if (JSONUtil.isTypeJSON(responseBody)) {
                Object jsonData = JSONUtil.parse(responseBody);
                
                if (jsonData instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> dataMap = (Map<String, Object>) jsonData;
                    
                    String countPath = (String) config.getOrDefault("countPath", "count");
                    Object countValue = dataMap.get(countPath);
                    
                    if (countValue instanceof Number) {
                        return ((Number) countValue).intValue();
                    }
                }
            }
            
            return null;
        } catch (Exception e) {
            log.error("解析变更数量失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取写入方法
     */
    private String getWriteMethod(String operationType, Map<String, Object> config) {
        switch (operationType) {
            case "CREATE":
                return (String) config.getOrDefault("createMethod", "POST");
            case "UPDATE":
                return (String) config.getOrDefault("updateMethod", "PUT");
            case "DELETE":
                return (String) config.getOrDefault("deleteMethod", "DELETE");
            default:
                return "POST";
        }
    }

    /**
     * 批量写入数据
     */
    private Map<String, Object> writeBatchData(String url, String method, Map<String, Object> config, 
                                               List<Map<String, Object>> data, String operationType) {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("operation", operationType);
        requestBody.put("data", data);
        
        HttpRequest request = buildHttpRequest(url, method, config, requestBody);
        HttpResponse response = request.execute();
        
        if (!response.isOk()) {
            throw new BusinessException("批量API请求失败: HTTP " + response.getStatus());
        }
        
        // 解析批量操作结果
        String responseBody = response.body();
        if (JSONUtil.isTypeJSON(responseBody)) {
            @SuppressWarnings("unchecked")
            Map<String, Object> result = JSONUtil.toBean(responseBody, Map.class);
            return result;
        } else {
            Map<String, Object> result = new HashMap<>();
            result.put("successCount", data.size());
            result.put("failedCount", 0);
            result.put("errors", new ArrayList<>());
            return result;
        }
    }

    /**
     * 写入单条记录
     */
    private void writeRecord(String url, String method, Map<String, Object> config, 
                            Map<String, Object> record, String operationType) {
        HttpRequest request = buildHttpRequest(url, method, config, record);
        HttpResponse response = request.execute();
        
        if (!response.isOk()) {
            throw new BusinessException("API请求失败: HTTP " + response.getStatus());
        }
    }

    /**
     * 应用字段映射
     */
    private List<Map<String, Object>> applyFieldMapping(List<Map<String, Object>> data, Map<String, Object> fieldMapping) {
        if (CollUtil.isEmpty(data) || fieldMapping == null || fieldMapping.isEmpty()) {
            return data;
        }
        
        List<Map<String, Object>> mappedData = new ArrayList<>();
        
        for (Map<String, Object> record : data) {
            Map<String, Object> mappedRecord = new HashMap<>();
            
            for (Map.Entry<String, Object> entry : record.entrySet()) {
                String sourceField = entry.getKey();
                Object value = entry.getValue();
                
                String targetField = (String) fieldMapping.get(sourceField);
                if (targetField != null) {
                    mappedRecord.put(targetField, value);
                } else {
                    mappedRecord.put(sourceField, value);
                }
            }
            
            mappedData.add(mappedRecord);
        }
        
        return mappedData;
    }

    /**
     * 反向字段映射
     */
    private List<Map<String, Object>> reverseFieldMapping(List<Map<String, Object>> data, Map<String, Object> fieldMapping) {
        if (CollUtil.isEmpty(data) || fieldMapping == null || fieldMapping.isEmpty()) {
            return data;
        }
        
        Map<String, String> reverseMapping = new HashMap<>();
        for (Map.Entry<String, Object> entry : fieldMapping.entrySet()) {
            reverseMapping.put((String) entry.getValue(), entry.getKey());
        }
        
        return applyFieldMapping(data, new HashMap<>(reverseMapping));
    }
}
