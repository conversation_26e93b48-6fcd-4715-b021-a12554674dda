package top.continew.admin.bot.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 命令处理引擎配置
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "continew.bot.command")
public class CommandProcessingConfig {

    /**
     * 是否启用命令历史记录
     */
    private Boolean historyEnabled = true;

    /**
     * 历史记录保留天数
     */
    private Integer historyRetentionDays = 90;

    /**
     * 是否启用智能推荐
     */
    private Boolean intelligentSuggestionEnabled = true;

    /**
     * 最大批处理命令数量
     */
    private Integer maxBatchSize = 10;

    /**
     * 命令执行超时时间（秒）
     */
    private Integer executionTimeoutSeconds = 30;

    /**
     * 是否启用异步处理
     */
    private Boolean asyncProcessingEnabled = true;

    /**
     * 异步处理线程池大小
     */
    private Integer asyncThreadPoolSize = 5;

    /**
     * 是否启用命令缓存
     */
    private Boolean cacheEnabled = true;

    /**
     * 缓存过期时间（分钟）
     */
    private Integer cacheExpirationMinutes = 60;

    /**
     * 最大缓存大小
     */
    private Integer maxCacheSize = 1000;

    /**
     * 是否启用命令验证
     */
    private Boolean validationEnabled = true;

    /**
     * 最小置信度阈值
     */
    private Double minConfidenceThreshold = 60.0;

    /**
     * 是否启用自动分类
     */
    private Boolean autoCategorizationEnabled = true;

    /**
     * 是否启用自动标签
     */
    private Boolean autoTaggingEnabled = true;

    /**
     * 命令重试次数
     */
    private Integer maxRetryAttempts = 3;

    /**
     * 重试间隔（毫秒）
     */
    private Long retryIntervalMs = 1000L;

    /**
     * 是否启用性能监控
     */
    private Boolean performanceMonitoringEnabled = true;

    /**
     * 性能监控采样率（0.0-1.0）
     */
    private Double performanceSamplingRate = 0.1;

    /**
     * 是否启用错误报告
     */
    private Boolean errorReportingEnabled = true;

    /**
     * 错误报告级别
     */
    private String errorReportingLevel = "WARN";

    /**
     * 是否启用命令建议
     */
    private Boolean commandSuggestionEnabled = true;

    /**
     * 建议命令数量限制
     */
    private Integer maxSuggestionCount = 5;

    /**
     * 是否启用自然语言处理
     */
    private Boolean naturalLanguageProcessingEnabled = true;

    /**
     * 自然语言处理置信度阈值
     */
    private Double nlpConfidenceThreshold = 70.0;

    /**
     * 是否启用多语言支持
     */
    private Boolean multiLanguageEnabled = false;

    /**
     * 默认语言
     */
    private String defaultLanguage = "zh-CN";

    /**
     * 支持的语言列表
     */
    private String[] supportedLanguages = {"zh-CN", "en-US"};

    /**
     * 是否启用命令模板
     */
    private Boolean templateEnabled = true;

    /**
     * 模板缓存大小
     */
    private Integer templateCacheSize = 100;

    /**
     * 是否启用命令宏
     */
    private Boolean macroEnabled = false;

    /**
     * 最大宏嵌套深度
     */
    private Integer maxMacroDepth = 3;

    /**
     * 是否启用命令调试
     */
    private Boolean debugEnabled = false;

    /**
     * 调试日志级别
     */
    private String debugLogLevel = "DEBUG";
}
