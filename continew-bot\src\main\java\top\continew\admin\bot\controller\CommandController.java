package top.continew.admin.bot.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.accounting.enums.PlatformType;
import top.continew.admin.bot.model.dto.CommandExecutionResult;
import top.continew.admin.bot.model.dto.ParsedCommand;
import top.continew.admin.bot.model.entity.CommandHistoryDO;
import top.continew.admin.bot.model.req.CommandProcessReq;
import top.continew.admin.bot.service.CommandHistoryService;
import top.continew.admin.bot.service.CommandParser;
import top.continew.admin.bot.service.CommandProcessingEngine;
import top.continew.admin.common.util.helper.LoginHelper;
import top.continew.starter.core.util.validate.CheckUtils;
import top.continew.starter.web.model.R;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 命令处理控制器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "命令处理 API")
@RestController
@RequestMapping("/api/v1/bot/command")
@RequiredArgsConstructor
@Validated
public class CommandController {

    private final CommandProcessingEngine commandProcessingEngine;
    private final CommandParser commandParser;
    private final CommandHistoryService commandHistoryService;

    @Operation(summary = "处理单个命令", description = "解析并执行单个命令")
    @PostMapping("/process")
    public R<CommandExecutionResult> processCommand(@RequestBody @Validated CommandProcessReq req) {
        Long userId = LoginHelper.getUserId();
        CommandExecutionResult result = commandProcessingEngine.processCommand(
            req.getCommand(), 
            req.getPlatform(), 
            req.getGroupId(), 
            userId
        );
        return R.ok(result);
    }

    @Operation(summary = "批量处理命令", description = "批量解析并执行多个命令")
    @PostMapping("/process/batch")
    public R<List<CommandExecutionResult>> processCommands(@RequestBody @Validated List<CommandProcessReq> reqs) {
        CheckUtils.throwIf(reqs.size() > 10, "批量处理命令数量不能超过10个");
        
        Long userId = LoginHelper.getUserId();
        List<CommandExecutionResult> results = reqs.stream()
            .map(req -> commandProcessingEngine.processCommand(
                req.getCommand(), 
                req.getPlatform(), 
                req.getGroupId(), 
                userId
            ))
            .toList();
        
        return R.ok(results);
    }

    @Operation(summary = "异步处理命令", description = "异步解析并执行命令")
    @PostMapping("/process/async")
    public R<String> processCommandAsync(@RequestBody @Validated CommandProcessReq req) {
        Long userId = LoginHelper.getUserId();
        CompletableFuture<CommandExecutionResult> future = commandProcessingEngine.processCommandAsync(
            req.getCommand(), 
            req.getPlatform(), 
            req.getGroupId(), 
            userId
        );
        
        // 这里可以返回任务ID，实际项目中可能需要任务管理系统
        return R.ok("命令已提交异步处理");
    }

    @Operation(summary = "解析命令", description = "仅解析命令，不执行")
    @PostMapping("/parse")
    public R<ParsedCommand> parseCommand(@RequestParam String command) {
        ParsedCommand parsed = commandParser.parseCommand(command);
        return R.ok(parsed);
    }

    @Operation(summary = "验证命令语法", description = "验证命令语法是否正确")
    @GetMapping("/validate")
    public R<Boolean> validateCommand(@RequestParam String command) {
        boolean valid = commandParser.isValidCommandSyntax(command);
        return R.ok(valid);
    }

    @Operation(summary = "获取命令建议", description = "根据输入获取命令建议")
    @GetMapping("/suggestions")
    public R<List<String>> getCommandSuggestions(@RequestParam(required = false) String partialText) {
        List<String> suggestions = commandParser.getCommandSuggestions(partialText);
        return R.ok(suggestions);
    }

    @Operation(summary = "获取帮助信息", description = "获取命令使用帮助")
    @GetMapping("/help")
    public R<String> getHelp() {
        String help = commandParser.getHelpMessage();
        return R.ok(help);
    }

    @Operation(summary = "处理系统命令", description = "处理系统级命令")
    @PostMapping("/system")
    public R<CommandExecutionResult> processSystemCommand(
        @RequestParam String command,
        @RequestParam PlatformType platform,
        @RequestParam Long groupId
    ) {
        Long userId = LoginHelper.getUserId();
        CommandExecutionResult result = commandProcessingEngine.processSystemCommand(
            command, platform, groupId, userId
        );
        return R.ok(result);
    }

    @Operation(summary = "获取命令历史", description = "获取用户命令执行历史")
    @GetMapping("/history")
    public R<List<CommandHistoryDO>> getCommandHistory(
        @Parameter(description = "群组ID") @RequestParam(required = false) Long groupId,
        @Parameter(description = "限制数量") @RequestParam(defaultValue = "20") int limit
    ) {
        Long userId = LoginHelper.getUserId();
        List<CommandHistoryDO> history = commandHistoryService.getUserCommandHistory(userId, groupId, limit);
        return R.ok(history);
    }

    @Operation(summary = "获取命令统计", description = "获取命令使用统计信息")
    @GetMapping("/statistics")
    public R<Map<String, Object>> getCommandStatistics(
        @Parameter(description = "群组ID") @RequestParam(required = false) Long groupId,
        @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") int days
    ) {
        Long userId = LoginHelper.getUserId();
        
        Map<String, Object> successRate = commandHistoryService.getSuccessRateStatistics(userId, groupId, days);
        Map<String, Object> executionTime = commandHistoryService.getAverageExecutionTime(userId, groupId, days);
        Map<String, Object> complexity = commandHistoryService.getCommandComplexityAnalysis(userId, groupId, days);
        List<String> mostUsed = commandHistoryService.getMostUsedCommands(userId, groupId, 10);
        
        Map<String, Object> statistics = Map.of(
            "successRate", successRate,
            "executionTime", executionTime,
            "complexity", complexity,
            "mostUsedCommands", mostUsed
        );
        
        return R.ok(statistics);
    }

    @Operation(summary = "获取失败命令", description = "获取执行失败的命令列表")
    @GetMapping("/failures")
    public R<List<CommandHistoryDO>> getFailedCommands(
        @Parameter(description = "群组ID") @RequestParam(required = false) Long groupId,
        @Parameter(description = "限制数量") @RequestParam(defaultValue = "10") int limit
    ) {
        Long userId = LoginHelper.getUserId();
        List<CommandHistoryDO> failures = commandHistoryService.getFailedCommands(userId, groupId, limit);
        return R.ok(failures);
    }

    @Operation(summary = "获取使用趋势", description = "获取命令使用趋势数据")
    @GetMapping("/trends")
    public R<Map<String, Long>> getUsageTrends(
        @Parameter(description = "群组ID") @RequestParam(required = "false") Long groupId,
        @Parameter(description = "统计天数") @RequestParam(defaultValue = "7") int days
    ) {
        Long userId = LoginHelper.getUserId();
        Map<String, Long> trends = commandHistoryService.getCommandUsageTrend(userId, groupId, days);
        return R.ok(trends);
    }

    @Operation(summary = "智能分类推荐", description = "根据描述推荐分类")
    @GetMapping("/suggest/category")
    public R<String> suggestCategory(@RequestParam String description) {
        String category = commandParser.suggestCategory(description);
        return R.ok(category);
    }

    @Operation(summary = "智能标签推荐", description = "根据描述推荐标签")
    @GetMapping("/suggest/tags")
    public R<List<String>> suggestTags(@RequestParam String description) {
        List<String> tags = commandParser.suggestTags(description);
        return R.ok(tags);
    }

    @Operation(summary = "清理历史记录", description = "清理过期的命令历史记录")
    @DeleteMapping("/history/cleanup")
    public R<Integer> cleanupHistory(@RequestParam(defaultValue = "90") int daysToKeep) {
        int cleaned = commandHistoryService.cleanExpiredHistory(daysToKeep);
        return R.ok(cleaned);
    }
}
