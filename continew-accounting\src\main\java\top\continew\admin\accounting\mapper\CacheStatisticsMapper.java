package top.continew.admin.accounting.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.accounting.model.entity.CacheStatisticsDO;
import top.continew.starter.extension.crud.mapper.BaseMapper;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 缓存统计 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface CacheStatisticsMapper extends BaseMapper<CacheStatisticsDO> {

    /**
     * 获取总体缓存统计
     *
     * @param groupId 群组ID
     * @return 总体统计
     */
    Map<String, Object> getOverallCacheStatistics(@Param("groupId") Long groupId);

    /**
     * 获取缓存类型统计
     *
     * @param groupId 群组ID
     * @return 缓存类型统计
     */
    List<Map<String, Object>> getCacheTypeStatistics(@Param("groupId") Long groupId);

    /**
     * 获取热点数据统计
     *
     * @param cacheName 缓存名称
     * @param limit     限制数量
     * @return 热点数据统计
     */
    List<Map<String, Object>> getHotspotStatistics(@Param("cacheName") String cacheName, @Param("limit") Integer limit);

    /**
     * 获取性能趋势统计
     *
     * @param cacheName 缓存名称
     * @param hours     时间范围（小时）
     * @return 性能趋势统计
     */
    List<Map<String, Object>> getPerformanceTrendStatistics(@Param("cacheName") String cacheName, @Param("hours") Integer hours);

    /**
     * 获取错误统计
     *
     * @param cacheName 缓存名称
     * @param hours     时间范围（小时）
     * @return 错误统计
     */
    List<Map<String, Object>> getErrorStatistics(@Param("cacheName") String cacheName, @Param("hours") Integer hours);

    /**
     * 获取缓存性能指标
     *
     * @param cacheName 缓存名称
     * @param hours     时间范围（小时）
     * @return 性能指标
     */
    List<Map<String, Object>> getCachePerformanceMetrics(@Param("cacheName") String cacheName, @Param("hours") Integer hours);

    /**
     * 获取缓存健康状态
     *
     * @param groupId 群组ID
     * @return 健康状态
     */
    Map<String, Object> getCacheHealthStatus(@Param("groupId") Long groupId);

    /**
     * 检测缓存异常
     *
     * @param groupId 群组ID
     * @return 异常信息
     */
    List<Map<String, Object>> detectCacheAnomalies(@Param("groupId") Long groupId);

    /**
     * 获取缓存使用模式分析
     *
     * @param cacheName 缓存名称
     * @param days      分析天数
     * @return 使用模式分析
     */
    Map<String, Object> getCacheUsagePattern(@Param("cacheName") String cacheName, @Param("days") Integer days);

    /**
     * 获取缓存容量预测
     *
     * @param cacheName 缓存名称
     * @param days      预测天数
     * @return 容量预测
     */
    Map<String, Object> getCacheCapacityPrediction(@Param("cacheName") String cacheName, @Param("days") Integer days);

    /**
     * 获取缓存优化建议
     *
     * @param groupId 群组ID
     * @return 优化建议
     */
    List<Map<String, Object>> getCacheOptimizationSuggestions(@Param("groupId") Long groupId);

    /**
     * 批量插入缓存统计数据
     *
     * @param statistics 统计数据列表
     * @return 插入数量
     */
    Integer batchInsertStatistics(@Param("statistics") List<CacheStatisticsDO> statistics);

    /**
     * 清理过期的统计数据
     *
     * @param expireTime 过期时间
     * @return 清理数量
     */
    Integer cleanupExpiredStatistics(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 聚合统计数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param period    聚合周期（分钟）
     * @return 聚合结果
     */
    List<Map<String, Object>> aggregateStatistics(@Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime,
                                                   @Param("period") Integer period);

    /**
     * 计算缓存命中率趋势
     *
     * @param cacheName 缓存名称
     * @param days      天数
     * @return 命中率趋势
     */
    List<Map<String, Object>> calculateHitRateTrend(@Param("cacheName") String cacheName, @Param("days") Integer days);

    /**
     * 计算缓存响应时间趋势
     *
     * @param cacheName 缓存名称
     * @param days      天数
     * @return 响应时间趋势
     */
    List<Map<String, Object>> calculateResponseTimeTrend(@Param("cacheName") String cacheName, @Param("days") Integer days);

    /**
     * 计算缓存QPS趋势
     *
     * @param cacheName 缓存名称
     * @param days      天数
     * @return QPS趋势
     */
    List<Map<String, Object>> calculateQpsTrend(@Param("cacheName") String cacheName, @Param("days") Integer days);

    /**
     * 计算缓存错误率趋势
     *
     * @param cacheName 缓存名称
     * @param days      天数
     * @return 错误率趋势
     */
    List<Map<String, Object>> calculateErrorRateTrend(@Param("cacheName") String cacheName, @Param("days") Integer days);

    /**
     * 获取缓存峰值统计
     *
     * @param cacheName 缓存名称
     * @param days      天数
     * @return 峰值统计
     */
    Map<String, Object> getCachePeakStatistics(@Param("cacheName") String cacheName, @Param("days") Integer days);

    /**
     * 获取缓存低谷统计
     *
     * @param cacheName 缓存名称
     * @param days      天数
     * @return 低谷统计
     */
    Map<String, Object> getCacheValleyStatistics(@Param("cacheName") String cacheName, @Param("days") Integer days);

    /**
     * 计算缓存稳定性指标
     *
     * @param cacheName 缓存名称
     * @param days      天数
     * @return 稳定性指标
     */
    Map<String, Object> calculateCacheStabilityMetrics(@Param("cacheName") String cacheName, @Param("days") Integer days);

    /**
     * 获取缓存资源利用率
     *
     * @param cacheName 缓存名称
     * @return 资源利用率
     */
    Map<String, Object> getCacheResourceUtilization(@Param("cacheName") String cacheName);

    /**
     * 获取缓存成本分析
     *
     * @param groupId 群组ID
     * @return 成本分析
     */
    Map<String, Object> getCacheCostAnalysis(@Param("groupId") Long groupId);

    /**
     * 获取缓存ROI分析
     *
     * @param cacheName 缓存名称
     * @param days      天数
     * @return ROI分析
     */
    Map<String, Object> getCacheRoiAnalysis(@Param("cacheName") String cacheName, @Param("days") Integer days);

    /**
     * 比较缓存性能
     *
     * @param cacheName1 缓存名称1
     * @param cacheName2 缓存名称2
     * @param days       天数
     * @return 性能比较
     */
    Map<String, Object> compareCachePerformance(@Param("cacheName1") String cacheName1,
                                                @Param("cacheName2") String cacheName2,
                                                @Param("days") Integer days);

    /**
     * 获取缓存基准测试结果
     *
     * @param cacheName 缓存名称
     * @return 基准测试结果
     */
    Map<String, Object> getCacheBenchmarkResults(@Param("cacheName") String cacheName);

    /**
     * 获取缓存SLA合规性
     *
     * @param cacheName 缓存名称
     * @param days      天数
     * @return SLA合规性
     */
    Map<String, Object> getCacheSlaCompliance(@Param("cacheName") String cacheName, @Param("days") Integer days);

}
