package top.continew.admin.bot.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import top.continew.admin.accounting.enums.PlatformType;
import top.continew.admin.bot.common.BotMessageQueue;
import top.continew.admin.bot.model.dto.BotMessage;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 异步消息管理器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AsyncMessageManager {

    private final RabbitTemplate rabbitTemplate;
    private final BotMessageQueue botMessageQueue;
    
    // 消息统计
    private final AtomicLong totalMessages = new AtomicLong(0);
    private final AtomicLong successMessages = new AtomicLong(0);
    private final AtomicLong failedMessages = new AtomicLong(0);
    private final Map<String, AtomicLong> platformStats = new ConcurrentHashMap<>();
    
    // 消息重试队列
    private final Map<String, BotMessage> retryQueue = new ConcurrentHashMap<>();

    /**
     * 异步发送消息
     */
    @Async
    public CompletableFuture<Boolean> sendMessageAsync(BotMessage message) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 设置消息ID和时间戳
                if (message.getMessageId() == null) {
                    message.setMessageId(generateMessageId());
                }
                message.setTimestamp(LocalDateTime.now());
                message.setProcessStatus(BotMessage.ProcessStatus.PENDING);
                
                // 发送到队列
                botMessageQueue.sendMessage(message);
                
                // 更新统计
                totalMessages.incrementAndGet();
                platformStats.computeIfAbsent(message.getPlatform().name(), k -> new AtomicLong(0))
                           .incrementAndGet();
                
                log.debug("异步消息发送成功: {}", message.getMessageId());
                return true;
                
            } catch (Exception e) {
                log.error("异步消息发送失败: {}", message, e);
                failedMessages.incrementAndGet();
                
                // 添加到重试队列
                if (message.canRetry()) {
                    addToRetryQueue(message);
                }
                
                return false;
            }
        });
    }

    /**
     * 异步发送通知
     */
    @Async
    public CompletableFuture<Boolean> sendNotificationAsync(BotMessage notification) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 设置通知ID和时间戳
                if (notification.getMessageId() == null) {
                    notification.setMessageId(generateMessageId());
                }
                notification.setTimestamp(LocalDateTime.now());
                notification.setProcessStatus(BotMessage.ProcessStatus.PENDING);
                
                // 发送到通知队列
                botMessageQueue.sendNotification(notification);
                
                log.debug("异步通知发送成功: {}", notification.getMessageId());
                return true;
                
            } catch (Exception e) {
                log.error("异步通知发送失败: {}", notification, e);
                
                // 添加到重试队列
                if (notification.canRetry()) {
                    addToRetryQueue(notification);
                }
                
                return false;
            }
        });
    }

    /**
     * 异步发送命令
     */
    @Async
    public CompletableFuture<Boolean> sendCommandAsync(BotMessage command) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 设置命令ID和时间戳
                if (command.getMessageId() == null) {
                    command.setMessageId(generateMessageId());
                }
                command.setTimestamp(LocalDateTime.now());
                command.setProcessStatus(BotMessage.ProcessStatus.PENDING);
                
                // 发送到命令队列
                botMessageQueue.sendCommand(command);
                
                log.debug("异步命令发送成功: {}", command.getMessageId());
                return true;
                
            } catch (Exception e) {
                log.error("异步命令发送失败: {}", command, e);
                
                // 添加到重试队列
                if (command.canRetry()) {
                    addToRetryQueue(command);
                }
                
                return false;
            }
        });
    }

    /**
     * 批量异步发送消息
     */
    @Async
    public CompletableFuture<List<Boolean>> sendBulkMessagesAsync(List<BotMessage> messages) {
        return CompletableFuture.supplyAsync(() -> {
            return messages.stream()
                .map(message -> {
                    try {
                        return sendMessageAsync(message).get();
                    } catch (Exception e) {
                        log.error("批量发送消息失败: {}", message, e);
                        return false;
                    }
                })
                .toList();
        });
    }

    /**
     * 发送延迟消息
     */
    public void sendDelayedMessage(BotMessage message, long delaySeconds) {
        CompletableFuture.delayedExecutor(delaySeconds, java.util.concurrent.TimeUnit.SECONDS)
            .execute(() -> sendMessageAsync(message));
    }

    /**
     * 发送定时消息
     */
    public void sendScheduledMessage(BotMessage message, LocalDateTime scheduledTime) {
        long delay = java.time.Duration.between(LocalDateTime.now(), scheduledTime).getSeconds();
        if (delay > 0) {
            sendDelayedMessage(message, delay);
        } else {
            sendMessageAsync(message);
        }
    }

    /**
     * 添加到重试队列
     */
    private void addToRetryQueue(BotMessage message) {
        message.incrementRetryCount();
        message.setProcessStatus(BotMessage.ProcessStatus.RETRY);
        retryQueue.put(message.getMessageId(), message);
        log.info("消息添加到重试队列: {}, 重试次数: {}", message.getMessageId(), message.getRetryCount());
    }

    /**
     * 处理重试队列
     */
    @Scheduled(fixedDelay = 30000) // 每30秒执行一次
    public void processRetryQueue() {
        if (retryQueue.isEmpty()) {
            return;
        }
        
        log.info("开始处理重试队列，待重试消息数量: {}", retryQueue.size());
        
        retryQueue.values().removeIf(message -> {
            try {
                if (!message.canRetry()) {
                    log.warn("消息重试次数已达上限，放弃重试: {}", message.getMessageId());
                    message.setProcessStatus(BotMessage.ProcessStatus.FAILED);
                    return true;
                }
                
                // 重新发送消息
                switch (message.getMessageType()) {
                    case COMMAND -> botMessageQueue.sendCommand(message);
                    case NOTIFICATION -> botMessageQueue.sendNotification(message);
                    default -> botMessageQueue.sendMessage(message);
                }
                
                log.info("重试发送消息成功: {}", message.getMessageId());
                return true; // 从重试队列中移除
                
            } catch (Exception e) {
                log.error("重试发送消息失败: {}", message.getMessageId(), e);
                message.incrementRetryCount();
                return false; // 保留在重试队列中
            }
        });
    }

    /**
     * 获取消息统计信息
     */
    public Map<String, Object> getMessageStatistics() {
        Map<String, Object> stats = new ConcurrentHashMap<>();
        stats.put("totalMessages", totalMessages.get());
        stats.put("successMessages", successMessages.get());
        stats.put("failedMessages", failedMessages.get());
        stats.put("retryQueueSize", retryQueue.size());
        stats.put("platformStats", platformStats);
        
        // 计算成功率
        long total = totalMessages.get();
        if (total > 0) {
            double successRate = (double) successMessages.get() / total * 100;
            stats.put("successRate", String.format("%.2f%%", successRate));
        } else {
            stats.put("successRate", "0.00%");
        }
        
        return stats;
    }

    /**
     * 清理过期的重试消息
     */
    @Scheduled(fixedDelay = 300000) // 每5分钟执行一次
    public void cleanupExpiredRetryMessages() {
        LocalDateTime expireTime = LocalDateTime.now().minusHours(1); // 1小时过期
        
        retryQueue.values().removeIf(message -> {
            if (message.getTimestamp().isBefore(expireTime)) {
                log.info("清理过期重试消息: {}", message.getMessageId());
                return true;
            }
            return false;
        });
    }

    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        totalMessages.set(0);
        successMessages.set(0);
        failedMessages.set(0);
        platformStats.clear();
        log.info("消息统计信息已重置");
    }

    /**
     * 生成消息ID
     */
    private String generateMessageId() {
        return "msg_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }

    /**
     * 标记消息处理成功
     */
    public void markMessageSuccess(String messageId) {
        successMessages.incrementAndGet();
        retryQueue.remove(messageId);
    }

    /**
     * 标记消息处理失败
     */
    public void markMessageFailed(String messageId, String errorMessage) {
        failedMessages.incrementAndGet();
        BotMessage message = retryQueue.get(messageId);
        if (message != null) {
            message.setProcessStatus(BotMessage.ProcessStatus.FAILED);
            message.setErrorMessage(errorMessage);
        }
    }

    /**
     * 获取重试队列状态
     */
    public Map<String, Object> getRetryQueueStatus() {
        Map<String, Object> status = new ConcurrentHashMap<>();
        status.put("queueSize", retryQueue.size());
        status.put("messages", retryQueue.values().stream()
            .map(msg -> Map.of(
                "messageId", msg.getMessageId(),
                "platform", msg.getPlatform(),
                "retryCount", msg.getRetryCount(),
                "timestamp", msg.getTimestamp()
            ))
            .toList());
        return status;
    }
}
