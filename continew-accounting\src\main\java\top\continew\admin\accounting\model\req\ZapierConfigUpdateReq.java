package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.io.Serial;

/**
 * Zapier配置更新请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "Zapier配置更新请求")
public class ZapierConfigUpdateReq extends ZapierConfigReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 配置名称
     */
    @Schema(description = "配置名称", example = "交易同步到Google Sheets")
    @Size(max = 100, message = "配置名称长度不能超过100个字符")
    private String name;

    /**
     * 配置描述
     */
    @Schema(description = "配置描述", example = "自动将新交易同步到Google Sheets表格")
    @Size(max = 500, message = "配置描述长度不能超过500个字符")
    private String description;

    /**
     * Webhook URL
     */
    @Schema(description = "Webhook URL", example = "https://hooks.zapier.com/hooks/catch/123456/abcdef/")
    @Size(max = 500, message = "Webhook URL长度不能超过500个字符")
    private String webhookUrl;

    /**
     * 触发器类型
     */
    @Schema(description = "触发器类型", example = "TRANSACTION_CREATED")
    private String triggerType;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    /**
     * 最大重试次数
     */
    @Schema(description = "最大重试次数", example = "3")
    @Min(value = 0, message = "最大重试次数不能小于0")
    @Max(value = 10, message = "最大重试次数不能大于10")
    private Integer maxRetries;

    /**
     * 超时时间（秒）
     */
    @Schema(description = "超时时间（秒）", example = "30")
    @Min(value = 5, message = "超时时间不能小于5秒")
    @Max(value = 300, message = "超时时间不能大于300秒")
    private Integer timeoutSeconds;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "用于自动化工作流")
    @Size(max = 1000, message = "备注长度不能超过1000个字符")
    private String remark;
}
