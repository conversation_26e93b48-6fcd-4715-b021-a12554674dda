package top.continew.admin.bot.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.continew.admin.accounting.enums.PlatformType;
import top.continew.admin.bot.model.dto.CommandExecutionResult;
import top.continew.admin.bot.model.dto.ParsedCommand;
import top.continew.admin.bot.model.entity.CommandHistoryDO;
import top.continew.admin.bot.mapper.CommandHistoryMapper;
import top.continew.starter.core.util.validate.CheckUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 命令历史记录服务
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CommandHistoryService {

    private final CommandHistoryMapper commandHistoryMapper;

    /**
     * 记录命令执行历史
     */
    public void recordCommandExecution(String originalCommand, ParsedCommand parsedCommand, 
                                     CommandExecutionResult result, PlatformType platform, 
                                     Long groupId, Long userId) {
        try {
            CommandHistoryDO history = new CommandHistoryDO();
            history.setUserId(userId);
            history.setGroupId(groupId);
            history.setPlatform(platform);
            history.setOriginalCommand(originalCommand);
            history.setSuccess(result.getSuccess());
            history.setErrorMessage(result.getSuccess() ? null : result.getMessage());
            history.setExecutionTime(result.getExecutionTime());
            history.setExecutionDuration(result.getExecutionDuration());
            history.setBusinessId(result.getBusinessId());
            
            if (parsedCommand != null) {
                history.setTransactionType(parsedCommand.getType());
                history.setAmount(parsedCommand.getAmount());
                history.setCurrency(parsedCommand.getCurrency());
                history.setDescription(parsedCommand.getDescription());
                history.setCategory(parsedCommand.getCategory());
                history.setConfidenceScore(parsedCommand.getConfidenceScore());
            }
            
            commandHistoryMapper.insert(history);
            
        } catch (Exception e) {
            log.error("记录命令历史失败: {}", originalCommand, e);
        }
    }

    /**
     * 获取用户命令历史
     */
    public List<CommandHistoryDO> getUserCommandHistory(Long userId, Long groupId, int limit) {
        CheckUtils.throwIfNull(userId, "用户ID不能为空");
        return commandHistoryMapper.selectUserHistory(userId, groupId, limit);
    }

    /**
     * 获取群组命令历史
     */
    public List<CommandHistoryDO> getGroupCommandHistory(Long groupId, int limit) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        return commandHistoryMapper.selectGroupHistory(groupId, limit);
    }

    /**
     * 获取命令统计信息
     */
    public Map<String, Object> getCommandStatistics(Long userId, Long groupId, LocalDateTime startTime, LocalDateTime endTime) {
        return commandHistoryMapper.selectCommandStatistics(userId, groupId, startTime, endTime);
    }

    /**
     * 获取最常用的命令
     */
    public List<String> getMostUsedCommands(Long userId, Long groupId, int limit) {
        return commandHistoryMapper.selectMostUsedCommands(userId, groupId, limit);
    }

    /**
     * 获取错误命令统计
     */
    public List<CommandHistoryDO> getFailedCommands(Long userId, Long groupId, int limit) {
        return commandHistoryMapper.selectFailedCommands(userId, groupId, limit);
    }

    /**
     * 清理过期历史记录
     */
    public int cleanExpiredHistory(int daysToKeep) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(daysToKeep);
        return commandHistoryMapper.deleteExpiredHistory(cutoffTime);
    }

    /**
     * 获取用户命令使用趋势
     */
    public Map<String, Long> getCommandUsageTrend(Long userId, Long groupId, int days) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);
        List<CommandHistoryDO> history = commandHistoryMapper.selectHistoryByTimeRange(userId, groupId, startTime, LocalDateTime.now());
        
        return history.stream()
            .collect(Collectors.groupingBy(
                h -> h.getExecutionTime().toLocalDate().toString(),
                Collectors.counting()
            ));
    }

    /**
     * 获取平台使用统计
     */
    public Map<PlatformType, Long> getPlatformUsageStatistics(Long userId, Long groupId, int days) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);
        List<CommandHistoryDO> history = commandHistoryMapper.selectHistoryByTimeRange(userId, groupId, startTime, LocalDateTime.now());
        
        return history.stream()
            .collect(Collectors.groupingBy(
                CommandHistoryDO::getPlatform,
                Collectors.counting()
            ));
    }

    /**
     * 获取成功率统计
     */
    public Map<String, Object> getSuccessRateStatistics(Long userId, Long groupId, int days) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);
        List<CommandHistoryDO> history = commandHistoryMapper.selectHistoryByTimeRange(userId, groupId, startTime, LocalDateTime.now());
        
        long totalCommands = history.size();
        long successfulCommands = history.stream()
            .mapToLong(h -> h.getSuccess() ? 1 : 0)
            .sum();
        
        double successRate = totalCommands > 0 ? (double) successfulCommands / totalCommands * 100 : 0;
        
        return Map.of(
            "totalCommands", totalCommands,
            "successfulCommands", successfulCommands,
            "failedCommands", totalCommands - successfulCommands,
            "successRate", successRate
        );
    }

    /**
     * 获取平均执行时间
     */
    public Map<String, Object> getAverageExecutionTime(Long userId, Long groupId, int days) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);
        List<CommandHistoryDO> history = commandHistoryMapper.selectHistoryByTimeRange(userId, groupId, startTime, LocalDateTime.now());
        
        double avgExecutionTime = history.stream()
            .filter(h -> h.getExecutionDuration() != null)
            .mapToLong(CommandHistoryDO::getExecutionDuration)
            .average()
            .orElse(0.0);
        
        long maxExecutionTime = history.stream()
            .filter(h -> h.getExecutionDuration() != null)
            .mapToLong(CommandHistoryDO::getExecutionDuration)
            .max()
            .orElse(0L);
        
        long minExecutionTime = history.stream()
            .filter(h -> h.getExecutionDuration() != null)
            .mapToLong(CommandHistoryDO::getExecutionDuration)
            .min()
            .orElse(0L);
        
        return Map.of(
            "averageExecutionTime", avgExecutionTime,
            "maxExecutionTime", maxExecutionTime,
            "minExecutionTime", minExecutionTime
        );
    }

    /**
     * 获取命令复杂度分析
     */
    public Map<String, Object> getCommandComplexityAnalysis(Long userId, Long groupId, int days) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);
        List<CommandHistoryDO> history = commandHistoryMapper.selectHistoryByTimeRange(userId, groupId, startTime, LocalDateTime.now());
        
        double avgConfidenceScore = history.stream()
            .filter(h -> h.getConfidenceScore() != null)
            .mapToDouble(CommandHistoryDO::getConfidenceScore)
            .average()
            .orElse(0.0);
        
        long simpleCommands = history.stream()
            .filter(h -> h.getConfidenceScore() != null && h.getConfidenceScore() >= 80.0)
            .count();
        
        long complexCommands = history.stream()
            .filter(h -> h.getConfidenceScore() != null && h.getConfidenceScore() < 60.0)
            .count();
        
        return Map.of(
            "averageConfidenceScore", avgConfidenceScore,
            "simpleCommands", simpleCommands,
            "complexCommands", complexCommands,
            "totalAnalyzedCommands", history.stream().filter(h -> h.getConfidenceScore() != null).count()
        );
    }
}
