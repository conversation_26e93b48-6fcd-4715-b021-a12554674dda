package top.continew.admin.accounting.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 通知状态枚举
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Getter
@RequiredArgsConstructor
public enum NotificationStatusEnum {

    /**
     * 待发送
     */
    PENDING("PENDING", "待发送"),

    /**
     * 发送中
     */
    SENDING("SENDING", "发送中"),

    /**
     * 发送成功
     */
    SENT("SENT", "发送成功"),

    /**
     * 发送失败
     */
    FAILED("FAILED", "发送失败"),

    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消"),

    /**
     * 已过期
     */
    EXPIRED("EXPIRED", "已过期"),

    /**
     * 重试中
     */
    RETRYING("RETRYING", "重试中"),

    /**
     * 部分成功
     */
    PARTIAL_SUCCESS("PARTIAL_SUCCESS", "部分成功");

    /**
     * 状态代码
     */
    private final String code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 根据代码获取枚举
     *
     * @param code 状态代码
     * @return 通知状态枚举
     */
    public static NotificationStatusEnum getByCode(String code) {
        for (NotificationStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 是否为最终状态
     *
     * @return 是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == SENT || this == FAILED || this == CANCELLED || this == EXPIRED || this == PARTIAL_SUCCESS;
    }

    /**
     * 是否为成功状态
     *
     * @return 是否为成功状态
     */
    public boolean isSuccessStatus() {
        return this == SENT || this == PARTIAL_SUCCESS;
    }

    /**
     * 是否为失败状态
     *
     * @return 是否为失败状态
     */
    public boolean isFailureStatus() {
        return this == FAILED || this == CANCELLED || this == EXPIRED;
    }

    /**
     * 是否可以重试
     *
     * @return 是否可以重试
     */
    public boolean canRetry() {
        return this == FAILED || this == RETRYING;
    }

}
