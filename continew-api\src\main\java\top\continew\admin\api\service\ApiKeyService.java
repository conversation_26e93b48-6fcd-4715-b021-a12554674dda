package top.continew.admin.api.service;

import top.continew.admin.api.model.entity.ApiKey;

/**
 * API密钥服务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface ApiKeyService {

    /**
     * 验证API密钥
     */
    ApiKey validateApiKey(String apiKey, String apiSecret);

    /**
     * 检查IP是否允许
     */
    boolean isIpAllowed(ApiKey apiKey, String clientIp);

    /**
     * 检查是否有权限
     */
    boolean hasPermission(ApiKey apiKey, String requestPath, String method);

    /**
     * 检查速率限制
     */
    boolean checkRateLimit(ApiKey apiKey, String clientIp);

    /**
     * 记录API调用
     */
    void recordApiCall(ApiKey apiKey, String requestPath, String method, String clientIp);

    /**
     * 生成API密钥
     */
    ApiKey generateApiKey(Long userId, String appName, String description);

    /**
     * 禁用API密钥
     */
    void disableApiKey(Long apiKeyId);

    /**
     * 启用API密钥
     */
    void enableApiKey(Long apiKeyId);

    /**
     * 更新API密钥权限
     */
    void updateApiKeyScopes(Long apiKeyId, String scopes);

    /**
     * 更新IP白名单
     */
    void updateIpWhitelist(Long apiKeyId, String ipWhitelist);

    /**
     * 更新速率限制
     */
    void updateRateLimit(Long apiKeyId, Integer rateLimit);
}
