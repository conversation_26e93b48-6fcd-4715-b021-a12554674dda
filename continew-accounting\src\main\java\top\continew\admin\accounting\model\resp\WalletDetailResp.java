package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 钱包详情响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "钱包详情响应")
public class WalletDetailResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Schema(description = "ID", example = "1")
    private Long id;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 群组名称
     */
    @Schema(description = "群组名称", example = "我的记账群")
    private String groupName;

    /**
     * 币种
     */
    @Schema(description = "币种", example = "CNY")
    private String currency;

    /**
     * 余额
     */
    @Schema(description = "余额", example = "1000.00")
    private BigDecimal balance;

    /**
     * 冻结金额
     */
    @Schema(description = "冻结金额", example = "100.00")
    private BigDecimal frozenAmount;

    /**
     * 可用余额
     */
    @Schema(description = "可用余额", example = "900.00")
    private BigDecimal availableBalance;

    /**
     * 最后更新时间
     */
    @Schema(description = "最后更新时间", example = "2025-01-01T12:00:00")
    private LocalDateTime lastUpdateTime;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID", example = "1")
    private Long createUser;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名", example = "张三")
    private String createUserName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-01-01T10:00:00")
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID", example = "1")
    private Long updateUser;

    /**
     * 更新人姓名
     */
    @Schema(description = "更新人姓名", example = "李四")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2025-01-01T13:00:00")
    private LocalDateTime updateTime;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "1")
    private Integer status;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "群组主钱包")
    private String remark;
}
