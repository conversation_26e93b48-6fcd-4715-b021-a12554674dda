<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>top.continew.admin</groupId>
        <artifactId>continew-admin</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>continew-bot</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>机器人集成模块</description>

    <dependencies>
        <!-- 记账核心模块 -->
        <dependency>
            <groupId>top.continew.admin</groupId>
            <artifactId>continew-accounting</artifactId>
        </dependency>

        <!-- 通用模块 -->
        <dependency>
            <groupId>top.continew.admin</groupId>
            <artifactId>continew-common</artifactId>
        </dependency>

        <!-- ContiNew Starter Web -->
        <dependency>
            <groupId>top.continew.starter</groupId>
            <artifactId>continew-starter-web</artifactId>
        </dependency>

        <!-- ContiNew Starter 消息队列 -->
        <dependency>
            <groupId>top.continew.starter</groupId>
            <artifactId>continew-starter-messaging-rabbitmq</artifactId>
        </dependency>

        <!-- Telegram Bot API -->
        <dependency>
            <groupId>org.telegram</groupId>
            <artifactId>telegrambots</artifactId>
            <version>6.8.0</version>
        </dependency>

        <!-- Discord JDA -->
        <dependency>
            <groupId>net.dv8tion</groupId>
            <artifactId>JDA</artifactId>
            <version>5.0.0-beta.18</version>
        </dependency>

        <!-- 微信SDK -->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-mp</artifactId>
            <version>4.5.0</version>
        </dependency>

        <!-- 钉钉SDK -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibaba-dingtalk-service-sdk</artifactId>
            <version>2.0.0</version>
        </dependency>

        <!-- 飞书SDK -->
        <dependency>
            <groupId>com.larksuite.oapi</groupId>
            <artifactId>oapi-sdk</artifactId>
            <version>2.0.19</version>
        </dependency>

        <!-- 正则表达式增强 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <!-- JSON处理 -->
        <dependency>
            <groupId>top.continew.starter</groupId>
            <artifactId>continew-starter-json-jackson</artifactId>
        </dependency>
    </dependencies>
</project>
