package top.continew.admin.api.filter;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import top.continew.admin.api.model.entity.ApiKey;
import top.continew.admin.api.service.ApiKeyService;
import top.continew.starter.web.model.R;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;

/**
 * API认证过滤器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ApiAuthenticationFilter extends OncePerRequestFilter {

    private static final String API_KEY_HEADER = "X-API-Key";
    private static final String API_SECRET_HEADER = "X-API-Secret";
    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String BEARER_PREFIX = "Bearer ";

    private final ApiKeyService apiKeyService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        // 跳过公开接口
        String requestURI = request.getRequestURI();
        if (isPublicEndpoint(requestURI)) {
            filterChain.doFilter(request, response);
            return;
        }

        try {
            // 尝试从请求头获取API密钥
            String apiKey = getApiKey(request);
            String apiSecret = getApiSecret(request);

            if (StrUtil.isBlank(apiKey)) {
                handleAuthenticationFailure(response, "API密钥不能为空");
                return;
            }

            // 验证API密钥
            ApiKey apiKeyEntity = apiKeyService.validateApiKey(apiKey, apiSecret);
            if (apiKeyEntity == null) {
                handleAuthenticationFailure(response, "API密钥无效或已过期");
                return;
            }

            // 检查API密钥状态
            if (!apiKeyEntity.getIsActive()) {
                handleAuthenticationFailure(response, "API密钥已被禁用");
                return;
            }

            // 检查过期时间
            if (apiKeyEntity.getExpiresAt() != null && LocalDateTime.now().isAfter(apiKeyEntity.getExpiresAt())) {
                handleAuthenticationFailure(response, "API密钥已过期");
                return;
            }

            // 检查IP白名单
            if (!apiKeyService.isIpAllowed(apiKeyEntity, getClientIp(request))) {
                handleAuthenticationFailure(response, "IP地址不在白名单中");
                return;
            }

            // 检查权限范围
            if (!apiKeyService.hasPermission(apiKeyEntity, requestURI, request.getMethod())) {
                handleAuthenticationFailure(response, "没有访问权限");
                return;
            }

            // 检查速率限制
            if (!apiKeyService.checkRateLimit(apiKeyEntity, getClientIp(request))) {
                handleAuthenticationFailure(response, "请求频率超过限制");
                return;
            }

            // 设置认证信息
            UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                apiKeyEntity.getUserId(), null, List.of()
            );
            authentication.setDetails(apiKeyEntity);
            SecurityContextHolder.getContext().setAuthentication(authentication);

            // 记录API调用
            apiKeyService.recordApiCall(apiKeyEntity, requestURI, request.getMethod(), getClientIp(request));

            filterChain.doFilter(request, response);

        } catch (Exception e) {
            log.error("API认证过程中发生错误", e);
            handleAuthenticationFailure(response, "认证失败");
        }
    }

    /**
     * 获取API密钥
     */
    private String getApiKey(HttpServletRequest request) {
        // 优先从Header获取
        String apiKey = request.getHeader(API_KEY_HEADER);
        if (StrUtil.isNotBlank(apiKey)) {
            return apiKey;
        }

        // 从Authorization Header获取
        String authorization = request.getHeader(AUTHORIZATION_HEADER);
        if (StrUtil.isNotBlank(authorization) && authorization.startsWith(BEARER_PREFIX)) {
            return authorization.substring(BEARER_PREFIX.length());
        }

        // 从查询参数获取
        return request.getParameter("api_key");
    }

    /**
     * 获取API密钥
     */
    private String getApiSecret(HttpServletRequest request) {
        // 从Header获取
        String apiSecret = request.getHeader(API_SECRET_HEADER);
        if (StrUtil.isNotBlank(apiSecret)) {
            return apiSecret;
        }

        // 从查询参数获取
        return request.getParameter("api_secret");
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StrUtil.isNotBlank(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (StrUtil.isNotBlank(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * 判断是否为公开接口
     */
    private boolean isPublicEndpoint(String requestURI) {
        return requestURI.startsWith("/api/v1/auth/") ||
               requestURI.startsWith("/api/v1/public/") ||
               requestURI.startsWith("/api/docs/") ||
               requestURI.startsWith("/api/swagger-ui/");
    }

    /**
     * 处理认证失败
     */
    private void handleAuthenticationFailure(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());

        R<Void> result = R.fail(HttpStatus.UNAUTHORIZED.value(), message);
        response.getWriter().write(JSONUtil.toJsonStr(result));
    }
}
