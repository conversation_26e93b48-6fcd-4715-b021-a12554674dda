package top.continew.admin.accounting.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.base.IBaseEnum;

/**
 * 交易类型枚举
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Getter
@RequiredArgsConstructor
public enum TransactionType implements IBaseEnum<String> {

    /**
     * 收入
     */
    INCOME("INCOME", "收入", "+"),

    /**
     * 支出
     */
    EXPENSE("EXPENSE", "支出", "-"),

    /**
     * 转账
     */
    TRANSFER("TRANSFER", "转账", "→");

    private final String value;
    private final String description;
    private final String symbol;

    /**
     * 根据符号判断交易类型
     */
    public static TransactionType fromSymbol(String symbol) {
        return switch (symbol) {
            case "+", "收入" -> INCOME;
            case "-", "支出" -> EXPENSE;
            case "→", "转账" -> TRANSFER;
            default -> null;
        };
    }

    /**
     * 判断是否为正向金额
     */
    public boolean isPositive() {
        return this == INCOME;
    }

    /**
     * 判断是否为负向金额
     */
    public boolean isNegative() {
        return this == EXPENSE;
    }
}
