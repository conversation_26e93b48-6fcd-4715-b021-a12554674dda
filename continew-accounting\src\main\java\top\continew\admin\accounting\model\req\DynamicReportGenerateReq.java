package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 动态报表生成请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "动态报表生成请求")
public class DynamicReportGenerateReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模板ID
     */
    @Schema(description = "模板ID", example = "1")
    @NotNull(message = "模板ID不能为空")
    private Long templateId;

    /**
     * 报表名称
     */
    @Schema(description = "报表名称", example = "2025年1月财务报表")
    @NotBlank(message = "报表名称不能为空")
    @Size(max = 200, message = "报表名称长度不能超过200个字符")
    private String reportName;

    /**
     * 报表描述
     */
    @Schema(description = "报表描述", example = "2025年1月份的详细财务分析报表")
    @Size(max = 500, message = "报表描述长度不能超过500个字符")
    private String reportDescription;

    /**
     * 过滤器参数
     */
    @Schema(description = "过滤器参数")
    private Map<String, Object> filterParams;

    /**
     * 动态参数
     */
    @Schema(description = "动态参数")
    private Map<String, Object> dynamicParams;

    /**
     * 导出格式
     */
    @Schema(description = "导出格式", example = "PDF", allowableValues = {"PDF", "EXCEL", "CSV", "HTML", "JSON"})
    private String exportFormat = "PDF";

    /**
     * 是否异步生成
     */
    @Schema(description = "是否异步生成", example = "false")
    private Boolean async = false;

    /**
     * 生成选项
     */
    @Schema(description = "生成选项")
    private GenerationOptions generationOptions;

    /**
     * 数据范围配置
     */
    @Schema(description = "数据范围配置")
    private DataRangeConfig dataRangeConfig;

    /**
     * 输出配置
     */
    @Schema(description = "输出配置")
    private OutputConfig outputConfig;

    /**
     * 通知配置
     */
    @Schema(description = "通知配置")
    private NotificationConfig notificationConfig;

    /**
     * 缓存配置
     */
    @Schema(description = "缓存配置")
    private CacheConfig cacheConfig;

    /**
     * 优先级
     */
    @Schema(description = "优先级", example = "NORMAL", allowableValues = {"LOW", "NORMAL", "HIGH", "URGENT"})
    private String priority = "NORMAL";

    /**
     * 超时时间（秒）
     */
    @Schema(description = "超时时间（秒）", example = "300")
    @Min(value = 30, message = "超时时间不能少于30秒")
    @Max(value = 3600, message = "超时时间不能超过3600秒")
    private Integer timeoutSeconds = 300;

    /**
     * 标签
     */
    @Schema(description = "标签", example = "[\"月报\", \"财务\"]")
    private List<String> tags;

    /**
     * 扩展属性
     */
    @Schema(description = "扩展属性")
    private Map<String, Object> attributes;

    /**
     * 生成选项
     */
    @Data
    @Schema(description = "生成选项")
    public static class GenerationOptions implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 是否包含图表
         */
        @Schema(description = "是否包含图表", example = "true")
        private Boolean includeCharts = true;

        /**
         * 是否包含数据表
         */
        @Schema(description = "是否包含数据表", example = "true")
        private Boolean includeTables = true;

        /**
         * 是否包含汇总信息
         */
        @Schema(description = "是否包含汇总信息", example = "true")
        private Boolean includeSummary = true;

        /**
         * 是否包含详细数据
         */
        @Schema(description = "是否包含详细数据", example = "false")
        private Boolean includeDetailData = false;

        /**
         * 是否包含趋势分析
         */
        @Schema(description = "是否包含趋势分析", example = "true")
        private Boolean includeTrendAnalysis = true;

        /**
         * 是否包含对比分析
         */
        @Schema(description = "是否包含对比分析", example = "false")
        private Boolean includeComparison = false;

        /**
         * 是否包含预测分析
         */
        @Schema(description = "是否包含预测分析", example = "false")
        private Boolean includeForecast = false;

        /**
         * 数据精度
         */
        @Schema(description = "数据精度", example = "2")
        @Min(value = 0, message = "数据精度不能小于0")
        @Max(value = 6, message = "数据精度不能大于6")
        private Integer dataPrecision = 2;

        /**
         * 图表质量
         */
        @Schema(description = "图表质量", example = "HIGH", allowableValues = {"LOW", "MEDIUM", "HIGH", "ULTRA"})
        private String chartQuality = "HIGH";

        /**
         * 是否压缩输出
         */
        @Schema(description = "是否压缩输出", example = "false")
        private Boolean compressOutput = false;

        /**
         * 水印设置
         */
        @Schema(description = "水印设置")
        private WatermarkSettings watermark;

        @Data
        @Schema(description = "水印设置")
        public static class WatermarkSettings implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 是否启用水印
             */
            @Schema(description = "是否启用水印", example = "false")
            private Boolean enabled = false;

            /**
             * 水印文本
             */
            @Schema(description = "水印文本", example = "机密文档")
            private String text;

            /**
             * 水印位置
             */
            @Schema(description = "水印位置", example = "CENTER", allowableValues = {"TOP_LEFT", "TOP_RIGHT", "CENTER", "BOTTOM_LEFT", "BOTTOM_RIGHT"})
            private String position = "CENTER";

            /**
             * 水印透明度
             */
            @Schema(description = "水印透明度", example = "0.3")
            @DecimalMin(value = "0.1", message = "水印透明度不能小于0.1")
            @DecimalMax(value = "1.0", message = "水印透明度不能大于1.0")
            private Double opacity = 0.3;
        }
    }

    /**
     * 数据范围配置
     */
    @Data
    @Schema(description = "数据范围配置")
    public static class DataRangeConfig implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 开始时间
         */
        @Schema(description = "开始时间", example = "2025-01-01 00:00:00")
        private LocalDateTime startTime;

        /**
         * 结束时间
         */
        @Schema(description = "结束时间", example = "2025-01-31 23:59:59")
        private LocalDateTime endTime;

        /**
         * 数据限制
         */
        @Schema(description = "数据限制", example = "10000")
        @Min(value = 1, message = "数据限制不能小于1")
        @Max(value = 100000, message = "数据限制不能大于100000")
        private Integer dataLimit = 10000;

        /**
         * 采样策略
         */
        @Schema(description = "采样策略", example = "NONE", allowableValues = {"NONE", "RANDOM", "SYSTEMATIC", "STRATIFIED"})
        private String samplingStrategy = "NONE";

        /**
         * 采样比例
         */
        @Schema(description = "采样比例", example = "1.0")
        @DecimalMin(value = "0.01", message = "采样比例不能小于0.01")
        @DecimalMax(value = "1.0", message = "采样比例不能大于1.0")
        private Double samplingRatio = 1.0;

        /**
         * 数据过滤条件
         */
        @Schema(description = "数据过滤条件")
        private Map<String, Object> filterConditions;

        /**
         * 排序配置
         */
        @Schema(description = "排序配置")
        private List<SortConfig> sortConfigs;

        @Data
        @Schema(description = "排序配置")
        public static class SortConfig implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 排序字段
             */
            @Schema(description = "排序字段", example = "transaction_date")
            private String field;

            /**
             * 排序方向
             */
            @Schema(description = "排序方向", example = "DESC", allowableValues = {"ASC", "DESC"})
            private String direction = "ASC";

            /**
             * 排序优先级
             */
            @Schema(description = "排序优先级", example = "1")
            private Integer priority = 1;
        }
    }

    /**
     * 输出配置
     */
    @Data
    @Schema(description = "输出配置")
    public static class OutputConfig implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 文件名模板
         */
        @Schema(description = "文件名模板", example = "{reportName}_{date}")
        private String fileNameTemplate;

        /**
         * 输出路径
         */
        @Schema(description = "输出路径", example = "/reports/financial/")
        private String outputPath;

        /**
         * 是否保存到服务器
         */
        @Schema(description = "是否保存到服务器", example = "true")
        private Boolean saveToServer = true;

        /**
         * 是否发送邮件
         */
        @Schema(description = "是否发送邮件", example = "false")
        private Boolean sendEmail = false;

        /**
         * 邮件接收人
         */
        @Schema(description = "邮件接收人")
        private List<String> emailRecipients;

        /**
         * 是否上传到云存储
         */
        @Schema(description = "是否上传到云存储", example = "false")
        private Boolean uploadToCloud = false;

        /**
         * 云存储配置
         */
        @Schema(description = "云存储配置")
        private Map<String, Object> cloudStorageConfig;

        /**
         * 文件保留天数
         */
        @Schema(description = "文件保留天数", example = "30")
        @Min(value = 1, message = "文件保留天数不能小于1")
        @Max(value = 365, message = "文件保留天数不能大于365")
        private Integer retentionDays = 30;

        /**
         * 访问权限
         */
        @Schema(description = "访问权限", example = "PRIVATE", allowableValues = {"PRIVATE", "GROUP", "PUBLIC"})
        private String accessPermission = "PRIVATE";
    }

    /**
     * 通知配置
     */
    @Data
    @Schema(description = "通知配置")
    public static class NotificationConfig implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 是否启用通知
         */
        @Schema(description = "是否启用通知", example = "true")
        private Boolean enabled = true;

        /**
         * 通知方式
         */
        @Schema(description = "通知方式", example = "[\"SYSTEM\", \"EMAIL\"]")
        private List<String> notificationMethods;

        /**
         * 通知接收人
         */
        @Schema(description = "通知接收人")
        private List<Long> recipients;

        /**
         * 成功通知模板
         */
        @Schema(description = "成功通知模板", example = "报表 {reportName} 生成成功")
        private String successTemplate;

        /**
         * 失败通知模板
         */
        @Schema(description = "失败通知模板", example = "报表 {reportName} 生成失败：{errorMessage}")
        private String failureTemplate;

        /**
         * 是否通知开始
         */
        @Schema(description = "是否通知开始", example = "false")
        private Boolean notifyStart = false;

        /**
         * 是否通知进度
         */
        @Schema(description = "是否通知进度", example = "false")
        private Boolean notifyProgress = false;

        /**
         * 进度通知间隔（秒）
         */
        @Schema(description = "进度通知间隔（秒）", example = "30")
        private Integer progressNotificationInterval = 30;
    }

    /**
     * 缓存配置
     */
    @Data
    @Schema(description = "缓存配置")
    public static class CacheConfig implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 是否启用缓存
         */
        @Schema(description = "是否启用缓存", example = "true")
        private Boolean enabled = true;

        /**
         * 缓存策略
         */
        @Schema(description = "缓存策略", example = "SMART", allowableValues = {"NONE", "SIMPLE", "SMART", "AGGRESSIVE"})
        private String cacheStrategy = "SMART";

        /**
         * 缓存时间（分钟）
         */
        @Schema(description = "缓存时间（分钟）", example = "60")
        @Min(value = 1, message = "缓存时间不能小于1分钟")
        @Max(value = 1440, message = "缓存时间不能大于1440分钟")
        private Integer cacheDurationMinutes = 60;

        /**
         * 缓存键
         */
        @Schema(description = "缓存键")
        private String cacheKey;

        /**
         * 是否强制刷新缓存
         */
        @Schema(description = "是否强制刷新缓存", example = "false")
        private Boolean forceRefresh = false;

        /**
         * 缓存标签
         */
        @Schema(description = "缓存标签")
        private List<String> cacheTags;
    }
}
