package top.continew.admin.accounting.service.sync.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import top.continew.admin.accounting.service.sync.DataSourceAdapter;
import top.continew.starter.core.exception.BusinessException;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 数据库适配器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DatabaseAdapter implements DataSourceAdapter {

    private final JdbcTemplate jdbcTemplate;

    @Override
    public String getAdapterType() {
        return "DATABASE";
    }

    @Override
    public Map<String, Object> testConnection(Map<String, Object> config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String tableName = (String) config.get("tableName");
            if (StrUtil.isBlank(tableName)) {
                throw new BusinessException("表名不能为空");
            }
            
            // 测试表是否存在
            String sql = "SELECT COUNT(*) FROM " + tableName + " LIMIT 1";
            jdbcTemplate.queryForObject(sql, Integer.class);
            
            result.put("success", true);
            result.put("message", "数据库连接测试成功");
            result.put("testTime", LocalDateTime.now());
            
        } catch (Exception e) {
            log.error("数据库连接测试失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "数据库连接测试失败: " + e.getMessage());
            result.put("testTime", LocalDateTime.now());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> validateConfig(Map<String, Object> config) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        
        String tableName = (String) config.get("tableName");
        if (StrUtil.isBlank(tableName)) {
            errors.add("表名不能为空");
        }
        
        result.put("valid", errors.isEmpty());
        result.put("errors", errors);
        result.put("validateTime", LocalDateTime.now());
        
        return result;
    }

    @Override
    public Map<String, Object> getDataStructure(Map<String, Object> config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String tableName = (String) config.get("tableName");
            
            // 获取表结构信息
            String sql = "SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT " +
                        "FROM INFORMATION_SCHEMA.COLUMNS " +
                        "WHERE TABLE_NAME = ? AND TABLE_SCHEMA = DATABASE()";
            
            List<Map<String, Object>> columns = jdbcTemplate.queryForList(sql, tableName);
            
            result.put("tableName", tableName);
            result.put("columns", columns);
            result.put("columnCount", columns.size());
            
        } catch (Exception e) {
            log.error("获取数据结构失败: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    @Override
    public List<Map<String, Object>> readData(Map<String, Object> config,
                                               Map<String, Object> fieldMapping,
                                               Map<String, Object> filterCondition,
                                               LocalDateTime lastSyncTime,
                                               Integer batchSize) {
        try {
            String tableName = (String) config.get("tableName");
            StringBuilder sql = new StringBuilder("SELECT * FROM ").append(tableName);
            List<Object> params = new ArrayList<>();
            
            // 构建WHERE条件
            List<String> conditions = new ArrayList<>();
            
            // 增量同步条件
            if (lastSyncTime != null) {
                String timestampField = (String) config.getOrDefault("timestampField", "update_time");
                conditions.add(timestampField + " > ?");
                params.add(lastSyncTime);
            }
            
            // 过滤条件
            if (filterCondition != null && !filterCondition.isEmpty()) {
                for (Map.Entry<String, Object> entry : filterCondition.entrySet()) {
                    conditions.add(entry.getKey() + " = ?");
                    params.add(entry.getValue());
                }
            }
            
            if (!conditions.isEmpty()) {
                sql.append(" WHERE ").append(String.join(" AND ", conditions));
            }
            
            // 排序
            String orderField = (String) config.getOrDefault("orderField", "id");
            sql.append(" ORDER BY ").append(orderField);
            
            // 限制数量
            if (batchSize != null && batchSize > 0) {
                sql.append(" LIMIT ").append(batchSize);
            }
            
            List<Map<String, Object>> data = jdbcTemplate.queryForList(sql.toString(), params.toArray());
            
            // 应用字段映射
            if (fieldMapping != null && !fieldMapping.isEmpty()) {
                data = applyFieldMapping(data, fieldMapping);
            }
            
            log.info("数据库读取数据成功: table={}, count={}", tableName, data.size());
            return data;
            
        } catch (Exception e) {
            log.error("数据库读取数据失败: {}", e.getMessage(), e);
            throw new BusinessException("数据库读取数据失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> writeData(Map<String, Object> config,
                                         Map<String, Object> fieldMapping,
                                         List<Map<String, Object>> data,
                                         String operationType) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String tableName = (String) config.get("tableName");
            int successCount = 0;
            int failedCount = 0;
            List<String> errors = new ArrayList<>();
            
            // 应用字段映射
            if (fieldMapping != null && !fieldMapping.isEmpty()) {
                data = reverseFieldMapping(data, fieldMapping);
            }
            
            for (Map<String, Object> record : data) {
                try {
                    switch (operationType) {
                        case "CREATE":
                            insertRecord(tableName, record);
                            break;
                        case "UPDATE":
                            updateRecord(tableName, record);
                            break;
                        case "DELETE":
                            deleteRecord(tableName, record);
                            break;
                        default:
                            throw new BusinessException("不支持的操作类型: " + operationType);
                    }
                    successCount++;
                } catch (Exception e) {
                    failedCount++;
                    errors.add("记录处理失败: " + e.getMessage());
                    log.error("记录处理失败: record={}, error={}", record, e.getMessage());
                }
            }
            
            result.put("success", true);
            result.put("totalCount", data.size());
            result.put("successCount", successCount);
            result.put("failedCount", failedCount);
            result.put("errors", errors);
            
            log.info("数据库写入数据完成: table={}, total={}, success={}, failed={}", 
                    tableName, data.size(), successCount, failedCount);
            
        } catch (Exception e) {
            log.error("数据库写入数据失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> detectChanges(Map<String, Object> config, LocalDateTime lastSyncTime) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String tableName = (String) config.get("tableName");
            String timestampField = (String) config.getOrDefault("timestampField", "update_time");
            
            String sql = "SELECT COUNT(*) FROM " + tableName + " WHERE " + timestampField + " > ?";
            Integer changeCount = jdbcTemplate.queryForObject(sql, Integer.class, lastSyncTime);
            
            result.put("hasChanges", changeCount != null && changeCount > 0);
            result.put("changeCount", changeCount != null ? changeCount : 0);
            result.put("lastCheckTime", LocalDateTime.now());
            
        } catch (Exception e) {
            log.error("检测数据变更失败: {}", e.getMessage(), e);
            result.put("hasChanges", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    @Override
    public Long getDataCount(Map<String, Object> config, Map<String, Object> filterCondition) {
        try {
            String tableName = (String) config.get("tableName");
            StringBuilder sql = new StringBuilder("SELECT COUNT(*) FROM ").append(tableName);
            List<Object> params = new ArrayList<>();
            
            // 构建WHERE条件
            if (filterCondition != null && !filterCondition.isEmpty()) {
                List<String> conditions = new ArrayList<>();
                for (Map.Entry<String, Object> entry : filterCondition.entrySet()) {
                    conditions.add(entry.getKey() + " = ?");
                    params.add(entry.getValue());
                }
                sql.append(" WHERE ").append(String.join(" AND ", conditions));
            }
            
            Integer count = jdbcTemplate.queryForObject(sql.toString(), Integer.class, params.toArray());
            return count != null ? count.longValue() : 0L;
            
        } catch (Exception e) {
            log.error("获取数据总数失败: {}", e.getMessage(), e);
            return 0L;
        }
    }

    @Override
    public Map<String, Object> suggestFieldMapping(Map<String, Object> sourceConfig, Map<String, Object> targetConfig) {
        Map<String, Object> mapping = new HashMap<>();
        
        try {
            // 获取源表和目标表的字段信息
            Map<String, Object> sourceStructure = getDataStructure(sourceConfig);
            Map<String, Object> targetStructure = getDataStructure(targetConfig);
            
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> sourceColumns = (List<Map<String, Object>>) sourceStructure.get("columns");
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> targetColumns = (List<Map<String, Object>>) targetStructure.get("columns");
            
            if (sourceColumns != null && targetColumns != null) {
                Map<String, String> suggestions = new HashMap<>();
                
                for (Map<String, Object> sourceColumn : sourceColumns) {
                    String sourceColumnName = (String) sourceColumn.get("COLUMN_NAME");
                    
                    // 寻找匹配的目标字段
                    for (Map<String, Object> targetColumn : targetColumns) {
                        String targetColumnName = (String) targetColumn.get("COLUMN_NAME");
                        
                        // 精确匹配
                        if (sourceColumnName.equals(targetColumnName)) {
                            suggestions.put(sourceColumnName, targetColumnName);
                            break;
                        }
                        
                        // 模糊匹配（忽略大小写和下划线）
                        if (sourceColumnName.toLowerCase().replace("_", "")
                                .equals(targetColumnName.toLowerCase().replace("_", ""))) {
                            suggestions.put(sourceColumnName, targetColumnName);
                            break;
                        }
                    }
                }
                
                mapping.put("suggestions", suggestions);
            }
            
        } catch (Exception e) {
            log.error("生成字段映射建议失败: {}", e.getMessage(), e);
            mapping.put("error", e.getMessage());
        }
        
        return mapping;
    }

    @Override
    public List<Map<String, Object>> executeCustomQuery(Map<String, Object> config, String query, Map<String, Object> params) {
        try {
            List<Object> paramList = new ArrayList<>();
            if (params != null) {
                paramList.addAll(params.values());
            }
            
            return jdbcTemplate.queryForList(query, paramList.toArray());
            
        } catch (Exception e) {
            log.error("执行自定义查询失败: query={}, error={}", query, e.getMessage(), e);
            throw new BusinessException("执行自定义查询失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getSyncProgress(Map<String, Object> config, String syncId) {
        // 数据库适配器不需要特殊的进度跟踪
        Map<String, Object> progress = new HashMap<>();
        progress.put("syncId", syncId);
        progress.put("progress", 100);
        progress.put("message", "数据库同步完成");
        return progress;
    }

    @Override
    public void cleanup(Map<String, Object> config, String syncId) {
        // 数据库适配器不需要特殊的清理操作
        log.debug("数据库适配器清理完成: syncId={}", syncId);
    }

    /**
     * 应用字段映射
     */
    private List<Map<String, Object>> applyFieldMapping(List<Map<String, Object>> data, Map<String, Object> fieldMapping) {
        if (CollUtil.isEmpty(data) || fieldMapping == null || fieldMapping.isEmpty()) {
            return data;
        }
        
        List<Map<String, Object>> mappedData = new ArrayList<>();
        
        for (Map<String, Object> record : data) {
            Map<String, Object> mappedRecord = new HashMap<>();
            
            for (Map.Entry<String, Object> entry : record.entrySet()) {
                String sourceField = entry.getKey();
                Object value = entry.getValue();
                
                // 查找映射的目标字段
                String targetField = (String) fieldMapping.get(sourceField);
                if (targetField != null) {
                    mappedRecord.put(targetField, value);
                } else {
                    mappedRecord.put(sourceField, value);
                }
            }
            
            mappedData.add(mappedRecord);
        }
        
        return mappedData;
    }

    /**
     * 反向字段映射
     */
    private List<Map<String, Object>> reverseFieldMapping(List<Map<String, Object>> data, Map<String, Object> fieldMapping) {
        if (CollUtil.isEmpty(data) || fieldMapping == null || fieldMapping.isEmpty()) {
            return data;
        }
        
        // 创建反向映射
        Map<String, String> reverseMapping = new HashMap<>();
        for (Map.Entry<String, Object> entry : fieldMapping.entrySet()) {
            reverseMapping.put((String) entry.getValue(), entry.getKey());
        }
        
        return applyFieldMapping(data, new HashMap<>(reverseMapping));
    }

    /**
     * 插入记录
     */
    private void insertRecord(String tableName, Map<String, Object> record) {
        if (record.isEmpty()) {
            return;
        }
        
        List<String> columns = new ArrayList<>(record.keySet());
        List<String> placeholders = new ArrayList<>();
        List<Object> values = new ArrayList<>();
        
        for (String column : columns) {
            placeholders.add("?");
            values.add(record.get(column));
        }
        
        String sql = "INSERT INTO " + tableName + " (" + String.join(", ", columns) + 
                    ") VALUES (" + String.join(", ", placeholders) + ")";
        
        jdbcTemplate.update(sql, values.toArray());
    }

    /**
     * 更新记录
     */
    private void updateRecord(String tableName, Map<String, Object> record) {
        if (record.isEmpty() || !record.containsKey("id")) {
            throw new BusinessException("更新记录必须包含ID字段");
        }
        
        Object id = record.get("id");
        Map<String, Object> updateFields = new HashMap<>(record);
        updateFields.remove("id");
        
        if (updateFields.isEmpty()) {
            return;
        }
        
        List<String> setClauses = new ArrayList<>();
        List<Object> values = new ArrayList<>();
        
        for (Map.Entry<String, Object> entry : updateFields.entrySet()) {
            setClauses.add(entry.getKey() + " = ?");
            values.add(entry.getValue());
        }
        
        values.add(id);
        
        String sql = "UPDATE " + tableName + " SET " + String.join(", ", setClauses) + " WHERE id = ?";
        jdbcTemplate.update(sql, values.toArray());
    }

    /**
     * 删除记录
     */
    private void deleteRecord(String tableName, Map<String, Object> record) {
        if (!record.containsKey("id")) {
            throw new BusinessException("删除记录必须包含ID字段");
        }
        
        Object id = record.get("id");
        String sql = "DELETE FROM " + tableName + " WHERE id = ?";
        jdbcTemplate.update(sql, id);
    }
}
