package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.admin.common.model.entity.BaseEntity;

import java.time.LocalDateTime;

/**
 * Google Sheets同步日志实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("acc_google_sheets_sync_log")
@Schema(description = "Google Sheets同步日志实体")
public class GoogleSheetsSyncLog extends BaseEntity {

    /**
     * 同步ID
     */
    @Schema(description = "同步ID", example = "SYNC_20250101_001")
    @TableField("sync_id")
    private String syncId;

    /**
     * 配置ID
     */
    @Schema(description = "配置ID", example = "1")
    @TableField("config_id")
    private Long configId;

    /**
     * 配置名称
     */
    @Schema(description = "配置名称", example = "我的记账表格")
    @TableField("config_name")
    private String configName;

    /**
     * Google Sheets ID
     */
    @Schema(description = "Google Sheets ID", example = "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms")
    @TableField("spreadsheet_id")
    private String spreadsheetId;

    /**
     * 工作表名称
     */
    @Schema(description = "工作表名称", example = "账单记录")
    @TableField("sheet_name")
    private String sheetName;

    /**
     * 同步类型
     */
    @Schema(description = "同步类型", example = "INCREMENTAL")
    @TableField("sync_type")
    private String syncType;

    /**
     * 同步方向
     */
    @Schema(description = "同步方向", example = "TO_SHEETS")
    @TableField("sync_direction")
    private String syncDirection;

    /**
     * 同步状态
     */
    @Schema(description = "同步状态", example = "SUCCESS")
    @TableField("sync_status")
    private String syncStatus;

    /**
     * 触发方式
     */
    @Schema(description = "触发方式", example = "MANUAL")
    @TableField("trigger_type")
    private String triggerType;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2025-01-01T10:00:00")
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", example = "2025-01-01T10:02:30")
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 执行耗时（秒）
     */
    @Schema(description = "执行耗时（秒）", example = "150")
    @TableField("duration_seconds")
    private Integer durationSeconds;

    /**
     * 处理记录数
     */
    @Schema(description = "处理记录数", example = "100")
    @TableField("processed_count")
    private Integer processedCount;

    /**
     * 成功记录数
     */
    @Schema(description = "成功记录数", example = "98")
    @TableField("success_count")
    private Integer successCount;

    /**
     * 失败记录数
     */
    @Schema(description = "失败记录数", example = "2")
    @TableField("failed_count")
    private Integer failedCount;

    /**
     * 跳过记录数
     */
    @Schema(description = "跳过记录数", example = "0")
    @TableField("skipped_count")
    private Integer skippedCount;

    /**
     * 成功率
     */
    @Schema(description = "成功率", example = "98.00")
    @TableField("success_rate")
    private Double successRate;

    /**
     * 是否异步执行
     */
    @Schema(description = "是否异步执行", example = "true")
    @TableField("is_async")
    private Boolean isAsync;

    /**
     * 批量大小
     */
    @Schema(description = "批量大小", example = "100")
    @TableField("batch_size")
    private Integer batchSize;

    /**
     * 批次数量
     */
    @Schema(description = "批次数量", example = "1")
    @TableField("batch_count")
    private Integer batchCount;

    /**
     * 重试次数
     */
    @Schema(description = "重试次数", example = "0")
    @TableField("retry_count")
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    @Schema(description = "最大重试次数", example = "3")
    @TableField("max_retry_count")
    private Integer maxRetryCount;

    /**
     * 冲突解决策略
     */
    @Schema(description = "冲突解决策略", example = "LOCAL_WINS")
    @TableField("conflict_resolution")
    private String conflictResolution;

    /**
     * 冲突记录数
     */
    @Schema(description = "冲突记录数", example = "0")
    @TableField("conflict_count")
    private Integer conflictCount;

    /**
     * 错误代码
     */
    @Schema(description = "错误代码", example = "VALIDATION_ERROR")
    @TableField("error_code")
    private String errorCode;

    /**
     * 错误消息
     */
    @Schema(description = "错误消息", example = "部分记录格式不正确")
    @TableField("error_message")
    private String errorMessage;

    /**
     * 错误堆栈
     */
    @Schema(description = "错误堆栈")
    @TableField("error_stack")
    private String errorStack;

    /**
     * 执行人ID
     */
    @Schema(description = "执行人ID", example = "1")
    @TableField("executed_by")
    private Long executedBy;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    @TableField("group_id")
    private Long groupId;

    /**
     * 同步参数JSON
     */
    @Schema(description = "同步参数JSON")
    @TableField("sync_params_json")
    private String syncParamsJson;

    /**
     * 同步结果JSON
     */
    @Schema(description = "同步结果JSON")
    @TableField("sync_result_json")
    private String syncResultJson;

    /**
     * 性能指标JSON
     */
    @Schema(description = "性能指标JSON")
    @TableField("performance_metrics_json")
    private String performanceMetricsJson;

    /**
     * 数据统计JSON
     */
    @Schema(description = "数据统计JSON")
    @TableField("data_statistics_json")
    private String dataStatisticsJson;

    /**
     * 备份信息JSON
     */
    @Schema(description = "备份信息JSON")
    @TableField("backup_info_json")
    private String backupInfoJson;

    /**
     * 回调状态
     */
    @Schema(description = "回调状态", example = "SUCCESS")
    @TableField("callback_status")
    private String callbackStatus;

    /**
     * 回调消息
     */
    @Schema(description = "回调消息", example = "回调执行成功")
    @TableField("callback_message")
    private String callbackMessage;

    /**
     * 回调耗时（秒）
     */
    @Schema(description = "回调耗时（秒）", example = "2")
    @TableField("callback_duration")
    private Integer callbackDuration;

    /**
     * 扩展属性JSON
     */
    @Schema(description = "扩展属性JSON")
    @TableField("extra_properties_json")
    private String extraPropertiesJson;
}
