package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import jakarta.validation.Valid;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 预算创建请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "预算创建请求")
public class BudgetCreateReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    @NotNull(message = "群组ID不能为空")
    private Long groupId;

    /**
     * 预算名称
     */
    @Schema(description = "预算名称", example = "2025年度预算")
    @NotBlank(message = "预算名称不能为空")
    @Size(max = 100, message = "预算名称长度不能超过100个字符")
    private String budgetName;

    /**
     * 预算描述
     */
    @Schema(description = "预算描述", example = "2025年度收支预算计划")
    @Size(max = 500, message = "预算描述长度不能超过500个字符")
    private String description;

    /**
     * 预算类型
     */
    @Schema(description = "预算类型", example = "ANNUAL", allowableValues = {"ANNUAL", "QUARTERLY", "MONTHLY", "WEEKLY", "CUSTOM"})
    @NotBlank(message = "预算类型不能为空")
    private String budgetType;

    /**
     * 预算周期
     */
    @Schema(description = "预算周期", example = "YEARLY", allowableValues = {"YEARLY", "QUARTERLY", "MONTHLY", "WEEKLY", "DAILY"})
    @NotBlank(message = "预算周期不能为空")
    private String budgetPeriod;

    /**
     * 开始日期
     */
    @Schema(description = "开始日期", example = "2025-01-01")
    @NotNull(message = "开始日期不能为空")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @Schema(description = "结束日期", example = "2025-12-31")
    @NotNull(message = "结束日期不能为空")
    private LocalDate endDate;

    /**
     * 总预算金额
     */
    @Schema(description = "总预算金额", example = "100000.00")
    @NotNull(message = "总预算金额不能为空")
    @DecimalMin(value = "0.01", message = "总预算金额必须大于0")
    @Digits(integer = 10, fraction = 2, message = "总预算金额格式不正确")
    private BigDecimal totalAmount;

    /**
     * 币种
     */
    @Schema(description = "币种", example = "CNY")
    @NotBlank(message = "币种不能为空")
    @Size(max = 10, message = "币种长度不能超过10个字符")
    private String currency;

    /**
     * 预算分配
     */
    @Schema(description = "预算分配")
    @NotEmpty(message = "预算分配不能为空")
    @Valid
    private List<BudgetAllocation> allocations;

    /**
     * 预警设置
     */
    @Schema(description = "预警设置")
    private AlertSettings alertSettings;

    /**
     * 审批设置
     */
    @Schema(description = "审批设置")
    private ApprovalSettings approvalSettings;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled = true;

    /**
     * 扩展属性
     */
    @Schema(description = "扩展属性")
    private Map<String, Object> attributes;

    /**
     * 预算分配
     */
    @Data
    @Schema(description = "预算分配")
    public static class BudgetAllocation implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 分配类型
         */
        @Schema(description = "分配类型", example = "CATEGORY", allowableValues = {"CATEGORY", "MEMBER", "PROJECT", "DEPARTMENT"})
        @NotBlank(message = "分配类型不能为空")
        private String allocationType;

        /**
         * 分配目标ID
         */
        @Schema(description = "分配目标ID", example = "1")
        @NotNull(message = "分配目标ID不能为空")
        private Long targetId;

        /**
         * 分配目标名称
         */
        @Schema(description = "分配目标名称", example = "餐饮消费")
        @NotBlank(message = "分配目标名称不能为空")
        private String targetName;

        /**
         * 分配金额
         */
        @Schema(description = "分配金额", example = "10000.00")
        @NotNull(message = "分配金额不能为空")
        @DecimalMin(value = "0.01", message = "分配金额必须大于0")
        private BigDecimal amount;

        /**
         * 分配比例
         */
        @Schema(description = "分配比例", example = "0.10")
        @DecimalMin(value = "0.01", message = "分配比例必须大于0")
        @DecimalMax(value = "1.00", message = "分配比例不能超过100%")
        private BigDecimal percentage;

        /**
         * 是否允许超支
         */
        @Schema(description = "是否允许超支", example = "false")
        private Boolean allowOverspend = false;

        /**
         * 超支限额
         */
        @Schema(description = "超支限额", example = "1000.00")
        @DecimalMin(value = "0", message = "超支限额不能为负数")
        private BigDecimal overspendLimit;

        /**
         * 备注
         */
        @Schema(description = "备注", example = "餐饮消费预算")
        @Size(max = 200, message = "备注长度不能超过200个字符")
        private String remark;
    }

    /**
     * 预警设置
     */
    @Data
    @Schema(description = "预警设置")
    public static class AlertSettings implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 是否启用预警
         */
        @Schema(description = "是否启用预警", example = "true")
        private Boolean enabled = true;

        /**
         * 预警阈值列表
         */
        @Schema(description = "预警阈值列表")
        private List<AlertThreshold> thresholds;

        /**
         * 预警通知方式
         */
        @Schema(description = "预警通知方式", example = "[\"EMAIL\", \"SMS\", \"PUSH\"]")
        private List<String> notificationMethods;

        /**
         * 预警接收人
         */
        @Schema(description = "预警接收人")
        private List<Long> recipients;

        @Data
        @Schema(description = "预警阈值")
        public static class AlertThreshold implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 阈值类型
             */
            @Schema(description = "阈值类型", example = "PERCENTAGE", allowableValues = {"PERCENTAGE", "AMOUNT"})
            private String thresholdType;

            /**
             * 阈值
             */
            @Schema(description = "阈值", example = "0.80")
            private BigDecimal threshold;

            /**
             * 预警级别
             */
            @Schema(description = "预警级别", example = "WARNING", allowableValues = {"INFO", "WARNING", "CRITICAL"})
            private String alertLevel;

            /**
             * 预警消息
             */
            @Schema(description = "预警消息", example = "预算使用已达到80%")
            private String message;
        }
    }

    /**
     * 审批设置
     */
    @Data
    @Schema(description = "审批设置")
    public static class ApprovalSettings implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 是否需要审批
         */
        @Schema(description = "是否需要审批", example = "true")
        private Boolean required = false;

        /**
         * 审批阈值
         */
        @Schema(description = "审批阈值", example = "1000.00")
        @DecimalMin(value = "0", message = "审批阈值不能为负数")
        private BigDecimal approvalThreshold;

        /**
         * 审批流程
         */
        @Schema(description = "审批流程")
        private List<ApprovalStep> approvalSteps;

        /**
         * 自动审批规则
         */
        @Schema(description = "自动审批规则")
        private Map<String, Object> autoApprovalRules;

        @Data
        @Schema(description = "审批步骤")
        public static class ApprovalStep implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 步骤序号
             */
            @Schema(description = "步骤序号", example = "1")
            private Integer stepOrder;

            /**
             * 步骤名称
             */
            @Schema(description = "步骤名称", example = "部门主管审批")
            private String stepName;

            /**
             * 审批人类型
             */
            @Schema(description = "审批人类型", example = "USER", allowableValues = {"USER", "ROLE", "DEPARTMENT"})
            private String approverType;

            /**
             * 审批人ID列表
             */
            @Schema(description = "审批人ID列表")
            private List<Long> approverIds;

            /**
             * 是否必须
             */
            @Schema(description = "是否必须", example = "true")
            private Boolean required = true;

            /**
             * 超时时间（小时）
             */
            @Schema(description = "超时时间（小时）", example = "24")
            private Integer timeoutHours;
        }
    }
}
