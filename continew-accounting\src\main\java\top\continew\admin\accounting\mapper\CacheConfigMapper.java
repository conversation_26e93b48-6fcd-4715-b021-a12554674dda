package top.continew.admin.accounting.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.accounting.model.entity.CacheConfigDO;
import top.continew.starter.extension.crud.mapper.BaseMapper;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 缓存配置 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface CacheConfigMapper extends BaseMapper<CacheConfigDO> {

    /**
     * 获取缓存配置统计
     *
     * @param groupId 群组ID
     * @return 统计信息
     */
    Map<String, Object> getCacheConfigStatistics(@Param("groupId") Long groupId);

    /**
     * 获取缓存类型分布统计
     *
     * @param groupId 群组ID
     * @return 类型分布统计
     */
    List<Map<String, Object>> getCacheTypeDistribution(@Param("groupId") Long groupId);

    /**
     * 获取缓存策略分布统计
     *
     * @param groupId 群组ID
     * @return 策略分布统计
     */
    List<Map<String, Object>> getCacheStrategyDistribution(@Param("groupId") Long groupId);

    /**
     * 获取缓存配置使用频率统计
     *
     * @param groupId 群组ID
     * @param limit   限制数量
     * @return 使用频率统计
     */
    List<Map<String, Object>> getCacheConfigUsageFrequency(@Param("groupId") Long groupId, @Param("limit") Integer limit);

    /**
     * 获取缓存配置性能指标
     *
     * @param configId 配置ID
     * @param hours    时间范围（小时）
     * @return 性能指标
     */
    List<Map<String, Object>> getCacheConfigPerformanceMetrics(@Param("configId") Long configId, @Param("hours") Integer hours);

    /**
     * 获取缓存配置健康状态
     *
     * @param groupId 群组ID
     * @return 健康状态
     */
    List<Map<String, Object>> getCacheConfigHealthStatus(@Param("groupId") Long groupId);

    /**
     * 获取缓存配置异常检测
     *
     * @param groupId 群组ID
     * @return 异常信息
     */
    List<Map<String, Object>> detectCacheConfigAnomalies(@Param("groupId") Long groupId);

    /**
     * 获取缓存配置优化建议
     *
     * @param groupId 群组ID
     * @return 优化建议
     */
    List<Map<String, Object>> getCacheConfigOptimizationSuggestions(@Param("groupId") Long groupId);

    /**
     * 批量更新缓存配置状态
     *
     * @param configIds 配置ID列表
     * @param status    状态
     * @return 更新数量
     */
    Integer batchUpdateStatus(@Param("configIds") List<Long> configIds, @Param("status") String status);

    /**
     * 批量更新应用统计
     *
     * @param configIds 配置ID列表
     * @return 更新数量
     */
    Integer batchUpdateAppliedStatistics(@Param("configIds") List<Long> configIds);

    /**
     * 清理过期的缓存配置
     *
     * @param expireTime 过期时间
     * @return 清理数量
     */
    Integer cleanupExpiredConfigs(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 获取缓存配置容量预测
     *
     * @param configId 配置ID
     * @param days     预测天数
     * @return 容量预测
     */
    Map<String, Object> getCacheConfigCapacityPrediction(@Param("configId") Long configId, @Param("days") Integer days);

    /**
     * 获取缓存配置使用模式分析
     *
     * @param configId 配置ID
     * @param days     分析天数
     * @return 使用模式分析
     */
    Map<String, Object> getCacheConfigUsagePattern(@Param("configId") Long configId, @Param("days") Integer days);

    /**
     * 获取缓存配置热点数据
     *
     * @param configId 配置ID
     * @param limit    限制数量
     * @return 热点数据
     */
    List<Map<String, Object>> getCacheConfigHotspotData(@Param("configId") Long configId, @Param("limit") Integer limit);

    /**
     * 获取缓存配置错误统计
     *
     * @param configId 配置ID
     * @param hours    时间范围（小时）
     * @return 错误统计
     */
    List<Map<String, Object>> getCacheConfigErrorStatistics(@Param("configId") Long configId, @Param("hours") Integer hours);

    /**
     * 获取缓存配置性能趋势
     *
     * @param configId 配置ID
     * @param days     天数
     * @return 性能趋势
     */
    List<Map<String, Object>> getCacheConfigPerformanceTrend(@Param("configId") Long configId, @Param("days") Integer days);

    /**
     * 验证缓存配置完整性
     *
     * @param configId 配置ID
     * @return 验证结果
     */
    Map<String, Object> validateCacheConfigIntegrity(@Param("configId") Long configId);

    /**
     * 获取缓存配置依赖关系
     *
     * @param configId 配置ID
     * @return 依赖关系
     */
    List<Map<String, Object>> getCacheConfigDependencies(@Param("configId") Long configId);

    /**
     * 获取缓存配置影响分析
     *
     * @param configId 配置ID
     * @return 影响分析
     */
    Map<String, Object> getCacheConfigImpactAnalysis(@Param("configId") Long configId);

    /**
     * 获取缓存配置版本历史
     *
     * @param configId 配置ID
     * @param limit    限制数量
     * @return 版本历史
     */
    List<Map<String, Object>> getCacheConfigVersionHistory(@Param("configId") Long configId, @Param("limit") Integer limit);

    /**
     * 计算缓存配置相似度
     *
     * @param configId1 配置ID1
     * @param configId2 配置ID2
     * @return 相似度
     */
    BigDecimal calculateCacheConfigSimilarity(@Param("configId1") Long configId1, @Param("configId2") Long configId2);

    /**
     * 获取缓存配置推荐
     *
     * @param groupId 群组ID
     * @param limit   限制数量
     * @return 推荐配置
     */
    List<Map<String, Object>> getCacheConfigRecommendations(@Param("groupId") Long groupId, @Param("limit") Integer limit);

}
