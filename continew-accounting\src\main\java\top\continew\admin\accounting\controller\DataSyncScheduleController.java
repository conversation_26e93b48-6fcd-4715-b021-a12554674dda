package top.continew.admin.accounting.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.accounting.model.query.DataSyncScheduleQuery;
import top.continew.admin.accounting.model.req.DataSyncScheduleCreateReq;
import top.continew.admin.accounting.model.req.DataSyncScheduleUpdateReq;
import top.continew.admin.accounting.model.resp.DataSyncScheduleDetailResp;
import top.continew.admin.accounting.model.resp.DataSyncScheduleListResp;
import top.continew.admin.accounting.service.DataSyncScheduleService;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.controller.BaseController;
import top.continew.starter.core.constant.StringConstants;
import top.continew.starter.core.util.validate.CheckUtils;
import top.continew.starter.web.model.R;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 数据同步计划管理 API
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "数据同步计划管理 API")
@RestController
@RequiredArgsConstructor
@Validated
@CrudRequestMapping(value = "/accounting/sync/schedule", api = {CrudRequestMapping.Api.PAGE, CrudRequestMapping.Api.GET, CrudRequestMapping.Api.ADD, CrudRequestMapping.Api.UPDATE, CrudRequestMapping.Api.DELETE})
public class DataSyncScheduleController extends BaseController<DataSyncScheduleService, DataSyncScheduleListResp, DataSyncScheduleDetailResp, DataSyncScheduleQuery, DataSyncScheduleCreateReq, DataSyncScheduleUpdateReq> {

    // ==================== 计划管理 ====================

    @Operation(summary = "启用计划", description = "启用指定的同步计划")
    @PostMapping("/{id}/enable")
    public R<Void> enableSchedule(@Parameter(description = "计划ID", example = "1") @PathVariable Long id) {
        baseService.enableSchedule(id);
        return R.ok();
    }

    @Operation(summary = "禁用计划", description = "禁用指定的同步计划")
    @PostMapping("/{id}/disable")
    public R<Void> disableSchedule(@Parameter(description = "计划ID", example = "1") @PathVariable Long id) {
        baseService.disableSchedule(id);
        return R.ok();
    }

    @Operation(summary = "立即执行计划", description = "立即执行指定的同步计划")
    @PostMapping("/{id}/execute")
    public R<Map<String, Object>> executeScheduleNow(@Parameter(description = "计划ID", example = "1") @PathVariable Long id) {
        Map<String, Object> result = baseService.executeScheduleNow(id);
        return R.ok(result);
    }

    @Operation(summary = "批量启用/禁用计划", description = "批量启用或禁用同步计划")
    @PostMapping("/batch-update-enabled")
    public R<Void> batchUpdateEnabled(@Parameter(description = "计划ID列表") @RequestBody @NotEmpty List<Long> scheduleIds,
                                     @Parameter(description = "是否启用") @RequestParam @NotNull Boolean enabled) {
        baseService.batchUpdateEnabled(scheduleIds, enabled);
        return R.ok();
    }

    // ==================== 执行历史 ====================

    @Operation(summary = "获取执行历史", description = "获取指定计划的执行历史")
    @GetMapping("/{id}/history")
    public R<List<Map<String, Object>>> getExecutionHistory(@Parameter(description = "计划ID", example = "1") @PathVariable Long id,
                                                           @Parameter(description = "限制数量", example = "10") @RequestParam(defaultValue = "10") Integer limit) {
        List<Map<String, Object>> history = baseService.getExecutionHistory(id, limit);
        return R.ok(history);
    }

    @Operation(summary = "获取执行统计", description = "获取指定计划的执行统计信息")
    @GetMapping("/{id}/statistics")
    public R<Map<String, Object>> getExecutionStatistics(@Parameter(description = "计划ID", example = "1") @PathVariable Long id,
                                                        @Parameter(description = "开始日期", example = "2025-01-01") @RequestParam(required = false) String startDate,
                                                        @Parameter(description = "结束日期", example = "2025-01-31") @RequestParam(required = false) String endDate) {
        Map<String, Object> statistics = baseService.getExecutionStatistics(id, startDate, endDate);
        return R.ok(statistics);
    }

    @Operation(summary = "清理执行历史", description = "清理指定计划的执行历史")
    @DeleteMapping("/{id}/history")
    public R<Integer> cleanupExecutionHistory(@Parameter(description = "计划ID", example = "1") @PathVariable Long id,
                                            @Parameter(description = "保留天数", example = "30") @RequestParam(defaultValue = "30") Integer retentionDays) {
        Integer cleanupCount = baseService.cleanupExecutionHistory(id, retentionDays);
        return R.ok(cleanupCount);
    }

    // ==================== 监控告警 ====================

    @Operation(summary = "获取计划健康状态", description = "获取所有计划的健康状态")
    @GetMapping("/health-status")
    public R<List<Map<String, Object>>> getScheduleHealthStatus(@Parameter(description = "群组ID") @RequestParam(required = false) Long groupId) {
        List<Map<String, Object>> healthStatus = baseService.getScheduleHealthStatus(groupId);
        return R.ok(healthStatus);
    }

    @Operation(summary = "获取失败计划", description = "获取最近失败的计划列表")
    @GetMapping("/failed")
    public R<List<Map<String, Object>>> getFailedSchedules(@Parameter(description = "群组ID") @RequestParam(required = false) Long groupId,
                                                          @Parameter(description = "小时数", example = "24") @RequestParam(defaultValue = "24") Integer hours) {
        List<Map<String, Object>> failedSchedules = baseService.getFailedSchedules(groupId, hours);
        return R.ok(failedSchedules);
    }

    @Operation(summary = "获取超时计划", description = "获取当前超时的计划列表")
    @GetMapping("/timeout")
    public R<List<Map<String, Object>>> getTimeoutSchedules(@Parameter(description = "群组ID") @RequestParam(required = false) Long groupId) {
        List<Map<String, Object>> timeoutSchedules = baseService.getTimeoutSchedules(groupId);
        return R.ok(timeoutSchedules);
    }

    // ==================== 配置验证 ====================

    @Operation(summary = "验证Cron表达式", description = "验证Cron表达式的有效性")
    @PostMapping("/validate-cron")
    public R<Map<String, Object>> validateCronExpression(@Parameter(description = "Cron表达式", example = "0 0 2 * * ?") @RequestParam String cronExpression) {
        CheckUtils.throwIfBlank(cronExpression, "Cron表达式不能为空");
        Map<String, Object> result = baseService.validateCronExpression(cronExpression);
        return R.ok(result);
    }

    @Operation(summary = "预览执行时间", description = "预览计划的执行时间")
    @PostMapping("/preview-execution-times")
    public R<List<LocalDateTime>> previewExecutionTimes(@Parameter(description = "计划类型", example = "CRON") @RequestParam String scheduleType,
                                                       @Parameter(description = "Cron表达式", example = "0 0 2 * * ?") @RequestParam(required = false) String cronExpression,
                                                       @Parameter(description = "间隔秒数", example = "3600") @RequestParam(required = false) Integer intervalSeconds,
                                                       @Parameter(description = "时区", example = "Asia/Shanghai") @RequestParam(defaultValue = "Asia/Shanghai") String timezone,
                                                       @Parameter(description = "预览数量", example = "5") @RequestParam(defaultValue = "5") Integer count) {
        List<LocalDateTime> times = baseService.previewExecutionTimes(scheduleType, cronExpression, intervalSeconds, timezone, count);
        return R.ok(times);
    }

    // ==================== 统计分析 ====================

    @Operation(summary = "获取计划统计", description = "获取计划的统计信息")
    @GetMapping("/statistics")
    public R<Map<String, Object>> getScheduleStatistics(@Parameter(description = "群组ID") @RequestParam(required = false) Long groupId) {
        Map<String, Object> statistics = baseService.getScheduleStatistics(groupId);
        return R.ok(statistics);
    }

    @Operation(summary = "获取执行趋势", description = "获取计划执行的趋势数据")
    @GetMapping("/trends")
    public R<List<Map<String, Object>>> getExecutionTrends(@Parameter(description = "群组ID") @RequestParam(required = false) Long groupId,
                                                          @Parameter(description = "开始日期", example = "2025-01-01") @RequestParam(required = false) String startDate,
                                                          @Parameter(description = "结束日期", example = "2025-01-31") @RequestParam(required = false) String endDate,
                                                          @Parameter(description = "分组方式", example = "day") @RequestParam(defaultValue = "day") String groupBy) {
        List<Map<String, Object>> trends = baseService.getExecutionTrends(groupId, startDate, endDate, groupBy);
        return R.ok(trends);
    }

    @Operation(summary = "获取性能分析", description = "获取计划执行的性能分析数据")
    @GetMapping("/performance")
    public R<List<Map<String, Object>>> getPerformanceAnalysis(@Parameter(description = "群组ID") @RequestParam(required = false) Long groupId,
                                                              @Parameter(description = "开始日期", example = "2025-01-01") @RequestParam(required = false) String startDate,
                                                              @Parameter(description = "结束日期", example = "2025-01-31") @RequestParam(required = false) String endDate) {
        List<Map<String, Object>> performance = baseService.getPerformanceAnalysis(groupId, startDate, endDate);
        return R.ok(performance);
    }

}
