package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 审核请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "审核请求")
public class AuditReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 审核结果
     */
    @Schema(description = "审核结果", example = "APPROVED", allowableValues = {"APPROVED", "REJECTED", "RETURNED", "PENDING"})
    @NotBlank(message = "审核结果不能为空")
    private String auditResult;

    /**
     * 审核意见
     */
    @Schema(description = "审核意见", example = "费用合理，同意报销")
    @Size(max = 1000, message = "审核意见长度不能超过1000个字符")
    private String auditComment;

    /**
     * 审核详情
     */
    @Schema(description = "审核详情")
    private List<TransactionAuditDetail> auditDetails;

    /**
     * 修改建议
     */
    @Schema(description = "修改建议")
    private List<ModificationSuggestion> modificationSuggestions;

    /**
     * 风险评估
     */
    @Schema(description = "风险评估")
    private RiskAssessment riskAssessment;

    /**
     * 附件列表
     */
    @Schema(description = "附件列表")
    private List<String> attachments;

    /**
     * 下一步操作
     */
    @Schema(description = "下一步操作", example = "COMPLETE", allowableValues = {"COMPLETE", "FORWARD", "ESCALATE", "RETURN_TO_SUBMITTER"})
    private String nextAction = "COMPLETE";

    /**
     * 转发给的审核人ID列表（当nextAction为FORWARD时）
     */
    @Schema(description = "转发给的审核人ID列表")
    private List<Long> forwardToAuditorIds;

    /**
     * 升级原因（当nextAction为ESCALATE时）
     */
    @Schema(description = "升级原因")
    @Size(max = 500, message = "升级原因长度不能超过500个字符")
    private String escalationReason;

    /**
     * 扩展属性
     */
    @Schema(description = "扩展属性")
    private Map<String, Object> attributes;

    /**
     * 交易审核详情
     */
    @Data
    @Schema(description = "交易审核详情")
    public static class TransactionAuditDetail implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 交易ID
         */
        @Schema(description = "交易ID", example = "1")
        @NotNull(message = "交易ID不能为空")
        private Long transactionId;

        /**
         * 审核结果
         */
        @Schema(description = "审核结果", example = "APPROVED", allowableValues = {"APPROVED", "REJECTED", "RETURNED", "MODIFIED"})
        @NotBlank(message = "审核结果不能为空")
        private String result;

        /**
         * 审核意见
         */
        @Schema(description = "审核意见", example = "金额合理")
        @Size(max = 500, message = "审核意见长度不能超过500个字符")
        private String comment;

        /**
         * 原始金额
         */
        @Schema(description = "原始金额", example = "100.00")
        private BigDecimal originalAmount;

        /**
         * 审核后金额
         */
        @Schema(description = "审核后金额", example = "95.00")
        private BigDecimal auditedAmount;

        /**
         * 金额调整原因
         */
        @Schema(description = "金额调整原因", example = "去除不合理费用")
        @Size(max = 200, message = "金额调整原因长度不能超过200个字符")
        private String amountAdjustmentReason;

        /**
         * 分类调整
         */
        @Schema(description = "分类调整")
        private CategoryAdjustment categoryAdjustment;

        /**
         * 标签调整
         */
        @Schema(description = "标签调整")
        private List<String> tagAdjustments;

        /**
         * 风险等级
         */
        @Schema(description = "风险等级", example = "LOW", allowableValues = {"LOW", "MEDIUM", "HIGH", "CRITICAL"})
        private String riskLevel;

        /**
         * 合规性检查结果
         */
        @Schema(description = "合规性检查结果")
        private ComplianceCheckResult complianceResult;

        @Data
        @Schema(description = "分类调整")
        public static class CategoryAdjustment implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 原分类ID
             */
            @Schema(description = "原分类ID", example = "1")
            private Long originalCategoryId;

            /**
             * 原分类名称
             */
            @Schema(description = "原分类名称", example = "餐饮")
            private String originalCategoryName;

            /**
             * 新分类ID
             */
            @Schema(description = "新分类ID", example = "2")
            private Long newCategoryId;

            /**
             * 新分类名称
             */
            @Schema(description = "新分类名称", example = "商务招待")
            private String newCategoryName;

            /**
             * 调整原因
             */
            @Schema(description = "调整原因", example = "更符合实际用途")
            private String adjustmentReason;
        }

        @Data
        @Schema(description = "合规性检查结果")
        public static class ComplianceCheckResult implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 是否合规
             */
            @Schema(description = "是否合规", example = "true")
            private Boolean compliant;

            /**
             * 违规项目
             */
            @Schema(description = "违规项目")
            private List<String> violations;

            /**
             * 合规性评分
             */
            @Schema(description = "合规性评分", example = "85")
            private Integer complianceScore;

            /**
             * 检查详情
             */
            @Schema(description = "检查详情")
            private Map<String, Object> checkDetails;
        }
    }

    /**
     * 修改建议
     */
    @Data
    @Schema(description = "修改建议")
    public static class ModificationSuggestion implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 建议类型
         */
        @Schema(description = "建议类型", example = "AMOUNT_ADJUSTMENT", allowableValues = {"AMOUNT_ADJUSTMENT", "CATEGORY_CHANGE", "DESCRIPTION_UPDATE", "TAG_MODIFICATION", "SPLIT_TRANSACTION"})
        private String suggestionType;

        /**
         * 建议内容
         */
        @Schema(description = "建议内容", example = "建议将金额调整为95.00元")
        private String suggestionContent;

        /**
         * 建议原因
         */
        @Schema(description = "建议原因", example = "去除不合理费用")
        private String reason;

        /**
         * 优先级
         */
        @Schema(description = "优先级", example = "MEDIUM", allowableValues = {"LOW", "MEDIUM", "HIGH"})
        private String priority;

        /**
         * 是否必须
         */
        @Schema(description = "是否必须", example = "false")
        private Boolean mandatory = false;

        /**
         * 相关交易ID
         */
        @Schema(description = "相关交易ID", example = "1")
        private Long relatedTransactionId;

        /**
         * 建议的新值
         */
        @Schema(description = "建议的新值")
        private Map<String, Object> suggestedValues;
    }

    /**
     * 风险评估
     */
    @Data
    @Schema(description = "风险评估")
    public static class RiskAssessment implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 总体风险等级
         */
        @Schema(description = "总体风险等级", example = "LOW", allowableValues = {"LOW", "MEDIUM", "HIGH", "CRITICAL"})
        private String overallRiskLevel;

        /**
         * 风险评分
         */
        @Schema(description = "风险评分", example = "25")
        @Min(value = 0, message = "风险评分不能小于0")
        @Max(value = 100, message = "风险评分不能大于100")
        private Integer riskScore;

        /**
         * 风险因素
         */
        @Schema(description = "风险因素")
        private List<RiskFactor> riskFactors;

        /**
         * 缓解措施
         */
        @Schema(description = "缓解措施")
        private List<String> mitigationMeasures;

        /**
         * 监控建议
         */
        @Schema(description = "监控建议")
        private List<String> monitoringRecommendations;

        @Data
        @Schema(description = "风险因素")
        public static class RiskFactor implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 风险类型
             */
            @Schema(description = "风险类型", example = "AMOUNT_ANOMALY")
            private String riskType;

            /**
             * 风险描述
             */
            @Schema(description = "风险描述", example = "金额异常偏高")
            private String description;

            /**
             * 风险等级
             */
            @Schema(description = "风险等级", example = "MEDIUM")
            private String riskLevel;

            /**
             * 风险概率
             */
            @Schema(description = "风险概率", example = "0.3")
            private BigDecimal probability;

            /**
             * 影响程度
             */
            @Schema(description = "影响程度", example = "MEDIUM")
            private String impact;

            /**
             * 相关数据
             */
            @Schema(description = "相关数据")
            private Map<String, Object> relatedData;
        }
    }
}
