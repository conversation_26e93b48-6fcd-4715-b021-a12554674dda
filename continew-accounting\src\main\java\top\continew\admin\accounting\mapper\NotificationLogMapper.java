package top.continew.admin.accounting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.accounting.model.entity.NotificationLogDO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 通知日志 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface NotificationLogMapper extends BaseMapper<NotificationLogDO> {

    // ==================== 日志查询 ====================

    /**
     * 查询通知发送日志
     *
     * @param notificationId 通知ID
     * @return 日志列表
     */
    List<NotificationLogDO> selectByNotificationId(@Param("notificationId") Long notificationId);

    /**
     * 查询用户通知日志
     *
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 日志列表
     */
    List<NotificationLogDO> selectUserNotificationLogs(@Param("userId") Long userId,
                                                      @Param("startDate") String startDate,
                                                      @Param("endDate") String endDate,
                                                      @Param("limit") Integer limit);

    /**
     * 查询渠道发送日志
     *
     * @param channel 渠道
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 日志列表
     */
    List<NotificationLogDO> selectChannelSendLogs(@Param("channel") String channel,
                                                 @Param("startDate") String startDate,
                                                 @Param("endDate") String endDate,
                                                 @Param("limit") Integer limit);

    /**
     * 分页查询日志
     *
     * @param groupId 群组ID
     * @param notificationId 通知ID
     * @param channel 渠道
     * @param status 状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 日志列表
     */
    List<Map<String, Object>> selectLogPageList(@Param("groupId") Long groupId,
                                               @Param("notificationId") Long notificationId,
                                               @Param("channel") String channel,
                                               @Param("status") String status,
                                               @Param("startDate") String startDate,
                                               @Param("endDate") String endDate,
                                               @Param("offset") Integer offset,
                                               @Param("limit") Integer limit);

    /**
     * 统计日志数量
     *
     * @param groupId 群组ID
     * @param notificationId 通知ID
     * @param channel 渠道
     * @param status 状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日志数量
     */
    Integer countLogs(@Param("groupId") Long groupId,
                     @Param("notificationId") Long notificationId,
                     @Param("channel") String channel,
                     @Param("status") String status,
                     @Param("startDate") String startDate,
                     @Param("endDate") String endDate);

    /**
     * 查询失败日志
     *
     * @param groupId 群组ID
     * @param hours 小时数
     * @param limit 限制数量
     * @return 失败日志列表
     */
    List<NotificationLogDO> selectFailedLogs(@Param("groupId") Long groupId,
                                           @Param("hours") Integer hours,
                                           @Param("limit") Integer limit);

    // ==================== 日志统计 ====================

    /**
     * 查询发送统计
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 发送统计
     */
    Map<String, Object> selectSendStatistics(@Param("groupId") Long groupId,
                                            @Param("startDate") String startDate,
                                            @Param("endDate") String endDate);

    /**
     * 查询渠道统计
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 渠道统计
     */
    List<Map<String, Object>> selectChannelStatistics(@Param("groupId") Long groupId,
                                                     @Param("startDate") String startDate,
                                                     @Param("endDate") String endDate);

    /**
     * 查询用户统计
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 用户统计
     */
    List<Map<String, Object>> selectUserStatistics(@Param("groupId") Long groupId,
                                                  @Param("startDate") String startDate,
                                                  @Param("endDate") String endDate,
                                                  @Param("limit") Integer limit);

    /**
     * 查询错误统计
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 错误统计
     */
    List<Map<String, Object>> selectErrorStatistics(@Param("groupId") Long groupId,
                                                   @Param("startDate") String startDate,
                                                   @Param("endDate") String endDate);

    /**
     * 查询性能统计
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 性能统计
     */
    Map<String, Object> selectPerformanceStatistics(@Param("groupId") Long groupId,
                                                   @Param("startDate") String startDate,
                                                   @Param("endDate") String endDate);

    // ==================== 日志分析 ====================

    /**
     * 查询发送趋势
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param groupBy 分组方式
     * @return 趋势数据
     */
    List<Map<String, Object>> selectSendTrends(@Param("groupId") Long groupId,
                                             @Param("startDate") String startDate,
                                             @Param("endDate") String endDate,
                                             @Param("groupBy") String groupBy);

    /**
     * 查询成功率分析
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 成功率分析
     */
    Map<String, Object> selectSuccessRateAnalysis(@Param("groupId") Long groupId,
                                                 @Param("startDate") String startDate,
                                                 @Param("endDate") String endDate);

    /**
     * 查询响应时间分析
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 响应时间分析
     */
    Map<String, Object> selectResponseTimeAnalysis(@Param("groupId") Long groupId,
                                                  @Param("startDate") String startDate,
                                                  @Param("endDate") String endDate);

    /**
     * 查询失败原因分析
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 失败原因分析
     */
    List<Map<String, Object>> selectFailureReasonAnalysis(@Param("groupId") Long groupId,
                                                         @Param("startDate") String startDate,
                                                         @Param("endDate") String endDate);

    // ==================== 日志清理 ====================

    /**
     * 清理过期日志
     *
     * @param expiredBefore 过期时间点
     * @return 清理数量
     */
    Integer deleteExpiredLogs(@Param("expiredBefore") LocalDateTime expiredBefore);

    /**
     * 归档日志
     *
     * @param archiveBefore 归档时间点
     * @return 归档数量
     */
    Integer archiveLogs(@Param("archiveBefore") LocalDateTime archiveBefore);

    /**
     * 压缩日志
     *
     * @param compressBefore 压缩时间点
     * @return 压缩数量
     */
    Integer compressLogs(@Param("compressBefore") LocalDateTime compressBefore);

    /**
     * 查询日志存储统计
     *
     * @return 存储统计
     */
    Map<String, Object> selectLogStorageStatistics();

    // ==================== 实时监控 ====================

    /**
     * 查询实时发送状态
     *
     * @return 实时状态
     */
    Map<String, Object> selectRealTimeSendStatus();

    /**
     * 查询渠道健康状态
     *
     * @return 渠道健康状态
     */
    List<Map<String, Object>> selectChannelHealthStatus();

    /**
     * 查询异常告警
     *
     * @param hours 小时数
     * @return 异常告警列表
     */
    List<Map<String, Object>> selectAbnormalAlerts(@Param("hours") Integer hours);

    /**
     * 查询队列状态
     *
     * @return 队列状态
     */
    Map<String, Object> selectQueueStatus();

    // ==================== 批量操作 ====================

    /**
     * 批量插入日志
     *
     * @param logs 日志列表
     * @return 插入数量
     */
    Integer batchInsertLogs(@Param("logs") List<NotificationLogDO> logs);

    /**
     * 批量更新日志状态
     *
     * @param logIds 日志ID列表
     * @param status 新状态
     * @param errorMessage 错误信息
     * @return 更新数量
     */
    Integer batchUpdateLogStatus(@Param("logIds") List<Long> logIds,
                                @Param("status") String status,
                                @Param("errorMessage") String errorMessage);

    /**
     * 批量删除日志
     *
     * @param logIds 日志ID列表
     * @return 删除数量
     */
    Integer batchDeleteLogs(@Param("logIds") List<Long> logIds);

    // ==================== 回调处理 ====================

    /**
     * 根据外部消息ID查询日志
     *
     * @param externalMessageId 外部消息ID
     * @param channel 渠道
     * @return 日志信息
     */
    NotificationLogDO selectByExternalMessageId(@Param("externalMessageId") String externalMessageId,
                                               @Param("channel") String channel);

    /**
     * 更新回调信息
     *
     * @param logId 日志ID
     * @param callbackData 回调数据
     * @param status 状态
     * @return 更新数量
     */
    Integer updateCallbackInfo(@Param("logId") Long logId,
                              @Param("callbackData") String callbackData,
                              @Param("status") String status);

}
