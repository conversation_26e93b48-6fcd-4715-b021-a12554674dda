package top.continew.admin.accounting.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 多维数据查询条件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "多维数据查询条件")
public class MultiDimensionQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    @NotNull(message = "群组ID不能为空")
    private Long groupId;

    /**
     * 维度列表
     */
    @Schema(description = "维度列表", example = "[\"category\", \"member\", \"time\"]")
    @Size(min = 1, max = 5, message = "维度数量必须在1-5之间")
    private List<String> dimensions;

    /**
     * 度量列表
     */
    @Schema(description = "度量列表", example = "[\"amount\", \"count\", \"avg_amount\"]")
    @Size(min = 1, max = 10, message = "度量数量必须在1-10之间")
    private List<String> measures;

    /**
     * 过滤条件
     */
    @Schema(description = "过滤条件")
    private Map<String, Object> filters;

    /**
     * 开始日期
     */
    @Schema(description = "开始日期", example = "2024-01-01")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @Schema(description = "结束日期", example = "2024-12-31")
    private LocalDate endDate;

    /**
     * 时间粒度
     */
    @Schema(description = "时间粒度", example = "DAILY", allowableValues = {"DAILY", "WEEKLY", "MONTHLY", "QUARTERLY", "YEARLY"})
    private String timeGranularity;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段", example = "amount")
    private String orderBy;

    /**
     * 排序方向
     */
    @Schema(description = "排序方向", example = "DESC", allowableValues = {"ASC", "DESC"})
    private String orderDirection;

    /**
     * 限制数量
     */
    @Schema(description = "限制数量", example = "100")
    private Integer limit;

    /**
     * 偏移量
     */
    @Schema(description = "偏移量", example = "0")
    private Integer offset;

    /**
     * 是否包含小计
     */
    @Schema(description = "是否包含小计", example = "true")
    private Boolean includeSubtotal;

    /**
     * 是否包含总计
     */
    @Schema(description = "是否包含总计", example = "true")
    private Boolean includeGrandTotal;

    /**
     * 数据格式
     */
    @Schema(description = "数据格式", example = "PIVOT", allowableValues = {"FLAT", "PIVOT", "TREE"})
    private String dataFormat;

    /**
     * 聚合函数配置
     */
    @Schema(description = "聚合函数配置")
    private Map<String, String> aggregationFunctions;

    /**
     * 计算字段配置
     */
    @Schema(description = "计算字段配置")
    private Map<String, String> calculatedFields;

    /**
     * 是否启用缓存
     */
    @Schema(description = "是否启用缓存", example = "true")
    private Boolean enableCache;

    /**
     * 缓存过期时间（秒）
     */
    @Schema(description = "缓存过期时间（秒）", example = "3600")
    private Integer cacheExpiration;
}
