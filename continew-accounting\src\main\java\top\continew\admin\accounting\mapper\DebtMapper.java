package top.continew.admin.accounting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.accounting.model.entity.DebtDO;
import top.continew.admin.accounting.model.query.DebtQuery;
import top.continew.admin.accounting.model.resp.DebtDetailResp;
import top.continew.admin.accounting.model.resp.DebtListResp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 债务 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface DebtMapper extends BaseMapper<DebtDO> {

    /**
     * 分页查询债务列表
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 债务列表
     */
    IPage<DebtListResp> selectDebtPage(Page<DebtListResp> page, @Param("query") DebtQuery query);

    /**
     * 查询债务详情
     *
     * @param id 债务ID
     * @return 债务详情
     */
    DebtDetailResp selectDebtDetail(@Param("id") Long id);

    /**
     * 查询用户相关的债务列表
     *
     * @param userId  用户ID
     * @param groupId 群组ID
     * @param status  状态
     * @return 债务列表
     */
    List<DebtListResp> selectByUserId(@Param("userId") Long userId, 
                                      @Param("groupId") Long groupId, 
                                      @Param("status") String status);

    /**
     * 查询群组债务统计
     *
     * @param groupId 群组ID
     * @param status  状态
     * @return 统计信息
     */
    DebtStatisticsResp selectGroupStatistics(@Param("groupId") Long groupId, 
                                             @Param("status") String status);

    /**
     * 查询用户债务统计
     *
     * @param userId  用户ID
     * @param groupId 群组ID
     * @param status  状态
     * @return 统计信息
     */
    DebtStatisticsResp selectUserStatistics(@Param("userId") Long userId, 
                                           @Param("groupId") Long groupId, 
                                           @Param("status") String status);

    /**
     * 查询即将到期的债务
     *
     * @param groupId 群组ID
     * @param days    天数
     * @return 债务列表
     */
    List<DebtListResp> selectDueSoon(@Param("groupId") Long groupId, @Param("days") Integer days);

    /**
     * 查询逾期债务
     *
     * @param groupId 群组ID
     * @return 债务列表
     */
    List<DebtListResp> selectOverdue(@Param("groupId") Long groupId);

    /**
     * 更新债务金额
     *
     * @param id              债务ID
     * @param paidAmount      已还金额
     * @param remainingAmount 剩余金额
     * @param lastPaymentDate 最后还款时间
     * @return 更新行数
     */
    int updateDebtAmount(@Param("id") Long id, 
                        @Param("paidAmount") BigDecimal paidAmount,
                        @Param("remainingAmount") BigDecimal remainingAmount,
                        @Param("lastPaymentDate") LocalDateTime lastPaymentDate);

    /**
     * 更新债务状态
     *
     * @param id     债务ID
     * @param status 状态
     * @return 更新行数
     */
    int updateDebtStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 检查用户权限
     *
     * @param id     债务ID
     * @param userId 用户ID
     * @return 是否有权限
     */
    boolean hasPermission(@Param("id") Long id, @Param("userId") Long userId);

    /**
     * 查询两个用户之间的债务关系
     *
     * @param groupId     群组ID
     * @param creditorId  债权人ID
     * @param debtorId    债务人ID
     * @param status      状态
     * @return 债务列表
     */
    List<DebtListResp> selectBetweenUsers(@Param("groupId") Long groupId,
                                         @Param("creditorId") Long creditorId,
                                         @Param("debtorId") Long debtorId,
                                         @Param("status") String status);

    /**
     * 债务统计响应内部类
     */
    class DebtStatisticsResp {
        private BigDecimal totalAmount;
        private BigDecimal totalPaidAmount;
        private BigDecimal totalRemainingAmount;
        private Integer totalCount;
        private Integer activeCount;
        private Integer paidCount;
        private Integer overdueCount;
        private BigDecimal overdueAmount;
        
        // getters and setters
        public BigDecimal getTotalAmount() { return totalAmount; }
        public void setTotalAmount(BigDecimal totalAmount) { this.totalAmount = totalAmount; }
        
        public BigDecimal getTotalPaidAmount() { return totalPaidAmount; }
        public void setTotalPaidAmount(BigDecimal totalPaidAmount) { this.totalPaidAmount = totalPaidAmount; }
        
        public BigDecimal getTotalRemainingAmount() { return totalRemainingAmount; }
        public void setTotalRemainingAmount(BigDecimal totalRemainingAmount) { this.totalRemainingAmount = totalRemainingAmount; }
        
        public Integer getTotalCount() { return totalCount; }
        public void setTotalCount(Integer totalCount) { this.totalCount = totalCount; }
        
        public Integer getActiveCount() { return activeCount; }
        public void setActiveCount(Integer activeCount) { this.activeCount = activeCount; }
        
        public Integer getPaidCount() { return paidCount; }
        public void setPaidCount(Integer paidCount) { this.paidCount = paidCount; }
        
        public Integer getOverdueCount() { return overdueCount; }
        public void setOverdueCount(Integer overdueCount) { this.overdueCount = overdueCount; }
        
        public BigDecimal getOverdueAmount() { return overdueAmount; }
        public void setOverdueAmount(BigDecimal overdueAmount) { this.overdueAmount = overdueAmount; }
    }
}
