package top.continew.admin.bot.common;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;
import top.continew.admin.accounting.enums.PlatformType;
import top.continew.admin.bot.model.dto.BotMessage;
import top.continew.admin.bot.service.CommandProcessingEngine;
import top.continew.admin.bot.service.MessageProcessor;
import top.continew.admin.bot.service.NotificationService;

/**
 * 机器人消息队列处理器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BotMessageQueue {

    private final RabbitTemplate rabbitTemplate;
    private final CommandProcessingEngine commandProcessingEngine;
    private final MessageProcessor messageProcessor;
    private final NotificationService notificationService;

    /**
     * 队列名称
     */
    public static final String QUEUE_BOT_MESSAGE = "bot.message";
    public static final String QUEUE_BOT_NOTIFICATION = "bot.notification";
    public static final String QUEUE_BOT_COMMAND = "bot.command";

    /**
     * 交换机名称
     */
    public static final String EXCHANGE_BOT = "bot.exchange";

    /**
     * 路由键
     */
    public static final String ROUTING_KEY_MESSAGE = "bot.message";
    public static final String ROUTING_KEY_NOTIFICATION = "bot.notification";
    public static final String ROUTING_KEY_COMMAND = "bot.command";

    /**
     * 发送消息到队列
     */
    public void sendMessage(BotMessage message) {
        try {
            rabbitTemplate.convertAndSend(EXCHANGE_BOT, ROUTING_KEY_MESSAGE, message);
            log.debug("发送机器人消息到队列: {}", message);
        } catch (Exception e) {
            log.error("发送机器人消息到队列失败", e);
        }
    }

    /**
     * 发送通知到队列
     */
    public void sendNotification(BotMessage notification) {
        try {
            rabbitTemplate.convertAndSend(EXCHANGE_BOT, ROUTING_KEY_NOTIFICATION, notification);
            log.debug("发送机器人通知到队列: {}", notification);
        } catch (Exception e) {
            log.error("发送机器人通知到队列失败", e);
        }
    }

    /**
     * 发送命令到队列
     */
    public void sendCommand(BotMessage command) {
        try {
            rabbitTemplate.convertAndSend(EXCHANGE_BOT, ROUTING_KEY_COMMAND, command);
            log.debug("发送机器人命令到队列: {}", command);
        } catch (Exception e) {
            log.error("发送机器人命令到队列失败", e);
        }
    }

    /**
     * 处理消息队列
     */
    @RabbitListener(queues = QUEUE_BOT_MESSAGE)
    public void handleMessage(BotMessage message) {
        try {
            log.info("处理机器人消息: {}", message);
            
            // 根据平台类型分发消息
            switch (message.getPlatform()) {
                case TELEGRAM -> handleTelegramMessage(message);
                case DISCORD -> handleDiscordMessage(message);
                case WECHAT -> handleWechatMessage(message);
                default -> log.warn("不支持的平台类型: {}", message.getPlatform());
            }
            
        } catch (Exception e) {
            log.error("处理机器人消息失败: {}", message, e);
        }
    }

    /**
     * 处理通知队列
     */
    @RabbitListener(queues = QUEUE_BOT_NOTIFICATION)
    public void handleNotification(BotMessage notification) {
        try {
            log.info("处理机器人通知: {}", notification);
            
            // 根据平台类型分发通知
            switch (notification.getPlatform()) {
                case TELEGRAM -> handleTelegramNotification(notification);
                case DISCORD -> handleDiscordNotification(notification);
                case WECHAT -> handleWechatNotification(notification);
                default -> log.warn("不支持的平台类型: {}", notification.getPlatform());
            }
            
        } catch (Exception e) {
            log.error("处理机器人通知失败: {}", notification, e);
        }
    }

    /**
     * 处理命令队列
     */
    @RabbitListener(queues = QUEUE_BOT_COMMAND)
    public void handleCommand(BotMessage command) {
        try {
            log.info("处理机器人命令: {}", command);
            
            // 根据平台类型分发命令
            switch (command.getPlatform()) {
                case TELEGRAM -> handleTelegramCommand(command);
                case DISCORD -> handleDiscordCommand(command);
                case WECHAT -> handleWechatCommand(command);
                default -> log.warn("不支持的平台类型: {}", command.getPlatform());
            }
            
        } catch (Exception e) {
            log.error("处理机器人命令失败: {}", command, e);
        }
    }

    /**
     * 处理Telegram消息
     */
    private void handleTelegramMessage(BotMessage message) {
        try {
            log.info("处理Telegram消息: {}", message.getContent());

            // 标记消息为处理中
            message.setProcessStatus(BotMessage.ProcessStatus.PROCESSING);

            // 根据消息类型处理
            switch (message.getMessageType()) {
                case COMMAND -> {
                    // 处理命令消息
                    var result = commandProcessingEngine.processCommand(
                        message.getContent(),
                        PlatformType.TELEGRAM,
                        Long.parseLong(message.getChatId()),
                        message.getUserId()
                    );

                    // 发送处理结果
                    if (result.getSuccess()) {
                        messageProcessor.sendTelegramMessage(message.getChatId(), result.getMessage());
                        message.markAsSuccess();
                    } else {
                        messageProcessor.sendTelegramMessage(message.getChatId(), "❌ " + result.getMessage());
                        message.setProcessStatus(BotMessage.ProcessStatus.FAILED);
                        message.setErrorMessage(result.getMessage());
                    }
                }
                case TEXT -> {
                    // 处理普通文本消息
                    String response = messageProcessor.processTextMessage(message);
                    if (response != null) {
                        messageProcessor.sendTelegramMessage(message.getChatId(), response);
                    }
                    message.markAsSuccess();
                }
                case CALLBACK -> {
                    // 处理回调消息
                    messageProcessor.processTelegramCallback(message);
                    message.markAsSuccess();
                }
                default -> {
                    log.warn("不支持的Telegram消息类型: {}", message.getMessageType());
                    message.setProcessStatus(BotMessage.ProcessStatus.FAILED);
                    message.setErrorMessage("不支持的消息类型");
                }
            }

        } catch (Exception e) {
            log.error("处理Telegram消息失败: {}", message, e);
            message.setProcessStatus(BotMessage.ProcessStatus.FAILED);
            message.setErrorMessage(e.getMessage());

            // 发送错误消息给用户
            try {
                messageProcessor.sendTelegramMessage(message.getChatId(), "❌ 处理消息时发生错误，请稍后重试");
            } catch (Exception sendError) {
                log.error("发送错误消息失败", sendError);
            }
        }
    }

    /**
     * 处理Discord消息
     */
    private void handleDiscordMessage(BotMessage message) {
        try {
            log.info("处理Discord消息: {}", message.getContent());

            // 标记消息为处理中
            message.setProcessStatus(BotMessage.ProcessStatus.PROCESSING);

            // 根据消息类型处理
            switch (message.getMessageType()) {
                case COMMAND -> {
                    // 处理命令消息
                    var result = commandProcessingEngine.processCommand(
                        message.getContent(),
                        PlatformType.DISCORD,
                        Long.parseLong(message.getChatId()),
                        message.getUserId()
                    );

                    // 发送处理结果
                    if (result.getSuccess()) {
                        messageProcessor.sendDiscordMessage(message.getChatId(), result.getMessage());
                        message.markAsSuccess();
                    } else {
                        messageProcessor.sendDiscordMessage(message.getChatId(), "❌ " + result.getMessage());
                        message.setProcessStatus(BotMessage.ProcessStatus.FAILED);
                        message.setErrorMessage(result.getMessage());
                    }
                }
                case TEXT -> {
                    // 处理普通文本消息
                    String response = messageProcessor.processTextMessage(message);
                    if (response != null) {
                        messageProcessor.sendDiscordMessage(message.getChatId(), response);
                    }
                    message.markAsSuccess();
                }
                case INTERACTION -> {
                    // 处理Discord交互消息
                    messageProcessor.processDiscordInteraction(message);
                    message.markAsSuccess();
                }
                default -> {
                    log.warn("不支持的Discord消息类型: {}", message.getMessageType());
                    message.setProcessStatus(BotMessage.ProcessStatus.FAILED);
                    message.setErrorMessage("不支持的消息类型");
                }
            }

        } catch (Exception e) {
            log.error("处理Discord消息失败: {}", message, e);
            message.setProcessStatus(BotMessage.ProcessStatus.FAILED);
            message.setErrorMessage(e.getMessage());

            // 发送错误消息给用户
            try {
                messageProcessor.sendDiscordMessage(message.getChatId(), "❌ 处理消息时发生错误，请稍后重试");
            } catch (Exception sendError) {
                log.error("发送错误消息失败", sendError);
            }
        }
    }

    /**
     * 处理微信消息
     */
    private void handleWechatMessage(BotMessage message) {
        try {
            log.info("处理微信消息: {}", message.getContent());

            // 标记消息为处理中
            message.setProcessStatus(BotMessage.ProcessStatus.PROCESSING);

            // 根据消息类型处理
            switch (message.getMessageType()) {
                case COMMAND -> {
                    // 处理命令消息
                    var result = commandProcessingEngine.processCommand(
                        message.getContent(),
                        PlatformType.WECHAT,
                        Long.parseLong(message.getChatId()),
                        message.getUserId()
                    );

                    // 发送处理结果
                    if (result.getSuccess()) {
                        messageProcessor.sendWechatMessage(message.getChatId(), result.getMessage());
                        message.markAsSuccess();
                    } else {
                        messageProcessor.sendWechatMessage(message.getChatId(), "❌ " + result.getMessage());
                        message.setProcessStatus(BotMessage.ProcessStatus.FAILED);
                        message.setErrorMessage(result.getMessage());
                    }
                }
                case TEXT -> {
                    // 处理普通文本消息
                    String response = messageProcessor.processTextMessage(message);
                    if (response != null) {
                        messageProcessor.sendWechatMessage(message.getChatId(), response);
                    }
                    message.markAsSuccess();
                }
                default -> {
                    log.warn("不支持的微信消息类型: {}", message.getMessageType());
                    message.setProcessStatus(BotMessage.ProcessStatus.FAILED);
                    message.setErrorMessage("不支持的消息类型");
                }
            }

        } catch (Exception e) {
            log.error("处理微信消息失败: {}", message, e);
            message.setProcessStatus(BotMessage.ProcessStatus.FAILED);
            message.setErrorMessage(e.getMessage());

            // 发送错误消息给用户
            try {
                messageProcessor.sendWechatMessage(message.getChatId(), "❌ 处理消息时发生错误，请稍后重试");
            } catch (Exception sendError) {
                log.error("发送错误消息失败", sendError);
            }
        }
    }

    /**
     * 处理Telegram通知
     */
    private void handleTelegramNotification(BotMessage notification) {
        try {
            log.info("处理Telegram通知: {}", notification.getContent());
            notificationService.sendTelegramNotification(notification);
            notification.markAsSuccess();
        } catch (Exception e) {
            log.error("处理Telegram通知失败: {}", notification, e);
            notification.setProcessStatus(BotMessage.ProcessStatus.FAILED);
            notification.setErrorMessage(e.getMessage());
        }
    }

    /**
     * 处理Discord通知
     */
    private void handleDiscordNotification(BotMessage notification) {
        try {
            log.info("处理Discord通知: {}", notification.getContent());
            notificationService.sendDiscordNotification(notification);
            notification.markAsSuccess();
        } catch (Exception e) {
            log.error("处理Discord通知失败: {}", notification, e);
            notification.setProcessStatus(BotMessage.ProcessStatus.FAILED);
            notification.setErrorMessage(e.getMessage());
        }
    }

    /**
     * 处理微信通知
     */
    private void handleWechatNotification(BotMessage notification) {
        try {
            log.info("处理微信通知: {}", notification.getContent());
            notificationService.sendWechatNotification(notification);
            notification.markAsSuccess();
        } catch (Exception e) {
            log.error("处理微信通知失败: {}", notification, e);
            notification.setProcessStatus(BotMessage.ProcessStatus.FAILED);
            notification.setErrorMessage(e.getMessage());
        }
    }

    /**
     * 处理Telegram命令
     */
    private void handleTelegramCommand(BotMessage command) {
        try {
            log.info("处理Telegram命令: {}", command.getContent());

            command.setProcessStatus(BotMessage.ProcessStatus.PROCESSING);

            // 处理系统命令或记账命令
            var result = commandProcessingEngine.processCommand(
                command.getContent(),
                PlatformType.TELEGRAM,
                Long.parseLong(command.getChatId()),
                command.getUserId()
            );

            // 发送处理结果
            String response = result.getSuccess() ?
                "✅ " + result.getMessage() :
                "❌ " + result.getMessage();

            messageProcessor.sendTelegramMessage(command.getChatId(), response);

            if (result.getSuccess()) {
                command.markAsSuccess();
            } else {
                command.setProcessStatus(BotMessage.ProcessStatus.FAILED);
                command.setErrorMessage(result.getMessage());
            }

        } catch (Exception e) {
            log.error("处理Telegram命令失败: {}", command, e);
            command.setProcessStatus(BotMessage.ProcessStatus.FAILED);
            command.setErrorMessage(e.getMessage());

            try {
                messageProcessor.sendTelegramMessage(command.getChatId(), "❌ 命令处理失败: " + e.getMessage());
            } catch (Exception sendError) {
                log.error("发送错误消息失败", sendError);
            }
        }
    }

    /**
     * 处理Discord命令
     */
    private void handleDiscordCommand(BotMessage command) {
        try {
            log.info("处理Discord命令: {}", command.getContent());

            command.setProcessStatus(BotMessage.ProcessStatus.PROCESSING);

            // 处理系统命令或记账命令
            var result = commandProcessingEngine.processCommand(
                command.getContent(),
                PlatformType.DISCORD,
                Long.parseLong(command.getChatId()),
                command.getUserId()
            );

            // 发送处理结果
            String response = result.getSuccess() ?
                "✅ " + result.getMessage() :
                "❌ " + result.getMessage();

            messageProcessor.sendDiscordMessage(command.getChatId(), response);

            if (result.getSuccess()) {
                command.markAsSuccess();
            } else {
                command.setProcessStatus(BotMessage.ProcessStatus.FAILED);
                command.setErrorMessage(result.getMessage());
            }

        } catch (Exception e) {
            log.error("处理Discord命令失败: {}", command, e);
            command.setProcessStatus(BotMessage.ProcessStatus.FAILED);
            command.setErrorMessage(e.getMessage());

            try {
                messageProcessor.sendDiscordMessage(command.getChatId(), "❌ 命令处理失败: " + e.getMessage());
            } catch (Exception sendError) {
                log.error("发送错误消息失败", sendError);
            }
        }
    }

    /**
     * 处理微信命令
     */
    private void handleWechatCommand(BotMessage command) {
        try {
            log.info("处理微信命令: {}", command.getContent());

            command.setProcessStatus(BotMessage.ProcessStatus.PROCESSING);

            // 处理系统命令或记账命令
            var result = commandProcessingEngine.processCommand(
                command.getContent(),
                PlatformType.WECHAT,
                Long.parseLong(command.getChatId()),
                command.getUserId()
            );

            // 发送处理结果
            String response = result.getSuccess() ?
                "✅ " + result.getMessage() :
                "❌ " + result.getMessage();

            messageProcessor.sendWechatMessage(command.getChatId(), response);

            if (result.getSuccess()) {
                command.markAsSuccess();
            } else {
                command.setProcessStatus(BotMessage.ProcessStatus.FAILED);
                command.setErrorMessage(result.getMessage());
            }

        } catch (Exception e) {
            log.error("处理微信命令失败: {}", command, e);
            command.setProcessStatus(BotMessage.ProcessStatus.FAILED);
            command.setErrorMessage(e.getMessage());

            try {
                messageProcessor.sendWechatMessage(command.getChatId(), "❌ 命令处理失败: " + e.getMessage());
            } catch (Exception sendError) {
                log.error("发送错误消息失败", sendError);
            }
        }
    }
}
