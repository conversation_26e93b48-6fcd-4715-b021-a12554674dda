package top.continew.admin.accounting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import top.continew.admin.accounting.model.entity.WalletDO;
import top.continew.admin.accounting.model.resp.WalletListResp;

import java.math.BigDecimal;
import java.util.List;

/**
 * 钱包 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface WalletMapper extends BaseMapper<WalletDO> {

    /**
     * 查询群组钱包列表
     *
     * @param groupId 群组ID
     * @return 钱包列表
     */
    @Select("""
        SELECT w.id, w.group_id, g.name as group_name, w.currency, w.balance, 
               w.frozen_amount, (w.balance - w.frozen_amount) as available_balance,
               w.last_update_time, w.create_time, w.status
        FROM acc_wallet w
        LEFT JOIN acc_group g ON w.group_id = g.id
        WHERE w.group_id = #{groupId} AND w.status = 1
        ORDER BY w.currency ASC, w.create_time ASC
        """)
    List<WalletListResp> selectGroupWallets(@Param("groupId") Long groupId);

    /**
     * 根据群组ID和币种查询钱包
     *
     * @param groupId  群组ID
     * @param currency 币种
     * @return 钱包信息
     */
    @Select("""
        SELECT * FROM acc_wallet 
        WHERE group_id = #{groupId} AND currency = #{currency} AND status = 1
        """)
    WalletDO selectByGroupIdAndCurrency(@Param("groupId") Long groupId, @Param("currency") String currency);

    /**
     * 更新钱包余额
     *
     * @param id      钱包ID
     * @param balance 新余额
     * @return 更新行数
     */
    @Update("""
        UPDATE acc_wallet 
        SET balance = #{balance}, last_update_time = NOW() 
        WHERE id = #{id}
        """)
    int updateBalance(@Param("id") Long id, @Param("balance") BigDecimal balance);

    /**
     * 原子性增加余额
     *
     * @param id     钱包ID
     * @param amount 增加金额
     * @return 更新行数
     */
    @Update("""
        UPDATE acc_wallet 
        SET balance = balance + #{amount}, last_update_time = NOW() 
        WHERE id = #{id}
        """)
    int addBalance(@Param("id") Long id, @Param("amount") BigDecimal amount);

    /**
     * 原子性减少余额
     *
     * @param id     钱包ID
     * @param amount 减少金额
     * @return 更新行数
     */
    @Update("""
        UPDATE acc_wallet 
        SET balance = balance - #{amount}, last_update_time = NOW() 
        WHERE id = #{id} AND balance >= #{amount}
        """)
    int subtractBalance(@Param("id") Long id, @Param("amount") BigDecimal amount);

    /**
     * 冻结金额
     *
     * @param id     钱包ID
     * @param amount 冻结金额
     * @return 更新行数
     */
    @Update("""
        UPDATE acc_wallet 
        SET frozen_amount = frozen_amount + #{amount}, last_update_time = NOW() 
        WHERE id = #{id} AND (balance - frozen_amount) >= #{amount}
        """)
    int freezeAmount(@Param("id") Long id, @Param("amount") BigDecimal amount);

    /**
     * 解冻金额
     *
     * @param id     钱包ID
     * @param amount 解冻金额
     * @return 更新行数
     */
    @Update("""
        UPDATE acc_wallet 
        SET frozen_amount = frozen_amount - #{amount}, last_update_time = NOW() 
        WHERE id = #{id} AND frozen_amount >= #{amount}
        """)
    int unfreezeAmount(@Param("id") Long id, @Param("amount") BigDecimal amount);

    /**
     * 检查余额是否足够
     *
     * @param groupId  群组ID
     * @param currency 币种
     * @param amount   需要的金额
     * @return 是否足够
     */
    @Select("""
        SELECT COUNT(*) > 0 FROM acc_wallet 
        WHERE group_id = #{groupId} AND currency = #{currency} 
        AND (balance - frozen_amount) >= #{amount} AND status = 1
        """)
    boolean hasEnoughBalance(@Param("groupId") Long groupId, @Param("currency") String currency, @Param("amount") BigDecimal amount);

    /**
     * 获取钱包余额
     *
     * @param groupId  群组ID
     * @param currency 币种
     * @return 余额
     */
    @Select("""
        SELECT COALESCE(balance, 0) FROM acc_wallet 
        WHERE group_id = #{groupId} AND currency = #{currency} AND status = 1
        """)
    BigDecimal getBalance(@Param("groupId") Long groupId, @Param("currency") String currency);

    /**
     * 获取可用余额
     *
     * @param groupId  群组ID
     * @param currency 币种
     * @return 可用余额
     */
    @Select("""
        SELECT COALESCE(balance - frozen_amount, 0) FROM acc_wallet 
        WHERE group_id = #{groupId} AND currency = #{currency} AND status = 1
        """)
    BigDecimal getAvailableBalance(@Param("groupId") Long groupId, @Param("currency") String currency);

    /**
     * 统计群组钱包数量
     *
     * @param groupId 群组ID
     * @return 钱包数量
     */
    @Select("""
        SELECT COUNT(*) FROM acc_wallet 
        WHERE group_id = #{groupId} AND status = 1
        """)
    int countByGroupId(@Param("groupId") Long groupId);
}
