package top.continew.admin.accounting.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.accounting.model.req.NotificationBatchSendReq;
import top.continew.admin.accounting.model.req.NotificationSendReq;
import top.continew.admin.accounting.model.resp.NotificationSendResp;
import top.continew.admin.accounting.model.resp.NotificationStatisticsResp;
import top.continew.admin.accounting.service.NotificationService;
import top.continew.starter.web.model.R;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 通知推送 API
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "通知推送 API")
@RestController
@RequestMapping("/accounting/notification")
@RequiredArgsConstructor
@Validated
public class NotificationController {

    private final NotificationService notificationService;

    // ==================== 发送通知 ====================

    @Operation(summary = "发送通知", description = "发送单条通知")
    @PostMapping("/send")
    public R<NotificationSendResp> sendNotification(@Valid @RequestBody NotificationSendReq req) {
        NotificationSendResp result = notificationService.sendNotification(req);
        return R.ok(result);
    }

    @Operation(summary = "异步发送通知", description = "异步发送单条通知")
    @PostMapping("/send/async")
    public R<String> sendNotificationAsync(@Valid @RequestBody NotificationSendReq req) {
        CompletableFuture<NotificationSendResp> future = notificationService.sendNotificationAsync(req);
        return R.ok("通知已提交异步发送");
    }

    @Operation(summary = "批量发送通知", description = "批量发送多条通知")
    @PostMapping("/send/batch")
    public R<List<NotificationSendResp>> batchSendNotifications(@Valid @RequestBody NotificationBatchSendReq req) {
        List<NotificationSendResp> results = notificationService.batchSendNotifications(req);
        return R.ok(results);
    }

    @Operation(summary = "异步批量发送通知", description = "异步批量发送多条通知")
    @PostMapping("/send/batch/async")
    public R<String> batchSendNotificationsAsync(@Valid @RequestBody NotificationBatchSendReq req) {
        CompletableFuture<List<NotificationSendResp>> future = notificationService.batchSendNotificationsAsync(req);
        return R.ok("批量通知已提交异步发送");
    }

    @Operation(summary = "发送模板通知", description = "使用模板发送通知")
    @PostMapping("/send/template")
    public R<NotificationSendResp> sendTemplateNotification(
            @Parameter(description = "模板代码") @RequestParam String templateCode,
            @Parameter(description = "模板参数") @RequestBody Map<String, Object> templateParams,
            @Parameter(description = "发送渠道") @RequestParam List<String> channels,
            @Parameter(description = "目标用户") @RequestParam List<Long> targetUsers) {
        NotificationSendResp result = notificationService.sendTemplateNotification(
                templateCode, templateParams, channels, targetUsers);
        return R.ok(result);
    }

    // ==================== 计划发送 ====================

    @Operation(summary = "计划发送通知", description = "创建计划发送的通知")
    @PostMapping("/schedule")
    public R<Long> scheduleNotification(
            @Valid @RequestBody NotificationSendReq req,
            @Parameter(description = "计划发送时间") @RequestParam LocalDateTime scheduledTime) {
        Long notificationId = notificationService.scheduleNotification(req, scheduledTime);
        return R.ok(notificationId);
    }

    @Operation(summary = "取消计划通知", description = "取消计划发送的通知")
    @DeleteMapping("/schedule/{notificationId}")
    public R<Boolean> cancelScheduledNotification(
            @Parameter(description = "通知ID") @PathVariable Long notificationId) {
        Boolean result = notificationService.cancelScheduledNotification(notificationId);
        return R.ok(result);
    }

    @Operation(summary = "执行计划通知", description = "立即执行计划发送的通知")
    @PostMapping("/schedule/{notificationId}/execute")
    public R<NotificationSendResp> executeScheduledNotification(
            @Parameter(description = "通知ID") @PathVariable Long notificationId) {
        NotificationSendResp result = notificationService.executeScheduledNotification(notificationId);
        return R.ok(result);
    }

    // ==================== 重试机制 ====================

    @Operation(summary = "重试通知", description = "重试失败的通知")
    @PostMapping("/{notificationId}/retry")
    public R<NotificationSendResp> retryNotification(
            @Parameter(description = "通知ID") @PathVariable Long notificationId) {
        NotificationSendResp result = notificationService.retryNotification(notificationId);
        return R.ok(result);
    }

    @Operation(summary = "批量重试通知", description = "批量重试失败的通知")
    @PostMapping("/retry/batch")
    public R<List<NotificationSendResp>> batchRetryNotifications(
            @Parameter(description = "通知ID列表") @RequestBody List<Long> notificationIds) {
        List<NotificationSendResp> results = notificationService.batchRetryNotifications(notificationIds);
        return R.ok(results);
    }

    @Operation(summary = "自动重试失败通知", description = "自动重试所有符合条件的失败通知")
    @PostMapping("/retry/auto")
    public R<Integer> autoRetryFailedNotifications() {
        Integer retryCount = notificationService.autoRetryFailedNotifications();
        return R.ok(retryCount);
    }

    // ==================== 通知管理 ====================

    @Operation(summary = "获取通知详情", description = "获取通知的详细信息")
    @GetMapping("/{notificationId}")
    public R<Map<String, Object>> getNotificationDetail(
            @Parameter(description = "通知ID") @PathVariable Long notificationId) {
        Map<String, Object> detail = notificationService.getNotificationDetail(notificationId);
        return R.ok(detail);
    }

    @Operation(summary = "获取通知日志", description = "获取通知的发送日志")
    @GetMapping("/{notificationId}/logs")
    public R<List<Map<String, Object>>> getNotificationLogs(
            @Parameter(description = "通知ID") @PathVariable Long notificationId) {
        List<Map<String, Object>> logs = notificationService.getNotificationLogs(notificationId);
        return R.ok(logs);
    }

    @Operation(summary = "更新通知状态", description = "手动更新通知状态")
    @PutMapping("/{notificationId}/status")
    public R<Void> updateNotificationStatus(
            @Parameter(description = "通知ID") @PathVariable Long notificationId,
            @Parameter(description = "新状态") @RequestParam String status,
            @Parameter(description = "错误信息") @RequestParam(required = false) String errorMessage) {
        notificationService.updateNotificationStatus(notificationId, status, errorMessage);
        return R.ok();
    }

    @Operation(summary = "删除过期通知", description = "删除过期的通知记录")
    @DeleteMapping("/expired")
    public R<Integer> deleteExpiredNotifications(
            @Parameter(description = "过期时间点") @RequestParam LocalDateTime expiredBefore) {
        Integer deletedCount = notificationService.deleteExpiredNotifications(expiredBefore);
        return R.ok(deletedCount);
    }

    // ==================== 统计分析 ====================

    @Operation(summary = "获取通知统计", description = "获取通知发送统计信息")
    @GetMapping("/statistics")
    public R<NotificationStatisticsResp> getNotificationStatistics(
            @Parameter(description = "群组ID") @RequestParam(required = false) Long groupId,
            @Parameter(description = "开始日期") @RequestParam(required = false) String startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) String endDate) {
        NotificationStatisticsResp statistics = notificationService.getNotificationStatistics(groupId, startDate, endDate);
        return R.ok(statistics);
    }

    @Operation(summary = "获取渠道性能统计", description = "获取各渠道的性能统计")
    @GetMapping("/statistics/channel-performance")
    public R<Map<String, Object>> getChannelPerformanceStatistics(
            @Parameter(description = "群组ID") @RequestParam(required = false) Long groupId,
            @Parameter(description = "开始日期") @RequestParam(required = false) String startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) String endDate) {
        Map<String, Object> statistics = notificationService.getChannelPerformanceStatistics(groupId, startDate, endDate);
        return R.ok(statistics);
    }

    @Operation(summary = "获取发送趋势", description = "获取通知发送趋势数据")
    @GetMapping("/statistics/trends")
    public R<List<Map<String, Object>>> getSendTrends(
            @Parameter(description = "群组ID") @RequestParam(required = false) Long groupId,
            @Parameter(description = "开始日期") @RequestParam(required = false) String startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) String endDate,
            @Parameter(description = "分组方式") @RequestParam(defaultValue = "day") String groupBy) {
        List<Map<String, Object>> trends = notificationService.getSendTrends(groupId, startDate, endDate, groupBy);
        return R.ok(trends);
    }

    @Operation(summary = "获取失败分析", description = "获取通知发送失败分析")
    @GetMapping("/statistics/failure-analysis")
    public R<Map<String, Object>> getFailureAnalysis(
            @Parameter(description = "群组ID") @RequestParam(required = false) Long groupId,
            @Parameter(description = "开始日期") @RequestParam(required = false) String startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) String endDate) {
        Map<String, Object> analysis = notificationService.getFailureAnalysis(groupId, startDate, endDate);
        return R.ok(analysis);
    }

    // ==================== 渠道管理 ====================

    @Operation(summary = "测试渠道连接", description = "测试通知渠道的连接状态")
    @PostMapping("/channel/{channel}/test")
    public R<Map<String, Object>> testChannelConnection(
            @Parameter(description = "渠道代码") @PathVariable String channel,
            @Parameter(description = "配置参数") @RequestBody Map<String, Object> config) {
        Map<String, Object> result = notificationService.testChannelConnection(channel, config);
        return R.ok(result);
    }

    @Operation(summary = "获取渠道状态", description = "获取所有通知渠道的状态")
    @GetMapping("/channel/status")
    public R<List<Map<String, Object>>> getChannelStatus() {
        List<Map<String, Object>> status = notificationService.getChannelStatus();
        return R.ok(status);
    }

    @Operation(summary = "更新渠道状态", description = "启用或禁用通知渠道")
    @PutMapping("/channel/{channel}/status")
    public R<Void> updateChannelStatus(
            @Parameter(description = "渠道代码") @PathVariable String channel,
            @Parameter(description = "是否启用") @RequestParam Boolean enabled) {
        notificationService.updateChannelStatus(channel, enabled);
        return R.ok();
    }

    @Operation(summary = "获取渠道配置", description = "获取通知渠道的配置信息")
    @GetMapping("/channel/{channel}/config")
    public R<Map<String, Object>> getChannelConfig(
            @Parameter(description = "渠道代码") @PathVariable String channel) {
        Map<String, Object> config = notificationService.getChannelConfig(channel);
        return R.ok(config);
    }

    @Operation(summary = "更新渠道配置", description = "更新通知渠道的配置信息")
    @PutMapping("/channel/{channel}/config")
    public R<Void> updateChannelConfig(
            @Parameter(description = "渠道代码") @PathVariable String channel,
            @Parameter(description = "配置参数") @RequestBody Map<String, Object> config) {
        notificationService.updateChannelConfig(channel, config);
        return R.ok();
    }

    // ==================== 模板管理 ====================

    @Operation(summary = "渲染模板", description = "渲染通知模板")
    @PostMapping("/template/{templateCode}/render")
    public R<Map<String, String>> renderTemplate(
            @Parameter(description = "模板代码") @PathVariable String templateCode,
            @Parameter(description = "渠道") @RequestParam String channel,
            @Parameter(description = "参数") @RequestBody Map<String, Object> params) {
        Map<String, String> result = notificationService.renderTemplate(templateCode, channel, params);
        return R.ok(result);
    }

    @Operation(summary = "验证模板", description = "验证通知模板的语法和参数")
    @PostMapping("/template/{templateCode}/validate")
    public R<Map<String, Object>> validateTemplate(
            @Parameter(description = "模板代码") @PathVariable String templateCode,
            @Parameter(description = "参数") @RequestBody Map<String, Object> params) {
        Map<String, Object> result = notificationService.validateTemplate(templateCode, params);
        return R.ok(result);
    }

    @Operation(summary = "预览模板", description = "预览通知模板的渲染效果")
    @PostMapping("/template/{templateCode}/preview")
    public R<Map<String, String>> previewTemplate(
            @Parameter(description = "模板代码") @PathVariable String templateCode,
            @Parameter(description = "渠道") @RequestParam String channel,
            @Parameter(description = "参数") @RequestBody Map<String, Object> params) {
        Map<String, String> result = notificationService.previewTemplate(templateCode, channel, params);
        return R.ok(result);
    }

}
