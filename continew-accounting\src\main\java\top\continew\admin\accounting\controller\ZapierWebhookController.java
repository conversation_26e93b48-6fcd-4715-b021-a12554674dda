package top.continew.admin.accounting.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.accounting.service.ZapierWebhookService;
import top.continew.starter.core.util.response.Response;

import java.util.Map;

/**
 * Zapier Webhook执行管理
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "Zapier Webhook执行管理")
@RestController
@RequestMapping("/accounting/zapier/webhook")
@RequiredArgsConstructor
@Validated
public class ZapierWebhookController {

    private final ZapierWebhookService zapierWebhookService;

    @Operation(summary = "手动触发事件", description = "手动触发指定群组和触发器类型的事件")
    @PostMapping("/trigger")
    public Response<Void> triggerEvent(
            @Parameter(description = "群组ID", example = "1") @RequestParam Long groupId,
            @Parameter(description = "触发器类型", example = "TRANSACTION_CREATED") @RequestParam String triggerType,
            @Parameter(description = "事件类型", example = "CREATE") @RequestParam String eventType,
            @Parameter(description = "业务ID", example = "123") @RequestParam(required = false) Long businessId,
            @Parameter(description = "业务类型", example = "TRANSACTION") @RequestParam(required = false) String businessType,
            @Parameter(description = "事件数据") @RequestBody(required = false) Map<String, Object> data) {
        zapierWebhookService.triggerEvent(groupId, triggerType, eventType, businessId, businessType, data);
        return Response.success();
    }

    @Operation(summary = "异步触发事件", description = "异步触发指定群组和触发器类型的事件")
    @PostMapping("/trigger-async")
    public Response<Void> triggerEventAsync(
            @Parameter(description = "群组ID", example = "1") @RequestParam Long groupId,
            @Parameter(description = "触发器类型", example = "TRANSACTION_CREATED") @RequestParam String triggerType,
            @Parameter(description = "事件类型", example = "CREATE") @RequestParam String eventType,
            @Parameter(description = "业务ID", example = "123") @RequestParam(required = false) Long businessId,
            @Parameter(description = "业务类型", example = "TRANSACTION") @RequestParam(required = false) String businessType,
            @Parameter(description = "事件数据") @RequestBody(required = false) Map<String, Object> data) {
        zapierWebhookService.triggerEventAsync(groupId, triggerType, eventType, businessId, businessType, data);
        return Response.success();
    }

    @Operation(summary = "重试失败的执行", description = "重试指定的失败执行")
    @PostMapping("/retry/{logId}")
    public Response<Void> retryExecution(
            @Parameter(description = "日志ID", example = "1") @PathVariable Long logId) {
        zapierWebhookService.retryExecution(logId);
        return Response.success();
    }

    @Operation(summary = "批量重试失败的执行", description = "批量重试指定配置在指定时间内的失败执行")
    @PostMapping("/batch-retry")
    public Response<Void> batchRetryFailures(
            @Parameter(description = "配置ID", example = "1") @RequestParam Long configId,
            @Parameter(description = "时间范围（小时）", example = "24") @RequestParam(defaultValue = "24") Integer hours) {
        zapierWebhookService.batchRetryFailures(configId, hours);
        return Response.success();
    }

    @Operation(summary = "验证Webhook URL", description = "验证指定Webhook URL的可达性")
    @PostMapping("/validate-url")
    public Response<Map<String, Object>> validateWebhookUrl(
            @Parameter(description = "Webhook URL") @RequestParam String webhookUrl) {
        return Response.success(zapierWebhookService.validateWebhookUrl(webhookUrl));
    }

    @Operation(summary = "获取执行统计", description = "获取指定群组的Webhook执行统计")
    @GetMapping("/stats/{groupId}")
    public Response<Map<String, Object>> getExecutionStatistics(
            @Parameter(description = "群组ID", example = "1") @PathVariable Long groupId) {
        return Response.success(zapierWebhookService.getExecutionStatistics(groupId));
    }

    @Operation(summary = "清理过期日志", description = "清理指定天数之前的过期执行日志")
    @DeleteMapping("/cleanup-logs")
    public Response<Integer> cleanupExpiredLogs(
            @Parameter(description = "保留天数", example = "30") @RequestParam Integer retentionDays) {
        return Response.success(zapierWebhookService.cleanupExpiredLogs(retentionDays));
    }

    @Operation(summary = "监控Webhook健康状态", description = "监控指定群组的Webhook健康状态")
    @GetMapping("/health/{groupId}")
    public Response<Map<String, Object>> monitorWebhookHealth(
            @Parameter(description = "群组ID", example = "1") @PathVariable Long groupId) {
        return Response.success(zapierWebhookService.monitorWebhookHealth(groupId));
    }

    @Operation(summary = "暂停配置执行", description = "暂停指定配置的Webhook执行")
    @PostMapping("/pause/{configId}")
    public Response<Void> pauseConfig(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long configId,
            @Parameter(description = "暂停原因", example = "维护中") @RequestParam String reason) {
        zapierWebhookService.pauseConfig(configId, reason);
        return Response.success();
    }

    @Operation(summary = "恢复配置执行", description = "恢复指定配置的Webhook执行")
    @PostMapping("/resume/{configId}")
    public Response<Void> resumeConfig(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long configId) {
        zapierWebhookService.resumeConfig(configId);
        return Response.success();
    }

    @Operation(summary = "获取执行队列状态", description = "获取当前Webhook执行队列的状态信息")
    @GetMapping("/queue-status")
    public Response<Map<String, Object>> getExecutionQueueStatus() {
        return Response.success(zapierWebhookService.getExecutionQueueStatus());
    }

    @Operation(summary = "手动触发定时重试", description = "手动触发定时重试任务的执行")
    @PostMapping("/process-retries")
    public Response<Void> processScheduledRetries() {
        zapierWebhookService.processScheduledRetries();
        return Response.success();
    }
}
