<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.accounting.mapper.SubscriptionMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="top.continew.admin.accounting.model.entity.SubscriptionDO">
        <id column="id" property="id" />
        <result column="group_id" property="groupId" />
        <result column="user_id" property="userId" />
        <result column="plan_id" property="planId" />
        <result column="status" property="status" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="trial_end_date" property="trialEndDate" />
        <result column="billing_cycle" property="billingCycle" />
        <result column="amount_paid" property="amountPaid" />
        <result column="currency" property="currency" />
        <result column="payment_method" property="paymentMethod" />
        <result column="auto_renew" property="autoRenew" />
        <result column="cancelled_at" property="cancelledAt" />
        <result column="cancellation_reason" property="cancellationReason" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, group_id, user_id, plan_id, status, start_date, end_date, trial_end_date,
        billing_cycle, amount_paid, currency, payment_method, auto_renew, cancelled_at,
        cancellation_reason, create_user, create_time, update_user, update_time
    </sql>

    <!-- 查询群组当前有效订阅 -->
    <select id="selectActiveByGroupId" parameterType="long" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM acc_subscription 
        WHERE group_id = #{groupId} 
        AND status = 'ACTIVE' 
        AND end_date > NOW()
        ORDER BY create_time DESC 
        LIMIT 1
    </select>

    <!-- 查询订阅详情（包含套餐信息） -->
    <select id="selectSubscriptionDetail" parameterType="long" resultType="top.continew.admin.accounting.model.resp.SubscriptionDetailResp">
        SELECT 
            s.id,
            s.group_id,
            s.user_id,
            s.plan_id,
            p.name AS planName,
            p.code AS planCode,
            s.status,
            s.start_date,
            s.end_date,
            s.trial_end_date,
            s.billing_cycle,
            s.amount_paid,
            s.currency,
            s.payment_method,
            s.auto_renew,
            s.cancelled_at,
            s.cancellation_reason,
            s.create_time,
            s.update_time,
            DATEDIFF(s.end_date, NOW()) AS remainingDays,
            CASE 
                WHEN s.end_date <= NOW() THEN 'EXPIRED'
                WHEN s.end_date <= DATE_ADD(NOW(), INTERVAL 7 DAY) THEN 'EXPIRING_SOON'
                ELSE 'NORMAL'
            END AS expirationStatus
        FROM acc_subscription s
        LEFT JOIN acc_subscription_plan p ON s.plan_id = p.id
        WHERE s.group_id = #{groupId} 
        AND s.status = 'ACTIVE' 
        AND s.end_date > NOW()
        ORDER BY s.create_time DESC 
        LIMIT 1
    </select>

    <!-- 查询用户订阅历史 -->
    <select id="selectUserSubscriptionHistory" parameterType="long" resultType="top.continew.admin.accounting.model.resp.SubscriptionListResp">
        SELECT 
            s.id,
            s.group_id,
            g.name AS groupName,
            s.plan_id,
            p.name AS planName,
            p.code AS planCode,
            s.status,
            s.start_date,
            s.end_date,
            s.billing_cycle,
            s.amount_paid,
            s.currency,
            s.auto_renew,
            s.create_time
        FROM acc_subscription s
        LEFT JOIN acc_subscription_plan p ON s.plan_id = p.id
        LEFT JOIN acc_group g ON s.group_id = g.id
        WHERE s.user_id = #{userId}
        ORDER BY s.create_time DESC
    </select>

    <!-- 查询群组订阅历史 -->
    <select id="selectGroupSubscriptionHistory" parameterType="long" resultType="top.continew.admin.accounting.model.resp.SubscriptionListResp">
        SELECT 
            s.id,
            s.group_id,
            s.user_id,
            u.nickname AS userName,
            s.plan_id,
            p.name AS planName,
            p.code AS planCode,
            s.status,
            s.start_date,
            s.end_date,
            s.billing_cycle,
            s.amount_paid,
            s.currency,
            s.auto_renew,
            s.create_time
        FROM acc_subscription s
        LEFT JOIN acc_subscription_plan p ON s.plan_id = p.id
        LEFT JOIN sys_user u ON s.user_id = u.id
        WHERE s.group_id = #{groupId}
        ORDER BY s.create_time DESC
    </select>

    <!-- 取消订阅 -->
    <update id="cancelSubscription">
        UPDATE acc_subscription 
        SET status = 'CANCELLED',
            cancelled_at = #{cancelledAt},
            cancellation_reason = #{reason},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 续费订阅 -->
    <update id="renewSubscription">
        UPDATE acc_subscription 
        SET end_date = #{newEndDate},
            status = 'ACTIVE',
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <!-- 更新订阅状态 -->
    <update id="updateStatus">
        UPDATE acc_subscription 
        SET status = #{status},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <!-- 查询即将过期的订阅 -->
    <select id="selectExpiringSubscriptions" parameterType="int" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM acc_subscription 
        WHERE status = 'ACTIVE' 
        AND end_date > NOW() 
        AND end_date <= DATE_ADD(NOW(), INTERVAL #{days} DAY)
        ORDER BY end_date ASC
    </select>

    <!-- 查询已过期的订阅 -->
    <select id="selectExpiredSubscriptions" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM acc_subscription 
        WHERE status = 'ACTIVE' 
        AND end_date <= NOW()
        ORDER BY end_date ASC
    </select>

    <!-- 统计订阅数量 -->
    <select id="countSubscriptions" resultType="long">
        SELECT COUNT(*) 
        FROM acc_subscription 
        <where>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="endDate != null">
                AND DATE(create_time) &lt;= DATE(#{endDate})
            </if>
        </where>
    </select>

    <!-- 计算收入 -->
    <select id="sumRevenue" resultType="decimal">
        SELECT COALESCE(SUM(amount_paid), 0) 
        FROM acc_subscription 
        WHERE status IN ('ACTIVE', 'EXPIRED', 'CANCELLED')
        <if test="startDate != null">
            AND create_time >= #{startDate}
        </if>
        <if test="endDate != null">
            AND create_time &lt;= #{endDate}
        </if>
    </select>

    <!-- 查询订阅趋势统计 -->
    <select id="selectSubscriptionTrend" resultType="map">
        SELECT 
            DATE(create_time) AS date,
            COUNT(*) AS newSubscriptions,
            COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) AS activeSubscriptions,
            COUNT(CASE WHEN status = 'CANCELLED' THEN 1 END) AS cancelledSubscriptions,
            SUM(amount_paid) AS revenue
        FROM acc_subscription 
        WHERE create_time >= #{startDate} 
        AND create_time &lt;= #{endDate}
        GROUP BY DATE(create_time)
        ORDER BY DATE(create_time) ASC
    </select>

    <!-- 查询套餐订阅分布 -->
    <select id="selectPlanDistribution" resultType="map">
        SELECT 
            p.name AS planName,
            p.code AS planCode,
            COUNT(*) AS subscriptionCount,
            COUNT(CASE WHEN s.status = 'ACTIVE' THEN 1 END) AS activeCount,
            SUM(s.amount_paid) AS totalRevenue
        FROM acc_subscription s
        LEFT JOIN acc_subscription_plan p ON s.plan_id = p.id
        GROUP BY s.plan_id, p.name, p.code
        ORDER BY subscriptionCount DESC
    </select>

    <!-- 查询订阅生命周期统计 -->
    <select id="selectSubscriptionLifecycleStats" resultType="map">
        SELECT 
            AVG(DATEDIFF(COALESCE(cancelled_at, end_date), start_date)) AS avgLifecycleDays,
            AVG(CASE WHEN status = 'ACTIVE' THEN DATEDIFF(NOW(), start_date) END) AS avgActiveLifecycleDays,
            AVG(CASE WHEN status = 'CANCELLED' THEN DATEDIFF(cancelled_at, start_date) END) AS avgCancelledLifecycleDays,
            COUNT(CASE WHEN auto_renew = 1 THEN 1 END) AS autoRenewCount,
            COUNT(*) AS totalCount,
            ROUND(COUNT(CASE WHEN auto_renew = 1 THEN 1 END) * 100.0 / COUNT(*), 2) AS autoRenewRate
        FROM acc_subscription
    </select>

    <!-- 查询流失分析 -->
    <select id="selectChurnAnalysis" resultType="map">
        SELECT 
            DATE_FORMAT(cancelled_at, '%Y-%m') AS month,
            COUNT(*) AS churnedSubscriptions,
            AVG(DATEDIFF(cancelled_at, start_date)) AS avgDaysBeforeChurn,
            GROUP_CONCAT(DISTINCT cancellation_reason) AS churnReasons
        FROM acc_subscription 
        WHERE status = 'CANCELLED' 
        AND cancelled_at >= #{startDate} 
        AND cancelled_at &lt;= #{endDate}
        GROUP BY DATE_FORMAT(cancelled_at, '%Y-%m')
        ORDER BY month DESC
    </select>

</mapper>
