package top.continew.admin.accounting.service;

import top.continew.admin.accounting.model.entity.SubscriptionPlanDO;
import top.continew.admin.accounting.model.req.SubscriptionPlanCreateReq;
import top.continew.admin.accounting.model.req.SubscriptionPlanUpdateReq;
import top.continew.admin.accounting.model.resp.SubscriptionPlanDetailResp;
import top.continew.admin.accounting.model.resp.SubscriptionPlanListResp;
import top.continew.admin.accounting.model.query.SubscriptionPlanQuery;
import top.continew.starter.extension.crud.service.BaseService;

import java.util.List;

/**
 * 订阅套餐服务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface SubscriptionPlanService extends BaseService<SubscriptionPlanDO, SubscriptionPlanListResp, SubscriptionPlanDetailResp, SubscriptionPlanQuery, SubscriptionPlanCreateReq> {

    /**
     * 查询启用的套餐列表
     */
    List<SubscriptionPlanListResp> listEnabledPlans();

    /**
     * 根据代码查询套餐
     */
    SubscriptionPlanDetailResp getByCode(String code);

    /**
     * 查询热门套餐
     */
    List<SubscriptionPlanListResp> listPopularPlans();

    /**
     * 更新套餐
     */
    void update(SubscriptionPlanUpdateReq req, Long id);

    /**
     * 启用套餐
     */
    void enable(Long id);

    /**
     * 禁用套餐
     */
    void disable(Long id);

    /**
     * 批量更新排序
     */
    void updateSortOrder(List<Long> ids);

    /**
     * 检查套餐是否存在
     */
    boolean exists(Long id);

    /**
     * 检查套餐代码是否存在
     */
    boolean existsByCode(String code, Long excludeId);

    /**
     * 获取套餐价格
     */
    java.math.BigDecimal getPlanPrice(Long planId, String billingCycle);

    /**
     * 验证套餐功能
     */
    boolean validatePlanFeature(Long planId, String featureName);

    /**
     * 获取套餐限制
     */
    Integer getPlanLimit(Long planId, String limitName);
}
