package top.continew.admin.accounting.model.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 债务列表响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "债务列表响应")
public class DebtListResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Schema(description = "ID", example = "1")
    @ExcelProperty("ID")
    private Long id;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 群组名称
     */
    @Schema(description = "群组名称", example = "家庭账本")
    @ExcelProperty("群组名称")
    private String groupName;

    /**
     * 债权人ID
     */
    @Schema(description = "债权人ID", example = "1")
    private Long creditorId;

    /**
     * 债权人姓名
     */
    @Schema(description = "债权人姓名", example = "张三")
    @ExcelProperty("债权人")
    private String creditorName;

    /**
     * 债务人ID
     */
    @Schema(description = "债务人ID", example = "2")
    private Long debtorId;

    /**
     * 债务人姓名
     */
    @Schema(description = "债务人姓名", example = "李四")
    @ExcelProperty("债务人")
    private String debtorName;

    /**
     * 原始金额
     */
    @Schema(description = "原始金额", example = "1000.00")
    @ExcelProperty("原始金额")
    private BigDecimal amount;

    /**
     * 已还金额
     */
    @Schema(description = "已还金额", example = "300.00")
    @ExcelProperty("已还金额")
    private BigDecimal paidAmount;

    /**
     * 剩余金额
     */
    @Schema(description = "剩余金额", example = "700.00")
    @ExcelProperty("剩余金额")
    private BigDecimal remainingAmount;

    /**
     * 币种
     */
    @Schema(description = "币种", example = "CNY")
    @ExcelProperty("币种")
    private String currency;

    /**
     * 描述
     */
    @Schema(description = "描述", example = "借款用于购买设备")
    @ExcelProperty("描述")
    private String description;

    /**
     * 到期时间
     */
    @Schema(description = "到期时间", example = "2025-02-01 00:00:00")
    @ExcelProperty("到期时间")
    private LocalDateTime dueDate;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "ACTIVE")
    @ExcelProperty("状态")
    private String status;

    /**
     * 状态描述
     */
    @Schema(description = "状态描述", example = "进行中")
    @ExcelProperty("状态描述")
    private String statusDesc;

    /**
     * 债务类型
     */
    @Schema(description = "债务类型", example = "PERSONAL")
    @ExcelProperty("债务类型")
    private String debtType;

    /**
     * 债务类型描述
     */
    @Schema(description = "债务类型描述", example = "个人借贷")
    @ExcelProperty("债务类型描述")
    private String debtTypeDesc;

    /**
     * 利率
     */
    @Schema(description = "利率", example = "0.05")
    @ExcelProperty("利率")
    private BigDecimal interestRate;

    /**
     * 最后还款时间
     */
    @Schema(description = "最后还款时间", example = "2025-01-15 10:30:00")
    @ExcelProperty("最后还款时间")
    private LocalDateTime lastPaymentDate;

    /**
     * 是否逾期
     */
    @Schema(description = "是否逾期", example = "false")
    @ExcelProperty("是否逾期")
    private Boolean overdue;

    /**
     * 逾期天数
     */
    @Schema(description = "逾期天数", example = "0")
    @ExcelProperty("逾期天数")
    private Integer overdueDays;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-01-01 10:00:00")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "紧急借款")
    @ExcelProperty("备注")
    private String remark;
}
