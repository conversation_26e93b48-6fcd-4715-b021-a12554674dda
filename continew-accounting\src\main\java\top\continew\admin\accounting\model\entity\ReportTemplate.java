package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.admin.common.model.entity.BaseEntity;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 报表模板实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("acc_report_template")
public class ReportTemplate extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模板ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long templateId;

    /**
     * 群组ID
     */
    private Long groupId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板描述
     */
    private String templateDescription;

    /**
     * 模板类型
     */
    private String templateType;

    /**
     * 模板状态
     */
    private String templateStatus;

    /**
     * 版本号
     */
    private String version;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 是否公开
     */
    private Boolean isPublic;

    /**
     * 是否为系统模板
     */
    private Boolean isSystemTemplate;

    /**
     * 报表配置JSON
     */
    private String reportConfigJson;

    /**
     * 数据源配置JSON
     */
    private String dataSourceConfigJson;

    /**
     * 布局配置JSON
     */
    private String layoutConfigJson;

    /**
     * 图表配置JSON
     */
    private String chartConfigsJson;

    /**
     * 过滤器配置JSON
     */
    private String filterConfigsJson;

    /**
     * 导出配置JSON
     */
    private String exportConfigJson;

    /**
     * 调度配置JSON
     */
    private String scheduleConfigJson;

    /**
     * 权限配置JSON
     */
    private String permissionConfigJson;

    /**
     * 标签JSON
     */
    private String tagsJson;

    /**
     * 扩展属性JSON
     */
    private String attributesJson;

    /**
     * 使用次数
     */
    private Integer usageCount;

    /**
     * 最后使用时间
     */
    private LocalDateTime lastUsedTime;

    /**
     * 最后使用人ID
     */
    private Long lastUsedBy;

    /**
     * 平均执行时间（毫秒）
     */
    private Long avgExecutionTime;

    /**
     * 最大执行时间（毫秒）
     */
    private Long maxExecutionTime;

    /**
     * 最小执行时间（毫秒）
     */
    private Long minExecutionTime;

    /**
     * 成功执行次数
     */
    private Integer successCount;

    /**
     * 失败执行次数
     */
    private Integer failureCount;

    /**
     * 用户评分
     */
    private Double rating;

    /**
     * 评分人数
     */
    private Integer ratingCount;

    /**
     * 收藏次数
     */
    private Integer favoriteCount;

    /**
     * 模板复杂度
     */
    private String complexity;

    /**
     * 平均数据量
     */
    private Integer avgDataVolume;

    /**
     * 最大数据量
     */
    private Integer maxDataVolume;

    /**
     * 最小数据量
     */
    private Integer minDataVolume;
}
