package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.accounting.enums.BillingCycle;
import top.continew.starter.core.util.validation.CheckUtils;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;

/**
 * 创建订阅请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "创建订阅请求")
public class SubscriptionCreateReq {

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    @NotNull(message = "群组ID不能为空")
    private Long groupId;

    /**
     * 套餐ID
     */
    @Schema(description = "套餐ID", example = "1")
    @NotNull(message = "套餐ID不能为空")
    private Long planId;

    /**
     * 计费周期
     */
    @Schema(description = "计费周期", example = "MONTHLY")
    @NotNull(message = "计费周期不能为空")
    private BillingCycle billingCycle;

    /**
     * 支付方式
     */
    @Schema(description = "支付方式", example = "STRIPE")
    private String paymentMethod;

    /**
     * 支付金额
     */
    @Schema(description = "支付金额", example = "9.99")
    @Positive(message = "支付金额必须大于0")
    private BigDecimal amountPaid;

    /**
     * 币种
     */
    @Schema(description = "币种", example = "USD")
    private String currency = "USD";

    /**
     * 自动续费
     */
    @Schema(description = "自动续费", example = "true")
    private Boolean autoRenew = true;

    /**
     * 优惠码
     */
    @Schema(description = "优惠码", example = "WELCOME2025")
    private String couponCode;
}
