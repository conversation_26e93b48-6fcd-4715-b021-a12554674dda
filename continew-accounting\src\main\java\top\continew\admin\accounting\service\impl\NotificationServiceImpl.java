package top.continew.admin.accounting.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.accounting.enums.NotificationStatusEnum;
import top.continew.admin.accounting.mapper.NotificationMapper;
import top.continew.admin.accounting.model.entity.NotificationDO;
import top.continew.admin.accounting.model.req.NotificationBatchSendReq;
import top.continew.admin.accounting.model.req.NotificationSendReq;
import top.continew.admin.accounting.model.resp.NotificationSendResp;
import top.continew.admin.accounting.model.resp.NotificationStatisticsResp;
import top.continew.admin.accounting.service.NotificationService;
import top.continew.admin.accounting.service.notification.NotificationChannel;
import top.continew.admin.accounting.service.notification.NotificationChannelFactory;
import top.continew.starter.core.util.validate.CheckUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 通知推送服务实现
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationServiceImpl implements NotificationService {

    private final NotificationMapper notificationMapper;
    private final NotificationChannelFactory channelFactory;

    /**
     * 发送进度缓存
     */
    private final Map<Long, Map<String, Object>> sendProgressCache = new ConcurrentHashMap<>();

    @Override
    @Transactional(rollbackFor = Exception.class)
    public NotificationSendResp sendNotification(NotificationSendReq req) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 创建通知记录
            NotificationDO notification = createNotification(req);
            notificationMapper.insert(notification);
            
            // 获取目标用户列表
            List<Long> targetUsers = resolveTargetUsers(req);
            CheckUtils.throwIfEmpty(targetUsers, "目标用户列表为空");
            
            // 过滤可用渠道
            List<String> availableChannels = channelFactory.filterAvailableChannels(
                    req.getChannels(), req.getNotificationType());
            CheckUtils.throwIfEmpty(availableChannels, "没有可用的通知渠道");
            
            // 按优先级排序渠道
            availableChannels = channelFactory.sortChannelsByPriority(
                    availableChannels, req.getNotificationType());
            
            // 执行发送
            Map<String, Object> sendResult = executeSend(notification, targetUsers, availableChannels);
            
            // 更新通知状态
            updateNotificationAfterSend(notification.getId(), sendResult);
            
            // 构建响应
            return buildSendResponse(notification.getId(), sendResult, 
                    System.currentTimeMillis() - startTime);
            
        } catch (Exception e) {
            log.error("发送通知失败", e);
            throw new RuntimeException("发送通知失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Async("notificationExecutor")
    public CompletableFuture<NotificationSendResp> sendNotificationAsync(NotificationSendReq req) {
        return CompletableFuture.supplyAsync(() -> sendNotification(req));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<NotificationSendResp> batchSendNotifications(NotificationBatchSendReq req) {
        List<NotificationSendResp> results = new ArrayList<>();
        
        for (NotificationSendReq notification : req.getNotifications()) {
            try {
                NotificationSendResp result = sendNotification(notification);
                results.add(result);
                
                // 批次间延迟
                if (req.getNotifications().size() > 1) {
                    Thread.sleep(500);
                }
                
            } catch (Exception e) {
                log.error("批量发送通知失败: {}", notification, e);
                
                // 创建失败响应
                NotificationSendResp failedResp = new NotificationSendResp();
                failedResp.setStatus(NotificationStatusEnum.FAILED.name());
                failedResp.setErrorMessage(e.getMessage());
                failedResp.setSendTime(LocalDateTime.now());
                results.add(failedResp);
            }
        }
        
        return results;
    }

    @Override
    @Async("notificationExecutor")
    public CompletableFuture<List<NotificationSendResp>> batchSendNotificationsAsync(NotificationBatchSendReq req) {
        return CompletableFuture.supplyAsync(() -> batchSendNotifications(req));
    }

    @Override
    public NotificationSendResp sendTemplateNotification(String templateCode, Map<String, Object> templateParams,
                                                        List<String> channels, List<Long> targetUsers) {
        // 构建发送请求
        NotificationSendReq req = new NotificationSendReq();
        req.setTemplateCode(templateCode);
        req.setTemplateParams(templateParams);
        req.setChannels(channels);
        req.setTargetUsers(targetUsers);
        req.setNotificationType("TEMPLATE");
        
        return sendNotification(req);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long scheduleNotification(NotificationSendReq req, LocalDateTime scheduledTime) {
        // 创建计划通知记录
        NotificationDO notification = createNotification(req);
        notification.setScheduledTime(scheduledTime);
        notification.setStatus(NotificationStatusEnum.PENDING.name());
        
        notificationMapper.insert(notification);
        
        log.info("创建计划通知: id={}, scheduledTime={}", notification.getId(), scheduledTime);
        
        return notification.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelScheduledNotification(Long notificationId) {
        NotificationDO notification = notificationMapper.selectById(notificationId);
        CheckUtils.throwIfNull(notification, "通知不存在");
        
        if (!NotificationStatusEnum.PENDING.name().equals(notification.getStatus())) {
            throw new RuntimeException("只能取消待发送状态的通知");
        }
        
        notification.setStatus(NotificationStatusEnum.CANCELLED.name());
        notification.setUpdateTime(LocalDateTime.now());
        
        int updated = notificationMapper.updateById(notification);
        
        log.info("取消计划通知: id={}, success={}", notificationId, updated > 0);
        
        return updated > 0;
    }

    @Override
    public List<Long> getPendingScheduledNotifications(LocalDateTime currentTime, Integer limit) {
        return notificationMapper.selectPendingScheduledNotifications(currentTime, limit);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public NotificationSendResp executeScheduledNotification(Long notificationId) {
        NotificationDO notification = notificationMapper.selectById(notificationId);
        CheckUtils.throwIfNull(notification, "通知不存在");
        
        if (!NotificationStatusEnum.PENDING.name().equals(notification.getStatus())) {
            throw new RuntimeException("通知状态不正确，无法执行");
        }
        
        // 转换为发送请求
        NotificationSendReq req = convertToSendRequest(notification);
        
        // 执行发送
        return sendNotification(req);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public NotificationSendResp retryNotification(Long notificationId) {
        NotificationDO notification = notificationMapper.selectById(notificationId);
        CheckUtils.throwIfNull(notification, "通知不存在");
        
        // 检查重试次数
        if (notification.getRetryCount() >= notification.getMaxRetries()) {
            throw new RuntimeException("已达到最大重试次数");
        }
        
        // 更新重试次数
        notification.setRetryCount(notification.getRetryCount() + 1);
        notification.setStatus(NotificationStatusEnum.RETRYING.name());
        notification.setUpdateTime(LocalDateTime.now());
        notificationMapper.updateById(notification);
        
        // 转换为发送请求
        NotificationSendReq req = convertToSendRequest(notification);
        
        // 执行重试
        return sendNotification(req);
    }

    @Override
    public List<NotificationSendResp> batchRetryNotifications(List<Long> notificationIds) {
        return notificationIds.stream()
                .map(this::retryNotification)
                .collect(Collectors.toList());
    }

    @Override
    public List<Long> getNotificationsForRetry(LocalDateTime currentTime, Integer limit) {
        return notificationMapper.selectNotificationsForRetry(currentTime, limit);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer autoRetryFailedNotifications() {
        LocalDateTime currentTime = LocalDateTime.now();
        List<Long> retryNotifications = getNotificationsForRetry(currentTime, 100);
        
        int retryCount = 0;
        for (Long notificationId : retryNotifications) {
            try {
                retryNotification(notificationId);
                retryCount++;
            } catch (Exception e) {
                log.error("自动重试通知失败: id={}", notificationId, e);
            }
        }
        
        log.info("自动重试完成: total={}, success={}", retryNotifications.size(), retryCount);
        
        return retryCount;
    }

    @Override
    public Map<String, Object> getNotificationDetail(Long notificationId) {
        NotificationDO notification = notificationMapper.selectById(notificationId);
        CheckUtils.throwIfNull(notification, "通知不存在");
        
        Map<String, Object> detail = new HashMap<>();
        detail.put("id", notification.getId());
        detail.put("title", notification.getTitle());
        detail.put("content", notification.getContent());
        detail.put("notificationType", notification.getNotificationType());
        detail.put("priority", notification.getPriority());
        detail.put("status", notification.getStatus());
        detail.put("channels", notification.getChannels());
        detail.put("targetUsers", notification.getTargetUsers());
        detail.put("sendTime", notification.getSendTime());
        detail.put("scheduledTime", notification.getScheduledTime());
        detail.put("retryCount", notification.getRetryCount());
        detail.put("maxRetries", notification.getMaxRetries());
        detail.put("sendResult", notification.getSendResult());
        detail.put("createTime", notification.getCreateTime());
        detail.put("updateTime", notification.getUpdateTime());
        
        return detail;
    }

    @Override
    public List<Map<String, Object>> getNotificationLogs(Long notificationId) {
        return notificationMapper.selectNotificationLogs(notificationId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateNotificationStatus(Long notificationId, String status, String errorMessage) {
        NotificationDO notification = new NotificationDO();
        notification.setId(notificationId);
        notification.setStatus(status);
        notification.setUpdateTime(LocalDateTime.now());
        
        if (StrUtil.isNotBlank(errorMessage)) {
            Map<String, Object> sendResult = new HashMap<>();
            sendResult.put("errorMessage", errorMessage);
            sendResult.put("updateTime", LocalDateTime.now());
            notification.setSendResult(sendResult);
        }
        
        notificationMapper.updateById(notification);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteExpiredNotifications(LocalDateTime expiredBefore) {
        return notificationMapper.deleteExpiredNotifications(expiredBefore);
    }

    @Override
    public NotificationStatisticsResp getNotificationStatistics(Long groupId, String startDate, String endDate) {
        return notificationMapper.selectNotificationStatistics(groupId, startDate, endDate);
    }

    @Override
    public Map<String, Object> getChannelPerformanceStatistics(Long groupId, String startDate, String endDate) {
        return notificationMapper.selectChannelPerformanceStatistics(groupId, startDate, endDate);
    }

    @Override
    public List<Map<String, Object>> getSendTrends(Long groupId, String startDate, String endDate, String groupBy) {
        return notificationMapper.selectSendTrends(groupId, startDate, endDate, groupBy);
    }

    @Override
    public Map<String, Object> getFailureAnalysis(Long groupId, String startDate, String endDate) {
        return notificationMapper.selectFailureAnalysis(groupId, startDate, endDate);
    }

    @Override
    public Map<String, Object> testChannelConnection(String channel, Map<String, Object> config) {
        return channelFactory.testChannelConnection(channel, config);
    }

    @Override
    public List<Map<String, Object>> getChannelStatus() {
        return channelFactory.getChannelStatuses();
    }

    @Override
    public void updateChannelStatus(String channel, Boolean enabled) {
        // TODO: 实现渠道启用/禁用逻辑
        log.info("更新渠道状态: channel={}, enabled={}", channel, enabled);
    }

    @Override
    public Map<String, Object> getChannelConfig(String channel) {
        // TODO: 实现获取渠道配置逻辑
        return new HashMap<>();
    }

    @Override
    public void updateChannelConfig(String channel, Map<String, Object> config) {
        // TODO: 实现更新渠道配置逻辑
        log.info("更新渠道配置: channel={}, config={}", channel, config);
    }

    @Override
    public Map<String, String> renderTemplate(String templateCode, String channel, Map<String, Object> params) {
        // TODO: 实现模板渲染逻辑
        Map<String, String> result = new HashMap<>();
        result.put("title", "模板标题");
        result.put("content", "模板内容");
        return result;
    }

    @Override
    public Map<String, Object> validateTemplate(String templateCode, Map<String, Object> params) {
        // TODO: 实现模板验证逻辑
        Map<String, Object> result = new HashMap<>();
        result.put("valid", true);
        result.put("message", "模板验证通过");
        return result;
    }

    @Override
    public Map<String, String> previewTemplate(String templateCode, String channel, Map<String, Object> params) {
        // TODO: 实现模板预览逻辑
        return renderTemplate(templateCode, channel, params);
    }

    // ==================== 私有方法 ====================

    /**
     * 创建通知记录
     */
    private NotificationDO createNotification(NotificationSendReq req) {
        NotificationDO notification = new NotificationDO();
        
        notification.setTitle(req.getTitle());
        notification.setContent(req.getContent());
        notification.setNotificationType(req.getNotificationType());
        notification.setPriority(req.getPriority());
        notification.setChannels(req.getChannels());
        notification.setTargetUsers(req.getTargetUsers());
        notification.setTargetGroups(req.getTargetGroups());
        notification.setTargetRoles(req.getTargetRoles());
        notification.setScheduledTime(req.getScheduledTime());
        notification.setTemplateCode(req.getTemplateCode());
        notification.setTemplateParams(req.getTemplateParams());
        notification.setAttachments(req.getAttachments());
        notification.setExtraData(req.getExtraData());
        notification.setMaxRetries(req.getMaxRetries() != null ? req.getMaxRetries() : 3);
        notification.setRetryInterval(req.getRetryInterval() != null ? req.getRetryInterval() : 300);
        notification.setStatus(NotificationStatusEnum.PENDING.name());
        notification.setRetryCount(0);
        notification.setCreateTime(LocalDateTime.now());
        notification.setUpdateTime(LocalDateTime.now());
        
        return notification;
    }

    /**
     * 解析目标用户列表
     */
    private List<Long> resolveTargetUsers(NotificationSendReq req) {
        Set<Long> userSet = new HashSet<>();
        
        // 直接指定的用户
        if (CollUtil.isNotEmpty(req.getTargetUsers())) {
            userSet.addAll(req.getTargetUsers());
        }
        
        // TODO: 根据群组和角色解析用户
        
        return new ArrayList<>(userSet);
    }

    /**
     * 执行发送
     */
    private Map<String, Object> executeSend(NotificationDO notification, List<Long> targetUsers, 
                                          List<String> channels) {
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> channelResults = new HashMap<>();
        List<String> successChannels = new ArrayList<>();
        List<String> failedChannels = new ArrayList<>();
        
        for (String channelCode : channels) {
            try {
                NotificationChannel channel = channelFactory.getChannel(channelCode);
                if (channel == null) {
                    log.warn("渠道不存在: {}", channelCode);
                    failedChannels.add(channelCode);
                    continue;
                }
                
                Map<String, Object> channelResult = channel.sendNotification(notification, targetUsers);
                channelResults.put(channelCode, channelResult);
                
                Boolean success = (Boolean) channelResult.get("success");
                if (Boolean.TRUE.equals(success)) {
                    successChannels.add(channelCode);
                } else {
                    failedChannels.add(channelCode);
                }
                
            } catch (Exception e) {
                log.error("渠道发送失败: channel={}", channelCode, e);
                failedChannels.add(channelCode);
                channelResults.put(channelCode, Map.of(
                        "success", false,
                        "errorMessage", e.getMessage()
                ));
            }
        }
        
        result.put("channelResults", channelResults);
        result.put("successChannels", successChannels);
        result.put("failedChannels", failedChannels);
        result.put("totalChannels", channels.size());
        result.put("successChannelCount", successChannels.size());
        result.put("failedChannelCount", failedChannels.size());
        result.put("success", !successChannels.isEmpty());
        
        return result;
    }

    /**
     * 发送后更新通知状态
     */
    private void updateNotificationAfterSend(Long notificationId, Map<String, Object> sendResult) {
        NotificationDO notification = new NotificationDO();
        notification.setId(notificationId);
        notification.setSendTime(LocalDateTime.now());
        notification.setSendResult(sendResult);
        notification.setUpdateTime(LocalDateTime.now());
        
        Boolean success = (Boolean) sendResult.get("success");
        if (Boolean.TRUE.equals(success)) {
            notification.setStatus(NotificationStatusEnum.SENT.name());
        } else {
            notification.setStatus(NotificationStatusEnum.FAILED.name());
        }
        
        notificationMapper.updateById(notification);
    }

    /**
     * 构建发送响应
     */
    private NotificationSendResp buildSendResponse(Long notificationId, Map<String, Object> sendResult, long duration) {
        NotificationSendResp resp = new NotificationSendResp();
        
        resp.setNotificationId(notificationId);
        resp.setSendTime(LocalDateTime.now());
        resp.setDuration(duration);
        
        Boolean success = (Boolean) sendResult.get("success");
        resp.setStatus(Boolean.TRUE.equals(success) ? NotificationStatusEnum.SENT.name() : NotificationStatusEnum.FAILED.name());
        
        @SuppressWarnings("unchecked")
        List<String> successChannels = (List<String>) sendResult.get("successChannels");
        @SuppressWarnings("unchecked")
        List<String> failedChannels = (List<String>) sendResult.get("failedChannels");
        
        resp.setSuccessChannels(successChannels);
        resp.setFailedChannels(failedChannels);
        resp.setChannelResults((Map<String, Object>) sendResult.get("channelResults"));
        
        Integer totalChannels = (Integer) sendResult.get("totalChannels");
        Integer successChannelCount = (Integer) sendResult.get("successChannelCount");
        Integer failedChannelCount = (Integer) sendResult.get("failedChannelCount");
        
        resp.setTotalCount(totalChannels);
        resp.setSuccessCount(successChannelCount);
        resp.setFailedCount(failedChannelCount);
        
        // 判断是否需要重试
        resp.setNeedRetry(!failedChannels.isEmpty());
        if (resp.getNeedRetry()) {
            resp.setNextRetryTime(LocalDateTime.now().plusMinutes(5));
        }
        
        return resp;
    }

    /**
     * 转换为发送请求
     */
    private NotificationSendReq convertToSendRequest(NotificationDO notification) {
        NotificationSendReq req = new NotificationSendReq();
        
        req.setTitle(notification.getTitle());
        req.setContent(notification.getContent());
        req.setNotificationType(notification.getNotificationType());
        req.setPriority(notification.getPriority());
        req.setChannels(notification.getChannels());
        req.setTargetUsers(notification.getTargetUsers());
        req.setTargetGroups(notification.getTargetGroups());
        req.setTargetRoles(notification.getTargetRoles());
        req.setTemplateCode(notification.getTemplateCode());
        req.setTemplateParams(notification.getTemplateParams());
        req.setAttachments(notification.getAttachments());
        req.setExtraData(notification.getExtraData());
        req.setMaxRetries(notification.getMaxRetries());
        req.setRetryInterval(notification.getRetryInterval());
        
        return req;
    }

}
