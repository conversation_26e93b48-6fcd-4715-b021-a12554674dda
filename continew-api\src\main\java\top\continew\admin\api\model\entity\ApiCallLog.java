package top.continew.admin.api.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.data.mybatis.base.BaseEntity;

import java.time.LocalDateTime;

/**
 * API调用日志实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("acc_api_call_log")
public class ApiCallLog extends BaseEntity {

    /**
     * API密钥ID
     */
    @TableField("api_key_id")
    private Long apiKeyId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 请求路径
     */
    @TableField("request_path")
    private String requestPath;

    /**
     * 请求方法
     */
    @TableField("request_method")
    private String requestMethod;

    /**
     * 客户端IP
     */
    @TableField("client_ip")
    private String clientIp;

    /**
     * 用户代理
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 响应状态码
     */
    @TableField("response_status")
    private Integer responseStatus;

    /**
     * 响应时间（毫秒）
     */
    @TableField("response_time")
    private Long responseTime;

    /**
     * 请求大小（字节）
     */
    @TableField("request_size")
    private Long requestSize;

    /**
     * 响应大小（字节）
     */
    @TableField("response_size")
    private Long responseSize;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 调用时间
     */
    @TableField("call_time")
    private LocalDateTime callTime;
}
