package top.continew.admin.api.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.api.model.req.ApiKeyCreateReq;
import top.continew.admin.api.model.resp.ApiKeyResp;
import top.continew.admin.api.service.ApiKeyService;
import top.continew.starter.web.model.R;

import jakarta.validation.Valid;
import java.util.List;

/**
 * API认证控制器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "API认证管理", description = "API密钥的创建、管理和认证")
@RestController
@RequestMapping("/api/v1/auth")
@RequiredArgsConstructor
@Validated
public class ApiAuthController {

    private final ApiKeyService apiKeyService;

    @Operation(summary = "创建API密钥", description = "为用户创建新的API密钥")
    @PostMapping("/api-keys")
    public R<ApiKeyResp> createApiKey(@RequestBody @Valid ApiKeyCreateReq req) {
        ApiKeyResp result = apiKeyService.createApiKey(req);
        return R.ok(result);
    }

    @Operation(summary = "获取API密钥列表", description = "获取当前用户的API密钥列表")
    @GetMapping("/api-keys")
    public R<List<ApiKeyResp>> getApiKeys() {
        List<ApiKeyResp> result = apiKeyService.getApiKeys();
        return R.ok(result);
    }

    @Operation(summary = "获取API密钥详情", description = "获取指定API密钥的详细信息")
    @GetMapping("/api-keys/{id}")
    public R<ApiKeyResp> getApiKey(@PathVariable Long id) {
        ApiKeyResp result = apiKeyService.getApiKey(id);
        return R.ok(result);
    }

    @Operation(summary = "更新API密钥", description = "更新API密钥的配置信息")
    @PutMapping("/api-keys/{id}")
    public R<Void> updateApiKey(
        @PathVariable Long id,
        @RequestBody @Valid ApiKeyCreateReq req
    ) {
        apiKeyService.updateApiKey(id, req);
        return R.ok();
    }

    @Operation(summary = "删除API密钥", description = "删除指定的API密钥")
    @DeleteMapping("/api-keys/{id}")
    public R<Void> deleteApiKey(@PathVariable Long id) {
        apiKeyService.deleteApiKey(id);
        return R.ok();
    }

    @Operation(summary = "启用API密钥", description = "启用指定的API密钥")
    @PostMapping("/api-keys/{id}/enable")
    public R<Void> enableApiKey(@PathVariable Long id) {
        apiKeyService.enableApiKey(id);
        return R.ok();
    }

    @Operation(summary = "禁用API密钥", description = "禁用指定的API密钥")
    @PostMapping("/api-keys/{id}/disable")
    public R<Void> disableApiKey(@PathVariable Long id) {
        apiKeyService.disableApiKey(id);
        return R.ok();
    }

    @Operation(summary = "重新生成API密钥", description = "重新生成API密钥和密钥")
    @PostMapping("/api-keys/{id}/regenerate")
    public R<ApiKeyResp> regenerateApiKey(@PathVariable Long id) {
        ApiKeyResp result = apiKeyService.regenerateApiKey(id);
        return R.ok(result);
    }

    @Operation(summary = "验证API密钥", description = "验证API密钥的有效性")
    @PostMapping("/validate")
    public R<ApiKeyResp.ValidationResult> validateApiKey(
        @Parameter(description = "API密钥") @RequestParam String apiKey,
        @Parameter(description = "API密钥") @RequestParam(required = false) String apiSecret
    ) {
        ApiKeyResp.ValidationResult result = apiKeyService.validateApiKey(apiKey, apiSecret);
        return R.ok(result);
    }

    @Operation(summary = "获取API使用统计", description = "获取API密钥的使用统计信息")
    @GetMapping("/api-keys/{id}/statistics")
    public R<ApiKeyResp.UsageStatistics> getApiKeyStatistics(
        @PathVariable Long id,
        @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") int days
    ) {
        ApiKeyResp.UsageStatistics result = apiKeyService.getApiKeyStatistics(id, days);
        return R.ok(result);
    }

    @Operation(summary = "获取API调用日志", description = "获取API密钥的调用日志")
    @GetMapping("/api-keys/{id}/logs")
    public R<List<ApiKeyResp.CallLog>> getApiKeyLogs(
        @PathVariable Long id,
        @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
        @Parameter(description = "页大小") @RequestParam(defaultValue = "20") int size
    ) {
        List<ApiKeyResp.CallLog> result = apiKeyService.getApiKeyLogs(id, page, size);
        return R.ok(result);
    }

    @Operation(summary = "获取权限范围", description = "获取可用的权限范围列表")
    @GetMapping("/scopes")
    public R<List<ApiKeyResp.Scope>> getAvailableScopes() {
        List<ApiKeyResp.Scope> result = apiKeyService.getAvailableScopes();
        return R.ok(result);
    }

    @Operation(summary = "测试API连接", description = "测试API连接是否正常")
    @GetMapping("/test")
    public R<String> testConnection() {
        return R.ok("API连接正常");
    }
}
