package top.continew.admin.accounting.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import top.continew.admin.accounting.model.req.FileQueryReq;
import top.continew.admin.accounting.model.req.FileUploadReq;
import top.continew.admin.accounting.model.resp.FileStatisticsResp;
import top.continew.admin.accounting.model.resp.FileStorageResp;
import top.continew.admin.accounting.model.resp.FileUploadResp;
import top.continew.admin.accounting.service.FileStorageService;
import top.continew.starter.core.util.validate.CheckUtils;
import top.continew.starter.web.model.R;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * 文件存储控制器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "文件存储管理")
@RestController
@RequestMapping("/accounting/file-storage")
@RequiredArgsConstructor
@Validated
public class FileStorageController {

    private final FileStorageService fileStorageService;

    @Operation(summary = "上传文件", description = "上传单个文件")
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<FileUploadResp> uploadFile(
            @Parameter(description = "文件", required = true) @RequestParam("file") MultipartFile file,
            @Parameter(description = "上传请求") @Valid FileUploadReq uploadReq) {
        
        FileUploadResp result = fileStorageService.uploadFile(file, uploadReq);
        return R.ok(result);
    }

    @Operation(summary = "批量上传文件", description = "批量上传多个文件")
    @PostMapping(value = "/upload/batch", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<List<FileUploadResp>> uploadFiles(
            @Parameter(description = "文件列表", required = true) @RequestParam("files") List<MultipartFile> files,
            @Parameter(description = "上传请求") @Valid FileUploadReq uploadReq) {
        
        List<FileUploadResp> results = fileStorageService.uploadFiles(files, uploadReq);
        return R.ok(results);
    }

    @Operation(summary = "下载文件", description = "下载指定文件")
    @GetMapping("/download/{fileId}")
    public void downloadFile(
            @Parameter(description = "文件ID", required = true) @PathVariable Long fileId,
            HttpServletResponse response) throws IOException {
        
        FileStorageResp fileInfo = fileStorageService.getFileInfo(fileId);
        CheckUtils.throwIfNull(fileInfo, "文件不存在");
        
        try (InputStream inputStream = fileStorageService.downloadFile(fileId)) {
            response.setContentType(fileInfo.getMimeType());
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileInfo.getOriginalFileName() + "\"");
            response.setContentLengthLong(fileInfo.getFileSize());
            
            inputStream.transferTo(response.getOutputStream());
            response.flushBuffer();
        }
    }

    @Operation(summary = "获取下载URL", description = "获取文件下载链接")
    @GetMapping("/download-url/{fileId}")
    public R<String> getDownloadUrl(
            @Parameter(description = "文件ID", required = true) @PathVariable Long fileId,
            @Parameter(description = "过期时间（秒）") @RequestParam(required = false) Long expireTime) {
        
        String downloadUrl = fileStorageService.getDownloadUrl(fileId, expireTime);
        return R.ok(downloadUrl);
    }

    @Operation(summary = "获取预览URL", description = "获取文件预览链接")
    @GetMapping("/preview-url/{fileId}")
    public R<String> getPreviewUrl(
            @Parameter(description = "文件ID", required = true) @PathVariable Long fileId,
            @Parameter(description = "过期时间（秒）") @RequestParam(required = false) Long expireTime) {
        
        String previewUrl = fileStorageService.getPreviewUrl(fileId, expireTime);
        return R.ok(previewUrl);
    }

    @Operation(summary = "删除文件", description = "删除指定文件")
    @DeleteMapping("/{fileId}")
    public R<Boolean> deleteFile(
            @Parameter(description = "文件ID", required = true) @PathVariable Long fileId) {
        
        Boolean result = fileStorageService.deleteFile(fileId);
        return R.ok(result);
    }

    @Operation(summary = "批量删除文件", description = "批量删除多个文件")
    @DeleteMapping("/batch")
    public R<Map<Long, Boolean>> deleteFiles(
            @Parameter(description = "文件ID列表", required = true) @RequestBody List<Long> fileIds) {
        
        Map<Long, Boolean> results = fileStorageService.deleteFiles(fileIds);
        return R.ok(results);
    }

    @Operation(summary = "复制文件", description = "复制文件到指定位置")
    @PostMapping("/copy/{fileId}")
    public R<FileStorageResp> copyFile(
            @Parameter(description = "文件ID", required = true) @PathVariable Long fileId,
            @Parameter(description = "目标路径", required = true) @RequestParam String targetPath,
            @Parameter(description = "目标文件名", required = true) @RequestParam String targetName) {
        
        FileStorageResp result = fileStorageService.copyFile(fileId, targetPath, targetName);
        return R.ok(result);
    }

    @Operation(summary = "移动文件", description = "移动文件到指定位置")
    @PostMapping("/move/{fileId}")
    public R<Boolean> moveFile(
            @Parameter(description = "文件ID", required = true) @PathVariable Long fileId,
            @Parameter(description = "目标路径", required = true) @RequestParam String targetPath,
            @Parameter(description = "目标文件名", required = true) @RequestParam String targetName) {
        
        Boolean result = fileStorageService.moveFile(fileId, targetPath, targetName);
        return R.ok(result);
    }

    @Operation(summary = "重命名文件", description = "重命名指定文件")
    @PostMapping("/rename/{fileId}")
    public R<Boolean> renameFile(
            @Parameter(description = "文件ID", required = true) @PathVariable Long fileId,
            @Parameter(description = "新文件名", required = true) @RequestParam String newName) {
        
        Boolean result = fileStorageService.renameFile(fileId, newName);
        return R.ok(result);
    }

    @Operation(summary = "生成缩略图", description = "为图片文件生成缩略图")
    @PostMapping("/thumbnail/{fileId}")
    public R<String> generateThumbnail(
            @Parameter(description = "文件ID", required = true) @PathVariable Long fileId,
            @Parameter(description = "宽度") @RequestParam(required = false) Integer width,
            @Parameter(description = "高度") @RequestParam(required = false) Integer height) {
        
        String thumbnailUrl = fileStorageService.generateThumbnail(fileId, width, height);
        return R.ok(thumbnailUrl);
    }

    @Operation(summary = "添加水印", description = "为文件添加水印")
    @PostMapping("/watermark/{fileId}")
    public R<String> addWatermark(
            @Parameter(description = "文件ID", required = true) @PathVariable Long fileId,
            @Parameter(description = "水印文本", required = true) @RequestParam String watermarkText,
            @Parameter(description = "水印位置") @RequestParam(required = false, defaultValue = "bottom-right") String position) {
        
        String watermarkUrl = fileStorageService.addWatermark(fileId, watermarkText, position);
        return R.ok(watermarkUrl);
    }

    @Operation(summary = "安全扫描", description = "对文件进行安全扫描")
    @PostMapping("/security-scan/{fileId}")
    public R<Map<String, Object>> securityScan(
            @Parameter(description = "文件ID", required = true) @PathVariable Long fileId) {
        
        Map<String, Object> scanResult = fileStorageService.securityScan(fileId);
        return R.ok(scanResult);
    }

    @Operation(summary = "获取文件信息", description = "获取指定文件的详细信息")
    @GetMapping("/{fileId}")
    public R<FileStorageResp> getFileInfo(
            @Parameter(description = "文件ID", required = true) @PathVariable Long fileId) {
        
        FileStorageResp fileInfo = fileStorageService.getFileInfo(fileId);
        return R.ok(fileInfo);
    }

    @Operation(summary = "分页查询文件", description = "分页查询文件列表")
    @GetMapping("/page")
    public R<IPage<FileStorageResp>> pageFiles(@Valid FileQueryReq queryReq) {
        IPage<FileStorageResp> page = fileStorageService.pageFiles(queryReq);
        return R.ok(page);
    }

    @Operation(summary = "查询文件列表", description = "查询文件列表")
    @GetMapping("/list")
    public R<List<FileStorageResp>> listFiles(@Valid FileQueryReq queryReq) {
        List<FileStorageResp> list = fileStorageService.listFiles(queryReq);
        return R.ok(list);
    }

    @Operation(summary = "获取文件统计", description = "获取文件统计信息")
    @GetMapping("/statistics")
    public R<FileStatisticsResp> getFileStatistics(
            @Parameter(description = "群组ID") @RequestParam(required = false) Long groupId) {
        
        FileStatisticsResp statistics = fileStorageService.getFileStatistics(groupId);
        return R.ok(statistics);
    }

    @Operation(summary = "清理过期文件", description = "清理已过期的文件")
    @PostMapping("/clean/expired")
    public R<Long> cleanExpiredFiles() {
        Long cleanCount = fileStorageService.cleanExpiredFiles();
        return R.ok(cleanCount);
    }

    @Operation(summary = "清理临时文件", description = "清理指定天数前的临时文件")
    @PostMapping("/clean/temporary")
    public R<Long> cleanTemporaryFiles(
            @Parameter(description = "天数", required = true) @RequestParam Integer days) {
        
        Long cleanCount = fileStorageService.cleanTemporaryFiles(days);
        return R.ok(cleanCount);
    }

    @Operation(summary = "检查文件存在", description = "检查文件是否存在")
    @GetMapping("/exists/{fileId}")
    public R<Boolean> checkFileExists(
            @Parameter(description = "文件ID", required = true) @PathVariable Long fileId) {
        
        Boolean exists = fileStorageService.checkFileExists(fileId);
        return R.ok(exists);
    }

    @Operation(summary = "验证文件完整性", description = "验证文件完整性")
    @PostMapping("/validate/{fileId}")
    public R<Map<String, Object>> validateFileIntegrity(
            @Parameter(description = "文件ID", required = true) @PathVariable Long fileId) {
        
        Map<String, Object> result = fileStorageService.validateFileIntegrity(fileId);
        return R.ok(result);
    }

    @Operation(summary = "获取文件访问日志", description = "获取文件访问日志")
    @GetMapping("/access-log/{fileId}")
    public R<List<Map<String, Object>>> getFileAccessLog(
            @Parameter(description = "文件ID", required = true) @PathVariable Long fileId,
            @Parameter(description = "限制数量") @RequestParam(required = false, defaultValue = "100") Integer limit) {
        
        List<Map<String, Object>> accessLog = fileStorageService.getFileAccessLog(fileId, limit);
        return R.ok(accessLog);
    }

    @Operation(summary = "获取存储使用情况", description = "获取存储使用情况")
    @GetMapping("/storage-usage")
    public R<Map<String, Object>> getStorageUsage(
            @Parameter(description = "群组ID") @RequestParam(required = false) Long groupId) {
        
        Map<String, Object> usage = fileStorageService.getStorageUsage(groupId);
        return R.ok(usage);
    }

    @Operation(summary = "同步文件元数据", description = "同步文件元数据")
    @PostMapping("/sync-metadata/{fileId}")
    public R<Boolean> syncFileMetadata(
            @Parameter(description = "文件ID", required = true) @PathVariable Long fileId) {
        
        Boolean result = fileStorageService.syncFileMetadata(fileId);
        return R.ok(result);
    }

}
