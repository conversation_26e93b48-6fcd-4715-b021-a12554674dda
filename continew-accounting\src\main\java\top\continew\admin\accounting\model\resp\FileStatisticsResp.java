package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 文件统计响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "文件统计响应")
public class FileStatisticsResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 总文件数量
     */
    @Schema(description = "总文件数量", example = "1000")
    private Long totalFileCount;

    /**
     * 总文件大小（字节）
     */
    @Schema(description = "总文件大小（字节）", example = "**********")
    private Long totalFileSize;

    /**
     * 总文件大小（格式化）
     */
    @Schema(description = "总文件大小（格式化）", example = "1.0 GB")
    private String totalFileSizeFormatted;

    /**
     * 今日上传文件数量
     */
    @Schema(description = "今日上传文件数量", example = "50")
    private Long todayUploadCount;

    /**
     * 今日上传文件大小（字节）
     */
    @Schema(description = "今日上传文件大小（字节）", example = "52428800")
    private Long todayUploadSize;

    /**
     * 本月上传文件数量
     */
    @Schema(description = "本月上传文件数量", example = "500")
    private Long monthUploadCount;

    /**
     * 本月上传文件大小（字节）
     */
    @Schema(description = "本月上传文件大小（字节）", example = "524288000")
    private Long monthUploadSize;

    /**
     * 文件类型统计
     */
    @Schema(description = "文件类型统计")
    private List<FileTypeStatistics> fileTypeStatistics;

    /**
     * 存储类型统计
     */
    @Schema(description = "存储类型统计")
    private List<StorageTypeStatistics> storageTypeStatistics;

    /**
     * 访问权限统计
     */
    @Schema(description = "访问权限统计")
    private List<AccessTypeStatistics> accessTypeStatistics;

    /**
     * 上传趋势统计（最近30天）
     */
    @Schema(description = "上传趋势统计（最近30天）")
    private List<UploadTrendStatistics> uploadTrendStatistics;

    /**
     * 热门文件排行
     */
    @Schema(description = "热门文件排行")
    private List<PopularFileStatistics> popularFileStatistics;

    /**
     * 大文件排行
     */
    @Schema(description = "大文件排行")
    private List<LargeFileStatistics> largeFileStatistics;

    /**
     * 存储使用情况
     */
    @Schema(description = "存储使用情况")
    private Map<String, Object> storageUsage;

    /**
     * 文件类型统计
     */
    @Data
    @Schema(description = "文件类型统计")
    public static class FileTypeStatistics implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 文件类型
         */
        @Schema(description = "文件类型", example = "IMAGE")
        private String fileType;

        /**
         * 文件类型名称
         */
        @Schema(description = "文件类型名称", example = "图片")
        private String fileTypeName;

        /**
         * 文件数量
         */
        @Schema(description = "文件数量", example = "500")
        private Long fileCount;

        /**
         * 文件大小（字节）
         */
        @Schema(description = "文件大小（字节）", example = "536870912")
        private Long fileSize;

        /**
         * 占比
         */
        @Schema(description = "占比", example = "50.0")
        private Double percentage;
    }

    /**
     * 存储类型统计
     */
    @Data
    @Schema(description = "存储类型统计")
    public static class StorageTypeStatistics implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 存储类型
         */
        @Schema(description = "存储类型", example = "ALIYUN_OSS")
        private String storageType;

        /**
         * 存储类型名称
         */
        @Schema(description = "存储类型名称", example = "阿里云OSS")
        private String storageTypeName;

        /**
         * 文件数量
         */
        @Schema(description = "文件数量", example = "800")
        private Long fileCount;

        /**
         * 文件大小（字节）
         */
        @Schema(description = "文件大小（字节）", example = "858993459")
        private Long fileSize;

        /**
         * 占比
         */
        @Schema(description = "占比", example = "80.0")
        private Double percentage;
    }

    /**
     * 访问权限统计
     */
    @Data
    @Schema(description = "访问权限统计")
    public static class AccessTypeStatistics implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 访问权限类型
         */
        @Schema(description = "访问权限类型", example = "PRIVATE")
        private String accessType;

        /**
         * 访问权限类型名称
         */
        @Schema(description = "访问权限类型名称", example = "私有访问")
        private String accessTypeName;

        /**
         * 文件数量
         */
        @Schema(description = "文件数量", example = "700")
        private Long fileCount;

        /**
         * 占比
         */
        @Schema(description = "占比", example = "70.0")
        private Double percentage;
    }

    /**
     * 上传趋势统计
     */
    @Data
    @Schema(description = "上传趋势统计")
    public static class UploadTrendStatistics implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 日期
         */
        @Schema(description = "日期", example = "2025-01-01")
        private String date;

        /**
         * 上传数量
         */
        @Schema(description = "上传数量", example = "20")
        private Long uploadCount;

        /**
         * 上传大小（字节）
         */
        @Schema(description = "上传大小（字节）", example = "20971520")
        private Long uploadSize;
    }

    /**
     * 热门文件统计
     */
    @Data
    @Schema(description = "热门文件统计")
    public static class PopularFileStatistics implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 文件ID
         */
        @Schema(description = "文件ID", example = "1")
        private Long fileId;

        /**
         * 文件名称
         */
        @Schema(description = "文件名称", example = "receipt_001.jpg")
        private String fileName;

        /**
         * 访问次数
         */
        @Schema(description = "访问次数", example = "100")
        private Long accessCount;

        /**
         * 下载次数
         */
        @Schema(description = "下载次数", example = "50")
        private Long downloadCount;
    }

    /**
     * 大文件统计
     */
    @Data
    @Schema(description = "大文件统计")
    public static class LargeFileStatistics implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 文件ID
         */
        @Schema(description = "文件ID", example = "1")
        private Long fileId;

        /**
         * 文件名称
         */
        @Schema(description = "文件名称", example = "large_file.pdf")
        private String fileName;

        /**
         * 文件大小（字节）
         */
        @Schema(description = "文件大小（字节）", example = "104857600")
        private Long fileSize;

        /**
         * 文件大小（格式化）
         */
        @Schema(description = "文件大小（格式化）", example = "100.0 MB")
        private String fileSizeFormatted;
    }

}
