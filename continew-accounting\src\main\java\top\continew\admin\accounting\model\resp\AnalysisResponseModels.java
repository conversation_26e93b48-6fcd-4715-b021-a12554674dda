package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 分析响应模型集合
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public class AnalysisResponseModels {

    /**
     * 关联分析响应
     */
    @Data
    @Schema(description = "关联分析响应")
    public static class CorrelationAnalysisResp implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 分析ID
         */
        @Schema(description = "分析ID", example = "corr_123456")
        private String analysisId;

        /**
         * 关联规则
         */
        @Schema(description = "关联规则")
        private List<CorrelationRule> correlationRules;

        /**
         * 相关系数矩阵
         */
        @Schema(description = "相关系数矩阵")
        private Map<String, Map<String, Double>> correlationMatrix;

        /**
         * 生成时间
         */
        @Schema(description = "生成时间")
        private LocalDateTime generatedAt;

        @Data
        @Schema(description = "关联规则")
        public static class CorrelationRule implements Serializable {
            @Serial
            private static final long serialVersionUID = 1L;

            @Schema(description = "前件", example = "餐饮消费")
            private String antecedent;

            @Schema(description = "后件", example = "交通出行")
            private String consequent;

            @Schema(description = "支持度", example = "0.3")
            private Double support;

            @Schema(description = "置信度", example = "0.8")
            private Double confidence;

            @Schema(description = "提升度", example = "1.5")
            private Double lift;
        }
    }

    /**
     * 异常检测响应
     */
    @Data
    @Schema(description = "异常检测响应")
    public static class AnomalyDetectionResp implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 检测ID
         */
        @Schema(description = "检测ID", example = "anomaly_123456")
        private String detectionId;

        /**
         * 异常项列表
         */
        @Schema(description = "异常项列表")
        private List<AnomalyItem> anomalies;

        /**
         * 检测统计
         */
        @Schema(description = "检测统计")
        private DetectionStatistics statistics;

        /**
         * 生成时间
         */
        @Schema(description = "生成时间")
        private LocalDateTime generatedAt;

        @Data
        @Schema(description = "异常项")
        public static class AnomalyItem implements Serializable {
            @Serial
            private static final long serialVersionUID = 1L;

            @Schema(description = "异常ID", example = "anom_001")
            private String anomalyId;

            @Schema(description = "异常类型", example = "OUTLIER")
            private String anomalyType;

            @Schema(description = "异常值", example = "5000.00")
            private BigDecimal anomalyValue;

            @Schema(description = "异常分数", example = "0.95")
            private Double anomalyScore;

            @Schema(description = "描述", example = "金额异常偏高")
            private String description;

            @Schema(description = "相关数据")
            private Map<String, Object> relatedData;
        }

        @Data
        @Schema(description = "检测统计")
        public static class DetectionStatistics implements Serializable {
            @Serial
            private static final long serialVersionUID = 1L;

            @Schema(description = "总检测数", example = "1000")
            private Long totalDetected;

            @Schema(description = "异常数", example = "25")
            private Long anomalyCount;

            @Schema(description = "异常率", example = "0.025")
            private Double anomalyRate;

            @Schema(description = "检测方法", example = "IsolationForest")
            private String detectionMethod;
        }
    }

    /**
     * 预测分析响应
     */
    @Data
    @Schema(description = "预测分析响应")
    public static class PredictionAnalysisResp implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 预测ID
         */
        @Schema(description = "预测ID", example = "pred_123456")
        private String predictionId;

        /**
         * 预测结果
         */
        @Schema(description = "预测结果")
        private List<PredictionPoint> predictions;

        /**
         * 模型信息
         */
        @Schema(description = "模型信息")
        private ModelInfo modelInfo;

        /**
         * 生成时间
         */
        @Schema(description = "生成时间")
        private LocalDateTime generatedAt;

        @Data
        @Schema(description = "预测点")
        public static class PredictionPoint implements Serializable {
            @Serial
            private static final long serialVersionUID = 1L;

            @Schema(description = "时间点", example = "2025-02-01")
            private String timePoint;

            @Schema(description = "预测值", example = "3500.00")
            private BigDecimal predictedValue;

            @Schema(description = "置信区间下限", example = "3200.00")
            private BigDecimal lowerBound;

            @Schema(description = "置信区间上限", example = "3800.00")
            private BigDecimal upperBound;

            @Schema(description = "置信度", example = "0.95")
            private Double confidence;
        }

        @Data
        @Schema(description = "模型信息")
        public static class ModelInfo implements Serializable {
            @Serial
            private static final long serialVersionUID = 1L;

            @Schema(description = "模型类型", example = "ARIMA")
            private String modelType;

            @Schema(description = "模型精度", example = "0.85")
            private Double accuracy;

            @Schema(description = "训练数据量", example = "365")
            private Integer trainingDataSize;

            @Schema(description = "特征数量", example = "5")
            private Integer featureCount;
        }
    }

    /**
     * 聚合分析响应
     */
    @Data
    @Schema(description = "聚合分析响应")
    public static class AggregationAnalysisResp implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 聚合ID
         */
        @Schema(description = "聚合ID", example = "agg_123456")
        private String aggregationId;

        /**
         * 聚合结果
         */
        @Schema(description = "聚合结果")
        private List<AggregationResult> results;

        /**
         * 汇总统计
         */
        @Schema(description = "汇总统计")
        private Map<String, BigDecimal> summaryStats;

        /**
         * 生成时间
         */
        @Schema(description = "生成时间")
        private LocalDateTime generatedAt;

        @Data
        @Schema(description = "聚合结果")
        public static class AggregationResult implements Serializable {
            @Serial
            private static final long serialVersionUID = 1L;

            @Schema(description = "分组键")
            private Map<String, String> groupKey;

            @Schema(description = "聚合值", example = "2500.00")
            private BigDecimal aggregatedValue;

            @Schema(description = "记录数", example = "15")
            private Long recordCount;

            @Schema(description = "百分比", example = "25.5")
            private Double percentage;
        }
    }

    /**
     * 分析配置响应
     */
    @Data
    @Schema(description = "分析配置响应")
    public static class AnalysisConfigResp implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 配置ID
         */
        @Schema(description = "配置ID", example = "config_123456")
        private String configId;

        /**
         * 配置名称
         */
        @Schema(description = "配置名称", example = "月度收支分析")
        private String configName;

        /**
         * 配置描述
         */
        @Schema(description = "配置描述", example = "按月统计收支情况")
        private String description;

        /**
         * 配置内容
         */
        @Schema(description = "配置内容")
        private Map<String, Object> configContent;

        /**
         * 创建时间
         */
        @Schema(description = "创建时间")
        private LocalDateTime createdAt;

        /**
         * 更新时间
         */
        @Schema(description = "更新时间")
        private LocalDateTime updatedAt;

        /**
         * 是否公共配置
         */
        @Schema(description = "是否公共配置", example = "false")
        private Boolean isPublic;

        /**
         * 使用次数
         */
        @Schema(description = "使用次数", example = "25")
        private Integer usageCount;
    }

    /**
     * 其他简化的响应模型
     */
    @Data
    @Schema(description = "同期对比响应")
    public static class PeriodComparisonResp implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;
        
        @Schema(description = "对比ID")
        private String comparisonId;
        
        @Schema(description = "对比结果")
        private List<Map<String, Object>> comparisonResults;
        
        @Schema(description = "生成时间")
        private LocalDateTime generatedAt;
    }

    @Data
    @Schema(description = "漏斗分析响应")
    public static class FunnelAnalysisResp implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;
        
        @Schema(description = "漏斗ID")
        private String funnelId;
        
        @Schema(description = "漏斗步骤")
        private List<Map<String, Object>> funnelSteps;
        
        @Schema(description = "生成时间")
        private LocalDateTime generatedAt;
    }

    @Data
    @Schema(description = "队列分析响应")
    public static class CohortAnalysisResp implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;
        
        @Schema(description = "队列ID")
        private String cohortId;
        
        @Schema(description = "队列数据")
        private List<Map<String, Object>> cohortData;
        
        @Schema(description = "生成时间")
        private LocalDateTime generatedAt;
    }

    @Data
    @Schema(description = "实时分析响应")
    public static class RealTimeAnalysisResp implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;
        
        @Schema(description = "实时数据")
        private Map<String, Object> realTimeData;
        
        @Schema(description = "更新时间")
        private LocalDateTime lastUpdated;
    }

    @Data
    @Schema(description = "自定义分析响应")
    public static class CustomAnalysisResp implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;
        
        @Schema(description = "分析结果")
        private Map<String, Object> analysisResult;
        
        @Schema(description = "生成时间")
        private LocalDateTime generatedAt;
    }
}
