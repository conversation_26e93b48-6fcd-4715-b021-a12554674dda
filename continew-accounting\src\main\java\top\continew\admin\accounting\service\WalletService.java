package top.continew.admin.accounting.service;

import top.continew.admin.common.base.service.BaseService;
import top.continew.admin.accounting.model.entity.WalletDO;
import top.continew.admin.accounting.model.query.WalletQuery;
import top.continew.admin.accounting.model.req.WalletCreateReq;
import top.continew.admin.accounting.model.req.WalletUpdateReq;
import top.continew.admin.accounting.model.resp.WalletDetailResp;
import top.continew.admin.accounting.model.resp.WalletListResp;
import top.continew.admin.accounting.model.resp.WalletSummaryResp;
import top.continew.admin.accounting.model.resp.WalletHistoryResp;
import top.continew.admin.accounting.enums.TransactionType;
import top.continew.starter.data.service.IService;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 钱包管理业务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface WalletService extends BaseService<WalletListResp, WalletDetailResp, WalletQuery, WalletCreateReq>, IService<WalletDO> {

    /**
     * 更新钱包信息
     *
     * @param req 更新请求
     * @param id  钱包ID
     */
    void update(WalletUpdateReq req, Long id);

    /**
     * 获取群组钱包列表
     *
     * @param groupId 群组ID
     * @return 钱包列表
     */
    List<WalletListResp> getGroupWallets(Long groupId);

    /**
     * 获取或创建钱包
     *
     * @param groupId  群组ID
     * @param currency 币种
     * @return 钱包信息
     */
    WalletDO getOrCreateWallet(Long groupId, String currency);

    /**
     * 更新钱包余额
     *
     * @param groupId        群组ID
     * @param currency       币种
     * @param amount         金额
     * @param transactionType 交易类型
     */
    void updateBalance(Long groupId, String currency, BigDecimal amount, TransactionType transactionType);

    /**
     * 冻结金额
     *
     * @param groupId  群组ID
     * @param currency 币种
     * @param amount   冻结金额
     */
    void freezeAmount(Long groupId, String currency, BigDecimal amount);

    /**
     * 解冻金额
     *
     * @param groupId  群组ID
     * @param currency 币种
     * @param amount   解冻金额
     */
    void unfreezeAmount(Long groupId, String currency, BigDecimal amount);

    /**
     * 获取钱包余额
     *
     * @param groupId  群组ID
     * @param currency 币种
     * @return 余额
     */
    BigDecimal getBalance(Long groupId, String currency);

    /**
     * 获取可用余额（余额-冻结金额）
     *
     * @param groupId  群组ID
     * @param currency 币种
     * @return 可用余额
     */
    BigDecimal getAvailableBalance(Long groupId, String currency);

    /**
     * 检查余额是否足够
     *
     * @param groupId  群组ID
     * @param currency 币种
     * @param amount   需要的金额
     * @return 是否足够
     */
    boolean hasEnoughBalance(Long groupId, String currency, BigDecimal amount);

    /**
     * 获取群组钱包汇总信息
     *
     * @param groupId 群组ID
     * @return 汇总信息
     */
    WalletSummaryResp getGroupWalletSummary(Long groupId);

    /**
     * 转账
     *
     * @param fromGroupId 转出群组ID
     * @param toGroupId   转入群组ID
     * @param currency    币种
     * @param amount      转账金额
     * @param description 转账说明
     * @param operatorId  操作人ID
     */
    void transfer(Long fromGroupId, Long toGroupId, String currency, BigDecimal amount, String description, Long operatorId);

    /**
     * 重置钱包余额（管理员功能）
     *
     * @param groupId    群组ID
     * @param currency   币种
     * @param newBalance 新余额
     * @param operatorId 操作人ID
     * @param reason     重置原因
     */
    void resetBalance(Long groupId, String currency, BigDecimal newBalance, Long operatorId, String reason);

    /**
     * 获取钱包历史记录
     *
     * @param groupId  群组ID
     * @param currency 币种
     * @param limit    记录数量限制
     * @return 历史记录
     */
    List<WalletHistoryResp> getWalletHistory(Long groupId, String currency, Integer limit);

    /**
     * 获取钱包操作统计
     *
     * @param groupId   群组ID
     * @param currency  币种
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 操作统计
     */
    Map<String, Object> getWalletOperationStats(Long groupId, String currency,
                                               java.time.LocalDateTime startTime,
                                               java.time.LocalDateTime endTime);

    /**
     * 获取钱包余额变化趋势
     *
     * @param groupId   群组ID
     * @param currency  币种
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 余额变化趋势
     */
    List<Map<String, Object>> getWalletBalanceTrend(Long groupId, String currency,
                                                   java.time.LocalDateTime startTime,
                                                   java.time.LocalDateTime endTime);

    /**
     * 记录钱包操作历史
     *
     * @param groupId       群组ID
     * @param walletId      钱包ID
     * @param currency      币种
     * @param operationType 操作类型
     * @param amount        金额
     * @param balanceBefore 操作前余额
     * @param balanceAfter  操作后余额
     * @param description   描述
     * @param businessId    业务ID
     * @param businessType  业务类型
     */
    void recordWalletHistory(Long groupId, Long walletId, String currency, String operationType,
                           BigDecimal amount, BigDecimal balanceBefore, BigDecimal balanceAfter,
                           String description, Long businessId, String businessType);
}
