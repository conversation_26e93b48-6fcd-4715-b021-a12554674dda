package top.continew.admin.accounting.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.accounting.mapper.DebtMapper;
import top.continew.admin.accounting.model.entity.DebtDO;
import top.continew.admin.accounting.model.query.DebtQuery;
import top.continew.admin.accounting.model.req.DebtCreateReq;
import top.continew.admin.accounting.model.req.DebtPaymentReq;
import top.continew.admin.accounting.model.req.DebtUpdateReq;
import top.continew.admin.accounting.model.resp.DebtDetailResp;
import top.continew.admin.accounting.model.resp.DebtListResp;
import top.continew.admin.accounting.service.DebtService;
import top.continew.admin.accounting.service.GroupService;
import top.continew.admin.accounting.mapper.DebtMapper.DebtStatisticsResp;
import top.continew.starter.core.util.validate.CheckUtils;
import top.continew.starter.data.mybatis.base.BaseServiceImpl;
import top.continew.starter.security.util.SecurityContextHolder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * 债务管理业务实现
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DebtServiceImpl extends BaseServiceImpl<DebtMapper, DebtDO, DebtListResp, DebtDetailResp, DebtQuery, DebtCreateReq> implements DebtService {

    private final GroupService groupService;

    @Override
    public void beforeCreate(DebtCreateReq req) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查群组权限
        CheckUtils.throwIf(!groupService.isMember(req.getGroupId(), userId), "您不是该群组成员");
        
        // 检查债权人和债务人不能相同
        CheckUtils.throwIf(req.getCreditorId().equals(req.getDebtorId()), "债权人和债务人不能是同一人");
        
        // 检查债权人和债务人都是群组成员
        CheckUtils.throwIf(!groupService.isMember(req.getGroupId(), req.getCreditorId()), "债权人不是群组成员");
        CheckUtils.throwIf(!groupService.isMember(req.getGroupId(), req.getDebtorId()), "债务人不是群组成员");
        
        // 检查金额
        CheckUtils.throwIf(req.getAmount().compareTo(BigDecimal.ZERO) <= 0, "金额必须大于0");
        
        // 检查到期时间
        if (req.getDueDate() != null) {
            CheckUtils.throwIf(req.getDueDate().isBefore(LocalDateTime.now()), "到期时间不能早于当前时间");
        }
        
        // 检查利率
        if (req.getInterestRate() != null) {
            CheckUtils.throwIf(req.getInterestRate().compareTo(BigDecimal.ZERO) < 0, "利率不能为负数");
            CheckUtils.throwIf(req.getInterestRate().compareTo(BigDecimal.ONE) > 0, "利率不能超过100%");
        }
    }

    @Override
    public void afterCreate(DebtDO entity, DebtCreateReq req) {
        // 初始化债务金额
        entity.setPaidAmount(BigDecimal.ZERO);
        entity.setRemainingAmount(req.getAmount());
        entity.setStatus("ACTIVE");
        
        // 如果需要自动创建还款计划
        if (Boolean.TRUE.equals(req.getAutoCreatePaymentPlan()) && req.getPaymentPeriods() != null) {
            this.generatePaymentPlan(entity.getId(), req.getPaymentPeriods(), req.getPaymentFrequency());
        }
        
        log.info("创建债务成功，ID: {}, 债权人: {}, 债务人: {}, 金额: {}", 
                entity.getId(), req.getCreditorId(), req.getDebtorId(), req.getAmount());
    }

    @Override
    public void update(DebtUpdateReq req, Long id) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查操作权限
        CheckUtils.throwIf(!baseMapper.hasPermission(id, userId), "您没有权限修改此债务");
        
        DebtDO debt = this.getById(id);
        CheckUtils.throwIfNull(debt, "债务不存在");
        
        // 检查债务状态
        CheckUtils.throwIf("PAID".equals(debt.getStatus()), "已结清的债务不能修改");
        CheckUtils.throwIf("CANCELLED".equals(debt.getStatus()), "已取消的债务不能修改");
        
        // 更新债务信息
        if (req.getAmount() != null) {
            CheckUtils.throwIf(req.getAmount().compareTo(BigDecimal.ZERO) <= 0, "金额必须大于0");
            debt.setAmount(req.getAmount());
            // 重新计算剩余金额
            debt.setRemainingAmount(req.getAmount().subtract(debt.getPaidAmount()));
        }
        
        if (StrUtil.isNotBlank(req.getDescription())) {
            debt.setDescription(req.getDescription());
        }
        
        if (req.getDueDate() != null) {
            CheckUtils.throwIf(req.getDueDate().isBefore(LocalDateTime.now()), "到期时间不能早于当前时间");
            debt.setDueDate(req.getDueDate());
        }
        
        if (req.getInterestRate() != null) {
            CheckUtils.throwIf(req.getInterestRate().compareTo(BigDecimal.ZERO) < 0, "利率不能为负数");
            CheckUtils.throwIf(req.getInterestRate().compareTo(BigDecimal.ONE) > 0, "利率不能超过100%");
            debt.setInterestRate(req.getInterestRate());
        }
        
        if (StrUtil.isNotBlank(req.getRemark())) {
            debt.setRemark(req.getRemark());
        }
        
        this.updateById(debt);
        log.info("更新债务成功，ID: {}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long makePayment(DebtPaymentReq req) {
        Long userId = SecurityContextHolder.getUserId();
        
        DebtDO debt = this.getById(req.getDebtId());
        CheckUtils.throwIfNull(debt, "债务不存在");
        
        // 检查权限（债务人或债权人可以还款）
        CheckUtils.throwIf(!debt.getDebtorId().equals(userId) && !debt.getCreditorId().equals(userId), 
                "您没有权限操作此债务");
        
        // 检查债务状态
        CheckUtils.throwIf(!"ACTIVE".equals(debt.getStatus()), "只有进行中的债务才能还款");
        
        // 检查还款金额
        CheckUtils.throwIf(req.getAmount().compareTo(BigDecimal.ZERO) <= 0, "还款金额必须大于0");
        CheckUtils.throwIf(req.getAmount().compareTo(debt.getRemainingAmount()) > 0, "还款金额不能超过剩余金额");
        
        // 创建还款记录
        // TODO: 实现还款记录创建逻辑
        
        // 更新债务金额
        BigDecimal newPaidAmount = debt.getPaidAmount().add(req.getAmount());
        BigDecimal newRemainingAmount = debt.getAmount().subtract(newPaidAmount);
        
        baseMapper.updateDebtAmount(debt.getId(), newPaidAmount, newRemainingAmount, LocalDateTime.now());
        
        // 如果全部还清，更新状态
        if (newRemainingAmount.compareTo(BigDecimal.ZERO) == 0) {
            baseMapper.updateDebtStatus(debt.getId(), "PAID");
        }
        
        log.info("还款成功，债务ID: {}, 还款金额: {}, 剩余金额: {}", debt.getId(), req.getAmount(), newRemainingAmount);
        return 1L; // TODO: 返回实际的还款记录ID
    }

    @Override
    public void confirmPayment(Long paymentId) {
        // TODO: 实现确认还款逻辑
        log.info("确认还款成功，还款记录ID: {}", paymentId);
    }

    @Override
    public void cancelPayment(Long paymentId) {
        // TODO: 实现取消还款逻辑
        log.info("取消还款成功，还款记录ID: {}", paymentId);
    }

    @Override
    public List<DebtListResp> getUserDebts(Long userId, Long groupId, String status) {
        return baseMapper.selectByUserId(userId, groupId, status);
    }

    @Override
    public DebtStatisticsResp getGroupStatistics(Long groupId, String status) {
        return baseMapper.selectGroupStatistics(groupId, status);
    }

    @Override
    public DebtStatisticsResp getUserStatistics(Long userId, Long groupId, String status) {
        return baseMapper.selectUserStatistics(userId, groupId, status);
    }

    @Override
    public List<DebtListResp> getDueSoonDebts(Long groupId, Integer days) {
        return baseMapper.selectDueSoon(groupId, days);
    }

    @Override
    public List<DebtListResp> getOverdueDebts(Long groupId) {
        return baseMapper.selectOverdue(groupId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void settleDebt(Long id) {
        Long userId = SecurityContextHolder.getUserId();
        
        DebtDO debt = this.getById(id);
        CheckUtils.throwIfNull(debt, "债务不存在");
        
        // 检查权限
        CheckUtils.throwIf(!baseMapper.hasPermission(id, userId), "您没有权限操作此债务");
        
        // 检查状态
        CheckUtils.throwIf(!"ACTIVE".equals(debt.getStatus()), "只有进行中的债务才能结清");
        
        // 更新状态和金额
        baseMapper.updateDebtAmount(id, debt.getAmount(), BigDecimal.ZERO, LocalDateTime.now());
        baseMapper.updateDebtStatus(id, "PAID");
        
        log.info("结清债务成功，ID: {}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelDebt(Long id) {
        Long userId = SecurityContextHolder.getUserId();
        
        DebtDO debt = this.getById(id);
        CheckUtils.throwIfNull(debt, "债务不存在");
        
        // 检查权限
        CheckUtils.throwIf(!baseMapper.hasPermission(id, userId), "您没有权限操作此债务");
        
        // 检查状态
        CheckUtils.throwIf("PAID".equals(debt.getStatus()), "已结清的债务不能取消");
        
        // 更新状态
        baseMapper.updateDebtStatus(id, "CANCELLED");
        
        log.info("取消债务成功，ID: {}", id);
    }

    @Override
    public void mergeDebts(List<Long> sourceIds, Long targetId) {
        // TODO: 实现债务合并逻辑
        log.info("合并债务成功，源债务: {}, 目标债务: {}", sourceIds, targetId);
    }

    @Override
    public Long splitDebt(Long id, BigDecimal amount) {
        // TODO: 实现债务分割逻辑
        log.info("分割债务成功，原债务ID: {}, 分割金额: {}", id, amount);
        return 1L; // TODO: 返回新债务ID
    }

    @Override
    public void transferDebt(Long id, Long newCreditorId, Long newDebtorId) {
        // TODO: 实现债务转移逻辑
        log.info("转移债务成功，债务ID: {}, 新债权人: {}, 新债务人: {}", id, newCreditorId, newDebtorId);
    }

    @Override
    public BigDecimal calculateInterest(Long id) {
        DebtDO debt = this.getById(id);
        CheckUtils.throwIfNull(debt, "债务不存在");
        
        if (debt.getInterestRate() == null || debt.getInterestRate().compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        
        // 计算天数
        long days = ChronoUnit.DAYS.between(debt.getCreateTime().toLocalDate(), LocalDateTime.now().toLocalDate());
        
        // 计算利息：本金 * 年利率 * 天数 / 365
        return debt.getRemainingAmount()
                .multiply(debt.getInterestRate())
                .multiply(BigDecimal.valueOf(days))
                .divide(BigDecimal.valueOf(365), 2, RoundingMode.HALF_UP);
    }

    @Override
    public void generatePaymentPlan(Long id, Integer periods, String paymentFrequency) {
        // TODO: 实现还款计划生成逻辑
        log.info("生成还款计划成功，债务ID: {}, 期数: {}, 频率: {}", id, periods, paymentFrequency);
    }

    @Override
    public void sendPaymentReminder(Long groupId, Integer days) {
        // TODO: 实现还款提醒逻辑
        log.info("发送还款提醒成功，群组ID: {}, 提前天数: {}", groupId, days);
    }

    @Override
    public void autoSettleDebts(Long groupId) {
        // TODO: 实现自动结算逻辑
        log.info("自动结算债务成功，群组ID: {}", groupId);
    }

    @Override
    public Object getDebtRelationshipGraph(Long groupId) {
        // TODO: 实现债务关系图逻辑
        log.info("获取债务关系图成功，群组ID: {}", groupId);
        return null;
    }
}
