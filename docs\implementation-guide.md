# 群组记账机器人系统实施指南

## 🚀 快速开始

### 前置条件
- Java 17+
- Maven 3.8+
- MySQL 8.0+
- Redis 7.0+
- Node.js 18+ (前端开发)

### 环境搭建

#### 1. 克隆项目
```bash
git clone https://github.com/your-org/continew-admin.git
cd continew-admin
```

#### 2. 数据库初始化
```sql
-- 创建数据库
CREATE DATABASE continew_admin CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 执行初始化脚本
source docs/database-design.sql
```

#### 3. 配置文件修改
```yaml
# continew-server/src/main/resources/config/application-dev.yml
spring:
  datasource:
    url: ******************************************
    username: your_username
    password: your_password
  data:
    redis:
      host: localhost
      port: 6379
      password: your_redis_password
```

#### 4. 启动项目
```bash
# 后端启动
mvn clean install
mvn spring-boot:run -pl continew-server

# 前端启动 (如果有前端项目)
cd continew-web
npm install
npm run dev
```

## 📝 开发规范

### 代码结构规范

#### 1. 包结构
```
top.continew.admin.accounting/
├── controller/          # 控制器层
├── service/            # 服务层
│   └── impl/          # 服务实现
├── mapper/             # 数据访问层
├── model/              # 数据模型
│   ├── entity/        # 实体类
│   ├── dto/           # 数据传输对象
│   ├── req/           # 请求对象
│   ├── resp/          # 响应对象
│   └── vo/            # 视图对象
├── enums/              # 枚举类
├── config/             # 配置类
└── util/               # 工具类
```

#### 2. 命名规范
- **实体类**: 以`DO`结尾，如`GroupDO`
- **请求对象**: 以`Req`结尾，如`GroupCreateReq`
- **响应对象**: 以`Resp`结尾，如`GroupDetailResp`
- **服务接口**: 以`Service`结尾，如`GroupService`
- **服务实现**: 以`ServiceImpl`结尾，如`GroupServiceImpl`
- **控制器**: 以`Controller`结尾，如`GroupController`
- **数据访问**: 以`Mapper`结尾，如`GroupMapper`

#### 3. 注解使用
```java
// 控制器
@Tag(name = "群组管理 API")
@RestController
@RequestMapping("/api/v1/groups")
@RequiredArgsConstructor
public class GroupController {
    
    @Operation(summary = "创建群组")
    @PostMapping
    public R<Long> createGroup(@Valid @RequestBody GroupCreateReq req) {
        // 实现
    }
}

// 服务层
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class GroupServiceImpl implements GroupService {
    // 实现
}

// 实体类
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("acc_group")
@Schema(description = "群组信息")
public class GroupDO extends BaseEntity {
    // 字段定义
}
```

### 数据库规范

#### 1. 表命名
- 记账相关表以`acc_`开头
- 机器人相关表以`bot_`开头
- 系统表以`sys_`开头

#### 2. 字段规范
- 主键统一使用`id`，类型为`bigint(20)`
- 创建时间使用`create_time`，类型为`datetime`
- 更新时间使用`update_time`，类型为`datetime`
- 创建人使用`create_user`，类型为`bigint(20)`
- 更新人使用`update_user`，类型为`bigint(20)`
- 状态字段使用`status`，类型为`tinyint(1)`

#### 3. 索引规范
- 主键自动创建聚簇索引
- 外键字段创建普通索引
- 查询频繁的字段组合创建复合索引
- 唯一约束字段创建唯一索引

### API设计规范

#### 1. RESTful API
```
GET    /api/v1/groups           # 获取群组列表
POST   /api/v1/groups           # 创建群组
GET    /api/v1/groups/{id}      # 获取群组详情
PUT    /api/v1/groups/{id}      # 更新群组
DELETE /api/v1/groups/{id}      # 删除群组
```

#### 2. 响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "测试群组"
  },
  "timestamp": "2025-01-01T12:00:00Z"
}
```

#### 3. 错误处理
```java
// 业务异常
throw new BusinessException("群组不存在");

// 参数验证异常
@Valid @RequestBody GroupCreateReq req
```

## 🔧 核心功能实现

### 1. 群组管理

#### 创建群组
```java
@Override
@Transactional(rollbackFor = Exception.class)
public Long createGroup(GroupCreateReq createReq) {
    // 1. 参数验证
    validateCreateRequest(createReq);
    
    // 2. 检查群组是否已存在
    GroupDO existingGroup = findByPlatformInfo(
        createReq.getPlatform(), 
        createReq.getPlatformGroupId()
    );
    if (existingGroup != null) {
        throw new BusinessException("群组已存在");
    }
    
    // 3. 创建群组
    GroupDO group = new GroupDO();
    BeanUtils.copyProperties(createReq, group);
    group.setSubscriptionPlan(SubscriptionPlan.TRIAL);
    group.setStatus(1);
    
    // 4. 保存群组
    save(group);
    
    // 5. 创建群主成员记录
    createOwnerMember(group.getId(), createReq.getOwnerId());
    
    return group.getId();
}
```

#### 权限验证
```java
@Override
public boolean hasFeature(Long groupId, String feature) {
    GroupDO group = getById(groupId);
    if (group == null) {
        return false;
    }
    return group.getSubscriptionPlan().supportsFeature(feature);
}
```

### 2. 账单管理

#### 创建账单
```java
@Override
@Transactional(rollbackFor = Exception.class)
public Long createTransaction(TransactionCreateReq createReq) {
    // 1. 权限验证
    validatePermission(createReq.getGroupId(), createReq.getUserId());
    
    // 2. 检查交易次数限制
    if (groupService.isTransactionLimitExceeded(createReq.getGroupId())) {
        throw new BusinessException("已达到当月交易次数限制");
    }
    
    // 3. 创建账单
    TransactionDO transaction = new TransactionDO();
    BeanUtils.copyProperties(createReq, transaction);
    transaction.setTransactionDate(LocalDateTime.now());
    transaction.setStatus(1);
    
    // 4. 保存账单
    save(transaction);
    
    // 5. 更新钱包余额
    walletService.updateBalance(
        createReq.getGroupId(),
        createReq.getCurrency(),
        createReq.getAmount(),
        createReq.getType()
    );
    
    // 6. 记录操作历史
    recordHistory(transaction.getId(), "CREATE", null, transaction);
    
    return transaction.getId();
}
```

### 3. 机器人集成

#### 命令解析
```java
@Component
public class CommandParser {
    
    private static final Pattern AMOUNT_PATTERN = 
        Pattern.compile("([+-]?)([0-9]+(?:\\.[0-9]+)?)(?:\\*([0-9]+(?:\\.[0-9]+)?))?");
    
    public ParsedCommand parseCommand(String text) {
        ParsedCommand command = new ParsedCommand();
        
        // 解析金额表达式
        Matcher matcher = AMOUNT_PATTERN.matcher(text);
        if (matcher.find()) {
            String sign = matcher.group(1);
            BigDecimal amount = new BigDecimal(matcher.group(2));
            String multiplier = matcher.group(3);
            
            if (multiplier != null) {
                amount = amount.multiply(new BigDecimal(multiplier));
            }
            
            command.setAmount(amount);
            command.setType(TransactionType.fromSymbol(sign));
        }
        
        // 解析标签
        command.setTags(extractTags(text));
        
        // 解析描述
        command.setDescription(extractDescription(text));
        
        return command;
    }
}
```

#### Telegram Bot处理
```java
@Component
@RequiredArgsConstructor
public class TelegramBotService {
    
    private final CommandParser commandParser;
    private final TransactionService transactionService;
    
    @EventListener
    public void handleMessage(TelegramMessageEvent event) {
        try {
            // 1. 解析命令
            ParsedCommand command = commandParser.parseCommand(event.getText());
            
            // 2. 验证群组
            GroupDO group = groupService.findByPlatformInfo(
                PlatformType.TELEGRAM, 
                event.getChatId()
            );
            
            if (group == null) {
                sendMessage(event.getChatId(), "群组未注册，请先注册群组");
                return;
            }
            
            // 3. 创建账单
            TransactionCreateReq createReq = buildCreateRequest(command, group, event);
            Long transactionId = transactionService.createTransaction(createReq);
            
            // 4. 发送确认消息
            sendConfirmation(event.getChatId(), transactionId);
            
        } catch (Exception e) {
            log.error("处理Telegram消息失败", e);
            sendMessage(event.getChatId(), "处理失败：" + e.getMessage());
        }
    }
}
```

## 🧪 测试策略

### 1. 单元测试
```java
@ExtendWith(MockitoExtension.class)
class GroupServiceTest {
    
    @Mock
    private GroupMapper groupMapper;
    
    @InjectMocks
    private GroupServiceImpl groupService;
    
    @Test
    void testCreateGroup() {
        // Given
        GroupCreateReq createReq = new GroupCreateReq();
        createReq.setName("测试群组");
        
        // When
        Long groupId = groupService.createGroup(createReq);
        
        // Then
        assertThat(groupId).isNotNull();
        verify(groupMapper).insert(any(GroupDO.class));
    }
}
```

### 2. 集成测试
```java
@SpringBootTest
@Transactional
class GroupControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void testCreateGroupApi() {
        // Given
        GroupCreateReq request = new GroupCreateReq();
        request.setName("测试群组");
        
        // When
        ResponseEntity<R<Long>> response = restTemplate.postForEntity(
            "/api/v1/groups", 
            request, 
            new ParameterizedTypeReference<R<Long>>() {}
        );
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getData()).isNotNull();
    }
}
```

## 📊 监控与运维

### 1. 应用监控
```yaml
# application.yml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
```

### 2. 日志配置
```xml
<!-- logback-spring.xml -->
<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <logger name="top.continew.admin" level="DEBUG"/>
    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>
```

### 3. 性能监控
```java
@Component
@RequiredArgsConstructor
public class PerformanceMonitor {
    
    private final MeterRegistry meterRegistry;
    
    @EventListener
    public void onTransactionCreated(TransactionCreatedEvent event) {
        // 记录交易创建指标
        meterRegistry.counter("transaction.created", 
            "group", event.getGroupId().toString(),
            "type", event.getType().getValue()
        ).increment();
    }
}
```

## 🚀 部署指南

### 1. Docker部署
```dockerfile
# Dockerfile
FROM openjdk:17-jre-slim

COPY target/continew-admin.jar app.jar

EXPOSE 8000

ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 2. Docker Compose
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=mysql
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
      
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: continew_admin
    volumes:
      - mysql_data:/var/lib/mysql
      
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

### 3. 生产环境配置
```yaml
# application-prod.yml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      connection-timeout: 30000
  data:
    redis:
      timeout: 10s
      lettuce:
        pool:
          max-active: 20

logging:
  level:
    top.continew.admin: INFO
  file:
    name: logs/continew-admin.log
```

这个实施指南提供了完整的开发、测试和部署流程，确保项目能够按照既定的架构和规范顺利实施。
