package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.data.mybatis.plus.base.BaseEntity;

import java.time.LocalDateTime;

/**
 * Zapier配置实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("acc_zapier_config")
@Schema(description = "Zapier配置")
public class ZapierConfigDO extends BaseEntity {

    /**
     * 群组ID
     */
    @Schema(description = "群组ID")
    private Long groupId;

    /**
     * 配置名称
     */
    @Schema(description = "配置名称")
    private String name;

    /**
     * 配置描述
     */
    @Schema(description = "配置描述")
    private String description;

    /**
     * Webhook URL
     */
    @Schema(description = "Webhook URL")
    private String webhookUrl;

    /**
     * 密钥
     */
    @Schema(description = "密钥")
    private String secretKey;

    /**
     * 触发器类型
     */
    @Schema(description = "触发器类型")
    private String triggerType;

    /**
     * 触发条件
     */
    @Schema(description = "触发条件")
    private String triggerConditions;

    /**
     * 数据映射配置
     */
    @Schema(description = "数据映射配置")
    private String dataMapping;

    /**
     * 过滤规则
     */
    @Schema(description = "过滤规则")
    private String filterRules;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    private Boolean enabled;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;

    /**
     * 最后触发时间
     */
    @Schema(description = "最后触发时间")
    private LocalDateTime lastTriggeredAt;

    /**
     * 触发次数
     */
    @Schema(description = "触发次数")
    private Long triggerCount;

    /**
     * 成功次数
     */
    @Schema(description = "成功次数")
    private Long successCount;

    /**
     * 失败次数
     */
    @Schema(description = "失败次数")
    private Long failureCount;

    /**
     * 最后错误信息
     */
    @Schema(description = "最后错误信息")
    private String lastError;

    /**
     * 最后错误时间
     */
    @Schema(description = "最后错误时间")
    private LocalDateTime lastErrorAt;

    /**
     * 重试次数
     */
    @Schema(description = "重试次数")
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    @Schema(description = "最大重试次数")
    private Integer maxRetries;

    /**
     * 超时时间（秒）
     */
    @Schema(description = "超时时间（秒）")
    private Integer timeoutSeconds;

    /**
     * 请求头配置
     */
    @Schema(description = "请求头配置")
    private String headers;

    /**
     * 认证配置
     */
    @Schema(description = "认证配置")
    private String authConfig;

    /**
     * 标签
     */
    @Schema(description = "标签")
    private String tags;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}
