package top.continew.admin.accounting.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

/**
 * 文件访问权限类型枚举
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Getter
@RequiredArgsConstructor
public enum FileAccessTypeEnum implements BaseEnum<String> {

    /**
     * 公开访问
     */
    PUBLIC("PUBLIC", "公开访问"),

    /**
     * 私有访问
     */
    PRIVATE("PRIVATE", "私有访问"),

    /**
     * 群组访问
     */
    GROUP("GROUP", "群组访问"),

    /**
     * 用户访问
     */
    USER("USER", "用户访问"),

    /**
     * 临时访问
     */
    TEMPORARY("TEMPORARY", "临时访问");

    private final String value;
    private final String description;

    /**
     * 是否需要权限验证
     */
    public boolean requiresAuth() {
        return !PUBLIC.equals(this);
    }

    /**
     * 是否支持临时链接
     */
    public boolean supportsTemporaryUrl() {
        return PRIVATE.equals(this) || GROUP.equals(this) || USER.equals(this) || TEMPORARY.equals(this);
    }

}
