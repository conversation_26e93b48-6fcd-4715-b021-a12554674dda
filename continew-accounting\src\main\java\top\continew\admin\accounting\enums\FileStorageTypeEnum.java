package top.continew.admin.accounting.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

/**
 * 文件存储类型枚举
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Getter
@RequiredArgsConstructor
public enum FileStorageTypeEnum implements BaseEnum<String> {

    /**
     * 本地存储
     */
    LOCAL("LOCAL", "本地存储"),

    /**
     * 阿里云OSS
     */
    ALIYUN_OSS("ALIYUN_OSS", "阿里云OSS"),

    /**
     * 腾讯云COS
     */
    TENCENT_COS("TENCENT_COS", "腾讯云COS"),

    /**
     * AWS S3
     */
    AWS_S3("AWS_S3", "AWS S3"),

    /**
     * MinIO
     */
    MINIO("MINIO", "MinIO"),

    /**
     * 华为云OBS
     */
    HUAWEI_OBS("HUAWEI_OBS", "华为云OBS"),

    /**
     * 七牛云
     */
    QINIU("QINIU", "七牛云"),

    /**
     * 又拍云
     */
    UPYUN("UPYUN", "又拍云");

    private final String value;
    private final String description;

    /**
     * 是否为云存储
     */
    public boolean isCloudStorage() {
        return !LOCAL.equals(this);
    }

    /**
     * 是否支持CDN
     */
    public boolean supportsCdn() {
        return isCloudStorage();
    }

    /**
     * 是否支持图片处理
     */
    public boolean supportsImageProcessing() {
        return ALIYUN_OSS.equals(this) || TENCENT_COS.equals(this) || QINIU.equals(this);
    }

    /**
     * 是否支持视频处理
     */
    public boolean supportsVideoProcessing() {
        return ALIYUN_OSS.equals(this) || TENCENT_COS.equals(this);
    }

    /**
     * 获取默认域名后缀
     */
    public String getDefaultDomainSuffix() {
        return switch (this) {
            case ALIYUN_OSS -> ".oss-cn-hangzhou.aliyuncs.com";
            case TENCENT_COS -> ".cos.ap-guangzhou.myqcloud.com";
            case AWS_S3 -> ".s3.amazonaws.com";
            case HUAWEI_OBS -> ".obs.cn-north-4.myhuaweicloud.com";
            case QINIU -> ".qiniudn.com";
            case UPYUN -> ".b0.upaiyun.com";
            default -> "";
        };
    }

}
