package top.continew.admin.accounting.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.accounting.enums.PlatformType;
import top.continew.admin.accounting.enums.SubscriptionPlan;
import top.continew.starter.extension.crud.model.query.SortQuery;

import java.io.Serial;
import java.io.Serializable;

/**
 * 群组查询条件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "群组查询条件")
public class GroupQuery extends SortQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组名称
     */
    @Schema(description = "群组名称", example = "我的记账群")
    private String name;

    /**
     * 平台类型
     */
    @Schema(description = "平台类型", example = "TELEGRAM")
    private PlatformType platform;

    /**
     * 平台群组ID
     */
    @Schema(description = "平台群组ID", example = "-*************")
    private String platformGroupId;

    /**
     * 订阅计划
     */
    @Schema(description = "订阅计划", example = "PRO")
    private SubscriptionPlan subscriptionPlan;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "1")
    private Integer status;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID", example = "1")
    private Long createUser;
}
