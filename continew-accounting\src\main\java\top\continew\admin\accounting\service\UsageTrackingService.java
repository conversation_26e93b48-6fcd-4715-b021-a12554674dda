package top.continew.admin.accounting.service;

/**
 * 使用量跟踪服务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface UsageTrackingService {

    /**
     * 记录交易创建
     *
     * @param groupId 群组ID
     */
    void recordTransactionCreated(Long groupId);

    /**
     * 记录OCR使用
     *
     * @param groupId 群组ID
     * @param count   使用次数
     */
    void recordOcrUsage(Long groupId, int count);

    /**
     * 记录API调用
     *
     * @param groupId 群组ID
     * @param count   调用次数
     */
    void recordApiCall(Long groupId, int count);

    /**
     * 记录存储使用
     *
     * @param groupId   群组ID
     * @param sizeBytes 存储大小（字节）
     */
    void recordStorageUsage(Long groupId, long sizeBytes);

    /**
     * 记录导出操作
     *
     * @param groupId 群组ID
     */
    void recordExport(Long groupId);

    /**
     * 记录Webhook调用
     *
     * @param groupId 群组ID
     */
    void recordWebhookCall(Long groupId);

    /**
     * 记录活跃用户
     *
     * @param groupId 群组ID
     * @param userId  用户ID
     */
    void recordActiveUser(Long groupId, Long userId);

    /**
     * 检查是否超出限制
     *
     * @param groupId   群组ID
     * @param limitType 限制类型
     * @return 是否超出限制
     */
    boolean isLimitExceeded(Long groupId, String limitType);

    /**
     * 获取当前使用量
     *
     * @param groupId   群组ID
     * @param limitType 限制类型
     * @return 当前使用量
     */
    int getCurrentUsage(Long groupId, String limitType);

    /**
     * 获取限制值
     *
     * @param groupId   群组ID
     * @param limitType 限制类型
     * @return 限制值（-1表示无限制）
     */
    int getLimit(Long groupId, String limitType);
}
