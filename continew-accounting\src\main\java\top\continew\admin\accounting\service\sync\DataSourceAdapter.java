package top.continew.admin.accounting.service.sync;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 数据源适配器接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface DataSourceAdapter {

    /**
     * 获取适配器类型
     *
     * @return 适配器类型
     */
    String getAdapterType();

    /**
     * 测试连接
     *
     * @param config 数据源配置
     * @return 测试结果
     */
    Map<String, Object> testConnection(Map<String, Object> config);

    /**
     * 验证配置
     *
     * @param config 数据源配置
     * @return 验证结果
     */
    Map<String, Object> validateConfig(Map<String, Object> config);

    /**
     * 获取数据结构信息
     *
     * @param config 数据源配置
     * @return 数据结构信息
     */
    Map<String, Object> getDataStructure(Map<String, Object> config);

    /**
     * 读取数据
     *
     * @param config        数据源配置
     * @param fieldMapping  字段映射
     * @param filterCondition 过滤条件
     * @param lastSyncTime  最后同步时间（增量同步）
     * @param batchSize     批量大小
     * @return 数据列表
     */
    List<Map<String, Object>> readData(Map<String, Object> config,
                                       Map<String, Object> fieldMapping,
                                       Map<String, Object> filterCondition,
                                       LocalDateTime lastSyncTime,
                                       Integer batchSize);

    /**
     * 写入数据
     *
     * @param config       数据源配置
     * @param fieldMapping 字段映射
     * @param data         数据列表
     * @param operationType 操作类型（CREATE/UPDATE/DELETE）
     * @return 写入结果
     */
    Map<String, Object> writeData(Map<String, Object> config,
                                  Map<String, Object> fieldMapping,
                                  List<Map<String, Object>> data,
                                  String operationType);

    /**
     * 检测数据变更
     *
     * @param config       数据源配置
     * @param lastSyncTime 最后同步时间
     * @return 变更信息
     */
    Map<String, Object> detectChanges(Map<String, Object> config, LocalDateTime lastSyncTime);

    /**
     * 获取数据总数
     *
     * @param config          数据源配置
     * @param filterCondition 过滤条件
     * @return 数据总数
     */
    Long getDataCount(Map<String, Object> config, Map<String, Object> filterCondition);

    /**
     * 获取字段建议映射
     *
     * @param sourceConfig 源配置
     * @param targetConfig 目标配置
     * @return 字段映射建议
     */
    Map<String, Object> suggestFieldMapping(Map<String, Object> sourceConfig, Map<String, Object> targetConfig);

    /**
     * 执行自定义查询
     *
     * @param config 数据源配置
     * @param query  查询语句
     * @param params 查询参数
     * @return 查询结果
     */
    List<Map<String, Object>> executeCustomQuery(Map<String, Object> config, String query, Map<String, Object> params);

    /**
     * 获取同步进度
     *
     * @param config  数据源配置
     * @param syncId  同步ID
     * @return 同步进度
     */
    Map<String, Object> getSyncProgress(Map<String, Object> config, String syncId);

    /**
     * 清理临时数据
     *
     * @param config 数据源配置
     * @param syncId 同步ID
     */
    void cleanup(Map<String, Object> config, String syncId);
}
