package top.continew.admin.accounting.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 预算列表响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "预算列表响应")
public class BudgetListResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 预算ID
     */
    @Schema(description = "预算ID", example = "budget_123456")
    private String budgetId;

    /**
     * 预算名称
     */
    @Schema(description = "预算名称", example = "2025年度预算")
    private String budgetName;

    /**
     * 预算描述
     */
    @Schema(description = "预算描述", example = "2025年度收支预算计划")
    private String description;

    /**
     * 预算类型
     */
    @Schema(description = "预算类型", example = "ANNUAL")
    private String budgetType;

    /**
     * 预算类型名称
     */
    @Schema(description = "预算类型名称", example = "年度预算")
    private String budgetTypeName;

    /**
     * 预算周期
     */
    @Schema(description = "预算周期", example = "YEARLY")
    private String budgetPeriod;

    /**
     * 预算周期名称
     */
    @Schema(description = "预算周期名称", example = "年度")
    private String budgetPeriodName;

    /**
     * 开始日期
     */
    @Schema(description = "开始日期", example = "2025-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @Schema(description = "结束日期", example = "2025-12-31")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    /**
     * 总预算金额
     */
    @Schema(description = "总预算金额", example = "100000.00")
    private BigDecimal totalAmount;

    /**
     * 已使用金额
     */
    @Schema(description = "已使用金额", example = "35000.00")
    private BigDecimal usedAmount;

    /**
     * 剩余金额
     */
    @Schema(description = "剩余金额", example = "65000.00")
    private BigDecimal remainingAmount;

    /**
     * 使用率
     */
    @Schema(description = "使用率", example = "0.35")
    private BigDecimal usageRate;

    /**
     * 币种
     */
    @Schema(description = "币种", example = "CNY")
    private String currency;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "ACTIVE")
    private String status;

    /**
     * 状态名称
     */
    @Schema(description = "状态名称", example = "生效中")
    private String statusName;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    /**
     * 分配数量
     */
    @Schema(description = "分配数量", example = "5")
    private Integer allocationCount;

    /**
     * 预警数量
     */
    @Schema(description = "预警数量", example = "2")
    private Integer alertCount;

    /**
     * 是否有预警
     */
    @Schema(description = "是否有预警", example = "true")
    private Boolean hasAlert;

    /**
     * 最高预警级别
     */
    @Schema(description = "最高预警级别", example = "WARNING")
    private String highestAlertLevel;

    /**
     * 最高预警级别名称
     */
    @Schema(description = "最高预警级别名称", example = "警告")
    private String highestAlertLevelName;

    /**
     * 是否需要审批
     */
    @Schema(description = "是否需要审批", example = "true")
    private Boolean requiresApproval;

    /**
     * 待审批数量
     */
    @Schema(description = "待审批数量", example = "3")
    private Integer pendingApprovalCount;

    /**
     * 标签列表
     */
    @Schema(description = "标签列表", example = "[\"重要\", \"年度\"]")
    private List<String> tags;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID", example = "1")
    private Long createdBy;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名", example = "张三")
    private String createdByName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-01-01 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2025-01-01 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 剩余天数
     */
    @Schema(description = "剩余天数", example = "300")
    private Long remainingDays;

    /**
     * 进度百分比
     */
    @Schema(description = "进度百分比", example = "35.5")
    private BigDecimal progressPercentage;

    /**
     * 趋势
     */
    @Schema(description = "趋势", example = "INCREASING")
    private String trend;

    /**
     * 趋势名称
     */
    @Schema(description = "趋势名称", example = "上升")
    private String trendName;

    /**
     * 风险等级
     */
    @Schema(description = "风险等级", example = "LOW")
    private String riskLevel;

    /**
     * 风险等级名称
     */
    @Schema(description = "风险等级名称", example = "低风险")
    private String riskLevelName;
}
