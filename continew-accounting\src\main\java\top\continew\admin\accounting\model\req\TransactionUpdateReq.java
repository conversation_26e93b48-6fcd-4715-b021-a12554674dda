package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.accounting.enums.SplitType;
import top.continew.admin.accounting.enums.TransactionType;

import jakarta.validation.constraints.*;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 账单更新请求参数
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "账单更新请求参数")
public class TransactionUpdateReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型", example = "EXPENSE")
    private TransactionType type;

    /**
     * 金额
     */
    @Schema(description = "金额", example = "100.50")
    @DecimalMin(value = "0.01", message = "金额必须大于0")
    @Digits(integer = 10, fraction = 2, message = "金额格式不正确")
    private BigDecimal amount;

    /**
     * 币种
     */
    @Schema(description = "币种", example = "CNY")
    @Pattern(regexp = "^[A-Z]{3}$", message = "币种格式不正确")
    private String currency;

    /**
     * 描述
     */
    @Schema(description = "描述", example = "午餐费用")
    @Size(max = 200, message = "描述长度不能超过200个字符")
    private String description;

    /**
     * 分类
     */
    @Schema(description = "分类", example = "餐饮")
    @Size(max = 50, message = "分类长度不能超过50个字符")
    private String category;

    /**
     * 标签
     */
    @Schema(description = "标签", example = "工作,午餐")
    @Size(max = 200, message = "标签长度不能超过200个字符")
    private String tags;

    /**
     * 交易时间
     */
    @Schema(description = "交易时间", example = "2025-01-01T12:00:00")
    private LocalDateTime transactionDate;

    /**
     * 附件URL列表
     */
    @Schema(description = "附件URL列表")
    private List<String> attachments;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "与同事聚餐")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    /**
     * 分摊类型
     */
    @Schema(description = "分摊类型", example = "EQUAL")
    private SplitType splitType;

    /**
     * 分摊参与者列表
     */
    @Schema(description = "分摊参与者列表")
    private List<TransactionCreateReq.SplitParticipant> splitParticipants;
}
