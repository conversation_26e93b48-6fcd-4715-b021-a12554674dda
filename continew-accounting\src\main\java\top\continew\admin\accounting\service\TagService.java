package top.continew.admin.accounting.service;

import top.continew.admin.common.base.service.BaseService;
import top.continew.admin.accounting.mapper.TagMapper;
import top.continew.admin.accounting.model.entity.TagDO;
import top.continew.admin.accounting.model.query.TagQuery;
import top.continew.admin.accounting.model.req.TagCreateReq;
import top.continew.admin.accounting.model.req.TagUpdateReq;
import top.continew.admin.accounting.model.resp.TagDetailResp;
import top.continew.admin.accounting.model.resp.TagListResp;
import top.continew.starter.data.service.IService;

import java.util.List;

/**
 * 标签管理业务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface TagService extends BaseService<TagListResp, TagDetailResp, TagQuery, TagCreateReq>, IService<TagDO> {

    /**
     * 更新标签信息
     *
     * @param req 更新请求
     * @param id  标签ID
     */
    void update(TagUpdateReq req, Long id);

    /**
     * 获取群组标签列表
     *
     * @param groupId 群组ID
     * @return 标签列表
     */
    List<TagListResp> getGroupTags(Long groupId);

    /**
     * 获取默认标签
     *
     * @param groupId 群组ID
     * @return 默认标签列表
     */
    List<TagDO> getDefaultTags(Long groupId);

    /**
     * 创建默认标签
     *
     * @param groupId 群组ID
     */
    void createDefaultTags(Long groupId);

    /**
     * 检查标签名称是否重复
     *
     * @param name    标签名称
     * @param groupId 群组ID
     * @param id      排除的标签ID
     * @return 是否重复
     */
    boolean isNameExists(String name, Long groupId, Long id);

    /**
     * 检查是否可以删除标签
     *
     * @param tagId 标签ID
     * @return 是否可以删除
     */
    boolean canDelete(Long tagId);

    /**
     * 批量删除标签
     *
     * @param tagIds 标签ID列表
     */
    void batchDelete(List<Long> tagIds);

    /**
     * 复制标签到其他群组
     *
     * @param tagId         源标签ID
     * @param targetGroupId 目标群组ID
     * @param operatorId    操作人ID
     * @return 新标签ID
     */
    Long copyToGroup(Long tagId, Long targetGroupId, Long operatorId);

    /**
     * 批量导入标签
     *
     * @param groupId    群组ID
     * @param tags       标签列表
     * @param operatorId 操作人ID
     * @return 导入结果
     */
    ImportResult batchImport(Long groupId, List<TagCreateReq> tags, Long operatorId);

    /**
     * 获取热门标签
     *
     * @param groupId 群组ID
     * @param limit   限制数量
     * @return 热门标签列表
     */
    List<TagDO> getHotTags(Long groupId, int limit);

    /**
     * 获取未使用的标签
     *
     * @param groupId 群组ID
     * @return 未使用的标签列表
     */
    List<TagDO> getUnusedTags(Long groupId);

    /**
     * 根据名称查询标签
     *
     * @param names   标签名称列表
     * @param groupId 群组ID
     * @return 标签列表
     */
    List<TagDO> getByNames(List<String> names, Long groupId);

    /**
     * 获取标签使用统计
     *
     * @param groupId 群组ID
     * @return 标签使用统计列表
     */
    List<TagMapper.TagStatistics> getTagStatistics(Long groupId);

    /**
     * 合并标签
     *
     * @param sourceTagIds 源标签ID列表
     * @param targetTagId  目标标签ID
     * @param operatorId   操作人ID
     */
    void mergeTags(List<Long> sourceTagIds, Long targetTagId, Long operatorId);

    /**
     * 自动创建标签
     *
     * @param groupId    群组ID
     * @param tagNames   标签名称列表
     * @param operatorId 操作人ID
     * @return 创建的标签列表
     */
    List<TagDO> autoCreateTags(Long groupId, List<String> tagNames, Long operatorId);

    /**
     * 清理未使用的标签
     *
     * @param groupId    群组ID
     * @param operatorId 操作人ID
     * @return 清理的标签数量
     */
    int cleanUnusedTags(Long groupId, Long operatorId);

    /**
     * 导入结果
     */
    class ImportResult {
        private int totalCount;
        private int successCount;
        private int failureCount;
        private List<String> errorMessages;

        // Getters and Setters
        public int getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(int totalCount) {
            this.totalCount = totalCount;
        }

        public int getSuccessCount() {
            return successCount;
        }

        public void setSuccessCount(int successCount) {
            this.successCount = successCount;
        }

        public int getFailureCount() {
            return failureCount;
        }

        public void setFailureCount(int failureCount) {
            this.failureCount = failureCount;
        }

        public List<String> getErrorMessages() {
            return errorMessages;
        }

        public void setErrorMessages(List<String> errorMessages) {
            this.errorMessages = errorMessages;
        }
    }
}
