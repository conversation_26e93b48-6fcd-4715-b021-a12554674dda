<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.accounting.mapper.DataSyncConfigMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="top.continew.admin.accounting.model.entity.DataSyncConfigDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="group_id" property="groupId" jdbcType="BIGINT"/>
        <result column="config_name" property="configName" jdbcType="VARCHAR"/>
        <result column="config_description" property="configDescription" jdbcType="VARCHAR"/>
        <result column="source_type" property="sourceType" jdbcType="VARCHAR"/>
        <result column="target_type" property="targetType" jdbcType="VARCHAR"/>
        <result column="sync_direction" property="syncDirection" jdbcType="VARCHAR"/>
        <result column="sync_mode" property="syncMode" jdbcType="VARCHAR"/>
        <result column="data_type" property="dataType" jdbcType="VARCHAR"/>
        <result column="source_config_json" property="sourceConfigJson" jdbcType="LONGTEXT"/>
        <result column="target_config_json" property="targetConfigJson" jdbcType="LONGTEXT"/>
        <result column="field_mapping_json" property="fieldMappingJson" jdbcType="LONGTEXT"/>
        <result column="filter_condition_json" property="filterConditionJson" jdbcType="LONGTEXT"/>
        <result column="transform_rules_json" property="transformRulesJson" jdbcType="LONGTEXT"/>
        <result column="conflict_resolution" property="conflictResolution" jdbcType="VARCHAR"/>
        <result column="sync_frequency" property="syncFrequency" jdbcType="VARCHAR"/>
        <result column="sync_settings_json" property="syncSettingsJson" jdbcType="LONGTEXT"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="last_sync_time" property="lastSyncTime" jdbcType="TIMESTAMP"/>
        <result column="next_sync_time" property="nextSyncTime" jdbcType="TIMESTAMP"/>
        <result column="total_sync_count" property="totalSyncCount" jdbcType="INTEGER"/>
        <result column="successful_sync_count" property="successfulSyncCount" jdbcType="INTEGER"/>
        <result column="failed_sync_count" property="failedSyncCount" jdbcType="INTEGER"/>
        <result column="avg_sync_duration" property="avgSyncDuration" jdbcType="BIGINT"/>
        <result column="last_error_message" property="lastErrorMessage" jdbcType="TEXT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="BIGINT"/>
        <result column="update_by" property="updateBy" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, group_id, config_name, config_description, source_type, target_type,
        sync_direction, sync_mode, data_type, source_config_json, target_config_json,
        field_mapping_json, filter_condition_json, transform_rules_json, conflict_resolution,
        sync_frequency, sync_settings_json, status, last_sync_time, next_sync_time,
        total_sync_count, successful_sync_count, failed_sync_count, avg_sync_duration,
        last_error_message, create_time, update_time, create_by, update_by
    </sql>

    <!-- 查询配置统计信息 -->
    <select id="selectConfigStatistics" resultType="java.util.Map">
        SELECT 
            COUNT(*) as totalConfigs,
            COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as activeConfigs,
            COUNT(CASE WHEN status = 'INACTIVE' THEN 1 END) as inactiveConfigs,
            COUNT(CASE WHEN status = 'ERROR' THEN 1 END) as errorConfigs,
            COUNT(CASE WHEN sync_mode = 'FULL' THEN 1 END) as fullSyncConfigs,
            COUNT(CASE WHEN sync_mode = 'INCREMENTAL' THEN 1 END) as incrementalSyncConfigs,
            COUNT(CASE WHEN sync_direction = 'UNIDIRECTIONAL' THEN 1 END) as unidirectionalConfigs,
            COUNT(CASE WHEN sync_direction = 'BIDIRECTIONAL' THEN 1 END) as bidirectionalConfigs,
            AVG(total_sync_count) as avgTotalSyncCount,
            AVG(successful_sync_count) as avgSuccessfulSyncCount,
            AVG(failed_sync_count) as avgFailedSyncCount,
            AVG(avg_sync_duration) as avgSyncDuration,
            SUM(total_sync_count) as totalSyncExecutions,
            SUM(successful_sync_count) as totalSuccessfulSyncs,
            SUM(failed_sync_count) as totalFailedSyncs
        FROM acc_data_sync_config
        <where>
            <if test="groupId != null">
                AND group_id = #{groupId}
            </if>
        </where>
    </select>

    <!-- 查询性能分析数据 -->
    <select id="selectPerformanceAnalysis" resultType="java.util.Map">
        SELECT 
            config_name,
            source_type,
            target_type,
            sync_mode,
            total_sync_count,
            successful_sync_count,
            failed_sync_count,
            CASE 
                WHEN total_sync_count > 0 THEN ROUND((successful_sync_count * 100.0 / total_sync_count), 2)
                ELSE 0 
            END as successRate,
            avg_sync_duration,
            last_sync_time,
            status
        FROM acc_data_sync_config
        <where>
            <if test="groupId != null">
                AND group_id = #{groupId}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="sourceType != null and sourceType != ''">
                AND source_type = #{sourceType}
            </if>
            <if test="targetType != null and targetType != ''">
                AND target_type = #{targetType}
            </if>
        </where>
        ORDER BY 
        <choose>
            <when test="orderBy == 'successRate'">
                successRate DESC
            </when>
            <when test="orderBy == 'avgDuration'">
                avg_sync_duration ASC
            </when>
            <when test="orderBy == 'totalCount'">
                total_sync_count DESC
            </when>
            <otherwise>
                last_sync_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 查询健康监控数据 -->
    <select id="selectHealthMonitoring" resultType="java.util.Map">
        SELECT 
            id,
            config_name,
            status,
            last_sync_time,
            next_sync_time,
            total_sync_count,
            failed_sync_count,
            last_error_message,
            CASE 
                WHEN status = 'ERROR' THEN 'CRITICAL'
                WHEN failed_sync_count > 0 AND total_sync_count > 0 
                     AND (failed_sync_count * 100.0 / total_sync_count) > 20 THEN 'WARNING'
                WHEN last_sync_time IS NULL OR last_sync_time &lt; DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 'WARNING'
                ELSE 'HEALTHY'
            END as healthStatus,
            CASE 
                WHEN total_sync_count > 0 THEN ROUND((failed_sync_count * 100.0 / total_sync_count), 2)
                ELSE 0 
            END as errorRate,
            TIMESTAMPDIFF(HOUR, last_sync_time, NOW()) as hoursSinceLastSync
        FROM acc_data_sync_config
        <where>
            <if test="groupId != null">
                AND group_id = #{groupId}
            </if>
            <if test="healthStatus != null and healthStatus != ''">
                AND (
                    <choose>
                        <when test="healthStatus == 'CRITICAL'">
                            status = 'ERROR'
                        </when>
                        <when test="healthStatus == 'WARNING'">
                            (failed_sync_count > 0 AND total_sync_count > 0 
                             AND (failed_sync_count * 100.0 / total_sync_count) > 20)
                            OR (last_sync_time IS NULL OR last_sync_time &lt; DATE_SUB(NOW(), INTERVAL 24 HOUR))
                        </when>
                        <when test="healthStatus == 'HEALTHY'">
                            status != 'ERROR' 
                            AND (total_sync_count = 0 OR (failed_sync_count * 100.0 / total_sync_count) &lt;= 20)
                            AND (last_sync_time IS NOT NULL AND last_sync_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR))
                        </when>
                    </choose>
                )
            </if>
        </where>
        ORDER BY 
            CASE 
                WHEN status = 'ERROR' THEN 1
                WHEN failed_sync_count > 0 AND total_sync_count > 0 
                     AND (failed_sync_count * 100.0 / total_sync_count) > 20 THEN 2
                WHEN last_sync_time IS NULL OR last_sync_time &lt; DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 3
                ELSE 4
            END,
            last_sync_time DESC
    </select>

    <!-- 查询同步趋势数据 -->
    <select id="selectSyncTrends" resultType="java.util.Map">
        SELECT 
            DATE(create_time) as date,
            COUNT(*) as configCount,
            COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as activeCount,
            COUNT(CASE WHEN status = 'ERROR' THEN 1 END) as errorCount,
            SUM(total_sync_count) as totalSyncs,
            SUM(successful_sync_count) as successfulSyncs,
            SUM(failed_sync_count) as failedSyncs,
            AVG(avg_sync_duration) as avgDuration
        FROM acc_data_sync_config
        <where>
            <if test="groupId != null">
                AND group_id = #{groupId}
            </if>
            <if test="startDate != null and startDate != ''">
                AND create_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND create_time &lt;= #{endDate}
            </if>
        </where>
        GROUP BY DATE(create_time)
        ORDER BY date DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 批量更新配置状态 -->
    <update id="batchUpdateStatus">
        UPDATE acc_data_sync_config 
        SET status = #{status}, update_time = NOW()
        WHERE id IN
        <foreach collection="configIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量更新同步统计 -->
    <update id="batchUpdateSyncStats">
        <foreach collection="statsList" item="stats" separator=";">
            UPDATE acc_data_sync_config 
            SET 
                total_sync_count = #{stats.totalSyncCount},
                successful_sync_count = #{stats.successfulSyncCount},
                failed_sync_count = #{stats.failedSyncCount},
                avg_sync_duration = #{stats.avgSyncDuration},
                last_sync_time = #{stats.lastSyncTime},
                last_error_message = #{stats.lastErrorMessage},
                update_time = NOW()
            WHERE id = #{stats.configId}
        </foreach>
    </update>

    <!-- 查询配置模板 -->
    <select id="selectConfigTemplates" resultType="java.util.Map">
        SELECT 
            source_type,
            target_type,
            sync_mode,
            conflict_resolution,
            COUNT(*) as usageCount,
            AVG(CASE WHEN total_sync_count > 0 THEN (successful_sync_count * 100.0 / total_sync_count) ELSE 0 END) as avgSuccessRate,
            AVG(avg_sync_duration) as avgDuration
        FROM acc_data_sync_config
        <where>
            <if test="groupId != null">
                AND group_id = #{groupId}
            </if>
            AND status = 'ACTIVE'
            AND total_sync_count > 0
        </where>
        GROUP BY source_type, target_type, sync_mode, conflict_resolution
        HAVING usageCount >= #{minUsageCount}
        ORDER BY avgSuccessRate DESC, usageCount DESC
    </select>

    <!-- 验证配置唯一性 -->
    <select id="validateConfigUniqueness" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM acc_data_sync_config
        WHERE group_id = #{groupId}
          AND config_name = #{configName}
          <if test="excludeId != null">
              AND id != #{excludeId}
          </if>
    </select>

    <!-- 查询相关配置 -->
    <select id="selectRelatedConfigs" resultType="top.continew.admin.accounting.model.entity.DataSyncConfigDO">
        SELECT <include refid="Base_Column_List"/>
        FROM acc_data_sync_config
        WHERE group_id = #{groupId}
          AND id != #{configId}
          AND (
              (source_type = #{sourceType} AND target_type = #{targetType})
              OR (source_type = #{targetType} AND target_type = #{sourceType})
              OR (data_type = #{dataType})
          )
        ORDER BY create_time DESC
    </select>

    <!-- 清理过期配置 -->
    <delete id="cleanupExpiredConfigs">
        DELETE FROM acc_data_sync_config
        WHERE status = 'INACTIVE'
          AND update_time &lt; DATE_SUB(NOW(), INTERVAL #{retentionDays} DAY)
          <if test="groupId != null">
              AND group_id = #{groupId}
          </if>
    </delete>

    <!-- 查询配置依赖关系 -->
    <select id="selectConfigDependencies" resultType="java.util.Map">
        SELECT 
            c1.id as configId,
            c1.config_name as configName,
            c2.id as dependentConfigId,
            c2.config_name as dependentConfigName,
            'DATA_SOURCE' as dependencyType
        FROM acc_data_sync_config c1
        JOIN acc_data_sync_config c2 ON c1.target_type = c2.source_type
        WHERE c1.group_id = #{groupId}
          AND c2.group_id = #{groupId}
          AND c1.id != c2.id
        
        UNION ALL
        
        SELECT 
            c1.id as configId,
            c1.config_name as configName,
            c2.id as dependentConfigId,
            c2.config_name as dependentConfigName,
            'BIDIRECTIONAL' as dependencyType
        FROM acc_data_sync_config c1
        JOIN acc_data_sync_config c2 ON c1.source_type = c2.target_type AND c1.target_type = c2.source_type
        WHERE c1.group_id = #{groupId}
          AND c2.group_id = #{groupId}
          AND c1.id &lt; c2.id
          AND c1.sync_direction = 'BIDIRECTIONAL'
          AND c2.sync_direction = 'BIDIRECTIONAL'
        
        ORDER BY configId, dependencyType
    </select>

</mapper>
