package top.continew.admin.accounting.event;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import top.continew.admin.accounting.service.ZapierWebhookService;

import java.util.HashMap;
import java.util.Map;

/**
 * Zapier事件触发器
 * 监听业务事件并触发相应的Zapier配置
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ZapierEventTrigger {

    private final ZapierWebhookService zapierWebhookService;

    /**
     * 监听交易创建事件
     */
    @EventListener
    @Async("zapierExecutor")
    public void handleTransactionCreated(TransactionCreatedEvent event) {
        log.debug("处理交易创建事件: transactionId={}, groupId={}", event.getTransactionId(), event.getGroupId());

        Map<String, Object> data = new HashMap<>();
        data.put("transactionId", event.getTransactionId());
        data.put("amount", event.getAmount());
        data.put("description", event.getDescription());
        data.put("categoryId", event.getCategoryId());
        data.put("categoryName", event.getCategoryName());
        data.put("walletId", event.getWalletId());
        data.put("walletName", event.getWalletName());
        data.put("type", event.getType());
        data.put("createTime", event.getCreateTime());
        data.put("createUser", event.getCreateUser());
        data.put("tags", event.getTags());
        data.put("participants", event.getParticipants());

        zapierWebhookService.triggerEventAsync(
                event.getGroupId(),
                "TRANSACTION_CREATED",
                "CREATE",
                event.getTransactionId(),
                "TRANSACTION",
                data
        );
    }

    /**
     * 监听交易更新事件
     */
    @EventListener
    @Async("zapierExecutor")
    public void handleTransactionUpdated(TransactionUpdatedEvent event) {
        log.debug("处理交易更新事件: transactionId={}, groupId={}", event.getTransactionId(), event.getGroupId());

        Map<String, Object> data = new HashMap<>();
        data.put("transactionId", event.getTransactionId());
        data.put("oldAmount", event.getOldAmount());
        data.put("newAmount", event.getNewAmount());
        data.put("oldDescription", event.getOldDescription());
        data.put("newDescription", event.getNewDescription());
        data.put("oldCategoryId", event.getOldCategoryId());
        data.put("newCategoryId", event.getNewCategoryId());
        data.put("updateTime", event.getUpdateTime());
        data.put("updateUser", event.getUpdateUser());
        data.put("changeFields", event.getChangeFields());

        zapierWebhookService.triggerEventAsync(
                event.getGroupId(),
                "TRANSACTION_UPDATED",
                "UPDATE",
                event.getTransactionId(),
                "TRANSACTION",
                data
        );
    }

    /**
     * 监听交易删除事件
     */
    @EventListener
    @Async("zapierExecutor")
    public void handleTransactionDeleted(TransactionDeletedEvent event) {
        log.debug("处理交易删除事件: transactionId={}, groupId={}", event.getTransactionId(), event.getGroupId());

        Map<String, Object> data = new HashMap<>();
        data.put("transactionId", event.getTransactionId());
        data.put("amount", event.getAmount());
        data.put("description", event.getDescription());
        data.put("categoryId", event.getCategoryId());
        data.put("categoryName", event.getCategoryName());
        data.put("walletId", event.getWalletId());
        data.put("walletName", event.getWalletName());
        data.put("type", event.getType());
        data.put("deleteTime", event.getDeleteTime());
        data.put("deleteUser", event.getDeleteUser());
        data.put("deleteReason", event.getDeleteReason());

        zapierWebhookService.triggerEventAsync(
                event.getGroupId(),
                "TRANSACTION_DELETED",
                "DELETE",
                event.getTransactionId(),
                "TRANSACTION",
                data
        );
    }

    /**
     * 监听钱包更新事件
     */
    @EventListener
    @Async("zapierExecutor")
    public void handleWalletUpdated(WalletUpdatedEvent event) {
        log.debug("处理钱包更新事件: walletId={}, groupId={}", event.getWalletId(), event.getGroupId());

        Map<String, Object> data = new HashMap<>();
        data.put("walletId", event.getWalletId());
        data.put("walletName", event.getWalletName());
        data.put("oldBalance", event.getOldBalance());
        data.put("newBalance", event.getNewBalance());
        data.put("balanceChange", event.getBalanceChange());
        data.put("currency", event.getCurrency());
        data.put("updateTime", event.getUpdateTime());
        data.put("updateUser", event.getUpdateUser());
        data.put("updateReason", event.getUpdateReason());
        data.put("relatedTransactionId", event.getRelatedTransactionId());

        zapierWebhookService.triggerEventAsync(
                event.getGroupId(),
                "WALLET_UPDATED",
                "UPDATE",
                event.getWalletId(),
                "WALLET",
                data
        );
    }

    /**
     * 监听报表生成事件
     */
    @EventListener
    @Async("zapierExecutor")
    public void handleReportGenerated(ReportGeneratedEvent event) {
        log.debug("处理报表生成事件: reportId={}, groupId={}", event.getReportId(), event.getGroupId());

        Map<String, Object> data = new HashMap<>();
        data.put("reportId", event.getReportId());
        data.put("reportType", event.getReportType());
        data.put("reportName", event.getReportName());
        data.put("startDate", event.getStartDate());
        data.put("endDate", event.getEndDate());
        data.put("totalIncome", event.getTotalIncome());
        data.put("totalExpense", event.getTotalExpense());
        data.put("netAmount", event.getNetAmount());
        data.put("transactionCount", event.getTransactionCount());
        data.put("generateTime", event.getGenerateTime());
        data.put("generateUser", event.getGenerateUser());
        data.put("fileUrl", event.getFileUrl());
        data.put("format", event.getFormat());

        zapierWebhookService.triggerEventAsync(
                event.getGroupId(),
                "REPORT_GENERATED",
                "CREATE",
                event.getReportId(),
                "REPORT",
                data
        );
    }

    /**
     * 监听用户加入群组事件
     */
    @EventListener
    @Async("zapierExecutor")
    public void handleUserJoinedGroup(UserJoinedGroupEvent event) {
        log.debug("处理用户加入群组事件: userId={}, groupId={}", event.getUserId(), event.getGroupId());

        Map<String, Object> data = new HashMap<>();
        data.put("userId", event.getUserId());
        data.put("username", event.getUsername());
        data.put("nickname", event.getNickname());
        data.put("email", event.getEmail());
        data.put("role", event.getRole());
        data.put("joinTime", event.getJoinTime());
        data.put("invitedBy", event.getInvitedBy());
        data.put("inviteCode", event.getInviteCode());

        zapierWebhookService.triggerEventAsync(
                event.getGroupId(),
                "USER_JOINED",
                "CREATE",
                event.getUserId(),
                "USER",
                data
        );
    }

    /**
     * 监听用户离开群组事件
     */
    @EventListener
    @Async("zapierExecutor")
    public void handleUserLeftGroup(UserLeftGroupEvent event) {
        log.debug("处理用户离开群组事件: userId={}, groupId={}", event.getUserId(), event.getGroupId());

        Map<String, Object> data = new HashMap<>();
        data.put("userId", event.getUserId());
        data.put("username", event.getUsername());
        data.put("nickname", event.getNickname());
        data.put("role", event.getRole());
        data.put("leaveTime", event.getLeaveTime());
        data.put("leaveReason", event.getLeaveReason());
        data.put("isKicked", event.getIsKicked());
        data.put("kickedBy", event.getKickedBy());

        zapierWebhookService.triggerEventAsync(
                event.getGroupId(),
                "USER_LEFT",
                "DELETE",
                event.getUserId(),
                "USER",
                data
        );
    }

    /**
     * 监听债务创建事件
     */
    @EventListener
    @Async("zapierExecutor")
    public void handleDebtCreated(DebtCreatedEvent event) {
        log.debug("处理债务创建事件: debtId={}, groupId={}", event.getDebtId(), event.getGroupId());

        Map<String, Object> data = new HashMap<>();
        data.put("debtId", event.getDebtId());
        data.put("creditorId", event.getCreditorId());
        data.put("creditorName", event.getCreditorName());
        data.put("debtorId", event.getDebtorId());
        data.put("debtorName", event.getDebtorName());
        data.put("amount", event.getAmount());
        data.put("description", event.getDescription());
        data.put("relatedTransactionId", event.getRelatedTransactionId());
        data.put("createTime", event.getCreateTime());
        data.put("dueDate", event.getDueDate());
        data.put("status", event.getStatus());

        zapierWebhookService.triggerEventAsync(
                event.getGroupId(),
                "DEBT_CREATED",
                "CREATE",
                event.getDebtId(),
                "DEBT",
                data
        );
    }

    /**
     * 监听债务结算事件
     */
    @EventListener
    @Async("zapierExecutor")
    public void handleDebtSettled(DebtSettledEvent event) {
        log.debug("处理债务结算事件: debtId={}, groupId={}", event.getDebtId(), event.getGroupId());

        Map<String, Object> data = new HashMap<>();
        data.put("debtId", event.getDebtId());
        data.put("creditorId", event.getCreditorId());
        data.put("creditorName", event.getCreditorName());
        data.put("debtorId", event.getDebtorId());
        data.put("debtorName", event.getDebtorName());
        data.put("originalAmount", event.getOriginalAmount());
        data.put("settledAmount", event.getSettledAmount());
        data.put("settleTime", event.getSettleTime());
        data.put("settleMethod", event.getSettleMethod());
        data.put("settleNote", event.getSettleNote());
        data.put("relatedTransactionId", event.getRelatedTransactionId());

        zapierWebhookService.triggerEventAsync(
                event.getGroupId(),
                "DEBT_SETTLED",
                "UPDATE",
                event.getDebtId(),
                "DEBT",
                data
        );
    }
}
