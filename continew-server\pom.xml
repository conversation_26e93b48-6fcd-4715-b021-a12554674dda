<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>top.continew.admin</groupId>
        <artifactId>continew-admin</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>continew-server</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>API 及打包部署模块</description>

    <properties>
        <!-- ### 打包配置相关 ### -->
        <!-- 启动类 -->
        <main-class>top.continew.admin.ContiNewAdminApplication</main-class>
        <!-- 程序 jar 输出目录 -->
        <bin-path>bin/</bin-path>
        <!-- 配置文件输出目录 -->
        <config-path>config/</config-path>
        <!-- 依赖 jar 输出目录 -->
        <lib-path>lib/</lib-path>
    </properties>

    <dependencies>
        <!-- 系统管理模块 -->
        <dependency>
            <groupId>top.continew.admin</groupId>
            <artifactId>continew-system</artifactId>
        </dependency>

        <!-- 能力开放插件 -->
        <dependency>
            <groupId>top.continew.admin</groupId>
            <artifactId>continew-plugin-open</artifactId>
        </dependency>

        <!-- 租户插件 -->
        <dependency>
            <groupId>top.continew.admin</groupId>
            <artifactId>continew-plugin-tenant</artifactId>
        </dependency>

        <!-- 记账核心模块 -->
        <dependency>
            <groupId>top.continew.admin</groupId>
            <artifactId>continew-accounting</artifactId>
        </dependency>

        <!-- 机器人集成模块 -->
        <dependency>
            <groupId>top.continew.admin</groupId>
            <artifactId>continew-bot</artifactId>
        </dependency>

        <!-- 任务调度插件 -->
        <dependency>
            <groupId>top.continew.admin</groupId>
            <artifactId>continew-plugin-schedule</artifactId>
        </dependency>

        <!-- 代码生成器插件 -->
        <dependency>
            <groupId>top.continew.admin</groupId>
            <artifactId>continew-plugin-generator</artifactId>
        </dependency>

        <!-- ContiNew Starter 链路追踪模块 -->
        <dependency>
            <groupId>top.continew.starter</groupId>
            <artifactId>continew-starter-trace</artifactId>
        </dependency>

        <!-- Liquibase（用于管理数据库版本，跟踪、管理和应用数据库变化） -->
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.parent.name}</finalName>
        <plugins>
            <!-- Maven 打包插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <!-- 排除配置文件 -->
                    <excludes>
                        <exclude>${config-path}</exclude>
                        <exclude>db/</exclude>
                        <exclude>templates/</exclude>
                        <exclude>logback-spring.xml</exclude>
                    </excludes>
                    <archive>
                        <manifest>
                            <mainClass>${main-class}</mainClass>
                            <!-- 为 MANIFEST.MF 中的 Class-Path 加入依赖 jar 目录前缀 -->
                            <classpathPrefix>../${lib-path}</classpathPrefix>
                            <addClasspath>true</addClasspath>
                            <!-- jar 包不包含唯一版本标识 -->
                            <useUniqueVersions>false</useUniqueVersions>
                        </manifest>
                        <manifestEntries>
                            <!--为 MANIFEST.MF 中的 Class-Path 加入配置文件目录前缀 -->
                            <Class-Path>../${config-path}</Class-Path>
                        </manifestEntries>
                    </archive>
                    <outputDirectory>${project.build.directory}/app/${bin-path}</outputDirectory>
                </configuration>
            </plugin>
            <!-- 拷贝依赖 jar -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/app/${lib-path}</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- 拷贝配置文件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-resources</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>src/main/resources/${config-path}</directory>
                                </resource>
                                <resource>
                                    <directory>src/main/resources</directory>
                                    <includes>
                                        <include>db/</include>
                                        <include>templates/</include>
                                        <include>logback-spring.xml</include>
                                    </includes>
                                </resource>
                            </resources>
                            <outputDirectory>${project.build.directory}/app/${config-path}</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>