-- =============================================
-- 订阅管理系统数据库表定义
-- =============================================

-- 订阅套餐表
CREATE TABLE `acc_subscription_plan` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name` varchar(100) NOT NULL COMMENT '套餐名称',
    `code` varchar(50) NOT NULL COMMENT '套餐代码',
    `description` text COMMENT '套餐描述',
    `price` decimal(10,2) NOT NULL COMMENT '价格',
    `currency` varchar(10) NOT NULL DEFAULT 'USD' COMMENT '币种',
    `billing_cycle` varchar(20) NOT NULL COMMENT '计费周期',
    `features` json COMMENT '功能特性',
    `limits` json COMMENT '使用限制',
    `trial_days` int NOT NULL DEFAULT 0 COMMENT '试用天数',
    `is_popular` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否热门',
    `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `create_user` bigint COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint COMMENT '更新人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`),
    KEY `idx_status_sort` (`status`, `sort_order`),
    KEY `idx_popular` (`is_popular`),
    KEY `idx_billing_cycle` (`billing_cycle`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订阅套餐表';

-- 订阅记录表
CREATE TABLE `acc_subscription` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `group_id` bigint NOT NULL COMMENT '群组ID',
    `user_id` bigint NOT NULL COMMENT '用户ID',
    `plan_id` bigint NOT NULL COMMENT '套餐ID',
    `status` varchar(20) NOT NULL COMMENT '状态：ACTIVE-有效，EXPIRED-过期，CANCELLED-取消，SUSPENDED-暂停，TRIAL-试用',
    `start_date` datetime NOT NULL COMMENT '开始时间',
    `end_date` datetime NOT NULL COMMENT '结束时间',
    `trial_end_date` datetime COMMENT '试用结束时间',
    `billing_cycle` varchar(20) NOT NULL COMMENT '计费周期',
    `amount_paid` decimal(10,2) NOT NULL COMMENT '支付金额',
    `currency` varchar(10) NOT NULL DEFAULT 'USD' COMMENT '币种',
    `payment_method` varchar(50) COMMENT '支付方式',
    `auto_renew` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否自动续费',
    `cancelled_at` datetime COMMENT '取消时间',
    `cancellation_reason` varchar(500) COMMENT '取消原因',
    `create_user` bigint COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint COMMENT '更新人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_group_id` (`group_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_plan_id` (`plan_id`),
    KEY `idx_status` (`status`),
    KEY `idx_end_date` (`end_date`),
    KEY `idx_group_status_end` (`group_id`, `status`, `end_date`),
    KEY `idx_auto_renew_end` (`auto_renew`, `end_date`),
    CONSTRAINT `fk_subscription_plan` FOREIGN KEY (`plan_id`) REFERENCES `acc_subscription_plan` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订阅记录表';

-- 使用量统计表
CREATE TABLE `acc_usage_statistics` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `group_id` bigint NOT NULL COMMENT '群组ID',
    `stat_date` date NOT NULL COMMENT '统计日期',
    `transaction_count` int NOT NULL DEFAULT 0 COMMENT '交易数量',
    `ocr_count` int NOT NULL DEFAULT 0 COMMENT 'OCR识别次数',
    `api_calls` int NOT NULL DEFAULT 0 COMMENT 'API调用次数',
    `storage_used` bigint NOT NULL DEFAULT 0 COMMENT '存储使用量(字节)',
    `export_count` int NOT NULL DEFAULT 0 COMMENT '导出次数',
    `webhook_calls` int NOT NULL DEFAULT 0 COMMENT 'Webhook调用次数',
    `active_users` int NOT NULL DEFAULT 0 COMMENT '活跃用户数',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_group_date` (`group_id`, `stat_date`),
    KEY `idx_stat_date` (`stat_date`),
    KEY `idx_group_id` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='使用量统计表';

-- =============================================
-- 索引优化
-- =============================================

-- 订阅套餐表索引
CREATE INDEX `idx_subscription_plan_price` ON `acc_subscription_plan` (`price`);
CREATE INDEX `idx_subscription_plan_create_time` ON `acc_subscription_plan` (`create_time`);

-- 订阅记录表索引
CREATE INDEX `idx_subscription_create_time` ON `acc_subscription` (`create_time`);
CREATE INDEX `idx_subscription_cancelled_at` ON `acc_subscription` (`cancelled_at`);
CREATE INDEX `idx_subscription_trial_end` ON `acc_subscription` (`trial_end_date`);

-- 使用量统计表索引
CREATE INDEX `idx_usage_statistics_group_date_range` ON `acc_usage_statistics` (`group_id`, `stat_date`);

-- =============================================
-- 视图定义
-- =============================================

-- 订阅概览视图
CREATE VIEW `v_subscription_overview` AS
SELECT 
    s.id,
    s.group_id,
    g.name AS group_name,
    s.user_id,
    u.nickname AS user_name,
    s.plan_id,
    p.name AS plan_name,
    p.code AS plan_code,
    s.status,
    s.start_date,
    s.end_date,
    s.billing_cycle,
    s.amount_paid,
    s.currency,
    s.auto_renew,
    DATEDIFF(s.end_date, NOW()) AS remaining_days,
    CASE 
        WHEN s.end_date <= NOW() THEN 'EXPIRED'
        WHEN s.end_date <= DATE_ADD(NOW(), INTERVAL 7 DAY) THEN 'EXPIRING_SOON'
        ELSE 'NORMAL'
    END AS expiration_status,
    s.create_time
FROM acc_subscription s
LEFT JOIN acc_subscription_plan p ON s.plan_id = p.id
LEFT JOIN acc_group g ON s.group_id = g.id
LEFT JOIN sys_user u ON s.user_id = u.id;

-- 使用量汇总视图
CREATE VIEW `v_usage_summary` AS
SELECT 
    us.group_id,
    g.name AS group_name,
    DATE_FORMAT(us.stat_date, '%Y-%m') AS stat_month,
    SUM(us.transaction_count) AS monthly_transactions,
    SUM(us.ocr_count) AS monthly_ocr_usage,
    SUM(us.api_calls) AS monthly_api_calls,
    SUM(us.storage_used) AS monthly_storage_used,
    SUM(us.export_count) AS monthly_exports,
    SUM(us.webhook_calls) AS monthly_webhook_calls,
    AVG(us.active_users) AS avg_active_users
FROM acc_usage_statistics us
LEFT JOIN acc_group g ON us.group_id = g.id
GROUP BY us.group_id, g.name, DATE_FORMAT(us.stat_date, '%Y-%m');

-- =============================================
-- 存储过程
-- =============================================

DELIMITER $$

-- 自动处理过期订阅
CREATE PROCEDURE `sp_handle_expired_subscriptions`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE subscription_id BIGINT;
    DECLARE cur CURSOR FOR 
        SELECT id FROM acc_subscription 
        WHERE status = 'ACTIVE' AND end_date <= NOW();
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    OPEN cur;
    read_loop: LOOP
        FETCH cur INTO subscription_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        UPDATE acc_subscription 
        SET status = 'EXPIRED', update_time = NOW() 
        WHERE id = subscription_id;
    END LOOP;
    CLOSE cur;
    
    SELECT ROW_COUNT() AS affected_rows;
END$$

-- 计算订阅统计
CREATE PROCEDURE `sp_calculate_subscription_stats`(
    IN start_date DATE,
    IN end_date DATE
)
BEGIN
    SELECT 
        COUNT(*) AS total_subscriptions,
        COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) AS active_subscriptions,
        COUNT(CASE WHEN status = 'EXPIRED' THEN 1 END) AS expired_subscriptions,
        COUNT(CASE WHEN status = 'CANCELLED' THEN 1 END) AS cancelled_subscriptions,
        SUM(amount_paid) AS total_revenue,
        AVG(amount_paid) AS avg_revenue,
        AVG(DATEDIFF(COALESCE(cancelled_at, end_date), start_date)) AS avg_subscription_days
    FROM acc_subscription 
    WHERE create_time >= start_date AND create_time <= end_date;
END$$

-- 重置使用量统计
CREATE PROCEDURE `sp_reset_usage_statistics`(
    IN reset_date DATE
)
BEGIN
    UPDATE acc_usage_statistics 
    SET 
        transaction_count = 0,
        ocr_count = 0,
        api_calls = 0,
        export_count = 0,
        webhook_calls = 0,
        update_time = NOW()
    WHERE stat_date = reset_date;
    
    SELECT ROW_COUNT() AS affected_rows;
END$$

DELIMITER ;

-- =============================================
-- 示例数据
-- =============================================

-- 插入订阅套餐示例数据
INSERT INTO `acc_subscription_plan` (`name`, `code`, `description`, `price`, `currency`, `billing_cycle`, `features`, `limits`, `trial_days`, `is_popular`, `sort_order`, `status`) VALUES
('试用版', 'TRIAL', '免费试用版本，功能有限', 0.00, 'USD', 'MONTHLY', 
 '{"basicReports": true, "exportExcel": false, "advancedReports": false, "apiAccess": false, "webhooks": false, "ocrRecognition": false}',
 '{"maxTransactionsPerMonth": 50, "maxOcrPerMonth": 0, "maxApiCallsPerMonth": 0, "maxStorageGB": 0.1, "maxExportsPerMonth": 0, "maxWebhooksPerMonth": 0, "maxActiveUsers": 3}',
 7, 0, 1, 1),
('专业版', 'PRO', '适合小团队使用的专业版套餐', 9.99, 'USD', 'MONTHLY',
 '{"basicReports": true, "exportExcel": true, "advancedReports": true, "apiAccess": true, "webhooks": false, "ocrRecognition": true}',
 '{"maxTransactionsPerMonth": 1000, "maxOcrPerMonth": 100, "maxApiCallsPerMonth": 5000, "maxStorageGB": 5, "maxExportsPerMonth": 50, "maxWebhooksPerMonth": 0, "maxActiveUsers": 10}',
 14, 1, 2, 1),
('商业版', 'BUSINESS', '适合中型企业的商业版套餐', 29.99, 'USD', 'MONTHLY',
 '{"basicReports": true, "exportExcel": true, "advancedReports": true, "apiAccess": true, "webhooks": true, "ocrRecognition": true}',
 '{"maxTransactionsPerMonth": 10000, "maxOcrPerMonth": 500, "maxApiCallsPerMonth": 50000, "maxStorageGB": 50, "maxExportsPerMonth": 500, "maxWebhooksPerMonth": 100, "maxActiveUsers": 50}',
 14, 1, 3, 1),
('企业版', 'ENTERPRISE', '适合大型企业的企业版套餐', 99.99, 'USD', 'MONTHLY',
 '{"basicReports": true, "exportExcel": true, "advancedReports": true, "apiAccess": true, "webhooks": true, "ocrRecognition": true}',
 '{"maxTransactionsPerMonth": -1, "maxOcrPerMonth": -1, "maxApiCallsPerMonth": -1, "maxStorageGB": -1, "maxExportsPerMonth": -1, "maxWebhooksPerMonth": -1, "maxActiveUsers": -1}',
 30, 0, 4, 1);
