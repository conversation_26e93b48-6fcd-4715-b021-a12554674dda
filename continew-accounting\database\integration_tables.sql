-- 群组记账机器人系统集成相关数据表
-- OCR识别、第三方集成、订阅管理等功能表

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ================================
-- OCR识别相关表
-- ================================

-- OCR任务表
DROP TABLE IF EXISTS `acc_ocr_task`;
CREATE TABLE `acc_ocr_task` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `group_id` bigint NOT NULL COMMENT '群组ID',
    `user_id` bigint NOT NULL COMMENT '用户ID',
    `file_name` varchar(255) NOT NULL COMMENT '文件名',
    `file_path` varchar(500) NOT NULL COMMENT '文件路径',
    `file_size` bigint NOT NULL COMMENT '文件大小',
    `file_type` varchar(50) NOT NULL COMMENT '文件类型',
    `engine_type` varchar(50) NOT NULL COMMENT 'OCR引擎类型',
    `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '状态(PENDING/PROCESSING/COMPLETED/FAILED)',
    `confidence_score` decimal(5,2) DEFAULT NULL COMMENT '置信度分数',
    `raw_text` text DEFAULT NULL COMMENT '原始识别文本',
    `structured_data` json DEFAULT NULL COMMENT '结构化数据',
    `extracted_info` json DEFAULT NULL COMMENT '提取的信息',
    `processing_time` bigint DEFAULT NULL COMMENT '处理耗时(毫秒)',
    `error_message` text DEFAULT NULL COMMENT '错误信息',
    `retry_count` int NOT NULL DEFAULT 0 COMMENT '重试次数',
    `completed_at` datetime DEFAULT NULL COMMENT '完成时间',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除 1:已删除)',
    PRIMARY KEY (`id`),
    KEY `idx_group_id` (`group_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_engine_type` (`engine_type`),
    KEY `idx_completed_at` (`completed_at`),
    CONSTRAINT `fk_ocr_task_group` FOREIGN KEY (`group_id`) REFERENCES `acc_group` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OCR任务表';

-- OCR模板表
DROP TABLE IF EXISTS `acc_ocr_template`;
CREATE TABLE `acc_ocr_template` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `group_id` bigint DEFAULT NULL COMMENT '群组ID(NULL表示系统模板)',
    `name` varchar(100) NOT NULL COMMENT '模板名称',
    `description` varchar(500) DEFAULT NULL COMMENT '模板描述',
    `receipt_type` varchar(50) NOT NULL COMMENT '收据类型',
    `field_mapping` json NOT NULL COMMENT '字段映射配置',
    `extraction_rules` json DEFAULT NULL COMMENT '提取规则',
    `validation_rules` json DEFAULT NULL COMMENT '验证规则',
    `is_system` tinyint NOT NULL DEFAULT 0 COMMENT '是否系统模板(0:否 1:是)',
    `usage_count` bigint NOT NULL DEFAULT 0 COMMENT '使用次数',
    `accuracy_rate` decimal(5,2) DEFAULT NULL COMMENT '准确率',
    `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除 1:已删除)',
    PRIMARY KEY (`id`),
    KEY `idx_group_id` (`group_id`),
    KEY `idx_receipt_type` (`receipt_type`),
    KEY `idx_is_system` (`is_system`),
    KEY `idx_status` (`status`),
    CONSTRAINT `fk_ocr_template_group` FOREIGN KEY (`group_id`) REFERENCES `acc_group` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OCR模板表';

-- ================================
-- Google Sheets集成表
-- ================================

-- Google Sheets配置表
DROP TABLE IF EXISTS `acc_google_sheets_config`;
CREATE TABLE `acc_google_sheets_config` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `group_id` bigint NOT NULL COMMENT '群组ID',
    `name` varchar(100) NOT NULL COMMENT '配置名称',
    `spreadsheet_id` varchar(100) NOT NULL COMMENT '电子表格ID',
    `sheet_name` varchar(100) NOT NULL COMMENT '工作表名称',
    `sync_type` varchar(20) NOT NULL COMMENT '同步类型(EXPORT/IMPORT/BIDIRECTIONAL)',
    `data_type` varchar(50) NOT NULL COMMENT '数据类型',
    `field_mapping` json NOT NULL COMMENT '字段映射',
    `sync_frequency` varchar(20) DEFAULT NULL COMMENT '同步频率',
    `last_sync_time` datetime DEFAULT NULL COMMENT '最后同步时间',
    `next_sync_time` datetime DEFAULT NULL COMMENT '下次同步时间',
    `auth_config` json DEFAULT NULL COMMENT '认证配置',
    `enabled` tinyint NOT NULL DEFAULT 1 COMMENT '是否启用(0:否 1:是)',
    `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态',
    `error_message` text DEFAULT NULL COMMENT '错误信息',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除 1:已删除)',
    PRIMARY KEY (`id`),
    KEY `idx_group_id` (`group_id`),
    KEY `idx_spreadsheet_id` (`spreadsheet_id`),
    KEY `idx_sync_type` (`sync_type`),
    KEY `idx_enabled` (`enabled`),
    KEY `idx_status` (`status`),
    CONSTRAINT `fk_google_sheets_config_group` FOREIGN KEY (`group_id`) REFERENCES `acc_group` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Google Sheets配置表';

-- Google Sheets同步日志表
DROP TABLE IF EXISTS `acc_google_sheets_sync_log`;
CREATE TABLE `acc_google_sheets_sync_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `config_id` bigint NOT NULL COMMENT '配置ID',
    `group_id` bigint NOT NULL COMMENT '群组ID',
    `sync_type` varchar(20) NOT NULL COMMENT '同步类型',
    `operation_type` varchar(20) NOT NULL COMMENT '操作类型(CREATE/UPDATE/DELETE)',
    `records_processed` int NOT NULL DEFAULT 0 COMMENT '处理记录数',
    `records_success` int NOT NULL DEFAULT 0 COMMENT '成功记录数',
    `records_failed` int NOT NULL DEFAULT 0 COMMENT '失败记录数',
    `status` varchar(20) NOT NULL COMMENT '状态(SUCCESS/FAILED/PARTIAL)',
    `start_time` datetime NOT NULL COMMENT '开始时间',
    `end_time` datetime DEFAULT NULL COMMENT '结束时间',
    `duration` bigint DEFAULT NULL COMMENT '耗时(毫秒)',
    `error_details` json DEFAULT NULL COMMENT '错误详情',
    `sync_summary` json DEFAULT NULL COMMENT '同步摘要',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除 1:已删除)',
    PRIMARY KEY (`id`),
    KEY `idx_config_id` (`config_id`),
    KEY `idx_group_id` (`group_id`),
    KEY `idx_sync_type` (`sync_type`),
    KEY `idx_status` (`status`),
    KEY `idx_start_time` (`start_time`),
    CONSTRAINT `fk_google_sheets_sync_log_config` FOREIGN KEY (`config_id`) REFERENCES `acc_google_sheets_config` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_google_sheets_sync_log_group` FOREIGN KEY (`group_id`) REFERENCES `acc_group` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Google Sheets同步日志表';

-- ================================
-- 订阅管理表
-- ================================

-- 订阅套餐表
DROP TABLE IF EXISTS `acc_subscription_plan`;
CREATE TABLE `acc_subscription_plan` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name` varchar(50) NOT NULL COMMENT '套餐名称',
    `code` varchar(20) NOT NULL COMMENT '套餐代码',
    `description` varchar(500) DEFAULT NULL COMMENT '套餐描述',
    `price` decimal(10,2) NOT NULL COMMENT '价格',
    `currency` varchar(10) NOT NULL DEFAULT 'USD' COMMENT '币种',
    `billing_cycle` varchar(20) NOT NULL COMMENT '计费周期(MONTHLY/YEARLY)',
    `features` json NOT NULL COMMENT '功能特性',
    `limits` json NOT NULL COMMENT '使用限制',
    `trial_days` int NOT NULL DEFAULT 0 COMMENT '试用天数',
    `is_popular` tinyint NOT NULL DEFAULT 0 COMMENT '是否热门(0:否 1:是)',
    `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
    `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除 1:已删除)',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`),
    KEY `idx_billing_cycle` (`billing_cycle`),
    KEY `idx_status` (`status`),
    KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订阅套餐表';

-- 订阅记录表
DROP TABLE IF EXISTS `acc_subscription`;
CREATE TABLE `acc_subscription` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `group_id` bigint NOT NULL COMMENT '群组ID',
    `plan_id` bigint NOT NULL COMMENT '套餐ID',
    `user_id` bigint NOT NULL COMMENT '订阅用户ID',
    `status` varchar(20) NOT NULL COMMENT '状态(ACTIVE/EXPIRED/CANCELLED/SUSPENDED)',
    `start_date` datetime NOT NULL COMMENT '开始时间',
    `end_date` datetime NOT NULL COMMENT '结束时间',
    `auto_renew` tinyint NOT NULL DEFAULT 1 COMMENT '自动续费(0:否 1:是)',
    `payment_method` varchar(50) DEFAULT NULL COMMENT '支付方式',
    `amount_paid` decimal(10,2) NOT NULL COMMENT '支付金额',
    `currency` varchar(10) NOT NULL COMMENT '币种',
    `billing_cycle` varchar(20) NOT NULL COMMENT '计费周期',
    `trial_end_date` datetime DEFAULT NULL COMMENT '试用结束时间',
    `cancelled_at` datetime DEFAULT NULL COMMENT '取消时间',
    `cancellation_reason` varchar(500) DEFAULT NULL COMMENT '取消原因',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除 1:已删除)',
    PRIMARY KEY (`id`),
    KEY `idx_group_id` (`group_id`),
    KEY `idx_plan_id` (`plan_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_start_date` (`start_date`),
    KEY `idx_end_date` (`end_date`),
    CONSTRAINT `fk_subscription_group` FOREIGN KEY (`group_id`) REFERENCES `acc_group` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_subscription_plan` FOREIGN KEY (`plan_id`) REFERENCES `acc_subscription_plan` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订阅记录表';

-- 使用量统计表
DROP TABLE IF EXISTS `acc_usage_statistics`;
CREATE TABLE `acc_usage_statistics` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `group_id` bigint NOT NULL COMMENT '群组ID',
    `stat_date` date NOT NULL COMMENT '统计日期',
    `transaction_count` int NOT NULL DEFAULT 0 COMMENT '交易数量',
    `ocr_count` int NOT NULL DEFAULT 0 COMMENT 'OCR识别次数',
    `api_calls` int NOT NULL DEFAULT 0 COMMENT 'API调用次数',
    `storage_used` bigint NOT NULL DEFAULT 0 COMMENT '存储使用量(字节)',
    `export_count` int NOT NULL DEFAULT 0 COMMENT '导出次数',
    `webhook_calls` int NOT NULL DEFAULT 0 COMMENT 'Webhook调用次数',
    `active_users` int NOT NULL DEFAULT 0 COMMENT '活跃用户数',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_group_date` (`group_id`, `stat_date`),
    KEY `idx_stat_date` (`stat_date`),
    CONSTRAINT `fk_usage_statistics_group` FOREIGN KEY (`group_id`) REFERENCES `acc_group` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='使用量统计表';

SET FOREIGN_KEY_CHECKS = 1;
