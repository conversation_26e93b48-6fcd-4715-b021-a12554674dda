package top.continew.admin.bot.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.admin.accounting.enums.PlatformType;
import top.continew.admin.bot.model.entity.config.BotSettings;
import top.continew.starter.data.mybatis.plus.base.BaseEntity;

/**
 * 机器人配置实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "bot_config", autoResultMap = true)
@Schema(description = "机器人配置")
public class BotConfigDO extends BaseEntity {

    /**
     * 群组ID
     */
    @Schema(description = "群组ID")
    private Long groupId;

    /**
     * 平台类型
     */
    @Schema(description = "平台类型")
    private PlatformType platform;

    /**
     * 机器人Token
     */
    @Schema(description = "机器人Token")
    private String botToken;

    /**
     * 机器人用户名
     */
    @Schema(description = "机器人用户名")
    private String botUsername;

    /**
     * Webhook URL
     */
    @Schema(description = "Webhook URL")
    private String webhookUrl;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    private Boolean enabled;

    /**
     * 机器人设置
     */
    @Schema(description = "机器人设置")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private BotSettings settings;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;
}
