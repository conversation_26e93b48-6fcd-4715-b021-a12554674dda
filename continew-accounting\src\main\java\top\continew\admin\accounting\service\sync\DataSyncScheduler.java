package top.continew.admin.accounting.service.sync;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import top.continew.admin.accounting.model.entity.DataSyncConfigDO;
import top.continew.admin.accounting.model.entity.DataSyncScheduleDO;
import top.continew.admin.accounting.model.req.DataSyncExecuteReq;
import top.continew.admin.accounting.model.resp.DataSyncResultResp;
import top.continew.admin.accounting.service.DataSyncScheduleService;
import top.continew.admin.accounting.service.DataSyncService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 数据同步调度器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "continew.accounting.sync.scheduler.enabled", havingValue = "true", matchIfMissing = true)
public class DataSyncScheduler {

    private final DataSyncScheduleService dataSyncScheduleService;
    private final DataSyncService dataSyncService;
    
    /**
     * 执行器线程池
     */
    private final ExecutorService executorService = Executors.newFixedThreadPool(10);
    
    /**
     * 正在执行的任务
     */
    private final ConcurrentHashMap<Long, CompletableFuture<Void>> runningTasks = new ConcurrentHashMap<>();

    /**
     * 定时扫描待执行的同步计划
     * 每分钟执行一次
     */
    @Scheduled(cron = "0 * * * * ?")
    public void scanPendingSchedules() {
        try {
            log.debug("开始扫描待执行的同步计划");
            
            LocalDateTime currentTime = LocalDateTime.now();
            List<DataSyncScheduleDO> pendingSchedules = dataSyncScheduleService.getPendingSchedules(currentTime, 100);
            
            if (CollUtil.isEmpty(pendingSchedules)) {
                log.debug("没有待执行的同步计划");
                return;
            }
            
            log.info("发现 {} 个待执行的同步计划", pendingSchedules.size());
            
            for (DataSyncScheduleDO schedule : pendingSchedules) {
                executeSchedule(schedule);
            }
            
        } catch (Exception e) {
            log.error("扫描待执行同步计划时发生错误", e);
        }
    }

    /**
     * 执行同步计划
     *
     * @param schedule 同步计划
     */
    private void executeSchedule(DataSyncScheduleDO schedule) {
        Long scheduleId = schedule.getId();
        
        // 检查是否已在执行
        if (runningTasks.containsKey(scheduleId)) {
            log.warn("同步计划 {} 正在执行中，跳过本次执行", scheduleId);
            return;
        }
        
        log.info("开始执行同步计划: {} - {}", scheduleId, schedule.getScheduleName());
        
        // 异步执行同步任务
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            try {
                executeScheduleInternal(schedule);
            } catch (Exception e) {
                log.error("执行同步计划 {} 时发生错误", scheduleId, e);
                handleScheduleError(schedule, e);
            } finally {
                // 移除正在执行的任务
                runningTasks.remove(scheduleId);
            }
        }, executorService);
        
        runningTasks.put(scheduleId, future);
        
        // 设置超时处理
        if (schedule.getTimeoutSeconds() != null && schedule.getTimeoutSeconds() > 0) {
            CompletableFuture.delayedExecutor(schedule.getTimeoutSeconds(), java.util.concurrent.TimeUnit.SECONDS)
                    .execute(() -> {
                        if (runningTasks.containsKey(scheduleId)) {
                            log.warn("同步计划 {} 执行超时，强制停止", scheduleId);
                            future.cancel(true);
                            runningTasks.remove(scheduleId);
                            dataSyncScheduleService.updateExecutionStatus(scheduleId, "TIMEOUT", "执行超时");
                        }
                    });
        }
    }

    /**
     * 执行同步计划内部逻辑
     *
     * @param schedule 同步计划
     */
    private void executeScheduleInternal(DataSyncScheduleDO schedule) {
        Long scheduleId = schedule.getId();
        Long configId = schedule.getConfigId();
        
        try {
            // 更新执行状态
            dataSyncScheduleService.updateExecutionStatus(scheduleId, "RUNNING", null);
            
            // 创建同步执行请求
            DataSyncExecuteReq executeReq = new DataSyncExecuteReq();
            executeReq.setSyncType("SCHEDULED");
            executeReq.setAsync(false);
            
            // 执行同步
            DataSyncResultResp result = dataSyncService.executeSync(configId, executeReq);
            
            // 处理执行结果
            handleScheduleResult(schedule, result);
            
        } catch (Exception e) {
            log.error("执行同步计划 {} 时发生错误", scheduleId, e);
            handleScheduleError(schedule, e);
            throw e;
        }
    }

    /**
     * 处理同步计划执行结果
     *
     * @param schedule 同步计划
     * @param result 执行结果
     */
    private void handleScheduleResult(DataSyncScheduleDO schedule, DataSyncResultResp result) {
        Long scheduleId = schedule.getId();
        
        try {
            String status = "SUCCESS".equals(result.getStatus()) ? "SUCCESS" : "FAILED";
            String errorMessage = "FAILED".equals(status) ? result.getErrorMessage() : null;
            
            // 更新执行状态
            dataSyncScheduleService.updateExecutionStatus(scheduleId, status, errorMessage);
            
            // 计算下次执行时间
            LocalDateTime nextExecutionTime = dataSyncScheduleService.calculateNextExecutionTime(schedule, LocalDateTime.now());
            if (nextExecutionTime != null) {
                dataSyncScheduleService.updateNextExecutionTime(scheduleId, nextExecutionTime);
            }
            
            log.info("同步计划 {} 执行完成，状态: {}, 处理记录数: {}, 成功: {}, 失败: {}", 
                    scheduleId, status, result.getTotalCount(), result.getSuccessCount(), result.getFailedCount());
            
            // 发送告警通知（如果需要）
            if ("FAILED".equals(status)) {
                dataSyncScheduleService.sendAlert(scheduleId, "EXECUTION_FAILED", errorMessage);
            }
            
        } catch (Exception e) {
            log.error("处理同步计划 {} 执行结果时发生错误", scheduleId, e);
        }
    }

    /**
     * 处理同步计划执行错误
     *
     * @param schedule 同步计划
     * @param error 错误信息
     */
    private void handleScheduleError(DataSyncScheduleDO schedule, Exception error) {
        Long scheduleId = schedule.getId();
        
        try {
            String errorMessage = error.getMessage();
            if (StrUtil.isBlank(errorMessage)) {
                errorMessage = error.getClass().getSimpleName();
            }
            
            // 更新执行状态
            dataSyncScheduleService.updateExecutionStatus(scheduleId, "FAILED", errorMessage);
            
            // 检查是否需要重试
            if (shouldRetry(schedule)) {
                scheduleRetry(schedule);
            } else {
                // 计算下次执行时间
                LocalDateTime nextExecutionTime = dataSyncScheduleService.calculateNextExecutionTime(schedule, LocalDateTime.now());
                if (nextExecutionTime != null) {
                    dataSyncScheduleService.updateNextExecutionTime(scheduleId, nextExecutionTime);
                }
            }
            
            // 发送告警通知
            dataSyncScheduleService.sendAlert(scheduleId, "EXECUTION_ERROR", errorMessage);
            
        } catch (Exception e) {
            log.error("处理同步计划 {} 执行错误时发生异常", scheduleId, e);
        }
    }

    /**
     * 判断是否需要重试
     *
     * @param schedule 同步计划
     * @return 是否需要重试
     */
    private boolean shouldRetry(DataSyncScheduleDO schedule) {
        if (schedule.getMaxRetryCount() == null || schedule.getMaxRetryCount() <= 0) {
            return false;
        }
        
        // 这里可以添加更复杂的重试逻辑，比如检查当前重试次数
        // 暂时简化处理
        return schedule.getFailureCount() < schedule.getMaxRetryCount();
    }

    /**
     * 安排重试
     *
     * @param schedule 同步计划
     */
    private void scheduleRetry(DataSyncScheduleDO schedule) {
        Long scheduleId = schedule.getId();
        
        try {
            Integer retryIntervalSeconds = schedule.getRetryIntervalSeconds();
            if (retryIntervalSeconds == null || retryIntervalSeconds <= 0) {
                retryIntervalSeconds = 300; // 默认5分钟后重试
            }
            
            LocalDateTime retryTime = LocalDateTime.now().plusSeconds(retryIntervalSeconds);
            dataSyncScheduleService.updateNextExecutionTime(scheduleId, retryTime);
            
            log.info("同步计划 {} 将在 {} 后重试", scheduleId, DateUtil.formatDateTime(DateUtil.date()));
            
        } catch (Exception e) {
            log.error("安排同步计划 {} 重试时发生错误", scheduleId, e);
        }
    }

    /**
     * 清理超时任务
     * 每小时执行一次
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void cleanupTimeoutTasks() {
        try {
            log.debug("开始清理超时任务");
            
            runningTasks.entrySet().removeIf(entry -> {
                CompletableFuture<Void> future = entry.getValue();
                if (future.isDone() || future.isCancelled()) {
                    log.debug("清理已完成的任务: {}", entry.getKey());
                    return true;
                }
                return false;
            });
            
            log.debug("清理超时任务完成，当前运行任务数: {}", runningTasks.size());
            
        } catch (Exception e) {
            log.error("清理超时任务时发生错误", e);
        }
    }

    /**
     * 健康检查
     * 每10分钟执行一次
     */
    @Scheduled(cron = "0 */10 * * * ?")
    public void healthCheck() {
        try {
            log.debug("开始执行健康检查");
            
            // 检查失败的计划
            List<Map<String, Object>> failedSchedules = dataSyncScheduleService.getFailedSchedules(null, 24);
            if (CollUtil.isNotEmpty(failedSchedules)) {
                log.warn("发现 {} 个失败的同步计划", failedSchedules.size());
                
                for (Map<String, Object> failedSchedule : failedSchedules) {
                    Long scheduleId = (Long) failedSchedule.get("id");
                    String errorMessage = (String) failedSchedule.get("lastErrorMessage");
                    dataSyncScheduleService.sendAlert(scheduleId, "HEALTH_CHECK_FAILED", errorMessage);
                }
            }
            
            // 检查超时的计划
            List<Map<String, Object>> timeoutSchedules = dataSyncScheduleService.getTimeoutSchedules(null);
            if (CollUtil.isNotEmpty(timeoutSchedules)) {
                log.warn("发现 {} 个超时的同步计划", timeoutSchedules.size());
                
                for (Map<String, Object> timeoutSchedule : timeoutSchedules) {
                    Long scheduleId = (Long) timeoutSchedule.get("id");
                    dataSyncScheduleService.sendAlert(scheduleId, "HEALTH_CHECK_TIMEOUT", "同步计划执行超时");
                }
            }
            
            log.debug("健康检查完成");
            
        } catch (Exception e) {
            log.error("执行健康检查时发生错误", e);
        }
    }

    /**
     * 获取正在运行的任务数量
     *
     * @return 正在运行的任务数量
     */
    public int getRunningTaskCount() {
        return runningTasks.size();
    }

    /**
     * 停止指定的同步计划
     *
     * @param scheduleId 计划ID
     * @return 是否成功停止
     */
    public boolean stopSchedule(Long scheduleId) {
        CompletableFuture<Void> future = runningTasks.get(scheduleId);
        if (future != null) {
            boolean cancelled = future.cancel(true);
            if (cancelled) {
                runningTasks.remove(scheduleId);
                dataSyncScheduleService.updateExecutionStatus(scheduleId, "STOPPED", "手动停止");
                log.info("同步计划 {} 已被手动停止", scheduleId);
            }
            return cancelled;
        }
        return false;
    }

}
