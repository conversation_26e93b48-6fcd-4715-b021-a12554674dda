package top.continew.admin.accounting.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.accounting.model.req.ReportTemplateCreateReq;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 报表模板响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "报表模板响应")
public class ReportTemplateResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模板ID
     */
    @Schema(description = "模板ID", example = "1")
    private Long templateId;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 群组名称
     */
    @Schema(description = "群组名称", example = "开发团队")
    private String groupName;

    /**
     * 模板名称
     */
    @Schema(description = "模板名称", example = "月度财务报表")
    private String templateName;

    /**
     * 模板描述
     */
    @Schema(description = "模板描述", example = "用于生成月度收支统计报表")
    private String templateDescription;

    /**
     * 模板类型
     */
    @Schema(description = "模板类型", example = "FINANCIAL_OVERVIEW")
    private String templateType;

    /**
     * 模板类型名称
     */
    @Schema(description = "模板类型名称", example = "财务概览")
    private String templateTypeName;

    /**
     * 模板状态
     */
    @Schema(description = "模板状态", example = "ACTIVE")
    private String templateStatus;

    /**
     * 模板状态名称
     */
    @Schema(description = "模板状态名称", example = "活跃")
    private String templateStatusName;

    /**
     * 版本号
     */
    @Schema(description = "版本号", example = "1.0")
    private String version;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    /**
     * 是否公开
     */
    @Schema(description = "是否公开", example = "false")
    private Boolean isPublic;

    /**
     * 是否为系统模板
     */
    @Schema(description = "是否为系统模板", example = "false")
    private Boolean isSystemTemplate;

    /**
     * 标签列表
     */
    @Schema(description = "标签列表", example = "[\"财务\", \"月报\"]")
    private List<String> tags;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID", example = "1")
    private Long createdBy;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名", example = "张三")
    private String createdByName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-01-01 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID", example = "1")
    private Long updatedBy;

    /**
     * 更新人姓名
     */
    @Schema(description = "更新人姓名", example = "李四")
    private String updatedByName;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2025-01-01 15:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 使用次数
     */
    @Schema(description = "使用次数", example = "25")
    private Integer usageCount;

    /**
     * 最后使用时间
     */
    @Schema(description = "最后使用时间", example = "2025-01-01 14:20:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastUsedTime;

    /**
     * 最后使用人ID
     */
    @Schema(description = "最后使用人ID", example = "2")
    private Long lastUsedBy;

    /**
     * 最后使用人姓名
     */
    @Schema(description = "最后使用人姓名", example = "王五")
    private String lastUsedByName;

    /**
     * 平均执行时间（毫秒）
     */
    @Schema(description = "平均执行时间", example = "2500")
    private Long avgExecutionTime;

    /**
     * 最大执行时间（毫秒）
     */
    @Schema(description = "最大执行时间", example = "5000")
    private Long maxExecutionTime;

    /**
     * 最小执行时间（毫秒）
     */
    @Schema(description = "最小执行时间", example = "1000")
    private Long minExecutionTime;

    /**
     * 成功执行次数
     */
    @Schema(description = "成功执行次数", example = "23")
    private Integer successCount;

    /**
     * 失败执行次数
     */
    @Schema(description = "失败执行次数", example = "2")
    private Integer failureCount;

    /**
     * 错误率（百分比）
     */
    @Schema(description = "错误率", example = "8.0")
    private Double errorRate;

    /**
     * 用户评分
     */
    @Schema(description = "用户评分", example = "4.5")
    private Double rating;

    /**
     * 评分人数
     */
    @Schema(description = "评分人数", example = "10")
    private Integer ratingCount;

    /**
     * 收藏次数
     */
    @Schema(description = "收藏次数", example = "5")
    private Integer favoriteCount;

    /**
     * 当前用户是否收藏
     */
    @Schema(description = "当前用户是否收藏", example = "true")
    private Boolean isFavorite;

    /**
     * 模板复杂度
     */
    @Schema(description = "模板复杂度", example = "MEDIUM")
    private String complexity;

    /**
     * 模板复杂度名称
     */
    @Schema(description = "模板复杂度名称", example = "中等")
    private String complexityName;

    /**
     * 数据量统计
     */
    @Schema(description = "数据量统计")
    private DataVolumeStats dataVolumeStats;

    /**
     * 调度配置信息
     */
    @Schema(description = "调度配置信息")
    private ScheduleInfo scheduleInfo;

    /**
     * 权限信息
     */
    @Schema(description = "权限信息")
    private PermissionInfo permissionInfo;

    /**
     * 支持的导出格式
     */
    @Schema(description = "支持的导出格式", example = "[\"PDF\", \"EXCEL\", \"CSV\"]")
    private List<String> supportedFormats;

    /**
     * 图表类型统计
     */
    @Schema(description = "图表类型统计")
    private Map<String, Integer> chartTypeStats;

    /**
     * 组件数量统计
     */
    @Schema(description = "组件数量统计")
    private ComponentStats componentStats;

    /**
     * 扩展属性
     */
    @Schema(description = "扩展属性")
    private Map<String, Object> attributes;

    /**
     * 最近执行记录
     */
    @Schema(description = "最近执行记录")
    private List<ExecutionRecord> recentExecutions;

    /**
     * 数据量统计
     */
    @Data
    @Schema(description = "数据量统计")
    public static class DataVolumeStats implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 平均数据量
         */
        @Schema(description = "平均数据量", example = "1500")
        private Integer avgDataVolume;

        /**
         * 最大数据量
         */
        @Schema(description = "最大数据量", example = "5000")
        private Integer maxDataVolume;

        /**
         * 最小数据量
         */
        @Schema(description = "最小数据量", example = "100")
        private Integer minDataVolume;

        /**
         * 最近30天平均数据量
         */
        @Schema(description = "最近30天平均数据量", example = "1800")
        private Integer recent30DaysAvgVolume;
    }

    /**
     * 调度配置信息
     */
    @Data
    @Schema(description = "调度配置信息")
    public static class ScheduleInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 是否启用调度
         */
        @Schema(description = "是否启用调度", example = "true")
        private Boolean enabled;

        /**
         * 调度类型
         */
        @Schema(description = "调度类型", example = "CRON")
        private String scheduleType;

        /**
         * 调度表达式
         */
        @Schema(description = "调度表达式", example = "0 0 9 1 * ?")
        private String scheduleExpression;

        /**
         * 下次执行时间
         */
        @Schema(description = "下次执行时间", example = "2025-02-01 09:00:00")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime nextExecutionTime;

        /**
         * 最后执行时间
         */
        @Schema(description = "最后执行时间", example = "2025-01-01 09:00:00")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime lastExecutionTime;

        /**
         * 调度状态
         */
        @Schema(description = "调度状态", example = "ACTIVE")
        private String scheduleStatus;

        /**
         * 调度状态名称
         */
        @Schema(description = "调度状态名称", example = "运行中")
        private String scheduleStatusName;

        /**
         * 接收人数量
         */
        @Schema(description = "接收人数量", example = "3")
        private Integer recipientCount;
    }

    /**
     * 权限信息
     */
    @Data
    @Schema(description = "权限信息")
    public static class PermissionInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 当前用户是否可查看
         */
        @Schema(description = "当前用户是否可查看", example = "true")
        private Boolean canView;

        /**
         * 当前用户是否可编辑
         */
        @Schema(description = "当前用户是否可编辑", example = "true")
        private Boolean canEdit;

        /**
         * 当前用户是否可执行
         */
        @Schema(description = "当前用户是否可执行", example = "true")
        private Boolean canExecute;

        /**
         * 当前用户是否可删除
         */
        @Schema(description = "当前用户是否可删除", example = "false")
        private Boolean canDelete;

        /**
         * 数据权限范围
         */
        @Schema(description = "数据权限范围", example = "GROUP")
        private String dataScope;

        /**
         * 数据权限范围名称
         */
        @Schema(description = "数据权限范围名称", example = "群组")
        private String dataScopeName;

        /**
         * 是否启用数据脱敏
         */
        @Schema(description = "是否启用数据脱敏", example = "false")
        private Boolean dataMasking;
    }

    /**
     * 组件数量统计
     */
    @Data
    @Schema(description = "组件数量统计")
    public static class ComponentStats implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 总组件数
         */
        @Schema(description = "总组件数", example = "8")
        private Integer totalComponents;

        /**
         * 卡片组件数
         */
        @Schema(description = "卡片组件数", example = "3")
        private Integer cardComponents;

        /**
         * 表格组件数
         */
        @Schema(description = "表格组件数", example = "2")
        private Integer tableComponents;

        /**
         * 图表组件数
         */
        @Schema(description = "图表组件数", example = "3")
        private Integer chartComponents;

        /**
         * 文本组件数
         */
        @Schema(description = "文本组件数", example = "0")
        private Integer textComponents;

        /**
         * 其他组件数
         */
        @Schema(description = "其他组件数", example = "0")
        private Integer otherComponents;
    }

    /**
     * 执行记录
     */
    @Data
    @Schema(description = "执行记录")
    public static class ExecutionRecord implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 执行时间
         */
        @Schema(description = "执行时间", example = "2025-01-01 14:20:00")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime executionTime;

        /**
         * 执行人ID
         */
        @Schema(description = "执行人ID", example = "1")
        private Long executedBy;

        /**
         * 执行人姓名
         */
        @Schema(description = "执行人姓名", example = "张三")
        private String executedByName;

        /**
         * 执行状态
         */
        @Schema(description = "执行状态", example = "SUCCESS")
        private String executionStatus;

        /**
         * 执行状态名称
         */
        @Schema(description = "执行状态名称", example = "成功")
        private String executionStatusName;

        /**
         * 执行耗时（毫秒）
         */
        @Schema(description = "执行耗时", example = "2500")
        private Long executionDuration;

        /**
         * 数据量
         */
        @Schema(description = "数据量", example = "1500")
        private Integer dataVolume;

        /**
         * 导出格式
         */
        @Schema(description = "导出格式", example = "PDF")
        private String exportFormat;

        /**
         * 文件大小（字节）
         */
        @Schema(description = "文件大小", example = "1048576")
        private Long fileSize;

        /**
         * 错误信息
         */
        @Schema(description = "错误信息")
        private String errorMessage;
    }
}
