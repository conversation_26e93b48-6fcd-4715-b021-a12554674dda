package top.continew.admin.bot.telegram.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.meta.api.objects.Update;
import org.telegram.telegrambots.meta.api.objects.replykeyboard.InlineKeyboardMarkup;
import top.continew.admin.accounting.enums.PlatformType;
import top.continew.admin.accounting.model.entity.GroupDO;
import top.continew.admin.accounting.model.req.TransactionCreateReq;
import top.continew.admin.accounting.service.GroupService;
import top.continew.admin.accounting.service.TransactionService;
import top.continew.admin.bot.model.dto.ParsedCommand;
import top.continew.admin.bot.service.CommandParser;
import top.continew.admin.bot.telegram.TelegramBotService;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Telegram命令处理器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TelegramCommandHandler {

    private final CommandParser commandParser;
    private final GroupService groupService;
    private final TransactionService transactionService;
    private final TelegramBotService telegramBotService;

    /**
     * 处理命令
     */
    public void handleCommand(String chatId, Long userId, String username, String messageText, Update update) {
        try {
            // 检查是否为系统命令
            if (messageText.startsWith("/")) {
                handleSystemCommand(chatId, userId, username, messageText, update);
                return;
            }

            // 解析记账命令
            ParsedCommand command = commandParser.parseCommand(messageText);
            if (command == null || !command.getValid()) {
                String errorMsg = command != null ? command.getErrorMessage() : "无法解析命令";
                telegramBotService.sendMessage(chatId, "❌ " + errorMsg + "\n\n" + getQuickHelp());
                return;
            }

            // 验证群组
            GroupDO group = groupService.findByPlatformInfo(PlatformType.TELEGRAM, chatId);
            if (group == null) {
                sendGroupNotRegisteredMessage(chatId);
                return;
            }

            // 验证用户权限
            if (!groupService.isMember(group.getId(), userId)) {
                telegramBotService.sendMessage(chatId, "❌ 您不是该群组成员，无法记账");
                return;
            }

            // 创建交易记录
            createTransaction(chatId, userId, username, command, group);

        } catch (Exception e) {
            log.error("处理Telegram命令失败: {}", messageText, e);
            telegramBotService.sendMessage(chatId, "❌ 处理命令时发生错误: " + e.getMessage());
        }
    }

    /**
     * 处理系统命令
     */
    private void handleSystemCommand(String chatId, Long userId, String username, String messageText, Update update) {
        String command = messageText.split(" ")[0].toLowerCase();
        
        switch (command) {
            case "/start" -> handleStartCommand(chatId, userId, username);
            case "/help" -> handleHelpCommand(chatId);
            case "/register" -> handleRegisterCommand(chatId, userId, username, messageText);
            case "/balance" -> handleBalanceCommand(chatId, userId);
            case "/history" -> handleHistoryCommand(chatId, userId, messageText);
            case "/stats" -> handleStatsCommand(chatId, userId);
            case "/settings" -> handleSettingsCommand(chatId, userId);
            case "/export" -> handleExportCommand(chatId, userId);
            default -> telegramBotService.sendMessage(chatId, "❌ 未知命令: " + command + "\n\n" + getQuickHelp());
        }
    }

    /**
     * 处理回调查询
     */
    public void handleCallback(String chatId, Long userId, String username, String callbackData, Update update) {
        try {
            String[] parts = callbackData.split(":");
            String action = parts[0];
            
            switch (action) {
                case "confirm_transaction" -> confirmTransaction(chatId, userId, parts[1], update);
                case "cancel_transaction" -> cancelTransaction(chatId, userId, parts[1], update);
                case "edit_transaction" -> editTransaction(chatId, userId, parts[1], update);
                case "delete_transaction" -> deleteTransaction(chatId, userId, parts[1], update);
                case "view_details" -> viewTransactionDetails(chatId, userId, parts[1]);
                case "register_group" -> registerGroup(chatId, userId, username);
                default -> telegramBotService.sendMessage(chatId, "❌ 未知操作: " + action);
            }
        } catch (Exception e) {
            log.error("处理Telegram回调失败: {}", callbackData, e);
            telegramBotService.sendMessage(chatId, "❌ 处理操作时发生错误: " + e.getMessage());
        }
    }

    /**
     * 处理内联查询
     */
    public void handleInlineQuery(Long userId, String username, String query, Update update) {
        // TODO: 实现内联查询处理
        log.info("处理内联查询: 用户={}, 查询={}", username, query);
    }

    /**
     * 处理开始命令
     */
    private void handleStartCommand(String chatId, Long userId, String username) {
        String welcomeMessage = """
            🎉 欢迎使用ContiNew记账机器人！
            
            我可以帮助您：
            • 📝 快速记录收支
            • 📊 查看账单统计
            • 💰 管理群组财务
            • 📈 生成财务报表
            
            开始使用前，请先注册群组：
            /register 群组名称
            
            或者查看帮助：
            /help
            """;
        
        List<List<org.telegram.telegrambots.meta.api.objects.replykeyboard.buttons.InlineKeyboardButton>> keyboard = new ArrayList<>();
        List<org.telegram.telegrambots.meta.api.objects.replykeyboard.buttons.InlineKeyboardButton> row1 = new ArrayList<>();
        row1.add(telegramBotService.createInlineButton("📝 注册群组", "register_group"));
        row1.add(telegramBotService.createInlineButton("❓ 查看帮助", "help"));
        keyboard.add(row1);
        
        telegramBotService.sendMessageWithKeyboard(chatId, welcomeMessage, telegramBotService.createInlineKeyboard(keyboard));
    }

    /**
     * 处理帮助命令
     */
    private void handleHelpCommand(String chatId) {
        telegramBotService.sendMessage(chatId, commandParser.getHelpMessage());
    }

    /**
     * 处理注册命令
     */
    private void handleRegisterCommand(String chatId, Long userId, String username, String messageText) {
        String[] parts = messageText.split(" ", 2);
        if (parts.length < 2) {
            telegramBotService.sendMessage(chatId, "❌ 请提供群组名称\n\n用法: /register 群组名称");
            return;
        }
        
        String groupName = parts[1];
        
        // 检查群组是否已注册
        GroupDO existingGroup = groupService.findByPlatformInfo(PlatformType.TELEGRAM, chatId);
        if (existingGroup != null) {
            telegramBotService.sendMessage(chatId, "❌ 该群组已注册，名称: " + existingGroup.getName());
            return;
        }
        
        // TODO: 实现群组注册逻辑
        telegramBotService.sendMessage(chatId, "✅ 群组注册成功: " + groupName + "\n\n现在可以开始记账了！");
    }

    /**
     * 处理余额命令
     */
    private void handleBalanceCommand(String chatId, Long userId) {
        // TODO: 实现余额查询逻辑
        telegramBotService.sendMessage(chatId, "💰 余额查询功能开发中...");
    }

    /**
     * 处理历史命令
     */
    private void handleHistoryCommand(String chatId, Long userId, String messageText) {
        // TODO: 实现历史记录查询逻辑
        telegramBotService.sendMessage(chatId, "📋 历史记录功能开发中...");
    }

    /**
     * 处理统计命令
     */
    private void handleStatsCommand(String chatId, Long userId) {
        // TODO: 实现统计功能
        telegramBotService.sendMessage(chatId, "📊 统计功能开发中...");
    }

    /**
     * 处理设置命令
     */
    private void handleSettingsCommand(String chatId, Long userId) {
        // TODO: 实现设置功能
        telegramBotService.sendMessage(chatId, "⚙️ 设置功能开发中...");
    }

    /**
     * 处理导出命令
     */
    private void handleExportCommand(String chatId, Long userId) {
        // TODO: 实现导出功能
        telegramBotService.sendMessage(chatId, "📤 导出功能开发中...");
    }

    /**
     * 创建交易记录
     */
    private void createTransaction(String chatId, Long userId, String username, ParsedCommand command, GroupDO group) {
        try {
            // 构建交易创建请求
            TransactionCreateReq createReq = new TransactionCreateReq();
            createReq.setGroupId(group.getId());
            createReq.setUserId(userId);
            createReq.setType(command.getType());
            createReq.setAmount(command.getAmount());
            createReq.setCurrency(command.getCurrency());
            createReq.setDescription(command.getDescription());
            createReq.setTransactionTime(command.getTransactionTime());
            createReq.setTags(command.getTags());
            
            // TODO: 处理分类、分摊等逻辑
            
            // 创建交易
            Long transactionId = transactionService.create(createReq);
            
            // 发送确认消息
            sendTransactionConfirmation(chatId, transactionId, command);
            
        } catch (Exception e) {
            log.error("创建交易记录失败", e);
            telegramBotService.sendMessage(chatId, "❌ 创建交易记录失败: " + e.getMessage());
        }
    }

    /**
     * 发送交易确认消息
     */
    private void sendTransactionConfirmation(String chatId, Long transactionId, ParsedCommand command) {
        String message = String.format("""
            ✅ 记账成功！
            
            💰 金额: %s %s %s
            📝 描述: %s
            🕐 时间: %s
            %s
            """,
            command.getType().getSymbol(),
            command.getAmount(),
            command.getCurrency(),
            command.getDescription(),
            command.getTransactionTime().toString(),
            CollUtil.isNotEmpty(command.getTags()) ? "🏷️ 标签: " + String.join(", ", command.getTags()) : ""
        );
        
        InlineKeyboardMarkup keyboard = telegramBotService.createActionKeyboard(
            "📋 查看详情", "view_details:" + transactionId,
            "✏️ 编辑", "edit_transaction:" + transactionId,
            "🗑️ 删除", "delete_transaction:" + transactionId
        );
        
        telegramBotService.sendMessageWithKeyboard(chatId, message, keyboard);
    }

    /**
     * 发送群组未注册消息
     */
    private void sendGroupNotRegisteredMessage(String chatId) {
        String message = """
            ❌ 群组未注册
            
            请先注册群组才能使用记账功能：
            /register 群组名称
            """;
        
        InlineKeyboardMarkup keyboard = telegramBotService.createActionKeyboard(
            "📝 注册群组", "register_group"
        );
        
        telegramBotService.sendMessageWithKeyboard(chatId, message, keyboard);
    }

    /**
     * 获取快速帮助
     */
    private String getQuickHelp() {
        return """
            💡 快速帮助：
            • +100 午餐 - 收入
            • -50 打车 - 支出
            • /help - 完整帮助
            """;
    }

    /**
     * 确认交易
     */
    private void confirmTransaction(String chatId, Long userId, String transactionId, Update update) {
        // TODO: 实现交易确认逻辑
        telegramBotService.editMessage(chatId, update.getCallbackQuery().getMessage().getMessageId(), 
                "✅ 交易已确认");
    }

    /**
     * 取消交易
     */
    private void cancelTransaction(String chatId, Long userId, String transactionId, Update update) {
        // TODO: 实现交易取消逻辑
        telegramBotService.editMessage(chatId, update.getCallbackQuery().getMessage().getMessageId(), 
                "❌ 交易已取消");
    }

    /**
     * 编辑交易
     */
    private void editTransaction(String chatId, Long userId, String transactionId, Update update) {
        // TODO: 实现交易编辑逻辑
        telegramBotService.sendMessage(chatId, "✏️ 交易编辑功能开发中...");
    }

    /**
     * 删除交易
     */
    private void deleteTransaction(String chatId, Long userId, String transactionId, Update update) {
        // TODO: 实现交易删除逻辑
        InlineKeyboardMarkup keyboard = telegramBotService.createConfirmKeyboard(
            "confirm_delete:" + transactionId, 
            "cancel_delete:" + transactionId
        );
        
        telegramBotService.sendMessageWithKeyboard(chatId, "⚠️ 确定要删除这笔交易吗？", keyboard);
    }

    /**
     * 查看交易详情
     */
    private void viewTransactionDetails(String chatId, Long userId, String transactionId) {
        // TODO: 实现交易详情查看逻辑
        telegramBotService.sendMessage(chatId, "📋 交易详情功能开发中...");
    }

    /**
     * 注册群组
     */
    private void registerGroup(String chatId, Long userId, String username) {
        telegramBotService.sendMessage(chatId, "📝 请发送: /register 群组名称");
    }
}
