package top.continew.admin.accounting.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.accounting.model.query.DrillDownQuery;
import top.continew.admin.accounting.model.query.MultiDimensionQuery;
import top.continew.admin.accounting.model.resp.DataCubeResp;
import top.continew.admin.accounting.model.resp.DrillDownAnalysisResp;
import top.continew.admin.accounting.model.resp.AnalysisResponseModels.*;

import java.util.List;
import java.util.Map;

/**
 * 高级数据分析 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface AdvancedAnalyticsMapper {

    /**
     * 查询多维数据
     *
     * @param query 多维查询条件
     * @return 数据矩阵
     */
    List<List<Object>> selectMultiDimensionData(@Param("query") MultiDimensionQuery query);

    /**
     * 查询钻取数据
     *
     * @param query 钻取查询条件
     * @return 钻取数据项列表
     */
    List<DrillDownAnalysisResp.DrillDataItem> selectDrillDownData(@Param("query") DrillDownQuery query);

    /**
     * 查询数据立方体单元格
     *
     * @param groupId    群组ID
     * @param dimensions 维度列表
     * @param measures   度量列表
     * @return 数据单元格列表
     */
    List<DataCubeResp.DataCell> selectDataCubeCells(@Param("groupId") Long groupId,
                                                    @Param("dimensions") List<String> dimensions,
                                                    @Param("measures") List<String> measures);

    /**
     * 查询关联规则
     *
     * @param groupId      群组ID
     * @param analysisType 分析类型
     * @return 关联规则列表
     */
    List<CorrelationAnalysisResp.CorrelationRule> selectCorrelationRules(@Param("groupId") Long groupId,
                                                                         @Param("analysisType") String analysisType);

    /**
     * 查询相关系数矩阵
     *
     * @param groupId      群组ID
     * @param analysisType 分析类型
     * @return 相关系数矩阵
     */
    Map<String, Map<String, Double>> selectCorrelationMatrix(@Param("groupId") Long groupId,
                                                             @Param("analysisType") String analysisType);

    /**
     * 查询异常项
     *
     * @param groupId       群组ID
     * @param detectionType 检测类型
     * @return 异常项列表
     */
    List<AnomalyDetectionResp.AnomalyItem> selectAnomalies(@Param("groupId") Long groupId,
                                                           @Param("detectionType") String detectionType);

    /**
     * 查询预测数据
     *
     * @param groupId        群组ID
     * @param predictionType 预测类型
     * @param periods        预测期数
     * @return 预测点列表
     */
    List<PredictionAnalysisResp.PredictionPoint> selectPredictionData(@Param("groupId") Long groupId,
                                                                      @Param("predictionType") String predictionType,
                                                                      @Param("periods") int periods);

    /**
     * 查询聚合数据
     *
     * @param groupId         群组ID
     * @param aggregationType 聚合类型
     * @param groupByFields   分组字段
     * @return 聚合结果列表
     */
    List<AggregationAnalysisResp.AggregationResult> selectAggregationData(@Param("groupId") Long groupId,
                                                                          @Param("aggregationType") String aggregationType,
                                                                          @Param("groupByFields") List<String> groupByFields);

    /**
     * 查询同期对比数据
     *
     * @param groupId     群组ID
     * @param compareType 对比类型
     * @param period      对比周期
     * @return 对比结果
     */
    List<Map<String, Object>> selectPeriodComparisonData(@Param("groupId") Long groupId,
                                                         @Param("compareType") String compareType,
                                                         @Param("period") String period);

    /**
     * 查询漏斗分析数据
     *
     * @param groupId     群组ID
     * @param funnelSteps 漏斗步骤
     * @return 漏斗数据
     */
    List<Map<String, Object>> selectFunnelAnalysisData(@Param("groupId") Long groupId,
                                                       @Param("funnelSteps") List<String> funnelSteps);

    /**
     * 查询队列分析数据
     *
     * @param groupId    群组ID
     * @param cohortType 队列类型
     * @return 队列数据
     */
    List<Map<String, Object>> selectCohortAnalysisData(@Param("groupId") Long groupId,
                                                       @Param("cohortType") String cohortType);

    /**
     * 查询实时分析数据
     *
     * @param groupId 群组ID
     * @param metrics 指标列表
     * @return 实时数据
     */
    Map<String, Object> selectRealTimeAnalysisData(@Param("groupId") Long groupId,
                                                   @Param("metrics") List<String> metrics);

    /**
     * 查询自定义分析数据
     *
     * @param groupId        群组ID
     * @param analysisConfig 分析配置
     * @return 分析结果
     */
    Map<String, Object> selectCustomAnalysisData(@Param("groupId") Long groupId,
                                                 @Param("analysisConfig") Map<String, Object> analysisConfig);

    /**
     * 查询维度层次结构
     *
     * @param groupId   群组ID
     * @param dimension 维度名称
     * @return 层次结构
     */
    List<Map<String, Object>> selectDimensionHierarchy(@Param("groupId") Long groupId,
                                                       @Param("dimension") String dimension);

    /**
     * 查询维度成员
     *
     * @param groupId   群组ID
     * @param dimension 维度名称
     * @param level     层次级别
     * @return 维度成员列表
     */
    List<Map<String, Object>> selectDimensionMembers(@Param("groupId") Long groupId,
                                                     @Param("dimension") String dimension,
                                                     @Param("level") Integer level);

    /**
     * 查询度量统计信息
     *
     * @param groupId 群组ID
     * @param measure 度量名称
     * @return 统计信息
     */
    Map<String, Object> selectMeasureStatistics(@Param("groupId") Long groupId,
                                               @Param("measure") String measure);

    /**
     * 查询数据质量信息
     *
     * @param groupId 群组ID
     * @return 数据质量信息
     */
    Map<String, Object> selectDataQualityInfo(@Param("groupId") Long groupId);

    /**
     * 查询分析配置
     *
     * @param groupId 群组ID
     * @return 分析配置列表
     */
    List<AnalysisConfigResp> selectAnalysisConfigs(@Param("groupId") Long groupId);

    /**
     * 插入分析配置
     *
     * @param config 分析配置
     * @return 影响行数
     */
    int insertAnalysisConfig(@Param("config") AnalysisConfigResp config);

    /**
     * 更新分析配置
     *
     * @param config 分析配置
     * @return 影响行数
     */
    int updateAnalysisConfig(@Param("config") AnalysisConfigResp config);

    /**
     * 删除分析配置
     *
     * @param configId 配置ID
     * @return 影响行数
     */
    int deleteAnalysisConfig(@Param("configId") String configId);

    /**
     * 查询分析历史记录
     *
     * @param groupId 群组ID
     * @param limit   限制数量
     * @return 历史记录列表
     */
    List<Map<String, Object>> selectAnalysisHistory(@Param("groupId") Long groupId,
                                                    @Param("limit") Integer limit);

    /**
     * 插入分析历史记录
     *
     * @param record 历史记录
     * @return 影响行数
     */
    int insertAnalysisHistory(@Param("record") Map<String, Object> record);

    /**
     * 查询缓存统计信息
     *
     * @return 缓存统计
     */
    Map<String, Object> selectCacheStatistics();

    /**
     * 清理过期缓存
     *
     * @param expiredTime 过期时间
     * @return 清理数量
     */
    int cleanExpiredCache(@Param("expiredTime") String expiredTime);
}
