package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 通知统计响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "通知统计响应")
public class NotificationStatisticsResp {

    @Schema(description = "总通知数量", example = "1000")
    private Long totalNotifications;

    @Schema(description = "今日通知数量", example = "50")
    private Long todayNotifications;

    @Schema(description = "成功发送数量", example = "950")
    private Long successNotifications;

    @Schema(description = "失败发送数量", example = "50")
    private Long failedNotifications;

    @Schema(description = "待发送数量", example = "20")
    private Long pendingNotifications;

    @Schema(description = "成功率", example = "95.0")
    private BigDecimal successRate;

    @Schema(description = "平均发送耗时（毫秒）", example = "1200")
    private BigDecimal avgDuration;

    @Schema(description = "各渠道统计")
    private Map<String, ChannelStatistics> channelStatistics;

    @Schema(description = "各类型统计")
    private Map<String, TypeStatistics> typeStatistics;

    @Schema(description = "发送趋势数据")
    private List<TrendData> trendData;

    @Schema(description = "热门模板")
    private List<TemplateUsage> popularTemplates;

    /**
     * 渠道统计
     */
    @Data
    @Schema(description = "渠道统计")
    public static class ChannelStatistics {
        @Schema(description = "渠道名称")
        private String channelName;

        @Schema(description = "发送数量")
        private Long sendCount;

        @Schema(description = "成功数量")
        private Long successCount;

        @Schema(description = "失败数量")
        private Long failedCount;

        @Schema(description = "成功率")
        private BigDecimal successRate;

        @Schema(description = "平均耗时")
        private BigDecimal avgDuration;
    }

    /**
     * 类型统计
     */
    @Data
    @Schema(description = "类型统计")
    public static class TypeStatistics {
        @Schema(description = "类型名称")
        private String typeName;

        @Schema(description = "发送数量")
        private Long sendCount;

        @Schema(description = "成功数量")
        private Long successCount;

        @Schema(description = "失败数量")
        private Long failedCount;

        @Schema(description = "成功率")
        private BigDecimal successRate;
    }

    /**
     * 趋势数据
     */
    @Data
    @Schema(description = "趋势数据")
    public static class TrendData {
        @Schema(description = "时间点")
        private String timePoint;

        @Schema(description = "发送数量")
        private Long sendCount;

        @Schema(description = "成功数量")
        private Long successCount;

        @Schema(description = "失败数量")
        private Long failedCount;
    }

    /**
     * 模板使用情况
     */
    @Data
    @Schema(description = "模板使用情况")
    public static class TemplateUsage {
        @Schema(description = "模板ID")
        private Long templateId;

        @Schema(description = "模板名称")
        private String templateName;

        @Schema(description = "使用次数")
        private Long usageCount;

        @Schema(description = "成功率")
        private BigDecimal successRate;
    }

}
