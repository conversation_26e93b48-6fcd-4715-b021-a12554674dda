name: Scan

on:
  push:
    branches:
      - dev
  pull_request:
    branches:
      - dev

jobs:
  sonar-scan:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        jdk-version:
          - 17
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: "adopt"
          java-version: ${{ matrix.jdk-version }}
          cache: "maven"
      - name: Cache SonarCloud packages
        uses: actions/cache@v3
        with:
          path: ~/.sonar/cache
          key: ${{ runner.os }}-sonar
          restore-keys: ${{ runner.os }}-sonar
      - name: Cache Maven packages
        uses: actions/cache@v3
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: ${{ runner.os }}-m2
      - name: Analyze
        run: |
          sed -i.bak '/<repositories>/,/<\/repositories>/d' pom.xml
          sed -i.bak '/<pluginRepositories>/,/<\/pluginRepositories>/d' pom.xml
          mvn -B verify -Psonar
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }} # Needed to get PR information, if any
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
