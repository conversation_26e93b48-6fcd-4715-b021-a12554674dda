package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 发送通知响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "发送通知响应")
public class NotificationSendResp {

    @Schema(description = "通知ID", example = "1")
    private Long notificationId;

    @Schema(description = "发送状态", example = "SENT")
    private String status;

    @Schema(description = "发送时间", example = "2025-01-01T10:00:00")
    private LocalDateTime sendTime;

    @Schema(description = "成功渠道列表")
    private List<String> successChannels;

    @Schema(description = "失败渠道列表")
    private List<String> failedChannels;

    @Schema(description = "渠道发送结果")
    private Map<String, Object> channelResults;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "总发送数量", example = "10")
    private Integer totalCount;

    @Schema(description = "成功发送数量", example = "8")
    private Integer successCount;

    @Schema(description = "失败发送数量", example = "2")
    private Integer failedCount;

    @Schema(description = "发送耗时（毫秒）", example = "1500")
    private Long duration;

    @Schema(description = "是否需要重试", example = "false")
    private Boolean needRetry;

    @Schema(description = "下次重试时间", example = "2025-01-01T10:05:00")
    private LocalDateTime nextRetryTime;

}
