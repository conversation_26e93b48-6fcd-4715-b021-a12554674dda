package top.continew.admin.accounting.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.base.IBaseEnum;

/**
 * 平台类型枚举
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Getter
@RequiredArgsConstructor
public enum PlatformType implements IBaseEnum<String> {

    /**
     * Telegram
     */
    TELEGRAM("TELEGRAM", "Telegram"),

    /**
     * Discord
     */
    DISCORD("DISCORD", "Discord"),

    /**
     * 微信
     */
    WECHAT("WECHAT", "微信"),

    /**
     * QQ
     */
    QQ("QQ", "QQ"),

    /**
     * 钉钉
     */
    DINGTALK("DINGTALK", "钉钉"),

    /**
     * 飞书
     */
    FEISHU("FEISHU", "飞书");

    private final String value;
    private final String description;
}
