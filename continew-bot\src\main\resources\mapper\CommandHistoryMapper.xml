<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.bot.mapper.CommandHistoryMapper">

    <!-- 查询用户命令历史 -->
    <select id="selectUserHistory" resultType="top.continew.admin.bot.model.entity.CommandHistoryDO">
        SELECT *
        FROM bot_command_history
        WHERE user_id = #{userId}
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        ORDER BY execution_time DESC
        LIMIT #{limit}
    </select>

    <!-- 查询群组命令历史 -->
    <select id="selectGroupHistory" resultType="top.continew.admin.bot.model.entity.CommandHistoryDO">
        SELECT *
        FROM bot_command_history
        WHERE group_id = #{groupId}
        ORDER BY execution_time DESC
        LIMIT #{limit}
    </select>

    <!-- 查询命令统计信息 -->
    <select id="selectCommandStatistics" resultType="map">
        SELECT 
            COUNT(*) as total_commands,
            COUNT(CASE WHEN success = 1 THEN 1 END) as successful_commands,
            COUNT(CASE WHEN success = 0 THEN 1 END) as failed_commands,
            AVG(execution_duration) as avg_execution_time,
            AVG(confidence_score) as avg_confidence_score,
            COUNT(DISTINCT platform) as platforms_used,
            COUNT(DISTINCT DATE(execution_time)) as active_days
        FROM bot_command_history
        WHERE 1=1
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        <if test="startTime != null">
            AND execution_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND execution_time <= #{endTime}
        </if>
    </select>

    <!-- 查询最常用的命令 -->
    <select id="selectMostUsedCommands" resultType="string">
        SELECT original_command
        FROM bot_command_history
        WHERE 1=1
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        AND success = 1
        GROUP BY original_command
        ORDER BY COUNT(*) DESC
        LIMIT #{limit}
    </select>

    <!-- 查询失败的命令 -->
    <select id="selectFailedCommands" resultType="top.continew.admin.bot.model.entity.CommandHistoryDO">
        SELECT *
        FROM bot_command_history
        WHERE success = 0
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        ORDER BY execution_time DESC
        LIMIT #{limit}
    </select>

    <!-- 删除过期历史记录 -->
    <delete id="deleteExpiredHistory">
        DELETE FROM bot_command_history
        WHERE execution_time < #{cutoffTime}
    </delete>

    <!-- 按时间范围查询历史记录 -->
    <select id="selectHistoryByTimeRange" resultType="top.continew.admin.bot.model.entity.CommandHistoryDO">
        SELECT *
        FROM bot_command_history
        WHERE execution_time BETWEEN #{startTime} AND #{endTime}
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        ORDER BY execution_time DESC
    </select>

</mapper>
