#!/bin/bash

echo "========================================"
echo "ContiNew Admin 记账模块测试执行脚本"
echo "========================================"

echo ""
echo "1. 清理之前的构建结果..."
mvn clean

echo ""
echo "2. 编译项目..."
mvn compile test-compile

echo ""
echo "3. 运行单元测试..."
mvn test

echo ""
echo "4. 运行集成测试..."
mvn failsafe:integration-test failsafe:verify

echo ""
echo "5. 生成测试覆盖率报告..."
mvn jacoco:report

echo ""
echo "6. 检查测试覆盖率..."
mvn jacoco:check

echo ""
echo "========================================"
echo "测试执行完成！"
echo "========================================"
echo ""
echo "测试报告位置："
echo "- 单元测试报告: target/surefire-reports/"
echo "- 集成测试报告: target/failsafe-reports/"
echo "- 覆盖率报告: target/site/jacoco/"
echo ""
