-- Zapier集成相关数据表

-- Zapier配置表
CREATE TABLE `acc_zapier_config` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `group_id` bigint NOT NULL COMMENT '群组ID',
    `name` varchar(100) NOT NULL COMMENT '配置名称',
    `description` varchar(500) DEFAULT NULL COMMENT '配置描述',
    `webhook_url` varchar(1000) NOT NULL COMMENT 'Webhook URL',
    `trigger_type` varchar(50) NOT NULL COMMENT '触发器类型(TRANSACTION_CREATED/TRANSACTION_UPDATED/TRANSACTION_DELETED/WALLET_UPDATED/REPORT_GENERATED)',
    `trigger_conditions` json DEFAULT NULL COMMENT '触发条件(JSON格式)',
    `data_mapping` json DEFAULT NULL COMMENT '数据映射规则(JSON格式)',
    `filter_rules` json DEFAULT NULL COMMENT '过滤规则(JSON格式)',
    `headers` json DEFAULT NULL COMMENT '自定义请求头(JSON格式)',
    `timeout_seconds` int DEFAULT 30 COMMENT '超时时间(秒)',
    `max_retries` int DEFAULT 3 COMMENT '最大重试次数',
    `retry_interval` int DEFAULT 5 COMMENT '重试间隔(分钟)',
    `enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用(0:禁用 1:启用)',
    `status` varchar(20) DEFAULT 'ACTIVE' COMMENT '状态(ACTIVE:活跃 INACTIVE:非活跃 ERROR:错误 DISABLED:已禁用)',
    `trigger_count` bigint DEFAULT 0 COMMENT '触发次数',
    `success_count` bigint DEFAULT 0 COMMENT '成功次数',
    `failure_count` bigint DEFAULT 0 COMMENT '失败次数',
    `last_triggered_at` datetime DEFAULT NULL COMMENT '最后触发时间',
    `last_error` text DEFAULT NULL COMMENT '最后错误信息',
    `last_error_at` datetime DEFAULT NULL COMMENT '最后错误时间',
    `retry_count` int DEFAULT 0 COMMENT '当前重试次数',
    `tags` json DEFAULT NULL COMMENT '标签(JSON数组)',
    `priority` int DEFAULT 0 COMMENT '优先级(数值越大优先级越高)',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_group_id` (`group_id`),
    KEY `idx_trigger_type` (`trigger_type`),
    KEY `idx_enabled_status` (`enabled`, `status`),
    KEY `idx_last_triggered` (`last_triggered_at`),
    KEY `idx_priority` (`priority`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Zapier配置表';

-- Zapier执行日志表
CREATE TABLE `acc_zapier_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `config_id` bigint NOT NULL COMMENT '配置ID',
    `group_id` bigint NOT NULL COMMENT '群组ID',
    `trigger_type` varchar(50) NOT NULL COMMENT '触发器类型',
    `event_type` varchar(50) NOT NULL COMMENT '事件类型(CREATE/UPDATE/DELETE/SYNC)',
    `business_id` bigint DEFAULT NULL COMMENT '业务ID',
    `business_type` varchar(50) DEFAULT NULL COMMENT '业务类型(TRANSACTION/WALLET/REPORT/USER/GROUP)',
    `request_data` json DEFAULT NULL COMMENT '请求数据(JSON格式)',
    `response_data` json DEFAULT NULL COMMENT '响应数据(JSON格式)',
    `request_headers` json DEFAULT NULL COMMENT '请求头(JSON格式)',
    `response_headers` json DEFAULT NULL COMMENT '响应头(JSON格式)',
    `http_status` int DEFAULT NULL COMMENT 'HTTP状态码',
    `status` varchar(20) NOT NULL COMMENT '执行状态(SUCCESS:成功 FAILED:失败 TIMEOUT:超时 RETRY:重试中)',
    `executed_at` datetime NOT NULL COMMENT '执行时间',
    `execution_time` bigint DEFAULT NULL COMMENT '执行耗时(毫秒)',
    `request_size` bigint DEFAULT NULL COMMENT '请求大小(字节)',
    `response_size` bigint DEFAULT NULL COMMENT '响应大小(字节)',
    `error_message` text DEFAULT NULL COMMENT '错误信息',
    `error_code` varchar(50) DEFAULT NULL COMMENT '错误代码',
    `retry_count` int DEFAULT 0 COMMENT '重试次数',
    `is_retry` tinyint(1) DEFAULT 0 COMMENT '是否为重试(0:否 1:是)',
    `original_log_id` bigint DEFAULT NULL COMMENT '原始日志ID(重试时关联)',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_config_id` (`config_id`),
    KEY `idx_group_id` (`group_id`),
    KEY `idx_trigger_type` (`trigger_type`),
    KEY `idx_event_type` (`event_type`),
    KEY `idx_business` (`business_type`, `business_id`),
    KEY `idx_status` (`status`),
    KEY `idx_executed_at` (`executed_at`),
    KEY `idx_execution_time` (`execution_time`),
    KEY `idx_http_status` (`http_status`),
    KEY `idx_is_retry` (`is_retry`),
    KEY `idx_original_log_id` (`original_log_id`),
    KEY `idx_create_time` (`create_time`),
    CONSTRAINT `fk_zapier_log_config` FOREIGN KEY (`config_id`) REFERENCES `acc_zapier_config` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_zapier_log_original` FOREIGN KEY (`original_log_id`) REFERENCES `acc_zapier_log` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Zapier执行日志表';

-- 创建分区表(按月分区，提高查询性能)
-- ALTER TABLE `acc_zapier_log` PARTITION BY RANGE (TO_DAYS(`executed_at`)) (
--     PARTITION p202501 VALUES LESS THAN (TO_DAYS('2025-02-01')),
--     PARTITION p202502 VALUES LESS THAN (TO_DAYS('2025-03-01')),
--     PARTITION p202503 VALUES LESS THAN (TO_DAYS('2025-04-01')),
--     PARTITION p202504 VALUES LESS THAN (TO_DAYS('2025-05-01')),
--     PARTITION p202505 VALUES LESS THAN (TO_DAYS('2025-06-01')),
--     PARTITION p202506 VALUES LESS THAN (TO_DAYS('2025-07-01')),
--     PARTITION p202507 VALUES LESS THAN (TO_DAYS('2025-08-01')),
--     PARTITION p202508 VALUES LESS THAN (TO_DAYS('2025-09-01')),
--     PARTITION p202509 VALUES LESS THAN (TO_DAYS('2025-10-01')),
--     PARTITION p202510 VALUES LESS THAN (TO_DAYS('2025-11-01')),
--     PARTITION p202511 VALUES LESS THAN (TO_DAYS('2025-12-01')),
--     PARTITION p202512 VALUES LESS THAN (TO_DAYS('2026-01-01')),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- 插入示例数据
INSERT INTO `acc_zapier_config` (
    `group_id`, `name`, `description`, `webhook_url`, `trigger_type`,
    `trigger_conditions`, `data_mapping`, `enabled`, `status`, `priority`
) VALUES
(1, '交易创建通知', '当创建新交易时发送通知到Slack', 'https://hooks.slack.com/services/xxx/yyy/zzz', 'TRANSACTION_CREATED',
 '{"amount_threshold": 1000, "categories": ["餐饮", "交通"]}',
 '{"fieldMapping": {"id": "transaction_id", "amount": "amount", "description": "description"}, "includeMetadata": true}',
 1, 'ACTIVE', 10),

(1, '钱包余额更新', '当钱包余额发生变化时同步到外部系统', 'https://api.example.com/webhook/wallet', 'WALLET_UPDATED',
 '{"balance_change_threshold": 100}',
 '{"fieldMapping": {"id": "wallet_id", "balance": "balance", "currency": "currency"}}',
 1, 'ACTIVE', 5),

(1, '报表生成完成', '当月度报表生成完成时发送邮件通知', 'https://api.mailgun.net/v3/domain/messages', 'REPORT_GENERATED',
 '{"report_types": ["monthly", "quarterly"]}',
 '{"fieldMapping": {"reportId": "report_id", "reportType": "type", "generatedAt": "created_at"}}',
 1, 'ACTIVE', 8);

-- 插入示例日志数据
INSERT INTO `acc_zapier_log` (
    `config_id`, `group_id`, `trigger_type`, `event_type`, `business_id`, `business_type`,
    `request_data`, `response_data`, `http_status`, `status`, `executed_at`, `execution_time`,
    `request_size`, `response_size`
) VALUES
(1, 1, 'TRANSACTION_CREATED', 'CREATE', 123, 'TRANSACTION',
 '{"transaction_id": 123, "amount": 1500.00, "description": "午餐费用", "category": "餐饮"}',
 '{"success": true, "message": "Notification sent"}',
 200, 'SUCCESS', NOW() - INTERVAL 1 HOUR, 850, 156, 45),

(1, 1, 'TRANSACTION_CREATED', 'CREATE', 124, 'TRANSACTION',
 '{"transaction_id": 124, "amount": 2000.00, "description": "打车费用", "category": "交通"}',
 '{"error": "Invalid webhook URL"}',
 404, 'FAILED', NOW() - INTERVAL 30 MINUTE, 5000, 158, 0),

(2, 1, 'WALLET_UPDATED', 'UPDATE', 1, 'WALLET',
 '{"wallet_id": 1, "balance": 15000.00, "currency": "CNY", "change": -1500.00}',
 '{"success": true, "synced": true}',
 200, 'SUCCESS', NOW() - INTERVAL 15 MINUTE, 1200, 142, 38);

-- 创建视图：配置统计视图
CREATE VIEW `v_zapier_config_stats` AS
SELECT
    c.id,
    c.group_id,
    c.name,
    c.trigger_type,
    c.enabled,
    c.status,
    c.trigger_count,
    c.success_count,
    c.failure_count,
    CASE
        WHEN c.trigger_count > 0 THEN ROUND(c.success_count * 100.0 / c.trigger_count, 2)
        ELSE 0
    END as success_rate,
    c.last_triggered_at,
    CASE
        WHEN c.last_triggered_at IS NULL THEN 'NEVER_TRIGGERED'
        WHEN c.last_triggered_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 'HEALTHY'
        WHEN c.last_triggered_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 'WARNING'
        ELSE 'CRITICAL'
    END as health_status,
    (
        SELECT COUNT(*)
        FROM acc_zapier_log l
        WHERE l.config_id = c.id
        AND l.status = 'FAILED'
        AND l.executed_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ) as recent_failures_24h,
    (
        SELECT AVG(l.execution_time)
        FROM acc_zapier_log l
        WHERE l.config_id = c.id
        AND l.executed_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ) as avg_execution_time_24h
FROM acc_zapier_config c;

-- 创建视图：日志统计视图
CREATE VIEW `v_zapier_log_stats` AS
SELECT
    DATE(executed_at) as log_date,
    config_id,
    group_id,
    trigger_type,
    COUNT(*) as total_executions,
    SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
    SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failure_count,
    SUM(CASE WHEN status = 'TIMEOUT' THEN 1 ELSE 0 END) as timeout_count,
    ROUND(SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as success_rate,
    AVG(execution_time) as avg_execution_time,
    MIN(execution_time) as min_execution_time,
    MAX(execution_time) as max_execution_time,
    SUM(request_size) as total_request_size,
    SUM(response_size) as total_response_size
FROM acc_zapier_log
GROUP BY DATE(executed_at), config_id, group_id, trigger_type;

-- 创建存储过程：清理过期日志
DELIMITER //
CREATE PROCEDURE `sp_cleanup_zapier_logs`(IN retention_days INT)
BEGIN
    DECLARE deleted_count INT DEFAULT 0;
    
    -- 删除过期日志
    DELETE FROM acc_zapier_log
    WHERE executed_at <= DATE_SUB(NOW(), INTERVAL retention_days DAY);
    
    SET deleted_count = ROW_COUNT();
    
    -- 记录清理结果
    INSERT INTO acc_system_log (operation, description, create_time)
    VALUES ('ZAPIER_LOG_CLEANUP', CONCAT('清理了 ', deleted_count, ' 条过期Zapier日志'), NOW());
    
    SELECT deleted_count as deleted_logs;
END //
DELIMITER ;

-- 创建存储过程：配置健康检查
DELIMITER //
CREATE PROCEDURE `sp_zapier_health_check`()
BEGIN
    -- 检查长时间未触发的配置
    SELECT
        'INACTIVE_CONFIGS' as check_type,
        COUNT(*) as count,
        GROUP_CONCAT(name SEPARATOR ', ') as details
    FROM acc_zapier_config
    WHERE enabled = 1
    AND (last_triggered_at IS NULL OR last_triggered_at <= DATE_SUB(NOW(), INTERVAL 7 DAY))
    
    UNION ALL
    
    -- 检查错误率高的配置
    SELECT
        'HIGH_ERROR_RATE_CONFIGS' as check_type,
        COUNT(*) as count,
        GROUP_CONCAT(name SEPARATOR ', ') as details
    FROM acc_zapier_config
    WHERE enabled = 1
    AND trigger_count > 10
    AND (failure_count * 100.0 / trigger_count) > 20
    
    UNION ALL
    
    -- 检查最近24小时的失败次数
    SELECT
        'RECENT_FAILURES' as check_type,
        COUNT(*) as count,
        'Recent failures in last 24 hours' as details
    FROM acc_zapier_log
    WHERE status = 'FAILED'
    AND executed_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR);
END //
DELIMITER ;

-- 创建定时任务相关的事件（需要开启事件调度器）
-- SET GLOBAL event_scheduler = ON;

-- 每天凌晨2点清理30天前的日志
-- CREATE EVENT IF NOT EXISTS `ev_cleanup_zapier_logs`
-- ON SCHEDULE EVERY 1 DAY
-- STARTS '2025-01-02 02:00:00'
-- DO
--   CALL sp_cleanup_zapier_logs(30);

-- 每小时执行一次健康检查
-- CREATE EVENT IF NOT EXISTS `ev_zapier_health_check`
-- ON SCHEDULE EVERY 1 HOUR
-- DO
--   CALL sp_zapier_health_check();
