package top.continew.admin.accounting.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

/**
 * 文件处理状态枚举
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Getter
@RequiredArgsConstructor
public enum FileProcessStatusEnum implements BaseEnum<String> {

    /**
     * 待处理
     */
    PENDING("PENDING", "待处理"),

    /**
     * 处理中
     */
    PROCESSING("PROCESSING", "处理中"),

    /**
     * 处理完成
     */
    COMPLETED("COMPLETED", "处理完成"),

    /**
     * 处理失败
     */
    FAILED("FAILED", "处理失败"),

    /**
     * 已跳过
     */
    SKIPPED("SKIPPED", "已跳过");

    private final String value;
    private final String description;

    /**
     * 是否为最终状态
     */
    public boolean isFinalStatus() {
        return COMPLETED.equals(this) || FAILED.equals(this) || SKIPPED.equals(this);
    }

    /**
     * 是否可以重试
     */
    public boolean canRetry() {
        return FAILED.equals(this);
    }

    /**
     * 是否正在处理
     */
    public boolean isProcessing() {
        return PROCESSING.equals(this);
    }

}
