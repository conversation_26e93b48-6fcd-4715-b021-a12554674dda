package top.continew.admin.api.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;

/**
 * API群组创建请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "API群组创建请求")
public class ApiGroupCreateReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组名称
     */
    @Schema(description = "群组名称", example = "我的记账群组")
    @NotBlank(message = "群组名称不能为空")
    @Size(max = 50, message = "群组名称长度不能超过 {max} 个字符")
    private String name;

    /**
     * 群组描述
     */
    @Schema(description = "群组描述", example = "用于家庭日常开支记录")
    @Size(max = 200, message = "群组描述长度不能超过 {max} 个字符")
    private String description;

    /**
     * 群组头像
     */
    @Schema(description = "群组头像URL", example = "https://example.com/avatar.jpg")
    @Size(max = 500, message = "群组头像URL长度不能超过 {max} 个字符")
    private String avatar;

    /**
     * 默认货币
     */
    @Schema(description = "默认货币", example = "CNY")
    @Size(max = 10, message = "默认货币长度不能超过 {max} 个字符")
    private String defaultCurrency;

    /**
     * 是否公开
     */
    @Schema(description = "是否公开群组", example = "false")
    private Boolean isPublic;

    /**
     * 群组设置
     */
    @Schema(description = "群组设置")
    private GroupSettings settings;

    /**
     * 群组设置
     */
    @Data
    @Schema(description = "群组设置")
    public static class GroupSettings implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 是否允许成员邀请
         */
        @Schema(description = "是否允许成员邀请其他用户", example = "true")
        private Boolean allowMemberInvite;

        /**
         * 是否需要审批加入
         */
        @Schema(description = "是否需要审批加入", example = "false")
        private Boolean requireApproval;

        /**
         * 是否允许成员创建分类
         */
        @Schema(description = "是否允许成员创建分类", example = "true")
        private Boolean allowMemberCreateCategory;

        /**
         * 是否允许成员创建标签
         */
        @Schema(description = "是否允许成员创建标签", example = "true")
        private Boolean allowMemberCreateTag;

        /**
         * 是否启用预算功能
         */
        @Schema(description = "是否启用预算功能", example = "true")
        private Boolean enableBudget;

        /**
         * 是否启用债务跟踪
         */
        @Schema(description = "是否启用债务跟踪", example = "true")
        private Boolean enableDebtTracking;

        /**
         * 是否启用定期报告
         */
        @Schema(description = "是否启用定期报告", example = "true")
        private Boolean enablePeriodicReport;

        /**
         * 报告频率
         */
        @Schema(description = "报告频率", example = "WEEKLY", allowableValues = {"DAILY", "WEEKLY", "MONTHLY"})
        private String reportFrequency;

        /**
         * 时区
         */
        @Schema(description = "时区", example = "Asia/Shanghai")
        private String timezone;

        /**
         * 语言
         */
        @Schema(description = "语言", example = "zh-CN")
        private String language;
    }
}
