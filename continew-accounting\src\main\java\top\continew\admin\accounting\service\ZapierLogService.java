package top.continew.admin.accounting.service;

import top.continew.admin.accounting.model.entity.ZapierLogDO;
import top.continew.admin.accounting.model.query.ZapierLogQuery;
import top.continew.admin.accounting.model.resp.ZapierLogResp;
import top.continew.starter.extension.crud.service.BaseService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Zapier日志服务
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface ZapierLogService extends BaseService<ZapierLogResp, ZapierLogResp, ZapierLogQuery, ZapierLogDO, ZapierLogDO> {

    /**
     * 记录执行日志
     *
     * @param configId      配置ID
     * @param groupId       群组ID
     * @param triggerType   触发器类型
     * @param eventType     事件类型
     * @param businessId    业务ID
     * @param businessType  业务类型
     * @param requestData   请求数据
     * @param responseData  响应数据
     * @param httpStatus    HTTP状态码
     * @param status        执行状态
     * @param executionTime 执行耗时
     * @param errorMessage  错误信息
     * @param errorCode     错误代码
     * @return 日志ID
     */
    Long recordLog(Long configId, Long groupId, String triggerType, String eventType,
                   Long businessId, String businessType, Map<String, Object> requestData,
                   Map<String, Object> responseData, Integer httpStatus, String status,
                   Long executionTime, String errorMessage, String errorCode);

    /**
     * 记录重试日志
     *
     * @param originalLogId 原始日志ID
     * @param configId      配置ID
     * @param groupId       群组ID
     * @param triggerType   触发器类型
     * @param eventType     事件类型
     * @param businessId    业务ID
     * @param businessType  业务类型
     * @param requestData   请求数据
     * @param responseData  响应数据
     * @param httpStatus    HTTP状态码
     * @param status        执行状态
     * @param executionTime 执行耗时
     * @param errorMessage  错误信息
     * @param errorCode     错误代码
     * @param retryCount    重试次数
     * @return 日志ID
     */
    Long recordRetryLog(Long originalLogId, Long configId, Long groupId, String triggerType,
                        String eventType, Long businessId, String businessType,
                        Map<String, Object> requestData, Map<String, Object> responseData,
                        Integer httpStatus, String status, Long executionTime,
                        String errorMessage, String errorCode, Integer retryCount);

    /**
     * 查询配置的执行日志
     *
     * @param configId 配置ID
     * @param limit    限制数量
     * @return 日志列表
     */
    List<ZapierLogResp> listByConfigId(Long configId, Integer limit);

    /**
     * 查询群组的执行日志
     *
     * @param groupId 群组ID
     * @param limit   限制数量
     * @return 日志列表
     */
    List<ZapierLogResp> listByGroupId(Long groupId, Integer limit);

    /**
     * 查询失败的执行日志
     *
     * @param configId 配置ID
     * @param hours    小时数
     * @return 失败日志列表
     */
    List<ZapierLogResp> listFailedLogs(Long configId, Integer hours);

    /**
     * 查询执行统计
     *
     * @param configId  配置ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 执行统计
     */
    Map<String, Object> getExecutionStats(Long configId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询群组执行统计
     *
     * @param groupId   群组ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 群组执行统计
     */
    Map<String, Object> getGroupExecutionStats(Long groupId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询执行趋势
     *
     * @param configId  配置ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param interval  时间间隔（hour/day/week/month）
     * @return 执行趋势
     */
    List<Map<String, Object>> getExecutionTrend(Long configId, LocalDateTime startTime, LocalDateTime endTime, String interval);

    /**
     * 查询错误分布统计
     *
     * @param configId  配置ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 错误分布统计
     */
    List<Map<String, Object>> getErrorDistribution(Long configId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询性能统计
     *
     * @param configId  配置ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 性能统计
     */
    Map<String, Object> getPerformanceStats(Long configId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询慢执行日志
     *
     * @param configId    配置ID
     * @param thresholdMs 执行时间阈值（毫秒）
     * @param hours       小时数
     * @return 慢执行日志列表
     */
    List<ZapierLogResp> listSlowExecutionLogs(Long configId, Long thresholdMs, Integer hours);

    /**
     * 查询重试日志
     *
     * @param originalLogId 原始日志ID
     * @return 重试日志列表
     */
    List<ZapierLogResp> listRetryLogs(Long originalLogId);

    /**
     * 查询业务相关日志
     *
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @return 日志列表
     */
    List<ZapierLogResp> listByBusiness(String businessType, Long businessId);

    /**
     * 删除过期日志
     *
     * @param days 保留天数
     * @return 删除数量
     */
    Integer deleteExpiredLogs(Integer days);

    /**
     * 删除配置的所有日志
     *
     * @param configId 配置ID
     * @return 删除数量
     */
    Integer deleteByConfigId(Long configId);

    /**
     * 删除群组的所有日志
     *
     * @param groupId 群组ID
     * @return 删除数量
     */
    Integer deleteByGroupId(Long groupId);

    /**
     * 查询最近执行日志
     *
     * @param configId 配置ID
     * @param limit    限制数量
     * @return 最近执行日志
     */
    List<ZapierLogResp> listRecentLogs(Long configId, Integer limit);

    /**
     * 查询异常日志
     *
     * @param groupId 群组ID
     * @param hours   小时数
     * @return 异常日志列表
     */
    List<ZapierLogResp> listAnomalyLogs(Long groupId, Integer hours);

    /**
     * 查询HTTP状态码分布
     *
     * @param configId  配置ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return HTTP状态码分布
     */
    List<Map<String, Object>> getHttpStatusDistribution(Long configId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询数据传输统计
     *
     * @param configId  配置ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 数据传输统计
     */
    Map<String, Object> getDataTransferStats(Long configId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询执行时间分布
     *
     * @param configId  配置ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 执行时间分布
     */
    List<Map<String, Object>> getExecutionTimeDistribution(Long configId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 导出日志
     *
     * @param query 查询条件
     * @return 导出数据
     */
    Map<String, Object> exportLogs(ZapierLogQuery query);

    /**
     * 清理日志
     *
     * @param configId 配置ID
     * @param days     保留天数
     * @return 清理数量
     */
    Integer cleanupLogs(Long configId, Integer days);

    /**
     * 获取日志统计概览
     *
     * @param groupId 群组ID
     * @return 统计概览
     */
    Map<String, Object> getLogOverview(Long groupId);

    /**
     * 获取日志详情（包含配置信息）
     *
     * @param id 日志ID
     * @return 日志详情
     */
    Map<String, Object> getLogDetail(Long id);
}
