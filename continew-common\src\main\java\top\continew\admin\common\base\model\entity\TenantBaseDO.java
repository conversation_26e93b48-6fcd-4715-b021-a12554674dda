/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.common.base.model.entity;

import lombok.Data;

import java.io.Serial;

/**
 * 租户实体类基类
 *
 * <p>
 * 通用字段：ID、创建人、创建时间、修改人、修改时间、租户 ID
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/17 20:20
 */
@Data
public class TenantBaseDO extends BaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 租户 ID
     */
    private Long tenantId;
}
