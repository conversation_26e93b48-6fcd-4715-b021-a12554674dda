package top.continew.admin.accounting.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import top.continew.admin.accounting.base.BaseServiceTest;
import top.continew.admin.accounting.enums.TransactionType;
import top.continew.admin.accounting.mapper.TransactionMapper;
import top.continew.admin.accounting.model.entity.TransactionDO;
import top.continew.admin.accounting.model.req.TransactionCreateReq;
import top.continew.admin.accounting.model.req.TransactionQueryReq;
import top.continew.admin.accounting.model.req.TransactionUpdateReq;
import top.continew.admin.accounting.model.resp.TransactionResp;
import top.continew.admin.accounting.service.impl.TransactionServiceImpl;
import top.continew.admin.accounting.util.TestDataUtil;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 交易服务测试
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@ExtendWith(MockitoExtension.class)
class TransactionServiceTest extends BaseServiceTest {

    @Mock
    private TransactionMapper transactionMapper;

    @InjectMocks
    private TransactionServiceImpl transactionService;

    private TransactionDO testTransaction;
    private TransactionCreateReq createReq;
    private TransactionUpdateReq updateReq;

    @BeforeEach
    @Override
    protected void beforeEach() {
        super.beforeEach();
        testTransaction = TestDataUtil.createTestTransaction();
        createReq = TestDataUtil.createTestTransactionCreateReq();
        
        updateReq = new TransactionUpdateReq();
        updateReq.setAmount(new BigDecimal("75.00"));
        updateReq.setDescription("更新后的交易描述");
        updateReq.setCategoryId(2L);
    }

    @Test
    void testAdd() {
        // Given
        when(transactionMapper.insert(any(TransactionDO.class))).thenReturn(1);
        when(transactionMapper.selectById(anyLong())).thenReturn(testTransaction);

        // When
        Long transactionId = transactionService.add(createReq);

        // Then
        assertNotNull(transactionId);
        verify(transactionMapper).insert(any(TransactionDO.class));
    }

    @Test
    void testGetById() {
        // Given
        when(transactionMapper.selectById(1L)).thenReturn(testTransaction);

        // When
        TransactionResp result = transactionService.get(1L);

        // Then
        assertNotNull(result);
        assertEquals(testTransaction.getAmount(), result.getAmount());
        assertEquals(testTransaction.getDescription(), result.getDescription());
        assertEquals(testTransaction.getType(), result.getType());
        verify(transactionMapper).selectById(1L);
    }

    @Test
    void testUpdate() {
        // Given
        when(transactionMapper.selectById(1L)).thenReturn(testTransaction);
        when(transactionMapper.updateById(any(TransactionDO.class))).thenReturn(1);

        // When
        assertDoesNotThrow(() -> transactionService.update(updateReq, 1L));

        // Then
        verify(transactionMapper).selectById(1L);
        verify(transactionMapper).updateById(any(TransactionDO.class));
    }

    @Test
    void testDelete() {
        // Given
        when(transactionMapper.selectById(1L)).thenReturn(testTransaction);
        when(transactionMapper.deleteById(1L)).thenReturn(1);

        // When
        assertDoesNotThrow(() -> transactionService.delete(Arrays.asList(1L)));

        // Then
        verify(transactionMapper).selectById(1L);
        verify(transactionMapper).deleteById(1L);
    }

    @Test
    void testPage() {
        // Given
        TransactionQueryReq queryReq = new TransactionQueryReq();
        queryReq.setGroupId(1L);
        queryReq.setType(TransactionType.EXPENSE);
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPage(1);
        pageQuery.setSize(10);

        List<TransactionDO> transactions = Arrays.asList(testTransaction);
        when(transactionMapper.selectPage(any(), any())).thenReturn(transactions);
        when(transactionMapper.selectCount(any())).thenReturn(1L);

        // When
        PageResp<TransactionResp> result = transactionService.page(queryReq, pageQuery);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getList().size());
        assertEquals(testTransaction.getAmount(), result.getList().get(0).getAmount());
    }

    @Test
    void testList() {
        // Given
        TransactionQueryReq queryReq = new TransactionQueryReq();
        queryReq.setGroupId(1L);
        List<TransactionDO> transactions = Arrays.asList(testTransaction);
        when(transactionMapper.selectList(any())).thenReturn(transactions);

        // When
        List<TransactionResp> result = transactionService.list(queryReq);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testTransaction.getAmount(), result.get(0).getAmount());
    }

    @Test
    void testBatchAdd() {
        // Given
        List<TransactionCreateReq> requests = Arrays.asList(createReq, createReq);
        when(transactionMapper.insert(any(TransactionDO.class))).thenReturn(1);

        // When
        List<Long> result = transactionService.batchAdd(requests);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(transactionMapper, times(2)).insert(any(TransactionDO.class));
    }

    @Test
    void testGetStatistics() {
        // Given
        when(transactionMapper.selectTotalAmount(anyLong(), any(), any(), any()))
            .thenReturn(new BigDecimal("1000.00"));
        when(transactionMapper.selectTransactionCount(anyLong(), any(), any(), any()))
            .thenReturn(10L);

        // When
        var statistics = transactionService.getStatistics(1L, TransactionType.EXPENSE, 
            LocalDateTime.now().minusDays(30), LocalDateTime.now());

        // Then
        assertNotNull(statistics);
        verify(transactionMapper).selectTotalAmount(anyLong(), any(), any(), any());
        verify(transactionMapper).selectTransactionCount(anyLong(), any(), any(), any());
    }

    @Test
    void testGetMonthlyTrend() {
        // Given
        when(transactionMapper.selectMonthlyTrend(anyLong(), any(), anyInt()))
            .thenReturn(Arrays.asList());

        // When
        var trend = transactionService.getMonthlyTrend(1L, TransactionType.EXPENSE, 12);

        // Then
        assertNotNull(trend);
        verify(transactionMapper).selectMonthlyTrend(anyLong(), any(), anyInt());
    }

    @Test
    void testGetCategoryStatistics() {
        // Given
        when(transactionMapper.selectCategoryStatistics(anyLong(), any(), any(), any()))
            .thenReturn(Arrays.asList());

        // When
        var statistics = transactionService.getCategoryStatistics(1L, TransactionType.EXPENSE,
            LocalDateTime.now().minusDays(30), LocalDateTime.now());

        // Then
        assertNotNull(statistics);
        verify(transactionMapper).selectCategoryStatistics(anyLong(), any(), any(), any());
    }

    @Test
    void testGetUserStatistics() {
        // Given
        when(transactionMapper.selectUserStatistics(anyLong(), any(), any(), any()))
            .thenReturn(Arrays.asList());

        // When
        var statistics = transactionService.getUserStatistics(1L, TransactionType.EXPENSE,
            LocalDateTime.now().minusDays(30), LocalDateTime.now());

        // Then
        assertNotNull(statistics);
        verify(transactionMapper).selectUserStatistics(anyLong(), any(), any(), any());
    }

}
