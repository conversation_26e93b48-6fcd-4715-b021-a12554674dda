package top.continew.admin.accounting.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.extension.crud.model.query.PageQuery;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * Zapier日志查询条件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "Zapier日志查询条件")
public class ZapierLogQuery extends PageQuery {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 配置ID
     */
    @Schema(description = "配置ID", example = "1")
    private Long configId;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 触发器类型
     */
    @Schema(description = "触发器类型", example = "TRANSACTION_CREATED")
    private String triggerType;

    /**
     * 事件类型
     */
    @Schema(description = "事件类型", example = "CREATE")
    private String eventType;

    /**
     * 业务类型
     */
    @Schema(description = "业务类型", example = "TRANSACTION")
    private String businessType;

    /**
     * 执行状态
     */
    @Schema(description = "执行状态", example = "SUCCESS")
    private String status;

    /**
     * HTTP状态码
     */
    @Schema(description = "HTTP状态码", example = "200")
    private Integer httpStatus;

    /**
     * 是否为重试
     */
    @Schema(description = "是否为重试", example = "false")
    private Boolean isRetry;

    /**
     * 执行时间开始
     */
    @Schema(description = "执行时间开始", example = "2025-01-01T00:00:00")
    private LocalDateTime executedStart;

    /**
     * 执行时间结束
     */
    @Schema(description = "执行时间结束", example = "2025-01-31T23:59:59")
    private LocalDateTime executedEnd;

    /**
     * 最小执行耗时（毫秒）
     */
    @Schema(description = "最小执行耗时（毫秒）", example = "100")
    private Long minExecutionTime;

    /**
     * 最大执行耗时（毫秒）
     */
    @Schema(description = "最大执行耗时（毫秒）", example = "5000")
    private Long maxExecutionTime;

    /**
     * 错误代码
     */
    @Schema(description = "错误代码", example = "TIMEOUT")
    private String errorCode;
}
