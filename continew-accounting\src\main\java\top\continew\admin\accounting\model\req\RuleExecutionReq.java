package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.common.model.req.BaseReq;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 规则执行请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "规则执行请求")
public class RuleExecutionReq extends BaseReq {

    /**
     * 规则ID列表
     */
    @Schema(description = "规则ID列表", example = "[1, 2, 3]")
    @NotNull(message = "规则ID列表不能为空")
    private List<Long> ruleIds;

    /**
     * 执行模式
     */
    @Schema(description = "执行模式", example = "SYNC")
    private String executionMode = "SYNC";

    /**
     * 执行范围
     */
    @Schema(description = "执行范围")
    @Valid
    private ExecutionScope executionScope;

    /**
     * 执行参数
     */
    @Schema(description = "执行参数")
    private Map<String, Object> executionParams;

    /**
     * 是否强制执行
     */
    @Schema(description = "是否强制执行", example = "false")
    private Boolean forceExecution = false;

    /**
     * 是否跳过频率限制
     */
    @Schema(description = "是否跳过频率限制", example = "false")
    private Boolean skipFrequencyLimit = false;

    /**
     * 是否跳过条件检查
     */
    @Schema(description = "是否跳过条件检查", example = "false")
    private Boolean skipConditionCheck = false;

    /**
     * 执行优先级
     */
    @Schema(description = "执行优先级", example = "NORMAL")
    private String executionPriority = "NORMAL";

    /**
     * 超时时间（秒）
     */
    @Schema(description = "超时时间（秒）", example = "300")
    private Integer timeoutSeconds = 300;

    /**
     * 批处理配置
     */
    @Schema(description = "批处理配置")
    @Valid
    private BatchConfig batchConfig;

    /**
     * 回调配置
     */
    @Schema(description = "回调配置")
    @Valid
    private CallbackConfig callbackConfig;

    /**
     * 执行范围
     */
    @Data
    @Schema(description = "执行范围")
    public static class ExecutionScope {

        /**
         * 范围类型
         */
        @Schema(description = "范围类型", example = "ALL")
        private String scopeType = "ALL";

        /**
         * 目标数据ID列表
         */
        @Schema(description = "目标数据ID列表", example = "[1, 2, 3]")
        private List<Long> targetIds;

        /**
         * 过滤条件
         */
        @Schema(description = "过滤条件")
        private Map<String, Object> filterConditions;

        /**
         * 时间范围
         */
        @Schema(description = "时间范围")
        @Valid
        private TimeRange timeRange;

        /**
         * 最大处理数量
         */
        @Schema(description = "最大处理数量", example = "1000")
        private Integer maxRecords;

        /**
         * 是否包含历史数据
         */
        @Schema(description = "是否包含历史数据", example = "false")
        private Boolean includeHistorical = false;
    }

    /**
     * 时间范围
     */
    @Data
    @Schema(description = "时间范围")
    public static class TimeRange {

        /**
         * 开始时间
         */
        @Schema(description = "开始时间", example = "2024-01-01 00:00:00")
        private LocalDateTime startTime;

        /**
         * 结束时间
         */
        @Schema(description = "结束时间", example = "2024-12-31 23:59:59")
        private LocalDateTime endTime;

        /**
         * 相对时间范围
         */
        @Schema(description = "相对时间范围", example = "LAST_7_DAYS")
        private String relativeRange;

        /**
         * 时区
         */
        @Schema(description = "时区", example = "Asia/Shanghai")
        private String timezone = "Asia/Shanghai";
    }

    /**
     * 批处理配置
     */
    @Data
    @Schema(description = "批处理配置")
    public static class BatchConfig {

        /**
         * 批处理大小
         */
        @Schema(description = "批处理大小", example = "100")
        private Integer batchSize = 100;

        /**
         * 批次间隔（毫秒）
         */
        @Schema(description = "批次间隔（毫秒）", example = "1000")
        private Integer batchIntervalMs = 1000;

        /**
         * 最大并发数
         */
        @Schema(description = "最大并发数", example = "5")
        private Integer maxConcurrency = 5;

        /**
         * 失败策略
         */
        @Schema(description = "失败策略", example = "CONTINUE")
        private String failureStrategy = "CONTINUE";

        /**
         * 进度回调间隔
         */
        @Schema(description = "进度回调间隔", example = "10")
        private Integer progressCallbackInterval = 10;
    }

    /**
     * 回调配置
     */
    @Data
    @Schema(description = "回调配置")
    public static class CallbackConfig {

        /**
         * 回调URL
         */
        @Schema(description = "回调URL", example = "http://localhost:8080/callback")
        private String callbackUrl;

        /**
         * 回调方法
         */
        @Schema(description = "回调方法", example = "POST")
        private String callbackMethod = "POST";

        /**
         * 回调头部
         */
        @Schema(description = "回调头部")
        private Map<String, String> callbackHeaders;

        /**
         * 回调条件
         */
        @Schema(description = "回调条件", example = "ON_COMPLETION")
        private String callbackCondition = "ON_COMPLETION";

        /**
         * 重试次数
         */
        @Schema(description = "重试次数", example = "3")
        private Integer retryCount = 3;

        /**
         * 重试间隔（秒）
         */
        @Schema(description = "重试间隔（秒）", example = "60")
        private Integer retryIntervalSeconds = 60;
    }
}
