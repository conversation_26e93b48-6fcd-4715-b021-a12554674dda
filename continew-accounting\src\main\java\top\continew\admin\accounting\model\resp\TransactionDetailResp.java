package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.accounting.enums.SplitType;
import top.continew.admin.accounting.enums.TransactionType;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 账单详情响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "账单详情响应")
public class TransactionDetailResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Schema(description = "ID", example = "1")
    private Long id;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 群组名称
     */
    @Schema(description = "群组名称", example = "我的记账群")
    private String groupName;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型", example = "EXPENSE")
    private TransactionType type;

    /**
     * 金额
     */
    @Schema(description = "金额", example = "100.50")
    private BigDecimal amount;

    /**
     * 币种
     */
    @Schema(description = "币种", example = "CNY")
    private String currency;

    /**
     * 描述
     */
    @Schema(description = "描述", example = "午餐费用")
    private String description;

    /**
     * 分类
     */
    @Schema(description = "分类", example = "餐饮")
    private String category;

    /**
     * 标签
     */
    @Schema(description = "标签", example = "工作,午餐")
    private String tags;

    /**
     * 交易时间
     */
    @Schema(description = "交易时间", example = "2025-01-01T12:00:00")
    private LocalDateTime transactionDate;

    /**
     * 附件URL列表
     */
    @Schema(description = "附件URL列表")
    private List<String> attachments;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "与同事聚餐")
    private String remark;

    /**
     * 分摊类型
     */
    @Schema(description = "分摊类型", example = "EQUAL")
    private SplitType splitType;

    /**
     * 分摊参与者列表
     */
    @Schema(description = "分摊参与者列表")
    private List<SplitParticipantResp> splitParticipants;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID", example = "1")
    private Long createUser;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名", example = "张三")
    private String createUserName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-01-01T12:00:00")
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID", example = "1")
    private Long updateUser;

    /**
     * 更新人姓名
     */
    @Schema(description = "更新人姓名", example = "李四")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2025-01-01T13:00:00")
    private LocalDateTime updateTime;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "1")
    private Integer status;

    /**
     * 分摊参与者响应
     */
    @Data
    @Schema(description = "分摊参与者响应")
    public static class SplitParticipantResp implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 用户ID
         */
        @Schema(description = "用户ID", example = "1")
        private Long userId;

        /**
         * 用户姓名
         */
        @Schema(description = "用户姓名", example = "张三")
        private String userName;

        /**
         * 分摊金额
         */
        @Schema(description = "分摊金额", example = "50.00")
        private BigDecimal amount;

        /**
         * 分摊比例
         */
        @Schema(description = "分摊比例", example = "30.5")
        private BigDecimal percentage;

        /**
         * 是否已结算
         */
        @Schema(description = "是否已结算", example = "false")
        private Boolean settled;
    }
}
