package top.continew.admin.accounting.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.accounting.enums.TransactionType;
import top.continew.starter.extension.crud.model.query.SortQuery;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 账单查询条件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "账单查询条件")
public class TransactionQuery extends SortQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型", example = "EXPENSE")
    private TransactionType type;

    /**
     * 分类
     */
    @Schema(description = "分类", example = "餐饮")
    private String category;

    /**
     * 币种
     */
    @Schema(description = "币种", example = "CNY")
    private String currency;

    /**
     * 最小金额
     */
    @Schema(description = "最小金额", example = "0")
    private BigDecimal minAmount;

    /**
     * 最大金额
     */
    @Schema(description = "最大金额", example = "1000")
    private BigDecimal maxAmount;

    /**
     * 开始日期
     */
    @Schema(description = "开始日期", example = "2025-01-01")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @Schema(description = "结束日期", example = "2025-01-31")
    private LocalDate endDate;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID", example = "1")
    private Long createUser;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "1")
    private Integer status;

    /**
     * 关键词搜索（描述、标签）
     */
    @Schema(description = "关键词搜索", example = "午餐")
    private String keyword;

    /**
     * 是否包含分摊
     */
    @Schema(description = "是否包含分摊", example = "true")
    private Boolean hasSplit;
}
