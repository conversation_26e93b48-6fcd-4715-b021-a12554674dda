package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.accounting.enums.SplitType;
import top.continew.admin.accounting.enums.TransactionType;
import top.continew.starter.core.constant.RegexConstants;

import jakarta.validation.constraints.*;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 账单创建请求参数
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "账单创建请求参数")
public class TransactionCreateReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    @NotNull(message = "群组ID不能为空")
    private Long groupId;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型", example = "EXPENSE")
    @NotNull(message = "交易类型不能为空")
    private TransactionType type;

    /**
     * 金额
     */
    @Schema(description = "金额", example = "100.50")
    @NotNull(message = "金额不能为空")
    @DecimalMin(value = "0.01", message = "金额必须大于0")
    @Digits(integer = 10, fraction = 2, message = "金额格式不正确")
    private BigDecimal amount;

    /**
     * 币种
     */
    @Schema(description = "币种", example = "CNY")
    @NotBlank(message = "币种不能为空")
    @Pattern(regexp = "^[A-Z]{3}$", message = "币种格式不正确")
    private String currency;

    /**
     * 描述
     */
    @Schema(description = "描述", example = "午餐费用")
    @NotBlank(message = "描述不能为空")
    @Size(max = 200, message = "描述长度不能超过200个字符")
    private String description;

    /**
     * 分类
     */
    @Schema(description = "分类", example = "餐饮")
    @Size(max = 50, message = "分类长度不能超过50个字符")
    private String category;

    /**
     * 标签
     */
    @Schema(description = "标签", example = "工作,午餐")
    @Size(max = 200, message = "标签长度不能超过200个字符")
    private String tags;

    /**
     * 交易时间
     */
    @Schema(description = "交易时间", example = "2025-01-01T12:00:00")
    private LocalDateTime transactionDate;

    /**
     * 附件URL列表
     */
    @Schema(description = "附件URL列表")
    private List<String> attachments;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "与同事聚餐")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    /**
     * 分摊类型
     */
    @Schema(description = "分摊类型", example = "EQUAL")
    private SplitType splitType;

    /**
     * 分摊参与者列表
     */
    @Schema(description = "分摊参与者列表")
    private List<SplitParticipant> splitParticipants;

    /**
     * 分摊参与者
     */
    @Data
    @Schema(description = "分摊参与者")
    public static class SplitParticipant implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 用户ID
         */
        @Schema(description = "用户ID", example = "1")
        @NotNull(message = "用户ID不能为空")
        private Long userId;

        /**
         * 分摊金额（固定金额分摊时使用）
         */
        @Schema(description = "分摊金额", example = "50.00")
        @DecimalMin(value = "0.01", message = "分摊金额必须大于0")
        @Digits(integer = 10, fraction = 2, message = "分摊金额格式不正确")
        private BigDecimal amount;

        /**
         * 分摊比例（百分比分摊时使用）
         */
        @Schema(description = "分摊比例", example = "30.5")
        @DecimalMin(value = "0.01", message = "分摊比例必须大于0")
        @DecimalMax(value = "100.00", message = "分摊比例不能超过100%")
        private BigDecimal percentage;
    }
}
