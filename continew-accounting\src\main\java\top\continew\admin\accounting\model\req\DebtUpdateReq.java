package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 债务更新请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "债务更新请求")
public class DebtUpdateReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 金额
     */
    @Schema(description = "金额", example = "1000.00")
    @DecimalMin(value = "0.01", message = "金额必须大于0")
    @Digits(integer = 13, fraction = 2, message = "金额格式不正确")
    private BigDecimal amount;

    /**
     * 描述
     */
    @Schema(description = "描述", example = "借款用于购买设备")
    @Size(max = 500, message = "描述长度不能超过500个字符")
    private String description;

    /**
     * 到期时间
     */
    @Schema(description = "到期时间", example = "2025-02-01 00:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dueDate;

    /**
     * 利率（年化）
     */
    @Schema(description = "利率", example = "0.05")
    @DecimalMin(value = "0", message = "利率不能为负数")
    @DecimalMax(value = "1", message = "利率不能超过100%")
    @Digits(integer = 1, fraction = 4, message = "利率格式不正确")
    private BigDecimal interestRate;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "紧急借款")
    @Size(max = 1000, message = "备注长度不能超过1000个字符")
    private String remark;
}
