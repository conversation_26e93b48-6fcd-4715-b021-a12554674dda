package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 多维数据分析响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "多维数据分析响应")
public class MultiDimensionAnalysisResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分析ID
     */
    @Schema(description = "分析ID", example = "analysis_123456")
    private String analysisId;

    /**
     * 分析名称
     */
    @Schema(description = "分析名称", example = "收支多维分析")
    private String analysisName;

    /**
     * 维度信息
     */
    @Schema(description = "维度信息")
    private List<DimensionInfo> dimensions;

    /**
     * 度量信息
     */
    @Schema(description = "度量信息")
    private List<MeasureInfo> measures;

    /**
     * 数据矩阵
     */
    @Schema(description = "数据矩阵")
    private List<List<Object>> dataMatrix;

    /**
     * 数据透视表
     */
    @Schema(description = "数据透视表")
    private Map<String, Object> pivotTable;

    /**
     * 汇总数据
     */
    @Schema(description = "汇总数据")
    private SummaryData summaryData;

    /**
     * 统计信息
     */
    @Schema(description = "统计信息")
    private AnalysisStatistics statistics;

    /**
     * 生成时间
     */
    @Schema(description = "生成时间")
    private LocalDateTime generatedAt;

    /**
     * 执行时间（毫秒）
     */
    @Schema(description = "执行时间（毫秒）", example = "1250")
    private Long executionTime;

    /**
     * 数据来源
     */
    @Schema(description = "数据来源", example = "database")
    private String dataSource;

    /**
     * 是否来自缓存
     */
    @Schema(description = "是否来自缓存", example = "false")
    private Boolean fromCache;

    /**
     * 维度信息
     */
    @Data
    @Schema(description = "维度信息")
    public static class DimensionInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 维度名称
         */
        @Schema(description = "维度名称", example = "category")
        private String name;

        /**
         * 维度显示名称
         */
        @Schema(description = "维度显示名称", example = "分类")
        private String displayName;

        /**
         * 维度类型
         */
        @Schema(description = "维度类型", example = "STRING")
        private String type;

        /**
         * 维度值列表
         */
        @Schema(description = "维度值列表")
        private List<String> values;

        /**
         * 层次结构
         */
        @Schema(description = "层次结构")
        private List<HierarchyLevel> hierarchy;

        /**
         * 是否支持钻取
         */
        @Schema(description = "是否支持钻取", example = "true")
        private Boolean drillable;
    }

    /**
     * 度量信息
     */
    @Data
    @Schema(description = "度量信息")
    public static class MeasureInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 度量名称
         */
        @Schema(description = "度量名称", example = "amount")
        private String name;

        /**
         * 度量显示名称
         */
        @Schema(description = "度量显示名称", example = "金额")
        private String displayName;

        /**
         * 度量类型
         */
        @Schema(description = "度量类型", example = "DECIMAL")
        private String type;

        /**
         * 聚合函数
         */
        @Schema(description = "聚合函数", example = "SUM")
        private String aggregationFunction;

        /**
         * 格式化规则
         */
        @Schema(description = "格式化规则", example = "#,##0.00")
        private String format;

        /**
         * 单位
         */
        @Schema(description = "单位", example = "元")
        private String unit;
    }

    /**
     * 层次结构级别
     */
    @Data
    @Schema(description = "层次结构级别")
    public static class HierarchyLevel implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 级别名称
         */
        @Schema(description = "级别名称", example = "一级分类")
        private String levelName;

        /**
         * 级别值
         */
        @Schema(description = "级别值", example = "餐饮")
        private String levelValue;

        /**
         * 父级值
         */
        @Schema(description = "父级值", example = "生活消费")
        private String parentValue;

        /**
         * 子级列表
         */
        @Schema(description = "子级列表")
        private List<String> children;
    }

    /**
     * 汇总数据
     */
    @Data
    @Schema(description = "汇总数据")
    public static class SummaryData implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 总计
         */
        @Schema(description = "总计")
        private Map<String, BigDecimal> grandTotal;

        /**
         * 小计
         */
        @Schema(description = "小计")
        private Map<String, Map<String, BigDecimal>> subtotals;

        /**
         * 平均值
         */
        @Schema(description = "平均值")
        private Map<String, BigDecimal> averages;

        /**
         * 最大值
         */
        @Schema(description = "最大值")
        private Map<String, BigDecimal> maximums;

        /**
         * 最小值
         */
        @Schema(description = "最小值")
        private Map<String, BigDecimal> minimums;

        /**
         * 计数
         */
        @Schema(description = "计数")
        private Map<String, Long> counts;
    }

    /**
     * 分析统计信息
     */
    @Data
    @Schema(description = "分析统计信息")
    public static class AnalysisStatistics implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 总行数
         */
        @Schema(description = "总行数", example = "1000")
        private Long totalRows;

        /**
         * 总列数
         */
        @Schema(description = "总列数", example = "10")
        private Integer totalColumns;

        /**
         * 维度数量
         */
        @Schema(description = "维度数量", example = "3")
        private Integer dimensionCount;

        /**
         * 度量数量
         */
        @Schema(description = "度量数量", example = "5")
        private Integer measureCount;

        /**
         * 数据密度
         */
        @Schema(description = "数据密度", example = "0.85")
        private Double dataDensity;

        /**
         * 空值比例
         */
        @Schema(description = "空值比例", example = "0.05")
        private Double nullRatio;

        /**
         * 唯一值数量
         */
        @Schema(description = "唯一值数量")
        private Map<String, Long> uniqueValueCounts;
    }
}
