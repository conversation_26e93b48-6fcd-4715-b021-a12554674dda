package top.continew.admin.accounting.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 计费周期枚举
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Getter
@RequiredArgsConstructor
public enum BillingCycle {

    /**
     * 月付
     */
    MONTHLY("MONTHLY", "月付", 1),

    /**
     * 年付
     */
    YEARLY("YEARLY", "年付", 12),

    /**
     * 季付
     */
    QUARTERLY("QUARTERLY", "季付", 3),

    /**
     * 半年付
     */
    SEMI_ANNUALLY("SEMI_ANNUALLY", "半年付", 6);

    private final String value;
    private final String description;
    private final Integer months;
}
