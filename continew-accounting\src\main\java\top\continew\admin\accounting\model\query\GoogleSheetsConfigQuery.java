package top.continew.admin.accounting.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.admin.common.model.query.PageQuery;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Google Sheets配置查询条件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "Google Sheets配置查询条件")
public class GoogleSheetsConfigQuery extends PageQuery {

    /**
     * 配置名称
     */
    @Schema(description = "配置名称", example = "我的记账表格")
    private String configName;

    /**
     * Google Sheets ID
     */
    @Schema(description = "Google Sheets ID", example = "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms")
    private String spreadsheetId;

    /**
     * 工作表名称
     */
    @Schema(description = "工作表名称", example = "账单记录")
    private String sheetName;

    /**
     * 同步方向
     */
    @Schema(description = "同步方向", allowableValues = {"TO_SHEETS", "FROM_SHEETS", "BIDIRECTIONAL"})
    private String syncDirection;

    /**
     * 同步模式
     */
    @Schema(description = "同步模式", allowableValues = {"REAL_TIME", "SCHEDULED", "MANUAL"})
    private String syncMode;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    /**
     * 认证类型
     */
    @Schema(description = "认证类型", allowableValues = {"SERVICE_ACCOUNT", "OAUTH2", "API_KEY"})
    private String authType;

    /**
     * 创建人
     */
    @Schema(description = "创建人", example = "1")
    private Long createdBy;

    /**
     * 创建时间范围 - 开始
     */
    @Schema(description = "创建时间范围 - 开始", example = "2025-01-01T00:00:00")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间范围 - 结束
     */
    @Schema(description = "创建时间范围 - 结束", example = "2025-01-31T23:59:59")
    private LocalDateTime createTimeEnd;

    /**
     * 更新时间范围 - 开始
     */
    @Schema(description = "更新时间范围 - 开始", example = "2025-01-01T00:00:00")
    private LocalDateTime updateTimeStart;

    /**
     * 更新时间范围 - 结束
     */
    @Schema(description = "更新时间范围 - 结束", example = "2025-01-31T23:59:59")
    private LocalDateTime updateTimeEnd;

    /**
     * 最后同步时间范围 - 开始
     */
    @Schema(description = "最后同步时间范围 - 开始", example = "2025-01-01T00:00:00")
    private LocalDateTime lastSyncTimeStart;

    /**
     * 最后同步时间范围 - 结束
     */
    @Schema(description = "最后同步时间范围 - 结束", example = "2025-01-31T23:59:59")
    private LocalDateTime lastSyncTimeEnd;

    /**
     * 最后同步状态
     */
    @Schema(description = "最后同步状态", allowableValues = {"SUCCESS", "FAILED", "RUNNING", "CANCELLED"})
    private String lastSyncStatus;

    /**
     * 群组ID列表
     */
    @Schema(description = "群组ID列表")
    private List<Long> groupIds;

    /**
     * 配置ID列表
     */
    @Schema(description = "配置ID列表")
    private List<Long> configIds;

    /**
     * 排除的配置ID列表
     */
    @Schema(description = "排除的配置ID列表")
    private List<Long> excludeConfigIds;

    /**
     * 关键字搜索
     */
    @Schema(description = "关键字搜索", example = "记账")
    private String keyword;

    /**
     * 是否包含已删除
     */
    @Schema(description = "是否包含已删除", example = "false")
    private Boolean includeDeleted = false;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段", example = "createTime", allowableValues = {
            "configName", "createTime", "updateTime", "lastSyncTime", "syncCount"
    })
    private String sortField = "createTime";

    /**
     * 排序方向
     */
    @Schema(description = "排序方向", example = "DESC", allowableValues = {"ASC", "DESC"})
    private String sortDirection = "DESC";

    /**
     * 高级过滤条件
     */
    @Schema(description = "高级过滤条件")
    private AdvancedFilter advancedFilter;

    /**
     * 高级过滤条件
     */
    @Data
    @Schema(description = "高级过滤条件")
    public static class AdvancedFilter {

        /**
         * 同步成功率范围 - 最小值
         */
        @Schema(description = "同步成功率范围 - 最小值", example = "0.8")
        private Double minSuccessRate;

        /**
         * 同步成功率范围 - 最大值
         */
        @Schema(description = "同步成功率范围 - 最大值", example = "1.0")
        private Double maxSuccessRate;

        /**
         * 同步次数范围 - 最小值
         */
        @Schema(description = "同步次数范围 - 最小值", example = "1")
        private Integer minSyncCount;

        /**
         * 同步次数范围 - 最大值
         */
        @Schema(description = "同步次数范围 - 最大值", example = "1000")
        private Integer maxSyncCount;

        /**
         * 平均同步耗时范围 - 最小值（秒）
         */
        @Schema(description = "平均同步耗时范围 - 最小值（秒）", example = "1")
        private Integer minAvgSyncDuration;

        /**
         * 平均同步耗时范围 - 最大值（秒）
         */
        @Schema(description = "平均同步耗时范围 - 最大值（秒）", example = "300")
        private Integer maxAvgSyncDuration;

        /**
         * 是否有错误记录
         */
        @Schema(description = "是否有错误记录", example = "false")
        private Boolean hasErrors;

        /**
         * 是否活跃（最近30天内有同步）
         */
        @Schema(description = "是否活跃（最近30天内有同步）", example = "true")
        private Boolean isActive;

        /**
         * 配置版本号
         */
        @Schema(description = "配置版本号", example = "1")
        private Integer configVersion;

        /**
         * 是否有备份
         */
        @Schema(description = "是否有备份", example = "true")
        private Boolean hasBackup;

        /**
         * 自定义标签
         */
        @Schema(description = "自定义标签")
        private List<String> customTags;
    }
}
