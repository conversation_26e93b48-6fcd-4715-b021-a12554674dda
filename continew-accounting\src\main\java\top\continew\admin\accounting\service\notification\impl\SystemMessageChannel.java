package top.continew.admin.accounting.service.notification.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.continew.admin.accounting.enums.NotificationChannelEnum;
import top.continew.admin.accounting.model.entity.NotificationDO;
import top.continew.admin.accounting.service.notification.AbstractNotificationChannel;
import top.continew.admin.system.model.req.MessageReq;
import top.continew.admin.system.service.MessageService;
import top.continew.starter.core.util.validate.CheckUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统消息通知渠道
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SystemMessageChannel extends AbstractNotificationChannel {

    private final MessageService messageService;

    @Override
    public NotificationChannelEnum getChannelType() {
        return NotificationChannelEnum.SYSTEM_MESSAGE;
    }

    @Override
    public boolean supports(String notificationType) {
        // 系统消息支持所有通知类型
        return true;
    }

    @Override
    protected Map<String, Object> doSendNotification(NotificationDO notification, List<Long> targetUsers) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 构建系统消息请求
            MessageReq messageReq = buildMessageRequest(notification);
            
            // 转换用户ID列表为字符串列表
            List<String> userIdList = targetUsers.stream()
                    .map(String::valueOf)
                    .collect(Collectors.toList());
            
            // 发送系统消息
            messageService.add(messageReq, userIdList);
            
            result.put("success", true);
            result.put("messageId", messageReq.getId());
            result.put("targetCount", targetUsers.size());
            result.put("channel", getChannelType().getCode());
            
            log.info("系统消息发送成功: notificationId={}, targetUsers={}", 
                    notification.getId(), targetUsers.size());
            
        } catch (Exception e) {
            log.error("系统消息发送失败: notificationId={}", notification.getId(), e);
            result.put("success", false);
            result.put("errorMessage", e.getMessage());
        }
        
        return result;
    }

    @Override
    protected Map<String, String> doFormatMessage(NotificationDO notification, Long targetUser) {
        Map<String, String> result = new HashMap<>();
        
        // 系统消息格式化
        String title = notification.getTitle();
        String content = notification.getContent();
        
        // 添加通知类型前缀
        if (StrUtil.isNotBlank(notification.getNotificationType())) {
            String typePrefix = getTypePrefix(notification.getNotificationType());
            if (StrUtil.isNotBlank(typePrefix)) {
                title = typePrefix + title;
            }
        }
        
        result.put("title", title);
        result.put("content", content);
        result.put("type", "SYSTEM");
        
        // 添加跳转路径
        String path = generatePath(notification);
        if (StrUtil.isNotBlank(path)) {
            result.put("path", path);
        }
        
        return result;
    }

    @Override
    public Map<String, Object> testConnection(Map<String, Object> config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 测试系统消息服务是否可用
            CheckUtils.throwIfNull(messageService, "系统消息服务不可用");
            
            result.put("success", true);
            result.put("message", "系统消息渠道连接正常");
            result.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("errorMessage", e.getMessage());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getChannelStatus() {
        Map<String, Object> status = new HashMap<>();
        
        status.put("channel", getChannelType().getCode());
        status.put("channelName", getChannelType().getName());
        status.put("enabled", isEnabled());
        status.put("available", messageService != null);
        status.put("description", "系统内部消息通知");
        
        return status;
    }

    @Override
    public Map<String, Object> validateConfig(Map<String, Object> config) {
        Map<String, Object> result = new HashMap<>();
        
        // 系统消息不需要额外配置
        result.put("valid", true);
        result.put("message", "系统消息渠道无需额外配置");
        
        return result;
    }

    @Override
    public String getUserContact(Long userId) {
        // 系统消息使用用户ID作为联系方式
        return String.valueOf(userId);
    }

    @Override
    public boolean isEnabled() {
        // 系统消息默认启用
        return true;
    }

    @Override
    public Map<String, Object> getSendLimits() {
        Map<String, Object> limits = new HashMap<>();
        
        // 系统消息限制
        limits.put("maxBatchSize", 1000);
        limits.put("maxDailyCount", 10000);
        limits.put("rateLimit", 100); // 每分钟100条
        
        return limits;
    }

    // ==================== 私有方法 ====================

    /**
     * 构建系统消息请求
     */
    private MessageReq buildMessageRequest(NotificationDO notification) {
        MessageReq messageReq = new MessageReq();
        
        // 设置消息类型
        messageReq.setType("SYSTEM");
        
        // 设置标题和内容
        messageReq.setTitle(notification.getTitle());
        messageReq.setContent(notification.getContent());
        
        // 设置跳转路径
        String path = generatePath(notification);
        if (StrUtil.isNotBlank(path)) {
            messageReq.setPath(path);
        }
        
        return messageReq;
    }

    /**
     * 获取通知类型前缀
     */
    private String getTypePrefix(String notificationType) {
        return switch (notificationType) {
            case "TRANSACTION" -> "【账单】";
            case "DEBT_REMINDER" -> "【债务】";
            case "BUDGET_WARNING" -> "【预算】";
            case "SUBSCRIPTION" -> "【订阅】";
            case "REPORT" -> "【报表】";
            case "SYNC" -> "【同步】";
            case "SECURITY" -> "【安全】";
            default -> "【系统】";
        };
    }

    /**
     * 生成跳转路径
     */
    private String generatePath(NotificationDO notification) {
        String notificationType = notification.getNotificationType();
        Map<String, Object> extraData = notification.getExtraData();
        
        if (extraData == null) {
            return null;
        }
        
        return switch (notificationType) {
            case "TRANSACTION" -> {
                Object transactionId = extraData.get("transactionId");
                yield transactionId != null ? "/accounting/transaction/" + transactionId : null;
            }
            case "DEBT_REMINDER" -> {
                Object debtId = extraData.get("debtId");
                yield debtId != null ? "/accounting/debt/" + debtId : null;
            }
            case "BUDGET_WARNING" -> "/accounting/budget";
            case "SUBSCRIPTION" -> "/accounting/subscription";
            case "REPORT" -> "/accounting/report";
            case "SYNC" -> "/accounting/sync";
            default -> null;
        };
    }

}
