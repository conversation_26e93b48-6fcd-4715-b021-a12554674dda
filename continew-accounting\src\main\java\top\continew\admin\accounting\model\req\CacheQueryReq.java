package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.admin.accounting.enums.CacheTypeEnum;
import top.continew.starter.extension.crud.model.query.PageQuery;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 缓存查询请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "缓存查询请求")
public class CacheQueryReq extends PageQuery {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 配置名称
     */
    @Schema(description = "配置名称", example = "用户信息缓存")
    private String configName;

    /**
     * 配置代码
     */
    @Schema(description = "配置代码", example = "USER_INFO")
    private String configCode;

    /**
     * 缓存类型
     */
    @Schema(description = "缓存类型", example = "BOTH")
    private CacheTypeEnum cacheType;

    /**
     * 缓存键前缀
     */
    @Schema(description = "缓存键前缀", example = "USER:")
    private String keyPrefix;

    /**
     * 配置状态
     */
    @Schema(description = "配置状态", example = "ENABLE")
    private String status;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 是否启用统计
     */
    @Schema(description = "是否启用统计", example = "true")
    private Boolean statisticsEnabled;

    /**
     * 是否启用监控
     */
    @Schema(description = "是否启用监控", example = "true")
    private Boolean monitorEnabled;

    /**
     * 是否启用热点数据识别
     */
    @Schema(description = "是否启用热点数据识别", example = "true")
    private Boolean hotspotDetection;

    /**
     * 创建时间开始
     */
    @Schema(description = "创建时间开始", example = "2025-01-01 00:00:00")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    @Schema(description = "创建时间结束", example = "2025-01-31 23:59:59")
    private LocalDateTime createTimeEnd;

    /**
     * 最后应用时间开始
     */
    @Schema(description = "最后应用时间开始", example = "2025-01-01 00:00:00")
    private LocalDateTime lastAppliedTimeStart;

    /**
     * 最后应用时间结束
     */
    @Schema(description = "最后应用时间结束", example = "2025-01-31 23:59:59")
    private LocalDateTime lastAppliedTimeEnd;

}
