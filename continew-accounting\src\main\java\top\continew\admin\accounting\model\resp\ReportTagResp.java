package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 报表标签统计响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "报表标签统计响应")
public class ReportTagResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 标签名称
     */
    @Schema(description = "标签名称", example = "工作")
    private String tagName;

    /**
     * 标签颜色
     */
    @Schema(description = "标签颜色", example = "#FF5722")
    private String tagColor;

    /**
     * 标签图标
     */
    @Schema(description = "标签图标", example = "icon-work")
    private String tagIcon;

    /**
     * 收入金额
     */
    @Schema(description = "收入金额", example = "2000.00")
    private BigDecimal incomeAmount;

    /**
     * 支出金额
     */
    @Schema(description = "支出金额", example = "1500.00")
    private BigDecimal expenseAmount;

    /**
     * 净收入
     */
    @Schema(description = "净收入", example = "500.00")
    private BigDecimal netAmount;

    /**
     * 交易笔数
     */
    @Schema(description = "交易笔数", example = "25")
    private Integer transactionCount;

    /**
     * 收入笔数
     */
    @Schema(description = "收入笔数", example = "10")
    private Integer incomeCount;

    /**
     * 支出笔数
     */
    @Schema(description = "支出笔数", example = "15")
    private Integer expenseCount;

    /**
     * 占比（百分比）
     */
    @Schema(description = "占比", example = "12.5")
    private BigDecimal percentage;

    /**
     * 平均金额
     */
    @Schema(description = "平均金额", example = "140.00")
    private BigDecimal avgAmount;
}
