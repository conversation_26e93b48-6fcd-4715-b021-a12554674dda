package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.extension.crud.model.entity.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 缓存统计实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("acc_cache_statistics")
public class CacheStatisticsDO extends BaseDO {

    /**
     * 缓存名称
     */
    private String cacheName;

    /**
     * 缓存键前缀
     */
    private String keyPrefix;

    /**
     * 统计时间
     */
    private LocalDateTime statisticsTime;

    /**
     * 统计周期（分钟）
     */
    private Integer statisticsPeriod;

    /**
     * 请求总数
     */
    private Long totalRequests;

    /**
     * 命中次数
     */
    private Long hitCount;

    /**
     * 未命中次数
     */
    private Long missCount;

    /**
     * 命中率
     */
    private BigDecimal hitRate;

    /**
     * 加载次数
     */
    private Long loadCount;

    /**
     * 加载异常次数
     */
    private Long loadExceptionCount;

    /**
     * 平均加载时间（毫秒）
     */
    private BigDecimal avgLoadTime;

    /**
     * 最大加载时间（毫秒）
     */
    private Long maxLoadTime;

    /**
     * 最小加载时间（毫秒）
     */
    private Long minLoadTime;

    /**
     * 淘汰次数
     */
    private Long evictionCount;

    /**
     * 淘汰权重
     */
    private Long evictionWeight;

    /**
     * 缓存大小
     */
    private Long cacheSize;

    /**
     * 缓存权重
     */
    private Long cacheWeight;

    /**
     * 本地缓存命中次数
     */
    private Long localHitCount;

    /**
     * 本地缓存未命中次数
     */
    private Long localMissCount;

    /**
     * 远程缓存命中次数
     */
    private Long remoteHitCount;

    /**
     * 远程缓存未命中次数
     */
    private Long remoteMissCount;

    /**
     * 本地缓存大小
     */
    private Long localCacheSize;

    /**
     * 远程缓存大小
     */
    private Long remoteCacheSize;

    /**
     * 穿透保护触发次数
     */
    private Long penetrationProtectCount;

    /**
     * 雪崩保护触发次数
     */
    private Long avalancheProtectCount;

    /**
     * 击穿保护触发次数
     */
    private Long breakdownProtectCount;

    /**
     * 自动刷新次数
     */
    private Long autoRefreshCount;

    /**
     * 预热次数
     */
    private Long preloadCount;

    /**
     * 热点数据识别次数
     */
    private Long hotspotDetectionCount;

    /**
     * 错误次数
     */
    private Long errorCount;

    /**
     * 错误率
     */
    private BigDecimal errorRate;

    /**
     * 平均响应时间（毫秒）
     */
    private BigDecimal avgResponseTime;

    /**
     * QPS（每秒查询数）
     */
    private BigDecimal qps;

    /**
     * TPS（每秒事务数）
     */
    private BigDecimal tps;

    /**
     * 内存使用量（字节）
     */
    private Long memoryUsage;

    /**
     * 网络流量（字节）
     */
    private Long networkTraffic;

    /**
     * 群组ID
     */
    private Long groupId;

    /**
     * 统计详情（JSON格式）
     */
    private String statisticsDetail;

}
