package top.continew.admin.bot.telegram;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.bots.TelegramLongPollingBot;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import org.telegram.telegrambots.meta.api.methods.updatingmessages.EditMessageText;
import org.telegram.telegrambots.meta.api.objects.Update;
import org.telegram.telegrambots.meta.api.objects.replykeyboard.InlineKeyboardMarkup;
import org.telegram.telegrambots.meta.api.objects.replykeyboard.buttons.InlineKeyboardButton;
import org.telegram.telegrambots.meta.exceptions.TelegramApiException;
import top.continew.admin.bot.config.TelegramBotConfig;
import top.continew.admin.bot.telegram.handler.TelegramCommandHandler;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * Telegram机器人服务
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "bot.telegram", name = "enabled", havingValue = "true")
public class TelegramBotService extends TelegramLongPollingBot {

    private final TelegramBotConfig telegramBotConfig;
    private final TelegramCommandHandler commandHandler;

    @PostConstruct
    public void init() {
        log.info("Telegram机器人服务初始化完成，Token: {}***",
                telegramBotConfig.getToken() != null ? telegramBotConfig.getToken().substring(0, 10) : "未配置");

        // 注册机器人到Telegram API
        try {
            org.telegram.telegrambots.meta.TelegramBotsApi botsApi =
                new org.telegram.telegrambots.meta.TelegramBotsApi(org.telegram.telegrambots.updatesreceivers.DefaultBotSession.class);
            botsApi.registerBot(this);
            log.info("Telegram机器人注册成功");
        } catch (Exception e) {
            log.error("Telegram机器人注册失败", e);
        }
    }

    @Override
    public String getBotUsername() {
        return "ContiNewAccountingBot";
    }

    @Override
    public String getBotToken() {
        return telegramBotConfig.getToken();
    }

    @Override
    public void onUpdateReceived(Update update) {
        try {
            if (update.hasMessage() && update.getMessage().hasText()) {
                handleTextMessage(update);
            } else if (update.hasCallbackQuery()) {
                handleCallbackQuery(update);
            } else if (update.hasInlineQuery()) {
                handleInlineQuery(update);
            }
        } catch (Exception e) {
            log.error("处理Telegram更新失败", e);
            sendErrorMessage(update, "处理消息时发生错误，请稍后重试");
        }
    }

    /**
     * 处理文本消息
     */
    private void handleTextMessage(Update update) {
        String chatId = update.getMessage().getChatId().toString();
        String messageText = update.getMessage().getText();
        Long userId = update.getMessage().getFrom().getId();
        String username = update.getMessage().getFrom().getUserName();

        log.info("收到Telegram消息 - 群组: {}, 用户: {}({}), 内容: {}", 
                chatId, username, userId, messageText);

        // 委托给命令处理器
        commandHandler.handleCommand(chatId, userId, username, messageText, update);
    }

    /**
     * 处理回调查询
     */
    private void handleCallbackQuery(Update update) {
        String chatId = update.getCallbackQuery().getMessage().getChatId().toString();
        String callbackData = update.getCallbackQuery().getData();
        Long userId = update.getCallbackQuery().getFrom().getId();
        String username = update.getCallbackQuery().getFrom().getUserName();

        log.info("收到Telegram回调 - 群组: {}, 用户: {}({}), 数据: {}", 
                chatId, username, userId, callbackData);

        // 委托给命令处理器
        commandHandler.handleCallback(chatId, userId, username, callbackData, update);
    }

    /**
     * 处理内联查询
     */
    private void handleInlineQuery(Update update) {
        String query = update.getInlineQuery().getQuery();
        Long userId = update.getInlineQuery().getFrom().getId();
        String username = update.getInlineQuery().getFrom().getUserName();

        log.info("收到Telegram内联查询 - 用户: {}({}), 查询: {}", username, userId, query);

        // 委托给命令处理器
        commandHandler.handleInlineQuery(userId, username, query, update);
    }

    /**
     * 发送文本消息
     */
    public void sendMessage(String chatId, String text) {
        SendMessage message = new SendMessage();
        message.setChatId(chatId);
        message.setText(text);
        message.setParseMode("HTML");

        try {
            execute(message);
            log.debug("发送Telegram消息成功 - 群组: {}", chatId);
        } catch (TelegramApiException e) {
            log.error("发送Telegram消息失败 - 群组: {}, 错误: {}", chatId, e.getMessage());
        }
    }

    /**
     * 发送带键盘的消息
     */
    public void sendMessageWithKeyboard(String chatId, String text, InlineKeyboardMarkup keyboard) {
        SendMessage message = new SendMessage();
        message.setChatId(chatId);
        message.setText(text);
        message.setParseMode("HTML");
        message.setReplyMarkup(keyboard);

        try {
            execute(message);
            log.debug("发送Telegram键盘消息成功 - 群组: {}", chatId);
        } catch (TelegramApiException e) {
            log.error("发送Telegram键盘消息失败 - 群组: {}, 错误: {}", chatId, e.getMessage());
        }
    }

    /**
     * 编辑消息
     */
    public void editMessage(String chatId, Integer messageId, String text) {
        EditMessageText editMessage = new EditMessageText();
        editMessage.setChatId(chatId);
        editMessage.setMessageId(messageId);
        editMessage.setText(text);
        editMessage.setParseMode("HTML");

        try {
            execute(editMessage);
            log.debug("编辑Telegram消息成功 - 群组: {}, 消息ID: {}", chatId, messageId);
        } catch (TelegramApiException e) {
            log.error("编辑Telegram消息失败 - 群组: {}, 消息ID: {}, 错误: {}", chatId, messageId, e.getMessage());
        }
    }

    /**
     * 编辑消息和键盘
     */
    public void editMessageWithKeyboard(String chatId, Integer messageId, String text, InlineKeyboardMarkup keyboard) {
        EditMessageText editMessage = new EditMessageText();
        editMessage.setChatId(chatId);
        editMessage.setMessageId(messageId);
        editMessage.setText(text);
        editMessage.setParseMode("HTML");
        editMessage.setReplyMarkup(keyboard);

        try {
            execute(editMessage);
            log.debug("编辑Telegram键盘消息成功 - 群组: {}, 消息ID: {}", chatId, messageId);
        } catch (TelegramApiException e) {
            log.error("编辑Telegram键盘消息失败 - 群组: {}, 消息ID: {}, 错误: {}", chatId, messageId, e.getMessage());
        }
    }

    /**
     * 创建内联键盘
     */
    public InlineKeyboardMarkup createInlineKeyboard(List<List<InlineKeyboardButton>> buttons) {
        InlineKeyboardMarkup keyboard = new InlineKeyboardMarkup();
        keyboard.setKeyboard(buttons);
        return keyboard;
    }

    /**
     * 创建内联键盘按钮
     */
    public InlineKeyboardButton createInlineButton(String text, String callbackData) {
        InlineKeyboardButton button = new InlineKeyboardButton();
        button.setText(text);
        button.setCallbackData(callbackData);
        return button;
    }

    /**
     * 创建URL按钮
     */
    public InlineKeyboardButton createUrlButton(String text, String url) {
        InlineKeyboardButton button = new InlineKeyboardButton();
        button.setText(text);
        button.setUrl(url);
        return button;
    }

    /**
     * 发送错误消息
     */
    private void sendErrorMessage(Update update, String errorMessage) {
        String chatId = null;
        
        if (update.hasMessage()) {
            chatId = update.getMessage().getChatId().toString();
        } else if (update.hasCallbackQuery()) {
            chatId = update.getCallbackQuery().getMessage().getChatId().toString();
        }
        
        if (chatId != null) {
            sendMessage(chatId, "❌ " + errorMessage);
        }
    }

    /**
     * 创建确认键盘
     */
    public InlineKeyboardMarkup createConfirmKeyboard(String confirmData, String cancelData) {
        List<List<InlineKeyboardButton>> keyboard = new ArrayList<>();
        List<InlineKeyboardButton> row = new ArrayList<>();
        
        row.add(createInlineButton("✅ 确认", confirmData));
        row.add(createInlineButton("❌ 取消", cancelData));
        
        keyboard.add(row);
        return createInlineKeyboard(keyboard);
    }

    /**
     * 创建操作键盘
     */
    public InlineKeyboardMarkup createActionKeyboard(String... actions) {
        List<List<InlineKeyboardButton>> keyboard = new ArrayList<>();
        
        for (int i = 0; i < actions.length; i += 2) {
            List<InlineKeyboardButton> row = new ArrayList<>();
            row.add(createInlineButton(actions[i], actions[i + 1]));
            
            if (i + 2 < actions.length) {
                row.add(createInlineButton(actions[i + 2], actions[i + 3]));
                i += 2;
            }
            
            keyboard.add(row);
        }
        
        return createInlineKeyboard(keyboard);
    }
}
