package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.extension.crud.model.entity.BaseDO;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 订阅套餐实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "acc_subscription_plan", autoResultMap = true)
public class SubscriptionPlanDO extends BaseDO {

    /**
     * 套餐名称
     */
    private String name;

    /**
     * 套餐代码
     */
    private String code;

    /**
     * 套餐描述
     */
    private String description;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 币种
     */
    private String currency;

    /**
     * 计费周期(MONTHLY/YEARLY)
     */
    private String billingCycle;

    /**
     * 功能特性
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> features;

    /**
     * 使用限制
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> limits;

    /**
     * 试用天数
     */
    private Integer trialDays;

    /**
     * 是否热门(0:否 1:是)
     */
    private Boolean isPopular;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 状态(0:禁用 1:启用)
     */
    private Integer status;
}
