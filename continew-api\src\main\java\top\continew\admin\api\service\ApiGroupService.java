package top.continew.admin.api.service;

import top.continew.admin.accounting.model.query.GroupQuery;
import top.continew.admin.api.model.req.ApiGroupCreateReq;
import top.continew.admin.api.model.req.ApiGroupUpdateReq;
import top.continew.admin.api.model.resp.ApiGroupResp;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;

import java.util.List;

/**
 * API群组服务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface ApiGroupService {

    /**
     * 分页查询群组
     */
    PageResp<ApiGroupResp> page(GroupQuery query, PageQuery pageQuery);

    /**
     * 查询群组列表
     */
    List<ApiGroupResp> list(GroupQuery query);

    /**
     * 查询群组详情
     */
    ApiGroupResp get(Long id);

    /**
     * 创建群组
     */
    Long create(ApiGroupCreateReq req);

    /**
     * 更新群组
     */
    void update(ApiGroupUpdateReq req, Long id);

    /**
     * 删除群组
     */
    void delete(List<Long> ids);

    /**
     * 加入群组
     */
    void joinGroup(Long groupId, String inviteCode);

    /**
     * 离开群组
     */
    void leaveGroup(Long groupId);

    /**
     * 获取群组成员
     */
    List<ApiGroupResp.MemberInfo> getMembers(Long groupId);

    /**
     * 添加群组成员
     */
    void addMember(Long groupId, Long userId, String role);

    /**
     * 移除群组成员
     */
    void removeMember(Long groupId, Long userId);

    /**
     * 更新成员角色
     */
    void updateMemberRole(Long groupId, Long userId, String role);

    /**
     * 生成邀请码
     */
    String generateInviteCode(Long groupId, int expireHours);

    /**
     * 获取群组统计
     */
    ApiGroupResp.GroupStatistics getStatistics(Long groupId);

    /**
     * 获取群组设置
     */
    ApiGroupResp.GroupSettings getSettings(Long groupId);

    /**
     * 更新群组设置
     */
    void updateSettings(Long groupId, ApiGroupResp.GroupSettings settings);

    /**
     * 搜索群组
     */
    List<ApiGroupResp> search(String keyword, int limit);

    /**
     * 获取用户群组
     */
    List<ApiGroupResp> getMyGroups();

    /**
     * 检查群组权限
     */
    List<String> checkPermissions(Long groupId);

    /**
     * 获取群组活动日志
     */
    PageResp<ApiGroupResp.ActivityLog> getActivityLogs(Long groupId, PageQuery pageQuery);

    /**
     * 导出群组数据
     */
    String exportData(Long groupId, String format, String dataType);
}
