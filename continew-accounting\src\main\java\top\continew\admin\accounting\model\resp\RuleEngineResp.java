package top.continew.admin.accounting.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 规则引擎响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "规则引擎响应")
public class RuleEngineResp {

    /**
     * 规则ID
     */
    @Schema(description = "规则ID", example = "1")
    private Long ruleId;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 规则名称
     */
    @Schema(description = "规则名称", example = "自动分类规则")
    private String ruleName;

    /**
     * 规则描述
     */
    @Schema(description = "规则描述", example = "根据关键词自动分类交易")
    private String ruleDescription;

    /**
     * 规则类型
     */
    @Schema(description = "规则类型", example = "AUTO_CATEGORY")
    private String ruleType;

    /**
     * 规则状态
     */
    @Schema(description = "规则状态", example = "ACTIVE")
    private String ruleStatus;

    /**
     * 规则优先级
     */
    @Schema(description = "规则优先级", example = "100")
    private Integer priority;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    /**
     * 版本号
     */
    @Schema(description = "版本号", example = "1.0")
    private String version;

    /**
     * 触发类型
     */
    @Schema(description = "触发类型", example = "EVENT")
    private String triggerType;

    /**
     * 事件类型
     */
    @Schema(description = "事件类型", example = "TRANSACTION_CREATED")
    private String eventType;

    /**
     * 执行次数
     */
    @Schema(description = "执行次数", example = "100")
    private Integer executionCount;

    /**
     * 成功次数
     */
    @Schema(description = "成功次数", example = "95")
    private Integer successCount;

    /**
     * 失败次数
     */
    @Schema(description = "失败次数", example = "5")
    private Integer failureCount;

    /**
     * 成功率
     */
    @Schema(description = "成功率", example = "0.95")
    private Double successRate;

    /**
     * 平均执行时间（毫秒）
     */
    @Schema(description = "平均执行时间（毫秒）", example = "150")
    private Long avgExecutionTime;

    /**
     * 最大执行时间（毫秒）
     */
    @Schema(description = "最大执行时间（毫秒）", example = "500")
    private Long maxExecutionTime;

    /**
     * 最小执行时间（毫秒）
     */
    @Schema(description = "最小执行时间（毫秒）", example = "50")
    private Long minExecutionTime;

    /**
     * 最后执行时间
     */
    @Schema(description = "最后执行时间", example = "2024-01-01 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastExecutionTime;

    /**
     * 最后执行状态
     */
    @Schema(description = "最后执行状态", example = "SUCCESS")
    private String lastExecutionStatus;

    /**
     * 最后执行结果
     */
    @Schema(description = "最后执行结果", example = "成功处理1条记录")
    private String lastExecutionResult;

    /**
     * 下次执行时间
     */
    @Schema(description = "下次执行时间", example = "2024-01-01 13:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime nextExecutionTime;

    /**
     * 标签列表
     */
    @Schema(description = "标签列表", example = "[\"自动化\", \"分类\"]")
    private List<String> tags;

    /**
     * 扩展属性
     */
    @Schema(description = "扩展属性")
    private Map<String, Object> attributes;

    /**
     * 执行统计
     */
    @Schema(description = "执行统计")
    private ExecutionStats executionStats;

    /**
     * 性能指标
     */
    @Schema(description = "性能指标")
    private PerformanceMetrics performanceMetrics;

    /**
     * 调度信息
     */
    @Schema(description = "调度信息")
    private ScheduleInfo scheduleInfo;

    /**
     * 通知信息
     */
    @Schema(description = "通知信息")
    private NotificationInfo notificationInfo;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2024-01-01 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2024-01-01 11:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID", example = "1")
    private Long createdBy;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名", example = "张三")
    private String createdByName;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID", example = "1")
    private Long updatedBy;

    /**
     * 更新人姓名
     */
    @Schema(description = "更新人姓名", example = "李四")
    private String updatedByName;

    /**
     * 执行统计
     */
    @Data
    @Schema(description = "执行统计")
    public static class ExecutionStats {

        /**
         * 今日执行次数
         */
        @Schema(description = "今日执行次数", example = "10")
        private Integer todayExecutions;

        /**
         * 本周执行次数
         */
        @Schema(description = "本周执行次数", example = "50")
        private Integer weekExecutions;

        /**
         * 本月执行次数
         */
        @Schema(description = "本月执行次数", example = "200")
        private Integer monthExecutions;

        /**
         * 今日成功次数
         */
        @Schema(description = "今日成功次数", example = "9")
        private Integer todaySuccesses;

        /**
         * 本周成功次数
         */
        @Schema(description = "本周成功次数", example = "48")
        private Integer weekSuccesses;

        /**
         * 本月成功次数
         */
        @Schema(description = "本月成功次数", example = "190")
        private Integer monthSuccesses;

        /**
         * 今日成功率
         */
        @Schema(description = "今日成功率", example = "0.9")
        private Double todaySuccessRate;

        /**
         * 本周成功率
         */
        @Schema(description = "本周成功率", example = "0.96")
        private Double weekSuccessRate;

        /**
         * 本月成功率
         */
        @Schema(description = "本月成功率", example = "0.95")
        private Double monthSuccessRate;

        /**
         * 执行趋势
         */
        @Schema(description = "执行趋势", example = "INCREASING")
        private String executionTrend;

        /**
         * 是否活跃
         */
        @Schema(description = "是否活跃", example = "true")
        private Boolean isActive;
    }

    /**
     * 性能指标
     */
    @Data
    @Schema(description = "性能指标")
    public static class PerformanceMetrics {

        /**
         * 响应时间分布
         */
        @Schema(description = "响应时间分布")
        private Map<String, Integer> responseTimeDistribution;

        /**
         * 错误率
         */
        @Schema(description = "错误率", example = "0.05")
        private Double errorRate;

        /**
         * 吞吐量（次/分钟）
         */
        @Schema(description = "吞吐量（次/分钟）", example = "10.5")
        private Double throughput;

        /**
         * 资源使用率
         */
        @Schema(description = "资源使用率")
        private Map<String, Double> resourceUsage;

        /**
         * 性能等级
         */
        @Schema(description = "性能等级", example = "GOOD")
        private String performanceGrade;

        /**
         * 性能评分
         */
        @Schema(description = "性能评分", example = "85")
        private Integer performanceScore;
    }

    /**
     * 调度信息
     */
    @Data
    @Schema(description = "调度信息")
    public static class ScheduleInfo {

        /**
         * 是否启用调度
         */
        @Schema(description = "是否启用调度", example = "true")
        private Boolean enabled;

        /**
         * 调度类型
         */
        @Schema(description = "调度类型", example = "CRON")
        private String scheduleType;

        /**
         * Cron表达式
         */
        @Schema(description = "Cron表达式", example = "0 0 * * * ?")
        private String cronExpression;

        /**
         * 固定间隔（秒）
         */
        @Schema(description = "固定间隔（秒）", example = "3600")
        private Integer fixedIntervalSeconds;

        /**
         * 调度状态
         */
        @Schema(description = "调度状态", example = "RUNNING")
        private String scheduleStatus;

        /**
         * 下次执行时间
         */
        @Schema(description = "下次执行时间", example = "2024-01-01 13:00:00")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime nextExecutionTime;

        /**
         * 最后执行时间
         */
        @Schema(description = "最后执行时间", example = "2024-01-01 12:00:00")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastExecutionTime;
    }

    /**
     * 通知信息
     */
    @Data
    @Schema(description = "通知信息")
    public static class NotificationInfo {

        /**
         * 是否启用通知
         */
        @Schema(description = "是否启用通知", example = "true")
        private Boolean enabled;

        /**
         * 通知渠道
         */
        @Schema(description = "通知渠道", example = "[\"EMAIL\", \"SMS\"]")
        private List<String> channels;

        /**
         * 通知模板
         */
        @Schema(description = "通知模板", example = "规则执行通知模板")
        private String template;

        /**
         * 通知接收人数量
         */
        @Schema(description = "通知接收人数量", example = "3")
        private Integer recipientCount;

        /**
         * 通知条件
         */
        @Schema(description = "通知条件", example = "ON_FAILURE")
        private String notificationCondition;

        /**
         * 最后通知时间
         */
        @Schema(description = "最后通知时间", example = "2024-01-01 12:00:00")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastNotificationTime;

        /**
         * 通知发送次数
         */
        @Schema(description = "通知发送次数", example = "5")
        private Integer notificationCount;
    }
}
