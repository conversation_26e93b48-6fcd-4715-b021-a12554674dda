package top.continew.admin.accounting.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 预算执行情况响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "预算执行情况响应")
public class BudgetExecutionResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 预算ID
     */
    @Schema(description = "预算ID", example = "budget_123456")
    private String budgetId;

    /**
     * 预算名称
     */
    @Schema(description = "预算名称", example = "2025年度预算")
    private String budgetName;

    /**
     * 执行概览
     */
    @Schema(description = "执行概览")
    private ExecutionOverview overview;

    /**
     * 分配执行情况
     */
    @Schema(description = "分配执行情况")
    private List<AllocationExecution> allocationExecutions;

    /**
     * 时间维度执行情况
     */
    @Schema(description = "时间维度执行情况")
    private List<TimeExecution> timeExecutions;

    /**
     * 预警信息
     */
    @Schema(description = "预警信息")
    private List<AlertInfo> alerts;

    /**
     * 执行趋势
     */
    @Schema(description = "执行趋势")
    private ExecutionTrend trend;

    /**
     * 对比分析
     */
    @Schema(description = "对比分析")
    private ComparisonAnalysis comparison;

    /**
     * 预测分析
     */
    @Schema(description = "预测分析")
    private ForecastAnalysis forecast;

    /**
     * 生成时间
     */
    @Schema(description = "生成时间", example = "2025-01-01 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime generatedAt;

    /**
     * 执行概览
     */
    @Data
    @Schema(description = "执行概览")
    public static class ExecutionOverview implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 总预算金额
         */
        @Schema(description = "总预算金额", example = "100000.00")
        private BigDecimal totalBudget;

        /**
         * 已使用金额
         */
        @Schema(description = "已使用金额", example = "35000.00")
        private BigDecimal usedAmount;

        /**
         * 剩余金额
         */
        @Schema(description = "剩余金额", example = "65000.00")
        private BigDecimal remainingAmount;

        /**
         * 使用率
         */
        @Schema(description = "使用率", example = "0.35")
        private BigDecimal usageRate;

        /**
         * 预算期间总天数
         */
        @Schema(description = "预算期间总天数", example = "365")
        private Long totalDays;

        /**
         * 已过天数
         */
        @Schema(description = "已过天数", example = "65")
        private Long elapsedDays;

        /**
         * 剩余天数
         */
        @Schema(description = "剩余天数", example = "300")
        private Long remainingDays;

        /**
         * 时间进度
         */
        @Schema(description = "时间进度", example = "0.18")
        private BigDecimal timeProgress;

        /**
         * 执行状态
         */
        @Schema(description = "执行状态", example = "NORMAL")
        private String executionStatus;

        /**
         * 执行状态名称
         */
        @Schema(description = "执行状态名称", example = "正常")
        private String executionStatusName;

        /**
         * 平均每日支出
         */
        @Schema(description = "平均每日支出", example = "538.46")
        private BigDecimal avgDailyExpense;

        /**
         * 预计每日支出
         */
        @Schema(description = "预计每日支出", example = "273.97")
        private BigDecimal expectedDailyExpense;

        /**
         * 支出偏差率
         */
        @Schema(description = "支出偏差率", example = "0.96")
        private BigDecimal expenseDeviationRate;

        /**
         * 健康度评分
         */
        @Schema(description = "健康度评分", example = "75.5")
        private BigDecimal healthScore;

        /**
         * 风险等级
         */
        @Schema(description = "风险等级", example = "MEDIUM")
        private String riskLevel;

        /**
         * 风险等级名称
         */
        @Schema(description = "风险等级名称", example = "中等风险")
        private String riskLevelName;
    }

    /**
     * 分配执行情况
     */
    @Data
    @Schema(description = "分配执行情况")
    public static class AllocationExecution implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 分配ID
         */
        @Schema(description = "分配ID", example = "alloc_123456")
        private String allocationId;

        /**
         * 分配类型
         */
        @Schema(description = "分配类型", example = "CATEGORY")
        private String allocationType;

        /**
         * 分配类型名称
         */
        @Schema(description = "分配类型名称", example = "分类")
        private String allocationTypeName;

        /**
         * 目标ID
         */
        @Schema(description = "目标ID", example = "1")
        private Long targetId;

        /**
         * 目标名称
         */
        @Schema(description = "目标名称", example = "餐饮消费")
        private String targetName;

        /**
         * 预算金额
         */
        @Schema(description = "预算金额", example = "10000.00")
        private BigDecimal budgetAmount;

        /**
         * 已使用金额
         */
        @Schema(description = "已使用金额", example = "3500.00")
        private BigDecimal usedAmount;

        /**
         * 剩余金额
         */
        @Schema(description = "剩余金额", example = "6500.00")
        private BigDecimal remainingAmount;

        /**
         * 使用率
         */
        @Schema(description = "使用率", example = "0.35")
        private BigDecimal usageRate;

        /**
         * 交易数量
         */
        @Schema(description = "交易数量", example = "25")
        private Long transactionCount;

        /**
         * 平均交易金额
         */
        @Schema(description = "平均交易金额", example = "140.00")
        private BigDecimal avgTransactionAmount;

        /**
         * 最大交易金额
         */
        @Schema(description = "最大交易金额", example = "500.00")
        private BigDecimal maxTransactionAmount;

        /**
         * 最小交易金额
         */
        @Schema(description = "最小交易金额", example = "15.00")
        private BigDecimal minTransactionAmount;

        /**
         * 执行状态
         */
        @Schema(description = "执行状态", example = "NORMAL")
        private String executionStatus;

        /**
         * 执行状态名称
         */
        @Schema(description = "执行状态名称", example = "正常")
        private String executionStatusName;

        /**
         * 预警级别
         */
        @Schema(description = "预警级别", example = "NONE")
        private String alertLevel;

        /**
         * 预警级别名称
         */
        @Schema(description = "预警级别名称", example = "无预警")
        private String alertLevelName;

        /**
         * 趋势
         */
        @Schema(description = "趋势", example = "STABLE")
        private String trend;

        /**
         * 趋势名称
         */
        @Schema(description = "趋势名称", example = "稳定")
        private String trendName;

        /**
         * 预计完成时间
         */
        @Schema(description = "预计完成时间", example = "2025-10-15")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate estimatedCompletionDate;
    }

    /**
     * 时间维度执行情况
     */
    @Data
    @Schema(description = "时间维度执行情况")
    public static class TimeExecution implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 时间点
         */
        @Schema(description = "时间点", example = "2025-01")
        private String timePoint;

        /**
         * 时间点名称
         */
        @Schema(description = "时间点名称", example = "2025年1月")
        private String timePointName;

        /**
         * 预算金额
         */
        @Schema(description = "预算金额", example = "8333.33")
        private BigDecimal budgetAmount;

        /**
         * 实际支出
         */
        @Schema(description = "实际支出", example = "7500.00")
        private BigDecimal actualExpense;

        /**
         * 差异金额
         */
        @Schema(description = "差异金额", example = "833.33")
        private BigDecimal variance;

        /**
         * 差异率
         */
        @Schema(description = "差异率", example = "0.10")
        private BigDecimal varianceRate;

        /**
         * 累计预算
         */
        @Schema(description = "累计预算", example = "8333.33")
        private BigDecimal cumulativeBudget;

        /**
         * 累计支出
         */
        @Schema(description = "累计支出", example = "7500.00")
        private BigDecimal cumulativeExpense;

        /**
         * 累计差异
         */
        @Schema(description = "累计差异", example = "833.33")
        private BigDecimal cumulativeVariance;

        /**
         * 执行状态
         */
        @Schema(description = "执行状态", example = "UNDER_BUDGET")
        private String executionStatus;

        /**
         * 执行状态名称
         */
        @Schema(description = "执行状态名称", example = "低于预算")
        private String executionStatusName;
    }

    /**
     * 预警信息
     */
    @Data
    @Schema(description = "预警信息")
    public static class AlertInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 预警ID
         */
        @Schema(description = "预警ID", example = "alert_123456")
        private String alertId;

        /**
         * 预警类型
         */
        @Schema(description = "预警类型", example = "USAGE_THRESHOLD")
        private String alertType;

        /**
         * 预警类型名称
         */
        @Schema(description = "预警类型名称", example = "使用率阈值")
        private String alertTypeName;

        /**
         * 预警级别
         */
        @Schema(description = "预警级别", example = "WARNING")
        private String alertLevel;

        /**
         * 预警级别名称
         */
        @Schema(description = "预警级别名称", example = "警告")
        private String alertLevelName;

        /**
         * 预警消息
         */
        @Schema(description = "预警消息", example = "餐饮消费预算使用率已达到80%")
        private String message;

        /**
         * 触发时间
         */
        @Schema(description = "触发时间", example = "2025-01-01 10:00:00")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime triggeredAt;

        /**
         * 相关分配ID
         */
        @Schema(description = "相关分配ID", example = "alloc_123456")
        private String relatedAllocationId;

        /**
         * 相关分配名称
         */
        @Schema(description = "相关分配名称", example = "餐饮消费")
        private String relatedAllocationName;

        /**
         * 当前值
         */
        @Schema(description = "当前值", example = "0.80")
        private BigDecimal currentValue;

        /**
         * 阈值
         */
        @Schema(description = "阈值", example = "0.80")
        private BigDecimal threshold;

        /**
         * 是否已处理
         */
        @Schema(description = "是否已处理", example = "false")
        private Boolean handled;

        /**
         * 处理时间
         */
        @Schema(description = "处理时间", example = "2025-01-01 11:00:00")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime handledAt;

        /**
         * 处理人
         */
        @Schema(description = "处理人", example = "张三")
        private String handledBy;
    }

    /**
     * 执行趋势
     */
    @Data
    @Schema(description = "执行趋势")
    public static class ExecutionTrend implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 趋势方向
         */
        @Schema(description = "趋势方向", example = "INCREASING")
        private String direction;

        /**
         * 趋势方向名称
         */
        @Schema(description = "趋势方向名称", example = "上升")
        private String directionName;

        /**
         * 趋势强度
         */
        @Schema(description = "趋势强度", example = "MODERATE")
        private String intensity;

        /**
         * 趋势强度名称
         */
        @Schema(description = "趋势强度名称", example = "中等")
        private String intensityName;

        /**
         * 变化率
         */
        @Schema(description = "变化率", example = "0.15")
        private BigDecimal changeRate;

        /**
         * 趋势描述
         */
        @Schema(description = "趋势描述", example = "支出呈现中等强度上升趋势")
        private String description;

        /**
         * 趋势数据点
         */
        @Schema(description = "趋势数据点")
        private List<TrendDataPoint> dataPoints;

        @Data
        @Schema(description = "趋势数据点")
        public static class TrendDataPoint implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 时间点
             */
            @Schema(description = "时间点", example = "2025-01-01")
            @JsonFormat(pattern = "yyyy-MM-dd")
            private LocalDate timePoint;

            /**
             * 数值
             */
            @Schema(description = "数值", example = "1500.00")
            private BigDecimal value;

            /**
             * 累计数值
             */
            @Schema(description = "累计数值", example = "7500.00")
            private BigDecimal cumulativeValue;

            /**
             * 移动平均值
             */
            @Schema(description = "移动平均值", example = "1450.00")
            private BigDecimal movingAverage;
        }
    }

    /**
     * 对比分析
     */
    @Data
    @Schema(description = "对比分析")
    public static class ComparisonAnalysis implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 同期对比
         */
        @Schema(description = "同期对比")
        private PeriodComparison periodComparison;

        /**
         * 目标对比
         */
        @Schema(description = "目标对比")
        private TargetComparison targetComparison;

        /**
         * 行业对比
         */
        @Schema(description = "行业对比")
        private IndustryComparison industryComparison;

        @Data
        @Schema(description = "同期对比")
        public static class PeriodComparison implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 对比期间
             */
            @Schema(description = "对比期间", example = "2024年同期")
            private String comparisonPeriod;

            /**
             * 当前值
             */
            @Schema(description = "当前值", example = "35000.00")
            private BigDecimal currentValue;

            /**
             * 对比值
             */
            @Schema(description = "对比值", example = "30000.00")
            private BigDecimal comparisonValue;

            /**
             * 差异金额
             */
            @Schema(description = "差异金额", example = "5000.00")
            private BigDecimal variance;

            /**
             * 差异率
             */
            @Schema(description = "差异率", example = "0.167")
            private BigDecimal varianceRate;

            /**
             * 对比结果
             */
            @Schema(description = "对比结果", example = "HIGHER")
            private String result;

            /**
             * 对比结果名称
             */
            @Schema(description = "对比结果名称", example = "高于同期")
            private String resultName;
        }

        @Data
        @Schema(description = "目标对比")
        public static class TargetComparison implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 目标值
             */
            @Schema(description = "目标值", example = "40000.00")
            private BigDecimal targetValue;

            /**
             * 当前值
             */
            @Schema(description = "当前值", example = "35000.00")
            private BigDecimal currentValue;

            /**
             * 完成率
             */
            @Schema(description = "完成率", example = "0.875")
            private BigDecimal completionRate;

            /**
             * 差距
             */
            @Schema(description = "差距", example = "5000.00")
            private BigDecimal gap;

            /**
             * 达成状态
             */
            @Schema(description = "达成状态", example = "ON_TRACK")
            private String achievementStatus;

            /**
             * 达成状态名称
             */
            @Schema(description = "达成状态名称", example = "按计划进行")
            private String achievementStatusName;
        }

        @Data
        @Schema(description = "行业对比")
        public static class IndustryComparison implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 行业平均值
             */
            @Schema(description = "行业平均值", example = "32000.00")
            private BigDecimal industryAverage;

            /**
             * 当前值
             */
            @Schema(description = "当前值", example = "35000.00")
            private BigDecimal currentValue;

            /**
             * 相对位置
             */
            @Schema(description = "相对位置", example = "ABOVE_AVERAGE")
            private String relativePosition;

            /**
             * 相对位置名称
             */
            @Schema(description = "相对位置名称", example = "高于平均")
            private String relativePositionName;

            /**
             * 百分位排名
             */
            @Schema(description = "百分位排名", example = "75")
            private Integer percentileRank;
        }
    }

    /**
     * 预测分析
     */
    @Data
    @Schema(description = "预测分析")
    public static class ForecastAnalysis implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 预测模型
         */
        @Schema(description = "预测模型", example = "LINEAR_REGRESSION")
        private String forecastModel;

        /**
         * 预测模型名称
         */
        @Schema(description = "预测模型名称", example = "线性回归")
        private String forecastModelName;

        /**
         * 预测准确度
         */
        @Schema(description = "预测准确度", example = "0.85")
        private BigDecimal accuracy;

        /**
         * 预测期末支出
         */
        @Schema(description = "预测期末支出", example = "95000.00")
        private BigDecimal forecastEndExpense;

        /**
         * 预测完成时间
         */
        @Schema(description = "预测完成时间", example = "2025-11-15")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate forecastCompletionDate;

        /**
         * 预测超支金额
         */
        @Schema(description = "预测超支金额", example = "0.00")
        private BigDecimal forecastOverspend;

        /**
         * 预测超支概率
         */
        @Schema(description = "预测超支概率", example = "0.15")
        private BigDecimal overspendProbability;

        /**
         * 建议措施
         */
        @Schema(description = "建议措施")
        private List<String> recommendations;

        /**
         * 风险因素
         */
        @Schema(description = "风险因素")
        private List<String> riskFactors;

        /**
         * 预测数据点
         */
        @Schema(description = "预测数据点")
        private List<ForecastDataPoint> dataPoints;

        @Data
        @Schema(description = "预测数据点")
        public static class ForecastDataPoint implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 时间点
             */
            @Schema(description = "时间点", example = "2025-02-01")
            @JsonFormat(pattern = "yyyy-MM-dd")
            private LocalDate timePoint;

            /**
             * 预测值
             */
            @Schema(description = "预测值", example = "8500.00")
            private BigDecimal forecastValue;

            /**
             * 置信区间下限
             */
            @Schema(description = "置信区间下限", example = "7500.00")
            private BigDecimal lowerBound;

            /**
             * 置信区间上限
             */
            @Schema(description = "置信区间上限", example = "9500.00")
            private BigDecimal upperBound;

            /**
             * 置信度
             */
            @Schema(description = "置信度", example = "0.95")
            private BigDecimal confidence;
        }
    }
}
