package top.continew.admin.accounting.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.common.base.controller.BaseController;
import top.continew.admin.accounting.model.query.GroupQuery;
import top.continew.admin.accounting.model.req.GroupCreateReq;
import top.continew.admin.accounting.model.req.GroupUpdateReq;
import top.continew.admin.accounting.model.resp.GroupDetailResp;
import top.continew.admin.accounting.model.resp.GroupListResp;
import top.continew.admin.accounting.service.GroupService;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 群组管理 API
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "群组管理 API")
@Validated
@RestController
@RequiredArgsConstructor
@CrudRequestMapping(value = "/accounting/group", api = {Api.PAGE, Api.LIST, Api.GET, Api.CREATE, Api.UPDATE, Api.BATCH_DELETE, Api.EXPORT, Api.DICT})
public class GroupController extends BaseController<GroupService, GroupListResp, GroupDetailResp, GroupQuery, GroupCreateReq> {

    @Operation(summary = "更新群组信息", description = "更新群组信息")
    @Parameter(name = "id", description = "ID", example = "1", in = ParameterIn.PATH)
    @SaCheckPermission("accounting:group:update")
    @PutMapping("/{id}")
    public void update(@RequestBody @Valid GroupUpdateReq req, @PathVariable Long id) {
        baseService.update(req, id);
    }

    @Operation(summary = "获取用户群组列表", description = "获取当前用户的群组列表")
    @SaCheckPermission("accounting:group:list")
    @GetMapping("/user")
    public List<GroupListResp> getUserGroups() {
        // TODO: 从当前登录用户获取userId
        Long userId = 1L; // 临时硬编码，实际应从SecurityContext获取
        return baseService.getUserGroups(userId);
    }

    @Operation(summary = "添加群组成员", description = "添加群组成员")
    @Parameter(name = "id", description = "群组ID", example = "1", in = ParameterIn.PATH)
    @SaCheckPermission("accounting:group:addMember")
    @PostMapping("/{id}/member")
    public void addMember(@PathVariable Long id,
                         @RequestParam Long userId,
                         @RequestParam String platformUserId,
                         @RequestParam String nickname,
                         @RequestParam(required = false) Long inviterId) {
        baseService.addMember(id, userId, platformUserId, nickname, inviterId);
    }

    @Operation(summary = "移除群组成员", description = "移除群组成员")
    @Parameter(name = "id", description = "群组ID", example = "1", in = ParameterIn.PATH)
    @Parameter(name = "userId", description = "用户ID", example = "1", in = ParameterIn.PATH)
    @SaCheckPermission("accounting:group:removeMember")
    @DeleteMapping("/{id}/member/{userId}")
    public void removeMember(@PathVariable Long id, @PathVariable Long userId) {
        baseService.removeMember(id, userId);
    }

    @Operation(summary = "检查用户权限", description = "检查用户是否为群组成员或管理员")
    @Parameter(name = "id", description = "群组ID", example = "1", in = ParameterIn.PATH)
    @SaCheckPermission("accounting:group:checkPermission")
    @GetMapping("/{id}/permission")
    public GroupPermissionResp checkPermission(@PathVariable Long id, @RequestParam Long userId) {
        boolean isMember = baseService.isMember(id, userId);
        boolean isAdmin = baseService.isAdmin(id, userId);
        
        GroupPermissionResp resp = new GroupPermissionResp();
        resp.setMember(isMember);
        resp.setAdmin(isAdmin);
        return resp;
    }

    @Operation(summary = "检查交易限制", description = "检查群组交易次数是否超限")
    @Parameter(name = "id", description = "群组ID", example = "1", in = ParameterIn.PATH)
    @SaCheckPermission("accounting:group:checkLimit")
    @GetMapping("/{id}/limit")
    public GroupLimitResp checkTransactionLimit(@PathVariable Long id) {
        boolean exceeded = baseService.isTransactionLimitExceeded(id);
        
        GroupLimitResp resp = new GroupLimitResp();
        resp.setExceeded(exceeded);
        return resp;
    }

    /**
     * 群组权限响应
     */
    public static class GroupPermissionResp {
        private boolean isMember;
        private boolean isAdmin;

        public boolean isMember() {
            return isMember;
        }

        public void setMember(boolean member) {
            isMember = member;
        }

        public boolean isAdmin() {
            return isAdmin;
        }

        public void setAdmin(boolean admin) {
            isAdmin = admin;
        }
    }

    /**
     * 群组限制响应
     */
    public static class GroupLimitResp {
        private boolean exceeded;

        public boolean isExceeded() {
            return exceeded;
        }

        public void setExceeded(boolean exceeded) {
            this.exceeded = exceeded;
        }
    }
}
