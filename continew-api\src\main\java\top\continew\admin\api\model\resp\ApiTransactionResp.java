package top.continew.admin.api.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * API交易响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "API交易响应")
public class ApiTransactionResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Schema(description = "交易ID", example = "1")
    private Long id;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 群组名称
     */
    @Schema(description = "群组名称", example = "我的记账群组")
    private String groupName;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型", example = "EXPENSE")
    private String type;

    /**
     * 金额
     */
    @Schema(description = "交易金额", example = "100.50")
    private BigDecimal amount;

    /**
     * 货币
     */
    @Schema(description = "货币类型", example = "CNY")
    private String currency;

    /**
     * 描述
     */
    @Schema(description = "交易描述", example = "午餐费用")
    private String description;

    /**
     * 分类ID
     */
    @Schema(description = "分类ID", example = "1")
    private Long categoryId;

    /**
     * 分类名称
     */
    @Schema(description = "分类名称", example = "餐饮")
    private String categoryName;

    /**
     * 分类图标
     */
    @Schema(description = "分类图标", example = "🍽️")
    private String categoryIcon;

    /**
     * 钱包ID
     */
    @Schema(description = "钱包ID", example = "1")
    private Long walletId;

    /**
     * 钱包名称
     */
    @Schema(description = "钱包名称", example = "现金")
    private String walletName;

    /**
     * 目标钱包ID
     */
    @Schema(description = "目标钱包ID", example = "2")
    private Long targetWalletId;

    /**
     * 目标钱包名称
     */
    @Schema(description = "目标钱包名称", example = "银行卡")
    private String targetWalletName;

    /**
     * 交易时间
     */
    @Schema(description = "交易时间")
    private LocalDateTime transactionTime;

    /**
     * 创建者ID
     */
    @Schema(description = "创建者ID", example = "1")
    private Long creatorId;

    /**
     * 创建者名称
     */
    @Schema(description = "创建者名称", example = "张三")
    private String creatorName;

    /**
     * 标签列表
     */
    @Schema(description = "标签列表")
    private List<String> tags;

    /**
     * 参与者列表
     */
    @Schema(description = "参与者列表")
    private List<ParticipantInfo> participants;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "与同事聚餐")
    private String remark;

    /**
     * 位置信息
     */
    @Schema(description = "位置信息")
    private LocationInfo location;

    /**
     * 附件列表
     */
    @Schema(description = "附件列表")
    private List<AttachmentInfo> attachments;

    /**
     * 是否定期交易
     */
    @Schema(description = "是否定期交易", example = "false")
    private Boolean isRecurring;

    /**
     * 定期交易设置
     */
    @Schema(description = "定期交易设置")
    private RecurringInfo recurring;

    /**
     * 分摊信息
     */
    @Schema(description = "分摊信息")
    private SplitInfo split;

    /**
     * 交易状态
     */
    @Schema(description = "交易状态", example = "COMPLETED")
    private String status;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 参与者信息
     */
    @Data
    @Schema(description = "参与者信息")
    public static class ParticipantInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 用户ID
         */
        @Schema(description = "用户ID", example = "1")
        private Long userId;

        /**
         * 用户名
         */
        @Schema(description = "用户名", example = "张三")
        private String username;

        /**
         * 昵称
         */
        @Schema(description = "昵称", example = "小张")
        private String nickname;

        /**
         * 头像
         */
        @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
        private String avatar;
    }

    /**
     * 位置信息
     */
    @Data
    @Schema(description = "位置信息")
    public static class LocationInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 纬度
         */
        @Schema(description = "纬度", example = "39.9042")
        private Double latitude;

        /**
         * 经度
         */
        @Schema(description = "经度", example = "116.4074")
        private Double longitude;

        /**
         * 地址
         */
        @Schema(description = "地址", example = "北京市朝阳区")
        private String address;

        /**
         * 地点名称
         */
        @Schema(description = "地点名称", example = "某某餐厅")
        private String placeName;
    }

    /**
     * 附件信息
     */
    @Data
    @Schema(description = "附件信息")
    public static class AttachmentInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 附件ID
         */
        @Schema(description = "附件ID", example = "1")
        private Long id;

        /**
         * 文件名
         */
        @Schema(description = "文件名", example = "receipt.jpg")
        private String fileName;

        /**
         * 文件URL
         */
        @Schema(description = "文件URL", example = "https://example.com/files/receipt.jpg")
        private String fileUrl;

        /**
         * 文件类型
         */
        @Schema(description = "文件类型", example = "image/jpeg")
        private String fileType;

        /**
         * 文件大小
         */
        @Schema(description = "文件大小（字节）", example = "1024000")
        private Long fileSize;

        /**
         * 缩略图URL
         */
        @Schema(description = "缩略图URL", example = "https://example.com/thumbnails/receipt_thumb.jpg")
        private String thumbnailUrl;

        /**
         * 上传时间
         */
        @Schema(description = "上传时间")
        private LocalDateTime uploadTime;
    }

    /**
     * 定期交易信息
     */
    @Data
    @Schema(description = "定期交易信息")
    public static class RecurringInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 重复类型
         */
        @Schema(description = "重复类型", example = "MONTHLY")
        private String type;

        /**
         * 重复间隔
         */
        @Schema(description = "重复间隔", example = "1")
        private Integer interval;

        /**
         * 结束日期
         */
        @Schema(description = "结束日期")
        private LocalDateTime endDate;

        /**
         * 重复次数
         */
        @Schema(description = "重复次数", example = "12")
        private Integer count;

        /**
         * 已执行次数
         */
        @Schema(description = "已执行次数", example = "3")
        private Integer executedCount;

        /**
         * 是否自动创建
         */
        @Schema(description = "是否自动创建", example = "true")
        private Boolean autoCreate;

        /**
         * 下次执行时间
         */
        @Schema(description = "下次执行时间")
        private LocalDateTime nextExecuteTime;
    }

    /**
     * 分摊信息
     */
    @Data
    @Schema(description = "分摊信息")
    public static class SplitInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 分摊类型
         */
        @Schema(description = "分摊类型", example = "EQUAL")
        private String type;

        /**
         * 分摊详情
         */
        @Schema(description = "分摊详情")
        private List<SplitDetail> details;

        /**
         * 总分摊金额
         */
        @Schema(description = "总分摊金额", example = "100.00")
        private BigDecimal totalAmount;

        /**
         * 已支付金额
         */
        @Schema(description = "已支付金额", example = "50.00")
        private BigDecimal paidAmount;

        /**
         * 未支付金额
         */
        @Schema(description = "未支付金额", example = "50.00")
        private BigDecimal unpaidAmount;

        /**
         * 分摊详情
         */
        @Data
        @Schema(description = "分摊详情")
        public static class SplitDetail implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 用户ID
             */
            @Schema(description = "用户ID", example = "1")
            private Long userId;

            /**
             * 用户名
             */
            @Schema(description = "用户名", example = "张三")
            private String username;

            /**
             * 昵称
             */
            @Schema(description = "昵称", example = "小张")
            private String nickname;

            /**
             * 分摊金额
             */
            @Schema(description = "分摊金额", example = "50.25")
            private BigDecimal amount;

            /**
             * 分摊比例
             */
            @Schema(description = "分摊比例", example = "0.5")
            private BigDecimal percentage;

            /**
             * 是否已支付
             */
            @Schema(description = "是否已支付", example = "false")
            private Boolean isPaid;

            /**
             * 支付时间
             */
            @Schema(description = "支付时间")
            private LocalDateTime paidTime;
        }
    }

    /**
     * 交易统计
     */
    @Data
    @Schema(description = "交易统计")
    public static class TransactionStatistics implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 总交易数
         */
        @Schema(description = "总交易数", example = "150")
        private Long totalCount;

        /**
         * 总收入
         */
        @Schema(description = "总收入", example = "10000.00")
        private BigDecimal totalIncome;

        /**
         * 总支出
         */
        @Schema(description = "总支出", example = "8500.00")
        private BigDecimal totalExpense;

        /**
         * 净收入
         */
        @Schema(description = "净收入", example = "1500.00")
        private BigDecimal netIncome;

        /**
         * 平均交易金额
         */
        @Schema(description = "平均交易金额", example = "123.33")
        private BigDecimal averageAmount;

        /**
         * 最大交易金额
         */
        @Schema(description = "最大交易金额", example = "2000.00")
        private BigDecimal maxAmount;

        /**
         * 最小交易金额
         */
        @Schema(description = "最小交易金额", example = "5.00")
        private BigDecimal minAmount;

        /**
         * 统计开始日期
         */
        @Schema(description = "统计开始日期")
        private LocalDate startDate;

        /**
         * 统计结束日期
         */
        @Schema(description = "统计结束日期")
        private LocalDate endDate;
    }

    /**
     * 分类统计
     */
    @Data
    @Schema(description = "分类统计")
    public static class CategoryStatistics implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 分类ID
         */
        @Schema(description = "分类ID", example = "1")
        private Long categoryId;

        /**
         * 分类名称
         */
        @Schema(description = "分类名称", example = "餐饮")
        private String categoryName;

        /**
         * 分类图标
         */
        @Schema(description = "分类图标", example = "🍽️")
        private String categoryIcon;

        /**
         * 交易数量
         */
        @Schema(description = "交易数量", example = "25")
        private Long count;

        /**
         * 总金额
         */
        @Schema(description = "总金额", example = "1500.00")
        private BigDecimal totalAmount;

        /**
         * 平均金额
         */
        @Schema(description = "平均金额", example = "60.00")
        private BigDecimal averageAmount;

        /**
         * 占比
         */
        @Schema(description = "占比", example = "0.15")
        private BigDecimal percentage;
    }

    /**
     * 月度趋势
     */
    @Data
    @Schema(description = "月度趋势")
    public static class MonthlyTrend implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 年月
         */
        @Schema(description = "年月", example = "2024-01")
        private String month;

        /**
         * 收入
         */
        @Schema(description = "收入", example = "5000.00")
        private BigDecimal income;

        /**
         * 支出
         */
        @Schema(description = "支出", example = "3500.00")
        private BigDecimal expense;

        /**
         * 净收入
         */
        @Schema(description = "净收入", example = "1500.00")
        private BigDecimal netIncome;

        /**
         * 交易数量
         */
        @Schema(description = "交易数量", example = "45")
        private Long transactionCount;
    }

    /**
     * 用户统计
     */
    @Data
    @Schema(description = "用户统计")
    public static class UserStatistics implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 用户ID
         */
        @Schema(description = "用户ID", example = "1")
        private Long userId;

        /**
         * 用户名
         */
        @Schema(description = "用户名", example = "张三")
        private String username;

        /**
         * 昵称
         */
        @Schema(description = "昵称", example = "小张")
        private String nickname;

        /**
         * 头像
         */
        @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
        private String avatar;

        /**
         * 交易数量
         */
        @Schema(description = "交易数量", example = "30")
        private Long transactionCount;

        /**
         * 总金额
         */
        @Schema(description = "总金额", example = "2500.00")
        private BigDecimal totalAmount;

        /**
         * 收入
         */
        @Schema(description = "收入", example = "3000.00")
        private BigDecimal income;

        /**
         * 支出
         */
        @Schema(description = "支出", example = "2500.00")
        private BigDecimal expense;

        /**
         * 平均交易金额
         */
        @Schema(description = "平均交易金额", example = "83.33")
        private BigDecimal averageAmount;
    }

    /**
     * 验证结果
     */
    @Data
    @Schema(description = "验证结果")
    public static class ValidationResult implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 是否有效
         */
        @Schema(description = "是否有效", example = "true")
        private Boolean isValid;

        /**
         * 错误信息
         */
        @Schema(description = "错误信息")
        private List<String> errors;

        /**
         * 警告信息
         */
        @Schema(description = "警告信息")
        private List<String> warnings;

        /**
         * 建议信息
         */
        @Schema(description = "建议信息")
        private List<String> suggestions;
    }

    /**
     * 交易建议
     */
    @Data
    @Schema(description = "交易建议")
    public static class TransactionSuggestion implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 建议类型
         */
        @Schema(description = "建议类型", example = "CATEGORY")
        private String type;

        /**
         * 建议值
         */
        @Schema(description = "建议值", example = "餐饮")
        private String value;

        /**
         * 建议ID
         */
        @Schema(description = "建议ID", example = "1")
        private Long valueId;

        /**
         * 置信度
         */
        @Schema(description = "置信度", example = "0.85")
        private Double confidence;

        /**
         * 建议原因
         */
        @Schema(description = "建议原因", example = "基于历史交易记录")
        private String reason;
    }

    /**
     * 导入结果
     */
    @Data
    @Schema(description = "导入结果")
    public static class ImportResult implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 总记录数
         */
        @Schema(description = "总记录数", example = "100")
        private Integer totalRecords;

        /**
         * 成功导入数
         */
        @Schema(description = "成功导入数", example = "95")
        private Integer successCount;

        /**
         * 失败数
         */
        @Schema(description = "失败数", example = "5")
        private Integer failureCount;

        /**
         * 跳过数
         */
        @Schema(description = "跳过数", example = "0")
        private Integer skipCount;

        /**
         * 错误信息
         */
        @Schema(description = "错误信息")
        private List<String> errors;

        /**
         * 导入的交易ID列表
         */
        @Schema(description = "导入的交易ID列表")
        private List<Long> importedIds;
    }

    /**
     * 交易模板
     */
    @Data
    @Schema(description = "交易模板")
    public static class TransactionTemplate implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 模板ID
         */
        @Schema(description = "模板ID", example = "1")
        private Long id;

        /**
         * 模板名称
         */
        @Schema(description = "模板名称", example = "午餐模板")
        private String name;

        /**
         * 交易类型
         */
        @Schema(description = "交易类型", example = "EXPENSE")
        private String type;

        /**
         * 金额
         */
        @Schema(description = "金额", example = "30.00")
        private BigDecimal amount;

        /**
         * 描述
         */
        @Schema(description = "描述", example = "午餐费用")
        private String description;

        /**
         * 分类ID
         */
        @Schema(description = "分类ID", example = "1")
        private Long categoryId;

        /**
         * 分类名称
         */
        @Schema(description = "分类名称", example = "餐饮")
        private String categoryName;

        /**
         * 钱包ID
         */
        @Schema(description = "钱包ID", example = "1")
        private Long walletId;

        /**
         * 钱包名称
         */
        @Schema(description = "钱包名称", example = "现金")
        private String walletName;

        /**
         * 标签列表
         */
        @Schema(description = "标签列表")
        private List<String> tags;

        /**
         * 使用次数
         */
        @Schema(description = "使用次数", example = "15")
        private Integer useCount;

        /**
         * 创建时间
         */
        @Schema(description = "创建时间")
        private LocalDateTime createTime;

        /**
         * 最后使用时间
         */
        @Schema(description = "最后使用时间")
        private LocalDateTime lastUsedTime;
    }
}
