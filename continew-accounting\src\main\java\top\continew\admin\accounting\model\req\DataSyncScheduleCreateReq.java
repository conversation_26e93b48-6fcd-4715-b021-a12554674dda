package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 创建数据同步计划请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "创建数据同步计划请求")
public class DataSyncScheduleCreateReq implements Serializable {

    /**
     * 配置ID
     */
    @Schema(description = "配置ID", example = "1")
    @NotNull(message = "配置ID不能为空")
    private Long configId;

    /**
     * 计划名称
     */
    @Schema(description = "计划名称", example = "每日同步计划")
    @NotBlank(message = "计划名称不能为空")
    @Length(max = 100, message = "计划名称长度不能超过 {max} 个字符")
    private String scheduleName;

    /**
     * 计划类型
     */
    @Schema(description = "计划类型", example = "CRON", allowableValues = {"CRON", "INTERVAL", "ONE_TIME"})
    @NotBlank(message = "计划类型不能为空")
    private String scheduleType;

    /**
     * Cron表达式
     */
    @Schema(description = "Cron表达式", example = "0 0 2 * * ?")
    @Length(max = 100, message = "Cron表达式长度不能超过 {max} 个字符")
    private String cronExpression;

    /**
     * 间隔秒数
     */
    @Schema(description = "间隔秒数", example = "3600")
    @Positive(message = "间隔秒数必须为正数")
    private Integer intervalSeconds;

    /**
     * 计划执行时间(一次性)
     */
    @Schema(description = "计划执行时间(一次性)", example = "2025-01-01T10:00:00")
    private LocalDateTime scheduledTime;

    /**
     * 时区
     */
    @Schema(description = "时区", example = "Asia/Shanghai")
    @NotBlank(message = "时区不能为空")
    @Length(max = 50, message = "时区长度不能超过 {max} 个字符")
    private String timezone = "Asia/Shanghai";

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled = true;

    /**
     * 最大重试次数
     */
    @Schema(description = "最大重试次数", example = "3")
    @Positive(message = "最大重试次数必须为正数")
    private Integer maxRetryCount = 3;

    /**
     * 重试间隔秒数
     */
    @Schema(description = "重试间隔秒数", example = "300")
    @Positive(message = "重试间隔秒数必须为正数")
    private Integer retryIntervalSeconds = 300;

    /**
     * 超时时间(秒)
     */
    @Schema(description = "超时时间(秒)", example = "1800")
    @Positive(message = "超时时间必须为正数")
    private Integer timeoutSeconds;

}
