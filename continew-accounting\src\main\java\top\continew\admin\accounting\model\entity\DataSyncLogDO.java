package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.extension.crud.model.entity.BaseDO;

import java.time.LocalDateTime;

/**
 * 数据同步日志实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("acc_data_sync_log")
public class DataSyncLogDO extends BaseDO {

    /**
     * 配置ID
     */
    @TableField("config_id")
    private Long configId;

    /**
     * 群组ID
     */
    @TableField("group_id")
    private Long groupId;

    /**
     * 同步ID
     */
    @TableField("sync_id")
    private String syncId;

    /**
     * 同步类型
     */
    @TableField("sync_type")
    private String syncType;

    /**
     * 同步方向
     */
    @TableField("sync_direction")
    private String syncDirection;

    /**
     * 操作类型
     */
    @TableField("operation_type")
    private String operationType;

    /**
     * 数据类型
     */
    @TableField("data_type")
    private String dataType;

    /**
     * 处理记录数
     */
    @TableField("records_processed")
    private Integer recordsProcessed;

    /**
     * 成功记录数
     */
    @TableField("records_success")
    private Integer recordsSuccess;

    /**
     * 失败记录数
     */
    @TableField("records_failed")
    private Integer recordsFailed;

    /**
     * 跳过记录数
     */
    @TableField("records_skipped")
    private Integer recordsSkipped;

    /**
     * 冲突记录数
     */
    @TableField("records_conflict")
    private Integer recordsConflict;

    /**
     * 同步状态
     */
    @TableField("status")
    private String status;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 执行时长（毫秒）
     */
    @TableField("duration_ms")
    private Long durationMs;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 错误代码
     */
    @TableField("error_code")
    private String errorCode;

    /**
     * 错误详情JSON
     */
    @TableField("error_details_json")
    private String errorDetailsJson;

    /**
     * 同步详情JSON
     */
    @TableField("sync_details_json")
    private String syncDetailsJson;

    /**
     * 源数据摘要
     */
    @TableField("source_data_summary")
    private String sourceDataSummary;

    /**
     * 目标数据摘要
     */
    @TableField("target_data_summary")
    private String targetDataSummary;

    /**
     * 冲突详情JSON
     */
    @TableField("conflict_details_json")
    private String conflictDetailsJson;

    /**
     * 性能指标JSON
     */
    @TableField("performance_metrics_json")
    private String performanceMetricsJson;

    /**
     * 触发方式
     */
    @TableField("trigger_type")
    private String triggerType;

    /**
     * 触发用户ID
     */
    @TableField("trigger_user_id")
    private Long triggerUserId;

    /**
     * 是否重试
     */
    @TableField("is_retry")
    private Boolean isRetry;

    /**
     * 重试次数
     */
    @TableField("retry_count")
    private Integer retryCount;

    /**
     * 父日志ID
     */
    @TableField("parent_log_id")
    private Long parentLogId;
}
