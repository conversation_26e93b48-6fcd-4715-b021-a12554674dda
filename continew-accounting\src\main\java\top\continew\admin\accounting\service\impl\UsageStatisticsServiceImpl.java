package top.continew.admin.accounting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.accounting.mapper.UsageStatisticsMapper;
import top.continew.admin.accounting.model.entity.SubscriptionDO;
import top.continew.admin.accounting.model.entity.UsageStatisticsDO;
import top.continew.admin.accounting.model.resp.UsageStatisticsResp;
import top.continew.admin.accounting.service.SubscriptionService;
import top.continew.admin.accounting.service.SubscriptionPlanService;
import top.continew.admin.accounting.service.UsageStatisticsService;
import top.continew.starter.core.util.validation.CheckUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.List;

/**
 * 使用量统计服务实现
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UsageStatisticsServiceImpl implements UsageStatisticsService {

    private final UsageStatisticsMapper usageStatisticsMapper;
    private final SubscriptionService subscriptionService;
    private final SubscriptionPlanService subscriptionPlanService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordTransactionUsage(Long groupId) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        
        LocalDate today = LocalDate.now();
        UsageStatisticsDO usage = getOrCreateUsageRecord(groupId, today);
        
        usage.setTransactionCount(usage.getTransactionCount() + 1);
        usageStatisticsMapper.updateById(usage);
        
        log.debug("记录交易使用量，群组: {}, 日期: {}", groupId, today);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordOcrUsage(Long groupId) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        
        LocalDate today = LocalDate.now();
        UsageStatisticsDO usage = getOrCreateUsageRecord(groupId, today);
        
        usage.setOcrCount(usage.getOcrCount() + 1);
        usageStatisticsMapper.updateById(usage);
        
        log.debug("记录OCR使用量，群组: {}, 日期: {}", groupId, today);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordApiUsage(Long groupId) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        
        LocalDate today = LocalDate.now();
        UsageStatisticsDO usage = getOrCreateUsageRecord(groupId, today);
        
        usage.setApiCalls(usage.getApiCalls() + 1);
        usageStatisticsMapper.updateById(usage);
        
        log.debug("记录API使用量，群组: {}, 日期: {}", groupId, today);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordStorageUsage(Long groupId, Long storageBytes) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        CheckUtils.throwIfNull(storageBytes, "存储字节数不能为空");
        
        LocalDate today = LocalDate.now();
        UsageStatisticsDO usage = getOrCreateUsageRecord(groupId, today);
        
        usage.setStorageUsed(usage.getStorageUsed() + storageBytes);
        usageStatisticsMapper.updateById(usage);
        
        log.debug("记录存储使用量，群组: {}, 日期: {}, 字节: {}", groupId, today, storageBytes);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordExportUsage(Long groupId) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        
        LocalDate today = LocalDate.now();
        UsageStatisticsDO usage = getOrCreateUsageRecord(groupId, today);
        
        usage.setExportCount(usage.getExportCount() + 1);
        usageStatisticsMapper.updateById(usage);
        
        log.debug("记录导出使用量，群组: {}, 日期: {}", groupId, today);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordWebhookUsage(Long groupId) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        
        LocalDate today = LocalDate.now();
        UsageStatisticsDO usage = getOrCreateUsageRecord(groupId, today);
        
        usage.setWebhookCalls(usage.getWebhookCalls() + 1);
        usageStatisticsMapper.updateById(usage);
        
        log.debug("记录Webhook使用量，群组: {}, 日期: {}", groupId, today);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateActiveUsers(Long groupId, Integer activeUsers) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        CheckUtils.throwIfNull(activeUsers, "活跃用户数不能为空");
        
        LocalDate today = LocalDate.now();
        UsageStatisticsDO usage = getOrCreateUsageRecord(groupId, today);
        
        usage.setActiveUsers(activeUsers);
        usageStatisticsMapper.updateById(usage);
        
        log.debug("更新活跃用户数，群组: {}, 日期: {}, 用户数: {}", groupId, today, activeUsers);
    }

    @Override
    public UsageStatisticsResp getCurrentMonthUsage(Long groupId) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        
        LocalDate startDate = LocalDate.now().withDayOfMonth(1);
        LocalDate endDate = LocalDate.now();
        
        UsageStatisticsDO usage = usageStatisticsMapper.selectMonthlyUsage(groupId, startDate, endDate);
        if (usage == null) {
            return createEmptyUsageResp(groupId);
        }
        
        UsageStatisticsResp resp = BeanUtil.copyProperties(usage, UsageStatisticsResp.class);
        resp.setStorageUsedMB(convertBytesToMB(usage.getStorageUsed()));
        
        return resp;
    }

    @Override
    public List<UsageStatisticsResp> getUsageTrend(Long groupId, LocalDate startDate, LocalDate endDate) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        CheckUtils.throwIfNull(startDate, "开始日期不能为空");
        CheckUtils.throwIfNull(endDate, "结束日期不能为空");
        
        List<UsageStatisticsDO> usageList = usageStatisticsMapper.selectUsageTrend(groupId, startDate, endDate);
        
        return usageList.stream()
                .map(usage -> {
                    UsageStatisticsResp resp = BeanUtil.copyProperties(usage, UsageStatisticsResp.class);
                    resp.setStorageUsedMB(convertBytesToMB(usage.getStorageUsed()));
                    return resp;
                })
                .toList();
    }

    @Override
    public UsageStatisticsResp getTotalUsage(Long groupId, LocalDate startDate, LocalDate endDate) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        CheckUtils.throwIfNull(startDate, "开始日期不能为空");
        CheckUtils.throwIfNull(endDate, "结束日期不能为空");
        
        UsageStatisticsDO usage = usageStatisticsMapper.selectTotalUsage(groupId, startDate, endDate);
        if (usage == null) {
            return createEmptyUsageResp(groupId);
        }
        
        UsageStatisticsResp resp = BeanUtil.copyProperties(usage, UsageStatisticsResp.class);
        resp.setStorageUsedMB(convertBytesToMB(usage.getStorageUsed()));
        
        return resp;
    }

    @Override
    public boolean isUsageLimitExceeded(Long groupId, String limitType) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        CheckUtils.throwIfBlank(limitType, "限制类型不能为空");
        
        UsageStatisticsResp currentUsage = getCurrentMonthUsage(groupId);
        Integer currentValue = getCurrentUsageValue(currentUsage, limitType);
        
        return subscriptionService.isUsageLimitExceeded(groupId, limitType, currentValue);
    }

    @Override
    public Integer getRemainingUsage(Long groupId, String limitType) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        CheckUtils.throwIfBlank(limitType, "限制类型不能为空");
        
        UsageStatisticsResp currentUsage = getCurrentMonthUsage(groupId);
        Integer currentValue = getCurrentUsageValue(currentUsage, limitType);
        
        return subscriptionService.getRemainingUsage(groupId, limitType, currentValue);
    }

    @Override
    public Double getUsageRate(Long groupId, String limitType) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        CheckUtils.throwIfBlank(limitType, "限制类型不能为空");
        
        UsageStatisticsResp currentUsage = getCurrentMonthUsage(groupId);
        Integer currentValue = getCurrentUsageValue(currentUsage, limitType);
        Integer remainingValue = subscriptionService.getRemainingUsage(groupId, limitType, currentValue);
        
        if (remainingValue == Integer.MAX_VALUE) {
            return 0.0; // 无限制
        }
        
        Integer totalLimit = currentValue + remainingValue;
        if (totalLimit == 0) {
            return 0.0;
        }
        
        return (double) currentValue / totalLimit * 100;
    }

    @Override
    public List<Long> getOverLimitGroups(String limitType) {
        CheckUtils.throwIfBlank(limitType, "限制类型不能为空");
        
        return usageStatisticsMapper.selectOverLimitGroups(limitType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetMonthlyStatistics() {
        LocalDate lastMonth = LocalDate.now().minusMonths(1);
        int result = usageStatisticsMapper.resetMonthlyStatistics(lastMonth);
        
        log.info("重置月度统计完成，影响记录数: {}", result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cleanupExpiredStatistics(Integer retentionDays) {
        CheckUtils.throwIfNull(retentionDays, "保留天数不能为空");
        CheckUtils.throwIf(retentionDays <= 0, "保留天数必须大于0");
        
        LocalDate cutoffDate = LocalDate.now().minusDays(retentionDays);
        int result = usageStatisticsMapper.deleteExpiredStatistics(cutoffDate);
        
        log.info("清理过期统计数据完成，删除记录数: {}, 截止日期: {}", result, cutoffDate);
    }

    @Override
    public UsageReportResp generateUsageReport(Long groupId, LocalDate startDate, LocalDate endDate) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        CheckUtils.throwIfNull(startDate, "开始日期不能为空");
        CheckUtils.throwIfNull(endDate, "结束日期不能为空");
        
        // 获取总使用量
        UsageStatisticsResp totalUsage = getTotalUsage(groupId, startDate, endDate);
        
        // 获取每日统计
        List<UsageStatisticsResp> dailyStats = getUsageTrend(groupId, startDate, endDate);
        
        // 构建报告
        UsageReportResp report = new UsageReportResp();
        report.setGroupId(groupId);
        report.setStartDate(startDate);
        report.setEndDate(endDate);
        report.setTotalTransactions(totalUsage.getTransactionCount());
        report.setTotalOcrUsage(totalUsage.getOcrCount());
        report.setTotalApiCalls(totalUsage.getApiCalls());
        report.setTotalStorageUsed(totalUsage.getStorageUsed());
        report.setTotalExports(totalUsage.getExportCount());
        report.setTotalWebhookCalls(totalUsage.getWebhookCalls());
        report.setAvgActiveUsers(calculateAvgActiveUsers(dailyStats));
        report.setDailyStats(dailyStats);
        
        return report;
    }

    /**
     * 获取或创建使用记录
     */
    private UsageStatisticsDO getOrCreateUsageRecord(Long groupId, LocalDate statDate) {
        UsageStatisticsDO usage = usageStatisticsMapper.selectByGroupIdAndDate(groupId, statDate);
        
        if (usage == null) {
            usage = new UsageStatisticsDO();
            usage.setGroupId(groupId);
            usage.setStatDate(statDate);
            usage.setTransactionCount(0);
            usage.setOcrCount(0);
            usage.setApiCalls(0);
            usage.setStorageUsed(0L);
            usage.setExportCount(0);
            usage.setWebhookCalls(0);
            usage.setActiveUsers(0);
            
            usageStatisticsMapper.insert(usage);
        }
        
        return usage;
    }

    /**
     * 创建空的使用量响应
     */
    private UsageStatisticsResp createEmptyUsageResp(Long groupId) {
        UsageStatisticsResp resp = new UsageStatisticsResp();
        resp.setGroupId(groupId);
        resp.setTransactionCount(0);
        resp.setOcrCount(0);
        resp.setApiCalls(0);
        resp.setStorageUsed(0L);
        resp.setStorageUsedMB(BigDecimal.ZERO);
        resp.setExportCount(0);
        resp.setWebhookCalls(0);
        resp.setActiveUsers(0);
        return resp;
    }

    /**
     * 字节转MB
     */
    private BigDecimal convertBytesToMB(Long bytes) {
        if (bytes == null || bytes == 0) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(bytes).divide(new BigDecimal(1024 * 1024), 2, RoundingMode.HALF_UP);
    }

    /**
     * 获取当前使用量值
     */
    private Integer getCurrentUsageValue(UsageStatisticsResp usage, String limitType) {
        return switch (limitType) {
            case "maxTransactionsPerMonth" -> usage.getTransactionCount();
            case "maxOcrPerMonth" -> usage.getOcrCount();
            case "maxApiCallsPerMonth" -> usage.getApiCalls();
            case "maxExportsPerMonth" -> usage.getExportCount();
            case "maxWebhooksPerMonth" -> usage.getWebhookCalls();
            default -> 0;
        };
    }

    /**
     * 计算平均活跃用户数
     */
    private Integer calculateAvgActiveUsers(List<UsageStatisticsResp> dailyStats) {
        if (dailyStats.isEmpty()) {
            return 0;
        }
        
        int totalActiveUsers = dailyStats.stream()
                .mapToInt(stat -> stat.getActiveUsers() != null ? stat.getActiveUsers() : 0)
                .sum();
        
        return totalActiveUsers / dailyStats.size();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initializeUsageStatistics(Long groupId) {
        LocalDate today = LocalDate.now();

        // 检查今天是否已有统计记录
        UsageStatisticsDO existing = usageStatisticsMapper.selectByGroupIdAndDate(groupId, today);
        if (existing == null) {
            // 创建初始统计记录
            UsageStatisticsDO usage = new UsageStatisticsDO();
            usage.setGroupId(groupId);
            usage.setStatDate(today);
            usage.setTransactionCount(0);
            usage.setOcrCount(0);
            usage.setApiCalls(0);
            usage.setStorageUsed(0L);
            usage.setExportCount(0);
            usage.setWebhookCalls(0);
            usage.setActiveUsers(0);

            this.save(usage);
            log.info("初始化群组使用量统计: groupId={}, date={}", groupId, today);
        }
    }
}
