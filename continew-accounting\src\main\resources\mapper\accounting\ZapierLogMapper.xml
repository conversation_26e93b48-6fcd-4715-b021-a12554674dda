<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.accounting.mapper.ZapierLogMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="top.continew.admin.accounting.model.entity.ZapierLogDO">
        <id column="id" property="id" />
        <result column="config_id" property="configId" />
        <result column="group_id" property="groupId" />
        <result column="trigger_type" property="triggerType" />
        <result column="event_type" property="eventType" />
        <result column="business_id" property="businessId" />
        <result column="business_type" property="businessType" />
        <result column="request_data" property="requestData" />
        <result column="response_data" property="responseData" />
        <result column="request_headers" property="requestHeaders" />
        <result column="response_headers" property="responseHeaders" />
        <result column="http_status" property="httpStatus" />
        <result column="status" property="status" />
        <result column="executed_at" property="executedAt" />
        <result column="execution_time" property="executionTime" />
        <result column="request_size" property="requestSize" />
        <result column="response_size" property="responseSize" />
        <result column="error_message" property="errorMessage" />
        <result column="error_code" property="errorCode" />
        <result column="retry_count" property="retryCount" />
        <result column="is_retry" property="isRetry" />
        <result column="original_log_id" property="originalLogId" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 根据配置ID查询日志 -->
    <select id="selectByConfigId" resultMap="BaseResultMap">
        SELECT * FROM acc_zapier_log
        WHERE config_id = #{configId}
        ORDER BY executed_at DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据群组ID查询日志 -->
    <select id="selectByGroupId" resultMap="BaseResultMap">
        SELECT * FROM acc_zapier_log
        WHERE group_id = #{groupId}
        ORDER BY executed_at DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询失败的日志 -->
    <select id="selectFailedLogs" resultMap="BaseResultMap">
        SELECT * FROM acc_zapier_log
        WHERE status = 'FAILED'
        <if test="configId != null">
            AND config_id = #{configId}
        </if>
        <if test="hours != null">
            AND executed_at >= DATE_SUB(NOW(), INTERVAL #{hours} HOUR)
        </if>
        ORDER BY executed_at DESC
    </select>

    <!-- 查询执行统计 -->
    <select id="selectExecutionStats" resultType="map">
        SELECT
            COUNT(*) as total_executions,
            SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
            SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failure_count,
            SUM(CASE WHEN status = 'TIMEOUT' THEN 1 ELSE 0 END) as timeout_count,
            CASE
                WHEN COUNT(*) > 0 THEN ROUND(SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2)
                ELSE 0
            END as success_rate,
            AVG(execution_time) as avg_execution_time,
            MIN(execution_time) as min_execution_time,
            MAX(execution_time) as max_execution_time,
            SUM(request_size) as total_request_size,
            SUM(response_size) as total_response_size,
            COUNT(DISTINCT DATE(executed_at)) as active_days
        FROM acc_zapier_log
        WHERE config_id = #{configId}
        AND executed_at BETWEEN #{startTime} AND #{endTime}
    </select>

    <!-- 查询群组执行统计 -->
    <select id="selectGroupExecutionStats" resultType="map">
        SELECT
            COUNT(*) as total_executions,
            COUNT(DISTINCT config_id) as active_configs,
            SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
            SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failure_count,
            SUM(CASE WHEN status = 'TIMEOUT' THEN 1 ELSE 0 END) as timeout_count,
            CASE
                WHEN COUNT(*) > 0 THEN ROUND(SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2)
                ELSE 0
            END as success_rate,
            AVG(execution_time) as avg_execution_time,
            SUM(request_size) as total_request_size,
            SUM(response_size) as total_response_size,
            COUNT(DISTINCT DATE(executed_at)) as active_days
        FROM acc_zapier_log
        WHERE group_id = #{groupId}
        AND executed_at BETWEEN #{startTime} AND #{endTime}
    </select>

    <!-- 查询执行趋势 -->
    <select id="selectExecutionTrend" resultType="map">
        SELECT
            <choose>
                <when test="interval == 'hour'">
                    DATE_FORMAT(executed_at, '%Y-%m-%d %H:00:00') as time_point
                </when>
                <when test="interval == 'day'">
                    DATE_FORMAT(executed_at, '%Y-%m-%d') as time_point
                </when>
                <otherwise>
                    DATE_FORMAT(executed_at, '%Y-%m-%d %H:00:00') as time_point
                </otherwise>
            </choose>,
            COUNT(*) as execution_count,
            SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
            SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failure_count,
            AVG(execution_time) as avg_execution_time,
            SUM(request_size) as total_request_size,
            SUM(response_size) as total_response_size
        FROM acc_zapier_log
        WHERE config_id = #{configId}
        AND executed_at BETWEEN #{startTime} AND #{endTime}
        GROUP BY
            <choose>
                <when test="interval == 'hour'">
                    DATE_FORMAT(executed_at, '%Y-%m-%d %H:00:00')
                </when>
                <when test="interval == 'day'">
                    DATE_FORMAT(executed_at, '%Y-%m-%d')
                </when>
                <otherwise>
                    DATE_FORMAT(executed_at, '%Y-%m-%d %H:00:00')
                </otherwise>
            </choose>
        ORDER BY time_point
    </select>

    <!-- 查询错误分布统计 -->
    <select id="selectErrorDistribution" resultType="map">
        SELECT
            error_code,
            error_message,
            COUNT(*) as error_count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM acc_zapier_log WHERE config_id = #{configId} AND executed_at BETWEEN #{startTime} AND #{endTime}), 2) as error_rate,
            MIN(executed_at) as first_occurrence,
            MAX(executed_at) as last_occurrence
        FROM acc_zapier_log
        WHERE config_id = #{configId}
        AND status = 'FAILED'
        AND executed_at BETWEEN #{startTime} AND #{endTime}
        GROUP BY error_code, error_message
        ORDER BY error_count DESC
    </select>

    <!-- 查询性能统计 -->
    <select id="selectPerformanceStats" resultType="map">
        SELECT
            COUNT(*) as total_executions,
            AVG(execution_time) as avg_execution_time,
            MIN(execution_time) as min_execution_time,
            MAX(execution_time) as max_execution_time,
            PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY execution_time) as median_execution_time,
            PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY execution_time) as p95_execution_time,
            PERCENTILE_CONT(0.99) WITHIN GROUP (ORDER BY execution_time) as p99_execution_time,
            SUM(CASE WHEN execution_time &lt; 1000 THEN 1 ELSE 0 END) as fast_executions,
            SUM(CASE WHEN execution_time BETWEEN 1000 AND 5000 THEN 1 ELSE 0 END) as normal_executions,
            SUM(CASE WHEN execution_time > 5000 THEN 1 ELSE 0 END) as slow_executions,
            AVG(request_size) as avg_request_size,
            AVG(response_size) as avg_response_size,
            SUM(request_size) as total_request_size,
            SUM(response_size) as total_response_size
        FROM acc_zapier_log
        WHERE config_id = #{configId}
        AND executed_at BETWEEN #{startTime} AND #{endTime}
    </select>

    <!-- 查询慢执行日志 -->
    <select id="selectSlowExecutionLogs" resultMap="BaseResultMap">
        SELECT * FROM acc_zapier_log
        WHERE execution_time >= #{thresholdMs}
        <if test="configId != null">
            AND config_id = #{configId}
        </if>
        <if test="hours != null">
            AND executed_at >= DATE_SUB(NOW(), INTERVAL #{hours} HOUR)
        </if>
        ORDER BY execution_time DESC
    </select>

    <!-- 查询重试日志 -->
    <select id="selectRetryLogs" resultMap="BaseResultMap">
        SELECT * FROM acc_zapier_log
        WHERE original_log_id = #{originalLogId}
        ORDER BY executed_at ASC
    </select>

    <!-- 根据业务查询日志 -->
    <select id="selectByBusiness" resultMap="BaseResultMap">
        SELECT * FROM acc_zapier_log
        WHERE business_type = #{businessType}
        AND business_id = #{businessId}
        ORDER BY executed_at DESC
    </select>

    <!-- 删除过期日志 -->
    <delete id="deleteExpiredLogs">
        DELETE FROM acc_zapier_log
        WHERE executed_at &lt;= DATE_SUB(NOW(), INTERVAL #{days} DAY)
    </delete>

    <!-- 根据配置ID删除日志 -->
    <delete id="deleteByConfigId">
        DELETE FROM acc_zapier_log
        WHERE config_id = #{configId}
    </delete>

    <!-- 根据群组ID删除日志 -->
    <delete id="deleteByGroupId">
        DELETE FROM acc_zapier_log
        WHERE group_id = #{groupId}
    </delete>

    <!-- 查询最近执行日志 -->
    <select id="selectRecentLogs" resultMap="BaseResultMap">
        SELECT * FROM acc_zapier_log
        WHERE config_id = #{configId}
        ORDER BY executed_at DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询异常日志 -->
    <select id="selectAnomalyLogs" resultMap="BaseResultMap">
        SELECT * FROM acc_zapier_log
        WHERE group_id = #{groupId}
        AND (
            status = 'FAILED'
            OR execution_time > (
                SELECT AVG(execution_time) * 3
                FROM acc_zapier_log
                WHERE group_id = #{groupId}
                AND executed_at >= DATE_SUB(NOW(), INTERVAL #{hours} HOUR)
            )
        )
        AND executed_at >= DATE_SUB(NOW(), INTERVAL #{hours} HOUR)
        ORDER BY executed_at DESC
    </select>

    <!-- 查询HTTP状态码分布 -->
    <select id="selectHttpStatusDistribution" resultType="map">
        SELECT
            http_status,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM acc_zapier_log WHERE config_id = #{configId} AND executed_at BETWEEN #{startTime} AND #{endTime}), 2) as percentage
        FROM acc_zapier_log
        WHERE config_id = #{configId}
        AND executed_at BETWEEN #{startTime} AND #{endTime}
        AND http_status IS NOT NULL
        GROUP BY http_status
        ORDER BY count DESC
    </select>

    <!-- 查询数据传输统计 -->
    <select id="selectDataTransferStats" resultType="map">
        SELECT
            COUNT(*) as total_requests,
            SUM(request_size) as total_request_size,
            SUM(response_size) as total_response_size,
            AVG(request_size) as avg_request_size,
            AVG(response_size) as avg_response_size,
            MAX(request_size) as max_request_size,
            MAX(response_size) as max_response_size,
            SUM(request_size + response_size) as total_transfer_size
        FROM acc_zapier_log
        WHERE config_id = #{configId}
        AND executed_at BETWEEN #{startTime} AND #{endTime}
    </select>

    <!-- 查询执行时间分布 -->
    <select id="selectExecutionTimeDistribution" resultType="map">
        SELECT
            CASE
                WHEN execution_time &lt; 500 THEN '&lt;500ms'
                WHEN execution_time &lt; 1000 THEN '500ms-1s'
                WHEN execution_time &lt; 2000 THEN '1s-2s'
                WHEN execution_time &lt; 5000 THEN '2s-5s'
                WHEN execution_time &lt; 10000 THEN '5s-10s'
                ELSE '&gt;10s'
            END as time_range,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM acc_zapier_log WHERE config_id = #{configId} AND executed_at BETWEEN #{startTime} AND #{endTime}), 2) as percentage
        FROM acc_zapier_log
        WHERE config_id = #{configId}
        AND executed_at BETWEEN #{startTime} AND #{endTime}
        GROUP BY
            CASE
                WHEN execution_time &lt; 500 THEN '&lt;500ms'
                WHEN execution_time &lt; 1000 THEN '500ms-1s'
                WHEN execution_time &lt; 2000 THEN '1s-2s'
                WHEN execution_time &lt; 5000 THEN '2s-5s'
                WHEN execution_time &lt; 10000 THEN '5s-10s'
                ELSE '&gt;10s'
            END
        ORDER BY
            CASE
                WHEN execution_time &lt; 500 THEN 1
                WHEN execution_time &lt; 1000 THEN 2
                WHEN execution_time &lt; 2000 THEN 3
                WHEN execution_time &lt; 5000 THEN 4
                WHEN execution_time &lt; 10000 THEN 5
                ELSE 6
            END
    </select>

    <!-- 查询日志详情（包含配置信息） -->
    <select id="selectLogDetail" resultType="map">
        SELECT
            l.*,
            c.name as config_name,
            c.webhook_url,
            c.trigger_type as config_trigger_type,
            c.timeout_seconds,
            c.max_retries
        FROM acc_zapier_log l
        LEFT JOIN acc_zapier_config c ON l.config_id = c.id
        WHERE l.id = #{id}
    </select>

</mapper>
