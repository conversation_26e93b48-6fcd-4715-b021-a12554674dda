package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.admin.common.model.entity.BaseEntity;

import java.time.LocalDateTime;

/**
 * Google Sheets配置实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("acc_google_sheets_config")
@Schema(description = "Google Sheets配置实体")
public class GoogleSheetsConfig extends BaseEntity {

    /**
     * 配置名称
     */
    @Schema(description = "配置名称", example = "我的记账表格")
    @TableField("config_name")
    private String configName;

    /**
     * Google Sheets ID
     */
    @Schema(description = "Google Sheets ID", example = "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms")
    @TableField("spreadsheet_id")
    private String spreadsheetId;

    /**
     * 工作表名称
     */
    @Schema(description = "工作表名称", example = "账单记录")
    @TableField("sheet_name")
    private String sheetName;

    /**
     * 表格标题
     */
    @Schema(description = "表格标题", example = "家庭记账表")
    @TableField("spreadsheet_title")
    private String spreadsheetTitle;

    /**
     * 表格URL
     */
    @Schema(description = "表格URL", example = "https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit")
    @TableField("spreadsheet_url")
    private String spreadsheetUrl;

    /**
     * 同步方向
     */
    @Schema(description = "同步方向", example = "BIDIRECTIONAL")
    @TableField("sync_direction")
    private String syncDirection;

    /**
     * 同步模式
     */
    @Schema(description = "同步模式", example = "REAL_TIME")
    @TableField("sync_mode")
    private String syncMode;

    /**
     * 调度表达式
     */
    @Schema(description = "调度表达式", example = "0 0 * * * ?")
    @TableField("cron_expression")
    private String cronExpression;

    /**
     * 下次执行时间
     */
    @Schema(description = "下次执行时间", example = "2025-01-02T00:00:00")
    @TableField("next_execution_time")
    private LocalDateTime nextExecutionTime;

    /**
     * 字段映射配置JSON
     */
    @Schema(description = "字段映射配置JSON")
    @TableField("field_mapping_json")
    private String fieldMappingJson;

    /**
     * 过滤条件JSON
     */
    @Schema(description = "过滤条件JSON")
    @TableField("filter_condition_json")
    private String filterConditionJson;

    /**
     * 同步设置JSON
     */
    @Schema(description = "同步设置JSON")
    @TableField("sync_settings_json")
    private String syncSettingsJson;

    /**
     * 认证类型
     */
    @Schema(description = "认证类型", example = "SERVICE_ACCOUNT")
    @TableField("auth_type")
    private String authType;

    /**
     * 认证配置JSON
     */
    @Schema(description = "认证配置JSON")
    @TableField("auth_config_json")
    private String authConfigJson;

    /**
     * 认证状态
     */
    @Schema(description = "认证状态", example = "VALID")
    @TableField("auth_status")
    private String authStatus;

    /**
     * 令牌过期时间
     */
    @Schema(description = "令牌过期时间", example = "2025-12-31T23:59:59")
    @TableField("token_expires_at")
    private LocalDateTime tokenExpiresAt;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    @TableField("enabled")
    private Boolean enabled;

    /**
     * 配置状态
     */
    @Schema(description = "配置状态", example = "ACTIVE")
    @TableField("config_status")
    private String configStatus;

    /**
     * 配置版本
     */
    @Schema(description = "配置版本", example = "1")
    @TableField("config_version")
    private Integer configVersion;

    /**
     * 版本说明
     */
    @Schema(description = "版本说明", example = "v1.1 - 新增自定义列支持")
    @TableField("version_notes")
    private String versionNotes;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    @TableField("group_id")
    private Long groupId;

    /**
     * 最后同步时间
     */
    @Schema(description = "最后同步时间", example = "2025-01-01T10:00:00")
    @TableField("last_sync_time")
    private LocalDateTime lastSyncTime;

    /**
     * 最后同步状态
     */
    @Schema(description = "最后同步状态", example = "SUCCESS")
    @TableField("last_sync_status")
    private String lastSyncStatus;

    /**
     * 最后同步ID
     */
    @Schema(description = "最后同步ID", example = "SYNC_20250101_001")
    @TableField("last_sync_id")
    private String lastSyncId;

    /**
     * 最后同步消息
     */
    @Schema(description = "最后同步消息", example = "同步成功")
    @TableField("last_sync_message")
    private String lastSyncMessage;

    /**
     * 总同步次数
     */
    @Schema(description = "总同步次数", example = "150")
    @TableField("total_sync_count")
    private Integer totalSyncCount;

    /**
     * 成功同步次数
     */
    @Schema(description = "成功同步次数", example = "145")
    @TableField("success_sync_count")
    private Integer successSyncCount;

    /**
     * 失败同步次数
     */
    @Schema(description = "失败同步次数", example = "5")
    @TableField("failed_sync_count")
    private Integer failedSyncCount;

    /**
     * 同步成功率
     */
    @Schema(description = "同步成功率", example = "96.67")
    @TableField("success_rate")
    private Double successRate;

    /**
     * 总处理记录数
     */
    @Schema(description = "总处理记录数", example = "15000")
    @TableField("total_processed_records")
    private Long totalProcessedRecords;

    /**
     * 总成功记录数
     */
    @Schema(description = "总成功记录数", example = "14850")
    @TableField("total_success_records")
    private Long totalSuccessRecords;

    /**
     * 总失败记录数
     */
    @Schema(description = "总失败记录数", example = "150")
    @TableField("total_failed_records")
    private Long totalFailedRecords;

    /**
     * 平均同步耗时（秒）
     */
    @Schema(description = "平均同步耗时（秒）", example = "45")
    @TableField("avg_sync_duration")
    private Double avgSyncDuration;

    /**
     * 最大同步耗时（秒）
     */
    @Schema(description = "最大同步耗时（秒）", example = "120")
    @TableField("max_sync_duration")
    private Integer maxSyncDuration;

    /**
     * 最小同步耗时（秒）
     */
    @Schema(description = "最小同步耗时（秒）", example = "10")
    @TableField("min_sync_duration")
    private Integer minSyncDuration;

    /**
     * 最近30天同步次数
     */
    @Schema(description = "最近30天同步次数", example = "30")
    @TableField("recent_sync_count")
    private Integer recentSyncCount;

    /**
     * 最近30天成功率
     */
    @Schema(description = "最近30天成功率", example = "98.33")
    @TableField("recent_success_rate")
    private Double recentSuccessRate;

    /**
     * 自定义标签JSON
     */
    @Schema(description = "自定义标签JSON")
    @TableField("custom_tags_json")
    private String customTagsJson;

    /**
     * 扩展属性JSON
     */
    @Schema(description = "扩展属性JSON")
    @TableField("extra_properties_json")
    private String extraPropertiesJson;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "用于同步日常开销记录")
    @TableField("remark")
    private String remark;
}
