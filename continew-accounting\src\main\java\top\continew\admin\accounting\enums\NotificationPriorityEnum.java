package top.continew.admin.accounting.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 通知优先级枚举
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Getter
@RequiredArgsConstructor
public enum NotificationPriorityEnum {

    /**
     * 低优先级
     */
    LOW(1, "LOW", "低优先级"),

    /**
     * 普通优先级
     */
    NORMAL(2, "NORMAL", "普通优先级"),

    /**
     * 高优先级
     */
    HIGH(3, "HIGH", "高优先级"),

    /**
     * 紧急优先级
     */
    URGENT(4, "URGENT", "紧急优先级"),

    /**
     * 关键优先级
     */
    CRITICAL(5, "CRITICAL", "关键优先级");

    /**
     * 优先级值
     */
    private final Integer value;

    /**
     * 优先级代码
     */
    private final String code;

    /**
     * 优先级名称
     */
    private final String name;

    /**
     * 根据代码获取枚举
     *
     * @param code 优先级代码
     * @return 通知优先级枚举
     */
    public static NotificationPriorityEnum getByCode(String code) {
        for (NotificationPriorityEnum priority : values()) {
            if (priority.getCode().equals(code)) {
                return priority;
            }
        }
        return null;
    }

    /**
     * 根据值获取枚举
     *
     * @param value 优先级值
     * @return 通知优先级枚举
     */
    public static NotificationPriorityEnum getByValue(Integer value) {
        for (NotificationPriorityEnum priority : values()) {
            if (priority.getValue().equals(value)) {
                return priority;
            }
        }
        return null;
    }

    /**
     * 是否为高优先级
     *
     * @return 是否为高优先级
     */
    public boolean isHighPriority() {
        return this.value >= HIGH.value;
    }

    /**
     * 是否为紧急优先级
     *
     * @return 是否为紧急优先级
     */
    public boolean isUrgent() {
        return this == URGENT || this == CRITICAL;
    }

}
