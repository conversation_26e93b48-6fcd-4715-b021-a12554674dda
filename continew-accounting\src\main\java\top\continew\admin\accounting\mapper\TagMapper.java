package top.continew.admin.accounting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import top.continew.admin.accounting.model.entity.TagDO;
import top.continew.admin.accounting.model.resp.TagListResp;

import java.util.List;

/**
 * 标签 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface TagMapper extends BaseMapper<TagDO> {

    /**
     * 查询群组标签列表
     *
     * @param groupId 群组ID
     * @return 标签列表
     */
    @Select("""
        SELECT t.id, t.group_id, g.name as group_name, t.name, t.color, t.icon, 
               t.description, t.sort, t.is_system, t.is_default, t.status,
               (SELECT COUNT(*) FROM acc_transaction tr 
                WHERE JSON_CONTAINS(tr.tags, JSON_QUOTE(t.name)) 
                AND tr.group_id = t.group_id AND tr.status = 1) as usage_count,
               t.create_time, t.update_time
        FROM acc_tag t
        LEFT JOIN acc_group g ON t.group_id = g.id
        WHERE t.group_id = #{groupId} AND t.status = 1
        ORDER BY t.sort ASC, t.create_time ASC
        """)
    List<TagListResp> selectGroupTags(@Param("groupId") Long groupId);

    /**
     * 检查标签名称是否存在
     *
     * @param name      标签名称
     * @param groupId   群组ID
     * @param excludeId 排除的标签ID
     * @return 是否存在
     */
    @Select("""
        SELECT COUNT(*) > 0 FROM acc_tag 
        WHERE name = #{name} AND group_id = #{groupId}
        AND (#{excludeId} IS NULL OR id != #{excludeId})
        AND status = 1
        """)
    boolean existsByName(@Param("name") String name, @Param("groupId") Long groupId, @Param("excludeId") Long excludeId);

    /**
     * 查询默认标签
     *
     * @param groupId 群组ID
     * @return 默认标签列表
     */
    @Select("""
        SELECT * FROM acc_tag 
        WHERE group_id = #{groupId} AND is_default = 1 AND status = 1
        ORDER BY sort ASC, create_time ASC
        """)
    List<TagDO> selectDefaultTags(@Param("groupId") Long groupId);

    /**
     * 检查标签是否被使用
     *
     * @param tagId 标签ID
     * @return 使用次数
     */
    @Select("""
        SELECT COUNT(*) FROM acc_transaction tr
        INNER JOIN acc_tag t ON JSON_CONTAINS(tr.tags, JSON_QUOTE(t.name))
        WHERE t.id = #{tagId} AND tr.group_id = t.group_id AND tr.status = 1
        """)
    int countUsage(@Param("tagId") Long tagId);

    /**
     * 统计群组标签数量
     *
     * @param groupId 群组ID
     * @return 标签数量
     */
    @Select("""
        SELECT COUNT(*) FROM acc_tag 
        WHERE group_id = #{groupId} AND status = 1
        """)
    int countByGroupId(@Param("groupId") Long groupId);

    /**
     * 查询热门标签
     *
     * @param groupId 群组ID
     * @param limit   限制数量
     * @return 热门标签列表
     */
    @Select("""
        SELECT t.*, 
               (SELECT COUNT(*) FROM acc_transaction tr 
                WHERE JSON_CONTAINS(tr.tags, JSON_QUOTE(t.name)) 
                AND tr.group_id = t.group_id AND tr.status = 1) as usage_count
        FROM acc_tag t
        WHERE t.group_id = #{groupId} AND t.status = 1
        ORDER BY usage_count DESC, t.sort ASC
        LIMIT #{limit}
        """)
    List<TagDO> selectHotTags(@Param("groupId") Long groupId, @Param("limit") int limit);

    /**
     * 查询未使用的标签
     *
     * @param groupId 群组ID
     * @return 未使用的标签列表
     */
    @Select("""
        SELECT t.* FROM acc_tag t
        WHERE t.group_id = #{groupId} AND t.status = 1
        AND NOT EXISTS (
            SELECT 1 FROM acc_transaction tr 
            WHERE JSON_CONTAINS(tr.tags, JSON_QUOTE(t.name)) 
            AND tr.group_id = t.group_id AND tr.status = 1
        )
        ORDER BY t.sort ASC, t.create_time ASC
        """)
    List<TagDO> selectUnusedTags(@Param("groupId") Long groupId);

    /**
     * 根据名称查询标签
     *
     * @param names   标签名称列表
     * @param groupId 群组ID
     * @return 标签列表
     */
    @Select("""
        <script>
        SELECT * FROM acc_tag 
        WHERE group_id = #{groupId} AND status = 1
        AND name IN 
        <foreach collection="names" item="name" open="(" separator="," close=")">
            #{name}
        </foreach>
        </script>
        """)
    List<TagDO> selectByNames(@Param("names") List<String> names, @Param("groupId") Long groupId);

    /**
     * 批量删除标签（软删除）
     *
     * @param tagIds 标签ID列表
     * @return 删除行数
     */
    int batchDelete(@Param("tagIds") List<Long> tagIds);

    /**
     * 查询标签使用统计
     *
     * @param groupId 群组ID
     * @return 标签使用统计列表
     */
    @Select("""
        SELECT t.id, t.name, t.color, t.icon,
               COUNT(tr.id) as usage_count,
               SUM(CASE WHEN tr.type = 'INCOME' THEN tr.amount ELSE 0 END) as income_amount,
               SUM(CASE WHEN tr.type = 'EXPENSE' THEN tr.amount ELSE 0 END) as expense_amount
        FROM acc_tag t
        LEFT JOIN acc_transaction tr ON JSON_CONTAINS(tr.tags, JSON_QUOTE(t.name)) 
                                     AND tr.group_id = t.group_id AND tr.status = 1
        WHERE t.group_id = #{groupId} AND t.status = 1
        GROUP BY t.id, t.name, t.color, t.icon
        ORDER BY usage_count DESC, t.sort ASC
        """)
    List<TagStatistics> selectTagStatistics(@Param("groupId") Long groupId);

    /**
     * 标签统计信息
     */
    class TagStatistics {
        private Long id;
        private String name;
        private String color;
        private String icon;
        private Integer usageCount;
        private java.math.BigDecimal incomeAmount;
        private java.math.BigDecimal expenseAmount;

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getColor() { return color; }
        public void setColor(String color) { this.color = color; }
        public String getIcon() { return icon; }
        public void setIcon(String icon) { this.icon = icon; }
        public Integer getUsageCount() { return usageCount; }
        public void setUsageCount(Integer usageCount) { this.usageCount = usageCount; }
        public java.math.BigDecimal getIncomeAmount() { return incomeAmount; }
        public void setIncomeAmount(java.math.BigDecimal incomeAmount) { this.incomeAmount = incomeAmount; }
        public java.math.BigDecimal getExpenseAmount() { return expenseAmount; }
        public void setExpenseAmount(java.math.BigDecimal expenseAmount) { this.expenseAmount = expenseAmount; }
    }
}
