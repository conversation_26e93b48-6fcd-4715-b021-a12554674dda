package top.continew.admin.accounting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import top.continew.admin.accounting.model.entity.DebtDO;
import top.continew.admin.accounting.model.query.DebtQuery;
import top.continew.admin.accounting.model.req.DebtCreateReq;
import top.continew.admin.accounting.model.req.DebtPaymentReq;
import top.continew.admin.accounting.model.req.DebtUpdateReq;
import top.continew.admin.accounting.model.resp.DebtDetailResp;
import top.continew.admin.accounting.model.resp.DebtListResp;
import top.continew.admin.accounting.mapper.DebtMapper.DebtStatisticsResp;
import top.continew.starter.data.mybatis.base.BaseService;

import java.math.BigDecimal;
import java.util.List;

/**
 * 债务管理业务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface DebtService extends BaseService<DebtListResp, DebtDetailResp, DebtQ<PERSON><PERSON>, DebtCreateReq>, IService<DebtDO> {

    /**
     * 更新债务信息
     *
     * @param req 更新请求
     * @param id  债务ID
     */
    void update(DebtUpdateReq req, Long id);

    /**
     * 还款
     *
     * @param req 还款请求
     * @return 还款记录ID
     */
    Long makePayment(DebtPaymentReq req);

    /**
     * 确认还款
     *
     * @param paymentId 还款记录ID
     */
    void confirmPayment(Long paymentId);

    /**
     * 取消还款
     *
     * @param paymentId 还款记录ID
     */
    void cancelPayment(Long paymentId);

    /**
     * 获取用户相关的债务列表
     *
     * @param userId  用户ID
     * @param groupId 群组ID
     * @param status  状态
     * @return 债务列表
     */
    List<DebtListResp> getUserDebts(Long userId, Long groupId, String status);

    /**
     * 获取群组债务统计
     *
     * @param groupId 群组ID
     * @param status  状态
     * @return 统计信息
     */
    DebtStatisticsResp getGroupStatistics(Long groupId, String status);

    /**
     * 获取用户债务统计
     *
     * @param userId  用户ID
     * @param groupId 群组ID
     * @param status  状态
     * @return 统计信息
     */
    DebtStatisticsResp getUserStatistics(Long userId, Long groupId, String status);

    /**
     * 获取即将到期的债务
     *
     * @param groupId 群组ID
     * @param days    天数
     * @return 债务列表
     */
    List<DebtListResp> getDueSoonDebts(Long groupId, Integer days);

    /**
     * 获取逾期债务
     *
     * @param groupId 群组ID
     * @return 债务列表
     */
    List<DebtListResp> getOverdueDebts(Long groupId);

    /**
     * 结清债务
     *
     * @param id 债务ID
     */
    void settleDebt(Long id);

    /**
     * 取消债务
     *
     * @param id 债务ID
     */
    void cancelDebt(Long id);

    /**
     * 合并债务
     *
     * @param sourceIds 源债务ID列表
     * @param targetId  目标债务ID
     */
    void mergeDebts(List<Long> sourceIds, Long targetId);

    /**
     * 分割债务
     *
     * @param id     债务ID
     * @param amount 分割金额
     * @return 新债务ID
     */
    Long splitDebt(Long id, BigDecimal amount);

    /**
     * 转移债务
     *
     * @param id           债务ID
     * @param newCreditorId 新债权人ID
     * @param newDebtorId   新债务人ID
     */
    void transferDebt(Long id, Long newCreditorId, Long newDebtorId);

    /**
     * 计算利息
     *
     * @param id 债务ID
     * @return 利息金额
     */
    BigDecimal calculateInterest(Long id);

    /**
     * 生成还款计划
     *
     * @param id              债务ID
     * @param periods         期数
     * @param paymentFrequency 还款频率
     */
    void generatePaymentPlan(Long id, Integer periods, String paymentFrequency);

    /**
     * 发送还款提醒
     *
     * @param groupId 群组ID
     * @param days    提前天数
     */
    void sendPaymentReminder(Long groupId, Integer days);

    /**
     * 自动结算债务
     *
     * @param groupId 群组ID
     */
    void autoSettleDebts(Long groupId);

    /**
     * 获取债务关系图
     *
     * @param groupId 群组ID
     * @return 债务关系数据
     */
    Object getDebtRelationshipGraph(Long groupId);
}
