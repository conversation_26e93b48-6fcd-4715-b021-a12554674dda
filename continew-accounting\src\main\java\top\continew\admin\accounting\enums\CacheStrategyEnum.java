package top.continew.admin.accounting.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 缓存策略枚举
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Getter
@RequiredArgsConstructor
public enum CacheStrategyEnum {

    /**
     * 写入时缓存
     */
    WRITE_THROUGH("WRITE_THROUGH", "写入时缓存", "数据写入时同时更新缓存"),

    /**
     * 写入后缓存
     */
    WRITE_BEHIND("WRITE_BEHIND", "写入后缓存", "数据写入后异步更新缓存"),

    /**
     * 写入时失效
     */
    WRITE_INVALIDATE("WRITE_INVALIDATE", "写入时失效", "数据写入时删除缓存"),

    /**
     * 读取时缓存
     */
    READ_THROUGH("READ_THROUGH", "读取时缓存", "数据读取时缓存结果"),

    /**
     * 延迟加载
     */
    LAZY_LOADING("LAZY_LOADING", "延迟加载", "按需加载缓存数据"),

    /**
     * 预热加载
     */
    PRELOAD("PRELOAD", "预热加载", "系统启动时预加载缓存"),

    /**
     * 定时刷新
     */
    SCHEDULED_REFRESH("SCHEDULED_REFRESH", "定时刷新", "定时刷新缓存数据"),

    /**
     * 事件驱动
     */
    EVENT_DRIVEN("EVENT_DRIVEN", "事件驱动", "基于事件触发缓存更新");

    /**
     * 策略代码
     */
    private final String code;

    /**
     * 策略名称
     */
    private final String name;

    /**
     * 策略描述
     */
    private final String description;

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static CacheStrategyEnum getByCode(String code) {
        for (CacheStrategyEnum strategy : values()) {
            if (strategy.getCode().equals(code)) {
                return strategy;
            }
        }
        return READ_THROUGH;
    }

    /**
     * 是否为写入策略
     *
     * @return 是否为写入策略
     */
    public boolean isWriteStrategy() {
        return this == WRITE_THROUGH || this == WRITE_BEHIND || this == WRITE_INVALIDATE;
    }

    /**
     * 是否为读取策略
     *
     * @return 是否为读取策略
     */
    public boolean isReadStrategy() {
        return this == READ_THROUGH || this == LAZY_LOADING;
    }

    /**
     * 是否为主动策略
     *
     * @return 是否为主动策略
     */
    public boolean isActiveStrategy() {
        return this == PRELOAD || this == SCHEDULED_REFRESH || this == EVENT_DRIVEN;
    }

    /**
     * 是否需要异步处理
     *
     * @return 是否需要异步处理
     */
    public boolean isAsync() {
        return this == WRITE_BEHIND || this == SCHEDULED_REFRESH || this == EVENT_DRIVEN;
    }

}
