package top.continew.admin.accounting.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;

/**
 * 订阅取消事件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Getter
public class SubscriptionCancelledEvent extends ApplicationEvent {

    /**
     * 订阅ID
     */
    private final Long subscriptionId;

    /**
     * 群组ID
     */
    private final Long groupId;

    /**
     * 套餐ID
     */
    private final Long planId;

    /**
     * 套餐名称
     */
    private final String planName;

    /**
     * 用户ID
     */
    private final Long userId;

    /**
     * 取消原因
     */
    private final String cancellationReason;

    /**
     * 取消时间
     */
    private final LocalDateTime cancelledAt;

    /**
     * 剩余天数
     */
    private final Long remainingDays;

    /**
     * 事件时间
     */
    private final LocalDateTime eventTime;

    public SubscriptionCancelledEvent(Object source, Long subscriptionId, Long groupId, Long planId, String planName,
                                     Long userId, String cancellationReason, LocalDateTime cancelledAt,
                                     Long remainingDays, LocalDateTime eventTime) {
        super(source);
        this.subscriptionId = subscriptionId;
        this.groupId = groupId;
        this.planId = planId;
        this.planName = planName;
        this.userId = userId;
        this.cancellationReason = cancellationReason;
        this.cancelledAt = cancelledAt;
        this.remainingDays = remainingDays;
        this.eventTime = eventTime;
    }
}
