package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.admin.accounting.enums.PlatformType;
import top.continew.admin.accounting.enums.SubscriptionPlan;
import top.continew.admin.accounting.model.entity.settings.GroupSettings;
import top.continew.starter.data.mybatis.plus.base.BaseEntity;

import java.time.LocalDateTime;

/**
 * 群组信息实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "acc_group", autoResultMap = true)
@Schema(description = "群组信息")
public class GroupDO extends BaseEntity {

    /**
     * 群组名称
     */
    @Schema(description = "群组名称")
    private String name;

    /**
     * 群组描述
     */
    @Schema(description = "群组描述")
    private String description;

    /**
     * 平台类型
     */
    @Schema(description = "平台类型")
    private PlatformType platform;

    /**
     * 平台群组ID
     */
    @Schema(description = "平台群组ID")
    private String platformGroupId;

    /**
     * 群主用户ID
     */
    @Schema(description = "群主用户ID")
    private Long ownerId;

    /**
     * 订阅套餐
     */
    @Schema(description = "订阅套餐")
    private SubscriptionPlan subscriptionPlan;

    /**
     * 订阅到期时间
     */
    @Schema(description = "订阅到期时间")
    private LocalDateTime subscriptionExpires;

    /**
     * 默认币种
     */
    @Schema(description = "默认币种")
    private String defaultCurrency;

    /**
     * 时区
     */
    @Schema(description = "时区")
    private String timezone;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 群组设置
     */
    @Schema(description = "群组设置")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private GroupSettings settings;
}
