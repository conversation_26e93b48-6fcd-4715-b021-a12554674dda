package top.continew.admin.accounting.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.accounting.model.req.ReportTemplateCreateReq;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 报表模板详情响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "报表模板详情响应")
public class ReportTemplateDetailResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模板ID
     */
    @Schema(description = "模板ID", example = "1")
    private Long templateId;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 群组名称
     */
    @Schema(description = "群组名称", example = "开发团队")
    private String groupName;

    /**
     * 模板名称
     */
    @Schema(description = "模板名称", example = "月度财务报表")
    private String templateName;

    /**
     * 模板描述
     */
    @Schema(description = "模板描述", example = "用于生成月度收支统计报表")
    private String templateDescription;

    /**
     * 模板类型
     */
    @Schema(description = "模板类型", example = "FINANCIAL_OVERVIEW")
    private String templateType;

    /**
     * 模板类型名称
     */
    @Schema(description = "模板类型名称", example = "财务概览")
    private String templateTypeName;

    /**
     * 模板状态
     */
    @Schema(description = "模板状态", example = "ACTIVE")
    private String templateStatus;

    /**
     * 模板状态名称
     */
    @Schema(description = "模板状态名称", example = "活跃")
    private String templateStatusName;

    /**
     * 版本号
     */
    @Schema(description = "版本号", example = "1.0")
    private String version;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    /**
     * 是否公开
     */
    @Schema(description = "是否公开", example = "false")
    private Boolean isPublic;

    /**
     * 是否为系统模板
     */
    @Schema(description = "是否为系统模板", example = "false")
    private Boolean isSystemTemplate;

    /**
     * 标签列表
     */
    @Schema(description = "标签列表", example = "[\"财务\", \"月报\"]")
    private List<String> tags;

    /**
     * 报表配置
     */
    @Schema(description = "报表配置")
    private ReportTemplateCreateReq.ReportConfiguration reportConfig;

    /**
     * 数据源配置
     */
    @Schema(description = "数据源配置")
    private ReportTemplateCreateReq.DataSourceConfiguration dataSourceConfig;

    /**
     * 布局配置
     */
    @Schema(description = "布局配置")
    private ReportTemplateCreateReq.LayoutConfiguration layoutConfig;

    /**
     * 图表配置
     */
    @Schema(description = "图表配置")
    private List<ReportTemplateCreateReq.ChartConfiguration> chartConfigs;

    /**
     * 过滤器配置
     */
    @Schema(description = "过滤器配置")
    private List<ReportTemplateCreateReq.FilterConfiguration> filterConfigs;

    /**
     * 导出配置
     */
    @Schema(description = "导出配置")
    private ReportTemplateCreateReq.ExportConfiguration exportConfig;

    /**
     * 调度配置
     */
    @Schema(description = "调度配置")
    private ReportTemplateCreateReq.ScheduleConfiguration scheduleConfig;

    /**
     * 权限配置
     */
    @Schema(description = "权限配置")
    private ReportTemplateCreateReq.PermissionConfiguration permissionConfig;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID", example = "1")
    private Long createdBy;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名", example = "张三")
    private String createdByName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-01-01 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID", example = "1")
    private Long updatedBy;

    /**
     * 更新人姓名
     */
    @Schema(description = "更新人姓名", example = "李四")
    private String updatedByName;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2025-01-01 15:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 使用统计
     */
    @Schema(description = "使用统计")
    private UsageStatistics usageStats;

    /**
     * 性能统计
     */
    @Schema(description = "性能统计")
    private PerformanceStatistics performanceStats;

    /**
     * 权限信息
     */
    @Schema(description = "权限信息")
    private ReportTemplateResp.PermissionInfo permissionInfo;

    /**
     * 调度信息
     */
    @Schema(description = "调度信息")
    private ReportTemplateResp.ScheduleInfo scheduleInfo;

    /**
     * 版本历史
     */
    @Schema(description = "版本历史")
    private List<VersionHistory> versionHistory;

    /**
     * 执行历史
     */
    @Schema(description = "执行历史")
    private List<ReportTemplateResp.ExecutionRecord> executionHistory;

    /**
     * 用户评价
     */
    @Schema(description = "用户评价")
    private List<UserRating> userRatings;

    /**
     * 扩展属性
     */
    @Schema(description = "扩展属性")
    private Map<String, Object> attributes;

    /**
     * 使用统计
     */
    @Data
    @Schema(description = "使用统计")
    public static class UsageStatistics implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 总使用次数
         */
        @Schema(description = "总使用次数", example = "125")
        private Integer totalUsageCount;

        /**
         * 本月使用次数
         */
        @Schema(description = "本月使用次数", example = "15")
        private Integer monthlyUsageCount;

        /**
         * 本周使用次数
         */
        @Schema(description = "本周使用次数", example = "5")
        private Integer weeklyUsageCount;

        /**
         * 今日使用次数
         */
        @Schema(description = "今日使用次数", example = "2")
        private Integer dailyUsageCount;

        /**
         * 独立用户数
         */
        @Schema(description = "独立用户数", example = "8")
        private Integer uniqueUserCount;

        /**
         * 最后使用时间
         */
        @Schema(description = "最后使用时间", example = "2025-01-01 14:20:00")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime lastUsedTime;

        /**
         * 最后使用人ID
         */
        @Schema(description = "最后使用人ID", example = "2")
        private Long lastUsedBy;

        /**
         * 最后使用人姓名
         */
        @Schema(description = "最后使用人姓名", example = "王五")
        private String lastUsedByName;

        /**
         * 收藏次数
         */
        @Schema(description = "收藏次数", example = "12")
        private Integer favoriteCount;

        /**
         * 分享次数
         */
        @Schema(description = "分享次数", example = "3")
        private Integer shareCount;

        /**
         * 使用趋势（最近30天）
         */
        @Schema(description = "使用趋势")
        private List<UsageTrend> usageTrends;

        @Data
        @Schema(description = "使用趋势")
        public static class UsageTrend implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 日期
             */
            @Schema(description = "日期", example = "2025-01-01")
            private String date;

            /**
             * 使用次数
             */
            @Schema(description = "使用次数", example = "3")
            private Integer usageCount;

            /**
             * 独立用户数
             */
            @Schema(description = "独立用户数", example = "2")
            private Integer uniqueUsers;
        }
    }

    /**
     * 性能统计
     */
    @Data
    @Schema(description = "性能统计")
    public static class PerformanceStatistics implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 平均执行时间（毫秒）
         */
        @Schema(description = "平均执行时间", example = "2500")
        private Long avgExecutionTime;

        /**
         * 最大执行时间（毫秒）
         */
        @Schema(description = "最大执行时间", example = "8000")
        private Long maxExecutionTime;

        /**
         * 最小执行时间（毫秒）
         */
        @Schema(description = "最小执行时间", example = "800")
        private Long minExecutionTime;

        /**
         * 成功执行次数
         */
        @Schema(description = "成功执行次数", example = "118")
        private Integer successCount;

        /**
         * 失败执行次数
         */
        @Schema(description = "失败执行次数", example = "7")
        private Integer failureCount;

        /**
         * 成功率（百分比）
         */
        @Schema(description = "成功率", example = "94.4")
        private Double successRate;

        /**
         * 错误率（百分比）
         */
        @Schema(description = "错误率", example = "5.6")
        private Double errorRate;

        /**
         * 平均数据量
         */
        @Schema(description = "平均数据量", example = "1500")
        private Integer avgDataVolume;

        /**
         * 最大数据量
         */
        @Schema(description = "最大数据量", example = "5000")
        private Integer maxDataVolume;

        /**
         * 最小数据量
         */
        @Schema(description = "最小数据量", example = "100")
        private Integer minDataVolume;

        /**
         * 平均文件大小（字节）
         */
        @Schema(description = "平均文件大小", example = "1048576")
        private Long avgFileSize;

        /**
         * 性能趋势（最近30天）
         */
        @Schema(description = "性能趋势")
        private List<PerformanceTrend> performanceTrends;

        @Data
        @Schema(description = "性能趋势")
        public static class PerformanceTrend implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 日期
             */
            @Schema(description = "日期", example = "2025-01-01")
            private String date;

            /**
             * 平均执行时间
             */
            @Schema(description = "平均执行时间", example = "2500")
            private Long avgExecutionTime;

            /**
             * 成功率
             */
            @Schema(description = "成功率", example = "95.0")
            private Double successRate;

            /**
             * 平均数据量
             */
            @Schema(description = "平均数据量", example = "1500")
            private Integer avgDataVolume;
        }
    }

    /**
     * 版本历史
     */
    @Data
    @Schema(description = "版本历史")
    public static class VersionHistory implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 版本号
         */
        @Schema(description = "版本号", example = "1.0")
        private String version;

        /**
         * 版本说明
         */
        @Schema(description = "版本说明", example = "初始版本")
        private String versionNote;

        /**
         * 更新内容
         */
        @Schema(description = "更新内容", example = "新增财务概览功能")
        private String updateContent;

        /**
         * 更新人ID
         */
        @Schema(description = "更新人ID", example = "1")
        private Long updatedBy;

        /**
         * 更新人姓名
         */
        @Schema(description = "更新人姓名", example = "张三")
        private String updatedByName;

        /**
         * 更新时间
         */
        @Schema(description = "更新时间", example = "2025-01-01 10:00:00")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime updateTime;

        /**
         * 是否当前版本
         */
        @Schema(description = "是否当前版本", example = "true")
        private Boolean isCurrent;

        /**
         * 变更类型
         */
        @Schema(description = "变更类型", example = "FEATURE", allowableValues = {"FEATURE", "BUGFIX", "IMPROVEMENT", "BREAKING"})
        private String changeType;

        /**
         * 变更类型名称
         */
        @Schema(description = "变更类型名称", example = "新功能")
        private String changeTypeName;
    }

    /**
     * 用户评价
     */
    @Data
    @Schema(description = "用户评价")
    public static class UserRating implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 评价ID
         */
        @Schema(description = "评价ID", example = "1")
        private Long ratingId;

        /**
         * 用户ID
         */
        @Schema(description = "用户ID", example = "1")
        private Long userId;

        /**
         * 用户姓名
         */
        @Schema(description = "用户姓名", example = "张三")
        private String userName;

        /**
         * 评分
         */
        @Schema(description = "评分", example = "5")
        private Integer rating;

        /**
         * 评价内容
         */
        @Schema(description = "评价内容", example = "报表功能很实用，数据展示清晰")
        private String comment;

        /**
         * 评价时间
         */
        @Schema(description = "评价时间", example = "2025-01-01 16:30:00")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime ratingTime;

        /**
         * 是否推荐
         */
        @Schema(description = "是否推荐", example = "true")
        private Boolean recommended;
    }
}
