package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.io.Serial;
import java.io.Serializable;
import java.util.Map;
import java.util.List;

/**
 * Zapier配置请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "Zapier配置请求")
public class ZapierConfigReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    @NotNull(message = "群组ID不能为空")
    private Long groupId;

    /**
     * 配置名称
     */
    @Schema(description = "配置名称", example = "交易同步到Google Sheets")
    @NotBlank(message = "配置名称不能为空")
    @Size(max = 100, message = "配置名称长度不能超过100个字符")
    private String name;

    /**
     * 配置描述
     */
    @Schema(description = "配置描述", example = "自动将新交易同步到Google Sheets表格")
    @Size(max = 500, message = "配置描述长度不能超过500个字符")
    private String description;

    /**
     * Webhook URL
     */
    @Schema(description = "Webhook URL", example = "https://hooks.zapier.com/hooks/catch/123456/abcdef/")
    @NotBlank(message = "Webhook URL不能为空")
    @Size(max = 500, message = "Webhook URL长度不能超过500个字符")
    private String webhookUrl;

    /**
     * 触发器类型
     */
    @Schema(description = "触发器类型", example = "TRANSACTION_CREATED")
    @NotBlank(message = "触发器类型不能为空")
    private String triggerType;

    /**
     * 触发条件
     */
    @Schema(description = "触发条件", example = "{\"amount_min\": 100, \"category\": \"餐饮\"}")
    private String triggerConditions;

    /**
     * 数据映射配置
     */
    @Schema(description = "数据映射配置")
    private DataMapping dataMapping;

    /**
     * 过滤规则
     */
    @Schema(description = "过滤规则")
    private List<FilterRule> filterRules;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled = true;

    /**
     * 最大重试次数
     */
    @Schema(description = "最大重试次数", example = "3")
    @Min(value = 0, message = "最大重试次数不能小于0")
    @Max(value = 10, message = "最大重试次数不能大于10")
    private Integer maxRetries = 3;

    /**
     * 超时时间（秒）
     */
    @Schema(description = "超时时间（秒）", example = "30")
    @Min(value = 5, message = "超时时间不能小于5秒")
    @Max(value = 300, message = "超时时间不能大于300秒")
    private Integer timeoutSeconds = 30;

    /**
     * 请求头配置
     */
    @Schema(description = "请求头配置")
    private Map<String, String> headers;

    /**
     * 认证配置
     */
    @Schema(description = "认证配置")
    private AuthConfig authConfig;

    /**
     * 标签
     */
    @Schema(description = "标签", example = "['自动化', '同步']")
    private List<String> tags;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "用于自动化工作流")
    @Size(max = 1000, message = "备注长度不能超过1000个字符")
    private String remark;

    /**
     * 数据映射配置
     */
    @Data
    @Schema(description = "数据映射配置")
    public static class DataMapping implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 字段映射
         */
        @Schema(description = "字段映射")
        private Map<String, String> fieldMapping;

        /**
         * 数据转换规则
         */
        @Schema(description = "数据转换规则")
        private Map<String, String> transformRules;

        /**
         * 默认值
         */
        @Schema(description = "默认值")
        private Map<String, Object> defaultValues;

        /**
         * 是否包含元数据
         */
        @Schema(description = "是否包含元数据", example = "true")
        private Boolean includeMetadata = false;

        /**
         * 日期格式
         */
        @Schema(description = "日期格式", example = "yyyy-MM-dd HH:mm:ss")
        private String dateFormat = "yyyy-MM-dd HH:mm:ss";

        /**
         * 数字格式
         */
        @Schema(description = "数字格式", example = "#,##0.00")
        private String numberFormat = "#,##0.00";
    }

    /**
     * 过滤规则
     */
    @Data
    @Schema(description = "过滤规则")
    public static class FilterRule implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 字段名
         */
        @Schema(description = "字段名", example = "amount")
        @NotBlank(message = "字段名不能为空")
        private String field;

        /**
         * 操作符
         */
        @Schema(description = "操作符", example = ">=")
        @NotBlank(message = "操作符不能为空")
        private String operator;

        /**
         * 值
         */
        @Schema(description = "值", example = "100")
        @NotNull(message = "值不能为空")
        private Object value;

        /**
         * 逻辑操作符
         */
        @Schema(description = "逻辑操作符", example = "AND")
        private String logicOperator = "AND";
    }

    /**
     * 认证配置
     */
    @Data
    @Schema(description = "认证配置")
    public static class AuthConfig implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 认证类型
         */
        @Schema(description = "认证类型", example = "API_KEY")
        private String type;

        /**
         * API密钥
         */
        @Schema(description = "API密钥")
        private String apiKey;

        /**
         * 用户名
         */
        @Schema(description = "用户名")
        private String username;

        /**
         * 密码
         */
        @Schema(description = "密码")
        private String password;

        /**
         * Token
         */
        @Schema(description = "Token")
        private String token;

        /**
         * 其他参数
         */
        @Schema(description = "其他参数")
        private Map<String, String> parameters;
    }
}
