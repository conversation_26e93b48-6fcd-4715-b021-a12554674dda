package top.continew.admin.accounting.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.extension.crud.model.query.PageQuery;

import java.math.BigDecimal;

/**
 * 订阅套餐查询条件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "订阅套餐查询条件")
public class SubscriptionPlanQuery extends PageQuery {

    /**
     * 套餐名称
     */
    @Schema(description = "套餐名称", example = "专业版")
    private String name;

    /**
     * 套餐代码
     */
    @Schema(description = "套餐代码", example = "PRO")
    private String code;

    /**
     * 计费周期
     */
    @Schema(description = "计费周期", example = "MONTHLY")
    private String billingCycle;

    /**
     * 币种
     */
    @Schema(description = "币种", example = "USD")
    private String currency;

    /**
     * 价格范围-最小值
     */
    @Schema(description = "价格范围-最小值", example = "0")
    private BigDecimal priceMin;

    /**
     * 价格范围-最大值
     */
    @Schema(description = "价格范围-最大值", example = "100")
    private BigDecimal priceMax;

    /**
     * 是否热门
     */
    @Schema(description = "是否热门", example = "true")
    private Boolean isPopular;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "1")
    private Integer status;
}
