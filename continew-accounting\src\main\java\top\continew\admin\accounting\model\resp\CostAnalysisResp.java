package top.continew.admin.accounting.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 成本分析响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "成本分析响应")
public class CostAnalysisResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分析ID
     */
    @Schema(description = "分析ID", example = "analysis_123456")
    private String analysisId;

    /**
     * 分析类型
     */
    @Schema(description = "分析类型", example = "TREND_ANALYSIS")
    private String analysisType;

    /**
     * 分析类型名称
     */
    @Schema(description = "分析类型名称", example = "趋势分析")
    private String analysisTypeName;

    /**
     * 分析概览
     */
    @Schema(description = "分析概览")
    private AnalysisOverview overview;

    /**
     * 趋势分析数据
     */
    @Schema(description = "趋势分析数据")
    private TrendAnalysisData trendData;

    /**
     * 对比分析数据
     */
    @Schema(description = "对比分析数据")
    private ComparisonAnalysisData comparisonData;

    /**
     * 分解分析数据
     */
    @Schema(description = "分解分析数据")
    private BreakdownAnalysisData breakdownData;

    /**
     * 效益分析数据
     */
    @Schema(description = "效益分析数据")
    private BenefitAnalysisData benefitData;

    /**
     * 图表数据
     */
    @Schema(description = "图表数据")
    private List<ChartData> chartData;

    /**
     * 洞察和建议
     */
    @Schema(description = "洞察和建议")
    private InsightsAndRecommendations insights;

    /**
     * 生成时间
     */
    @Schema(description = "生成时间", example = "2025-01-01 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime generatedAt;

    /**
     * 数据时间范围
     */
    @Schema(description = "数据时间范围")
    private DataTimeRange dataTimeRange;

    /**
     * 分析概览
     */
    @Data
    @Schema(description = "分析概览")
    public static class AnalysisOverview implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 总成本
         */
        @Schema(description = "总成本", example = "15000.00")
        private BigDecimal totalCost;

        /**
         * 平均成本
         */
        @Schema(description = "平均成本", example = "500.00")
        private BigDecimal averageCost;

        /**
         * 最高成本
         */
        @Schema(description = "最高成本", example = "2000.00")
        private BigDecimal maxCost;

        /**
         * 最低成本
         */
        @Schema(description = "最低成本", example = "50.00")
        private BigDecimal minCost;

        /**
         * 成本变化率
         */
        @Schema(description = "成本变化率", example = "0.15")
        private BigDecimal costChangeRate;

        /**
         * 成本变化趋势
         */
        @Schema(description = "成本变化趋势", example = "INCREASING")
        private String costTrend;

        /**
         * 成本变化趋势名称
         */
        @Schema(description = "成本变化趋势名称", example = "上升")
        private String costTrendName;

        /**
         * 交易数量
         */
        @Schema(description = "交易数量", example = "150")
        private Long transactionCount;

        /**
         * 活跃天数
         */
        @Schema(description = "活跃天数", example = "25")
        private Long activeDays;

        /**
         * 平均每日成本
         */
        @Schema(description = "平均每日成本", example = "600.00")
        private BigDecimal avgDailyCost;

        /**
         * 币种
         */
        @Schema(description = "币种", example = "CNY")
        private String currency;
    }

    /**
     * 趋势分析数据
     */
    @Data
    @Schema(description = "趋势分析数据")
    public static class TrendAnalysisData implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 趋势数据点
         */
        @Schema(description = "趋势数据点")
        private List<TrendDataPoint> dataPoints;

        /**
         * 趋势线方程
         */
        @Schema(description = "趋势线方程", example = "y = 1.2x + 500")
        private String trendEquation;

        /**
         * 相关系数
         */
        @Schema(description = "相关系数", example = "0.85")
        private BigDecimal correlationCoefficient;

        /**
         * 预测数据
         */
        @Schema(description = "预测数据")
        private List<ForecastDataPoint> forecastData;

        /**
         * 季节性分析
         */
        @Schema(description = "季节性分析")
        private SeasonalityAnalysis seasonality;

        @Data
        @Schema(description = "趋势数据点")
        public static class TrendDataPoint implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 时间点
             */
            @Schema(description = "时间点", example = "2025-01-01")
            @JsonFormat(pattern = "yyyy-MM-dd")
            private LocalDate timePoint;

            /**
             * 成本值
             */
            @Schema(description = "成本值", example = "1500.00")
            private BigDecimal costValue;

            /**
             * 累计成本
             */
            @Schema(description = "累计成本", example = "7500.00")
            private BigDecimal cumulativeCost;

            /**
             * 移动平均值
             */
            @Schema(description = "移动平均值", example = "1450.00")
            private BigDecimal movingAverage;

            /**
             * 环比增长率
             */
            @Schema(description = "环比增长率", example = "0.05")
            private BigDecimal periodOverPeriodGrowth;

            /**
             * 同比增长率
             */
            @Schema(description = "同比增长率", example = "0.12")
            private BigDecimal yearOverYearGrowth;
        }

        @Data
        @Schema(description = "预测数据点")
        public static class ForecastDataPoint implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 时间点
             */
            @Schema(description = "时间点", example = "2025-02-01")
            @JsonFormat(pattern = "yyyy-MM-dd")
            private LocalDate timePoint;

            /**
             * 预测值
             */
            @Schema(description = "预测值", example = "1600.00")
            private BigDecimal forecastValue;

            /**
             * 置信区间下限
             */
            @Schema(description = "置信区间下限", example = "1400.00")
            private BigDecimal lowerBound;

            /**
             * 置信区间上限
             */
            @Schema(description = "置信区间上限", example = "1800.00")
            private BigDecimal upperBound;

            /**
             * 置信度
             */
            @Schema(description = "置信度", example = "0.95")
            private BigDecimal confidence;
        }

        @Data
        @Schema(description = "季节性分析")
        public static class SeasonalityAnalysis implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 是否存在季节性
             */
            @Schema(description = "是否存在季节性", example = "true")
            private Boolean hasSeasonality;

            /**
             * 季节性强度
             */
            @Schema(description = "季节性强度", example = "MODERATE")
            private String seasonalityStrength;

            /**
             * 季节性周期
             */
            @Schema(description = "季节性周期", example = "MONTHLY")
            private String seasonalityCycle;

            /**
             * 季节性因子
             */
            @Schema(description = "季节性因子")
            private Map<String, BigDecimal> seasonalityFactors;
        }
    }

    /**
     * 对比分析数据
     */
    @Data
    @Schema(description = "对比分析数据")
    public static class ComparisonAnalysisData implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 当前期间数据
         */
        @Schema(description = "当前期间数据")
        private PeriodData currentPeriod;

        /**
         * 对比期间数据
         */
        @Schema(description = "对比期间数据")
        private PeriodData comparisonPeriod;

        /**
         * 差异分析
         */
        @Schema(description = "差异分析")
        private VarianceAnalysis variance;

        /**
         * 分类对比
         */
        @Schema(description = "分类对比")
        private List<CategoryComparison> categoryComparisons;

        @Data
        @Schema(description = "期间数据")
        public static class PeriodData implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 期间名称
             */
            @Schema(description = "期间名称", example = "2025年1月")
            private String periodName;

            /**
             * 总成本
             */
            @Schema(description = "总成本", example = "15000.00")
            private BigDecimal totalCost;

            /**
             * 交易数量
             */
            @Schema(description = "交易数量", example = "150")
            private Long transactionCount;

            /**
             * 平均成本
             */
            @Schema(description = "平均成本", example = "100.00")
            private BigDecimal averageCost;

            /**
             * 活跃天数
             */
            @Schema(description = "活跃天数", example = "25")
            private Long activeDays;
        }

        @Data
        @Schema(description = "差异分析")
        public static class VarianceAnalysis implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 绝对差异
             */
            @Schema(description = "绝对差异", example = "2000.00")
            private BigDecimal absoluteVariance;

            /**
             * 相对差异
             */
            @Schema(description = "相对差异", example = "0.15")
            private BigDecimal relativeVariance;

            /**
             * 差异方向
             */
            @Schema(description = "差异方向", example = "INCREASE")
            private String varianceDirection;

            /**
             * 差异方向名称
             */
            @Schema(description = "差异方向名称", example = "增加")
            private String varianceDirectionName;

            /**
             * 显著性
             */
            @Schema(description = "显著性", example = "SIGNIFICANT")
            private String significance;

            /**
             * 显著性名称
             */
            @Schema(description = "显著性名称", example = "显著")
            private String significanceName;
        }

        @Data
        @Schema(description = "分类对比")
        public static class CategoryComparison implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 分类ID
             */
            @Schema(description = "分类ID", example = "1")
            private Long categoryId;

            /**
             * 分类名称
             */
            @Schema(description = "分类名称", example = "餐饮")
            private String categoryName;

            /**
             * 当前期间成本
             */
            @Schema(description = "当前期间成本", example = "3000.00")
            private BigDecimal currentCost;

            /**
             * 对比期间成本
             */
            @Schema(description = "对比期间成本", example = "2500.00")
            private BigDecimal comparisonCost;

            /**
             * 成本变化
             */
            @Schema(description = "成本变化", example = "500.00")
            private BigDecimal costChange;

            /**
             * 变化率
             */
            @Schema(description = "变化率", example = "0.20")
            private BigDecimal changeRate;

            /**
             * 占比变化
             */
            @Schema(description = "占比变化", example = "0.02")
            private BigDecimal shareChange;
        }
    }

    /**
     * 分解分析数据
     */
    @Data
    @Schema(description = "分解分析数据")
    public static class BreakdownAnalysisData implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 按分类分解
         */
        @Schema(description = "按分类分解")
        private List<CategoryBreakdown> categoryBreakdown;

        /**
         * 按成员分解
         */
        @Schema(description = "按成员分解")
        private List<MemberBreakdown> memberBreakdown;

        /**
         * 按时间分解
         */
        @Schema(description = "按时间分解")
        private List<TimeBreakdown> timeBreakdown;

        /**
         * 按标签分解
         */
        @Schema(description = "按标签分解")
        private List<TagBreakdown> tagBreakdown;

        @Data
        @Schema(description = "分类分解")
        public static class CategoryBreakdown implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 分类ID
             */
            @Schema(description = "分类ID", example = "1")
            private Long categoryId;

            /**
             * 分类名称
             */
            @Schema(description = "分类名称", example = "餐饮")
            private String categoryName;

            /**
             * 成本金额
             */
            @Schema(description = "成本金额", example = "3000.00")
            private BigDecimal amount;

            /**
             * 占比
             */
            @Schema(description = "占比", example = "0.20")
            private BigDecimal percentage;

            /**
             * 交易数量
             */
            @Schema(description = "交易数量", example = "30")
            private Long transactionCount;

            /**
             * 平均金额
             */
            @Schema(description = "平均金额", example = "100.00")
            private BigDecimal averageAmount;

            /**
             * 排名
             */
            @Schema(description = "排名", example = "1")
            private Integer rank;
        }

        @Data
        @Schema(description = "成员分解")
        public static class MemberBreakdown implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 成员ID
             */
            @Schema(description = "成员ID", example = "1")
            private Long memberId;

            /**
             * 成员姓名
             */
            @Schema(description = "成员姓名", example = "张三")
            private String memberName;

            /**
             * 成本金额
             */
            @Schema(description = "成本金额", example = "5000.00")
            private BigDecimal amount;

            /**
             * 占比
             */
            @Schema(description = "占比", example = "0.33")
            private BigDecimal percentage;

            /**
             * 交易数量
             */
            @Schema(description = "交易数量", example = "50")
            private Long transactionCount;

            /**
             * 平均金额
             */
            @Schema(description = "平均金额", example = "100.00")
            private BigDecimal averageAmount;

            /**
             * 排名
             */
            @Schema(description = "排名", example = "1")
            private Integer rank;
        }

        @Data
        @Schema(description = "时间分解")
        public static class TimeBreakdown implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 时间点
             */
            @Schema(description = "时间点", example = "2025-01")
            private String timePoint;

            /**
             * 时间点名称
             */
            @Schema(description = "时间点名称", example = "2025年1月")
            private String timePointName;

            /**
             * 成本金额
             */
            @Schema(description = "成本金额", example = "1500.00")
            private BigDecimal amount;

            /**
             * 占比
             */
            @Schema(description = "占比", example = "0.10")
            private BigDecimal percentage;

            /**
             * 交易数量
             */
            @Schema(description = "交易数量", example = "15")
            private Long transactionCount;

            /**
             * 环比增长
             */
            @Schema(description = "环比增长", example = "0.05")
            private BigDecimal periodOverPeriodGrowth;
        }

        @Data
        @Schema(description = "标签分解")
        public static class TagBreakdown implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 标签名称
             */
            @Schema(description = "标签名称", example = "工作")
            private String tagName;

            /**
             * 成本金额
             */
            @Schema(description = "成本金额", example = "2000.00")
            private BigDecimal amount;

            /**
             * 占比
             */
            @Schema(description = "占比", example = "0.13")
            private BigDecimal percentage;

            /**
             * 交易数量
             */
            @Schema(description = "交易数量", example = "20")
            private Long transactionCount;

            /**
             * 平均金额
             */
            @Schema(description = "平均金额", example = "100.00")
            private BigDecimal averageAmount;

            /**
             * 排名
             */
            @Schema(description = "排名", example = "2")
            private Integer rank;
        }
    }

    /**
     * 效益分析数据
     */
    @Data
    @Schema(description = "效益分析数据")
    public static class BenefitAnalysisData implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 成本效益比
         */
        @Schema(description = "成本效益比", example = "1.25")
        private BigDecimal costBenefitRatio;

        /**
         * 投资回报率
         */
        @Schema(description = "投资回报率", example = "0.15")
        private BigDecimal returnOnInvestment;

        /**
         * 节约金额
         */
        @Schema(description = "节约金额", example = "1000.00")
        private BigDecimal savingsAmount;

        /**
         * 节约率
         */
        @Schema(description = "节约率", example = "0.10")
        private BigDecimal savingsRate;

        /**
         * 效率指标
         */
        @Schema(description = "效率指标")
        private List<EfficiencyMetric> efficiencyMetrics;

        /**
         * 价值分析
         */
        @Schema(description = "价值分析")
        private ValueAnalysis valueAnalysis;

        @Data
        @Schema(description = "效率指标")
        public static class EfficiencyMetric implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 指标名称
             */
            @Schema(description = "指标名称", example = "成本控制率")
            private String metricName;

            /**
             * 指标值
             */
            @Schema(description = "指标值", example = "0.85")
            private BigDecimal metricValue;

            /**
             * 目标值
             */
            @Schema(description = "目标值", example = "0.90")
            private BigDecimal targetValue;

            /**
             * 达成率
             */
            @Schema(description = "达成率", example = "0.94")
            private BigDecimal achievementRate;

            /**
             * 评级
             */
            @Schema(description = "评级", example = "GOOD")
            private String rating;

            /**
             * 评级名称
             */
            @Schema(description = "评级名称", example = "良好")
            private String ratingName;
        }

        @Data
        @Schema(description = "价值分析")
        public static class ValueAnalysis implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 总价值
             */
            @Schema(description = "总价值", example = "20000.00")
            private BigDecimal totalValue;

            /**
             * 价值密度
             */
            @Schema(description = "价值密度", example = "1.33")
            private BigDecimal valueDensity;

            /**
             * 价值增长率
             */
            @Schema(description = "价值增长率", example = "0.08")
            private BigDecimal valueGrowthRate;

            /**
             * 价值分布
             */
            @Schema(description = "价值分布")
            private Map<String, BigDecimal> valueDistribution;
        }
    }

    /**
     * 图表数据
     */
    @Data
    @Schema(description = "图表数据")
    public static class ChartData implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 图表类型
         */
        @Schema(description = "图表类型", example = "LINE_CHART")
        private String chartType;

        /**
         * 图表标题
         */
        @Schema(description = "图表标题", example = "成本趋势图")
        private String chartTitle;

        /**
         * X轴标签
         */
        @Schema(description = "X轴标签", example = "时间")
        private String xAxisLabel;

        /**
         * Y轴标签
         */
        @Schema(description = "Y轴标签", example = "成本金额")
        private String yAxisLabel;

        /**
         * 数据系列
         */
        @Schema(description = "数据系列")
        private List<DataSeries> dataSeries;

        @Data
        @Schema(description = "数据系列")
        public static class DataSeries implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 系列名称
             */
            @Schema(description = "系列名称", example = "实际成本")
            private String seriesName;

            /**
             * 数据点
             */
            @Schema(description = "数据点")
            private List<DataPoint> dataPoints;

            @Data
            @Schema(description = "数据点")
            public static class DataPoint implements Serializable {

                @Serial
                private static final long serialVersionUID = 1L;

                /**
                 * X值
                 */
                @Schema(description = "X值", example = "2025-01-01")
                private String xValue;

                /**
                 * Y值
                 */
                @Schema(description = "Y值", example = "1500.00")
                private BigDecimal yValue;

                /**
                 * 标签
                 */
                @Schema(description = "标签", example = "1月1日")
                private String label;
            }
        }
    }

    /**
     * 洞察和建议
     */
    @Data
    @Schema(description = "洞察和建议")
    public static class InsightsAndRecommendations implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 关键洞察
         */
        @Schema(description = "关键洞察")
        private List<String> keyInsights;

        /**
         * 优化建议
         */
        @Schema(description = "优化建议")
        private List<String> recommendations;

        /**
         * 风险提示
         */
        @Schema(description = "风险提示")
        private List<String> riskAlerts;

        /**
         * 机会识别
         */
        @Schema(description = "机会识别")
        private List<String> opportunities;

        /**
         * 行动计划
         */
        @Schema(description = "行动计划")
        private List<ActionPlan> actionPlans;

        @Data
        @Schema(description = "行动计划")
        public static class ActionPlan implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 行动项
             */
            @Schema(description = "行动项", example = "优化餐饮支出")
            private String actionItem;

            /**
             * 优先级
             */
            @Schema(description = "优先级", example = "HIGH")
            private String priority;

            /**
             * 预期效果
             */
            @Schema(description = "预期效果", example = "节约成本15%")
            private String expectedOutcome;

            /**
             * 建议时间
             */
            @Schema(description = "建议时间", example = "30天内")
            private String suggestedTimeframe;
        }
    }

    /**
     * 数据时间范围
     */
    @Data
    @Schema(description = "数据时间范围")
    public static class DataTimeRange implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 开始日期
         */
        @Schema(description = "开始日期", example = "2025-01-01")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate startDate;

        /**
         * 结束日期
         */
        @Schema(description = "结束日期", example = "2025-01-31")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate endDate;

        /**
         * 总天数
         */
        @Schema(description = "总天数", example = "31")
        private Long totalDays;

        /**
         * 数据完整性
         */
        @Schema(description = "数据完整性", example = "0.95")
        private BigDecimal dataCompleteness;
    }
}
