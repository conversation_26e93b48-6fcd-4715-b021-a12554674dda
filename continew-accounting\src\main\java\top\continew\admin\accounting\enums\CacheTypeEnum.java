package top.continew.admin.accounting.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 缓存类型枚举
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Getter
@RequiredArgsConstructor
public enum CacheTypeEnum {

    /**
     * 本地缓存
     */
    LOCAL("LOCAL", "本地缓存", "基于Caffeine的本地内存缓存"),

    /**
     * 远程缓存
     */
    REMOTE("REMOTE", "远程缓存", "基于Redis的远程分布式缓存"),

    /**
     * 两级缓存
     */
    BOTH("BOTH", "两级缓存", "本地缓存+远程缓存的两级缓存"),

    /**
     * 无缓存
     */
    NONE("NONE", "无缓存", "不使用缓存");

    /**
     * 类型代码
     */
    private final String code;

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 类型描述
     */
    private final String description;

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static CacheTypeEnum getByCode(String code) {
        for (CacheTypeEnum cacheType : values()) {
            if (cacheType.getCode().equals(code)) {
                return cacheType;
            }
        }
        return NONE;
    }

    /**
     * 是否为本地缓存
     *
     * @return 是否为本地缓存
     */
    public boolean isLocal() {
        return this == LOCAL || this == BOTH;
    }

    /**
     * 是否为远程缓存
     *
     * @return 是否为远程缓存
     */
    public boolean isRemote() {
        return this == REMOTE || this == BOTH;
    }

    /**
     * 是否为两级缓存
     *
     * @return 是否为两级缓存
     */
    public boolean isBoth() {
        return this == BOTH;
    }

    /**
     * 是否启用缓存
     *
     * @return 是否启用缓存
     */
    public boolean isEnabled() {
        return this != NONE;
    }

}
