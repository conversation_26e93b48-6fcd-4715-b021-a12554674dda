package top.continew.admin.accounting.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.extension.crud.model.query.PageQuery;

import java.time.LocalDateTime;

/**
 * 数据同步配置查询条件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "数据同步配置查询条件")
public class DataSyncConfigQuery extends PageQuery {

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 配置名称
     */
    @Schema(description = "配置名称", example = "交易数据同步")
    private String configName;

    /**
     * 数据源类型
     */
    @Schema(description = "数据源类型", example = "DATABASE")
    private String sourceType;

    /**
     * 目标类型
     */
    @Schema(description = "目标类型", example = "GOOGLE_SHEETS")
    private String targetType;

    /**
     * 同步方向
     */
    @Schema(description = "同步方向", example = "TO_TARGET")
    private String syncDirection;

    /**
     * 同步模式
     */
    @Schema(description = "同步模式", example = "INCREMENTAL")
    private String syncMode;

    /**
     * 数据类型
     */
    @Schema(description = "数据类型", example = "TRANSACTION")
    private String dataType;

    /**
     * 同步频率
     */
    @Schema(description = "同步频率", example = "HOURLY")
    private String syncFrequency;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    /**
     * 同步状态
     */
    @Schema(description = "同步状态", example = "SUCCESS")
    private String syncStatus;

    /**
     * 最后同步结果
     */
    @Schema(description = "最后同步结果", example = "SUCCESS")
    private String lastSyncResult;

    /**
     * 创建时间范围 - 开始
     */
    @Schema(description = "创建时间范围 - 开始", example = "2025-01-01 00:00:00")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间范围 - 结束
     */
    @Schema(description = "创建时间范围 - 结束", example = "2025-01-31 23:59:59")
    private LocalDateTime createTimeEnd;

    /**
     * 最后同步时间范围 - 开始
     */
    @Schema(description = "最后同步时间范围 - 开始", example = "2025-01-01 00:00:00")
    private LocalDateTime lastSyncTimeStart;

    /**
     * 最后同步时间范围 - 结束
     */
    @Schema(description = "最后同步时间范围 - 结束", example = "2025-01-31 23:59:59")
    private LocalDateTime lastSyncTimeEnd;

    /**
     * 自定义标签
     */
    @Schema(description = "自定义标签", example = "production")
    private String customTag;

    /**
     * 关键词搜索
     */
    @Schema(description = "关键词搜索", example = "Google Sheets")
    private String keyword;
}
