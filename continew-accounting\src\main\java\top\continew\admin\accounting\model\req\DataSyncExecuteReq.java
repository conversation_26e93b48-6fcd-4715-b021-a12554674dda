package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 数据同步执行请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "数据同步执行请求")
public class DataSyncExecuteReq {

    /**
     * 配置ID
     */
    @Schema(description = "配置ID", example = "1")
    @NotNull(message = "配置ID不能为空")
    private Long configId;

    /**
     * 同步类型
     */
    @Schema(description = "同步类型", example = "FULL", allowableValues = {"FULL", "INCREMENTAL", "CUSTOM"})
    private String syncType = "FULL";

    /**
     * 同步方向
     */
    @Schema(description = "同步方向", example = "TO_TARGET", allowableValues = {
            "TO_TARGET", "FROM_SOURCE", "BIDIRECTIONAL"
    })
    private String syncDirection;

    /**
     * 是否强制同步
     */
    @Schema(description = "是否强制同步", example = "false")
    private Boolean forceSync = false;

    /**
     * 是否异步执行
     */
    @Schema(description = "是否异步执行", example = "true")
    private Boolean async = true;

    /**
     * 同步范围
     */
    @Schema(description = "同步范围")
    private SyncScope syncScope;

    /**
     * 同步选项
     */
    @Schema(description = "同步选项")
    private SyncOptions syncOptions;

    /**
     * 自定义参数
     */
    @Schema(description = "自定义参数")
    private Map<String, Object> customParams;

    /**
     * 同步范围
     */
    @Data
    @Schema(description = "同步范围")
    public static class SyncScope {

        /**
         * 指定记录ID列表
         */
        @Schema(description = "指定记录ID列表")
        private List<Long> recordIds;

        /**
         * 日期范围
         */
        @Schema(description = "日期范围")
        private DateRange dateRange;

        /**
         * 过滤条件
         */
        @Schema(description = "过滤条件")
        private Map<String, Object> filters;

        /**
         * 最大记录数
         */
        @Schema(description = "最大记录数", example = "1000")
        private Integer maxRecords;

        /**
         * 自定义查询条件
         */
        @Schema(description = "自定义查询条件")
        private String customQuery;
    }

    /**
     * 日期范围
     */
    @Data
    @Schema(description = "日期范围")
    public static class DateRange {

        /**
         * 开始日期
         */
        @Schema(description = "开始日期", example = "2025-01-01")
        private String startDate;

        /**
         * 结束日期
         */
        @Schema(description = "结束日期", example = "2025-01-31")
        private String endDate;
    }

    /**
     * 同步选项
     */
    @Data
    @Schema(description = "同步选项")
    public static class SyncOptions {

        /**
         * 批量大小
         */
        @Schema(description = "批量大小", example = "100")
        private Integer batchSize = 100;

        /**
         * 超时时间（秒）
         */
        @Schema(description = "超时时间（秒）", example = "300")
        private Integer timeoutSeconds = 300;

        /**
         * 重试次数
         */
        @Schema(description = "重试次数", example = "3")
        private Integer retryCount = 3;

        /**
         * 重试间隔（秒）
         */
        @Schema(description = "重试间隔（秒）", example = "5")
        private Integer retryIntervalSeconds = 5;

        /**
         * 冲突解决策略
         */
        @Schema(description = "冲突解决策略", example = "LOCAL_WINS", allowableValues = {
                "LOCAL_WINS", "REMOTE_WINS", "MERGE", "SKIP", "MANUAL"
        })
        private String conflictResolution;

        /**
         * 是否跳过错误
         */
        @Schema(description = "是否跳过错误", example = "false")
        private Boolean skipErrors = false;

        /**
         * 是否验证数据
         */
        @Schema(description = "是否验证数据", example = "true")
        private Boolean validateData = true;

        /**
         * 是否备份数据
         */
        @Schema(description = "是否备份数据", example = "false")
        private Boolean backupData = false;

        /**
         * 是否发送通知
         */
        @Schema(description = "是否发送通知", example = "true")
        private Boolean sendNotification = true;

        /**
         * 并发线程数
         */
        @Schema(description = "并发线程数", example = "4")
        private Integer concurrency = 4;

        /**
         * 是否压缩传输
         */
        @Schema(description = "是否压缩传输", example = "true")
        private Boolean compression = true;
    }
}
