package top.continew.admin.accounting.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.accounting.model.entity.UsageStatisticsDO;
import top.continew.starter.extension.crud.mapper.BaseMapper;

import java.time.LocalDate;
import java.util.List;

/**
 * 使用量统计 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface UsageStatisticsMapper extends BaseMapper<UsageStatisticsDO> {

    /**
     * 查询群组指定日期的使用统计
     */
    UsageStatisticsDO selectByGroupIdAndDate(@Param("groupId") Long groupId, @Param("statDate") LocalDate statDate);

    /**
     * 查询群组当月使用统计
     */
    UsageStatisticsDO selectCurrentMonthUsage(@Param("groupId") Long groupId);

    /**
     * 查询群组使用趋势
     */
    List<UsageStatisticsDO> selectUsageTrend(@Param("groupId") Long groupId, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 更新交易数量
     */
    int updateTransactionCount(@Param("groupId") Long groupId, @Param("statDate") LocalDate statDate, @Param("increment") Integer increment);

    /**
     * 更新OCR使用次数
     */
    int updateOcrCount(@Param("groupId") Long groupId, @Param("statDate") LocalDate statDate, @Param("increment") Integer increment);

    /**
     * 更新API调用次数
     */
    int updateApiCalls(@Param("groupId") Long groupId, @Param("statDate") LocalDate statDate, @Param("increment") Integer increment);

    /**
     * 更新存储使用量
     */
    int updateStorageUsed(@Param("groupId") Long groupId, @Param("statDate") LocalDate statDate, @Param("storageUsed") Long storageUsed);

    /**
     * 更新导出次数
     */
    int updateExportCount(@Param("groupId") Long groupId, @Param("statDate") LocalDate statDate, @Param("increment") Integer increment);

    /**
     * 更新Webhook调用次数
     */
    int updateWebhookCalls(@Param("groupId") Long groupId, @Param("statDate") LocalDate statDate, @Param("increment") Integer increment);

    /**
     * 更新活跃用户数
     */
    int updateActiveUsers(@Param("groupId") Long groupId, @Param("statDate") LocalDate statDate, @Param("activeUsers") Integer activeUsers);

    /**
     * 批量插入或更新使用统计
     */
    int insertOrUpdate(UsageStatisticsDO usageStatistics);

    /**
     * 清理过期统计数据
     */
    int deleteExpiredStatistics(@Param("beforeDate") LocalDate beforeDate);

    /**
     * 统计总使用量
     */
    UsageStatisticsDO selectTotalUsage(@Param("groupId") Long groupId, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 查询超限群组
     */
    List<Long> selectOverLimitGroups(@Param("limitType") String limitType, @Param("limitValue") Integer limitValue);
}
