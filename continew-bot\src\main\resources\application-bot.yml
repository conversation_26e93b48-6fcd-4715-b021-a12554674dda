# 机器人配置
bot:
  # 通用配置
  common:
    command-prefix: "/"
    rate-limit-enabled: true
    max-requests-per-minute: 30
    log-all-messages: false
    default-language: "zh-CN"
    default-timezone: "Asia/Shanghai"
    default-currency: "CNY"
    auto-reply-enabled: true
    command-hints-enabled: true
    error-messages-enabled: true
    message-timeout: 300
    session-timeout: 1800
    max-message-length: 4000
    debug-mode: false
    admin-users: []
    blacklist-users: []
    whitelist-groups: []
    banned-words: []
    
    # 通知配置
    notification:
      transaction-notifications: true
      error-notifications: true
      system-notifications: true
      notification-cooldown: 5
      group-notifications: true
      private-notifications: true
    
    # 缓存配置
    cache:
      enabled: true
      user-session-ttl: 1800
      group-info-ttl: 3600
      command-result-ttl: 300
      max-cache-size: 10000
    
    # 重试配置
    retry:
      enabled: true
      max-attempts: 3
      retry-interval: 1000
      retry-multiplier: 2.0
      max-retry-interval: 10000

  # Telegram机器人配置
  telegram:
    enabled: false  # 默认关闭，需要手动开启
    token: ""  # 从BotFather获取的Token
    username: ""  # 机器人用户名
    
    # 轮询配置
    polling:
      enabled: true
      timeout: 30
      limit: 100
      allowed-updates: ["message", "callback_query", "inline_query"]
      remove-webhook-on-startup: true
    
    # Webhook配置
    webhook:
      enabled: false
      url: ""
      path: "/bot/telegram/webhook"
      secret-token: ""
      max-connections: 40
      allowed-updates: ["message", "callback_query", "inline_query"]
      drop-pending-updates: false
    
    # 代理配置
    proxy:
      enabled: false
      type: "HTTP"  # HTTP, SOCKS4, SOCKS5
      host: ""
      port: 0
      username: ""
      password: ""
    
    # 连接配置
    connection:
      connect-timeout: 30000
      read-timeout: 30000
      write-timeout: 30000
      max-retries: 3
      retry-delay: 1000

  # Discord机器人配置
  discord:
    enabled: true  # 启用Discord机器人
    token: ""  # Discord Bot Token
    application-id: ""  # 应用ID
    enable-slash-commands: true
    enable-message-commands: true
    enable-button-interactions: true
    enable-select-menus: true
    enable-modals: true
    command-sync-mode: "GLOBAL"  # GLOBAL, GUILD, HYBRID
    test-guild-id: ""  # 测试服务器ID
    developer-mode: false
    
    # 活动配置
    activity:
      enabled: true
      type: "PLAYING"  # PLAYING, STREAMING, LISTENING, WATCHING, COMPETING
      name: "记账助手"
      url: ""  # 仅STREAMING类型需要
    
    # 状态配置
    status:
      online-status: "ONLINE"  # ONLINE, IDLE, DO_NOT_DISTURB, INVISIBLE
      idle: false
      do-not-disturb: false

# RabbitMQ配置（用于消息队列）
spring:
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    virtual-host: /
    connection-timeout: 15000
    publisher-confirm-type: correlated
    publisher-returns: true
    listener:
      simple:
        acknowledge-mode: manual
        retry:
          enabled: true
          max-attempts: 3
          initial-interval: 1000
          multiplier: 2.0
          max-interval: 10000

# 日志配置
logging:
  level:
    top.continew.admin.bot: DEBUG
    org.telegram.telegrambots: INFO
    net.dv8tion.jda: INFO
    org.springframework.amqp: INFO
