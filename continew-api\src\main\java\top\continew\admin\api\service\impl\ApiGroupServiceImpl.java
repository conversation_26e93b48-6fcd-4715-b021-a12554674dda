package top.continew.admin.api.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.accounting.enums.GroupRoleEnum;
import top.continew.admin.accounting.model.query.GroupQuery;
import top.continew.admin.accounting.model.req.GroupCreateReq;
import top.continew.admin.accounting.model.req.GroupUpdateReq;
import top.continew.admin.accounting.model.resp.GroupDetailResp;
import top.continew.admin.accounting.model.resp.GroupListResp;
import top.continew.admin.accounting.service.GroupService;
import top.continew.admin.accounting.service.TransactionService;
import top.continew.admin.api.model.req.ApiGroupCreateReq;
import top.continew.admin.api.model.req.ApiGroupUpdateReq;
import top.continew.admin.api.model.resp.ApiGroupResp;
import top.continew.admin.api.service.ApiGroupService;
import top.continew.starter.core.util.SecurityContextHolder;
import top.continew.starter.core.util.validation.CheckUtils;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * API群组服务实现
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApiGroupServiceImpl implements ApiGroupService {

    private final GroupService groupService;
    private final TransactionService transactionService;
    
    // 邀请码缓存 (实际项目中应使用Redis)
    private final Map<String, InviteCodeInfo> inviteCodeCache = new ConcurrentHashMap<>();

    @Override
    public PageResp<ApiGroupResp> page(GroupQuery query, PageQuery pageQuery) {
        PageResp<GroupListResp> pageResp = groupService.page(query, pageQuery);
        
        List<ApiGroupResp> apiGroupList = pageResp.getList().stream()
            .map(this::convertToApiGroupResp)
            .collect(Collectors.toList());
            
        return PageResp.<ApiGroupResp>builder()
            .list(apiGroupList)
            .total(pageResp.getTotal())
            .build();
    }

    @Override
    public List<ApiGroupResp> list(GroupQuery query) {
        List<GroupListResp> groupList = groupService.list(query);
        return groupList.stream()
            .map(this::convertToApiGroupResp)
            .collect(Collectors.toList());
    }

    @Override
    public ApiGroupResp get(Long id) {
        GroupDetailResp groupDetail = groupService.get(id);
        return convertToApiGroupResp(groupDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(ApiGroupCreateReq req) {
        GroupCreateReq createReq = new GroupCreateReq();
        BeanUtil.copyProperties(req, createReq);
        
        // 设置默认值
        if (req.getDefaultCurrency() == null) {
            createReq.setDefaultCurrency("CNY");
        }
        if (req.getIsPublic() == null) {
            createReq.setIsPublic(false);
        }
        
        Long groupId = groupService.create(createReq);
        
        // 处理群组设置
        if (req.getSettings() != null) {
            updateGroupSettings(groupId, req.getSettings());
        }
        
        log.info("API创建群组成功，ID: {}, 名称: {}", groupId, req.getName());
        return groupId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ApiGroupUpdateReq req, Long id) {
        GroupUpdateReq updateReq = new GroupUpdateReq();
        BeanUtil.copyProperties(req, updateReq);
        
        groupService.update(updateReq, id);
        
        // 处理群组设置
        if (req.getSettings() != null) {
            updateGroupSettings(id, req.getSettings());
        }
        
        log.info("API更新群组成功，ID: {}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> ids) {
        groupService.delete(ids);
        log.info("API删除群组成功，IDs: {}", ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void joinGroup(Long groupId, String inviteCode) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 验证邀请码
        if (StrUtil.isNotBlank(inviteCode)) {
            validateInviteCode(groupId, inviteCode);
        }
        
        // 检查是否已经是成员
        CheckUtils.throwIf(groupService.isMember(groupId, userId), "您已经是该群组的成员");
        
        // 加入群组
        groupService.addMember(groupId, userId, GroupRoleEnum.MEMBER);
        
        log.info("用户 {} 加入群组 {} 成功", userId, groupId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void leaveGroup(Long groupId) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查是否是群主
        CheckUtils.throwIf(groupService.isOwner(groupId, userId), "群主不能离开群组，请先转让群主权限");
        
        // 离开群组
        groupService.removeMember(groupId, userId);
        
        log.info("用户 {} 离开群组 {} 成功", userId, groupId);
    }

    @Override
    public List<ApiGroupResp.MemberInfo> getMembers(Long groupId) {
        // 检查权限
        Long userId = SecurityContextHolder.getUserId();
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组的成员");
        
        // TODO: 实现获取成员列表的逻辑
        return List.of();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addMember(Long groupId, Long userId, String role) {
        Long currentUserId = SecurityContextHolder.getUserId();
        
        // 检查权限
        CheckUtils.throwIf(!groupService.isAdmin(groupId, currentUserId), "您没有权限添加成员");
        
        // 添加成员
        GroupRoleEnum roleEnum = GroupRoleEnum.valueOf(role.toUpperCase());
        groupService.addMember(groupId, userId, roleEnum);
        
        log.info("用户 {} 被添加到群组 {}，角色: {}", userId, groupId, role);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeMember(Long groupId, Long userId) {
        Long currentUserId = SecurityContextHolder.getUserId();
        
        // 检查权限
        CheckUtils.throwIf(!groupService.isAdmin(groupId, currentUserId), "您没有权限移除成员");
        CheckUtils.throwIf(groupService.isOwner(groupId, userId), "不能移除群主");
        
        // 移除成员
        groupService.removeMember(groupId, userId);
        
        log.info("用户 {} 被从群组 {} 移除", userId, groupId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMemberRole(Long groupId, Long userId, String role) {
        Long currentUserId = SecurityContextHolder.getUserId();
        
        // 检查权限
        CheckUtils.throwIf(!groupService.isOwner(groupId, currentUserId), "只有群主可以修改成员角色");
        CheckUtils.throwIf(userId.equals(currentUserId), "不能修改自己的角色");
        
        // 更新角色
        GroupRoleEnum roleEnum = GroupRoleEnum.valueOf(role.toUpperCase());
        groupService.updateMemberRole(groupId, userId, roleEnum);
        
        log.info("群组 {} 中用户 {} 的角色更新为: {}", groupId, userId, role);
    }

    @Override
    public String generateInviteCode(Long groupId, int expireHours) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查权限
        CheckUtils.throwIf(!groupService.isAdmin(groupId, userId), "您没有权限生成邀请码");
        
        // 生成邀请码
        String inviteCode = IdUtil.fastSimpleUUID().substring(0, 8).toUpperCase();
        LocalDateTime expireTime = LocalDateTime.now().plusHours(expireHours);
        
        // 缓存邀请码信息
        inviteCodeCache.put(inviteCode, new InviteCodeInfo(groupId, expireTime));
        
        log.info("为群组 {} 生成邀请码: {}，有效期至: {}", groupId, inviteCode, expireTime);
        return inviteCode;
    }

    @Override
    public ApiGroupResp.GroupStatistics getStatistics(Long groupId) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查权限
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组的成员");
        
        // TODO: 实现统计逻辑
        ApiGroupResp.GroupStatistics statistics = new ApiGroupResp.GroupStatistics();
        statistics.setTotalTransactions(0L);
        statistics.setTotalIncome(BigDecimal.ZERO);
        statistics.setTotalExpense(BigDecimal.ZERO);
        statistics.setNetIncome(BigDecimal.ZERO);
        statistics.setMonthlyTransactions(0L);
        statistics.setMonthlyIncome(BigDecimal.ZERO);
        statistics.setMonthlyExpense(BigDecimal.ZERO);
        statistics.setActiveMembers(0);
        
        return statistics;
    }

    @Override
    public ApiGroupResp.GroupSettings getSettings(Long groupId) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查权限
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组的成员");
        
        // TODO: 实现获取设置的逻辑
        ApiGroupResp.GroupSettings settings = new ApiGroupResp.GroupSettings();
        settings.setAllowMemberInvite(true);
        settings.setRequireApproval(false);
        settings.setAllowMemberCreateCategory(true);
        settings.setAllowMemberCreateTag(true);
        settings.setEnableBudget(true);
        settings.setEnableDebtTracking(true);
        settings.setEnablePeriodicReport(true);
        settings.setReportFrequency("WEEKLY");
        settings.setTimezone("Asia/Shanghai");
        settings.setLanguage("zh-CN");
        
        return settings;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSettings(Long groupId, ApiGroupResp.GroupSettings settings) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查权限
        CheckUtils.throwIf(!groupService.isAdmin(groupId, userId), "您没有权限修改群组设置");
        
        // TODO: 实现更新设置的逻辑
        updateGroupSettings(groupId, settings);
        
        log.info("群组 {} 设置更新成功", groupId);
    }

    @Override
    public List<ApiGroupResp> search(String keyword, int limit) {
        // TODO: 实现搜索逻辑
        GroupQuery query = new GroupQuery();
        query.setName(keyword);
        
        List<GroupListResp> groupList = groupService.list(query);
        return groupList.stream()
            .limit(limit)
            .map(this::convertToApiGroupResp)
            .collect(Collectors.toList());
    }

    @Override
    public List<ApiGroupResp> getMyGroups() {
        Long userId = SecurityContextHolder.getUserId();
        
        // TODO: 实现获取用户群组的逻辑
        GroupQuery query = new GroupQuery();
        List<GroupListResp> groupList = groupService.list(query);
        
        return groupList.stream()
            .map(this::convertToApiGroupResp)
            .collect(Collectors.toList());
    }

    @Override
    public List<String> checkPermissions(Long groupId) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查用户在群组中的权限
        List<String> permissions = List.of();
        
        if (groupService.isOwner(groupId, userId)) {
            permissions = List.of("ALL");
        } else if (groupService.isAdmin(groupId, userId)) {
            permissions = List.of("MANAGE_MEMBERS", "MANAGE_SETTINGS", "CREATE_TRANSACTION", "VIEW_REPORTS");
        } else if (groupService.isMember(groupId, userId)) {
            permissions = List.of("CREATE_TRANSACTION", "VIEW_REPORTS");
        }
        
        return permissions;
    }

    @Override
    public PageResp<ApiGroupResp.ActivityLog> getActivityLogs(Long groupId, PageQuery pageQuery) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查权限
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组的成员");
        
        // TODO: 实现获取活动日志的逻辑
        return PageResp.<ApiGroupResp.ActivityLog>builder()
            .list(List.of())
            .total(0L)
            .build();
    }

    @Override
    public String exportData(Long groupId, String format, String dataType) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查权限
        CheckUtils.throwIf(!groupService.isAdmin(groupId, userId), "您没有权限导出数据");
        
        // TODO: 实现数据导出逻辑
        String fileName = String.format("group_%d_%s_%s.%s", groupId, dataType, 
            LocalDateTime.now().toString().substring(0, 10), format);
        
        log.info("导出群组 {} 数据，格式: {}，类型: {}，文件: {}", groupId, format, dataType, fileName);
        
        return "/downloads/" + fileName;
    }

    /**
     * 转换为API群组响应
     */
    private ApiGroupResp convertToApiGroupResp(GroupListResp groupResp) {
        ApiGroupResp apiGroupResp = new ApiGroupResp();
        BeanUtil.copyProperties(groupResp, apiGroupResp);
        
        // 设置当前用户角色
        Long userId = SecurityContextHolder.getUserId();
        if (groupService.isOwner(groupResp.getId(), userId)) {
            apiGroupResp.setCurrentUserRole("OWNER");
        } else if (groupService.isAdmin(groupResp.getId(), userId)) {
            apiGroupResp.setCurrentUserRole("ADMIN");
        } else if (groupService.isMember(groupResp.getId(), userId)) {
            apiGroupResp.setCurrentUserRole("MEMBER");
        }
        
        return apiGroupResp;
    }

    /**
     * 转换为API群组响应
     */
    private ApiGroupResp convertToApiGroupResp(GroupDetailResp groupDetail) {
        ApiGroupResp apiGroupResp = new ApiGroupResp();
        BeanUtil.copyProperties(groupDetail, apiGroupResp);
        
        // 设置当前用户角色
        Long userId = SecurityContextHolder.getUserId();
        if (groupService.isOwner(groupDetail.getId(), userId)) {
            apiGroupResp.setCurrentUserRole("OWNER");
        } else if (groupService.isAdmin(groupDetail.getId(), userId)) {
            apiGroupResp.setCurrentUserRole("ADMIN");
        } else if (groupService.isMember(groupDetail.getId(), userId)) {
            apiGroupResp.setCurrentUserRole("MEMBER");
        }
        
        return apiGroupResp;
    }

    /**
     * 验证邀请码
     */
    private void validateInviteCode(Long groupId, String inviteCode) {
        InviteCodeInfo codeInfo = inviteCodeCache.get(inviteCode);
        CheckUtils.throwIfNull(codeInfo, "邀请码无效");
        CheckUtils.throwIfNotEqual(groupId, codeInfo.getGroupId(), "邀请码不匹配");
        CheckUtils.throwIf(LocalDateTime.now().isAfter(codeInfo.getExpireTime()), "邀请码已过期");
    }

    /**
     * 更新群组设置
     */
    private void updateGroupSettings(Long groupId, Object settings) {
        // TODO: 实现群组设置更新逻辑
        log.debug("更新群组 {} 设置: {}", groupId, settings);
    }

    /**
     * 邀请码信息
     */
    private static class InviteCodeInfo {
        private final Long groupId;
        private final LocalDateTime expireTime;

        public InviteCodeInfo(Long groupId, LocalDateTime expireTime) {
            this.groupId = groupId;
            this.expireTime = expireTime;
        }

        public Long getGroupId() {
            return groupId;
        }

        public LocalDateTime getExpireTime() {
            return expireTime;
        }
    }
}
