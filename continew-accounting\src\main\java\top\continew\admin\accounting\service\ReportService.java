package top.continew.admin.accounting.service;

import jakarta.servlet.http.HttpServletResponse;
import top.continew.admin.accounting.mapper.ReportMapper;
import top.continew.admin.accounting.model.query.ReportQuery;
import top.continew.admin.accounting.model.resp.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 报表统计业务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface ReportService {

    /**
     * 获取报表总览数据
     *
     * @param query 查询条件
     * @return 总览数据
     */
    ReportOverviewResp getOverview(ReportQuery query);

    /**
     * 获取趋势数据
     *
     * @param query 查询条件
     * @return 趋势数据列表
     */
    List<ReportTrendResp> getTrend(ReportQuery query);

    /**
     * 获取分类统计数据
     *
     * @param query 查询条件
     * @return 分类统计列表
     */
    List<ReportCategoryResp> getCategoryStats(ReportQuery query);

    /**
     * 获取钱包统计数据
     *
     * @param query 查询条件
     * @return 钱包统计列表
     */
    List<ReportWalletResp> getWalletStats(ReportQuery query);

    /**
     * 获取成员统计数据
     *
     * @param query 查询条件
     * @return 成员统计列表
     */
    List<ReportMemberResp> getMemberStats(ReportQuery query);

    /**
     * 获取标签统计数据
     *
     * @param query 查询条件
     * @return 标签统计列表
     */
    List<ReportTagResp> getTagStats(ReportQuery query);

    /**
     * 获取收支对比数据
     *
     * @param groupId   群组ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 收支对比数据
     */
    ReportMapper.IncomeExpenseCompareResp getIncomeExpenseCompare(Long groupId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取月度统计数据
     *
     * @param groupId 群组ID
     * @param year    年份
     * @return 月度统计列表
     */
    List<ReportMapper.MonthlyStatResp> getMonthlyStats(Long groupId, Integer year);

    /**
     * 获取年度统计数据
     *
     * @param groupId   群组ID
     * @param startYear 开始年份
     * @param endYear   结束年份
     * @return 年度统计列表
     */
    List<ReportMapper.YearlyStatResp> getYearlyStats(Long groupId, Integer startYear, Integer endYear);

    /**
     * 获取预算执行情况
     *
     * @param groupId 群组ID
     * @param month   月份（格式：2025-01）
     * @return 预算执行列表
     */
    List<ReportMapper.BudgetExecutionResp> getBudgetExecution(Long groupId, String month);

    /**
     * 获取交易频率统计
     *
     * @param query 查询条件
     * @return 交易频率统计
     */
    ReportMapper.TransactionFrequencyResp getTransactionFrequency(ReportQuery query);

    /**
     * 获取时段分析数据
     *
     * @param query 查询条件
     * @return 时段分析列表
     */
    List<ReportMapper.TimeSlotAnalysisResp> getTimeSlotAnalysis(ReportQuery query);

    /**
     * 获取期间对比数据
     *
     * @param groupId        群组ID
     * @param currentStart   当前期间开始日期
     * @param currentEnd     当前期间结束日期
     * @param previousStart  上期开始日期
     * @param previousEnd    上期结束日期
     * @return 期间对比数据
     */
    ReportMapper.PeriodCompareResp getPeriodCompare(Long groupId, LocalDate currentStart, LocalDate currentEnd,
                                                   LocalDate previousStart, LocalDate previousEnd);

    /**
     * 导出财务报表
     *
     * @param query    查询条件
     * @param response 响应对象
     */
    void exportFinancialReport(ReportQuery query, HttpServletResponse response);

    /**
     * 导出分类统计报表
     *
     * @param query    查询条件
     * @param response 响应对象
     */
    void exportCategoryReport(ReportQuery query, HttpServletResponse response);

    /**
     * 导出成员统计报表
     *
     * @param query    查询条件
     * @param response 响应对象
     */
    void exportMemberReport(ReportQuery query, HttpServletResponse response);

    /**
     * 生成自定义报表
     *
     * @param query 查询条件
     * @return 自定义报表数据
     */
    CustomReportResp generateCustomReport(ReportQuery query);

    /**
     * 获取仪表盘数据
     *
     * @param groupId 群组ID
     * @return 仪表盘数据
     */
    DashboardResp getDashboard(Long groupId);

    /**
     * 获取快速统计数据
     *
     * @param groupId 群组ID
     * @param days    天数
     * @return 快速统计数据
     */
    QuickStatsResp getQuickStats(Long groupId, Integer days);

    /**
     * 自定义报表响应
     */
    class CustomReportResp {
        private String title;
        private String description;
        private List<ReportTrendResp> trendData;
        private List<ReportCategoryResp> categoryData;
        private List<ReportWalletResp> walletData;
        private List<ReportMemberResp> memberData;
        private List<ReportTagResp> tagData;

        // Getters and Setters
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public List<ReportTrendResp> getTrendData() { return trendData; }
        public void setTrendData(List<ReportTrendResp> trendData) { this.trendData = trendData; }
        public List<ReportCategoryResp> getCategoryData() { return categoryData; }
        public void setCategoryData(List<ReportCategoryResp> categoryData) { this.categoryData = categoryData; }
        public List<ReportWalletResp> getWalletData() { return walletData; }
        public void setWalletData(List<ReportWalletResp> walletData) { this.walletData = walletData; }
        public List<ReportMemberResp> getMemberData() { return memberData; }
        public void setMemberData(List<ReportMemberResp> memberData) { this.memberData = memberData; }
        public List<ReportTagResp> getTagData() { return tagData; }
        public void setTagData(List<ReportTagResp> tagData) { this.tagData = tagData; }
    }

    /**
     * 仪表盘响应
     */
    class DashboardResp {
        private ReportOverviewResp overview;
        private List<ReportTrendResp> recentTrend;
        private List<ReportCategoryResp> topCategories;
        private List<ReportWalletResp> walletSummary;
        private List<ReportMemberResp> memberActivity;

        // Getters and Setters
        public ReportOverviewResp getOverview() { return overview; }
        public void setOverview(ReportOverviewResp overview) { this.overview = overview; }
        public List<ReportTrendResp> getRecentTrend() { return recentTrend; }
        public void setRecentTrend(List<ReportTrendResp> recentTrend) { this.recentTrend = recentTrend; }
        public List<ReportCategoryResp> getTopCategories() { return topCategories; }
        public void setTopCategories(List<ReportCategoryResp> topCategories) { this.topCategories = topCategories; }
        public List<ReportWalletResp> getWalletSummary() { return walletSummary; }
        public void setWalletSummary(List<ReportWalletResp> walletSummary) { this.walletSummary = walletSummary; }
        public List<ReportMemberResp> getMemberActivity() { return memberActivity; }
        public void setMemberActivity(List<ReportMemberResp> memberActivity) { this.memberActivity = memberActivity; }
    }

    /**
     * 快速统计响应
     */
    class QuickStatsResp {
        private java.math.BigDecimal totalIncome;
        private java.math.BigDecimal totalExpense;
        private java.math.BigDecimal netIncome;
        private Integer transactionCount;
        private java.math.BigDecimal avgDailyExpense;
        private String topCategory;
        private String topWallet;

        // Getters and Setters
        public java.math.BigDecimal getTotalIncome() { return totalIncome; }
        public void setTotalIncome(java.math.BigDecimal totalIncome) { this.totalIncome = totalIncome; }
        public java.math.BigDecimal getTotalExpense() { return totalExpense; }
        public void setTotalExpense(java.math.BigDecimal totalExpense) { this.totalExpense = totalExpense; }
        public java.math.BigDecimal getNetIncome() { return netIncome; }
        public void setNetIncome(java.math.BigDecimal netIncome) { this.netIncome = netIncome; }
        public Integer getTransactionCount() { return transactionCount; }
        public void setTransactionCount(Integer transactionCount) { this.transactionCount = transactionCount; }
        public java.math.BigDecimal getAvgDailyExpense() { return avgDailyExpense; }
        public void setAvgDailyExpense(java.math.BigDecimal avgDailyExpense) { this.avgDailyExpense = avgDailyExpense; }
        public String getTopCategory() { return topCategory; }
        public void setTopCategory(String topCategory) { this.topCategory = topCategory; }
        public String getTopWallet() { return topWallet; }
        public void setTopWallet(String topWallet) { this.topWallet = topWallet; }
    }
}
