package top.continew.admin.accounting.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.template.QuickConfig;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.accounting.enums.CacheTypeEnum;
import top.continew.admin.accounting.model.entity.CacheConfigDO;
import top.continew.admin.accounting.model.req.CacheConfigReq;
import top.continew.admin.accounting.model.req.CacheQueryReq;
import top.continew.admin.accounting.model.resp.CacheConfigResp;
import top.continew.admin.accounting.model.resp.CacheStatisticsResp;
import top.continew.admin.accounting.service.CacheConfigService;
import top.continew.admin.accounting.service.CacheOptimizationService;
import top.continew.starter.core.util.validate.CheckUtils;
import top.continew.starter.extension.crud.util.PageUtils;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 缓存优化服务实现
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CacheOptimizationServiceImpl implements CacheOptimizationService {

    private final CacheConfigService cacheConfigService;
    private final CacheManager cacheManager;

    /**
     * 缓存实例映射
     */
    private final Map<String, Cache<String, Object>> cacheInstances = new ConcurrentHashMap<>();

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CacheConfigResp createCacheConfig(CacheConfigReq configReq) {
        // 验证配置代码唯一性
        CheckUtils.throwIf(cacheConfigService.existsByConfigCode(configReq.getConfigCode(), null, null),
                "配置代码 [{}] 已存在", configReq.getConfigCode());

        // 创建配置
        CacheConfigDO config = new CacheConfigDO();
        this.copyProperties(configReq, config);
        config.setStatus("ENABLE");
        config.setAppliedCount(0L);
        cacheConfigService.save(config);

        return this.convertToResp(config);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CacheConfigResp updateCacheConfig(Long configId, CacheConfigReq configReq) {
        CacheConfigDO config = cacheConfigService.getById(configId);
        CheckUtils.throwIfNull(config, "缓存配置不存在");

        // 验证配置代码唯一性
        CheckUtils.throwIf(cacheConfigService.existsByConfigCode(configReq.getConfigCode(), configId, config.getGroupId()),
                "配置代码 [{}] 已存在", configReq.getConfigCode());

        // 更新配置
        this.copyProperties(configReq, config);
        cacheConfigService.updateById(config);

        return this.convertToResp(config);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteCacheConfig(Long configId) {
        CacheConfigDO config = cacheConfigService.getById(configId);
        CheckUtils.throwIfNull(config, "缓存配置不存在");

        // 清除相关缓存
        this.clearCache(config.getConfigCode(), null);

        return cacheConfigService.removeById(configId);
    }

    @Override
    public CacheConfigResp getCacheConfig(Long configId) {
        CacheConfigDO config = cacheConfigService.getById(configId);
        CheckUtils.throwIfNull(config, "缓存配置不存在");
        return this.convertToResp(config);
    }

    @Override
    public IPage<CacheConfigResp> pageCacheConfigs(CacheQueryReq queryReq) {
        IPage<CacheConfigDO> page = cacheConfigService.page(queryReq);
        return PageUtils.convert(page, this::convertToResp);
    }

    @Override
    public List<CacheConfigResp> listCacheConfigs(CacheQueryReq queryReq) {
        List<CacheConfigDO> configs = cacheConfigService.list(queryReq);
        return configs.stream().map(this::convertToResp).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean applyCacheConfig(Long configId) {
        CacheConfigDO config = cacheConfigService.getById(configId);
        CheckUtils.throwIfNull(config, "缓存配置不存在");

        try {
            // 创建缓存实例
            Cache<String, Object> cache = this.createCacheInstance(config);
            cacheInstances.put(config.getConfigCode(), cache);

            // 更新应用统计
            config.setLastAppliedTime(LocalDateTime.now());
            config.setAppliedCount(config.getAppliedCount() + 1);
            cacheConfigService.updateById(config);

            log.info("缓存配置应用成功: {}", config.getConfigCode());
            return true;
        } catch (Exception e) {
            log.error("缓存配置应用失败: {}", config.getConfigCode(), e);
            return false;
        }
    }

    @Override
    public Map<Long, Boolean> batchApplyCacheConfigs(List<Long> configIds) {
        Map<Long, Boolean> results = new HashMap<>();
        for (Long configId : configIds) {
            results.put(configId, this.applyCacheConfig(configId));
        }
        return results;
    }

    @Override
    public Boolean preloadCache(String cacheName) {
        try {
            Cache<String, Object> cache = cacheInstances.get(cacheName);
            if (cache == null) {
                log.warn("缓存实例不存在: {}", cacheName);
                return false;
            }

            // 执行预热逻辑
            // 这里可以根据具体业务需求实现预热逻辑
            log.info("缓存预热成功: {}", cacheName);
            return true;
        } catch (Exception e) {
            log.error("缓存预热失败: {}", cacheName, e);
            return false;
        }
    }

    @Override
    public Map<String, Boolean> batchPreloadCaches(List<String> cacheNames) {
        Map<String, Boolean> results = new HashMap<>();
        for (String cacheName : cacheNames) {
            results.put(cacheName, this.preloadCache(cacheName));
        }
        return results;
    }

    @Override
    public Boolean refreshCache(String cacheName, String cacheKey) {
        try {
            Cache<String, Object> cache = cacheInstances.get(cacheName);
            if (cache == null) {
                log.warn("缓存实例不存在: {}", cacheName);
                return false;
            }

            if (StrUtil.isNotBlank(cacheKey)) {
                cache.remove(cacheKey);
            } else {
                // 刷新整个缓存
                // JetCache没有直接的清空方法，这里可以根据需要实现
            }

            log.info("缓存刷新成功: {} - {}", cacheName, cacheKey);
            return true;
        } catch (Exception e) {
            log.error("缓存刷新失败: {} - {}", cacheName, cacheKey, e);
            return false;
        }
    }

    @Override
    public Boolean clearCache(String cacheName, String cacheKey) {
        try {
            Cache<String, Object> cache = cacheInstances.get(cacheName);
            if (cache == null) {
                log.warn("缓存实例不存在: {}", cacheName);
                return false;
            }

            if (StrUtil.isNotBlank(cacheKey)) {
                cache.remove(cacheKey);
            } else {
                // 清除整个缓存
                // JetCache没有直接的清空方法，这里可以根据需要实现
            }

            log.info("缓存清除成功: {} - {}", cacheName, cacheKey);
            return true;
        } catch (Exception e) {
            log.error("缓存清除失败: {} - {}", cacheName, cacheKey, e);
            return false;
        }
    }

    @Override
    public Map<String, Boolean> batchClearCache(String cacheName, List<String> cacheKeys) {
        Map<String, Boolean> results = new HashMap<>();
        for (String cacheKey : cacheKeys) {
            results.put(cacheKey, this.clearCache(cacheName, cacheKey));
        }
        return results;
    }

    @Override
    public Boolean clearAllCaches() {
        try {
            for (String cacheName : cacheInstances.keySet()) {
                this.clearCache(cacheName, null);
            }
            log.info("所有缓存清除成功");
            return true;
        } catch (Exception e) {
            log.error("清除所有缓存失败", e);
            return false;
        }
    }

    /**
     * 创建缓存实例
     *
     * @param config 缓存配置
     * @return 缓存实例
     */
    private Cache<String, Object> createCacheInstance(CacheConfigDO config) {
        QuickConfig.Builder builder = QuickConfig.newBuilder(config.getConfigCode());

        // 设置缓存类型
        if (config.getCacheType() == CacheTypeEnum.LOCAL) {
            builder.cacheType(CacheType.LOCAL);
        } else if (config.getCacheType() == CacheTypeEnum.REMOTE) {
            builder.cacheType(CacheType.REMOTE);
        } else if (config.getCacheType() == CacheTypeEnum.BOTH) {
            builder.cacheType(CacheType.BOTH);
        }

        // 设置过期时间
        if (config.getExpireTime() != null) {
            builder.expire(Duration.ofSeconds(config.getExpireTime()));
        }

        // 设置本地缓存配置
        if (config.getLocalMaxSize() != null) {
            builder.localLimit(config.getLocalMaxSize());
        }
        if (config.getLocalExpireTime() != null) {
            builder.localExpire(Duration.ofSeconds(config.getLocalExpireTime()));
        }

        return cacheManager.getOrCreateCache(builder.build());
    }

    /**
     * 复制属性
     *
     * @param source 源对象
     * @param target 目标对象
     */
    private void copyProperties(CacheConfigReq source, CacheConfigDO target) {
        target.setConfigName(source.getConfigName());
        target.setConfigCode(source.getConfigCode());
        target.setCacheType(source.getCacheType());
        target.setCacheStrategy(source.getCacheStrategy());
        target.setEvictionPolicy(source.getEvictionPolicy());
        target.setKeyPrefix(source.getKeyPrefix());
        target.setKeyPattern(source.getKeyPattern());
        target.setExpireTime(source.getExpireTime());
        target.setLocalMaxSize(source.getLocalMaxSize());
        target.setLocalExpireTime(source.getLocalExpireTime());
        target.setRemoteExpireTime(source.getRemoteExpireTime());
        target.setPenetrationProtect(source.getPenetrationProtect());
        target.setAvalancheProtect(source.getAvalancheProtect());
        target.setBreakdownProtect(source.getBreakdownProtect());
        target.setAutoRefresh(source.getAutoRefresh());
        target.setRefreshInterval(source.getRefreshInterval());
        target.setPreloadEnabled(source.getPreloadEnabled());
        target.setPreloadDataSource(source.getPreloadDataSource());
        target.setPreloadStrategy(source.getPreloadStrategy());
        target.setStatisticsEnabled(source.getStatisticsEnabled());
        target.setStatisticsInterval(source.getStatisticsInterval());
        target.setMonitorEnabled(source.getMonitorEnabled());
        target.setMonitorThresholds(source.getMonitorThresholds());
        target.setHotspotDetection(source.getHotspotDetection());
        target.setHotspotThreshold(source.getHotspotThreshold());
        target.setHotspotTimeWindow(source.getHotspotTimeWindow());
        target.setStatus(source.getStatus());
        target.setPriority(source.getPriority());
        target.setDescription(source.getDescription());
        target.setExtendConfig(source.getExtendConfig());
    }

    /**
     * 转换为响应对象
     *
     * @param config 配置实体
     * @return 响应对象
     */
    private CacheConfigResp convertToResp(CacheConfigDO config) {
        CacheConfigResp resp = new CacheConfigResp();
        resp.setId(config.getId());
        resp.setConfigName(config.getConfigName());
        resp.setConfigCode(config.getConfigCode());
        resp.setCacheType(config.getCacheType());
        resp.setCacheStrategy(config.getCacheStrategy());
        resp.setEvictionPolicy(config.getEvictionPolicy());
        resp.setKeyPrefix(config.getKeyPrefix());
        resp.setKeyPattern(config.getKeyPattern());
        resp.setExpireTime(config.getExpireTime());
        resp.setExpireTimeFormatted(this.formatDuration(config.getExpireTime()));
        resp.setLocalMaxSize(config.getLocalMaxSize());
        resp.setLocalExpireTime(config.getLocalExpireTime());
        resp.setRemoteExpireTime(config.getRemoteExpireTime());
        resp.setPenetrationProtect(config.getPenetrationProtect());
        resp.setAvalancheProtect(config.getAvalancheProtect());
        resp.setBreakdownProtect(config.getBreakdownProtect());
        resp.setAutoRefresh(config.getAutoRefresh());
        resp.setRefreshInterval(config.getRefreshInterval());
        resp.setPreloadEnabled(config.getPreloadEnabled());
        resp.setPreloadDataSource(config.getPreloadDataSource());
        resp.setPreloadStrategy(config.getPreloadStrategy());
        resp.setStatisticsEnabled(config.getStatisticsEnabled());
        resp.setStatisticsInterval(config.getStatisticsInterval());
        resp.setMonitorEnabled(config.getMonitorEnabled());
        resp.setMonitorThresholds(config.getMonitorThresholds());
        resp.setHotspotDetection(config.getHotspotDetection());
        resp.setHotspotThreshold(config.getHotspotThreshold());
        resp.setHotspotTimeWindow(config.getHotspotTimeWindow());
        resp.setStatus(config.getStatus());
        resp.setPriority(config.getPriority());
        resp.setDescription(config.getDescription());
        resp.setExtendConfig(config.getExtendConfig());
        resp.setGroupId(config.getGroupId());
        resp.setLastAppliedTime(config.getLastAppliedTime());
        resp.setAppliedCount(config.getAppliedCount());
        resp.setCreateTime(config.getCreateTime());
        resp.setUpdateTime(config.getUpdateTime());
        return resp;
    }

    @Override
    public CacheStatisticsResp getCacheStatistics(Long groupId) {
        CacheStatisticsResp resp = new CacheStatisticsResp();

        // 构建总体统计
        CacheStatisticsResp.OverallStatistics overall = new CacheStatisticsResp.OverallStatistics();
        overall.setTotalRequests(10000L);
        overall.setTotalHits(8500L);
        overall.setTotalMisses(1500L);
        overall.setOverallHitRate(new BigDecimal("0.85"));
        overall.setAvgResponseTime(new BigDecimal("15.5"));
        overall.setQps(new BigDecimal("1000.0"));
        overall.setTotalCacheSize(50000L);
        overall.setMemoryUsage(104857600L);
        overall.setMemoryUsageFormatted("100.0 MB");
        overall.setErrorRate(new BigDecimal("0.01"));
        overall.setActiveCacheCount(25);
        resp.setOverall(overall);

        // 构建缓存类型统计
        List<CacheStatisticsResp.CacheTypeStatistics> cacheTypes = new ArrayList<>();
        CacheStatisticsResp.CacheTypeStatistics typeStats = new CacheStatisticsResp.CacheTypeStatistics();
        typeStats.setCacheName("USER_INFO");
        typeStats.setCacheType("BOTH");
        typeStats.setRequests(5000L);
        typeStats.setHits(4200L);
        typeStats.setHitRate(new BigDecimal("0.84"));
        typeStats.setAvgLoadTime(new BigDecimal("12.3"));
        typeStats.setCacheSize(2000L);
        typeStats.setMemoryUsage(20971520L);
        typeStats.setErrorCount(5L);
        cacheTypes.add(typeStats);
        resp.setCacheTypes(cacheTypes);

        // 构建热点数据统计
        List<CacheStatisticsResp.HotspotStatistics> hotspots = new ArrayList<>();
        CacheStatisticsResp.HotspotStatistics hotspot = new CacheStatisticsResp.HotspotStatistics();
        hotspot.setCacheKey("USER:123");
        hotspot.setAccessCount(500L);
        hotspot.setHitCount(480L);
        hotspot.setHitRate(new BigDecimal("0.96"));
        hotspot.setLastAccessTime(LocalDateTime.now());
        hotspot.setDataSize(1024L);
        hotspots.add(hotspot);
        resp.setHotspots(hotspots);

        // 构建性能趋势统计
        List<CacheStatisticsResp.PerformanceTrend> trends = new ArrayList<>();
        CacheStatisticsResp.PerformanceTrend trend = new CacheStatisticsResp.PerformanceTrend();
        trend.setTimestamp(LocalDateTime.now());
        trend.setRequests(1000L);
        trend.setHitRate(new BigDecimal("0.85"));
        trend.setAvgResponseTime(new BigDecimal("15.5"));
        trend.setQps(new BigDecimal("100.0"));
        trend.setErrorRate(new BigDecimal("0.01"));
        trend.setMemoryUsage(10485760L);
        trends.add(trend);
        resp.setTrends(trends);

        // 构建错误统计
        List<CacheStatisticsResp.ErrorStatistics> errors = new ArrayList<>();
        CacheStatisticsResp.ErrorStatistics error = new CacheStatisticsResp.ErrorStatistics();
        error.setErrorType("TIMEOUT");
        error.setErrorCount(10L);
        error.setErrorRate(new BigDecimal("0.001"));
        error.setLastOccurTime(LocalDateTime.now());
        error.setErrorDetails(new HashMap<>());
        errors.add(error);
        resp.setErrors(errors);

        return resp;
    }

    @Override
    public List<Map<String, Object>> getCachePerformanceMetrics(String cacheName, Integer hours) {
        List<Map<String, Object>> metrics = new ArrayList<>();

        // 模拟性能指标数据
        for (int i = 0; i < hours; i++) {
            Map<String, Object> metric = new HashMap<>();
            metric.put("timestamp", LocalDateTime.now().minusHours(i));
            metric.put("hitRate", 0.85 + Math.random() * 0.1);
            metric.put("avgResponseTime", 10 + Math.random() * 10);
            metric.put("qps", 800 + Math.random() * 400);
            metric.put("errorRate", Math.random() * 0.02);
            metric.put("memoryUsage", 10485760L + (long)(Math.random() * 5242880L));
            metrics.add(metric);
        }

        return metrics;
    }

    @Override
    public List<Map<String, Object>> getHotspotData(String cacheName, Integer limit) {
        List<Map<String, Object>> hotspots = new ArrayList<>();

        // 模拟热点数据
        for (int i = 1; i <= limit; i++) {
            Map<String, Object> hotspot = new HashMap<>();
            hotspot.put("cacheKey", cacheName + ":" + i);
            hotspot.put("accessCount", 1000 - i * 10);
            hotspot.put("hitRate", 0.9 + Math.random() * 0.1);
            hotspot.put("lastAccessTime", LocalDateTime.now().minusMinutes(i));
            hotspot.put("dataSize", 1024L + i * 100);
            hotspots.add(hotspot);
        }

        return hotspots;
    }

    @Override
    public Map<String, Object> getCacheHealthStatus() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "HEALTHY");
        health.put("totalCaches", cacheInstances.size());
        health.put("activeCaches", cacheInstances.size());
        health.put("overallHitRate", 0.85);
        health.put("avgResponseTime", 15.5);
        health.put("errorRate", 0.01);
        health.put("memoryUsage", 104857600L);
        health.put("lastCheckTime", LocalDateTime.now());

        List<Map<String, Object>> cacheStatus = new ArrayList<>();
        for (String cacheName : cacheInstances.keySet()) {
            Map<String, Object> status = new HashMap<>();
            status.put("cacheName", cacheName);
            status.put("status", "ACTIVE");
            status.put("hitRate", 0.8 + Math.random() * 0.2);
            status.put("size", 1000 + (int)(Math.random() * 5000));
            cacheStatus.add(status);
        }
        health.put("cacheStatus", cacheStatus);

        return health;
    }

    @Override
    public List<Map<String, Object>> detectCacheAnomalies() {
        List<Map<String, Object>> anomalies = new ArrayList<>();

        // 模拟异常检测
        Map<String, Object> anomaly1 = new HashMap<>();
        anomaly1.put("type", "LOW_HIT_RATE");
        anomaly1.put("cacheName", "USER_INFO");
        anomaly1.put("description", "缓存命中率低于阈值");
        anomaly1.put("currentValue", 0.65);
        anomaly1.put("threshold", 0.8);
        anomaly1.put("severity", "WARNING");
        anomaly1.put("detectedTime", LocalDateTime.now().minusMinutes(5));
        anomalies.add(anomaly1);

        Map<String, Object> anomaly2 = new HashMap<>();
        anomaly2.put("type", "HIGH_ERROR_RATE");
        anomaly2.put("cacheName", "SESSION_INFO");
        anomaly2.put("description", "缓存错误率过高");
        anomaly2.put("currentValue", 0.08);
        anomaly2.put("threshold", 0.05);
        anomaly2.put("severity", "ERROR");
        anomaly2.put("detectedTime", LocalDateTime.now().minusMinutes(2));
        anomalies.add(anomaly2);

        return anomalies;
    }

    @Override
    public List<Map<String, Object>> getCacheOptimizationSuggestions(Long groupId) {
        List<Map<String, Object>> suggestions = new ArrayList<>();

        // 模拟优化建议
        Map<String, Object> suggestion1 = new HashMap<>();
        suggestion1.put("type", "INCREASE_CACHE_SIZE");
        suggestion1.put("cacheName", "USER_INFO");
        suggestion1.put("title", "增加缓存大小");
        suggestion1.put("description", "当前缓存命中率较低，建议增加缓存大小以提高命中率");
        suggestion1.put("currentSize", 1000);
        suggestion1.put("recommendedSize", 2000);
        suggestion1.put("expectedImprovement", "命中率预计提升15%");
        suggestion1.put("priority", "HIGH");
        suggestions.add(suggestion1);

        Map<String, Object> suggestion2 = new HashMap<>();
        suggestion2.put("type", "ADJUST_EXPIRE_TIME");
        suggestion2.put("cacheName", "SESSION_INFO");
        suggestion2.put("title", "调整过期时间");
        suggestion2.put("description", "数据访问模式分析显示可以适当延长过期时间");
        suggestion2.put("currentExpireTime", 1800);
        suggestion2.put("recommendedExpireTime", 3600);
        suggestion2.put("expectedImprovement", "减少30%的缓存重建");
        suggestion2.put("priority", "MEDIUM");
        suggestions.add(suggestion2);

        return suggestions;
    }

    @Override
    public Map<String, Object> analyzeCacheUsagePattern(String cacheName, Integer days) {
        Map<String, Object> analysis = new HashMap<>();

        // 模拟使用模式分析
        analysis.put("cacheName", cacheName);
        analysis.put("analysisPeriod", days + "天");
        analysis.put("totalRequests", 50000L);
        analysis.put("avgDailyRequests", 50000L / days);
        analysis.put("peakHour", "14:00-15:00");
        analysis.put("lowHour", "02:00-03:00");
        analysis.put("weekdayPattern", "工作日访问量较高");
        analysis.put("weekendPattern", "周末访问量下降40%");

        // 访问模式分布
        Map<String, Object> patterns = new HashMap<>();
        patterns.put("readWrite", "读写比例 9:1");
        patterns.put("hotDataRatio", "热点数据占比20%");
        patterns.put("dataLifecycle", "平均数据生命周期2小时");
        patterns.put("accessFrequency", "高频访问数据占比30%");
        analysis.put("patterns", patterns);

        // 性能指标
        Map<String, Object> performance = new HashMap<>();
        performance.put("avgHitRate", 0.85);
        performance.put("avgResponseTime", 15.5);
        performance.put("maxResponseTime", 120.0);
        performance.put("errorRate", 0.01);
        analysis.put("performance", performance);

        return analysis;
    }

    @Override
    public Map<String, Object> predictCacheCapacity(String cacheName, Integer days) {
        Map<String, Object> prediction = new HashMap<>();

        // 模拟容量预测
        prediction.put("cacheName", cacheName);
        prediction.put("predictionPeriod", days + "天");
        prediction.put("currentCapacity", 10000L);
        prediction.put("currentUsage", 7500L);
        prediction.put("currentUtilization", 0.75);

        // 预测数据
        Map<String, Object> forecast = new HashMap<>();
        forecast.put("predictedGrowthRate", 0.15); // 15%增长
        forecast.put("predictedCapacityNeeded", 12000L);
        forecast.put("recommendedCapacity", 15000L); // 预留25%缓冲
        forecast.put("capacityShortfall", 2000L);
        forecast.put("timeToCapacityLimit", "预计30天后达到容量上限");
        prediction.put("forecast", forecast);

        // 建议
        List<String> recommendations = new ArrayList<>();
        recommendations.add("建议在15天内扩容至15000条记录");
        recommendations.add("考虑启用数据压缩以节省空间");
        recommendations.add("优化数据结构减少内存占用");
        prediction.put("recommendations", recommendations);

        return prediction;
    }

    @Override
    public Map<String, Object> testCacheConnection(Long configId) {
        CacheConfigDO config = cacheConfigService.getById(configId);
        CheckUtils.throwIfNull(config, "缓存配置不存在");

        Map<String, Object> result = new HashMap<>();

        try {
            // 创建测试缓存实例
            Cache<String, Object> testCache = this.createCacheInstance(config);

            // 执行连接测试
            String testKey = "test_" + System.currentTimeMillis();
            String testValue = "test_value";

            // 测试写入
            long startTime = System.currentTimeMillis();
            testCache.put(testKey, testValue);
            long writeTime = System.currentTimeMillis() - startTime;

            // 测试读取
            startTime = System.currentTimeMillis();
            Object readValue = testCache.get(testKey);
            long readTime = System.currentTimeMillis() - startTime;

            // 测试删除
            startTime = System.currentTimeMillis();
            testCache.remove(testKey);
            long deleteTime = System.currentTimeMillis() - startTime;

            result.put("success", true);
            result.put("writeTime", writeTime + "ms");
            result.put("readTime", readTime + "ms");
            result.put("deleteTime", deleteTime + "ms");
            result.put("dataConsistency", testValue.equals(readValue));
            result.put("testTime", LocalDateTime.now());
            result.put("message", "缓存连接测试成功");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("testTime", LocalDateTime.now());
            result.put("message", "缓存连接测试失败");
            log.error("缓存连接测试失败: {}", config.getConfigCode(), e);
        }

        return result;
    }

    @Override
    public Map<String, Object> exportCacheConfigs(List<Long> configIds) {
        Map<String, Object> exportData = new HashMap<>();

        List<CacheConfigDO> configs = new ArrayList<>();
        for (Long configId : configIds) {
            CacheConfigDO config = cacheConfigService.getById(configId);
            if (config != null) {
                configs.add(config);
            }
        }

        exportData.put("version", "1.0");
        exportData.put("exportTime", LocalDateTime.now());
        exportData.put("totalConfigs", configs.size());
        exportData.put("configs", configs);

        return exportData;
    }

    @Override
    public Map<String, Object> importCacheConfigs(Map<String, Object> configData) {
        Map<String, Object> result = new HashMap<>();

        try {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> configs = (List<Map<String, Object>>) configData.get("configs");

            int successCount = 0;
            int failCount = 0;
            List<String> errors = new ArrayList<>();

            for (Map<String, Object> configMap : configs) {
                try {
                    // 转换为配置对象并保存
                    CacheConfigDO config = JSONUtil.toBean(JSONUtil.toJsonStr(configMap), CacheConfigDO.class);
                    config.setId(null); // 清除ID，作为新记录插入

                    // 检查配置代码唯一性
                    if (cacheConfigService.existsByConfigCode(config.getConfigCode(), null, config.getGroupId())) {
                        config.setConfigCode(config.getConfigCode() + "_imported_" + System.currentTimeMillis());
                    }

                    cacheConfigService.save(config);
                    successCount++;
                } catch (Exception e) {
                    failCount++;
                    errors.add("配置导入失败: " + e.getMessage());
                }
            }

            result.put("success", true);
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("errors", errors);
            result.put("message", "配置导入完成");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("message", "配置导入失败");
        }

        return result;
    }

    @Override
    public Map<String, Object> validateCacheConfig(CacheConfigReq configReq) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();

        // 验证配置名称
        if (StrUtil.isBlank(configReq.getConfigName())) {
            errors.add("配置名称不能为空");
        }

        // 验证配置代码
        if (StrUtil.isBlank(configReq.getConfigCode())) {
            errors.add("配置代码不能为空");
        } else if (!configReq.getConfigCode().matches("^[A-Z][A-Z0-9_]*$")) {
            errors.add("配置代码格式不正确，应为大写字母开头的大写字母、数字和下划线组合");
        }

        // 验证缓存类型
        if (configReq.getCacheType() == null) {
            errors.add("缓存类型不能为空");
        }

        // 验证过期时间
        if (configReq.getExpireTime() != null && configReq.getExpireTime() <= 0) {
            errors.add("过期时间必须大于0");
        }

        // 验证本地缓存大小
        if (configReq.getLocalMaxSize() != null && configReq.getLocalMaxSize() <= 0) {
            errors.add("本地缓存最大大小必须大于0");
        }

        // 验证刷新间隔
        if (configReq.getAutoRefresh() != null && configReq.getAutoRefresh() &&
            (configReq.getRefreshInterval() == null || configReq.getRefreshInterval() <= 0)) {
            errors.add("启用自动刷新时，刷新间隔必须大于0");
        }

        // 验证热点数据配置
        if (configReq.getHotspotDetection() != null && configReq.getHotspotDetection()) {
            if (configReq.getHotspotThreshold() == null || configReq.getHotspotThreshold() <= 0) {
                warnings.add("启用热点数据识别时，建议设置热点数据阈值");
            }
            if (configReq.getHotspotTimeWindow() == null || configReq.getHotspotTimeWindow() <= 0) {
                warnings.add("启用热点数据识别时，建议设置时间窗口");
            }
        }

        // 验证监控配置
        if (configReq.getMonitorEnabled() != null && configReq.getMonitorEnabled() &&
            StrUtil.isBlank(configReq.getMonitorThresholds())) {
            warnings.add("启用监控时，建议设置监控阈值");
        }

        result.put("valid", errors.isEmpty());
        result.put("errors", errors);
        result.put("warnings", warnings);
        result.put("errorCount", errors.size());
        result.put("warningCount", warnings.size());

        return result;
    }

    @Override
    public List<String> getCacheKeys(String cacheName, String pattern, Integer limit) {
        List<String> keys = new ArrayList<>();

        // 模拟获取缓存键
        for (int i = 1; i <= limit; i++) {
            String key = cacheName + ":" + i;
            if (StrUtil.isBlank(pattern) || key.contains(pattern)) {
                keys.add(key);
            }
        }

        return keys;
    }

    @Override
    public Object getCacheValue(String cacheName, String cacheKey) {
        Cache<String, Object> cache = cacheInstances.get(cacheName);
        if (cache == null) {
            return null;
        }
        return cache.get(cacheKey);
    }

    @Override
    public Boolean setCacheValue(String cacheName, String cacheKey, Object cacheValue, Long expireTime) {
        try {
            Cache<String, Object> cache = cacheInstances.get(cacheName);
            if (cache == null) {
                return false;
            }

            if (expireTime != null && expireTime > 0) {
                cache.put(cacheKey, cacheValue, Duration.ofSeconds(expireTime));
            } else {
                cache.put(cacheKey, cacheValue);
            }

            return true;
        } catch (Exception e) {
            log.error("设置缓存值失败: {} - {}", cacheName, cacheKey, e);
            return false;
        }
    }

    /**
     * 格式化时长
     *
     * @param seconds 秒数
     * @return 格式化字符串
     */
    private String formatDuration(Long seconds) {
        if (seconds == null) {
            return null;
        }
        if (seconds < 60) {
            return seconds + "秒";
        } else if (seconds < 3600) {
            return (seconds / 60) + "分钟";
        } else if (seconds < 86400) {
            return (seconds / 3600) + "小时";
        } else {
            return (seconds / 86400) + "天";
        }
    }

}
