package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import top.continew.admin.accounting.enums.FileAccessTypeEnum;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 文件上传请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "文件上传请求")
public class FileUploadReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 上传路径
     */
    @Schema(description = "上传路径", example = "/accounting/receipts/")
    private String uploadPath;

    /**
     * 存储配置代码
     */
    @Schema(description = "存储配置代码", example = "default")
    private String storageConfigCode;

    /**
     * 访问权限类型
     */
    @Schema(description = "访问权限类型", example = "PRIVATE")
    private FileAccessTypeEnum accessType;

    /**
     * 文件描述
     */
    @Schema(description = "文件描述", example = "收据图片")
    @Size(max = 500, message = "文件描述长度不能超过500个字符")
    private String fileDescription;

    /**
     * 文件标签
     */
    @Schema(description = "文件标签", example = "receipt,expense")
    @Size(max = 200, message = "文件标签长度不能超过200个字符")
    private String fileTags;

    /**
     * 关联业务ID
     */
    @Schema(description = "关联业务ID", example = "123456")
    private String businessId;

    /**
     * 关联业务类型
     */
    @Schema(description = "关联业务类型", example = "TRANSACTION")
    private String businessType;

    /**
     * 是否为临时文件
     */
    @Schema(description = "是否为临时文件", example = "false")
    private Boolean isTemporary;

    /**
     * 过期时间
     */
    @Schema(description = "过期时间")
    private LocalDateTime expireTime;

    /**
     * 是否生成缩略图
     */
    @Schema(description = "是否生成缩略图", example = "true")
    private Boolean generateThumbnail;

    /**
     * 是否添加水印
     */
    @Schema(description = "是否添加水印", example = "false")
    private Boolean addWatermark;

    /**
     * 是否进行安全扫描
     */
    @Schema(description = "是否进行安全扫描", example = "true")
    private Boolean securityScan;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

}
