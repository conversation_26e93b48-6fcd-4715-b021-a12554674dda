package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.admin.accounting.enums.TransactionType;
import top.continew.admin.accounting.model.entity.split.SplitInfo;
import top.continew.starter.data.mybatis.plus.base.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 账单实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "acc_transaction", autoResultMap = true)
@Schema(description = "账单信息")
public class TransactionDO extends BaseEntity {

    /**
     * 群组ID
     */
    @Schema(description = "群组ID")
    private Long groupId;

    /**
     * 记账用户ID
     */
    @Schema(description = "记账用户ID")
    private Long userId;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型")
    private TransactionType type;

    /**
     * 金额
     */
    @Schema(description = "金额")
    private BigDecimal amount;

    /**
     * 币种
     */
    @Schema(description = "币种")
    private String currency;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 标签列表
     */
    @Schema(description = "标签列表")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> tags;

    /**
     * 分类
     */
    @Schema(description = "分类")
    private String category;

    /**
     * 交易时间
     */
    @Schema(description = "交易时间")
    private LocalDateTime transactionDate;

    /**
     * 附件列表
     */
    @Schema(description = "附件列表")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> attachments;

    /**
     * 分摊信息
     */
    @Schema(description = "分摊信息")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private SplitInfo splitInfo;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;
}
