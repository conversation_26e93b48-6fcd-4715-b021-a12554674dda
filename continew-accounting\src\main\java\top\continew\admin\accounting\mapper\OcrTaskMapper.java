package top.continew.admin.accounting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.accounting.model.entity.OcrTask;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * OCR任务Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface OcrTaskMapper extends BaseMapper<OcrTask> {

    // ==================== 基础查询 ====================

    /**
     * 根据任务编号查询任务详情
     *
     * @param taskNumber 任务编号
     * @return 任务详情
     */
    OcrTask selectDetailByTaskNumber(@Param("taskNumber") String taskNumber);

    /**
     * 分页查询任务列表（包含创建人姓名）
     *
     * @param groupId 群组ID
     * @param status 状态
     * @param ocrEngine OCR引擎
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param keyword 关键字
     * @return 任务列表
     */
    List<OcrTask> selectPageWithUserName(@Param("groupId") Long groupId,
                                         @Param("status") String status,
                                         @Param("ocrEngine") String ocrEngine,
                                         @Param("startTime") LocalDateTime startTime,
                                         @Param("endTime") LocalDateTime endTime,
                                         @Param("keyword") String keyword);

    /**
     * 查询群组的任务列表
     *
     * @param groupId 群组ID
     * @param limit 限制数量
     * @return 任务列表
     */
    List<OcrTask> selectByGroupId(@Param("groupId") Long groupId, @Param("limit") Integer limit);

    /**
     * 查询用户的任务列表
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 任务列表
     */
    List<OcrTask> selectByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 查询处理中的任务
     *
     * @param timeoutMinutes 超时分钟数
     * @return 处理中的任务列表
     */
    List<OcrTask> selectProcessingTasks(@Param("timeoutMinutes") Integer timeoutMinutes);

    /**
     * 查询失败的任务
     *
     * @param groupId 群组ID
     * @param days 天数
     * @return 失败任务列表
     */
    List<OcrTask> selectFailedTasks(@Param("groupId") Long groupId, @Param("days") Integer days);

    // ==================== 统计查询 ====================

    /**
     * 统计任务数量
     *
     * @param groupId 群组ID
     * @param status 状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 任务数量
     */
    Long countTasks(@Param("groupId") Long groupId,
                    @Param("status") String status,
                    @Param("startTime") LocalDateTime startTime,
                    @Param("endTime") LocalDateTime endTime);

    /**
     * 统计识别成功率
     *
     * @param groupId 群组ID
     * @param days 统计天数
     * @return 成功率统计
     */
    Map<String, Object> selectSuccessRateStats(@Param("groupId") Long groupId, @Param("days") Integer days);

    /**
     * 统计引擎性能
     *
     * @param groupId 群组ID
     * @param days 统计天数
     * @return 引擎性能统计
     */
    List<Map<String, Object>> selectEnginePerformanceStats(@Param("groupId") Long groupId, @Param("days") Integer days);

    /**
     * 统计处理时间分布
     *
     * @param groupId 群组ID
     * @param days 统计天数
     * @return 处理时间分布
     */
    List<Map<String, Object>> selectProcessingTimeDistribution(@Param("groupId") Long groupId, @Param("days") Integer days);

    /**
     * 统计置信度分布
     *
     * @param groupId 群组ID
     * @param days 统计天数
     * @return 置信度分布
     */
    List<Map<String, Object>> selectConfidenceDistribution(@Param("groupId") Long groupId, @Param("days") Integer days);

    /**
     * 统计商家识别排行
     *
     * @param groupId 群组ID
     * @param days 统计天数
     * @param limit 返回数量
     * @return 商家排行
     */
    List<Map<String, Object>> selectMerchantRecognitionRanking(@Param("groupId") Long groupId,
                                                               @Param("days") Integer days,
                                                               @Param("limit") Integer limit);

    /**
     * 统计分类识别情况
     *
     * @param groupId 群组ID
     * @param days 统计天数
     * @return 分类统计
     */
    List<Map<String, Object>> selectCategoryRecognitionStats(@Param("groupId") Long groupId, @Param("days") Integer days);

    /**
     * 统计识别趋势
     *
     * @param groupId 群组ID
     * @param days 统计天数
     * @return 趋势数据
     */
    List<Map<String, Object>> selectRecognitionTrend(@Param("groupId") Long groupId, @Param("days") Integer days);

    /**
     * 统计错误类型分布
     *
     * @param groupId 群组ID
     * @param days 统计天数
     * @return 错误类型分布
     */
    List<Map<String, Object>> selectErrorTypeDistribution(@Param("groupId") Long groupId, @Param("days") Integer days);

    /**
     * 统计每日识别量
     *
     * @param groupId 群组ID
     * @param days 统计天数
     * @return 每日识别量
     */
    List<Map<String, Object>> selectDailyRecognitionCount(@Param("groupId") Long groupId, @Param("days") Integer days);

    /**
     * 统计用户识别活跃度
     *
     * @param groupId 群组ID
     * @param days 统计天数
     * @param limit 返回数量
     * @return 用户活跃度
     */
    List<Map<String, Object>> selectUserRecognitionActivity(@Param("groupId") Long groupId,
                                                             @Param("days") Integer days,
                                                             @Param("limit") Integer limit);

    // ==================== 数据更新 ====================

    /**
     * 批量更新任务状态
     *
     * @param taskNumbers 任务编号列表
     * @param status 新状态
     * @param updatedBy 更新人
     * @return 更新数量
     */
    Integer batchUpdateStatus(@Param("taskNumbers") List<String> taskNumbers,
                              @Param("status") String status,
                              @Param("updatedBy") Long updatedBy);

    /**
     * 更新任务处理结果
     *
     * @param taskNumber 任务编号
     * @param status 状态
     * @param confidence 置信度
     * @param processingTime 处理时间
     * @param errorMessage 错误信息
     * @param errorCode 错误代码
     * @param updatedBy 更新人
     * @return 更新数量
     */
    Integer updateProcessingResult(@Param("taskNumber") String taskNumber,
                                   @Param("status") String status,
                                   @Param("confidence") Double confidence,
                                   @Param("processingTime") Long processingTime,
                                   @Param("errorMessage") String errorMessage,
                                   @Param("errorCode") String errorCode,
                                   @Param("updatedBy") Long updatedBy);

    /**
     * 更新任务识别结果
     *
     * @param taskNumber 任务编号
     * @param merchantName 商家名称
     * @param totalAmount 总金额
     * @param recognizedCategory 识别分类
     * @param recognizedTagsJson 识别标签JSON
     * @param updatedBy 更新人
     * @return 更新数量
     */
    Integer updateRecognitionResult(@Param("taskNumber") String taskNumber,
                                    @Param("merchantName") String merchantName,
                                    @Param("totalAmount") String totalAmount,
                                    @Param("recognizedCategory") String recognizedCategory,
                                    @Param("recognizedTagsJson") String recognizedTagsJson,
                                    @Param("updatedBy") Long updatedBy);

    /**
     * 更新账单创建状态
     *
     * @param taskNumber 任务编号
     * @param transactionCreated 是否创建账单
     * @param transactionId 账单ID
     * @param updatedBy 更新人
     * @return 更新数量
     */
    Integer updateTransactionStatus(@Param("taskNumber") String taskNumber,
                                    @Param("transactionCreated") Boolean transactionCreated,
                                    @Param("transactionId") Long transactionId,
                                    @Param("updatedBy") Long updatedBy);

    // ==================== 数据清理 ====================

    /**
     * 清理过期的处理中任务
     *
     * @param timeoutMinutes 超时分钟数
     * @param updatedBy 更新人
     * @return 清理数量
     */
    Integer cleanupTimeoutTasks(@Param("timeoutMinutes") Integer timeoutMinutes, @Param("updatedBy") Long updatedBy);

    /**
     * 清理历史数据
     *
     * @param days 保留天数
     * @return 清理数量
     */
    Integer cleanupHistoryData(@Param("days") Integer days);

    /**
     * 归档历史数据
     *
     * @param days 归档天数
     * @return 归档数量
     */
    Integer archiveHistoryData(@Param("days") Integer days);

    // ==================== 辅助查询 ====================

    /**
     * 检查任务编号是否存在
     *
     * @param taskNumber 任务编号
     * @return 是否存在
     */
    Boolean existsByTaskNumber(@Param("taskNumber") String taskNumber);

    /**
     * 获取下一个任务编号序号
     *
     * @param prefix 前缀
     * @return 序号
     */
    Integer getNextTaskSequence(@Param("prefix") String prefix);

    /**
     * 查询重复的任务
     *
     * @param groupId 群组ID
     * @param fileHash 文件哈希
     * @param days 查询天数
     * @return 重复任务列表
     */
    List<OcrTask> selectDuplicateTasks(@Param("groupId") Long groupId,
                                       @Param("fileHash") String fileHash,
                                       @Param("days") Integer days);

    /**
     * 查询可重试的失败任务
     *
     * @param groupId 群组ID
     * @param maxRetryCount 最大重试次数
     * @return 可重试任务列表
     */
    List<OcrTask> selectRetryableTasks(@Param("groupId") Long groupId, @Param("maxRetryCount") Integer maxRetryCount);
}
