package top.continew.admin.accounting.model.resp;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 数据同步配置列表响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "数据同步配置列表响应")
public class DataSyncConfigListResp {

    /**
     * ID
     */
    @Schema(description = "ID", example = "1")
    private Long id;

    /**
     * 配置名称
     */
    @Schema(description = "配置名称", example = "交易数据同步到Google Sheets")
    @ExcelProperty(value = "配置名称")
    private String configName;

    /**
     * 配置描述
     */
    @Schema(description = "配置描述", example = "将交易数据实时同步到Google Sheets进行分析")
    @ExcelProperty(value = "配置描述")
    private String configDescription;

    /**
     * 数据源类型
     */
    @Schema(description = "数据源类型", example = "DATABASE")
    @ExcelProperty(value = "数据源类型")
    private String sourceType;

    /**
     * 目标类型
     */
    @Schema(description = "目标类型", example = "GOOGLE_SHEETS")
    @ExcelProperty(value = "目标类型")
    private String targetType;

    /**
     * 同步方向
     */
    @Schema(description = "同步方向", example = "TO_TARGET")
    @ExcelProperty(value = "同步方向")
    private String syncDirection;

    /**
     * 同步模式
     */
    @Schema(description = "同步模式", example = "INCREMENTAL")
    @ExcelProperty(value = "同步模式")
    private String syncMode;

    /**
     * 数据类型
     */
    @Schema(description = "数据类型", example = "TRANSACTION")
    @ExcelProperty(value = "数据类型")
    private String dataType;

    /**
     * 同步频率
     */
    @Schema(description = "同步频率", example = "HOURLY")
    @ExcelProperty(value = "同步频率")
    private String syncFrequency;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    @ExcelProperty(value = "是否启用")
    private Boolean enabled;

    /**
     * 最后同步时间
     */
    @Schema(description = "最后同步时间", example = "2025-01-01 12:00:00")
    @ExcelProperty(value = "最后同步时间")
    private LocalDateTime lastSyncTime;

    /**
     * 下次同步时间
     */
    @Schema(description = "下次同步时间", example = "2025-01-01 13:00:00")
    @ExcelProperty(value = "下次同步时间")
    private LocalDateTime nextSyncTime;

    /**
     * 同步状态
     */
    @Schema(description = "同步状态", example = "SUCCESS")
    @ExcelProperty(value = "同步状态")
    private String syncStatus;

    /**
     * 最后同步结果
     */
    @Schema(description = "最后同步结果", example = "SUCCESS")
    @ExcelProperty(value = "最后同步结果")
    private String lastSyncResult;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 重试次数
     */
    @Schema(description = "重试次数", example = "0")
    private Integer retryCount;

    /**
     * 排序
     */
    @Schema(description = "排序", example = "1")
    private Integer sortOrder;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-01-01 10:00:00")
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2025-01-01 11:00:00")
    @ExcelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}
