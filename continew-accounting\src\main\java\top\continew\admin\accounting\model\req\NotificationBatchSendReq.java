package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 批量发送通知请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "批量发送通知请求")
public class NotificationBatchSendReq {

    @Schema(description = "通知列表")
    @NotEmpty(message = "通知列表不能为空")
    @Size(max = 100, message = "单次批量发送不能超过100条")
    @Valid
    private List<NotificationSendReq> notifications;

    @Schema(description = "是否异步发送", example = "true")
    private Boolean async = true;

    @Schema(description = "批次名称", example = "月度报表通知")
    private String batchName;

    @Schema(description = "批次描述")
    private String batchDescription;

}
