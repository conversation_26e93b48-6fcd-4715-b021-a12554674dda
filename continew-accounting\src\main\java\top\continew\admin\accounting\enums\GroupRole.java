package top.continew.admin.accounting.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.base.IBaseEnum;

/**
 * 群组角色枚举
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Getter
@RequiredArgsConstructor
public enum GroupRole implements IBaseEnum<String> {

    /**
     * 群主
     */
    OWNER("OWNER", "群主"),

    /**
     * 管理员
     */
    ADMIN("ADMIN", "管理员"),

    /**
     * 会计
     */
    ACCOUNTANT("ACCOUNTANT", "会计"),

    /**
     * 成员
     */
    MEMBER("MEMBER", "成员"),

    /**
     * 审计员
     */
    AUDITOR("AUDITOR", "审计员");

    private final String value;
    private final String description;

    /**
     * 检查是否有管理权限
     */
    public boolean hasAdminPermission() {
        return this == OWNER || this == ADMIN;
    }

    /**
     * 检查是否有记账权限
     */
    public boolean hasAccountingPermission() {
        return this == OWNER || this == ADMIN || this == ACCOUNTANT || this == MEMBER;
    }

    /**
     * 检查是否有查看权限
     */
    public boolean hasViewPermission() {
        return true; // 所有角色都有查看权限
    }

    /**
     * 检查是否有编辑权限
     */
    public boolean hasEditPermission() {
        return this == OWNER || this == ADMIN || this == ACCOUNTANT;
    }

    /**
     * 检查是否有删除权限
     */
    public boolean hasDeletePermission() {
        return this == OWNER || this == ADMIN;
    }

    /**
     * 检查是否有审计权限
     */
    public boolean hasAuditPermission() {
        return this == OWNER || this == ADMIN || this == AUDITOR;
    }
}
