package top.continew.admin.accounting.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.common.model.query.PageQuery;

import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.math.BigDecimal;
import java.util.List;

/**
 * 成本分析查询条件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "成本分析查询条件")
public class CostAnalysisQuery extends PageQuery {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    @NotNull(message = "群组ID不能为空")
    private Long groupId;

    /**
     * 分析类型
     */
    @Schema(description = "分析类型", example = "TREND_ANALYSIS", allowableValues = {"TREND_ANALYSIS", "COMPARISON_ANALYSIS", "BREAKDOWN_ANALYSIS", "BENEFIT_ANALYSIS", "VARIANCE_ANALYSIS"})
    private String analysisType;

    /**
     * 分析维度
     */
    @Schema(description = "分析维度", example = "[\"CATEGORY\", \"TIME\", \"MEMBER\"]")
    private List<String> analysisDimensions;

    /**
     * 时间范围
     */
    @Schema(description = "时间范围")
    private TimeRange timeRange;

    /**
     * 分类ID列表
     */
    @Schema(description = "分类ID列表", example = "[1, 2, 3]")
    private List<Long> categoryIds;

    /**
     * 成员ID列表
     */
    @Schema(description = "成员ID列表", example = "[1, 2, 3]")
    private List<Long> memberIds;

    /**
     * 钱包ID列表
     */
    @Schema(description = "钱包ID列表", example = "[1, 2, 3]")
    private List<Long> walletIds;

    /**
     * 标签列表
     */
    @Schema(description = "标签列表", example = "[\"工作\", \"生活\"]")
    private List<String> tags;

    /**
     * 金额范围
     */
    @Schema(description = "金额范围")
    private AmountRange amountRange;

    /**
     * 币种
     */
    @Schema(description = "币种", example = "CNY")
    private String currency;

    /**
     * 分组方式
     */
    @Schema(description = "分组方式", example = "MONTHLY", allowableValues = {"DAILY", "WEEKLY", "MONTHLY", "QUARTERLY", "YEARLY"})
    private String groupBy;

    /**
     * 聚合函数
     */
    @Schema(description = "聚合函数", example = "[\"SUM\", \"AVG\", \"COUNT\"]")
    private List<String> aggregations;

    /**
     * 对比基准
     */
    @Schema(description = "对比基准")
    private ComparisonBaseline comparisonBaseline;

    /**
     * 是否包含子分类
     */
    @Schema(description = "是否包含子分类", example = "true")
    private Boolean includeSubCategories = true;

    /**
     * 是否包含转账
     */
    @Schema(description = "是否包含转账", example = "false")
    private Boolean includeTransfers = false;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段", example = "amount")
    private String sortField;

    /**
     * 排序方向
     */
    @Schema(description = "排序方向", example = "DESC", allowableValues = {"ASC", "DESC"})
    private String sortDirection = "DESC";

    /**
     * 数据格式
     */
    @Schema(description = "数据格式", example = "DETAILED", allowableValues = {"SUMMARY", "DETAILED", "CHART_DATA"})
    private String dataFormat = "DETAILED";

    /**
     * 时间范围
     */
    @Data
    @Schema(description = "时间范围")
    public static class TimeRange {

        /**
         * 开始日期
         */
        @Schema(description = "开始日期", example = "2025-01-01")
        private String startDate;

        /**
         * 结束日期
         */
        @Schema(description = "结束日期", example = "2025-01-31")
        private String endDate;

        /**
         * 时间类型
         */
        @Schema(description = "时间类型", example = "CUSTOM", allowableValues = {"TODAY", "YESTERDAY", "THIS_WEEK", "LAST_WEEK", "THIS_MONTH", "LAST_MONTH", "THIS_QUARTER", "LAST_QUARTER", "THIS_YEAR", "LAST_YEAR", "CUSTOM"})
        private String timeType;

        /**
         * 相对时间值
         */
        @Schema(description = "相对时间值", example = "30")
        private Integer relativeValue;

        /**
         * 相对时间单位
         */
        @Schema(description = "相对时间单位", example = "DAYS", allowableValues = {"DAYS", "WEEKS", "MONTHS", "QUARTERS", "YEARS"})
        private String relativeUnit;
    }

    /**
     * 金额范围
     */
    @Data
    @Schema(description = "金额范围")
    public static class AmountRange {

        /**
         * 最小金额
         */
        @Schema(description = "最小金额", example = "0.00")
        private BigDecimal minAmount;

        /**
         * 最大金额
         */
        @Schema(description = "最大金额", example = "10000.00")
        private BigDecimal maxAmount;

        /**
         * 金额类型
         */
        @Schema(description = "金额类型", example = "ABSOLUTE", allowableValues = {"ABSOLUTE", "PERCENTAGE"})
        private String amountType = "ABSOLUTE";
    }

    /**
     * 对比基准
     */
    @Data
    @Schema(description = "对比基准")
    public static class ComparisonBaseline {

        /**
         * 基准类型
         */
        @Schema(description = "基准类型", example = "LAST_PERIOD", allowableValues = {"LAST_PERIOD", "SAME_PERIOD_LAST_YEAR", "BUDGET", "AVERAGE", "CUSTOM"})
        private String baselineType;

        /**
         * 基准时间范围
         */
        @Schema(description = "基准时间范围")
        private TimeRange baselineTimeRange;

        /**
         * 基准值
         */
        @Schema(description = "基准值", example = "5000.00")
        private BigDecimal baselineValue;

        /**
         * 基准描述
         */
        @Schema(description = "基准描述", example = "上月同期")
        private String baselineDescription;
    }
}
