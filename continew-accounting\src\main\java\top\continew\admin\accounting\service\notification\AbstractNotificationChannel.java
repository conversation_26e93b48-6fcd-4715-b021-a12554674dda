package top.continew.admin.accounting.service.notification;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import top.continew.admin.accounting.enums.NotificationChannelEnum;
import top.continew.admin.accounting.model.entity.NotificationDO;
import top.continew.admin.accounting.service.NotificationLogService;
import top.continew.admin.system.service.UserService;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 通知渠道抽象基类
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
public abstract class AbstractNotificationChannel implements NotificationChannel {

    @Autowired
    protected UserService userService;

    // @Autowired
    // protected NotificationLogService notificationLogService;

    /**
     * 发送限制缓存
     */
    private final Map<String, Map<String, Object>> sendLimitCache = new ConcurrentHashMap<>();

    @Override
    public Map<String, Object> sendNotification(NotificationDO notification, List<Long> targetUsers) {
        Map<String, Object> result = new HashMap<>();
        long startTime = System.currentTimeMillis();
        
        try {
            // 检查渠道是否启用
            if (!isEnabled()) {
                result.put("success", false);
                result.put("errorMessage", "渠道未启用");
                return result;
            }

            // 检查发送限制
            if (!checkSendLimits(targetUsers)) {
                result.put("success", false);
                result.put("errorMessage", "超出发送限制");
                return result;
            }

            // 验证目标用户
            if (CollUtil.isEmpty(targetUsers)) {
                result.put("success", false);
                result.put("errorMessage", "目标用户列表为空");
                return result;
            }

            // 执行具体发送逻辑
            Map<String, Object> sendResult = doSendNotification(notification, targetUsers);
            
            // 记录发送日志
            recordSendLog(notification, targetUsers, sendResult, System.currentTimeMillis() - startTime);
            
            return sendResult;
            
        } catch (Exception e) {
            log.error("发送通知失败: channel={}, notificationId={}", getChannelType().getCode(), notification.getId(), e);
            
            result.put("success", false);
            result.put("errorMessage", e.getMessage());
            result.put("exception", e.getClass().getSimpleName());
            
            // 记录错误日志
            recordErrorLog(notification, targetUsers, e, System.currentTimeMillis() - startTime);
            
            return result;
        }
    }

    @Override
    public Map<String, Object> batchSendNotification(NotificationDO notification, List<Long> targetUsers) {
        // 默认实现：分批发送
        Map<String, Object> result = new HashMap<>();
        int batchSize = getBatchSize();
        int totalUsers = targetUsers.size();
        int successCount = 0;
        int failedCount = 0;
        
        for (int i = 0; i < totalUsers; i += batchSize) {
            int endIndex = Math.min(i + batchSize, totalUsers);
            List<Long> batchUsers = targetUsers.subList(i, endIndex);
            
            Map<String, Object> batchResult = sendNotification(notification, batchUsers);
            Boolean success = (Boolean) batchResult.get("success");
            
            if (Boolean.TRUE.equals(success)) {
                successCount += batchUsers.size();
            } else {
                failedCount += batchUsers.size();
            }
            
            // 批次间延迟
            if (i + batchSize < totalUsers) {
                try {
                    Thread.sleep(getBatchDelay());
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        result.put("success", failedCount == 0);
        result.put("totalCount", totalUsers);
        result.put("successCount", successCount);
        result.put("failedCount", failedCount);
        result.put("successRate", totalUsers > 0 ? (double) successCount / totalUsers * 100 : 0);
        
        return result;
    }

    @Override
    public boolean checkSendLimits(List<Long> targetUsers) {
        Map<String, Object> limits = getSendLimits();
        if (limits == null || limits.isEmpty()) {
            return true;
        }
        
        // 检查单次发送数量限制
        Integer maxBatchSize = (Integer) limits.get("maxBatchSize");
        if (maxBatchSize != null && targetUsers.size() > maxBatchSize) {
            return false;
        }
        
        // 检查频率限制
        return checkRateLimit(targetUsers.size());
    }

    @Override
    public Map<String, String> formatMessage(NotificationDO notification, Long targetUser) {
        Map<String, String> result = new HashMap<>();
        
        // 基础格式化
        String title = formatTitle(notification, targetUser);
        String content = formatContent(notification, targetUser);
        
        result.put("title", title);
        result.put("content", content);
        
        // 渠道特定格式化
        Map<String, String> channelFormatted = doFormatMessage(notification, targetUser);
        if (channelFormatted != null) {
            result.putAll(channelFormatted);
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getRetryStrategy() {
        Map<String, Object> strategy = new HashMap<>();
        strategy.put("maxRetries", 3);
        strategy.put("retryInterval", 300); // 5分钟
        strategy.put("backoffMultiplier", 2.0);
        strategy.put("maxRetryInterval", 3600); // 1小时
        return strategy;
    }

    @Override
    public Map<String, Object> handleCallback(Map<String, Object> callbackData) {
        // 默认实现：记录回调日志
        log.info("收到渠道回调: channel={}, data={}", getChannelType().getCode(), callbackData);
        
        Map<String, Object> result = new HashMap<>();
        result.put("processed", true);
        result.put("timestamp", LocalDateTime.now());
        
        return result;
    }

    // ==================== 抽象方法 ====================

    /**
     * 执行具体的发送逻辑
     *
     * @param notification 通知信息
     * @param targetUsers 目标用户列表
     * @return 发送结果
     */
    protected abstract Map<String, Object> doSendNotification(NotificationDO notification, List<Long> targetUsers);

    /**
     * 渠道特定的消息格式化
     *
     * @param notification 通知信息
     * @param targetUser 目标用户
     * @return 格式化结果
     */
    protected abstract Map<String, String> doFormatMessage(NotificationDO notification, Long targetUser);

    // ==================== 辅助方法 ====================

    /**
     * 格式化标题
     */
    protected String formatTitle(NotificationDO notification, Long targetUser) {
        String title = notification.getTitle();
        if (StrUtil.isBlank(title)) {
            return "";
        }
        
        // 替换用户相关变量
        // TODO: 实现变量替换逻辑
        
        return title;
    }

    /**
     * 格式化内容
     */
    protected String formatContent(NotificationDO notification, Long targetUser) {
        String content = notification.getContent();
        if (StrUtil.isBlank(content)) {
            return "";
        }
        
        // 替换用户相关变量
        // TODO: 实现变量替换逻辑
        
        return content;
    }

    /**
     * 获取批次大小
     */
    protected int getBatchSize() {
        return 50;
    }

    /**
     * 获取批次延迟（毫秒）
     */
    protected long getBatchDelay() {
        return 1000;
    }

    /**
     * 检查频率限制
     */
    protected boolean checkRateLimit(int userCount) {
        // TODO: 实现频率限制检查
        return true;
    }

    /**
     * 记录发送日志
     */
    protected void recordSendLog(NotificationDO notification, List<Long> targetUsers, 
                               Map<String, Object> result, long duration) {
        // TODO: 实现发送日志记录
        log.debug("记录发送日志: channel={}, notificationId={}, userCount={}, duration={}ms", 
                getChannelType().getCode(), notification.getId(), targetUsers.size(), duration);
    }

    /**
     * 记录错误日志
     */
    protected void recordErrorLog(NotificationDO notification, List<Long> targetUsers, 
                                Exception error, long duration) {
        // TODO: 实现错误日志记录
        log.error("记录错误日志: channel={}, notificationId={}, userCount={}, duration={}ms", 
                getChannelType().getCode(), notification.getId(), targetUsers.size(), duration);
    }

}
