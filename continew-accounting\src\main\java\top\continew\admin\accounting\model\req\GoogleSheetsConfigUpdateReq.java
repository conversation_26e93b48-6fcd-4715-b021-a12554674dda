package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * Google Sheets配置更新请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "Google Sheets配置更新请求")
public class GoogleSheetsConfigUpdateReq extends GoogleSheetsConfigReq {

    /**
     * 配置ID
     */
    @Schema(description = "配置ID", example = "1")
    @NotNull(message = "配置ID不能为空")
    private Long configId;

    /**
     * 更新原因
     */
    @Schema(description = "更新原因", example = "调整字段映射配置")
    private String updateReason;

    /**
     * 是否保留历史版本
     */
    @Schema(description = "是否保留历史版本", example = "true")
    private Boolean keepHistory = true;

    /**
     * 版本说明
     */
    @Schema(description = "版本说明", example = "v1.1 - 新增自定义列支持")
    private String versionNotes;
}
