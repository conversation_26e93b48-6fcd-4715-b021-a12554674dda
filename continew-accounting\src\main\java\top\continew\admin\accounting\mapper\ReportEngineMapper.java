package top.continew.admin.accounting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.accounting.model.entity.ReportTemplate;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 报表引擎 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface ReportEngineMapper extends BaseMapper<ReportTemplate> {

    // ==================== 报表模板基础操作 ====================

    /**
     * 检查模板名称是否存在
     *
     * @param templateName 模板名称
     * @param groupId      群组ID
     * @return 是否存在
     */
    Boolean existsByNameAndGroup(@Param("templateName") String templateName, @Param("groupId") Long groupId);

    /**
     * 检查模板名称是否存在（排除指定ID）
     *
     * @param templateName 模板名称
     * @param groupId      群组ID
     * @param excludeId    排除的模板ID
     * @return 是否存在
     */
    Boolean existsByNameAndGroupExcludeId(@Param("templateName") String templateName, 
                                          @Param("groupId") Long groupId, 
                                          @Param("excludeId") Long excludeId);

    /**
     * 更新模板使用统计
     *
     * @param templateId    模板ID
     * @param executionTime 执行时间
     * @param success       是否成功
     * @param dataVolume    数据量
     * @return 影响行数
     */
    int updateTemplateUsageStats(@Param("templateId") Long templateId,
                                 @Param("executionTime") Long executionTime,
                                 @Param("success") Boolean success,
                                 @Param("dataVolume") Integer dataVolume);

    /**
     * 更新模板评分
     *
     * @param templateId 模板ID
     * @param rating     新评分
     * @param increment  评分人数增量
     * @return 影响行数
     */
    int updateTemplateRating(@Param("templateId") Long templateId,
                            @Param("rating") Double rating,
                            @Param("increment") Integer increment);

    /**
     * 更新模板收藏数
     *
     * @param templateId 模板ID
     * @param increment  收藏数增量（可为负数）
     * @return 影响行数
     */
    int updateTemplateFavoriteCount(@Param("templateId") Long templateId, @Param("increment") Integer increment);

    // ==================== 模板版本管理 ====================

    /**
     * 插入模板版本记录
     *
     * @param templateId    模板ID
     * @param version       版本号
     * @param versionNote   版本说明
     * @param updateContent 更新内容
     * @param changeType    变更类型
     * @param updatedBy     更新人ID
     * @return 影响行数
     */
    int insertTemplateVersion(@Param("templateId") Long templateId,
                             @Param("version") String version,
                             @Param("versionNote") String versionNote,
                             @Param("updateContent") String updateContent,
                             @Param("changeType") String changeType,
                             @Param("updatedBy") Long updatedBy);

    /**
     * 获取模板版本历史
     *
     * @param templateId 模板ID
     * @return 版本历史列表
     */
    List<Map<String, Object>> selectTemplateVersionHistory(@Param("templateId") Long templateId);

    /**
     * 获取指定版本的模板配置
     *
     * @param templateId 模板ID
     * @param version    版本号
     * @return 模板配置
     */
    Map<String, Object> selectTemplateVersionConfig(@Param("templateId") Long templateId, 
                                                    @Param("version") String version);

    /**
     * 删除模板版本记录
     *
     * @param templateId 模板ID
     * @return 影响行数
     */
    int deleteTemplateVersions(@Param("templateId") Long templateId);

    // ==================== 报表生成记录 ====================

    /**
     * 插入报表生成记录
     *
     * @param reportRecord 报表记录
     * @return 影响行数
     */
    int insertReportRecord(@Param("record") Map<String, Object> reportRecord);

    /**
     * 更新报表生成状态
     *
     * @param reportId 报表ID
     * @param status   状态
     * @param progress 进度
     * @param step     当前步骤
     * @return 影响行数
     */
    int updateReportStatus(@Param("reportId") Long reportId,
                          @Param("status") String status,
                          @Param("progress") Integer progress,
                          @Param("step") String step);

    /**
     * 完成报表生成
     *
     * @param reportId      报表ID
     * @param status        最终状态
     * @param filePath      文件路径
     * @param fileSize      文件大小
     * @param executionTime 执行时间
     * @param dataVolume    数据量
     * @param errorMessage  错误信息
     * @return 影响行数
     */
    int completeReportGeneration(@Param("reportId") Long reportId,
                                @Param("status") String status,
                                @Param("filePath") String filePath,
                                @Param("fileSize") Long fileSize,
                                @Param("executionTime") Long executionTime,
                                @Param("dataVolume") Integer dataVolume,
                                @Param("errorMessage") String errorMessage);

    /**
     * 获取模板执行历史
     *
     * @param templateId 模板ID
     * @param limit      限制数量
     * @return 执行历史列表
     */
    List<Map<String, Object>> selectTemplateExecutionHistory(@Param("templateId") Long templateId, 
                                                             @Param("limit") Integer limit);

    /**
     * 获取报表详情
     *
     * @param reportId 报表ID
     * @return 报表详情
     */
    Map<String, Object> selectReportDetail(@Param("reportId") Long reportId);

    /**
     * 更新报表访问统计
     *
     * @param reportId 报表ID
     * @return 影响行数
     */
    int updateReportAccessStats(@Param("reportId") Long reportId);

    /**
     * 删除过期报表
     *
     * @param expireTime 过期时间
     * @return 删除数量
     */
    int deleteExpiredReports(@Param("expireTime") LocalDateTime expireTime);

    // ==================== 调度任务管理 ====================

    /**
     * 插入调度任务
     *
     * @param scheduleTask 调度任务
     * @return 影响行数
     */
    int insertScheduleTask(@Param("task") Map<String, Object> scheduleTask);

    /**
     * 更新调度任务
     *
     * @param scheduleId   调度ID
     * @param scheduleTask 调度任务
     * @return 影响行数
     */
    int updateScheduleTask(@Param("scheduleId") String scheduleId, 
                          @Param("task") Map<String, Object> scheduleTask);

    /**
     * 删除调度任务
     *
     * @param scheduleId 调度ID
     * @return 影响行数
     */
    int deleteScheduleTask(@Param("scheduleId") String scheduleId);

    /**
     * 获取模板的调度任务列表
     *
     * @param templateId 模板ID
     * @return 调度任务列表
     */
    List<Map<String, Object>> selectTemplateScheduleTasks(@Param("templateId") Long templateId);

    /**
     * 获取活跃的调度任务
     *
     * @return 活跃调度任务列表
     */
    List<Map<String, Object>> selectActiveScheduleTasks();

    /**
     * 更新调度任务状态
     *
     * @param scheduleId 调度ID
     * @param status     状态
     * @return 影响行数
     */
    int updateScheduleTaskStatus(@Param("scheduleId") String scheduleId, @Param("status") String status);

    /**
     * 更新调度任务执行时间
     *
     * @param scheduleId         调度ID
     * @param lastExecutionTime  最后执行时间
     * @param nextExecutionTime  下次执行时间
     * @return 影响行数
     */
    int updateScheduleTaskExecutionTime(@Param("scheduleId") String scheduleId,
                                       @Param("lastExecutionTime") LocalDateTime lastExecutionTime,
                                       @Param("nextExecutionTime") LocalDateTime nextExecutionTime);

    // ==================== 权限管理 ====================

    /**
     * 插入模板权限
     *
     * @param templateId 模板ID
     * @param userId     用户ID
     * @param permission 权限
     * @return 影响行数
     */
    int insertTemplatePermission(@Param("templateId") Long templateId,
                                @Param("userId") Long userId,
                                @Param("permission") String permission);

    /**
     * 删除模板权限
     *
     * @param templateId 模板ID
     * @param userId     用户ID
     * @param permission 权限
     * @return 影响行数
     */
    int deleteTemplatePermission(@Param("templateId") Long templateId,
                                @Param("userId") Long userId,
                                @Param("permission") String permission);

    /**
     * 检查模板权限
     *
     * @param templateId 模板ID
     * @param userId     用户ID
     * @param permission 权限
     * @return 是否有权限
     */
    Boolean checkTemplatePermission(@Param("templateId") Long templateId,
                                   @Param("userId") Long userId,
                                   @Param("permission") String permission);

    /**
     * 获取模板权限列表
     *
     * @param templateId 模板ID
     * @return 权限列表
     */
    List<Map<String, Object>> selectTemplatePermissions(@Param("templateId") Long templateId);

    /**
     * 删除模板所有权限
     *
     * @param templateId 模板ID
     * @return 影响行数
     */
    int deleteTemplatePermissions(@Param("templateId") Long templateId);

    // ==================== 收藏管理 ====================

    /**
     * 插入模板收藏
     *
     * @param templateId 模板ID
     * @param userId     用户ID
     * @return 影响行数
     */
    int insertTemplateFavorite(@Param("templateId") Long templateId, @Param("userId") Long userId);

    /**
     * 删除模板收藏
     *
     * @param templateId 模板ID
     * @param userId     用户ID
     * @return 影响行数
     */
    int deleteTemplateFavorite(@Param("templateId") Long templateId, @Param("userId") Long userId);

    /**
     * 检查用户是否收藏模板
     *
     * @param templateId 模板ID
     * @param userId     用户ID
     * @return 是否收藏
     */
    Boolean checkUserFavoriteTemplate(@Param("templateId") Long templateId, @Param("userId") Long userId);

    /**
     * 获取用户收藏的模板
     *
     * @param userId 用户ID
     * @return 收藏模板列表
     */
    List<Map<String, Object>> selectUserFavoriteTemplates(@Param("userId") Long userId);

    /**
     * 删除模板所有收藏
     *
     * @param templateId 模板ID
     * @return 影响行数
     */
    int deleteTemplateFavorites(@Param("templateId") Long templateId);

    // ==================== 评价管理 ====================

    /**
     * 插入模板评价
     *
     * @param templateId 模板ID
     * @param userId     用户ID
     * @param rating     评分
     * @param comment    评价内容
     * @return 影响行数
     */
    int insertTemplateRating(@Param("templateId") Long templateId,
                            @Param("userId") Long userId,
                            @Param("rating") Integer rating,
                            @Param("comment") String comment);

    /**
     * 更新模板评价
     *
     * @param templateId 模板ID
     * @param userId     用户ID
     * @param rating     评分
     * @param comment    评价内容
     * @return 影响行数
     */
    int updateTemplateRating(@Param("templateId") Long templateId,
                            @Param("userId") Long userId,
                            @Param("rating") Integer rating,
                            @Param("comment") String comment);

    /**
     * 删除模板评价
     *
     * @param templateId 模板ID
     * @param userId     用户ID
     * @return 影响行数
     */
    int deleteTemplateRating(@Param("templateId") Long templateId, @Param("userId") Long userId);

    /**
     * 获取模板评价列表
     *
     * @param templateId 模板ID
     * @return 评价列表
     */
    List<Map<String, Object>> selectTemplateRatings(@Param("templateId") Long templateId);

    /**
     * 获取模板平均评分
     *
     * @param templateId 模板ID
     * @return 平均评分和评价数量
     */
    Map<String, Object> selectTemplateAverageRating(@Param("templateId") Long templateId);

    /**
     * 删除模板所有评价
     *
     * @param templateId 模板ID
     * @return 影响行数
     */
    int deleteTemplateRatings(@Param("templateId") Long templateId);

    // ==================== 统计分析 ====================

    /**
     * 获取模板使用统计
     *
     * @param templateId 模板ID
     * @param days       统计天数
     * @return 使用统计
     */
    Map<String, Object> selectTemplateUsageStats(@Param("templateId") Long templateId, @Param("days") Integer days);

    /**
     * 获取模板性能统计
     *
     * @param templateId 模板ID
     * @param days       统计天数
     * @return 性能统计
     */
    Map<String, Object> selectTemplatePerformanceStats(@Param("templateId") Long templateId, @Param("days") Integer days);

    /**
     * 获取报表生成趋势
     *
     * @param groupId 群组ID
     * @param days    统计天数
     * @return 生成趋势
     */
    List<Map<String, Object>> selectReportGenerationTrend(@Param("groupId") Long groupId, @Param("days") Integer days);

    /**
     * 获取热门模板排行
     *
     * @param groupId 群组ID
     * @param days    统计天数
     * @param limit   返回数量
     * @return 热门模板列表
     */
    List<Map<String, Object>> selectPopularTemplates(@Param("groupId") Long groupId, 
                                                     @Param("days") Integer days, 
                                                     @Param("limit") Integer limit);

    /**
     * 获取用户报表使用分析
     *
     * @param userId 用户ID
     * @param days   统计天数
     * @return 用户使用分析
     */
    Map<String, Object> selectUserReportUsageAnalysis(@Param("userId") Long userId, @Param("days") Integer days);

    /**
     * 获取群组报表统计
     *
     * @param groupId 群组ID
     * @return 群组统计
     */
    Map<String, Object> selectGroupReportStats(@Param("groupId") Long groupId);

    /**
     * 获取系统报表统计
     *
     * @return 系统统计
     */
    Map<String, Object> selectSystemReportStats();
}
