package top.continew.admin.api.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * API密钥创建请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "API密钥创建请求")
public class ApiKeyCreateReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 应用名称
     */
    @Schema(description = "应用名称", example = "我的记账应用")
    @NotBlank(message = "应用名称不能为空")
    @Size(max = 50, message = "应用名称长度不能超过 {max} 个字符")
    private String appName;

    /**
     * 描述
     */
    @Schema(description = "API密钥描述", example = "用于移动端应用的API访问")
    @Size(max = 200, message = "描述长度不能超过 {max} 个字符")
    private String description;

    /**
     * 权限范围
     */
    @Schema(description = "权限范围列表", example = "[\"groups:read\", \"transactions:write\"]")
    private List<String> scopes;

    /**
     * IP白名单
     */
    @Schema(description = "IP白名单", example = "[\"*************\", \"10.0.0.0/8\"]")
    private List<String> ipWhitelist;

    /**
     * 速率限制（每分钟请求数）
     */
    @Schema(description = "速率限制（每分钟请求数）", example = "100")
    @Min(value = 1, message = "速率限制不能小于1")
    @Max(value = 10000, message = "速率限制不能超过10000")
    private Integer rateLimit;

    /**
     * 过期时间
     */
    @Schema(description = "过期时间（为空表示永不过期）")
    private LocalDateTime expiresAt;

    /**
     * 是否激活
     */
    @Schema(description = "是否激活", example = "true")
    private Boolean isActive;
}
