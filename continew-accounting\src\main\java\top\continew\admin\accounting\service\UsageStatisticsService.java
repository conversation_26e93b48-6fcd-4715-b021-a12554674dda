package top.continew.admin.accounting.service;

import top.continew.admin.accounting.model.entity.UsageStatisticsDO;
import top.continew.admin.accounting.model.resp.UsageStatisticsResp;

import java.time.LocalDate;
import java.util.List;

/**
 * 使用量统计服务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface UsageStatisticsService {

    /**
     * 记录交易使用量
     */
    void recordTransactionUsage(Long groupId);

    /**
     * 记录OCR使用量
     */
    void recordOcrUsage(Long groupId);

    /**
     * 记录API调用使用量
     */
    void recordApiUsage(Long groupId);

    /**
     * 记录存储使用量
     */
    void recordStorageUsage(Long groupId, Long storageBytes);

    /**
     * 记录导出使用量
     */
    void recordExportUsage(Long groupId);

    /**
     * 记录Webhook使用量
     */
    void recordWebhookUsage(Long groupId);

    /**
     * 更新活跃用户数
     */
    void updateActiveUsers(Long groupId, Integer activeUsers);

    /**
     * 获取群组当月使用统计
     */
    UsageStatisticsResp getCurrentMonthUsage(Long groupId);

    /**
     * 获取群组使用趋势
     */
    List<UsageStatisticsResp> getUsageTrend(Long groupId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取群组总使用量
     */
    UsageStatisticsResp getTotalUsage(Long groupId, LocalDate startDate, LocalDate endDate);

    /**
     * 检查使用限制
     */
    boolean isUsageLimitExceeded(Long groupId, String limitType);

    /**
     * 获取剩余使用量
     */
    Integer getRemainingUsage(Long groupId, String limitType);

    /**
     * 获取使用率
     */
    Double getUsageRate(Long groupId, String limitType);

    /**
     * 查询超限群组
     */
    List<Long> getOverLimitGroups(String limitType);

    /**
     * 重置月度统计
     */
    void resetMonthlyStatistics();

    /**
     * 清理过期统计数据
     */
    void cleanupExpiredStatistics(Integer retentionDays);

    /**
     * 生成使用报告
     */
    UsageReportResp generateUsageReport(Long groupId, LocalDate startDate, LocalDate endDate);

    /**
     * 初始化使用量统计
     *
     * @param groupId 群组ID
     */
    void initializeUsageStatistics(Long groupId);

    /**
     * 使用报告响应
     */
    class UsageReportResp {
        private Long groupId;
        private String groupName;
        private LocalDate startDate;
        private LocalDate endDate;
        private Integer totalTransactions;
        private Integer totalOcrUsage;
        private Integer totalApiCalls;
        private Long totalStorageUsed;
        private Integer totalExports;
        private Integer totalWebhookCalls;
        private Integer avgActiveUsers;
        private List<UsageStatisticsResp> dailyStats;
        
        // getters and setters
        public Long getGroupId() { return groupId; }
        public void setGroupId(Long groupId) { this.groupId = groupId; }
        public String getGroupName() { return groupName; }
        public void setGroupName(String groupName) { this.groupName = groupName; }
        public LocalDate getStartDate() { return startDate; }
        public void setStartDate(LocalDate startDate) { this.startDate = startDate; }
        public LocalDate getEndDate() { return endDate; }
        public void setEndDate(LocalDate endDate) { this.endDate = endDate; }
        public Integer getTotalTransactions() { return totalTransactions; }
        public void setTotalTransactions(Integer totalTransactions) { this.totalTransactions = totalTransactions; }
        public Integer getTotalOcrUsage() { return totalOcrUsage; }
        public void setTotalOcrUsage(Integer totalOcrUsage) { this.totalOcrUsage = totalOcrUsage; }
        public Integer getTotalApiCalls() { return totalApiCalls; }
        public void setTotalApiCalls(Integer totalApiCalls) { this.totalApiCalls = totalApiCalls; }
        public Long getTotalStorageUsed() { return totalStorageUsed; }
        public void setTotalStorageUsed(Long totalStorageUsed) { this.totalStorageUsed = totalStorageUsed; }
        public Integer getTotalExports() { return totalExports; }
        public void setTotalExports(Integer totalExports) { this.totalExports = totalExports; }
        public Integer getTotalWebhookCalls() { return totalWebhookCalls; }
        public void setTotalWebhookCalls(Integer totalWebhookCalls) { this.totalWebhookCalls = totalWebhookCalls; }
        public Integer getAvgActiveUsers() { return avgActiveUsers; }
        public void setAvgActiveUsers(Integer avgActiveUsers) { this.avgActiveUsers = avgActiveUsers; }
        public List<UsageStatisticsResp> getDailyStats() { return dailyStats; }
        public void setDailyStats(List<UsageStatisticsResp> dailyStats) { this.dailyStats = dailyStats; }
    }
}
