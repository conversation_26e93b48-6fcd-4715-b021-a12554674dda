package top.continew.admin.accounting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.accounting.model.entity.ZapierConfigDO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Zapier配置 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface ZapierConfigMapper extends BaseMapper<ZapierConfigDO> {

    /**
     * 查询群组的Zapier配置列表
     *
     * @param groupId 群组ID
     * @return 配置列表
     */
    List<ZapierConfigDO> selectByGroupId(@Param("groupId") Long groupId);

    /**
     * 查询启用的Zapier配置
     *
     * @param groupId     群组ID
     * @param triggerType 触发器类型
     * @return 配置列表
     */
    List<ZapierConfigDO> selectEnabledConfigs(@Param("groupId") Long groupId, 
                                              @Param("triggerType") String triggerType);

    /**
     * 查询所有启用的配置
     *
     * @return 配置列表
     */
    List<ZapierConfigDO> selectAllEnabledConfigs();

    /**
     * 更新配置状态
     *
     * @param id     配置ID
     * @param status 状态
     * @return 更新行数
     */
    int updateStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 更新触发统计
     *
     * @param id            配置ID
     * @param triggerCount  触发次数
     * @param successCount  成功次数
     * @param failureCount  失败次数
     * @param lastTriggered 最后触发时间
     * @return 更新行数
     */
    int updateTriggerStats(@Param("id") Long id,
                          @Param("triggerCount") Long triggerCount,
                          @Param("successCount") Long successCount,
                          @Param("failureCount") Long failureCount,
                          @Param("lastTriggered") LocalDateTime lastTriggered);

    /**
     * 更新错误信息
     *
     * @param id        配置ID
     * @param error     错误信息
     * @param errorTime 错误时间
     * @return 更新行数
     */
    int updateError(@Param("id") Long id,
                   @Param("error") String error,
                   @Param("errorTime") LocalDateTime errorTime);

    /**
     * 查询配置统计信息
     *
     * @param groupId 群组ID
     * @return 统计信息
     */
    Map<String, Object> selectConfigStats(@Param("groupId") Long groupId);

    /**
     * 查询触发器类型统计
     *
     * @param groupId 群组ID
     * @return 触发器类型统计
     */
    List<Map<String, Object>> selectTriggerTypeStats(@Param("groupId") Long groupId);

    /**
     * 查询活跃配置排行
     *
     * @param groupId 群组ID
     * @param limit   限制数量
     * @return 活跃配置排行
     */
    List<Map<String, Object>> selectActiveConfigRanking(@Param("groupId") Long groupId, 
                                                        @Param("limit") Integer limit);

    /**
     * 查询配置性能统计
     *
     * @param groupId   群组ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 性能统计
     */
    List<Map<String, Object>> selectPerformanceStats(@Param("groupId") Long groupId,
                                                     @Param("startTime") LocalDateTime startTime,
                                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 查询错误配置
     *
     * @param groupId 群组ID
     * @param hours   小时数
     * @return 错误配置列表
     */
    List<ZapierConfigDO> selectErrorConfigs(@Param("groupId") Long groupId, 
                                           @Param("hours") Integer hours);

    /**
     * 查询长时间未触发的配置
     *
     * @param groupId 群组ID
     * @param days    天数
     * @return 配置列表
     */
    List<ZapierConfigDO> selectInactiveConfigs(@Param("groupId") Long groupId, 
                                              @Param("days") Integer days);

    /**
     * 批量更新配置状态
     *
     * @param ids    配置ID列表
     * @param status 状态
     * @return 更新行数
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") String status);

    /**
     * 批量启用/禁用配置
     *
     * @param ids     配置ID列表
     * @param enabled 是否启用
     * @return 更新行数
     */
    int batchUpdateEnabled(@Param("ids") List<Long> ids, @Param("enabled") Boolean enabled);

    /**
     * 删除群组的所有配置
     *
     * @param groupId 群组ID
     * @return 删除行数
     */
    int deleteByGroupId(@Param("groupId") Long groupId);

    /**
     * 查询配置详情（包含群组信息）
     *
     * @param id 配置ID
     * @return 配置详情
     */
    Map<String, Object> selectConfigDetail(@Param("id") Long id);

    /**
     * 查询配置列表（包含群组信息）
     *
     * @param groupId     群组ID
     * @param triggerType 触发器类型
     * @param enabled     是否启用
     * @param status      状态
     * @return 配置列表
     */
    List<Map<String, Object>> selectConfigList(@Param("groupId") Long groupId,
                                               @Param("triggerType") String triggerType,
                                               @Param("enabled") Boolean enabled,
                                               @Param("status") String status);

    /**
     * 查询用户创建的配置数量
     *
     * @param userId 用户ID
     * @return 配置数量
     */
    Long countByUserId(@Param("userId") Long userId);

    /**
     * 查询配置的触发历史趋势
     *
     * @param configId  配置ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 触发历史趋势
     */
    List<Map<String, Object>> selectTriggerTrend(@Param("configId") Long configId,
                                                 @Param("startTime") LocalDateTime startTime,
                                                 @Param("endTime") LocalDateTime endTime);

    /**
     * 重置配置统计
     *
     * @param id 配置ID
     * @return 更新行数
     */
    int resetStats(@Param("id") Long id);

    /**
     * 查询即将过期的配置（基于最后触发时间）
     *
     * @param days 天数阈值
     * @return 配置列表
     */
    List<ZapierConfigDO> selectExpiringConfigs(@Param("days") Integer days);

    /**
     * 查询高频触发配置
     *
     * @param groupId   群组ID
     * @param threshold 触发次数阈值
     * @param hours     时间范围（小时）
     * @return 配置列表
     */
    List<ZapierConfigDO> selectHighFrequencyConfigs(@Param("groupId") Long groupId,
                                                    @Param("threshold") Long threshold,
                                                    @Param("hours") Integer hours);

    /**
     * 查询配置健康状态
     *
     * @param id 配置ID
     * @return 健康状态信息
     */
    Map<String, Object> selectConfigHealth(@Param("id") Long id);
}
