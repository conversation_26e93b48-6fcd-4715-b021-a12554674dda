package top.continew.admin.accounting.model.resp;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 数据同步日志响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "数据同步日志响应")
public class DataSyncLogResp {

    /**
     * ID
     */
    @Schema(description = "ID", example = "1")
    private Long id;

    /**
     * 配置ID
     */
    @Schema(description = "配置ID", example = "1")
    private Long configId;

    /**
     * 配置名称
     */
    @Schema(description = "配置名称", example = "交易数据同步到Google Sheets")
    @ExcelProperty(value = "配置名称")
    private String configName;

    /**
     * 同步ID
     */
    @Schema(description = "同步ID", example = "sync_20250101_120000_001")
    @ExcelProperty(value = "同步ID")
    private String syncId;

    /**
     * 同步类型
     */
    @Schema(description = "同步类型", example = "INCREMENTAL")
    @ExcelProperty(value = "同步类型")
    private String syncType;

    /**
     * 同步方向
     */
    @Schema(description = "同步方向", example = "TO_TARGET")
    @ExcelProperty(value = "同步方向")
    private String syncDirection;

    /**
     * 操作类型
     */
    @Schema(description = "操作类型", example = "CREATE")
    @ExcelProperty(value = "操作类型")
    private String operationType;

    /**
     * 数据类型
     */
    @Schema(description = "数据类型", example = "TRANSACTION")
    @ExcelProperty(value = "数据类型")
    private String dataType;

    /**
     * 处理记录数
     */
    @Schema(description = "处理记录数", example = "1000")
    @ExcelProperty(value = "处理记录数")
    private Integer recordsProcessed;

    /**
     * 成功记录数
     */
    @Schema(description = "成功记录数", example = "980")
    @ExcelProperty(value = "成功记录数")
    private Integer recordsSuccess;

    /**
     * 失败记录数
     */
    @Schema(description = "失败记录数", example = "15")
    @ExcelProperty(value = "失败记录数")
    private Integer recordsFailed;

    /**
     * 跳过记录数
     */
    @Schema(description = "跳过记录数", example = "5")
    @ExcelProperty(value = "跳过记录数")
    private Integer recordsSkipped;

    /**
     * 冲突记录数
     */
    @Schema(description = "冲突记录数", example = "3")
    @ExcelProperty(value = "冲突记录数")
    private Integer recordsConflict;

    /**
     * 同步状态
     */
    @Schema(description = "同步状态", example = "SUCCESS")
    @ExcelProperty(value = "同步状态")
    private String status;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2025-01-01 12:00:00")
    @ExcelProperty(value = "开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", example = "2025-01-01 12:05:30")
    @ExcelProperty(value = "结束时间")
    private LocalDateTime endTime;

    /**
     * 执行时长（毫秒）
     */
    @Schema(description = "执行时长（毫秒）", example = "330000")
    @ExcelProperty(value = "执行时长（毫秒）")
    private Long durationMs;

    /**
     * 成功率
     */
    @Schema(description = "成功率", example = "98.0")
    @ExcelProperty(value = "成功率")
    private Double successRate;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 错误代码
     */
    @Schema(description = "错误代码")
    private String errorCode;

    /**
     * 错误详情
     */
    @Schema(description = "错误详情")
    private List<Map<String, Object>> errorDetails;

    /**
     * 同步详情
     */
    @Schema(description = "同步详情")
    private Map<String, Object> syncDetails;

    /**
     * 源数据摘要
     */
    @Schema(description = "源数据摘要")
    private String sourceDataSummary;

    /**
     * 目标数据摘要
     */
    @Schema(description = "目标数据摘要")
    private String targetDataSummary;

    /**
     * 冲突详情
     */
    @Schema(description = "冲突详情")
    private List<Map<String, Object>> conflictDetails;

    /**
     * 性能指标
     */
    @Schema(description = "性能指标")
    private Map<String, Object> performanceMetrics;

    /**
     * 触发方式
     */
    @Schema(description = "触发方式", example = "MANUAL")
    @ExcelProperty(value = "触发方式")
    private String triggerType;

    /**
     * 触发用户ID
     */
    @Schema(description = "触发用户ID", example = "1")
    private Long triggerUserId;

    /**
     * 触发用户名
     */
    @Schema(description = "触发用户名", example = "admin")
    @ExcelProperty(value = "触发用户")
    private String triggerUserName;

    /**
     * 是否重试
     */
    @Schema(description = "是否重试", example = "false")
    @ExcelProperty(value = "是否重试")
    private Boolean isRetry;

    /**
     * 重试次数
     */
    @Schema(description = "重试次数", example = "0")
    @ExcelProperty(value = "重试次数")
    private Integer retryCount;

    /**
     * 父日志ID
     */
    @Schema(description = "父日志ID", example = "1")
    private Long parentLogId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-01-01 12:00:00")
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createTime;
}
