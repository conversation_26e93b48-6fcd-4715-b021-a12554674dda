package top.continew.admin.accounting.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.accounting.service.sync.impl.WebhookAdapter;
import top.continew.admin.common.model.resp.R;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * Webhook接收控制器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "Webhook接收 API")
@RestController
@RequestMapping("/accounting/webhook")
@RequiredArgsConstructor
@Slf4j
public class WebhookController {

    private final WebhookAdapter webhookAdapter;

    @Operation(summary = "接收Webhook数据", description = "接收来自第三方系统的Webhook数据")
    @PostMapping("/receive/{configId}")
    public ResponseEntity<Map<String, Object>> receiveWebhook(
            @Parameter(description = "配置ID") @PathVariable String configId,
            @RequestBody Map<String, Object> payload,
            HttpServletRequest request) {
        
        try {
            log.info("接收Webhook数据: configId={}, payload={}", configId, payload);
            
            // 验证签名（如果配置了密钥）
            String signature = request.getHeader("X-Webhook-Signature");
            if (StrUtil.isNotBlank(signature)) {
                // TODO: 验证签名
                log.debug("Webhook签名验证: signature={}", signature);
            }
            
            // 添加请求元数据
            Map<String, Object> enrichedPayload = new HashMap<>(payload);
            enrichedPayload.put("sourceIp", getClientIpAddress(request));
            enrichedPayload.put("userAgent", request.getHeader("User-Agent"));
            enrichedPayload.put("contentType", request.getContentType());
            
            // 传递给适配器处理
            webhookAdapter.receiveWebhookData(configId, enrichedPayload);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Webhook数据接收成功");
            response.put("configId", configId);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("接收Webhook数据失败: configId={}, error={}", configId, e.getMessage(), e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Webhook数据接收失败: " + e.getMessage());
            response.put("configId", configId);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.status(500).body(response);
        }
    }

    @Operation(summary = "接收通用Webhook数据", description = "接收通用格式的Webhook数据")
    @PostMapping("/receive")
    public ResponseEntity<Map<String, Object>> receiveGenericWebhook(
            @RequestParam(required = false) String configId,
            @RequestBody Map<String, Object> payload,
            HttpServletRequest request) {
        
        try {
            // 如果没有指定configId，尝试从payload中获取
            if (StrUtil.isBlank(configId)) {
                configId = (String) payload.get("configId");
            }
            
            if (StrUtil.isBlank(configId)) {
                configId = "default";
            }
            
            log.info("接收通用Webhook数据: configId={}, payload={}", configId, payload);
            
            // 添加请求元数据
            Map<String, Object> enrichedPayload = new HashMap<>(payload);
            enrichedPayload.put("sourceIp", getClientIpAddress(request));
            enrichedPayload.put("userAgent", request.getHeader("User-Agent"));
            enrichedPayload.put("contentType", request.getContentType());
            
            // 传递给适配器处理
            webhookAdapter.receiveWebhookData(configId, enrichedPayload);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Webhook数据接收成功");
            response.put("configId", configId);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("接收通用Webhook数据失败: error={}", e.getMessage(), e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Webhook数据接收失败: " + e.getMessage());
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.status(500).body(response);
        }
    }

    @Operation(summary = "测试Webhook连接", description = "测试Webhook连接是否正常")
    @GetMapping("/test/{configId}")
    public R<Map<String, Object>> testWebhook(
            @Parameter(description = "配置ID") @PathVariable String configId) {
        
        try {
            log.info("测试Webhook连接: configId={}", configId);
            
            Map<String, Object> testData = new HashMap<>();
            testData.put("type", "test");
            testData.put("message", "Webhook连接测试");
            testData.put("timestamp", System.currentTimeMillis());
            
            webhookAdapter.receiveWebhookData(configId, testData);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "Webhook连接测试成功");
            result.put("configId", configId);
            
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("测试Webhook连接失败: configId={}, error={}", configId, e.getMessage(), e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Webhook连接测试失败: " + e.getMessage());
            result.put("configId", configId);
            
            return R.fail(result);
        }
    }

    @Operation(summary = "获取Webhook状态", description = "获取指定配置的Webhook接收状态")
    @GetMapping("/status/{configId}")
    public R<Map<String, Object>> getWebhookStatus(
            @Parameter(description = "配置ID") @PathVariable String configId) {
        
        try {
            log.debug("获取Webhook状态: configId={}", configId);
            
            // TODO: 从适配器获取状态信息
            Map<String, Object> status = new HashMap<>();
            status.put("configId", configId);
            status.put("active", true);
            status.put("lastReceived", System.currentTimeMillis());
            status.put("totalReceived", 0);
            status.put("errorCount", 0);
            
            return R.ok(status);
            
        } catch (Exception e) {
            log.error("获取Webhook状态失败: configId={}, error={}", configId, e.getMessage(), e);
            return R.fail("获取Webhook状态失败: " + e.getMessage());
        }
    }

    @Operation(summary = "清理Webhook数据", description = "清理指定配置的Webhook缓存数据")
    @DeleteMapping("/cleanup/{configId}")
    public R<String> cleanupWebhookData(
            @Parameter(description = "配置ID") @PathVariable String configId) {
        
        try {
            log.info("清理Webhook数据: configId={}", configId);
            
            Map<String, Object> config = new HashMap<>();
            config.put("configId", configId);
            
            webhookAdapter.cleanup(config, configId);
            
            return R.ok("Webhook数据清理成功");
            
        } catch (Exception e) {
            log.error("清理Webhook数据失败: configId={}, error={}", configId, e.getMessage(), e);
            return R.fail("清理Webhook数据失败: " + e.getMessage());
        }
    }

    @Operation(summary = "批量接收Webhook数据", description = "批量接收多条Webhook数据")
    @PostMapping("/batch/{configId}")
    public ResponseEntity<Map<String, Object>> receiveBatchWebhook(
            @Parameter(description = "配置ID") @PathVariable String configId,
            @RequestBody Map<String, Object> batchPayload,
            HttpServletRequest request) {
        
        try {
            log.info("批量接收Webhook数据: configId={}", configId);
            
            @SuppressWarnings("unchecked")
            java.util.List<Map<String, Object>> dataList = 
                (java.util.List<Map<String, Object>>) batchPayload.get("data");
            
            if (dataList == null || dataList.isEmpty()) {
                throw new IllegalArgumentException("批量数据不能为空");
            }
            
            int successCount = 0;
            int failedCount = 0;
            
            for (Map<String, Object> data : dataList) {
                try {
                    // 添加请求元数据
                    Map<String, Object> enrichedData = new HashMap<>(data);
                    enrichedData.put("sourceIp", getClientIpAddress(request));
                    enrichedData.put("userAgent", request.getHeader("User-Agent"));
                    enrichedData.put("batchIndex", successCount + failedCount);
                    
                    webhookAdapter.receiveWebhookData(configId, enrichedData);
                    successCount++;
                } catch (Exception e) {
                    failedCount++;
                    log.error("批量处理Webhook数据失败: index={}, error={}", 
                            successCount + failedCount - 1, e.getMessage());
                }
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "批量Webhook数据接收完成");
            response.put("configId", configId);
            response.put("totalCount", dataList.size());
            response.put("successCount", successCount);
            response.put("failedCount", failedCount);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("批量接收Webhook数据失败: configId={}, error={}", configId, e.getMessage(), e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "批量Webhook数据接收失败: " + e.getMessage());
            response.put("configId", configId);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StrUtil.isNotBlank(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (StrUtil.isNotBlank(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
