package top.continew.admin.accounting.service;

import java.util.Map;

/**
 * Zapier Webhook执行服务
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface ZapierWebhookService {

    /**
     * 触发事件
     *
     * @param groupId      群组ID
     * @param triggerType  触发器类型
     * @param eventType    事件类型
     * @param businessId   业务ID
     * @param businessType 业务类型
     * @param data         事件数据
     */
    void triggerEvent(Long groupId, String triggerType, String eventType, Long businessId, String businessType, Map<String, Object> data);

    /**
     * 异步触发事件
     *
     * @param groupId      群组ID
     * @param triggerType  触发器类型
     * @param eventType    事件类型
     * @param businessId   业务ID
     * @param businessType 业务类型
     * @param data         事件数据
     */
    void triggerEventAsync(Long groupId, String triggerType, String eventType, Long businessId, String businessType, Map<String, Object> data);

    /**
     * 重试失败的执行
     *
     * @param logId 日志ID
     */
    void retryExecution(Long logId);

    /**
     * 批量重试失败的执行
     *
     * @param configId 配置ID
     * @param hours    时间范围（小时）
     */
    void batchRetryFailures(Long configId, Integer hours);

    /**
     * 处理定时重试任务
     */
    void processScheduledRetries();

    /**
     * 验证Webhook URL的可达性
     *
     * @param webhookUrl Webhook URL
     * @return 验证结果
     */
    Map<String, Object> validateWebhookUrl(String webhookUrl);

    /**
     * 获取Webhook执行统计
     *
     * @param groupId 群组ID
     * @return 统计信息
     */
    Map<String, Object> getExecutionStatistics(Long groupId);

    /**
     * 清理过期的执行日志
     *
     * @param retentionDays 保留天数
     * @return 清理的记录数
     */
    Integer cleanupExpiredLogs(Integer retentionDays);

    /**
     * 监控Webhook健康状态
     *
     * @param groupId 群组ID
     * @return 健康状态报告
     */
    Map<String, Object> monitorWebhookHealth(Long groupId);

    /**
     * 暂停配置的执行
     *
     * @param configId 配置ID
     * @param reason   暂停原因
     */
    void pauseConfig(Long configId, String reason);

    /**
     * 恢复配置的执行
     *
     * @param configId 配置ID
     */
    void resumeConfig(Long configId);

    /**
     * 获取执行队列状态
     *
     * @return 队列状态信息
     */
    Map<String, Object> getExecutionQueueStatus();
}
