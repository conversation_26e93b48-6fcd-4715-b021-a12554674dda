package top.continew.admin.accounting.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;

/**
 * 订阅过期事件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Getter
public class SubscriptionExpiredEvent extends ApplicationEvent {

    /**
     * 订阅ID
     */
    private final Long subscriptionId;

    /**
     * 群组ID
     */
    private final Long groupId;

    /**
     * 套餐ID
     */
    private final Long planId;

    /**
     * 套餐名称
     */
    private final String planName;

    /**
     * 用户ID
     */
    private final Long userId;

    /**
     * 过期时间
     */
    private final LocalDateTime expiredAt;

    /**
     * 是否自动续费
     */
    private final Boolean autoRenew;

    /**
     * 事件时间
     */
    private final LocalDateTime eventTime;

    public SubscriptionExpiredEvent(Object source, Long subscriptionId, Long groupId, Long planId, String planName,
                                   Long userId, LocalDateTime expiredAt, Boolean autoRenew, LocalDateTime eventTime) {
        super(source);
        this.subscriptionId = subscriptionId;
        this.groupId = groupId;
        this.planId = planId;
        this.planName = planName;
        this.userId = userId;
        this.expiredAt = expiredAt;
        this.autoRenew = autoRenew;
        this.eventTime = eventTime;
    }
}
