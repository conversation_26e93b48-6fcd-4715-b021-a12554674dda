package top.continew.admin.api.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * API限流配置
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "api.rate-limit")
public class ApiRateLimitConfig {

    /**
     * 是否启用限流
     */
    private boolean enabled = true;

    /**
     * 默认限流配置
     */
    private RateLimitRule defaultRule = new RateLimitRule();

    /**
     * 特定路径的限流配置
     */
    private Map<String, RateLimitRule> pathRules;

    /**
     * 限流规则
     */
    @Data
    public static class RateLimitRule {

        /**
         * 时间窗口（秒）
         */
        private int windowSize = 60;

        /**
         * 最大请求数
         */
        private int maxRequests = 100;

        /**
         * 是否启用IP限流
         */
        private boolean enableIpLimit = true;

        /**
         * 是否启用用户限流
         */
        private boolean enableUserLimit = true;

        /**
         * 是否启用API密钥限流
         */
        private boolean enableApiKeyLimit = true;
    }
}
