package top.continew.admin.accounting.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import top.continew.admin.accounting.base.BaseServiceTest;
import top.continew.admin.accounting.enums.TransactionType;
import top.continew.admin.accounting.mapper.CategoryMapper;
import top.continew.admin.accounting.model.entity.CategoryDO;
import top.continew.admin.accounting.model.req.CategoryCreateReq;
import top.continew.admin.accounting.model.req.CategoryQueryReq;
import top.continew.admin.accounting.model.req.CategoryUpdateReq;
import top.continew.admin.accounting.model.resp.CategoryResp;
import top.continew.admin.accounting.service.impl.CategoryServiceImpl;
import top.continew.admin.accounting.util.TestDataUtil;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 分类服务测试
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@ExtendWith(MockitoExtension.class)
class CategoryServiceTest extends BaseServiceTest {

    @Mock
    private CategoryMapper categoryMapper;

    @InjectMocks
    private CategoryServiceImpl categoryService;

    private CategoryDO testCategory;
    private CategoryCreateReq createReq;
    private CategoryUpdateReq updateReq;

    @BeforeEach
    @Override
    protected void beforeEach() {
        super.beforeEach();
        testCategory = TestDataUtil.createTestCategory();
        createReq = TestDataUtil.createTestCategoryCreateReq();
        
        updateReq = new CategoryUpdateReq();
        updateReq.setName("更新后的分类");
        updateReq.setIcon("🎯");
        updateReq.setColor("#00FF00");
    }

    @Test
    void testAdd() {
        // Given
        when(categoryMapper.insert(any(CategoryDO.class))).thenReturn(1);
        when(categoryMapper.selectById(anyLong())).thenReturn(testCategory);

        // When
        Long categoryId = categoryService.add(createReq);

        // Then
        assertNotNull(categoryId);
        verify(categoryMapper).insert(any(CategoryDO.class));
    }

    @Test
    void testGetById() {
        // Given
        when(categoryMapper.selectById(1L)).thenReturn(testCategory);

        // When
        CategoryResp result = categoryService.get(1L);

        // Then
        assertNotNull(result);
        assertEquals(testCategory.getName(), result.getName());
        assertEquals(testCategory.getIcon(), result.getIcon());
        assertEquals(testCategory.getColor(), result.getColor());
        assertEquals(testCategory.getType(), result.getType());
        verify(categoryMapper).selectById(1L);
    }

    @Test
    void testUpdate() {
        // Given
        when(categoryMapper.selectById(1L)).thenReturn(testCategory);
        when(categoryMapper.updateById(any(CategoryDO.class))).thenReturn(1);

        // When
        assertDoesNotThrow(() -> categoryService.update(updateReq, 1L));

        // Then
        verify(categoryMapper).selectById(1L);
        verify(categoryMapper).updateById(any(CategoryDO.class));
    }

    @Test
    void testDelete() {
        // Given
        when(categoryMapper.selectById(1L)).thenReturn(testCategory);
        when(categoryMapper.deleteById(1L)).thenReturn(1);

        // When
        assertDoesNotThrow(() -> categoryService.delete(Arrays.asList(1L)));

        // Then
        verify(categoryMapper).selectById(1L);
        verify(categoryMapper).deleteById(1L);
    }

    @Test
    void testPage() {
        // Given
        CategoryQueryReq queryReq = new CategoryQueryReq();
        queryReq.setGroupId(1L);
        queryReq.setType(TransactionType.EXPENSE);
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPage(1);
        pageQuery.setSize(10);

        List<CategoryDO> categories = Arrays.asList(testCategory);
        when(categoryMapper.selectPage(any(), any())).thenReturn(categories);
        when(categoryMapper.selectCount(any())).thenReturn(1L);

        // When
        PageResp<CategoryResp> result = categoryService.page(queryReq, pageQuery);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getList().size());
        assertEquals(testCategory.getName(), result.getList().get(0).getName());
    }

    @Test
    void testList() {
        // Given
        CategoryQueryReq queryReq = new CategoryQueryReq();
        queryReq.setGroupId(1L);
        List<CategoryDO> categories = Arrays.asList(testCategory);
        when(categoryMapper.selectList(any())).thenReturn(categories);

        // When
        List<CategoryResp> result = categoryService.list(queryReq);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testCategory.getName(), result.get(0).getName());
    }

    @Test
    void testGetTree() {
        // Given
        CategoryDO parentCategory = TestDataUtil.createTestCategory();
        parentCategory.setId(1L);
        parentCategory.setParentId(0L);
        
        CategoryDO childCategory = TestDataUtil.createTestCategory();
        childCategory.setId(2L);
        childCategory.setParentId(1L);
        childCategory.setName("子分类");

        List<CategoryDO> categories = Arrays.asList(parentCategory, childCategory);
        when(categoryMapper.selectList(any())).thenReturn(categories);

        // When
        List<CategoryResp> result = categoryService.getTree(1L, TransactionType.EXPENSE);

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
        verify(categoryMapper).selectList(any());
    }

    @Test
    void testGetStatistics() {
        // Given
        when(categoryMapper.selectCategoryCount(1L, TransactionType.EXPENSE)).thenReturn(5L);
        when(categoryMapper.selectUsageStatistics(anyLong(), any(), any(), any()))
            .thenReturn(Arrays.asList());

        // When
        var statistics = categoryService.getStatistics(1L, TransactionType.EXPENSE);

        // Then
        assertNotNull(statistics);
        verify(categoryMapper).selectCategoryCount(1L, TransactionType.EXPENSE);
        verify(categoryMapper).selectUsageStatistics(anyLong(), any(), any(), any());
    }

    @Test
    void testInitDefaultCategories() {
        // Given
        when(categoryMapper.selectCount(any())).thenReturn(0L);
        when(categoryMapper.insert(any(CategoryDO.class))).thenReturn(1);

        // When
        assertDoesNotThrow(() -> categoryService.initDefaultCategories(1L));

        // Then
        verify(categoryMapper).selectCount(any());
        verify(categoryMapper, atLeastOnce()).insert(any(CategoryDO.class));
    }

    @Test
    void testUpdateSort() {
        // Given
        when(categoryMapper.selectById(1L)).thenReturn(testCategory);
        when(categoryMapper.updateById(any(CategoryDO.class))).thenReturn(1);

        // When
        assertDoesNotThrow(() -> categoryService.updateSort(1L, 5));

        // Then
        verify(categoryMapper).selectById(1L);
        verify(categoryMapper).updateById(any(CategoryDO.class));
    }

    @Test
    void testMoveToParent() {
        // Given
        CategoryDO parentCategory = TestDataUtil.createTestCategory();
        parentCategory.setId(2L);
        
        when(categoryMapper.selectById(1L)).thenReturn(testCategory);
        when(categoryMapper.selectById(2L)).thenReturn(parentCategory);
        when(categoryMapper.updateById(any(CategoryDO.class))).thenReturn(1);

        // When
        assertDoesNotThrow(() -> categoryService.moveToParent(1L, 2L));

        // Then
        verify(categoryMapper).selectById(1L);
        verify(categoryMapper).selectById(2L);
        verify(categoryMapper).updateById(any(CategoryDO.class));
    }

    @Test
    void testGetRecommendations() {
        // Given
        when(categoryMapper.selectRecommendations(anyString(), anyLong(), any(), anyInt()))
            .thenReturn(Arrays.asList(testCategory));

        // When
        List<CategoryResp> result = categoryService.getRecommendations("午餐", 1L, TransactionType.EXPENSE, 5);

        // Then
        assertNotNull(result);
        verify(categoryMapper).selectRecommendations("午餐", 1L, TransactionType.EXPENSE, 5);
    }

}
