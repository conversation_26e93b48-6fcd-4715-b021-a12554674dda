package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.admin.accounting.enums.CacheEvictionPolicyEnum;
import top.continew.admin.accounting.enums.CacheStrategyEnum;
import top.continew.admin.accounting.enums.CacheTypeEnum;
import top.continew.starter.extension.crud.model.entity.BaseDO;

import java.time.LocalDateTime;

/**
 * 缓存配置实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("acc_cache_config")
public class CacheConfigDO extends BaseDO {

    /**
     * 配置名称
     */
    private String configName;

    /**
     * 配置代码
     */
    private String configCode;

    /**
     * 缓存类型
     */
    private CacheTypeEnum cacheType;

    /**
     * 缓存策略
     */
    private CacheStrategyEnum cacheStrategy;

    /**
     * 淘汰策略
     */
    private CacheEvictionPolicyEnum evictionPolicy;

    /**
     * 缓存键前缀
     */
    private String keyPrefix;

    /**
     * 缓存键模式
     */
    private String keyPattern;

    /**
     * 过期时间（秒）
     */
    private Long expireTime;

    /**
     * 本地缓存最大大小
     */
    private Integer localMaxSize;

    /**
     * 本地缓存过期时间（秒）
     */
    private Long localExpireTime;

    /**
     * 远程缓存过期时间（秒）
     */
    private Long remoteExpireTime;

    /**
     * 是否启用缓存穿透保护
     */
    private Boolean penetrationProtect;

    /**
     * 是否启用缓存雪崩保护
     */
    private Boolean avalancheProtect;

    /**
     * 是否启用缓存击穿保护
     */
    private Boolean breakdownProtect;

    /**
     * 是否启用自动刷新
     */
    private Boolean autoRefresh;

    /**
     * 自动刷新间隔（秒）
     */
    private Long refreshInterval;

    /**
     * 是否启用预热
     */
    private Boolean preloadEnabled;

    /**
     * 预热数据源
     */
    private String preloadDataSource;

    /**
     * 预热策略
     */
    private String preloadStrategy;

    /**
     * 是否启用统计
     */
    private Boolean statisticsEnabled;

    /**
     * 统计间隔（分钟）
     */
    private Integer statisticsInterval;

    /**
     * 是否启用监控
     */
    private Boolean monitorEnabled;

    /**
     * 监控阈值配置
     */
    private String monitorThresholds;

    /**
     * 是否启用热点数据识别
     */
    private Boolean hotspotDetection;

    /**
     * 热点数据阈值
     */
    private Integer hotspotThreshold;

    /**
     * 热点数据时间窗口（分钟）
     */
    private Integer hotspotTimeWindow;

    /**
     * 配置状态
     */
    private String status;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 配置描述
     */
    private String description;

    /**
     * 扩展配置（JSON格式）
     */
    private String extendConfig;

    /**
     * 群组ID
     */
    private Long groupId;

    /**
     * 最后应用时间
     */
    private LocalDateTime lastAppliedTime;

    /**
     * 应用次数
     */
    private Long appliedCount;

}
