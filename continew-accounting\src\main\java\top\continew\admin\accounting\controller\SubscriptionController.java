package top.continew.admin.accounting.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.accounting.model.req.SubscriptionCreateReq;
import top.continew.admin.accounting.model.req.SubscriptionUpdateReq;
import top.continew.admin.accounting.model.resp.SubscriptionDetailResp;
import top.continew.admin.accounting.model.resp.SubscriptionListResp;
import top.continew.admin.accounting.model.query.SubscriptionQuery;
import top.continew.admin.accounting.service.SubscriptionService;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.controller.BaseController;
import top.continew.starter.core.util.response.R;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 订阅管理 API
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "订阅管理 API")
@RestController
@RequiredArgsConstructor
@Validated
@CrudRequestMapping(value = "/accounting/subscription", api = {CrudRequestMapping.Api.PAGE, CrudRequestMapping.Api.GET})
public class SubscriptionController extends BaseController<SubscriptionService, SubscriptionListResp, SubscriptionDetailResp, SubscriptionQuery, SubscriptionCreateReq> {

    @Operation(summary = "创建订阅", description = "创建新的订阅")
    @PostMapping
    public R<Long> create(@Valid @RequestBody SubscriptionCreateReq req) {
        return R.ok(baseService.createSubscription(req));
    }

    @Operation(summary = "更新订阅", description = "更新订阅信息")
    @PutMapping("/{id}")
    public R<Void> update(
            @Parameter(description = "订阅ID", example = "1") 
            @PathVariable Long id,
            @Valid @RequestBody SubscriptionUpdateReq req) {
        baseService.updateSubscription(req, id);
        return R.ok();
    }

    @Operation(summary = "取消订阅", description = "取消指定的订阅")
    @PutMapping("/{id}/cancel")
    public R<Void> cancel(
            @Parameter(description = "订阅ID", example = "1") 
            @PathVariable Long id,
            @Parameter(description = "取消原因", example = "用户主动取消") 
            @RequestParam @NotBlank String reason) {
        baseService.cancelSubscription(id, reason);
        return R.ok();
    }

    @Operation(summary = "续费订阅", description = "续费指定的订阅")
    @PutMapping("/{id}/renew")
    public R<Void> renew(
            @Parameter(description = "订阅ID", example = "1") 
            @PathVariable Long id) {
        baseService.renewSubscription(id);
        return R.ok();
    }

    @Operation(summary = "暂停订阅", description = "暂停指定的订阅")
    @PutMapping("/{id}/suspend")
    public R<Void> suspend(
            @Parameter(description = "订阅ID", example = "1") 
            @PathVariable Long id,
            @Parameter(description = "暂停原因", example = "违规使用") 
            @RequestParam @NotBlank String reason) {
        baseService.suspendSubscription(id, reason);
        return R.ok();
    }

    @Operation(summary = "恢复订阅", description = "恢复暂停的订阅")
    @PutMapping("/{id}/resume")
    public R<Void> resume(
            @Parameter(description = "订阅ID", example = "1") 
            @PathVariable Long id) {
        baseService.resumeSubscription(id);
        return R.ok();
    }

    @Operation(summary = "升级订阅", description = "升级订阅到更高级套餐")
    @PutMapping("/{id}/upgrade")
    public R<Void> upgrade(
            @Parameter(description = "订阅ID", example = "1") 
            @PathVariable Long id,
            @Parameter(description = "新套餐ID", example = "2") 
            @RequestParam @NotNull Long newPlanId) {
        baseService.upgradeSubscription(id, newPlanId);
        return R.ok();
    }

    @Operation(summary = "降级订阅", description = "降级订阅到更低级套餐")
    @PutMapping("/{id}/downgrade")
    public R<Void> downgrade(
            @Parameter(description = "订阅ID", example = "1") 
            @PathVariable Long id,
            @Parameter(description = "新套餐ID", example = "2") 
            @RequestParam @NotNull Long newPlanId) {
        baseService.downgradeSubscription(id, newPlanId);
        return R.ok();
    }

    @Operation(summary = "查询群组当前订阅", description = "查询群组当前有效的订阅")
    @GetMapping("/group/{groupId}/active")
    public R<SubscriptionDetailResp> getActiveByGroupId(
            @Parameter(description = "群组ID", example = "1") 
            @PathVariable Long groupId) {
        return R.ok(baseService.getActiveSubscriptionByGroupId(groupId));
    }

    @Operation(summary = "查询用户订阅历史", description = "查询用户的订阅历史记录")
    @GetMapping("/user/{userId}/history")
    public R<List<SubscriptionListResp>> getUserHistory(
            @Parameter(description = "用户ID", example = "1") 
            @PathVariable Long userId) {
        return R.ok(baseService.getUserSubscriptionHistory(userId));
    }

    @Operation(summary = "查询群组订阅历史", description = "查询群组的订阅历史记录")
    @GetMapping("/group/{groupId}/history")
    public R<List<SubscriptionListResp>> getGroupHistory(
            @Parameter(description = "群组ID", example = "1") 
            @PathVariable Long groupId) {
        return R.ok(baseService.getGroupSubscriptionHistory(groupId));
    }

    @Operation(summary = "检查群组订阅状态", description = "检查群组是否有有效订阅")
    @GetMapping("/group/{groupId}/active/exists")
    public R<Boolean> hasActiveSubscription(
            @Parameter(description = "群组ID", example = "1") 
            @PathVariable Long groupId) {
        return R.ok(baseService.hasActiveSubscription(groupId));
    }

    @Operation(summary = "检查功能权限", description = "检查群组是否有指定功能的权限")
    @GetMapping("/group/{groupId}/feature/{featureName}")
    public R<Boolean> hasFeaturePermission(
            @Parameter(description = "群组ID", example = "1") 
            @PathVariable Long groupId,
            @Parameter(description = "功能名称", example = "advancedReports") 
            @PathVariable String featureName) {
        return R.ok(baseService.hasFeaturePermission(groupId, featureName));
    }

    @Operation(summary = "检查使用限制", description = "检查群组是否超出使用限制")
    @GetMapping("/group/{groupId}/limit/{limitType}/exceeded")
    public R<Boolean> isUsageLimitExceeded(
            @Parameter(description = "群组ID", example = "1") 
            @PathVariable Long groupId,
            @Parameter(description = "限制类型", example = "maxTransactionsPerMonth") 
            @PathVariable String limitType,
            @Parameter(description = "当前使用量", example = "100") 
            @RequestParam @NotNull Integer currentUsage) {
        return R.ok(baseService.isUsageLimitExceeded(groupId, limitType, currentUsage));
    }

    @Operation(summary = "获取剩余使用量", description = "获取群组剩余的使用量")
    @GetMapping("/group/{groupId}/limit/{limitType}/remaining")
    public R<Integer> getRemainingUsage(
            @Parameter(description = "群组ID", example = "1") 
            @PathVariable Long groupId,
            @Parameter(description = "限制类型", example = "maxTransactionsPerMonth") 
            @PathVariable String limitType,
            @Parameter(description = "当前使用量", example = "100") 
            @RequestParam @NotNull Integer currentUsage) {
        return R.ok(baseService.getRemainingUsage(groupId, limitType, currentUsage));
    }

    @Operation(summary = "处理即将过期的订阅", description = "处理即将过期的订阅（系统调用）")
    @PostMapping("/handle-expiring")
    public R<Void> handleExpiringSubscriptions() {
        baseService.handleExpiringSubscriptions();
        return R.ok();
    }

    @Operation(summary = "处理已过期的订阅", description = "处理已过期的订阅（系统调用）")
    @PostMapping("/handle-expired")
    public R<Void> handleExpiredSubscriptions() {
        baseService.handleExpiredSubscriptions();
        return R.ok();
    }

    @Operation(summary = "自动续费处理", description = "处理自动续费（系统调用）")
    @PostMapping("/auto-renewal")
    public R<Void> processAutoRenewal() {
        baseService.processAutoRenewal();
        return R.ok();
    }

    @Operation(summary = "获取订阅统计", description = "获取订阅统计信息")
    @GetMapping("/stats")
    public R<SubscriptionService.SubscriptionStatsResp> getStats() {
        return R.ok(baseService.getSubscriptionStats());
    }
}
