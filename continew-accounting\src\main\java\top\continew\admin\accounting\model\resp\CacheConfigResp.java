package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.accounting.enums.CacheEvictionPolicyEnum;
import top.continew.admin.accounting.enums.CacheStrategyEnum;
import top.continew.admin.accounting.enums.CacheTypeEnum;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 缓存配置响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "缓存配置响应")
public class CacheConfigResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID", example = "1")
    private Long id;

    /**
     * 配置名称
     */
    @Schema(description = "配置名称", example = "用户信息缓存")
    private String configName;

    /**
     * 配置代码
     */
    @Schema(description = "配置代码", example = "USER_INFO")
    private String configCode;

    /**
     * 缓存类型
     */
    @Schema(description = "缓存类型", example = "BOTH")
    private CacheTypeEnum cacheType;

    /**
     * 缓存策略
     */
    @Schema(description = "缓存策略", example = "READ_THROUGH")
    private CacheStrategyEnum cacheStrategy;

    /**
     * 淘汰策略
     */
    @Schema(description = "淘汰策略", example = "LRU")
    private CacheEvictionPolicyEnum evictionPolicy;

    /**
     * 缓存键前缀
     */
    @Schema(description = "缓存键前缀", example = "USER:")
    private String keyPrefix;

    /**
     * 缓存键模式
     */
    @Schema(description = "缓存键模式", example = "USER:{userId}")
    private String keyPattern;

    /**
     * 过期时间（秒）
     */
    @Schema(description = "过期时间（秒）", example = "3600")
    private Long expireTime;

    /**
     * 过期时间格式化
     */
    @Schema(description = "过期时间格式化", example = "1小时")
    private String expireTimeFormatted;

    /**
     * 本地缓存最大大小
     */
    @Schema(description = "本地缓存最大大小", example = "1000")
    private Integer localMaxSize;

    /**
     * 本地缓存过期时间（秒）
     */
    @Schema(description = "本地缓存过期时间（秒）", example = "1800")
    private Long localExpireTime;

    /**
     * 远程缓存过期时间（秒）
     */
    @Schema(description = "远程缓存过期时间（秒）", example = "7200")
    private Long remoteExpireTime;

    /**
     * 是否启用缓存穿透保护
     */
    @Schema(description = "是否启用缓存穿透保护", example = "true")
    private Boolean penetrationProtect;

    /**
     * 是否启用缓存雪崩保护
     */
    @Schema(description = "是否启用缓存雪崩保护", example = "true")
    private Boolean avalancheProtect;

    /**
     * 是否启用缓存击穿保护
     */
    @Schema(description = "是否启用缓存击穿保护", example = "true")
    private Boolean breakdownProtect;

    /**
     * 是否启用自动刷新
     */
    @Schema(description = "是否启用自动刷新", example = "false")
    private Boolean autoRefresh;

    /**
     * 自动刷新间隔（秒）
     */
    @Schema(description = "自动刷新间隔（秒）", example = "3600")
    private Long refreshInterval;

    /**
     * 是否启用预热
     */
    @Schema(description = "是否启用预热", example = "false")
    private Boolean preloadEnabled;

    /**
     * 预热数据源
     */
    @Schema(description = "预热数据源", example = "database")
    private String preloadDataSource;

    /**
     * 预热策略
     */
    @Schema(description = "预热策略", example = "startup")
    private String preloadStrategy;

    /**
     * 是否启用统计
     */
    @Schema(description = "是否启用统计", example = "true")
    private Boolean statisticsEnabled;

    /**
     * 统计间隔（分钟）
     */
    @Schema(description = "统计间隔（分钟）", example = "15")
    private Integer statisticsInterval;

    /**
     * 是否启用监控
     */
    @Schema(description = "是否启用监控", example = "true")
    private Boolean monitorEnabled;

    /**
     * 监控阈值配置
     */
    @Schema(description = "监控阈值配置", example = "{\"hitRate\":0.8,\"errorRate\":0.05}")
    private String monitorThresholds;

    /**
     * 是否启用热点数据识别
     */
    @Schema(description = "是否启用热点数据识别", example = "true")
    private Boolean hotspotDetection;

    /**
     * 热点数据阈值
     */
    @Schema(description = "热点数据阈值", example = "100")
    private Integer hotspotThreshold;

    /**
     * 热点数据时间窗口（分钟）
     */
    @Schema(description = "热点数据时间窗口（分钟）", example = "10")
    private Integer hotspotTimeWindow;

    /**
     * 配置状态
     */
    @Schema(description = "配置状态", example = "ENABLE")
    private String status;

    /**
     * 优先级
     */
    @Schema(description = "优先级", example = "1")
    private Integer priority;

    /**
     * 配置描述
     */
    @Schema(description = "配置描述", example = "用户信息缓存配置")
    private String description;

    /**
     * 扩展配置（JSON格式）
     */
    @Schema(description = "扩展配置（JSON格式）", example = "{\"customKey\":\"customValue\"}")
    private String extendConfig;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 最后应用时间
     */
    @Schema(description = "最后应用时间", example = "2025-01-01 12:00:00")
    private LocalDateTime lastAppliedTime;

    /**
     * 应用次数
     */
    @Schema(description = "应用次数", example = "100")
    private Long appliedCount;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-01-01 10:00:00")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2025-01-01 12:00:00")
    private LocalDateTime updateTime;

}
