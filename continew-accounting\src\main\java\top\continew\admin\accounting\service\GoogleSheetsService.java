package top.continew.admin.accounting.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import top.continew.admin.accounting.model.entity.GoogleSheetsConfig;
import top.continew.admin.accounting.model.query.GoogleSheetsConfigQuery;
import top.continew.admin.accounting.model.query.GoogleSheetsSyncLogQuery;
import top.continew.admin.accounting.model.req.GoogleSheetsConfigReq;
import top.continew.admin.accounting.model.req.GoogleSheetsConfigUpdateReq;
import top.continew.admin.accounting.model.req.GoogleSheetsSyncReq;
import top.continew.admin.accounting.model.resp.GoogleSheetsConfigResp;
import top.continew.admin.accounting.model.resp.GoogleSheetsSyncLogResp;
import top.continew.admin.accounting.model.resp.GoogleSheetsSyncResp;
import top.continew.admin.common.model.resp.LabelValueResp;

import java.util.List;
import java.util.Map;

/**
 * Google Sheets服务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface GoogleSheetsService {

    // ==================== 配置管理 ====================

    /**
     * 创建Google Sheets配置
     *
     * @param req 配置请求
     * @return 配置ID
     */
    Long createConfig(GoogleSheetsConfigReq req);

    /**
     * 更新Google Sheets配置
     *
     * @param configId 配置ID
     * @param req      更新请求
     * @return 是否成功
     */
    Boolean updateConfig(Long configId, GoogleSheetsConfigUpdateReq req);

    /**
     * 删除Google Sheets配置
     *
     * @param configId 配置ID
     * @return 是否成功
     */
    Boolean deleteConfig(Long configId);

    /**
     * 批量删除Google Sheets配置
     *
     * @param configIds 配置ID列表
     * @return 是否成功
     */
    Boolean deleteConfigs(List<Long> configIds);

    /**
     * 获取Google Sheets配置详情
     *
     * @param configId 配置ID
     * @return 配置详情
     */
    GoogleSheetsConfigResp getConfigDetail(Long configId);

    /**
     * 分页查询Google Sheets配置
     *
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<GoogleSheetsConfigResp> pageConfigs(GoogleSheetsConfigQuery query);

    /**
     * 获取配置列表
     *
     * @param query 查询条件
     * @return 配置列表
     */
    List<GoogleSheetsConfigResp> listConfigs(GoogleSheetsConfigQuery query);

    /**
     * 获取配置选项列表
     *
     * @param groupId 群组ID
     * @return 配置选项列表
     */
    List<LabelValueResp<Long>> listConfigOptions(Long groupId);

    // ==================== 配置操作 ====================

    /**
     * 启用配置
     *
     * @param configId 配置ID
     * @return 是否成功
     */
    Boolean enableConfig(Long configId);

    /**
     * 禁用配置
     *
     * @param configId 配置ID
     * @return 是否成功
     */
    Boolean disableConfig(Long configId);

    /**
     * 复制配置
     *
     * @param configId 配置ID
     * @param newName  新配置名称
     * @return 新配置ID
     */
    Long copyConfig(Long configId, String newName);

    /**
     * 导出配置
     *
     * @param configIds 配置ID列表
     * @return 导出文件路径
     */
    String exportConfigs(List<Long> configIds);

    /**
     * 导入配置
     *
     * @param filePath 文件路径
     * @param groupId  群组ID
     * @return 导入结果
     */
    Map<String, Object> importConfigs(String filePath, Long groupId);

    // ==================== 认证管理 ====================

    /**
     * 验证Google认证
     *
     * @param configId 配置ID
     * @return 验证结果
     */
    Map<String, Object> validateAuth(Long configId);

    /**
     * 刷新认证令牌
     *
     * @param configId 配置ID
     * @return 是否成功
     */
    Boolean refreshAuthToken(Long configId);

    /**
     * 获取OAuth2授权URL
     *
     * @param configId 配置ID
     * @return 授权URL
     */
    String getOAuth2AuthUrl(Long configId);

    /**
     * 处理OAuth2回调
     *
     * @param configId 配置ID
     * @param code     授权码
     * @return 是否成功
     */
    Boolean handleOAuth2Callback(Long configId, String code);

    // ==================== 表格操作 ====================

    /**
     * 获取表格信息
     *
     * @param configId 配置ID
     * @return 表格信息
     */
    Map<String, Object> getSpreadsheetInfo(Long configId);

    /**
     * 获取工作表列表
     *
     * @param configId 配置ID
     * @return 工作表列表
     */
    List<Map<String, Object>> getSheetList(Long configId);

    /**
     * 创建工作表
     *
     * @param configId  配置ID
     * @param sheetName 工作表名称
     * @return 是否成功
     */
    Boolean createSheet(Long configId, String sheetName);

    /**
     * 删除工作表
     *
     * @param configId  配置ID
     * @param sheetName 工作表名称
     * @return 是否成功
     */
    Boolean deleteSheet(Long configId, String sheetName);

    /**
     * 获取表格数据预览
     *
     * @param configId 配置ID
     * @param limit    限制行数
     * @return 数据预览
     */
    Map<String, Object> previewData(Long configId, Integer limit);

    // ==================== 同步操作 ====================

    /**
     * 执行同步
     *
     * @param req 同步请求
     * @return 同步响应
     */
    GoogleSheetsSyncResp executeSync(GoogleSheetsSyncReq req);

    /**
     * 异步执行同步
     *
     * @param req 同步请求
     * @return 同步ID
     */
    String executeSyncAsync(GoogleSheetsSyncReq req);

    /**
     * 停止同步
     *
     * @param syncId 同步ID
     * @return 是否成功
     */
    Boolean stopSync(String syncId);

    /**
     * 重试同步
     *
     * @param syncId 同步ID
     * @return 同步响应
     */
    GoogleSheetsSyncResp retrySync(String syncId);

    /**
     * 获取同步状态
     *
     * @param syncId 同步ID
     * @return 同步状态
     */
    Map<String, Object> getSyncStatus(String syncId);

    /**
     * 获取同步进度
     *
     * @param syncId 同步ID
     * @return 同步进度
     */
    Map<String, Object> getSyncProgress(String syncId);

    // ==================== 同步日志 ====================

    /**
     * 分页查询同步日志
     *
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<GoogleSheetsSyncLogResp> pageSyncLogs(GoogleSheetsSyncLogQuery query);

    /**
     * 获取同步日志详情
     *
     * @param logId 日志ID
     * @return 日志详情
     */
    GoogleSheetsSyncLogResp getSyncLogDetail(Long logId);

    /**
     * 删除同步日志
     *
     * @param logId 日志ID
     * @return 是否成功
     */
    Boolean deleteSyncLog(Long logId);

    /**
     * 批量删除同步日志
     *
     * @param logIds 日志ID列表
     * @return 是否成功
     */
    Boolean deleteSyncLogs(List<Long> logIds);

    /**
     * 清理过期日志
     *
     * @param retentionDays 保留天数
     * @return 清理数量
     */
    Integer cleanupExpiredLogs(Integer retentionDays);

    // ==================== 备份管理 ====================

    /**
     * 创建备份
     *
     * @param configId   配置ID
     * @param backupType 备份类型
     * @return 备份ID
     */
    String createBackup(Long configId, String backupType);

    /**
     * 恢复备份
     *
     * @param backupId 备份ID
     * @return 是否成功
     */
    Boolean restoreBackup(String backupId);

    /**
     * 删除备份
     *
     * @param backupId 备份ID
     * @return 是否成功
     */
    Boolean deleteBackup(String backupId);

    /**
     * 获取备份列表
     *
     * @param configId 配置ID
     * @return 备份列表
     */
    List<Map<String, Object>> listBackups(Long configId);

    /**
     * 清理过期备份
     *
     * @param configId      配置ID
     * @param retentionDays 保留天数
     * @return 清理数量
     */
    Integer cleanupExpiredBackups(Long configId, Integer retentionDays);

    // ==================== 统计分析 ====================

    /**
     * 获取配置统计
     *
     * @param groupId 群组ID
     * @return 统计信息
     */
    Map<String, Object> getConfigStatistics(Long groupId);

    /**
     * 获取同步统计
     *
     * @param configId 配置ID
     * @param days     统计天数
     * @return 统计信息
     */
    Map<String, Object> getSyncStatistics(Long configId, Integer days);

    /**
     * 获取性能统计
     *
     * @param configId 配置ID
     * @param days     统计天数
     * @return 性能统计
     */
    Map<String, Object> getPerformanceStatistics(Long configId, Integer days);

    /**
     * 获取错误统计
     *
     * @param configId 配置ID
     * @param days     统计天数
     * @return 错误统计
     */
    Map<String, Object> getErrorStatistics(Long configId, Integer days);

    /**
     * 获取使用趋势
     *
     * @param groupId 群组ID
     * @param days    统计天数
     * @return 使用趋势
     */
    Map<String, Object> getUsageTrends(Long groupId, Integer days);

    // ==================== 健康检查 ====================

    /**
     * 检查配置健康状态
     *
     * @param configId 配置ID
     * @return 健康状态
     */
    Map<String, Object> checkConfigHealth(Long configId);

    /**
     * 检查所有配置健康状态
     *
     * @param groupId 群组ID
     * @return 健康状态列表
     */
    List<Map<String, Object>> checkAllConfigsHealth(Long groupId);

    /**
     * 修复配置问题
     *
     * @param configId 配置ID
     * @return 修复结果
     */
    Map<String, Object> repairConfig(Long configId);

    // ==================== 工具方法 ====================

    /**
     * 测试连接
     *
     * @param configId 配置ID
     * @return 测试结果
     */
    Map<String, Object> testConnection(Long configId);

    /**
     * 验证字段映射
     *
     * @param configId 配置ID
     * @return 验证结果
     */
    Map<String, Object> validateFieldMapping(Long configId);

    /**
     * 获取字段建议
     *
     * @param configId 配置ID
     * @return 字段建议
     */
    List<Map<String, Object>> getFieldSuggestions(Long configId);

    /**
     * 生成同步报告
     *
     * @param syncId 同步ID
     * @return 报告文件路径
     */
    String generateSyncReport(String syncId);

    /**
     * 获取配置模板
     *
     * @param templateType 模板类型
     * @return 配置模板
     */
    Map<String, Object> getConfigTemplate(String templateType);
}
