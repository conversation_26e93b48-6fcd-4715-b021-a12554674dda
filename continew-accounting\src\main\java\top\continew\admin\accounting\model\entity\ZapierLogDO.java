package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.data.mybatis.plus.base.BaseEntity;

import java.time.LocalDateTime;

/**
 * Zapier日志实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("acc_zapier_log")
@Schema(description = "Zapier日志")
public class ZapierLogDO extends BaseEntity {

    /**
     * 配置ID
     */
    @Schema(description = "配置ID")
    private Long configId;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID")
    private Long groupId;

    /**
     * 触发器类型
     */
    @Schema(description = "触发器类型")
    private String triggerType;

    /**
     * 事件类型
     */
    @Schema(description = "事件类型")
    private String eventType;

    /**
     * 业务ID
     */
    @Schema(description = "业务ID")
    private Long businessId;

    /**
     * 业务类型
     */
    @Schema(description = "业务类型")
    private String businessType;

    /**
     * 请求数据
     */
    @Schema(description = "请求数据")
    private String requestData;

    /**
     * 响应数据
     */
    @Schema(description = "响应数据")
    private String responseData;

    /**
     * 请求头
     */
    @Schema(description = "请求头")
    private String requestHeaders;

    /**
     * 响应头
     */
    @Schema(description = "响应头")
    private String responseHeaders;

    /**
     * HTTP状态码
     */
    @Schema(description = "HTTP状态码")
    private Integer httpStatus;

    /**
     * 执行状态
     */
    @Schema(description = "执行状态")
    private String status;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 错误代码
     */
    @Schema(description = "错误代码")
    private String errorCode;

    /**
     * 执行时间
     */
    @Schema(description = "执行时间")
    private LocalDateTime executedAt;

    /**
     * 执行耗时（毫秒）
     */
    @Schema(description = "执行耗时（毫秒）")
    private Long executionTime;

    /**
     * 重试次数
     */
    @Schema(description = "重试次数")
    private Integer retryCount;

    /**
     * 是否为重试
     */
    @Schema(description = "是否为重试")
    private Boolean isRetry;

    /**
     * 原始日志ID（重试时关联）
     */
    @Schema(description = "原始日志ID")
    private Long originalLogId;

    /**
     * 用户代理
     */
    @Schema(description = "用户代理")
    private String userAgent;

    /**
     * IP地址
     */
    @Schema(description = "IP地址")
    private String ipAddress;

    /**
     * 请求大小（字节）
     */
    @Schema(description = "请求大小（字节）")
    private Long requestSize;

    /**
     * 响应大小（字节）
     */
    @Schema(description = "响应大小（字节）")
    private Long responseSize;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}
