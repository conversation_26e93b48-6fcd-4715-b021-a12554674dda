package top.continew.admin.accounting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import top.continew.admin.accounting.enums.TransactionType;
import top.continew.admin.accounting.model.entity.CategoryDO;
import top.continew.admin.accounting.model.resp.CategoryListResp;

import java.util.List;

/**
 * 分类 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface CategoryMapper extends BaseMapper<CategoryDO> {

    /**
     * 查询群组分类列表
     *
     * @param groupId 群组ID
     * @param type    分类类型
     * @return 分类列表
     */
    @Select("""
        SELECT c.id, c.group_id, g.name as group_name, c.name, c.parent_id,
               p.name as parent_name, c.type, c.icon, c.color, c.description,
               c.sort, c.is_system, c.is_default, c.status,
               (SELECT COUNT(*) FROM acc_category child WHERE child.parent_id = c.id AND child.status = 1) as children_count,
               (SELECT COUNT(*) FROM acc_transaction t WHERE t.category = c.name AND t.group_id = c.group_id AND t.status = 1) as usage_count,
               c.create_time, c.update_time
        FROM acc_category c
        LEFT JOIN acc_group g ON c.group_id = g.id
        LEFT JOIN acc_category p ON c.parent_id = p.id
        WHERE c.group_id = #{groupId} 
        AND (#{type} IS NULL OR c.type = #{type})
        AND c.status = 1
        ORDER BY c.sort ASC, c.create_time ASC
        """)
    List<CategoryListResp> selectGroupCategories(@Param("groupId") Long groupId, @Param("type") TransactionType type);

    /**
     * 查询默认分类
     *
     * @param groupId 群组ID
     * @param type    分类类型
     * @return 默认分类
     */
    @Select("""
        SELECT * FROM acc_category 
        WHERE group_id = #{groupId} AND type = #{type} 
        AND is_default = 1 AND status = 1
        LIMIT 1
        """)
    CategoryDO selectDefaultCategory(@Param("groupId") Long groupId, @Param("type") TransactionType type);

    /**
     * 检查分类名称是否存在
     *
     * @param name     分类名称
     * @param parentId 父分类ID
     * @param groupId  群组ID
     * @param excludeId 排除的分类ID
     * @return 是否存在
     */
    @Select("""
        SELECT COUNT(*) > 0 FROM acc_category 
        WHERE name = #{name} AND group_id = #{groupId}
        AND (#{parentId} IS NULL AND parent_id IS NULL OR parent_id = #{parentId})
        AND (#{excludeId} IS NULL OR id != #{excludeId})
        AND status = 1
        """)
    boolean existsByName(@Param("name") String name, @Param("parentId") Long parentId, 
                        @Param("groupId") Long groupId, @Param("excludeId") Long excludeId);

    /**
     * 查询子分类ID列表
     *
     * @param categoryId 分类ID
     * @return 子分类ID列表
     */
    @Select("""
        SELECT id FROM acc_category 
        WHERE FIND_IN_SET(#{categoryId}, ancestors) > 0 
        OR id = #{categoryId}
        AND status = 1
        """)
    List<Long> selectChildrenIds(@Param("categoryId") Long categoryId);

    /**
     * 更新子分类的祖先路径
     *
     * @param categoryId   分类ID
     * @param newAncestors 新的祖先路径
     * @param oldAncestors 旧的祖先路径
     * @return 更新行数
     */
    @Update("""
        UPDATE acc_category 
        SET ancestors = REPLACE(ancestors, #{oldAncestors}, #{newAncestors})
        WHERE FIND_IN_SET(#{categoryId}, ancestors) > 0
        """)
    int updateChildrenAncestors(@Param("categoryId") Long categoryId, 
                               @Param("newAncestors") String newAncestors, 
                               @Param("oldAncestors") String oldAncestors);

    /**
     * 检查分类是否被使用
     *
     * @param categoryId 分类ID
     * @return 使用次数
     */
    @Select("""
        SELECT COUNT(*) FROM acc_transaction t
        INNER JOIN acc_category c ON t.category = c.name AND t.group_id = c.group_id
        WHERE c.id = #{categoryId} AND t.status = 1
        """)
    int countUsage(@Param("categoryId") Long categoryId);

    /**
     * 检查是否有子分类
     *
     * @param categoryId 分类ID
     * @return 子分类数量
     */
    @Select("""
        SELECT COUNT(*) FROM acc_category 
        WHERE parent_id = #{categoryId} AND status = 1
        """)
    int countChildren(@Param("categoryId") Long categoryId);

    /**
     * 批量删除分类（软删除）
     *
     * @param categoryIds 分类ID列表
     * @return 删除行数
     */
    int batchDelete(@Param("categoryIds") List<Long> categoryIds);

    /**
     * 查询分类的祖先路径
     *
     * @param parentId 父分类ID
     * @return 祖先路径
     */
    @Select("""
        SELECT CASE 
            WHEN #{parentId} IS NULL OR #{parentId} = 0 THEN '0'
            ELSE CONCAT(COALESCE(ancestors, '0'), ',', #{parentId})
        END as ancestors
        FROM acc_category 
        WHERE id = #{parentId}
        UNION ALL
        SELECT '0' as ancestors
        WHERE #{parentId} IS NULL OR #{parentId} = 0
        LIMIT 1
        """)
    String selectAncestors(@Param("parentId") Long parentId);

    /**
     * 统计群组分类数量
     *
     * @param groupId 群组ID
     * @param type    分类类型
     * @return 分类数量
     */
    @Select("""
        SELECT COUNT(*) FROM acc_category 
        WHERE group_id = #{groupId} 
        AND (#{type} IS NULL OR type = #{type})
        AND status = 1
        """)
    int countByGroupId(@Param("groupId") Long groupId, @Param("type") TransactionType type);

    /**
     * 查询群组的所有分类（用于树形结构）
     *
     * @param groupId 群组ID
     * @param type    分类类型
     * @return 分类列表
     */
    @Select("""
        SELECT * FROM acc_category 
        WHERE group_id = #{groupId} 
        AND (#{type} IS NULL OR type = #{type})
        AND status = 1
        ORDER BY sort ASC, create_time ASC
        """)
    List<CategoryDO> selectForTree(@Param("groupId") Long groupId, @Param("type") TransactionType type);
}
