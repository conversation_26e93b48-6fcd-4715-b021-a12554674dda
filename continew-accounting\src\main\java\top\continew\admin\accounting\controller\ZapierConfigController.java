package top.continew.admin.accounting.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.accounting.model.query.ZapierConfigQuery;
import top.continew.admin.accounting.model.req.ZapierConfigReq;
import top.continew.admin.accounting.model.req.ZapierConfigUpdateReq;
import top.continew.admin.accounting.model.resp.ZapierConfigResp;
import top.continew.admin.accounting.service.ZapierConfigService;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.controller.BaseController;
import top.continew.starter.core.util.response.Response;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Zapier配置管理
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "Zapier配置管理")
@RestController
@RequiredArgsConstructor
@Validated
@CrudRequestMapping(value = "/accounting/zapier/config", api = {CrudRequestMapping.Api.PAGE, CrudRequestMapping.Api.GET, CrudRequestMapping.Api.ADD, CrudRequestMapping.Api.UPDATE, CrudRequestMapping.Api.DELETE})
public class ZapierConfigController extends BaseController<ZapierConfigService, ZapierConfigResp, ZapierConfigResp, ZapierConfigQuery, ZapierConfigReq, ZapierConfigUpdateReq> {

    @Operation(summary = "查询群组的Zapier配置列表", description = "查询指定群组的所有Zapier配置")
    @GetMapping("/group/{groupId}")
    public Response<List<ZapierConfigResp>> listByGroupId(
            @Parameter(description = "群组ID", example = "1") @PathVariable Long groupId) {
        return Response.success(baseService.listByGroupId(groupId));
    }

    @Operation(summary = "启用/禁用配置", description = "启用或禁用指定的Zapier配置")
    @PutMapping("/{id}/enabled")
    public Response<Void> updateEnabled(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long id,
            @Parameter(description = "是否启用", example = "true") @RequestParam Boolean enabled) {
        baseService.updateEnabled(id, enabled);
        return Response.success();
    }

    @Operation(summary = "更新配置状态", description = "更新指定配置的状态")
    @PutMapping("/{id}/status")
    public Response<Void> updateStatus(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long id,
            @Parameter(description = "状态", example = "ACTIVE") @RequestParam String status) {
        baseService.updateStatus(id, status);
        return Response.success();
    }

    @Operation(summary = "测试配置连接", description = "测试指定配置的Webhook连接")
    @PostMapping("/{id}/test")
    public Response<Map<String, Object>> testConnection(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long id) {
        return Response.success(baseService.testConnection(id));
    }

    @Operation(summary = "测试Webhook URL", description = "测试指定的Webhook URL连接")
    @PostMapping("/test-webhook")
    public Response<Map<String, Object>> testWebhook(
            @Parameter(description = "Webhook URL") @RequestParam String webhookUrl,
            @Parameter(description = "测试数据") @RequestBody(required = false) Map<String, Object> testData) {
        return Response.success(baseService.testWebhook(webhookUrl, testData));
    }

    @Operation(summary = "触发配置执行", description = "手动触发指定配置的执行")
    @PostMapping("/{id}/trigger")
    public Response<Map<String, Object>> triggerExecution(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long id,
            @Parameter(description = "事件类型", example = "CREATE") @RequestParam String eventType,
            @Parameter(description = "业务ID", example = "123") @RequestParam(required = false) Long businessId,
            @Parameter(description = "数据") @RequestBody(required = false) Map<String, Object> data) {
        return Response.success(baseService.triggerExecution(id, eventType, businessId, data));
    }

    @Operation(summary = "批量触发配置执行", description = "批量触发指定群组和触发器类型的配置执行")
    @PostMapping("/batch-trigger")
    public Response<List<Map<String, Object>>> batchTriggerExecution(
            @Parameter(description = "群组ID", example = "1") @RequestParam Long groupId,
            @Parameter(description = "触发器类型", example = "TRANSACTION_CREATED") @RequestParam String triggerType,
            @Parameter(description = "事件类型", example = "CREATE") @RequestParam String eventType,
            @Parameter(description = "业务ID", example = "123") @RequestParam(required = false) Long businessId,
            @Parameter(description = "数据") @RequestBody(required = false) Map<String, Object> data) {
        return Response.success(baseService.batchTriggerExecution(groupId, triggerType, eventType, businessId, data));
    }

    @Operation(summary = "查询配置统计信息", description = "查询指定群组的配置统计信息")
    @GetMapping("/stats/{groupId}")
    public Response<Map<String, Object>> getConfigStats(
            @Parameter(description = "群组ID", example = "1") @PathVariable Long groupId) {
        return Response.success(baseService.getConfigStats(groupId));
    }

    @Operation(summary = "查询触发器类型统计", description = "查询指定群组的触发器类型统计")
    @GetMapping("/stats/{groupId}/trigger-types")
    public Response<List<Map<String, Object>>> getTriggerTypeStats(
            @Parameter(description = "群组ID", example = "1") @PathVariable Long groupId) {
        return Response.success(baseService.getTriggerTypeStats(groupId));
    }

    @Operation(summary = "查询活跃配置排行", description = "查询指定群组的活跃配置排行")
    @GetMapping("/ranking/{groupId}/active")
    public Response<List<Map<String, Object>>> getActiveConfigRanking(
            @Parameter(description = "群组ID", example = "1") @PathVariable Long groupId,
            @Parameter(description = "限制数量", example = "10") @RequestParam(defaultValue = "10") Integer limit) {
        return Response.success(baseService.getActiveConfigRanking(groupId, limit));
    }

    @Operation(summary = "查询配置性能统计", description = "查询指定群组在指定时间范围内的配置性能统计")
    @GetMapping("/stats/{groupId}/performance")
    public Response<List<Map<String, Object>>> getPerformanceStats(
            @Parameter(description = "群组ID", example = "1") @PathVariable Long groupId,
            @Parameter(description = "开始时间") @RequestParam LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam LocalDateTime endTime) {
        return Response.success(baseService.getPerformanceStats(groupId, startTime, endTime));
    }

    @Operation(summary = "查询错误配置", description = "查询指定群组在指定时间内的错误配置")
    @GetMapping("/error/{groupId}")
    public Response<List<ZapierConfigResp>> getErrorConfigs(
            @Parameter(description = "群组ID", example = "1") @PathVariable Long groupId,
            @Parameter(description = "小时数", example = "24") @RequestParam(defaultValue = "24") Integer hours) {
        return Response.success(baseService.getErrorConfigs(groupId, hours));
    }

    @Operation(summary = "查询长时间未触发的配置", description = "查询指定群组长时间未触发的配置")
    @GetMapping("/inactive/{groupId}")
    public Response<List<ZapierConfigResp>> getInactiveConfigs(
            @Parameter(description = "群组ID", example = "1") @PathVariable Long groupId,
            @Parameter(description = "天数", example = "7") @RequestParam(defaultValue = "7") Integer days) {
        return Response.success(baseService.getInactiveConfigs(groupId, days));
    }

    @Operation(summary = "批量更新配置状态", description = "批量更新多个配置的状态")
    @PutMapping("/batch/status")
    public Response<Void> batchUpdateStatus(
            @Parameter(description = "配置ID列表") @RequestParam List<Long> ids,
            @Parameter(description = "状态", example = "ACTIVE") @RequestParam String status) {
        baseService.batchUpdateStatus(ids, status);
        return Response.success();
    }

    @Operation(summary = "批量启用/禁用配置", description = "批量启用或禁用多个配置")
    @PutMapping("/batch/enabled")
    public Response<Void> batchUpdateEnabled(
            @Parameter(description = "配置ID列表") @RequestParam List<Long> ids,
            @Parameter(description = "是否启用", example = "true") @RequestParam Boolean enabled) {
        baseService.batchUpdateEnabled(ids, enabled);
        return Response.success();
    }

    @Operation(summary = "复制配置", description = "复制指定的配置")
    @PostMapping("/{id}/copy")
    public Response<Long> copyConfig(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long id,
            @Parameter(description = "新配置名称", example = "复制的配置") @RequestParam String name) {
        return Response.success(baseService.copyConfig(id, name));
    }

    @Operation(summary = "导出配置", description = "导出指定的配置")
    @PostMapping("/export")
    public Response<Map<String, Object>> exportConfigs(
            @Parameter(description = "配置ID列表") @RequestBody List<Long> ids) {
        return Response.success(baseService.exportConfigs(ids));
    }

    @Operation(summary = "导入配置", description = "导入配置到指定群组")
    @PostMapping("/import/{groupId}")
    public Response<Map<String, Object>> importConfigs(
            @Parameter(description = "群组ID", example = "1") @PathVariable Long groupId,
            @Parameter(description = "配置数据") @RequestBody Map<String, Object> configData) {
        return Response.success(baseService.importConfigs(groupId, configData));
    }

    @Operation(summary = "查询配置的触发历史趋势", description = "查询指定配置在指定时间范围内的触发历史趋势")
    @GetMapping("/{id}/trend")
    public Response<List<Map<String, Object>>> getTriggerTrend(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long id,
            @Parameter(description = "开始时间") @RequestParam LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam LocalDateTime endTime) {
        return Response.success(baseService.getTriggerTrend(id, startTime, endTime));
    }

    @Operation(summary = "重置配置统计", description = "重置指定配置的统计信息")
    @PostMapping("/{id}/reset-stats")
    public Response<Void> resetStats(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long id) {
        baseService.resetStats(id);
        return Response.success();
    }

    @Operation(summary = "查询即将过期的配置", description = "查询即将过期的配置")
    @GetMapping("/expiring")
    public Response<List<ZapierConfigResp>> getExpiringConfigs(
            @Parameter(description = "天数阈值", example = "30") @RequestParam(defaultValue = "30") Integer days) {
        return Response.success(baseService.getExpiringConfigs(days));
    }

    @Operation(summary = "查询高频触发配置", description = "查询指定群组的高频触发配置")
    @GetMapping("/high-frequency/{groupId}")
    public Response<List<ZapierConfigResp>> getHighFrequencyConfigs(
            @Parameter(description = "群组ID", example = "1") @PathVariable Long groupId,
            @Parameter(description = "触发次数阈值", example = "100") @RequestParam(defaultValue = "100") Long threshold,
            @Parameter(description = "时间范围（小时）", example = "24") @RequestParam(defaultValue = "24") Integer hours) {
        return Response.success(baseService.getHighFrequencyConfigs(groupId, threshold, hours));
    }

    @Operation(summary = "查询配置健康状态", description = "查询指定配置的健康状态")
    @GetMapping("/{id}/health")
    public Response<Map<String, Object>> getConfigHealth(
            @Parameter(description = "配置ID", example = "1") @PathVariable Long id) {
        return Response.success(baseService.getConfigHealth(id));
    }

    @Operation(summary = "验证配置", description = "验证配置信息的有效性")
    @PostMapping("/validate")
    public Response<Map<String, Object>> validateConfig(
            @Parameter(description = "配置信息") @Valid @RequestBody ZapierConfigReq config) {
        return Response.success(baseService.validateConfig(config));
    }

    @Operation(summary = "获取支持的触发器类型", description = "获取系统支持的所有触发器类型")
    @GetMapping("/trigger-types")
    public Response<List<Map<String, Object>>> getSupportedTriggerTypes() {
        return Response.success(baseService.getSupportedTriggerTypes());
    }

    @Operation(summary = "获取数据映射模板", description = "获取指定触发器类型的数据映射模板")
    @GetMapping("/mapping-template")
    public Response<Map<String, Object>> getDataMappingTemplate(
            @Parameter(description = "触发器类型", example = "TRANSACTION_CREATED") @RequestParam String triggerType) {
        return Response.success(baseService.getDataMappingTemplate(triggerType));
    }

    @Operation(summary = "预览数据映射结果", description = "预览数据映射的结果")
    @PostMapping("/preview-mapping")
    public Response<Map<String, Object>> previewDataMapping(
            @Parameter(description = "配置信息") @Valid @RequestBody ZapierConfigReq config,
            @Parameter(description = "测试数据") @RequestParam Map<String, Object> testData) {
        return Response.success(baseService.previewDataMapping(config, testData));
    }
}
