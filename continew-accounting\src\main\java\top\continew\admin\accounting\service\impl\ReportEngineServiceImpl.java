package top.continew.admin.accounting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.accounting.mapper.ReportEngineMapper;
import top.continew.admin.accounting.model.entity.ReportTemplate;
import top.continew.admin.accounting.model.query.ReportTemplateQuery;
import top.continew.admin.accounting.model.req.DynamicReportGenerateReq;
import top.continew.admin.accounting.model.req.ReportTemplateCreateReq;
import top.continew.admin.accounting.model.req.ReportTemplateUpdateReq;
import top.continew.admin.accounting.model.resp.DynamicReportResp;
import top.continew.admin.accounting.model.resp.ReportTemplateDetailResp;
import top.continew.admin.accounting.model.resp.ReportTemplateResp;
import top.continew.admin.accounting.service.ReportEngineService;
import top.continew.admin.common.util.helper.LoginHelper;
import top.continew.admin.common.util.validate.CheckUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 报表引擎服务实现
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReportEngineServiceImpl implements ReportEngineService {

    private final ReportEngineMapper reportEngineMapper;

    // 异步任务状态缓存
    private final Map<String, DynamicReportResp> taskStatusCache = new ConcurrentHashMap<>();

    // ==================== 报表模板管理 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTemplate(ReportTemplateCreateReq createReq) {
        log.info("创建报表模板: {}", createReq.getTemplateName());

        // 参数验证
        CheckUtils.throwIfBlank(createReq.getTemplateName(), "模板名称不能为空");
        CheckUtils.throwIfBlank(createReq.getTemplateType(), "模板类型不能为空");
        CheckUtils.throwIfNull(createReq.getReportConfig(), "报表配置不能为空");
        CheckUtils.throwIfNull(createReq.getDataSourceConfig(), "数据源配置不能为空");

        // 检查模板名称是否重复
        Long groupId = LoginHelper.getGroupId();
        CheckUtils.throwIfTrue(
            reportEngineMapper.existsByNameAndGroup(createReq.getTemplateName(), groupId),
            "模板名称已存在"
        );

        // 构建实体
        ReportTemplate template = new ReportTemplate();
        BeanUtil.copyProperties(createReq, template);
        template.setGroupId(groupId);
        template.setTemplateStatus("DRAFT");
        template.setVersion("1.0");
        template.setEnabled(true);
        template.setIsPublic(false);
        template.setIsSystemTemplate(false);
        template.setUsageCount(0);
        template.setSuccessCount(0);
        template.setFailureCount(0);
        template.setRating(0.0);
        template.setRatingCount(0);
        template.setFavoriteCount(0);

        // 序列化配置对象
        template.setReportConfigJson(JSONUtil.toJsonStr(createReq.getReportConfig()));
        template.setDataSourceConfigJson(JSONUtil.toJsonStr(createReq.getDataSourceConfig()));
        template.setLayoutConfigJson(JSONUtil.toJsonStr(createReq.getLayoutConfig()));
        template.setChartConfigsJson(JSONUtil.toJsonStr(createReq.getChartConfigs()));
        template.setFilterConfigsJson(JSONUtil.toJsonStr(createReq.getFilterConfigs()));
        template.setExportConfigJson(JSONUtil.toJsonStr(createReq.getExportConfig()));
        template.setScheduleConfigJson(JSONUtil.toJsonStr(createReq.getScheduleConfig()));
        template.setPermissionConfigJson(JSONUtil.toJsonStr(createReq.getPermissionConfig()));

        // 设置标签
        if (CollUtil.isNotEmpty(createReq.getTags())) {
            template.setTagsJson(JSONUtil.toJsonStr(createReq.getTags()));
        }

        // 设置扩展属性
        if (CollUtil.isNotEmpty(createReq.getAttributes())) {
            template.setAttributesJson(JSONUtil.toJsonStr(createReq.getAttributes()));
        }

        // 保存到数据库
        reportEngineMapper.insert(template);

        // 创建初始版本记录
        createTemplateVersion(template.getTemplateId(), "初始版本");

        log.info("报表模板创建成功，模板ID: {}", template.getTemplateId());
        return template.getTemplateId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateTemplate(Long templateId, ReportTemplateUpdateReq updateReq) {
        log.info("更新报表模板: {}", templateId);

        // 参数验证
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        CheckUtils.throwIfNull(updateReq, "更新请求不能为空");

        // 检查模板是否存在
        ReportTemplate template = reportEngineMapper.selectById(templateId);
        CheckUtils.throwIfNull(template, "报表模板不存在");

        // 检查权限
        checkTemplatePermission(templateId, LoginHelper.getUserId(), "EDIT");

        // 检查模板名称是否重复（排除自己）
        if (StrUtil.isNotBlank(updateReq.getTemplateName()) && 
            !updateReq.getTemplateName().equals(template.getTemplateName())) {
            CheckUtils.throwIfTrue(
                reportEngineMapper.existsByNameAndGroupExcludeId(
                    updateReq.getTemplateName(), template.getGroupId(), templateId),
                "模板名称已存在"
            );
        }

        // 更新基本信息
        if (StrUtil.isNotBlank(updateReq.getTemplateName())) {
            template.setTemplateName(updateReq.getTemplateName());
        }
        if (StrUtil.isNotBlank(updateReq.getTemplateDescription())) {
            template.setTemplateDescription(updateReq.getTemplateDescription());
        }
        if (StrUtil.isNotBlank(updateReq.getTemplateType())) {
            template.setTemplateType(updateReq.getTemplateType());
        }

        // 更新配置信息
        if (updateReq.getReportConfig() != null) {
            template.setReportConfigJson(JSONUtil.toJsonStr(updateReq.getReportConfig()));
        }
        if (updateReq.getDataSourceConfig() != null) {
            template.setDataSourceConfigJson(JSONUtil.toJsonStr(updateReq.getDataSourceConfig()));
        }
        if (updateReq.getLayoutConfig() != null) {
            template.setLayoutConfigJson(JSONUtil.toJsonStr(updateReq.getLayoutConfig()));
        }
        if (updateReq.getChartConfigs() != null) {
            template.setChartConfigsJson(JSONUtil.toJsonStr(updateReq.getChartConfigs()));
        }
        if (updateReq.getFilterConfigs() != null) {
            template.setFilterConfigsJson(JSONUtil.toJsonStr(updateReq.getFilterConfigs()));
        }
        if (updateReq.getExportConfig() != null) {
            template.setExportConfigJson(JSONUtil.toJsonStr(updateReq.getExportConfig()));
        }
        if (updateReq.getScheduleConfig() != null) {
            template.setScheduleConfigJson(JSONUtil.toJsonStr(updateReq.getScheduleConfig()));
        }
        if (updateReq.getPermissionConfig() != null) {
            template.setPermissionConfigJson(JSONUtil.toJsonStr(updateReq.getPermissionConfig()));
        }

        // 更新标签
        if (updateReq.getTags() != null) {
            template.setTagsJson(JSONUtil.toJsonStr(updateReq.getTags()));
        }

        // 更新扩展属性
        if (updateReq.getAttributes() != null) {
            template.setAttributesJson(JSONUtil.toJsonStr(updateReq.getAttributes()));
        }

        // 更新版本号
        String[] versionParts = template.getVersion().split("\\.");
        int majorVersion = Integer.parseInt(versionParts[0]);
        int minorVersion = versionParts.length > 1 ? Integer.parseInt(versionParts[1]) : 0;
        template.setVersion(majorVersion + "." + (minorVersion + 1));

        // 保存更新
        int result = reportEngineMapper.updateById(template);

        // 创建版本记录
        if (result > 0) {
            String versionNote = StrUtil.isNotBlank(updateReq.getUpdateReason()) ? 
                updateReq.getUpdateReason() : "模板更新";
            createTemplateVersion(templateId, versionNote);
        }

        log.info("报表模板更新完成，模板ID: {}, 结果: {}", templateId, result > 0);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteTemplate(Long templateId) {
        log.info("删除报表模板: {}", templateId);

        // 参数验证
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");

        // 检查模板是否存在
        ReportTemplate template = reportEngineMapper.selectById(templateId);
        CheckUtils.throwIfNull(template, "报表模板不存在");

        // 检查权限
        checkTemplatePermission(templateId, LoginHelper.getUserId(), "DELETE");

        // 检查是否为系统模板
        CheckUtils.throwIfTrue(template.getIsSystemTemplate(), "系统模板不能删除");

        // 检查是否有正在运行的调度任务
        List<Map<String, Object>> schedules = listReportSchedules(templateId);
        boolean hasActiveSchedule = schedules.stream()
            .anyMatch(schedule -> "ACTIVE".equals(schedule.get("status")));
        CheckUtils.throwIfTrue(hasActiveSchedule, "存在活跃的调度任务，请先停止调度任务");

        // 软删除模板
        int result = reportEngineMapper.deleteById(templateId);

        // 删除相关数据
        if (result > 0) {
            // 删除版本历史
            reportEngineMapper.deleteTemplateVersions(templateId);
            // 删除权限记录
            reportEngineMapper.deleteTemplatePermissions(templateId);
            // 删除收藏记录
            reportEngineMapper.deleteTemplateFavorites(templateId);
            // 删除评价记录
            reportEngineMapper.deleteTemplateRatings(templateId);
            // 清除缓存
            clearTemplateCache(templateId);
        }

        log.info("报表模板删除完成，模板ID: {}, 结果: {}", templateId, result > 0);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteTemplates(List<Long> templateIds) {
        log.info("批量删除报表模板: {}", templateIds);

        CheckUtils.throwIfEmpty(templateIds, "模板ID列表不能为空");

        boolean allSuccess = true;
        for (Long templateId : templateIds) {
            try {
                deleteTemplate(templateId);
            } catch (Exception e) {
                log.error("删除模板失败，模板ID: {}", templateId, e);
                allSuccess = false;
            }
        }

        log.info("批量删除报表模板完成，成功: {}", allSuccess);
        return allSuccess;
    }

    @Override
    public ReportTemplateDetailResp getTemplateDetail(Long templateId) {
        log.debug("获取报表模板详情: {}", templateId);

        // 参数验证
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");

        // 查询模板基本信息
        ReportTemplate template = reportEngineMapper.selectById(templateId);
        CheckUtils.throwIfNull(template, "报表模板不存在");

        // 检查权限
        checkTemplatePermission(templateId, LoginHelper.getUserId(), "VIEW");

        // 构建响应对象
        ReportTemplateDetailResp resp = new ReportTemplateDetailResp();
        BeanUtil.copyProperties(template, resp);

        // 反序列化配置对象
        if (StrUtil.isNotBlank(template.getReportConfigJson())) {
            resp.setReportConfig(JSONUtil.toBean(template.getReportConfigJson(), 
                ReportTemplateCreateReq.ReportConfiguration.class));
        }
        if (StrUtil.isNotBlank(template.getDataSourceConfigJson())) {
            resp.setDataSourceConfig(JSONUtil.toBean(template.getDataSourceConfigJson(), 
                ReportTemplateCreateReq.DataSourceConfiguration.class));
        }
        if (StrUtil.isNotBlank(template.getLayoutConfigJson())) {
            resp.setLayoutConfig(JSONUtil.toBean(template.getLayoutConfigJson(), 
                ReportTemplateCreateReq.LayoutConfiguration.class));
        }
        if (StrUtil.isNotBlank(template.getChartConfigsJson())) {
            resp.setChartConfigs(JSONUtil.toList(template.getChartConfigsJson(), 
                ReportTemplateCreateReq.ChartConfiguration.class));
        }
        if (StrUtil.isNotBlank(template.getFilterConfigsJson())) {
            resp.setFilterConfigs(JSONUtil.toList(template.getFilterConfigsJson(), 
                ReportTemplateCreateReq.FilterConfiguration.class));
        }
        if (StrUtil.isNotBlank(template.getExportConfigJson())) {
            resp.setExportConfig(JSONUtil.toBean(template.getExportConfigJson(), 
                ReportTemplateCreateReq.ExportConfiguration.class));
        }
        if (StrUtil.isNotBlank(template.getScheduleConfigJson())) {
            resp.setScheduleConfig(JSONUtil.toBean(template.getScheduleConfigJson(), 
                ReportTemplateCreateReq.ScheduleConfiguration.class));
        }
        if (StrUtil.isNotBlank(template.getPermissionConfigJson())) {
            resp.setPermissionConfig(JSONUtil.toBean(template.getPermissionConfigJson(), 
                ReportTemplateCreateReq.PermissionConfiguration.class));
        }

        // 反序列化标签和属性
        if (StrUtil.isNotBlank(template.getTagsJson())) {
            resp.setTags(JSONUtil.toList(template.getTagsJson(), String.class));
        }
        if (StrUtil.isNotBlank(template.getAttributesJson())) {
            resp.setAttributes(JSONUtil.toBean(template.getAttributesJson(), Map.class));
        }

        // 获取统计信息
        resp.setUsageStats(getTemplateUsageStatistics(templateId));
        resp.setPerformanceStats(getTemplatePerformanceStatistics(templateId));

        // 获取权限信息
        resp.setPermissionInfo(getTemplatePermissionInfo(templateId));

        // 获取调度信息
        resp.setScheduleInfo(getTemplateScheduleInfo(templateId));

        // 获取版本历史
        resp.setVersionHistory(getTemplateVersionHistory(templateId));

        // 获取执行历史
        resp.setExecutionHistory(getTemplateExecutionHistory(templateId, 10));

        // 获取用户评价
        resp.setUserRatings(getTemplateRatings(templateId));

        log.debug("获取报表模板详情完成: {}", templateId);
        return resp;
    }

    @Override
    public IPage<ReportTemplateResp> pageTemplates(ReportTemplateQuery query) {
        log.debug("分页查询报表模板: {}", query);

        // 构建查询条件
        QueryWrapper<ReportTemplate> queryWrapper = buildTemplateQueryWrapper(query);

        // 分页查询
        Page<ReportTemplate> page = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<ReportTemplate> templatePage = reportEngineMapper.selectPage(page, queryWrapper);

        // 转换响应对象
        IPage<ReportTemplateResp> respPage = templatePage.convert(this::convertToTemplateResp);

        log.debug("分页查询报表模板完成，总数: {}", respPage.getTotal());
        return respPage;
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 构建模板查询条件
     */
    private QueryWrapper<ReportTemplate> buildTemplateQueryWrapper(ReportTemplateQuery query) {
        QueryWrapper<ReportTemplate> queryWrapper = new QueryWrapper<>();

        // 群组过滤
        Long groupId = LoginHelper.getGroupId();
        queryWrapper.eq("group_id", groupId);

        // 基本条件
        if (StrUtil.isNotBlank(query.getTemplateName())) {
            queryWrapper.like("template_name", query.getTemplateName());
        }
        if (StrUtil.isNotBlank(query.getTemplateType())) {
            queryWrapper.eq("template_type", query.getTemplateType());
        }
        if (StrUtil.isNotBlank(query.getTemplateStatus())) {
            queryWrapper.eq("template_status", query.getTemplateStatus());
        }
        if (query.getEnabled() != null) {
            queryWrapper.eq("enabled", query.getEnabled());
        }
        if (query.getIsPublic() != null) {
            queryWrapper.eq("is_public", query.getIsPublic());
        }
        if (query.getIsSystemTemplate() != null) {
            queryWrapper.eq("is_system_template", query.getIsSystemTemplate());
        }

        // 时间范围
        if (query.getCreateTimeStart() != null) {
            queryWrapper.ge("create_time", query.getCreateTimeStart());
        }
        if (query.getCreateTimeEnd() != null) {
            queryWrapper.le("create_time", query.getCreateTimeEnd());
        }

        // 创建人过滤
        if (query.getCreatedBy() != null) {
            queryWrapper.eq("created_by", query.getCreatedBy());
        }

        // 使用统计过滤
        if (query.getMinUsageCount() != null) {
            queryWrapper.ge("usage_count", query.getMinUsageCount());
        }
        if (query.getMaxUsageCount() != null) {
            queryWrapper.le("usage_count", query.getMaxUsageCount());
        }

        // 评分过滤
        if (query.getMinRating() != null) {
            queryWrapper.ge("rating", query.getMinRating());
        }

        // 标签过滤
        if (CollUtil.isNotEmpty(query.getTags())) {
            for (String tag : query.getTags()) {
                queryWrapper.like("tags_json", "\"" + tag + "\"");
            }
        }

        // 关键词搜索
        if (StrUtil.isNotBlank(query.getKeyword())) {
            queryWrapper.and(wrapper -> wrapper
                .like("template_name", query.getKeyword())
                .or()
                .like("template_description", query.getKeyword())
                .or()
                .like("tags_json", query.getKeyword())
            );
        }

        // 排序
        if (StrUtil.isNotBlank(query.getSortField())) {
            if ("DESC".equalsIgnoreCase(query.getSortOrder())) {
                queryWrapper.orderByDesc(query.getSortField());
            } else {
                queryWrapper.orderByAsc(query.getSortField());
            }
        } else {
            queryWrapper.orderByDesc("update_time");
        }

        return queryWrapper;
    }

    /**
     * 转换为模板响应对象
     */
    private ReportTemplateResp convertToTemplateResp(ReportTemplate template) {
        ReportTemplateResp resp = new ReportTemplateResp();
        BeanUtil.copyProperties(template, resp);

        // 反序列化标签
        if (StrUtil.isNotBlank(template.getTagsJson())) {
            resp.setTags(JSONUtil.toList(template.getTagsJson(), String.class));
        }

        // 反序列化扩展属性
        if (StrUtil.isNotBlank(template.getAttributesJson())) {
            resp.setAttributes(JSONUtil.toBean(template.getAttributesJson(), Map.class));
        }

        // 设置权限信息
        resp.setPermissionInfo(getTemplatePermissionInfo(template.getTemplateId()));

        // 设置调度信息
        resp.setScheduleInfo(getTemplateScheduleInfo(template.getTemplateId()));

        // 设置当前用户是否收藏
        resp.setIsFavorite(checkUserFavoriteTemplate(template.getTemplateId(), LoginHelper.getUserId()));

        return resp;
    }

    // ==================== 待实现的辅助方法 ====================

    private ReportTemplateDetailResp.UsageStatistics getTemplateUsageStatistics(Long templateId) {
        // TODO: 实现使用统计获取逻辑
        return new ReportTemplateDetailResp.UsageStatistics();
    }

    private ReportTemplateDetailResp.PerformanceStatistics getTemplatePerformanceStatistics(Long templateId) {
        // TODO: 实现性能统计获取逻辑
        return new ReportTemplateDetailResp.PerformanceStatistics();
    }

    private ReportTemplateResp.PermissionInfo getTemplatePermissionInfo(Long templateId) {
        // TODO: 实现权限信息获取逻辑
        return new ReportTemplateResp.PermissionInfo();
    }

    private ReportTemplateResp.ScheduleInfo getTemplateScheduleInfo(Long templateId) {
        // TODO: 实现调度信息获取逻辑
        return new ReportTemplateResp.ScheduleInfo();
    }

    private List<ReportTemplateResp.ExecutionRecord> getTemplateExecutionHistory(Long templateId, int limit) {
        // TODO: 实现执行历史获取逻辑
        return new ArrayList<>();
    }

    private Boolean checkUserFavoriteTemplate(Long templateId, Long userId) {
        // TODO: 实现用户收藏检查逻辑
        return false;
    }

    // ==================== 其他接口方法的占位实现 ====================

    @Override
    public List<ReportTemplateResp> listTemplates(ReportTemplateQuery query) {
        log.debug("查询报表模板列表: {}", query);

        // 构建查询条件
        QueryWrapper<ReportTemplate> queryWrapper = buildTemplateQueryWrapper(query);

        // 查询模板列表
        List<ReportTemplate> templates = reportEngineMapper.selectList(queryWrapper);

        // 转换响应对象
        List<ReportTemplateResp> respList = templates.stream()
            .map(this::convertToTemplateResp)
            .toList();

        log.debug("查询报表模板列表完成，数量: {}", respList.size());
        return respList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long copyTemplate(Long templateId, String newName) {
        log.info("复制报表模板: {} -> {}", templateId, newName);

        // 参数验证
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        CheckUtils.throwIfBlank(newName, "新模板名称不能为空");

        // 获取源模板
        ReportTemplate sourceTemplate = reportEngineMapper.selectById(templateId);
        CheckUtils.throwIfNull(sourceTemplate, "源模板不存在");

        // 检查权限
        checkTemplatePermission(templateId, LoginHelper.getUserId(), "VIEW");

        // 检查新名称是否重复
        Long groupId = LoginHelper.getGroupId();
        CheckUtils.throwIfTrue(
            reportEngineMapper.existsByNameAndGroup(newName, groupId),
            "模板名称已存在"
        );

        // 创建新模板
        ReportTemplate newTemplate = new ReportTemplate();
        BeanUtil.copyProperties(sourceTemplate, newTemplate);
        newTemplate.setTemplateId(null);
        newTemplate.setTemplateName(newName);
        newTemplate.setTemplateDescription("复制自: " + sourceTemplate.getTemplateName());
        newTemplate.setTemplateStatus("DRAFT");
        newTemplate.setVersion("1.0");
        newTemplate.setIsPublic(false);
        newTemplate.setIsSystemTemplate(false);
        newTemplate.setUsageCount(0);
        newTemplate.setSuccessCount(0);
        newTemplate.setFailureCount(0);
        newTemplate.setRating(0.0);
        newTemplate.setRatingCount(0);
        newTemplate.setFavoriteCount(0);
        newTemplate.setLastUsedTime(null);
        newTemplate.setLastUsedBy(null);
        newTemplate.setAvgExecutionTime(null);
        newTemplate.setMaxExecutionTime(null);
        newTemplate.setMinExecutionTime(null);
        newTemplate.setAvgDataVolume(null);
        newTemplate.setMaxDataVolume(null);
        newTemplate.setMinDataVolume(null);

        // 保存新模板
        reportEngineMapper.insert(newTemplate);

        // 创建初始版本记录
        createTemplateVersion(newTemplate.getTemplateId(), "从模板复制创建");

        log.info("报表模板复制成功，新模板ID: {}", newTemplate.getTemplateId());
        return newTemplate.getTemplateId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean toggleTemplateStatus(Long templateId, Boolean enabled) {
        log.info("切换报表模板状态: {} -> {}", templateId, enabled);

        // 参数验证
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        CheckUtils.throwIfNull(enabled, "启用状态不能为空");

        // 检查模板是否存在
        ReportTemplate template = reportEngineMapper.selectById(templateId);
        CheckUtils.throwIfNull(template, "报表模板不存在");

        // 检查权限
        checkTemplatePermission(templateId, LoginHelper.getUserId(), "EDIT");

        // 更新状态
        template.setEnabled(enabled);
        int result = reportEngineMapper.updateById(template);

        log.info("报表模板状态切换完成，模板ID: {}, 结果: {}", templateId, result > 0);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean publishTemplate(Long templateId) {
        log.info("发布报表模板: {}", templateId);

        // 参数验证
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");

        // 检查模板是否存在
        ReportTemplate template = reportEngineMapper.selectById(templateId);
        CheckUtils.throwIfNull(template, "报表模板不存在");

        // 检查权限
        checkTemplatePermission(templateId, LoginHelper.getUserId(), "PUBLISH");

        // 检查模板状态
        CheckUtils.throwIfTrue(!"DRAFT".equals(template.getTemplateStatus()), "只有草稿状态的模板才能发布");
        CheckUtils.throwIfTrue(!template.getEnabled(), "模板必须启用后才能发布");

        // 更新状态
        template.setTemplateStatus("PUBLISHED");
        template.setIsPublic(true);
        int result = reportEngineMapper.updateById(template);

        // 创建版本记录
        if (result > 0) {
            createTemplateVersion(templateId, "模板发布");
        }

        log.info("报表模板发布完成，模板ID: {}, 结果: {}", templateId, result > 0);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean unpublishTemplate(Long templateId) {
        log.info("撤销发布报表模板: {}", templateId);

        // 参数验证
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");

        // 检查模板是否存在
        ReportTemplate template = reportEngineMapper.selectById(templateId);
        CheckUtils.throwIfNull(template, "报表模板不存在");

        // 检查权限
        checkTemplatePermission(templateId, LoginHelper.getUserId(), "PUBLISH");

        // 检查模板状态
        CheckUtils.throwIfTrue(!"PUBLISHED".equals(template.getTemplateStatus()), "只有已发布的模板才能撤销发布");

        // 更新状态
        template.setTemplateStatus("DRAFT");
        template.setIsPublic(false);
        int result = reportEngineMapper.updateById(template);

        // 创建版本记录
        if (result > 0) {
            createTemplateVersion(templateId, "撤销发布");
        }

        log.info("报表模板撤销发布完成，模板ID: {}, 结果: {}", templateId, result > 0);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createTemplateVersion(Long templateId, String versionNote) {
        log.debug("创建模板版本: {} - {}", templateId, versionNote);

        // 参数验证
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        CheckUtils.throwIfBlank(versionNote, "版本说明不能为空");

        // 获取当前模板
        ReportTemplate template = reportEngineMapper.selectById(templateId);
        CheckUtils.throwIfNull(template, "报表模板不存在");

        // 生成新版本号
        String currentVersion = template.getVersion();
        String[] versionParts = currentVersion.split("\\.");
        int majorVersion = Integer.parseInt(versionParts[0]);
        int minorVersion = versionParts.length > 1 ? Integer.parseInt(versionParts[1]) : 0;
        String newVersion = majorVersion + "." + (minorVersion + 1);

        // 创建版本记录
        reportEngineMapper.insertTemplateVersion(
            templateId,
            newVersion,
            versionNote,
            JSONUtil.toJsonStr(template),
            "UPDATE",
            LoginHelper.getUserId()
        );

        log.debug("模板版本创建完成: {} - {}", templateId, newVersion);
        return newVersion;
    }
}
