version: '3'
services:
  mysql:
    image: mysql:8.0.42
    restart: always
    container_name: mysql
    ports:
      - '3306:3306'
    environment:
      TZ: Asia/Shanghai
      MYSQL_ROOT_PASSWORD: 你的root用户密码
      # 初始化数据库（后续的初始化 SQL 会在这个库执行）
      MYSQL_DATABASE: continew_admin
      #MYSQL_USER: 你的数据库用户名
      #MYSQL_PASSWORD: 你的数据库密码
    volumes:
      - /docker/mysql/conf/:/etc/mysql/conf.d/
      - /docker/mysql/data/:/var/lib/mysql/
    command:
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --explicit_defaults_for_timestamp=true
      --lower_case_table_names=1
#  postgresql:
#    image: postgres:14.2
#    restart: always
#    container_name: postgresql
#    ports:
#      - '5432:5432'
#    environment:
#      TZ: Asia/Shanghai
#      POSTGRES_USER: 你的用户名
#      POSTGRES_PASSWORD: 你的用户密码
#      POSTGRES_DB: continew_admin
#    volumes:
#      - /docker/postgresql/data/:/var/lib/postgresql/data/
  redis:
    image: redis:7.2.8
    restart: always
    container_name: redis
    ports:
      - '6379:6379'
    environment:
      TZ: Asia/Shanghai
    volumes:
      - /docker/redis/conf/redis.conf:/usr/local/redis/config/redis.conf
      - /docker/redis/data/:/data/
      - /docker/redis/logs/:/logs/
    command: 'redis-server /usr/local/redis/config/redis.conf --appendonly yes --requirepass 你的 Redis 密码'
  continew-server:
    build: ./continew-admin
    restart: always
    container_name: continew-server
    ports:
      - '18000:18000'
      - '1789:1789'
    environment:
      TZ: Asia/Shanghai
      DB_HOST: **********
      DB_PORT: 3306
      DB_USER: 你的数据库用户名
      DB_PWD: 你的数据库密码
      DB_NAME: continew_admin
      REDIS_HOST: **********
      REDIS_PORT: 6379
      REDIS_PWD: 你的 Redis 密码
      REDIS_DB: 0
      SCHEDULE_HOST: **********
      SCHEDULE_PORT: 1788
      SCHEDULE_TOKEN: 任务调度服务端 Token
    volumes:
      - /docker/continew-admin/config/:/app/config/
      - /docker/continew-admin/data/file/:/app/data/file/
      - /docker/continew-admin/logs/:/app/logs/
      - /docker/continew-admin/lib/:/app/lib/
    depends_on:
      - redis
      - mysql
  continew-web:
    image: nginx:1.27.0
    restart: always
    container_name: continew-web
    ports:
      - '80:80'
      - '443:443'
    environment:
      TZ: Asia/Shanghai
    volumes:
      - /docker/nginx/conf/nginx.conf:/etc/nginx/nginx.conf
      - /docker/nginx/cert/:/etc/nginx/cert/
      - /docker/nginx/logs/:/var/log/nginx/
      # 前端页面目录
      - /docker/continew-admin/web/:/usr/share/nginx/html/
  schedule-server:
    build: ./schedule-server
    restart: always
    container_name: schedule-server
    ports:
      - '18001:18001'
      - '1788:1788'
    environment:
      TZ: Asia/Shanghai
      DB_HOST: **********
      DB_PORT: 3306
      DB_USER: 你的数据库用户名
      DB_PWD: 你的数据库密码
      DB_NAME: continew_admin_job
    volumes:
      - /docker/schedule-server/logs/:/app/logs/
    depends_on:
      - mysql
