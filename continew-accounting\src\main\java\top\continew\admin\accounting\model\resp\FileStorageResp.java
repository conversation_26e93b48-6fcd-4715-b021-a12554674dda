package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.accounting.enums.FileAccessTypeEnum;
import top.continew.admin.accounting.enums.FileProcessStatusEnum;
import top.continew.admin.accounting.enums.FileStorageTypeEnum;
import top.continew.admin.system.enums.FileTypeEnum;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 文件存储响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "文件存储响应")
public class FileStorageResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 文件ID
     */
    @Schema(description = "文件ID", example = "1")
    private Long id;

    /**
     * 文件名称
     */
    @Schema(description = "文件名称", example = "receipt_20250101_001.jpg")
    private String fileName;

    /**
     * 原始文件名
     */
    @Schema(description = "原始文件名", example = "receipt.jpg")
    private String originalFileName;

    /**
     * 文件路径
     */
    @Schema(description = "文件路径", example = "/accounting/receipts/2025/01/01/receipt_20250101_001.jpg")
    private String filePath;

    /**
     * 文件URL
     */
    @Schema(description = "文件URL", example = "https://example.com/files/receipt_20250101_001.jpg")
    private String fileUrl;

    /**
     * 文件大小（字节）
     */
    @Schema(description = "文件大小（字节）", example = "1048576")
    private Long fileSize;

    /**
     * 文件大小（格式化）
     */
    @Schema(description = "文件大小（格式化）", example = "1.0 MB")
    private String fileSizeFormatted;

    /**
     * 文件类型
     */
    @Schema(description = "文件类型", example = "IMAGE")
    private FileTypeEnum fileType;

    /**
     * 文件扩展名
     */
    @Schema(description = "文件扩展名", example = "jpg")
    private String fileExtension;

    /**
     * MIME类型
     */
    @Schema(description = "MIME类型", example = "image/jpeg")
    private String mimeType;

    /**
     * 存储类型
     */
    @Schema(description = "存储类型", example = "ALIYUN_OSS")
    private FileStorageTypeEnum storageType;

    /**
     * 访问权限类型
     */
    @Schema(description = "访问权限类型", example = "PRIVATE")
    private FileAccessTypeEnum accessType;

    /**
     * CDN URL
     */
    @Schema(description = "CDN URL", example = "https://cdn.example.com/files/receipt_20250101_001.jpg")
    private String cdnUrl;

    /**
     * 缩略图URL
     */
    @Schema(description = "缩略图URL", example = "https://example.com/thumbnails/receipt_20250101_001_thumb.jpg")
    private String thumbnailUrl;

    /**
     * 预览URL
     */
    @Schema(description = "预览URL", example = "https://example.com/preview/receipt_20250101_001.jpg")
    private String previewUrl;

    /**
     * 下载次数
     */
    @Schema(description = "下载次数", example = "5")
    private Long downloadCount;

    /**
     * 访问次数
     */
    @Schema(description = "访问次数", example = "10")
    private Long accessCount;

    /**
     * 最后访问时间
     */
    @Schema(description = "最后访问时间")
    private LocalDateTime lastAccessTime;

    /**
     * 处理状态
     */
    @Schema(description = "处理状态", example = "COMPLETED")
    private FileProcessStatusEnum processStatus;

    /**
     * 文件标签
     */
    @Schema(description = "文件标签", example = "receipt,expense")
    private String fileTags;

    /**
     * 文件描述
     */
    @Schema(description = "文件描述", example = "收据图片")
    private String fileDescription;

    /**
     * 是否为临时文件
     */
    @Schema(description = "是否为临时文件", example = "false")
    private Boolean isTemporary;

    /**
     * 过期时间
     */
    @Schema(description = "过期时间")
    private LocalDateTime expireTime;

    /**
     * 关联业务ID
     */
    @Schema(description = "关联业务ID", example = "123456")
    private String businessId;

    /**
     * 关联业务类型
     */
    @Schema(description = "关联业务类型", example = "TRANSACTION")
    private String businessType;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 上传用户ID
     */
    @Schema(description = "上传用户ID", example = "1")
    private Long uploadUserId;

    /**
     * 上传用户名
     */
    @Schema(description = "上传用户名", example = "张三")
    private String uploadUserName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

}
