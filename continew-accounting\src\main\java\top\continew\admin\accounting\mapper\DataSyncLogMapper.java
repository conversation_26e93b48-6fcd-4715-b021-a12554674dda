package top.continew.admin.accounting.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.accounting.model.entity.DataSyncLogDO;
import top.continew.admin.accounting.model.query.DataSyncLogQuery;
import top.continew.admin.accounting.model.resp.DataSyncLogResp;
import top.continew.starter.extension.crud.mapper.BaseMapper;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 数据同步日志 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface DataSyncLogMapper extends BaseMapper<DataSyncLogDO> {

    /**
     * 分页查询
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<DataSyncLogResp> selectPageList(Page<DataSyncLogResp> page, @Param("query") DataSyncLogQuery query);

    /**
     * 根据同步ID查询日志
     *
     * @param syncId 同步ID
     * @return 日志信息
     */
    DataSyncLogDO selectBySyncId(@Param("syncId") String syncId);

    /**
     * 根据配置ID查询最近的日志
     *
     * @param configId 配置ID
     * @param limit    限制数量
     * @return 日志列表
     */
    List<DataSyncLogDO> selectRecentByConfigId(@Param("configId") Long configId, @Param("limit") Integer limit);

    /**
     * 根据配置ID查询最后一次成功的日志
     *
     * @param configId 配置ID
     * @return 日志信息
     */
    DataSyncLogDO selectLastSuccessByConfigId(@Param("configId") Long configId);

    /**
     * 查询失败的日志
     *
     * @param configId 配置ID
     * @param hours    小时数
     * @return 日志列表
     */
    List<DataSyncLogDO> selectFailedLogs(@Param("configId") Long configId, @Param("hours") Integer hours);

    /**
     * 查询运行中的同步
     *
     * @param configId 配置ID
     * @return 日志列表
     */
    List<DataSyncLogDO> selectRunningSync(@Param("configId") Long configId);

    /**
     * 更新同步状态
     *
     * @param syncId    同步ID
     * @param status    状态
     * @param endTime   结束时间
     * @param duration  执行时长
     * @param errorMsg  错误信息
     * @param errorCode 错误代码
     */
    void updateSyncStatus(@Param("syncId") String syncId,
                          @Param("status") String status,
                          @Param("endTime") LocalDateTime endTime,
                          @Param("duration") Long duration,
                          @Param("errorMsg") String errorMsg,
                          @Param("errorCode") String errorCode);

    /**
     * 更新同步结果
     *
     * @param syncId           同步ID
     * @param recordsProcessed 处理记录数
     * @param recordsSuccess   成功记录数
     * @param recordsFailed    失败记录数
     * @param recordsSkipped   跳过记录数
     * @param recordsConflict  冲突记录数
     */
    void updateSyncResult(@Param("syncId") String syncId,
                          @Param("recordsProcessed") Integer recordsProcessed,
                          @Param("recordsSuccess") Integer recordsSuccess,
                          @Param("recordsFailed") Integer recordsFailed,
                          @Param("recordsSkipped") Integer recordsSkipped,
                          @Param("recordsConflict") Integer recordsConflict);

    /**
     * 更新同步详情
     *
     * @param syncId      同步ID
     * @param syncDetails 同步详情JSON
     * @param errorDetails 错误详情JSON
     * @param conflictDetails 冲突详情JSON
     * @param performanceMetrics 性能指标JSON
     */
    void updateSyncDetails(@Param("syncId") String syncId,
                           @Param("syncDetails") String syncDetails,
                           @Param("errorDetails") String errorDetails,
                           @Param("conflictDetails") String conflictDetails,
                           @Param("performanceMetrics") String performanceMetrics);

    /**
     * 删除过期日志
     *
     * @param retentionDays 保留天数
     * @return 删除数量
     */
    Integer deleteExpiredLogs(@Param("retentionDays") Integer retentionDays);

    /**
     * 批量删除日志
     *
     * @param logIds 日志ID列表
     * @return 删除数量
     */
    Integer batchDeleteLogs(@Param("logIds") List<Long> logIds);

    /**
     * 查询日志统计
     *
     * @param configId  配置ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 统计信息
     */
    Map<String, Object> selectLogStatistics(@Param("configId") Long configId,
                                             @Param("startDate") String startDate,
                                             @Param("endDate") String endDate);

    /**
     * 查询同步趋势
     *
     * @param configId  配置ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param groupBy   分组方式（DAY/HOUR）
     * @return 趋势数据
     */
    List<Map<String, Object>> selectSyncTrend(@Param("configId") Long configId,
                                               @Param("startDate") String startDate,
                                               @Param("endDate") String endDate,
                                               @Param("groupBy") String groupBy);

    /**
     * 查询错误分析
     *
     * @param configId  配置ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 错误分析
     */
    List<Map<String, Object>> selectErrorAnalysis(@Param("configId") Long configId,
                                                   @Param("startDate") String startDate,
                                                   @Param("endDate") String endDate);

    /**
     * 查询性能分析
     *
     * @param configId  配置ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 性能分析
     */
    Map<String, Object> selectPerformanceAnalysis(@Param("configId") Long configId,
                                                   @Param("startDate") String startDate,
                                                   @Param("endDate") String endDate);

    /**
     * 查询冲突分析
     *
     * @param configId  配置ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 冲突分析
     */
    List<Map<String, Object>> selectConflictAnalysis(@Param("configId") Long configId,
                                                      @Param("startDate") String startDate,
                                                      @Param("endDate") String endDate);

    /**
     * 查询重试分析
     *
     * @param configId  配置ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 重试分析
     */
    Map<String, Object> selectRetryAnalysis(@Param("configId") Long configId,
                                             @Param("startDate") String startDate,
                                             @Param("endDate") String endDate);

    /**
     * 查询数据量分析
     *
     * @param configId  配置ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 数据量分析
     */
    Map<String, Object> selectDataVolumeAnalysis(@Param("configId") Long configId,
                                                  @Param("startDate") String startDate,
                                                  @Param("endDate") String endDate);

    /**
     * 查询同步报告数据
     *
     * @param configId  配置ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 报告数据
     */
    Map<String, Object> selectSyncReportData(@Param("configId") Long configId,
                                              @Param("startDate") String startDate,
                                              @Param("endDate") String endDate);
}
