<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <!--
        下方 parent 为 ContiNew Starter（Continue New Starter）。
        ContiNew Starter 基于"约定优于配置"的理念，
        再次精简常规配置，提供一个更为完整的配置解决方案，帮助开发人员更加快速的集成常用第三方库或工具到 Spring Boot Web 应用程序中。
        ContiNew Starter 包含了一系列经过企业实践优化的依赖包（如 MyBatis-Plus、SaToken），
        可轻松集成到应用中，为开发人员减少手动引入依赖及配置的麻烦，为 Spring Boot Web 项目的灵活快速构建提供支持。
    -->
    <parent>
        <groupId>top.continew.starter</groupId>
        <artifactId>continew-starter</artifactId>
        <version>2.13.4</version>
    </parent>

    <groupId>top.continew.admin</groupId>
    <artifactId>continew-admin</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <name>${project.artifactId}</name>
    <description>ContiNew Admin（Continue New Admin）持续迭代优化的前后端分离中后台管理系统框架，开箱即用，持续提供舒适的开发体验。</description>
    <url>https://github.com/continew-org/continew-admin</url>

    <modules>
        <module>continew-server</module>
        <module>continew-system</module>
        <module>continew-plugin</module>
        <module>continew-common</module>
        <module>continew-extension</module>
        <module>continew-accounting</module>
        <module>continew-bot</module>
        <module>continew-analytics</module>
        <module>continew-integration</module>
        <module>continew-subscription</module>
    </modules>

    <properties>
        <!-- 项目版本号 -->
        <revision>4.0.0</revision>
    </properties>

    <!-- 全局依赖版本管理 -->
    <dependencyManagement>
        <dependencies>
            <!-- API 模块（存放 Controller 层代码，打包部署的模块） -->
            <dependency>
                <groupId>top.continew.admin</groupId>
                <artifactId>continew-server</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 系统管理模块（存放系统管理模块相关功能，例如：部门管理、角色管理、用户管理等） -->
            <dependency>
                <groupId>top.continew.admin</groupId>
                <artifactId>continew-system</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 公共模块（存放公共工具类，公共配置等） -->
            <dependency>
                <groupId>top.continew.admin</groupId>
                <artifactId>continew-common</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 能力开放插件（后续会改造为独立插件） -->
            <dependency>
                <groupId>top.continew.admin</groupId>
                <artifactId>continew-plugin-open</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 租户插件（后续会改造为独立插件） -->
            <dependency>
                <groupId>top.continew.admin</groupId>
                <artifactId>continew-plugin-tenant</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 记账核心模块 -->
            <dependency>
                <groupId>top.continew.admin</groupId>
                <artifactId>continew-accounting</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 机器人集成模块 -->
            <dependency>
                <groupId>top.continew.admin</groupId>
                <artifactId>continew-bot</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 数据分析模块 -->
            <dependency>
                <groupId>top.continew.admin</groupId>
                <artifactId>continew-analytics</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 第三方集成模块 -->
            <dependency>
                <groupId>top.continew.admin</groupId>
                <artifactId>continew-integration</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 订阅管理模块 -->
            <dependency>
                <groupId>top.continew.admin</groupId>
                <artifactId>continew-subscription</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 任务调度插件（后续会改造为独立插件） -->
            <dependency>
                <groupId>top.continew.admin</groupId>
                <artifactId>continew-plugin-schedule</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 代码生成器插件（后续会改造为独立插件） -->
            <dependency>
                <groupId>top.continew.admin</groupId>
                <artifactId>continew-plugin-generator</artifactId>
                <version>${revision}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- Hutool（小而全的 Java 工具类库，通过静态方法封装，降低相关 API 的学习成本，提高工作效率，使 Java 拥有函数式语言般的优雅，让 Java 语言也可以"甜甜的"） -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <!-- Lombok（在 Java 开发过程中用注解的方式，简化了 JavaBean 的编写，避免了冗余和样板式代码，让编写的类更加简洁） -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional> <!-- 表示依赖不会被传递 -->
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- 编译插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <compilerArgument>-parameters</compilerArgument>
                </configuration>
            </plugin>
            <!-- 单元测试相关插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <!-- 跳过单元测试 -->
                    <skip>true</skip>
                </configuration>
            </plugin>
            <!-- 代码格式化插件 -->
            <plugin>
                <groupId>com.diffplug.spotless</groupId>
                <artifactId>spotless-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>apply</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <java>
                        <removeUnusedImports/>
                        <eclipse>
                            <file>.style/p3c-codestyle.xml</file>
                        </eclipse>
                        <licenseHeader>
                            <file>.style/license-header</file>
                        </licenseHeader>
                    </java>
                </configuration>
            </plugin>
            <!-- 统一版本号插件 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten-clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <!-- Sonar 代码质量分析 -->
        <profile>
            <id>sonar</id>
            <properties>
                <sonar.host.url>https://sonarcloud.io</sonar.host.url>
                <sonar.organization>charles7c</sonar.organization>
                <sonar.projectKey>Charles7c_continew-admin</sonar.projectKey>
                <sonar.moduleKey>${project.groupId}:${project.artifactId}</sonar.moduleKey>
            </properties>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.sonarsource.scanner.maven</groupId>
                        <artifactId>sonar-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sonar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <!-- 依赖仓库配置 -->
    <repositories>
        <repository>
            <id>huawei-mirror</id>
            <name>HuaweiCloud Mirror</name>
            <url>https://mirrors.huaweicloud.com/repository/maven/</url>
        </repository>
        <repository>
            <id>ali-mirror</id>
            <name>AliYun Mirror</name>
            <url>https://maven.aliyun.com/repository/public/</url>
        </repository>
    </repositories>

    <!-- 插件仓库配置 -->
    <pluginRepositories>
        <pluginRepository>
            <id>huawei-mirror</id>
            <name>HuaweiCloud Mirror</name>
            <url>https://mirrors.huaweicloud.com/repository/maven/</url>
        </pluginRepository>
        <pluginRepository>
            <id>ali-mirror</id>
            <name>AliYun Mirror</name>
            <url>https://maven.aliyun.com/repository/public/</url>
        </pluginRepository>
    </pluginRepositories>
</project>
