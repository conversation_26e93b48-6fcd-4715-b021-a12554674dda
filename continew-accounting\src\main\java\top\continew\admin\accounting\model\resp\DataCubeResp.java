package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 数据立方体响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "数据立方体响应")
public class DataCubeResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 立方体ID
     */
    @Schema(description = "立方体ID", example = "cube_123456")
    private String cubeId;

    /**
     * 立方体名称
     */
    @Schema(description = "立方体名称", example = "财务数据立方体")
    private String cubeName;

    /**
     * 维度列表
     */
    @Schema(description = "维度列表")
    private List<CubeDimension> dimensions;

    /**
     * 度量列表
     */
    @Schema(description = "度量列表")
    private List<CubeMeasure> measures;

    /**
     * 数据单元格
     */
    @Schema(description = "数据单元格")
    private List<DataCell> dataCells;

    /**
     * 聚合级别
     */
    @Schema(description = "聚合级别")
    private Map<String, Integer> aggregationLevels;

    /**
     * 元数据
     */
    @Schema(description = "元数据")
    private CubeMetadata metadata;

    /**
     * 生成时间
     */
    @Schema(description = "生成时间")
    private LocalDateTime generatedAt;

    /**
     * 立方体维度
     */
    @Data
    @Schema(description = "立方体维度")
    public static class CubeDimension implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 维度名称
         */
        @Schema(description = "维度名称", example = "time")
        private String name;

        /**
         * 维度显示名称
         */
        @Schema(description = "维度显示名称", example = "时间")
        private String displayName;

        /**
         * 维度类型
         */
        @Schema(description = "维度类型", example = "TIME")
        private String type;

        /**
         * 层次结构
         */
        @Schema(description = "层次结构")
        private List<String> hierarchy;

        /**
         * 成员列表
         */
        @Schema(description = "成员列表")
        private List<DimensionMember> members;
    }

    /**
     * 立方体度量
     */
    @Data
    @Schema(description = "立方体度量")
    public static class CubeMeasure implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 度量名称
         */
        @Schema(description = "度量名称", example = "total_amount")
        private String name;

        /**
         * 度量显示名称
         */
        @Schema(description = "度量显示名称", example = "总金额")
        private String displayName;

        /**
         * 聚合函数
         */
        @Schema(description = "聚合函数", example = "SUM")
        private String aggregationFunction;

        /**
         * 数据类型
         */
        @Schema(description = "数据类型", example = "DECIMAL")
        private String dataType;

        /**
         * 格式化
         */
        @Schema(description = "格式化", example = "#,##0.00")
        private String format;
    }

    /**
     * 维度成员
     */
    @Data
    @Schema(description = "维度成员")
    public static class DimensionMember implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 成员键
         */
        @Schema(description = "成员键", example = "2024-01")
        private String key;

        /**
         * 成员名称
         */
        @Schema(description = "成员名称", example = "2024年1月")
        private String name;

        /**
         * 父成员
         */
        @Schema(description = "父成员", example = "2024")
        private String parent;

        /**
         * 级别
         */
        @Schema(description = "级别", example = "2")
        private Integer level;

        /**
         * 排序顺序
         */
        @Schema(description = "排序顺序", example = "1")
        private Integer sortOrder;
    }

    /**
     * 数据单元格
     */
    @Data
    @Schema(description = "数据单元格")
    public static class DataCell implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 坐标
         */
        @Schema(description = "坐标")
        private Map<String, String> coordinates;

        /**
         * 值
         */
        @Schema(description = "值", example = "1250.50")
        private BigDecimal value;

        /**
         * 格式化值
         */
        @Schema(description = "格式化值", example = "1,250.50")
        private String formattedValue;

        /**
         * 是否为空
         */
        @Schema(description = "是否为空", example = "false")
        private Boolean isEmpty;

        /**
         * 是否为聚合值
         */
        @Schema(description = "是否为聚合值", example = "false")
        private Boolean isAggregated;

        /**
         * 贡献度
         */
        @Schema(description = "贡献度", example = "0.125")
        private Double contribution;
    }

    /**
     * 立方体元数据
     */
    @Data
    @Schema(description = "立方体元数据")
    public static class CubeMetadata implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 总单元格数
         */
        @Schema(description = "总单元格数", example = "1000")
        private Long totalCells;

        /**
         * 非空单元格数
         */
        @Schema(description = "非空单元格数", example = "850")
        private Long nonEmptyCells;

        /**
         * 数据密度
         */
        @Schema(description = "数据密度", example = "0.85")
        private Double density;

        /**
         * 维度数量
         */
        @Schema(description = "维度数量", example = "3")
        private Integer dimensionCount;

        /**
         * 度量数量
         */
        @Schema(description = "度量数量", example = "5")
        private Integer measureCount;

        /**
         * 数据范围
         */
        @Schema(description = "数据范围")
        private Map<String, Object> dataRange;

        /**
         * 构建时间
         */
        @Schema(description = "构建时间")
        private LocalDateTime buildTime;

        /**
         * 数据源
         */
        @Schema(description = "数据源", example = "acc_transaction")
        private String dataSource;
    }
}
