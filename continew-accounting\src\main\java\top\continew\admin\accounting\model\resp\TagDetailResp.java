package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 标签详情响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "标签详情响应")
public class TagDetailResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Schema(description = "ID", example = "1")
    private Long id;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 群组名称
     */
    @Schema(description = "群组名称", example = "我的记账群")
    private String groupName;

    /**
     * 标签名称
     */
    @Schema(description = "标签名称", example = "工作")
    private String name;

    /**
     * 标签颜色
     */
    @Schema(description = "标签颜色", example = "#FF5722")
    private String color;

    /**
     * 标签图标
     */
    @Schema(description = "标签图标", example = "icon-work")
    private String icon;

    /**
     * 描述
     */
    @Schema(description = "描述", example = "工作相关标签")
    private String description;

    /**
     * 排序
     */
    @Schema(description = "排序", example = "1")
    private Integer sort;

    /**
     * 是否为系统标签
     */
    @Schema(description = "是否为系统标签", example = "false")
    private Boolean isSystem;

    /**
     * 是否为默认标签
     */
    @Schema(description = "是否为默认标签", example = "false")
    private Boolean isDefault;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "1")
    private Integer status;

    /**
     * 使用次数
     */
    @Schema(description = "使用次数", example = "15")
    private Integer usageCount;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID", example = "1")
    private Long createUser;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名", example = "张三")
    private String createUserName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-01-01T10:00:00")
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID", example = "1")
    private Long updateUser;

    /**
     * 更新人姓名
     */
    @Schema(description = "更新人姓名", example = "李四")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2025-01-01T12:00:00")
    private LocalDateTime updateTime;
}
