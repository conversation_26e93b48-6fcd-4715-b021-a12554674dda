package top.continew.admin.bot.discord.listener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.dv8tion.jda.api.entities.Message;
import net.dv8tion.jda.api.entities.User;
import net.dv8tion.jda.api.entities.channel.ChannelType;
import net.dv8tion.jda.api.events.guild.GuildJoinEvent;
import net.dv8tion.jda.api.events.guild.GuildLeaveEvent;
import net.dv8tion.jda.api.events.interaction.command.SlashCommandInteractionEvent;
import net.dv8tion.jda.api.events.interaction.component.ButtonInteractionEvent;
import net.dv8tion.jda.api.events.interaction.component.StringSelectInteractionEvent;
import net.dv8tion.jda.api.events.message.MessageReceivedEvent;
import net.dv8tion.jda.api.events.session.ReadyEvent;
import net.dv8tion.jda.api.hooks.ListenerAdapter;
import org.springframework.stereotype.Component;
import top.continew.admin.bot.config.CommonBotConfig;
import top.continew.admin.bot.discord.handler.DiscordCommandHandler;

/**
 * Discord事件监听器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DiscordEventListener extends ListenerAdapter {

    private final DiscordCommandHandler commandHandler;
    private final CommonBotConfig commonBotConfig;

    @Override
    public void onReady(ReadyEvent event) {
        log.info("Discord机器人已准备就绪: {}", event.getJDA().getSelfUser().getAsTag());
        log.info("连接到 {} 个服务器", event.getJDA().getGuilds().size());
    }

    @Override
    public void onMessageReceived(MessageReceivedEvent event) {
        // 忽略机器人自己的消息
        if (event.getAuthor().isBot()) {
            return;
        }

        Message message = event.getMessage();
        User author = event.getAuthor();
        String content = message.getContentRaw();

        // 记录消息（如果启用）
        if (commonBotConfig.getLogAllMessages()) {
            log.debug("收到Discord消息 - 频道: {}, 用户: {}({}), 内容: {}", 
                    event.getChannel().getId(), author.getName(), author.getId(), content);
        }

        // 检查是否为命令
        if (content.startsWith(commonBotConfig.getCommandPrefix())) {
            handleTextCommand(event);
        } else {
            // 处理普通消息（如果需要）
            handleRegularMessage(event);
        }
    }

    @Override
    public void onSlashCommandInteraction(SlashCommandInteractionEvent event) {
        try {
            String commandName = event.getName();
            String channelId = event.getChannel().getId();
            Long userId = event.getUser().getIdLong();
            String username = event.getUser().getName();

            log.info("收到Discord Slash命令 - 频道: {}, 用户: {}({}), 命令: {}", 
                    channelId, username, userId, commandName);

            // 委托给命令处理器
            commandHandler.handleSlashCommand(event);

        } catch (Exception e) {
            log.error("处理Discord Slash命令失败", e);
            if (!event.isAcknowledged()) {
                event.reply("❌ 处理命令时发生错误，请稍后重试").setEphemeral(true).queue();
            }
        }
    }

    @Override
    public void onButtonInteraction(ButtonInteractionEvent event) {
        try {
            String buttonId = event.getComponentId();
            String channelId = event.getChannel().getId();
            Long userId = event.getUser().getIdLong();
            String username = event.getUser().getName();

            log.info("收到Discord按钮交互 - 频道: {}, 用户: {}({}), 按钮: {}", 
                    channelId, username, userId, buttonId);

            // 委托给命令处理器
            commandHandler.handleButtonInteraction(event);

        } catch (Exception e) {
            log.error("处理Discord按钮交互失败", e);
            event.reply("❌ 处理操作时发生错误，请稍后重试").setEphemeral(true).queue();
        }
    }

    @Override
    public void onStringSelectInteraction(StringSelectInteractionEvent event) {
        try {
            String selectId = event.getComponentId();
            String channelId = event.getChannel().getId();
            Long userId = event.getUser().getIdLong();
            String username = event.getUser().getName();

            log.info("收到Discord选择菜单交互 - 频道: {}, 用户: {}({}), 选择: {}", 
                    channelId, username, userId, selectId);

            // 委托给命令处理器
            commandHandler.handleSelectMenuInteraction(event);

        } catch (Exception e) {
            log.error("处理Discord选择菜单交互失败", e);
            event.reply("❌ 处理操作时发生错误，请稍后重试").setEphemeral(true).queue();
        }
    }

    @Override
    public void onGuildJoin(GuildJoinEvent event) {
        log.info("机器人加入新服务器: {} (ID: {})", 
                event.getGuild().getName(), event.getGuild().getId());
        
        // 发送欢迎消息
        sendWelcomeMessage(event);
    }

    @Override
    public void onGuildLeave(GuildLeaveEvent event) {
        log.info("机器人离开服务器: {} (ID: {})", 
                event.getGuild().getName(), event.getGuild().getId());
        
        // 清理相关数据
        cleanupGuildData(event);
    }

    /**
     * 处理文本命令
     */
    private void handleTextCommand(MessageReceivedEvent event) {
        String content = event.getMessage().getContentRaw();
        String channelId = event.getChannel().getId();
        Long userId = event.getAuthor().getIdLong();
        String username = event.getAuthor().getName();
        boolean isPrivate = event.getChannelType() == ChannelType.PRIVATE;

        log.info("收到Discord文本命令 - 频道: {}, 用户: {}({}), 内容: {}", 
                channelId, username, userId, content);

        // 委托给命令处理器
        commandHandler.handleTextCommand(channelId, userId, username, content, isPrivate, event);
    }

    /**
     * 处理普通消息
     */
    private void handleRegularMessage(MessageReceivedEvent event) {
        // 检查是否包含记账关键词
        String content = event.getMessage().getContentRaw();
        
        // 简单的记账语法检测
        if (isAccountingMessage(content)) {
            String channelId = event.getChannel().getId();
            Long userId = event.getAuthor().getIdLong();
            String username = event.getAuthor().getName();
            boolean isPrivate = event.getChannelType() == ChannelType.PRIVATE;

            log.info("检测到Discord记账消息 - 频道: {}, 用户: {}({}), 内容: {}", 
                    channelId, username, userId, content);

            // 委托给命令处理器处理记账消息
            commandHandler.handleAccountingMessage(channelId, userId, username, content, isPrivate, event);
        }
    }

    /**
     * 检查是否为记账消息
     */
    private boolean isAccountingMessage(String content) {
        // 检查是否包含金额模式
        return content.matches(".*[+\\-]\\d+.*") || 
               content.matches(".*\\d+\\s*(元|￥|\\$|€|£).*") ||
               content.contains("收入") || content.contains("支出") || 
               content.contains("花费") || content.contains("赚了");
    }

    /**
     * 发送欢迎消息
     */
    private void sendWelcomeMessage(GuildJoinEvent event) {
        try {
            // 尝试找到系统频道或第一个文本频道
            var systemChannel = event.getGuild().getSystemChannel();
            var defaultChannel = systemChannel != null ? systemChannel : 
                    event.getGuild().getDefaultChannel();

            if (defaultChannel != null) {
                String welcomeMessage = """
                        🎉 **感谢邀请ContiNew记账机器人！**
                        
                        我可以帮助您的服务器：
                        • 📝 快速记录收支
                        • 📊 查看财务统计
                        • 💰 管理群组财务
                        • 📈 生成财务报表
                        
                        **快速开始：**
                        • 使用 `/help` 查看所有命令
                        • 使用 `/group register` 注册服务器
                        • 使用 `/add` 开始记账
                        
                        需要帮助？使用 `/help` 命令获取详细说明！
                        """;

                defaultChannel.sendMessage(welcomeMessage).queue(
                        success -> log.info("欢迎消息发送成功: {}", event.getGuild().getName()),
                        error -> log.warn("欢迎消息发送失败: {}", event.getGuild().getName(), error)
                );
            }
        } catch (Exception e) {
            log.error("发送欢迎消息失败", e);
        }
    }

    /**
     * 清理服务器数据
     */
    private void cleanupGuildData(GuildLeaveEvent event) {
        try {
            // TODO: 实现数据清理逻辑
            // 1. 清理群组配置
            // 2. 清理缓存数据
            // 3. 记录离开日志
            
            log.info("服务器数据清理完成: {}", event.getGuild().getName());
        } catch (Exception e) {
            log.error("清理服务器数据失败", e);
        }
    }

    /**
     * 检查用户权限
     */
    private boolean hasPermission(User user, String permission) {
        // TODO: 实现权限检查逻辑
        return true;
    }

    /**
     * 检查速率限制
     */
    private boolean isRateLimited(Long userId) {
        // TODO: 实现速率限制检查
        return false;
    }

    /**
     * 检查用户是否在黑名单
     */
    private boolean isBlacklisted(Long userId) {
        String userIdStr = userId.toString();
        for (String blacklistedUser : commonBotConfig.getBlacklistUsers()) {
            if (blacklistedUser.equals(userIdStr)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否为管理员用户
     */
    private boolean isAdminUser(Long userId) {
        String userIdStr = userId.toString();
        for (String adminUser : commonBotConfig.getAdminUsers()) {
            if (adminUser.equals(userIdStr)) {
                return true;
            }
        }
        return false;
    }
}
