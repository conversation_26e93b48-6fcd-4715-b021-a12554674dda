package top.continew.admin.accounting.model.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 报表分类统计响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "报表分类统计响应")
public class ReportCategoryResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分类ID
     */
    @Schema(description = "分类ID", example = "1")
    private Long categoryId;

    /**
     * 分类名称
     */
    @Schema(description = "分类名称", example = "餐饮")
    @ExcelProperty("分类名称")
    private String categoryName;

    /**
     * 分类图标
     */
    @Schema(description = "分类图标", example = "icon-food")
    private String categoryIcon;

    /**
     * 分类颜色
     */
    @Schema(description = "分类颜色", example = "#FF5722")
    private String categoryColor;

    /**
     * 父分类名称
     */
    @Schema(description = "父分类名称", example = "生活消费")
    private String parentCategoryName;

    /**
     * 总金额
     */
    @Schema(description = "总金额", example = "2500.00")
    @ExcelProperty("总金额")
    private BigDecimal totalAmount;

    /**
     * 交易笔数
     */
    @Schema(description = "交易笔数", example = "25")
    @ExcelProperty("交易笔数")
    private Integer transactionCount;

    /**
     * 平均金额
     */
    @Schema(description = "平均金额", example = "100.00")
    @ExcelProperty("平均金额")
    private BigDecimal avgAmount;

    /**
     * 占比（百分比）
     */
    @Schema(description = "占比", example = "15.5")
    @ExcelProperty("占比(%)")
    private BigDecimal percentage;

    /**
     * 最大单笔金额
     */
    @Schema(description = "最大单笔金额", example = "500.00")
    private BigDecimal maxAmount;

    /**
     * 最小单笔金额
     */
    @Schema(description = "最小单笔金额", example = "10.00")
    private BigDecimal minAmount;

    /**
     * 增长率（相比上期）
     */
    @Schema(description = "增长率", example = "12.3")
    private BigDecimal growthRate;
}
