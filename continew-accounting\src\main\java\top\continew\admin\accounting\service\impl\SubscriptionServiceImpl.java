package top.continew.admin.accounting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.accounting.enums.BillingCycle;
import top.continew.admin.accounting.enums.SubscriptionStatus;
import top.continew.admin.accounting.event.SubscriptionCancelledEvent;
import top.continew.admin.accounting.event.SubscriptionCreatedEvent;
import top.continew.admin.accounting.event.SubscriptionExpiredEvent;
import top.continew.admin.accounting.mapper.SubscriptionMapper;
import top.continew.admin.accounting.model.entity.SubscriptionDO;
import top.continew.admin.accounting.model.entity.SubscriptionPlanDO;
import top.continew.admin.accounting.model.req.SubscriptionCreateReq;
import top.continew.admin.accounting.model.req.SubscriptionUpdateReq;
import top.continew.admin.accounting.model.resp.SubscriptionDetailResp;
import top.continew.admin.accounting.model.resp.SubscriptionListResp;
import top.continew.admin.accounting.model.query.SubscriptionQuery;
import top.continew.admin.accounting.service.GroupService;
import top.continew.admin.accounting.service.SubscriptionPlanService;
import top.continew.admin.accounting.service.SubscriptionService;
import top.continew.admin.accounting.service.UsageStatisticsService;
import top.continew.admin.common.base.service.BaseServiceImpl;
import top.continew.starter.core.util.validation.CheckUtils;
import top.continew.starter.security.context.SecurityContextHolder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * 订阅服务实现
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SubscriptionServiceImpl extends BaseServiceImpl<SubscriptionMapper, SubscriptionDO, SubscriptionListResp, SubscriptionDetailResp, SubscriptionQuery, SubscriptionCreateReq> implements SubscriptionService {

    private final SubscriptionPlanService subscriptionPlanService;
    private final GroupService groupService;
    private final UsageStatisticsService usageStatisticsService;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public void beforeCreate(SubscriptionCreateReq req) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查群组权限
        CheckUtils.throwIf(!groupService.isMember(req.getGroupId(), userId), "您不是该群组成员");
        
        // 检查套餐是否存在
        CheckUtils.throwIf(!subscriptionPlanService.exists(req.getPlanId()), "套餐不存在");
        
        // 检查群组是否已有有效订阅
        CheckUtils.throwIf(this.hasActiveSubscription(req.getGroupId()), "该群组已有有效订阅");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createSubscription(SubscriptionCreateReq req) {
        // 创建订阅记录
        Long subscriptionId = super.create(req);
        
        // 获取订阅和套餐信息
        SubscriptionDO subscription = super.getById(subscriptionId);
        SubscriptionPlanDO plan = subscriptionPlanService.getById(req.getPlanId());
        
        // 发布订阅创建事件
        SubscriptionCreatedEvent event = new SubscriptionCreatedEvent(
                this,
                subscriptionId,
                req.getGroupId(),
                req.getPlanId(),
                plan.getName(),
                SecurityContextHolder.getUserId(),
                req.getAmountPaid(),
                req.getCurrency(),
                req.getBillingCycle().name(),
                subscription.getStartDate(),
                subscription.getEndDate(),
                req.getPaymentMethod(),
                subscription.getCreateTime()
        );
        eventPublisher.publishEvent(event);
        
        log.info("创建订阅成功，ID: {}, 群组: {}, 套餐: {}", subscriptionId, req.getGroupId(), req.getPlanId());
        return subscriptionId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSubscription(SubscriptionUpdateReq req, Long id) {
        CheckUtils.throwIfNull(id, "订阅ID不能为空");
        
        SubscriptionDO subscription = super.getById(id);
        CheckUtils.throwIfNull(subscription, "订阅不存在");
        
        BeanUtil.copyProperties(req, subscription, "id", "groupId", "userId", "createUser", "createTime");
        super.updateById(subscription);
        
        log.info("更新订阅成功，ID: {}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelSubscription(Long id, String reason) {
        CheckUtils.throwIfNull(id, "订阅ID不能为空");
        
        SubscriptionDO subscription = super.getById(id);
        CheckUtils.throwIfNull(subscription, "订阅不存在");
        
        LocalDateTime now = LocalDateTime.now();
        long remainingDays = ChronoUnit.DAYS.between(now, subscription.getEndDate());
        
        // 更新订阅状态
        baseMapper.cancelSubscription(id, reason, now);
        
        // 获取套餐信息
        SubscriptionPlanDO plan = subscriptionPlanService.getById(subscription.getPlanId());
        
        // 发布订阅取消事件
        SubscriptionCancelledEvent event = new SubscriptionCancelledEvent(
                this,
                id,
                subscription.getGroupId(),
                subscription.getPlanId(),
                plan.getName(),
                subscription.getUserId(),
                reason,
                now,
                remainingDays,
                now
        );
        eventPublisher.publishEvent(event);
        
        log.info("取消订阅成功，ID: {}, 原因: {}", id, reason);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void renewSubscription(Long id) {
        CheckUtils.throwIfNull(id, "订阅ID不能为空");
        
        SubscriptionDO subscription = super.getById(id);
        CheckUtils.throwIfNull(subscription, "订阅不存在");
        
        // 计算新的结束时间
        BillingCycle cycle = BillingCycle.valueOf(subscription.getBillingCycle());
        LocalDateTime newEndDate = subscription.getEndDate().plusMonths(cycle.getMonths());
        
        // 更新订阅
        baseMapper.renewSubscription(id, newEndDate, LocalDateTime.now());
        
        log.info("续费订阅成功，ID: {}, 新结束时间: {}", id, newEndDate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void suspendSubscription(Long id, String reason) {
        CheckUtils.throwIfNull(id, "订阅ID不能为空");
        
        baseMapper.updateStatus(id, SubscriptionStatus.SUSPENDED.getValue(), LocalDateTime.now());
        
        log.info("暂停订阅成功，ID: {}, 原因: {}", id, reason);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resumeSubscription(Long id) {
        CheckUtils.throwIfNull(id, "订阅ID不能为空");
        
        baseMapper.updateStatus(id, SubscriptionStatus.ACTIVE.getValue(), LocalDateTime.now());
        
        log.info("恢复订阅成功，ID: {}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void upgradeSubscription(Long id, Long newPlanId) {
        CheckUtils.throwIfNull(id, "订阅ID不能为空");
        CheckUtils.throwIfNull(newPlanId, "新套餐ID不能为空");
        
        SubscriptionDO subscription = super.getById(id);
        CheckUtils.throwIfNull(subscription, "订阅不存在");
        
        // 检查新套餐是否存在
        CheckUtils.throwIf(!subscriptionPlanService.exists(newPlanId), "新套餐不存在");
        
        subscription.setPlanId(newPlanId);
        super.updateById(subscription);
        
        log.info("升级订阅成功，ID: {}, 新套餐: {}", id, newPlanId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void downgradeSubscription(Long id, Long newPlanId) {
        CheckUtils.throwIfNull(id, "订阅ID不能为空");
        CheckUtils.throwIfNull(newPlanId, "新套餐ID不能为空");
        
        SubscriptionDO subscription = super.getById(id);
        CheckUtils.throwIfNull(subscription, "订阅不存在");
        
        // 检查新套餐是否存在
        CheckUtils.throwIf(!subscriptionPlanService.exists(newPlanId), "新套餐不存在");
        
        subscription.setPlanId(newPlanId);
        super.updateById(subscription);
        
        log.info("降级订阅成功，ID: {}, 新套餐: {}", id, newPlanId);
    }

    @Override
    public SubscriptionDetailResp getActiveSubscriptionByGroupId(Long groupId) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        
        return baseMapper.selectSubscriptionDetail(groupId);
    }

    @Override
    public List<SubscriptionListResp> getUserSubscriptionHistory(Long userId) {
        CheckUtils.throwIfNull(userId, "用户ID不能为空");
        
        return baseMapper.selectUserSubscriptionHistory(userId);
    }

    @Override
    public List<SubscriptionListResp> getGroupSubscriptionHistory(Long groupId) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        
        return baseMapper.selectGroupSubscriptionHistory(groupId);
    }

    @Override
    public boolean hasActiveSubscription(Long groupId) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        
        SubscriptionDO subscription = baseMapper.selectActiveByGroupId(groupId);
        return subscription != null && SubscriptionStatus.ACTIVE.getValue().equals(subscription.getStatus());
    }

    @Override
    public boolean hasFeaturePermission(Long groupId, String featureName) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        CheckUtils.throwIfBlank(featureName, "功能名称不能为空");
        
        SubscriptionDO subscription = baseMapper.selectActiveByGroupId(groupId);
        if (subscription == null || !SubscriptionStatus.ACTIVE.getValue().equals(subscription.getStatus())) {
            return false;
        }
        
        return subscriptionPlanService.validatePlanFeature(subscription.getPlanId(), featureName);
    }

    @Override
    public boolean isUsageLimitExceeded(Long groupId, String limitType, Integer currentUsage) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        CheckUtils.throwIfBlank(limitType, "限制类型不能为空");
        CheckUtils.throwIfNull(currentUsage, "当前使用量不能为空");
        
        SubscriptionDO subscription = baseMapper.selectActiveByGroupId(groupId);
        if (subscription == null) {
            return true; // 没有订阅，视为超限
        }
        
        Integer limit = subscriptionPlanService.getPlanLimit(subscription.getPlanId(), limitType);
        if (limit == null || limit == -1) {
            return false; // 无限制
        }
        
        return currentUsage >= limit;
    }

    @Override
    public Integer getRemainingUsage(Long groupId, String limitType, Integer currentUsage) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        CheckUtils.throwIfBlank(limitType, "限制类型不能为空");
        CheckUtils.throwIfNull(currentUsage, "当前使用量不能为空");
        
        SubscriptionDO subscription = baseMapper.selectActiveByGroupId(groupId);
        if (subscription == null) {
            return 0;
        }
        
        Integer limit = subscriptionPlanService.getPlanLimit(subscription.getPlanId(), limitType);
        if (limit == null || limit == -1) {
            return Integer.MAX_VALUE; // 无限制
        }
        
        return Math.max(0, limit - currentUsage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleExpiringSubscriptions() {
        List<SubscriptionDO> expiringSubscriptions = baseMapper.selectExpiringSubscriptions(7); // 7天内过期
        
        for (SubscriptionDO subscription : expiringSubscriptions) {
            // 发送过期提醒通知
            log.info("订阅即将过期提醒，订阅ID: {}, 群组: {}, 过期时间: {}", 
                    subscription.getId(), subscription.getGroupId(), subscription.getEndDate());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleExpiredSubscriptions() {
        List<SubscriptionDO> expiredSubscriptions = baseMapper.selectExpiredSubscriptions();
        
        for (SubscriptionDO subscription : expiredSubscriptions) {
            // 更新状态为过期
            baseMapper.updateStatus(subscription.getId(), SubscriptionStatus.EXPIRED.getValue(), LocalDateTime.now());
            
            // 获取套餐信息
            SubscriptionPlanDO plan = subscriptionPlanService.getById(subscription.getPlanId());
            
            // 发布订阅过期事件
            SubscriptionExpiredEvent event = new SubscriptionExpiredEvent(
                    this,
                    subscription.getId(),
                    subscription.getGroupId(),
                    subscription.getPlanId(),
                    plan.getName(),
                    subscription.getUserId(),
                    subscription.getEndDate(),
                    subscription.getAutoRenew(),
                    LocalDateTime.now()
            );
            eventPublisher.publishEvent(event);
            
            log.info("处理过期订阅，订阅ID: {}, 群组: {}", subscription.getId(), subscription.getGroupId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processAutoRenewal() {
        List<SubscriptionDO> autoRenewSubscriptions = baseMapper.selectExpiringSubscriptions(1); // 1天内过期且自动续费
        
        for (SubscriptionDO subscription : autoRenewSubscriptions) {
            if (Boolean.TRUE.equals(subscription.getAutoRenew())) {
                try {
                    this.renewSubscription(subscription.getId());
                    log.info("自动续费成功，订阅ID: {}", subscription.getId());
                } catch (Exception e) {
                    log.error("自动续费失败，订阅ID: {}, 错误: {}", subscription.getId(), e.getMessage());
                }
            }
        }
    }

    @Override
    public SubscriptionStatsResp getSubscriptionStats() {
        SubscriptionStatsResp stats = new SubscriptionStatsResp();
        
        stats.setTotalSubscriptions(baseMapper.countSubscriptions(null, null));
        stats.setActiveSubscriptions(baseMapper.countSubscriptions(SubscriptionStatus.ACTIVE.getValue(), null));
        stats.setExpiredSubscriptions(baseMapper.countSubscriptions(SubscriptionStatus.EXPIRED.getValue(), null));
        stats.setCancelledSubscriptions(baseMapper.countSubscriptions(SubscriptionStatus.CANCELLED.getValue(), null));
        
        LocalDateTime now = LocalDateTime.now();
        stats.setTotalRevenue(baseMapper.sumRevenue(null, now));
        stats.setMonthlyRevenue(baseMapper.sumRevenue(now.withDayOfMonth(1), now));
        
        return stats;
    }

    @Override
    public SubscriptionDO getActiveSubscription(Long groupId) {
        return baseMapper.selectActiveByGroupId(groupId);
    }

    @Override
    public SubscriptionPlanDO getSubscriptionPlan(Long planId) {
        return subscriptionPlanService.getById(planId);
    }
}
