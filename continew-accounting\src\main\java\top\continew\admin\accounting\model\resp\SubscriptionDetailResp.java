package top.continew.admin.accounting.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.accounting.enums.SubscriptionStatus;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 订阅详情响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "订阅详情响应")
public class SubscriptionDetailResp {

    /**
     * ID
     */
    @Schema(description = "ID", example = "1")
    private Long id;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 群组名称
     */
    @Schema(description = "群组名称", example = "我的群组")
    private String groupName;

    /**
     * 套餐ID
     */
    @Schema(description = "套餐ID", example = "1")
    private Long planId;

    /**
     * 套餐名称
     */
    @Schema(description = "套餐名称", example = "专业版")
    private String planName;

    /**
     * 套餐描述
     */
    @Schema(description = "套餐描述", example = "适合小团队使用的专业版套餐")
    private String planDescription;

    /**
     * 套餐功能特性
     */
    @Schema(description = "套餐功能特性")
    private Map<String, Object> planFeatures;

    /**
     * 订阅用户ID
     */
    @Schema(description = "订阅用户ID", example = "1")
    private Long userId;

    /**
     * 订阅用户名称
     */
    @Schema(description = "订阅用户名称", example = "张三")
    private String userName;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "ACTIVE")
    private SubscriptionStatus status;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2025-01-01 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startDate;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", example = "2025-02-01 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endDate;

    /**
     * 自动续费
     */
    @Schema(description = "自动续费", example = "true")
    private Boolean autoRenew;

    /**
     * 支付方式
     */
    @Schema(description = "支付方式", example = "STRIPE")
    private String paymentMethod;

    /**
     * 支付金额
     */
    @Schema(description = "支付金额", example = "9.99")
    private BigDecimal amountPaid;

    /**
     * 币种
     */
    @Schema(description = "币种", example = "USD")
    private String currency;

    /**
     * 计费周期
     */
    @Schema(description = "计费周期", example = "MONTHLY")
    private String billingCycle;

    /**
     * 试用结束时间
     */
    @Schema(description = "试用结束时间", example = "2025-01-08 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime trialEndDate;

    /**
     * 取消时间
     */
    @Schema(description = "取消时间", example = "2025-01-15 10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cancelledAt;

    /**
     * 取消原因
     */
    @Schema(description = "取消原因", example = "用户主动取消")
    private String cancellationReason;

    /**
     * 剩余天数
     */
    @Schema(description = "剩余天数", example = "25")
    private Long remainingDays;

    /**
     * 使用统计
     */
    @Schema(description = "使用统计")
    private UsageStats usageStats;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-01-01 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2025-01-01 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 使用统计内部类
     */
    @Data
    @Schema(description = "使用统计")
    public static class UsageStats {
        
        @Schema(description = "当月交易数量", example = "150")
        private Integer currentMonthTransactions;
        
        @Schema(description = "群组数量", example = "2")
        private Integer groupCount;
        
        @Schema(description = "成员数量", example = "15")
        private Integer memberCount;
        
        @Schema(description = "存储使用量(GB)", example = "2.5")
        private BigDecimal storageUsed;
        
        @Schema(description = "OCR使用次数", example = "25")
        private Integer ocrUsage;
        
        @Schema(description = "API调用次数", example = "1250")
        private Integer apiCalls;
    }
}
