/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.tenant.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 套餐和菜单关联实体
 *
 * <AUTHOR>
 * @since 2025/7/11 22:01
 */
@Data
@NoArgsConstructor
@TableName("tenant_package_menu")
public class PackageMenuDO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 套餐 ID
     */
    private Long packageId;

    /**
     * 菜单 ID
     */
    private Long menuId;

    public PackageMenuDO(Long packageId, Long menuId) {
        this.packageId = packageId;
        this.menuId = menuId;
    }
}