<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.accounting.mapper.FileStorageConfigMapper">

    <!-- 获取配置统计信息 -->
    <select id="getConfigStatistics" resultType="map">
        SELECT 
            c.id,
            c.config_name as configName,
            c.storage_type as storageType,
            c.status,
            COUNT(f.id) as fileCount,
            COALESCE(SUM(f.file_size), 0) as totalSize,
            COUNT(CASE WHEN DATE(f.create_time) = CURDATE() THEN 1 END) as todayUploadCount,
            COALESCE(SUM(CASE WHEN DATE(f.create_time) = CURDATE() THEN f.file_size ELSE 0 END), 0) as todayUploadSize
        FROM acc_file_storage_config c
        LEFT JOIN acc_file_storage f ON c.config_code = f.storage_type AND f.is_deleted = 0
        WHERE c.id = #{configId}
        GROUP BY c.id, c.config_name, c.storage_type, c.status
    </select>

    <!-- 获取配置使用情况 -->
    <select id="getConfigUsage" resultType="map">
        SELECT 
            c.id,
            c.config_name as configName,
            c.config_code as configCode,
            c.storage_type as storageType,
            c.status,
            c.is_default as isDefault,
            COUNT(f.id) as fileCount,
            COALESCE(SUM(f.file_size), 0) as totalSize,
            ROUND(COUNT(f.id) * 100.0 / NULLIF((SELECT COUNT(*) FROM acc_file_storage WHERE is_deleted = 0 AND group_id = #{groupId}), 0), 2) as usagePercentage
        FROM acc_file_storage_config c
        LEFT JOIN acc_file_storage f ON c.config_code = f.storage_type AND f.is_deleted = 0 AND f.group_id = #{groupId}
        WHERE c.group_id = #{groupId}
        GROUP BY c.id, c.config_name, c.config_code, c.storage_type, c.status, c.is_default
        ORDER BY c.sort ASC, c.create_time DESC
    </select>

    <!-- 获取存储类型分布 -->
    <select id="getStorageTypeDistribution" resultType="map">
        SELECT 
            c.storage_type as storageType,
            COUNT(DISTINCT c.id) as configCount,
            COUNT(f.id) as fileCount,
            COALESCE(SUM(f.file_size), 0) as totalSize
        FROM acc_file_storage_config c
        LEFT JOIN acc_file_storage f ON c.config_code = f.storage_type AND f.is_deleted = 0 AND f.group_id = #{groupId}
        WHERE c.group_id = #{groupId}
        GROUP BY c.storage_type
        ORDER BY configCount DESC
    </select>

    <!-- 获取配置健康状态 -->
    <select id="getConfigHealthStatus" resultType="map">
        SELECT 
            c.id,
            c.config_name as configName,
            c.config_code as configCode,
            c.storage_type as storageType,
            c.status,
            c.is_default as isDefault,
            CASE 
                WHEN c.status = 'DISABLE' THEN 'DISABLED'
                WHEN c.endpoint IS NULL OR c.endpoint = '' THEN 'INVALID_CONFIG'
                WHEN c.access_key_id IS NULL OR c.access_key_id = '' THEN 'INVALID_CREDENTIALS'
                ELSE 'HEALTHY'
            END as healthStatus,
            c.update_time as lastCheckTime
        FROM acc_file_storage_config c
        WHERE c.group_id = #{groupId}
        ORDER BY c.sort ASC, c.create_time DESC
    </select>

    <!-- 批量测试配置连接 -->
    <select id="batchTestConnection" resultType="map">
        SELECT 
            id,
            config_name as configName,
            config_code as configCode,
            storage_type as storageType,
            endpoint,
            bucket_name as bucketName,
            status
        FROM acc_file_storage_config
        WHERE id IN
        <foreach collection="configIds" item="configId" open="(" separator="," close=")">
            #{configId}
        </foreach>
    </select>

    <!-- 获取配置性能指标 -->
    <select id="getConfigPerformanceMetrics" resultType="map">
        SELECT 
            DATE(f.create_time) as date,
            COUNT(*) as uploadCount,
            COALESCE(SUM(f.file_size), 0) as uploadSize,
            AVG(f.file_size) as avgFileSize,
            COUNT(CASE WHEN f.process_status = 'COMPLETED' THEN 1 END) as successCount,
            COUNT(CASE WHEN f.process_status = 'FAILED' THEN 1 END) as failureCount,
            ROUND(COUNT(CASE WHEN f.process_status = 'COMPLETED' THEN 1 END) * 100.0 / COUNT(*), 2) as successRate
        FROM acc_file_storage f
        INNER JOIN acc_file_storage_config c ON c.config_code = f.storage_type
        WHERE c.id = #{configId}
            AND f.is_deleted = 0
            AND f.create_time >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
        GROUP BY DATE(f.create_time)
        ORDER BY date DESC
    </select>

    <!-- 获取配置错误日志 -->
    <select id="getConfigErrorLogs" resultType="map">
        SELECT 
            f.id as fileId,
            f.file_name as fileName,
            f.process_status as processStatus,
            f.process_result as processResult,
            f.create_time as createTime,
            f.update_time as updateTime
        FROM acc_file_storage f
        INNER JOIN acc_file_storage_config c ON c.config_code = f.storage_type
        WHERE c.id = #{configId}
            AND f.process_status = 'FAILED'
            AND f.is_deleted = 0
        ORDER BY f.update_time DESC
        LIMIT #{limit}
    </select>

    <!-- 清理配置缓存 -->
    <update id="clearConfigCache">
        UPDATE acc_file_storage_config 
        SET update_time = NOW()
        WHERE id = #{configId}
    </update>

    <!-- 同步配置状态 -->
    <update id="syncConfigStatus">
        UPDATE acc_file_storage_config 
        SET update_time = NOW()
        WHERE id = #{configId}
    </update>

</mapper>
