<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>top.continew.admin</groupId>
        <artifactId>continew-admin</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>continew-accounting</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>记账核心模块</description>

    <dependencies>
        <!-- 通用模块 -->
        <dependency>
            <groupId>top.continew.admin</groupId>
            <artifactId>continew-common</artifactId>
        </dependency>

        <!-- 系统管理模块 -->
        <dependency>
            <groupId>top.continew.admin</groupId>
            <artifactId>continew-system</artifactId>
        </dependency>

        <!-- ContiNew Starter Web -->
        <dependency>
            <groupId>top.continew.starter</groupId>
            <artifactId>continew-starter-web</artifactId>
        </dependency>

        <!-- ContiNew Starter 数据访问 -->
        <dependency>
            <groupId>top.continew.starter</groupId>
            <artifactId>continew-starter-data-mybatis-plus</artifactId>
        </dependency>

        <!-- ContiNew Starter 缓存 -->
        <dependency>
            <groupId>top.continew.starter</groupId>
            <artifactId>continew-starter-cache-jetcache</artifactId>
        </dependency>

        <!-- ContiNew Starter 安全 -->
        <dependency>
            <groupId>top.continew.starter</groupId>
            <artifactId>continew-starter-security-satoken</artifactId>
        </dependency>

        <!-- ContiNew Starter 验证 -->
        <dependency>
            <groupId>top.continew.starter</groupId>
            <artifactId>continew-starter-web-validation</artifactId>
        </dependency>

        <!-- ContiNew Starter JSON -->
        <dependency>
            <groupId>top.continew.starter</groupId>
            <artifactId>continew-starter-json-jackson</artifactId>
        </dependency>

        <!-- 数学计算 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-math3</artifactId>
        </dependency>

        <!-- 汇率API -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- H2 内存数据库（用于测试） -->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- TestContainers（用于集成测试） -->
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>mysql</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>redis</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- Maven Surefire Plugin for running tests -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                    <includes>
                        <include>**/*Test.java</include>
                        <include>**/*Tests.java</include>
                    </includes>
                    <excludes>
                        <exclude>**/integration/**/*Test.java</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <!-- Maven Failsafe Plugin for integration tests -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <configuration>
                    <includes>
                        <include>**/integration/**/*Test.java</include>
                    </includes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>integration-test</goal>
                            <goal>verify</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- JaCoCo Plugin for code coverage -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.8</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>check</id>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <rule>
                                    <element>BUNDLE</element>
                                    <limits>
                                        <limit>
                                            <counter>INSTRUCTION</counter>
                                            <value>COVEREDRATIO</value>
                                            <minimum>0.80</minimum>
                                        </limit>
                                        <limit>
                                            <counter>BRANCH</counter>
                                            <value>COVEREDRATIO</value>
                                            <minimum>0.75</minimum>
                                        </limit>
                                    </limits>
                                </rule>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
