package top.continew.admin.accounting.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.web.multipart.MultipartFile;
import top.continew.admin.accounting.model.entity.FileStorageDO;
import top.continew.admin.accounting.model.req.FileQueryReq;
import top.continew.admin.accounting.model.req.FileUploadReq;
import top.continew.admin.accounting.model.resp.FileStatisticsResp;
import top.continew.admin.accounting.model.resp.FileStorageResp;
import top.continew.admin.accounting.model.resp.FileUploadResp;
import top.continew.admin.common.base.service.BaseService;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * 文件存储服务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface FileStorageService extends BaseService<FileStorageDO> {

    /**
     * 上传文件
     *
     * @param file      文件
     * @param uploadReq 上传请求
     * @return 上传响应
     */
    FileUploadResp uploadFile(MultipartFile file, FileUploadReq uploadReq);

    /**
     * 批量上传文件
     *
     * @param files     文件列表
     * @param uploadReq 上传请求
     * @return 上传响应列表
     */
    List<FileUploadResp> uploadFiles(List<MultipartFile> files, FileUploadReq uploadReq);

    /**
     * 上传文件流
     *
     * @param inputStream 文件流
     * @param fileName    文件名
     * @param uploadReq   上传请求
     * @return 上传响应
     */
    FileUploadResp uploadFileStream(InputStream inputStream, String fileName, FileUploadReq uploadReq);

    /**
     * 下载文件
     *
     * @param fileId 文件ID
     * @return 文件流
     */
    InputStream downloadFile(Long fileId);

    /**
     * 获取文件下载URL
     *
     * @param fileId     文件ID
     * @param expireTime 过期时间（秒）
     * @return 下载URL
     */
    String getDownloadUrl(Long fileId, Long expireTime);

    /**
     * 获取文件预览URL
     *
     * @param fileId     文件ID
     * @param expireTime 过期时间（秒）
     * @return 预览URL
     */
    String getPreviewUrl(Long fileId, Long expireTime);

    /**
     * 删除文件
     *
     * @param fileId 文件ID
     * @return 是否成功
     */
    Boolean deleteFile(Long fileId);

    /**
     * 批量删除文件
     *
     * @param fileIds 文件ID列表
     * @return 删除结果
     */
    Map<Long, Boolean> deleteFiles(List<Long> fileIds);

    /**
     * 复制文件
     *
     * @param fileId      源文件ID
     * @param targetPath  目标路径
     * @param targetName  目标文件名
     * @return 新文件信息
     */
    FileStorageResp copyFile(Long fileId, String targetPath, String targetName);

    /**
     * 移动文件
     *
     * @param fileId      文件ID
     * @param targetPath  目标路径
     * @param targetName  目标文件名
     * @return 是否成功
     */
    Boolean moveFile(Long fileId, String targetPath, String targetName);

    /**
     * 重命名文件
     *
     * @param fileId  文件ID
     * @param newName 新文件名
     * @return 是否成功
     */
    Boolean renameFile(Long fileId, String newName);

    /**
     * 生成缩略图
     *
     * @param fileId 文件ID
     * @param width  宽度
     * @param height 高度
     * @return 缩略图URL
     */
    String generateThumbnail(Long fileId, Integer width, Integer height);

    /**
     * 添加水印
     *
     * @param fileId        文件ID
     * @param watermarkText 水印文本
     * @param position      水印位置
     * @return 水印文件URL
     */
    String addWatermark(Long fileId, String watermarkText, String position);

    /**
     * 文件安全扫描
     *
     * @param fileId 文件ID
     * @return 扫描结果
     */
    Map<String, Object> securityScan(Long fileId);

    /**
     * 获取文件信息
     *
     * @param fileId 文件ID
     * @return 文件信息
     */
    FileStorageResp getFileInfo(Long fileId);

    /**
     * 分页查询文件
     *
     * @param queryReq 查询请求
     * @return 分页结果
     */
    IPage<FileStorageResp> pageFiles(FileQueryReq queryReq);

    /**
     * 查询文件列表
     *
     * @param queryReq 查询请求
     * @return 文件列表
     */
    List<FileStorageResp> listFiles(FileQueryReq queryReq);

    /**
     * 获取文件统计信息
     *
     * @param groupId 群组ID
     * @return 统计信息
     */
    FileStatisticsResp getFileStatistics(Long groupId);

    /**
     * 清理过期文件
     *
     * @return 清理数量
     */
    Long cleanExpiredFiles();

    /**
     * 清理临时文件
     *
     * @param days 天数
     * @return 清理数量
     */
    Long cleanTemporaryFiles(Integer days);

    /**
     * 检查文件是否存在
     *
     * @param fileId 文件ID
     * @return 是否存在
     */
    Boolean checkFileExists(Long fileId);

    /**
     * 验证文件完整性
     *
     * @param fileId 文件ID
     * @return 验证结果
     */
    Map<String, Object> validateFileIntegrity(Long fileId);

    /**
     * 获取文件访问日志
     *
     * @param fileId 文件ID
     * @param limit  限制数量
     * @return 访问日志
     */
    List<Map<String, Object>> getFileAccessLog(Long fileId, Integer limit);

    /**
     * 记录文件访问
     *
     * @param fileId     文件ID
     * @param accessType 访问类型
     * @param userAgent  用户代理
     * @param clientIp   客户端IP
     */
    void recordFileAccess(Long fileId, String accessType, String userAgent, String clientIp);

    /**
     * 获取存储使用情况
     *
     * @param groupId 群组ID
     * @return 使用情况
     */
    Map<String, Object> getStorageUsage(Long groupId);

    /**
     * 同步文件元数据
     *
     * @param fileId 文件ID
     * @return 是否成功
     */
    Boolean syncFileMetadata(Long fileId);

}
