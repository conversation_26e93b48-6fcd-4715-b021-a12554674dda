package top.continew.admin.bot.telegram.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;
import org.telegram.telegrambots.meta.api.objects.Update;
import top.continew.admin.bot.telegram.TelegramBotService;
import top.continew.starter.web.model.R;

/**
 * Telegram Webhook控制器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "Telegram Webhook API")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/bot/telegram")
@ConditionalOnProperty(prefix = "bot.telegram", name = "enabled", havingValue = "true")
public class TelegramWebhookController {

    private final TelegramBotService telegramBotService;

    /**
     * 接收Telegram Webhook消息
     */
    @Operation(summary = "接收Telegram Webhook消息", description = "接收来自Telegram的Webhook消息")
    @PostMapping("/webhook")
    public R<Void> webhook(@RequestBody Update update) {
        try {
            log.debug("收到Telegram Webhook消息: {}", update);
            
            // 处理更新
            telegramBotService.onUpdateReceived(update);
            
            return R.ok();
        } catch (Exception e) {
            log.error("处理Telegram Webhook消息失败", e);
            return R.fail("处理消息失败");
        }
    }

    /**
     * 健康检查
     */
    @Operation(summary = "健康检查", description = "检查Telegram机器人服务状态")
    @GetMapping("/health")
    public R<String> health() {
        return R.ok("Telegram机器人服务正常");
    }

    /**
     * 获取机器人信息
     */
    @Operation(summary = "获取机器人信息", description = "获取Telegram机器人基本信息")
    @GetMapping("/info")
    public R<Object> info() {
        try {
            return R.ok(java.util.Map.of(
                "botUsername", telegramBotService.getBotUsername(),
                "status", "running"
            ));
        } catch (Exception e) {
            log.error("获取机器人信息失败", e);
            return R.fail("获取机器人信息失败");
        }
    }

    /**
     * 发送测试消息
     */
    @Operation(summary = "发送测试消息", description = "向指定聊天发送测试消息")
    @PostMapping("/test")
    public R<Void> sendTestMessage(@RequestParam String chatId, 
                                  @RequestParam(defaultValue = "测试消息") String message) {
        try {
            telegramBotService.sendMessage(chatId, message);
            return R.ok();
        } catch (Exception e) {
            log.error("发送测试消息失败", e);
            return R.fail("发送测试消息失败");
        }
    }
}
