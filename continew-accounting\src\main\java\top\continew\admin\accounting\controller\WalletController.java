package top.continew.admin.accounting.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.accounting.model.query.WalletQuery;
import top.continew.admin.accounting.model.req.WalletCreateReq;
import top.continew.admin.accounting.model.req.WalletUpdateReq;
import top.continew.admin.accounting.model.resp.WalletDetailResp;
import top.continew.admin.accounting.model.resp.WalletListResp;
import top.continew.admin.accounting.model.resp.WalletSummaryResp;
import top.continew.admin.accounting.model.resp.WalletHistoryResp;
import top.continew.admin.accounting.service.WalletService;
import top.continew.admin.common.base.controller.BaseController;
import top.continew.starter.core.util.validate.ValidationUtils;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;
import top.continew.starter.security.context.SecurityContextHolder;
import top.continew.starter.web.model.R;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 钱包管理 API
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "钱包管理 API")
@RestController
@RequiredArgsConstructor
@RequestMapping("/accounting/wallet")
@Validated
public class WalletController extends BaseController<WalletService, WalletListResp, WalletDetailResp, WalletQuery, WalletCreateReq> {

    /**
     * 启用标准 CRUD API
     */
    @CrudRequestMapping(value = "", api = {Api.PAGE, Api.LIST, Api.GET, Api.CREATE, Api.UPDATE, Api.BATCH_DELETE, Api.EXPORT, Api.DICT})
    @SaCheckPermission("accounting:wallet")
    public void crud() {
        // 标准 CRUD 操作由 @CrudRequestMapping 自动生成
    }

    /**
     * 更新钱包
     */
    @Operation(summary = "更新钱包", description = "更新钱包信息")
    @PutMapping("/{id}")
    @SaCheckPermission("accounting:wallet:update")
    public R<Void> update(@Parameter(description = "ID", example = "1") @PathVariable Long id,
                         @Validated @RequestBody WalletUpdateReq req) {
        baseService.update(req, id);
        return R.ok();
    }

    /**
     * 获取群组钱包列表
     */
    @Operation(summary = "获取群组钱包列表", description = "获取指定群组的所有钱包")
    @GetMapping("/group/{groupId}")
    @SaCheckPermission("accounting:wallet:list")
    public R<List<WalletListResp>> getGroupWallets(
            @Parameter(description = "群组ID", example = "1") @PathVariable @NotNull Long groupId) {
        
        List<WalletListResp> wallets = baseService.getGroupWallets(groupId);
        return R.ok(wallets);
    }

    /**
     * 获取钱包余额
     */
    @Operation(summary = "获取钱包余额", description = "获取指定群组和币种的钱包余额")
    @GetMapping("/balance")
    @SaCheckPermission("accounting:wallet:balance")
    public R<BigDecimal> getBalance(
            @Parameter(description = "群组ID", example = "1") @RequestParam @NotNull Long groupId,
            @Parameter(description = "币种", example = "CNY") @RequestParam(defaultValue = "CNY") String currency) {
        
        BigDecimal balance = baseService.getBalance(groupId, currency);
        return R.ok(balance);
    }

    /**
     * 获取可用余额
     */
    @Operation(summary = "获取可用余额", description = "获取指定群组和币种的可用余额（余额-冻结金额）")
    @GetMapping("/available-balance")
    @SaCheckPermission("accounting:wallet:balance")
    public R<BigDecimal> getAvailableBalance(
            @Parameter(description = "群组ID", example = "1") @RequestParam @NotNull Long groupId,
            @Parameter(description = "币种", example = "CNY") @RequestParam(defaultValue = "CNY") String currency) {
        
        BigDecimal availableBalance = baseService.getAvailableBalance(groupId, currency);
        return R.ok(availableBalance);
    }

    /**
     * 检查余额是否足够
     */
    @Operation(summary = "检查余额是否足够", description = "检查指定群组和币种的余额是否足够")
    @GetMapping("/check-balance")
    @SaCheckPermission("accounting:wallet:balance")
    public R<Boolean> hasEnoughBalance(
            @Parameter(description = "群组ID", example = "1") @RequestParam @NotNull Long groupId,
            @Parameter(description = "币种", example = "CNY") @RequestParam(defaultValue = "CNY") String currency,
            @Parameter(description = "需要的金额", example = "100.00") @RequestParam @NotNull BigDecimal amount) {
        
        ValidationUtils.throwIf(amount.compareTo(BigDecimal.ZERO) <= 0, "金额必须大于0");
        
        boolean hasEnough = baseService.hasEnoughBalance(groupId, currency, amount);
        return R.ok(hasEnough);
    }

    /**
     * 获取群组钱包汇总
     */
    @Operation(summary = "获取群组钱包汇总", description = "获取指定群组的钱包汇总信息")
    @GetMapping("/summary/{groupId}")
    @SaCheckPermission("accounting:wallet:summary")
    public R<WalletSummaryResp> getGroupWalletSummary(
            @Parameter(description = "群组ID", example = "1") @PathVariable @NotNull Long groupId) {
        
        WalletSummaryResp summary = baseService.getGroupWalletSummary(groupId);
        return R.ok(summary);
    }

    /**
     * 冻结金额
     */
    @Operation(summary = "冻结金额", description = "冻结指定群组和币种的金额")
    @PostMapping("/freeze")
    @SaCheckPermission("accounting:wallet:freeze")
    public R<Void> freezeAmount(
            @Parameter(description = "群组ID", example = "1") @RequestParam @NotNull Long groupId,
            @Parameter(description = "币种", example = "CNY") @RequestParam(defaultValue = "CNY") String currency,
            @Parameter(description = "冻结金额", example = "100.00") @RequestParam @NotNull BigDecimal amount) {
        
        ValidationUtils.throwIf(amount.compareTo(BigDecimal.ZERO) <= 0, "冻结金额必须大于0");
        
        baseService.freezeAmount(groupId, currency, amount);
        return R.ok();
    }

    /**
     * 解冻金额
     */
    @Operation(summary = "解冻金额", description = "解冻指定群组和币种的金额")
    @PostMapping("/unfreeze")
    @SaCheckPermission("accounting:wallet:unfreeze")
    public R<Void> unfreezeAmount(
            @Parameter(description = "群组ID", example = "1") @RequestParam @NotNull Long groupId,
            @Parameter(description = "币种", example = "CNY") @RequestParam(defaultValue = "CNY") String currency,
            @Parameter(description = "解冻金额", example = "100.00") @RequestParam @NotNull BigDecimal amount) {
        
        ValidationUtils.throwIf(amount.compareTo(BigDecimal.ZERO) <= 0, "解冻金额必须大于0");
        
        baseService.unfreezeAmount(groupId, currency, amount);
        return R.ok();
    }

    /**
     * 转账
     */
    @Operation(summary = "转账", description = "在群组间进行转账")
    @PostMapping("/transfer")
    @SaCheckPermission("accounting:wallet:transfer")
    public R<Void> transfer(
            @Parameter(description = "转出群组ID", example = "1") @RequestParam @NotNull Long fromGroupId,
            @Parameter(description = "转入群组ID", example = "2") @RequestParam @NotNull Long toGroupId,
            @Parameter(description = "币种", example = "CNY") @RequestParam(defaultValue = "CNY") String currency,
            @Parameter(description = "转账金额", example = "100.00") @RequestParam @NotNull BigDecimal amount,
            @Parameter(description = "转账说明", example = "群组间转账") @RequestParam(required = false) String description) {
        
        ValidationUtils.throwIf(amount.compareTo(BigDecimal.ZERO) <= 0, "转账金额必须大于0");
        ValidationUtils.throwIf(fromGroupId.equals(toGroupId), "转出和转入群组不能相同");
        
        Long operatorId = SecurityContextHolder.getUserId();
        baseService.transfer(fromGroupId, toGroupId, currency, amount, description, operatorId);
        return R.ok();
    }

    /**
     * 重置钱包余额（管理员功能）
     */
    @Operation(summary = "重置钱包余额", description = "重置指定群组和币种的钱包余额（管理员功能）")
    @PostMapping("/reset-balance")
    @SaCheckPermission("accounting:wallet:reset")
    public R<Void> resetBalance(
            @Parameter(description = "群组ID", example = "1") @RequestParam @NotNull Long groupId,
            @Parameter(description = "币种", example = "CNY") @RequestParam(defaultValue = "CNY") String currency,
            @Parameter(description = "新余额", example = "1000.00") @RequestParam @NotNull BigDecimal newBalance,
            @Parameter(description = "重置原因", example = "数据修正") @RequestParam @NotNull String reason) {
        
        ValidationUtils.throwIf(newBalance.compareTo(BigDecimal.ZERO) < 0, "新余额不能为负数");
        ValidationUtils.throwIfBlank(reason, "重置原因不能为空");
        
        Long operatorId = SecurityContextHolder.getUserId();
        baseService.resetBalance(groupId, currency, newBalance, operatorId, reason);
        return R.ok();
    }

    /**
     * 获取钱包历史记录
     */
    @Operation(summary = "获取钱包历史记录", description = "获取指定群组和币种的钱包历史记录")
    @GetMapping("/history")
    @SaCheckPermission("accounting:wallet:history")
    public R<List<WalletHistoryResp>> getWalletHistory(
            @Parameter(description = "群组ID", example = "1") @RequestParam @NotNull Long groupId,
            @Parameter(description = "币种", example = "CNY") @RequestParam(defaultValue = "CNY") String currency,
            @Parameter(description = "记录数量限制", example = "50") @RequestParam(defaultValue = "50") Integer limit) {
        
        ValidationUtils.throwIf(limit <= 0 || limit > 1000, "记录数量限制必须在1-1000之间");
        
        List<WalletHistoryResp> history = baseService.getWalletHistory(groupId, currency, limit);
        return R.ok(history);
    }
}
