package top.continew.admin.accounting.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cron.CronExpression;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.accounting.mapper.DataSyncScheduleMapper;
import top.continew.admin.accounting.model.entity.DataSyncScheduleDO;
import top.continew.admin.accounting.model.query.DataSyncScheduleQuery;
import top.continew.admin.accounting.model.req.DataSyncScheduleCreateReq;
import top.continew.admin.accounting.model.req.DataSyncScheduleUpdateReq;
import top.continew.admin.accounting.model.resp.DataSyncScheduleDetailResp;
import top.continew.admin.accounting.model.resp.DataSyncScheduleListResp;
import top.continew.admin.accounting.service.DataSyncScheduleService;
import top.continew.admin.accounting.service.DataSyncService;
import top.continew.starter.extension.crud.service.impl.BaseServiceImpl;
import top.continew.starter.core.util.validate.CheckUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;

/**
 * 数据同步计划服务实现
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataSyncScheduleServiceImpl extends BaseServiceImpl<DataSyncScheduleMapper, DataSyncScheduleDO, DataSyncScheduleListResp, DataSyncScheduleDetailResp, DataSyncScheduleQuery, DataSyncScheduleCreateReq> implements DataSyncScheduleService {

    private final DataSyncService dataSyncService;

    @Override
    protected void beforeAdd(DataSyncScheduleCreateReq req, DataSyncScheduleDO entity) {
        // 验证配置
        validateScheduleConfig(req.getScheduleType(), req.getCronExpression(), req.getIntervalSeconds(), req.getScheduledTime());
        
        // 计算下次执行时间
        LocalDateTime nextExecutionTime = calculateNextExecutionTime(entity, LocalDateTime.now());
        entity.setNextExecutionTime(nextExecutionTime);
        
        // 初始化统计字段
        entity.setExecutionCount(0);
        entity.setSuccessCount(0);
        entity.setFailureCount(0);
    }

    @Override
    protected void beforeUpdate(DataSyncScheduleUpdateReq req, DataSyncScheduleDO entity) {
        if (StrUtil.isNotBlank(req.getScheduleType()) || StrUtil.isNotBlank(req.getCronExpression()) 
            || req.getIntervalSeconds() != null || req.getScheduledTime() != null) {
            
            // 验证配置
            String scheduleType = StrUtil.isNotBlank(req.getScheduleType()) ? req.getScheduleType() : entity.getScheduleType();
            String cronExpression = StrUtil.isNotBlank(req.getCronExpression()) ? req.getCronExpression() : entity.getCronExpression();
            Integer intervalSeconds = req.getIntervalSeconds() != null ? req.getIntervalSeconds() : entity.getIntervalSeconds();
            LocalDateTime scheduledTime = req.getScheduledTime() != null ? req.getScheduledTime() : entity.getScheduledTime();
            
            validateScheduleConfig(scheduleType, cronExpression, intervalSeconds, scheduledTime);
            
            // 重新计算下次执行时间
            LocalDateTime nextExecutionTime = calculateNextExecutionTime(entity, LocalDateTime.now());
            entity.setNextExecutionTime(nextExecutionTime);
        }
    }

    // ==================== 计划管理 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableSchedule(Long scheduleId) {
        DataSyncScheduleDO schedule = super.getById(scheduleId);
        CheckUtils.throwIfNull(schedule, "同步计划不存在");
        
        schedule.setEnabled(true);
        LocalDateTime nextExecutionTime = calculateNextExecutionTime(schedule, LocalDateTime.now());
        schedule.setNextExecutionTime(nextExecutionTime);
        
        super.updateById(schedule);
        log.info("启用同步计划: {} - {}", scheduleId, schedule.getScheduleName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableSchedule(Long scheduleId) {
        DataSyncScheduleDO schedule = super.getById(scheduleId);
        CheckUtils.throwIfNull(schedule, "同步计划不存在");
        
        schedule.setEnabled(false);
        schedule.setNextExecutionTime(null);
        
        super.updateById(schedule);
        log.info("禁用同步计划: {} - {}", scheduleId, schedule.getScheduleName());
    }

    @Override
    public Map<String, Object> executeScheduleNow(Long scheduleId) {
        DataSyncScheduleDO schedule = super.getById(scheduleId);
        CheckUtils.throwIfNull(schedule, "同步计划不存在");
        
        try {
            // 执行同步
            Map<String, Object> result = dataSyncService.executeSync(schedule.getConfigId(), null);
            
            // 更新执行状态
            updateExecutionStatus(scheduleId, "SUCCESS", null);
            
            log.info("手动执行同步计划成功: {} - {}", scheduleId, schedule.getScheduleName());
            return result;
            
        } catch (Exception e) {
            log.error("手动执行同步计划失败: {} - {}", scheduleId, schedule.getScheduleName(), e);
            updateExecutionStatus(scheduleId, "FAILED", e.getMessage());
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateEnabled(List<Long> scheduleIds, Boolean enabled) {
        if (CollUtil.isEmpty(scheduleIds)) {
            return;
        }
        
        baseMapper.batchUpdateEnabled(scheduleIds, enabled);
        
        if (enabled) {
            // 重新计算下次执行时间
            List<DataSyncScheduleDO> schedules = super.listByIds(scheduleIds);
            LocalDateTime currentTime = LocalDateTime.now();
            
            for (DataSyncScheduleDO schedule : schedules) {
                LocalDateTime nextExecutionTime = calculateNextExecutionTime(schedule, currentTime);
                if (nextExecutionTime != null) {
                    updateNextExecutionTime(schedule.getId(), nextExecutionTime);
                }
            }
        }
        
        log.info("批量{}同步计划: {}", enabled ? "启用" : "禁用", scheduleIds);
    }

    // ==================== 计划调度 ====================

    @Override
    public List<DataSyncScheduleDO> getPendingSchedules(LocalDateTime currentTime, Integer limit) {
        return baseMapper.selectPendingSchedules(currentTime, limit);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateExecutionStatus(Long scheduleId, String status, String errorMessage) {
        baseMapper.updateExecutionStatus(scheduleId, status, errorMessage, LocalDateTime.now());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateNextExecutionTime(Long scheduleId, LocalDateTime nextExecutionTime) {
        baseMapper.updateNextExecutionTime(scheduleId, nextExecutionTime);
    }

    @Override
    public LocalDateTime calculateNextExecutionTime(DataSyncScheduleDO schedule, LocalDateTime currentTime) {
        if (!schedule.getEnabled()) {
            return null;
        }
        
        String scheduleType = schedule.getScheduleType();
        ZoneId zoneId = ZoneId.of(schedule.getTimezone());
        ZonedDateTime zonedCurrentTime = currentTime.atZone(zoneId);
        
        switch (scheduleType) {
            case "CRON":
                return calculateCronNextTime(schedule.getCronExpression(), zonedCurrentTime);
            case "INTERVAL":
                return calculateIntervalNextTime(schedule.getIntervalSeconds(), currentTime);
            case "ONE_TIME":
                return calculateOneTimeNextTime(schedule.getScheduledTime(), currentTime);
            default:
                log.warn("未知的计划类型: {}", scheduleType);
                return null;
        }
    }

    // ==================== 执行历史 ====================

    @Override
    public List<Map<String, Object>> getExecutionHistory(Long scheduleId, Integer limit) {
        return baseMapper.selectExecutionHistory(scheduleId, limit);
    }

    @Override
    public Map<String, Object> getExecutionStatistics(Long scheduleId, String startDate, String endDate) {
        return baseMapper.selectExecutionStatistics(scheduleId, startDate, endDate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer cleanupExecutionHistory(Long scheduleId, Integer retentionDays) {
        return baseMapper.cleanupExecutionHistory(scheduleId, retentionDays);
    }

    // ==================== 监控告警 ====================

    @Override
    public List<Map<String, Object>> getScheduleHealthStatus(Long groupId) {
        return baseMapper.selectScheduleHealthStatus(groupId);
    }

    @Override
    public List<Map<String, Object>> getFailedSchedules(Long groupId, Integer hours) {
        return baseMapper.selectFailedSchedules(groupId, hours);
    }

    @Override
    public List<Map<String, Object>> getTimeoutSchedules(Long groupId) {
        return baseMapper.selectTimeoutSchedules(groupId);
    }

    @Override
    public void sendAlert(Long scheduleId, String alertType, String message) {
        // TODO: 实现告警通知逻辑
        log.warn("同步计划告警 - 计划ID: {}, 类型: {}, 消息: {}", scheduleId, alertType, message);
    }

    // ==================== 配置验证 ====================

    @Override
    public Map<String, Object> validateCronExpression(String cronExpression) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            CronExpression cron = CronExpression.parse(cronExpression);
            result.put("valid", true);
            result.put("message", "Cron表达式有效");
            
            // 预览下几次执行时间
            List<LocalDateTime> nextTimes = new ArrayList<>();
            LocalDateTime time = LocalDateTime.now();
            for (int i = 0; i < 5; i++) {
                time = cron.next(time);
                if (time != null) {
                    nextTimes.add(time);
                } else {
                    break;
                }
            }
            result.put("nextExecutionTimes", nextTimes);
            
        } catch (Exception e) {
            result.put("valid", false);
            result.put("message", "Cron表达式无效: " + e.getMessage());
        }
        
        return result;
    }

    @Override
    public List<LocalDateTime> previewExecutionTimes(String scheduleType, String cronExpression, 
                                                   Integer intervalSeconds, String timezone, Integer count) {
        List<LocalDateTime> times = new ArrayList<>();
        
        if (count == null || count <= 0) {
            count = 5;
        }
        
        ZoneId zoneId = StrUtil.isNotBlank(timezone) ? ZoneId.of(timezone) : ZoneId.systemDefault();
        LocalDateTime currentTime = LocalDateTime.now();
        
        try {
            switch (scheduleType) {
                case "CRON":
                    if (StrUtil.isNotBlank(cronExpression)) {
                        CronExpression cron = CronExpression.parse(cronExpression);
                        LocalDateTime time = currentTime;
                        for (int i = 0; i < count; i++) {
                            time = cron.next(time);
                            if (time != null) {
                                times.add(time);
                            } else {
                                break;
                            }
                        }
                    }
                    break;
                case "INTERVAL":
                    if (intervalSeconds != null && intervalSeconds > 0) {
                        LocalDateTime time = currentTime;
                        for (int i = 0; i < count; i++) {
                            time = time.plusSeconds(intervalSeconds);
                            times.add(time);
                        }
                    }
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error("预览执行时间失败", e);
        }
        
        return times;
    }

    // ==================== 统计分析 ====================

    @Override
    public Map<String, Object> getScheduleStatistics(Long groupId) {
        return baseMapper.selectScheduleStatistics(groupId);
    }

    @Override
    public List<Map<String, Object>> getExecutionTrends(Long groupId, String startDate, String endDate, String groupBy) {
        return baseMapper.selectExecutionTrends(groupId, startDate, endDate, groupBy);
    }

    @Override
    public List<Map<String, Object>> getPerformanceAnalysis(Long groupId, String startDate, String endDate) {
        return baseMapper.selectPerformanceAnalysis(groupId, startDate, endDate);
    }

    // ==================== 私有方法 ====================

    /**
     * 验证计划配置
     */
    private void validateScheduleConfig(String scheduleType, String cronExpression, Integer intervalSeconds, LocalDateTime scheduledTime) {
        switch (scheduleType) {
            case "CRON":
                CheckUtils.throwIfBlank(cronExpression, "Cron表达式不能为空");
                try {
                    CronExpression.parse(cronExpression);
                } catch (Exception e) {
                    CheckUtils.throwIf(true, "Cron表达式格式错误: " + e.getMessage());
                }
                break;
            case "INTERVAL":
                CheckUtils.throwIfNull(intervalSeconds, "间隔秒数不能为空");
                CheckUtils.throwIf(intervalSeconds <= 0, "间隔秒数必须大于0");
                break;
            case "ONE_TIME":
                CheckUtils.throwIfNull(scheduledTime, "计划执行时间不能为空");
                CheckUtils.throwIf(scheduledTime.isBefore(LocalDateTime.now()), "计划执行时间不能早于当前时间");
                break;
            default:
                CheckUtils.throwIf(true, "不支持的计划类型: " + scheduleType);
        }
    }

    /**
     * 计算Cron下次执行时间
     */
    private LocalDateTime calculateCronNextTime(String cronExpression, ZonedDateTime currentTime) {
        try {
            CronExpression cron = CronExpression.parse(cronExpression);
            LocalDateTime nextTime = cron.next(currentTime.toLocalDateTime());
            return nextTime;
        } catch (Exception e) {
            log.error("计算Cron下次执行时间失败: {}", cronExpression, e);
            return null;
        }
    }

    /**
     * 计算间隔下次执行时间
     */
    private LocalDateTime calculateIntervalNextTime(Integer intervalSeconds, LocalDateTime currentTime) {
        if (intervalSeconds == null || intervalSeconds <= 0) {
            return null;
        }
        return currentTime.plusSeconds(intervalSeconds);
    }

    /**
     * 计算一次性下次执行时间
     */
    private LocalDateTime calculateOneTimeNextTime(LocalDateTime scheduledTime, LocalDateTime currentTime) {
        if (scheduledTime == null || scheduledTime.isBefore(currentTime)) {
            return null;
        }
        return scheduledTime;
    }

}
