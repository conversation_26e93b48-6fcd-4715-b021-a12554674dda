package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.accounting.enums.TransactionType;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 分类详情响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "分类详情响应")
public class CategoryDetailResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Schema(description = "ID", example = "1")
    private Long id;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 群组名称
     */
    @Schema(description = "群组名称", example = "我的记账群")
    private String groupName;

    /**
     * 分类名称
     */
    @Schema(description = "分类名称", example = "餐饮")
    private String name;

    /**
     * 父分类ID
     */
    @Schema(description = "父分类ID", example = "1")
    private Long parentId;

    /**
     * 父分类名称
     */
    @Schema(description = "父分类名称", example = "生活消费")
    private String parentName;

    /**
     * 分类类型
     */
    @Schema(description = "分类类型", example = "EXPENSE")
    private TransactionType type;

    /**
     * 图标
     */
    @Schema(description = "图标", example = "icon-food")
    private String icon;

    /**
     * 颜色
     */
    @Schema(description = "颜色", example = "#FF5722")
    private String color;

    /**
     * 描述
     */
    @Schema(description = "描述", example = "餐饮相关支出")
    private String description;

    /**
     * 排序
     */
    @Schema(description = "排序", example = "1")
    private Integer sort;

    /**
     * 祖先路径
     */
    @Schema(description = "祖先路径", example = "0,1")
    private String ancestors;

    /**
     * 是否为系统分类
     */
    @Schema(description = "是否为系统分类", example = "false")
    private Boolean isSystem;

    /**
     * 是否为默认分类
     */
    @Schema(description = "是否为默认分类", example = "false")
    private Boolean isDefault;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "1")
    private Integer status;

    /**
     * 子分类数量
     */
    @Schema(description = "子分类数量", example = "3")
    private Integer childrenCount;

    /**
     * 使用次数
     */
    @Schema(description = "使用次数", example = "15")
    private Integer usageCount;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID", example = "1")
    private Long createUser;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名", example = "张三")
    private String createUserName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-01-01T10:00:00")
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID", example = "1")
    private Long updateUser;

    /**
     * 更新人姓名
     */
    @Schema(description = "更新人姓名", example = "李四")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2025-01-01T12:00:00")
    private LocalDateTime updateTime;
}
