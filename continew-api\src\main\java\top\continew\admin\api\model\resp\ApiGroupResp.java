package top.continew.admin.api.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * API群组响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "API群组响应")
public class ApiGroupResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long id;

    /**
     * 群组名称
     */
    @Schema(description = "群组名称", example = "我的记账群组")
    private String name;

    /**
     * 群组描述
     */
    @Schema(description = "群组描述", example = "用于家庭日常开支记录")
    private String description;

    /**
     * 群组头像
     */
    @Schema(description = "群组头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;

    /**
     * 默认货币
     */
    @Schema(description = "默认货币", example = "CNY")
    private String defaultCurrency;

    /**
     * 是否公开
     */
    @Schema(description = "是否公开群组", example = "false")
    private Boolean isPublic;

    /**
     * 群组状态
     */
    @Schema(description = "群组状态", example = "ACTIVE")
    private String status;

    /**
     * 创建者ID
     */
    @Schema(description = "创建者ID", example = "1")
    private Long creatorId;

    /**
     * 创建者名称
     */
    @Schema(description = "创建者名称", example = "张三")
    private String creatorName;

    /**
     * 成员数量
     */
    @Schema(description = "成员数量", example = "5")
    private Integer memberCount;

    /**
     * 当前用户角色
     */
    @Schema(description = "当前用户在群组中的角色", example = "OWNER")
    private String currentUserRole;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 群组设置
     */
    @Schema(description = "群组设置")
    private GroupSettings settings;

    /**
     * 群组统计
     */
    @Schema(description = "群组统计")
    private GroupStatistics statistics;

    /**
     * 成员信息
     */
    @Data
    @Schema(description = "成员信息")
    public static class MemberInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 用户ID
         */
        @Schema(description = "用户ID", example = "1")
        private Long userId;

        /**
         * 用户名
         */
        @Schema(description = "用户名", example = "张三")
        private String username;

        /**
         * 昵称
         */
        @Schema(description = "昵称", example = "小张")
        private String nickname;

        /**
         * 头像
         */
        @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
        private String avatar;

        /**
         * 角色
         */
        @Schema(description = "角色", example = "MEMBER")
        private String role;

        /**
         * 加入时间
         */
        @Schema(description = "加入时间")
        private LocalDateTime joinTime;

        /**
         * 最后活跃时间
         */
        @Schema(description = "最后活跃时间")
        private LocalDateTime lastActiveTime;

        /**
         * 是否在线
         */
        @Schema(description = "是否在线", example = "true")
        private Boolean isOnline;
    }

    /**
     * 群组设置
     */
    @Data
    @Schema(description = "群组设置")
    public static class GroupSettings implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 是否允许成员邀请
         */
        @Schema(description = "是否允许成员邀请其他用户", example = "true")
        private Boolean allowMemberInvite;

        /**
         * 是否需要审批加入
         */
        @Schema(description = "是否需要审批加入", example = "false")
        private Boolean requireApproval;

        /**
         * 是否允许成员创建分类
         */
        @Schema(description = "是否允许成员创建分类", example = "true")
        private Boolean allowMemberCreateCategory;

        /**
         * 是否允许成员创建标签
         */
        @Schema(description = "是否允许成员创建标签", example = "true")
        private Boolean allowMemberCreateTag;

        /**
         * 是否启用预算功能
         */
        @Schema(description = "是否启用预算功能", example = "true")
        private Boolean enableBudget;

        /**
         * 是否启用债务跟踪
         */
        @Schema(description = "是否启用债务跟踪", example = "true")
        private Boolean enableDebtTracking;

        /**
         * 是否启用定期报告
         */
        @Schema(description = "是否启用定期报告", example = "true")
        private Boolean enablePeriodicReport;

        /**
         * 报告频率
         */
        @Schema(description = "报告频率", example = "WEEKLY")
        private String reportFrequency;

        /**
         * 时区
         */
        @Schema(description = "时区", example = "Asia/Shanghai")
        private String timezone;

        /**
         * 语言
         */
        @Schema(description = "语言", example = "zh-CN")
        private String language;
    }

    /**
     * 群组统计
     */
    @Data
    @Schema(description = "群组统计")
    public static class GroupStatistics implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 总交易数
         */
        @Schema(description = "总交易数", example = "150")
        private Long totalTransactions;

        /**
         * 总收入
         */
        @Schema(description = "总收入", example = "10000.00")
        private BigDecimal totalIncome;

        /**
         * 总支出
         */
        @Schema(description = "总支出", example = "8500.00")
        private BigDecimal totalExpense;

        /**
         * 净收入
         */
        @Schema(description = "净收入", example = "1500.00")
        private BigDecimal netIncome;

        /**
         * 本月交易数
         */
        @Schema(description = "本月交易数", example = "25")
        private Long monthlyTransactions;

        /**
         * 本月收入
         */
        @Schema(description = "本月收入", example = "3000.00")
        private BigDecimal monthlyIncome;

        /**
         * 本月支出
         */
        @Schema(description = "本月支出", example = "2500.00")
        private BigDecimal monthlyExpense;

        /**
         * 活跃成员数
         */
        @Schema(description = "活跃成员数", example = "4")
        private Integer activeMembers;

        /**
         * 最后交易时间
         */
        @Schema(description = "最后交易时间")
        private LocalDateTime lastTransactionTime;
    }

    /**
     * 活动日志
     */
    @Data
    @Schema(description = "活动日志")
    public static class ActivityLog implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 日志ID
         */
        @Schema(description = "日志ID", example = "1")
        private Long id;

        /**
         * 操作类型
         */
        @Schema(description = "操作类型", example = "CREATE_TRANSACTION")
        private String actionType;

        /**
         * 操作描述
         */
        @Schema(description = "操作描述", example = "创建了一笔支出记录")
        private String description;

        /**
         * 操作用户ID
         */
        @Schema(description = "操作用户ID", example = "1")
        private Long userId;

        /**
         * 操作用户名
         */
        @Schema(description = "操作用户名", example = "张三")
        private String username;

        /**
         * 操作时间
         */
        @Schema(description = "操作时间")
        private LocalDateTime actionTime;

        /**
         * 相关对象ID
         */
        @Schema(description = "相关对象ID", example = "123")
        private Long relatedId;

        /**
         * 相关对象类型
         */
        @Schema(description = "相关对象类型", example = "TRANSACTION")
        private String relatedType;
    }
}
