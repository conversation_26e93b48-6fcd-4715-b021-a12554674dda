<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.accounting.mapper.ZapierConfigMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="top.continew.admin.accounting.model.entity.ZapierConfigDO">
        <id column="id" property="id" />
        <result column="group_id" property="groupId" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="webhook_url" property="webhookUrl" />
        <result column="trigger_type" property="triggerType" />
        <result column="trigger_conditions" property="triggerConditions" />
        <result column="data_mapping" property="dataMapping" />
        <result column="filter_rules" property="filterRules" />
        <result column="headers" property="headers" />
        <result column="timeout_seconds" property="timeoutSeconds" />
        <result column="max_retries" property="maxRetries" />
        <result column="retry_interval" property="retryInterval" />
        <result column="enabled" property="enabled" />
        <result column="status" property="status" />
        <result column="trigger_count" property="triggerCount" />
        <result column="success_count" property="successCount" />
        <result column="failure_count" property="failureCount" />
        <result column="last_triggered_at" property="lastTriggeredAt" />
        <result column="last_error" property="lastError" />
        <result column="last_error_at" property="lastErrorAt" />
        <result column="retry_count" property="retryCount" />
        <result column="tags" property="tags" />
        <result column="priority" property="priority" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 根据群组ID查询配置列表 -->
    <select id="selectByGroupId" resultMap="BaseResultMap">
        SELECT * FROM acc_zapier_config
        WHERE group_id = #{groupId}
        ORDER BY priority DESC, create_time DESC
    </select>

    <!-- 查询启用的配置 -->
    <select id="selectEnabledConfigs" resultMap="BaseResultMap">
        SELECT * FROM acc_zapier_config
        WHERE enabled = 1 AND status = 'ACTIVE'
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        <if test="triggerType != null and triggerType != ''">
            AND trigger_type = #{triggerType}
        </if>
        ORDER BY priority DESC, create_time DESC
    </select>

    <!-- 查询所有启用的配置 -->
    <select id="selectAllEnabledConfigs" resultMap="BaseResultMap">
        SELECT * FROM acc_zapier_config
        WHERE enabled = 1 AND status = 'ACTIVE'
        ORDER BY priority DESC, create_time DESC
    </select>

    <!-- 更新配置状态 -->
    <update id="updateStatus">
        UPDATE acc_zapier_config
        SET status = #{status}, update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新触发统计 -->
    <update id="updateTriggerStats">
        UPDATE acc_zapier_config
        SET trigger_count = #{triggerCount},
            success_count = #{successCount},
            failure_count = #{failureCount},
            last_triggered_at = #{lastTriggered},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新错误信息 -->
    <update id="updateError">
        UPDATE acc_zapier_config
        SET last_error = #{error},
            last_error_at = #{errorTime},
            status = 'ERROR',
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 查询配置统计信息 -->
    <select id="selectConfigStats" resultType="map">
        SELECT
            COUNT(*) as total_count,
            SUM(CASE WHEN enabled = 1 THEN 1 ELSE 0 END) as enabled_count,
            SUM(CASE WHEN status = 'ACTIVE' THEN 1 ELSE 0 END) as active_count,
            SUM(CASE WHEN status = 'ERROR' THEN 1 ELSE 0 END) as error_count,
            SUM(CASE WHEN status = 'INACTIVE' THEN 1 ELSE 0 END) as inactive_count,
            COALESCE(SUM(trigger_count), 0) as total_triggers,
            COALESCE(SUM(success_count), 0) as total_success,
            COALESCE(SUM(failure_count), 0) as total_failures,
            CASE
                WHEN SUM(trigger_count) > 0 THEN ROUND(SUM(success_count) * 100.0 / SUM(trigger_count), 2)
                ELSE 0
            END as success_rate
        FROM acc_zapier_config
        WHERE group_id = #{groupId}
    </select>

    <!-- 查询触发器类型统计 -->
    <select id="selectTriggerTypeStats" resultType="map">
        SELECT
            trigger_type,
            COUNT(*) as config_count,
            SUM(CASE WHEN enabled = 1 THEN 1 ELSE 0 END) as enabled_count,
            COALESCE(SUM(trigger_count), 0) as total_triggers,
            COALESCE(SUM(success_count), 0) as total_success,
            COALESCE(SUM(failure_count), 0) as total_failures,
            CASE
                WHEN SUM(trigger_count) > 0 THEN ROUND(SUM(success_count) * 100.0 / SUM(trigger_count), 2)
                ELSE 0
            END as success_rate
        FROM acc_zapier_config
        WHERE group_id = #{groupId}
        GROUP BY trigger_type
        ORDER BY total_triggers DESC
    </select>

    <!-- 查询活跃配置排行 -->
    <select id="selectActiveConfigRanking" resultType="map">
        SELECT
            id,
            name,
            trigger_type,
            trigger_count,
            success_count,
            failure_count,
            CASE
                WHEN trigger_count > 0 THEN ROUND(success_count * 100.0 / trigger_count, 2)
                ELSE 0
            END as success_rate,
            last_triggered_at
        FROM acc_zapier_config
        WHERE group_id = #{groupId} AND enabled = 1
        ORDER BY trigger_count DESC, success_rate DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询性能统计 -->
    <select id="selectPerformanceStats" resultType="map">
        SELECT
            c.id,
            c.name,
            c.trigger_type,
            COUNT(l.id) as execution_count,
            AVG(l.execution_time) as avg_execution_time,
            MIN(l.execution_time) as min_execution_time,
            MAX(l.execution_time) as max_execution_time,
            SUM(CASE WHEN l.status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
            SUM(CASE WHEN l.status = 'FAILED' THEN 1 ELSE 0 END) as failure_count,
            CASE
                WHEN COUNT(l.id) > 0 THEN ROUND(SUM(CASE WHEN l.status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(l.id), 2)
                ELSE 0
            END as success_rate
        FROM acc_zapier_config c
        LEFT JOIN acc_zapier_log l ON c.id = l.config_id
            AND l.executed_at BETWEEN #{startTime} AND #{endTime}
        WHERE c.group_id = #{groupId}
        GROUP BY c.id, c.name, c.trigger_type
        HAVING execution_count > 0
        ORDER BY execution_count DESC, success_rate DESC
    </select>

    <!-- 查询错误配置 -->
    <select id="selectErrorConfigs" resultMap="BaseResultMap">
        SELECT * FROM acc_zapier_config
        WHERE group_id = #{groupId}
        AND (status = 'ERROR' OR last_error_at >= DATE_SUB(NOW(), INTERVAL #{hours} HOUR))
        ORDER BY last_error_at DESC
    </select>

    <!-- 查询长时间未触发的配置 -->
    <select id="selectInactiveConfigs" resultMap="BaseResultMap">
        SELECT * FROM acc_zapier_config
        WHERE group_id = #{groupId}
        AND enabled = 1
        AND (last_triggered_at IS NULL OR last_triggered_at <= DATE_SUB(NOW(), INTERVAL #{days} DAY))
        ORDER BY last_triggered_at ASC
    </select>

    <!-- 批量更新配置状态 -->
    <update id="batchUpdateStatus">
        UPDATE acc_zapier_config
        SET status = #{status}, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量更新配置启用状态 -->
    <update id="batchUpdateEnabled">
        UPDATE acc_zapier_config
        SET enabled = #{enabled},
            status = CASE WHEN #{enabled} = 1 THEN 'ACTIVE' ELSE 'INACTIVE' END,
            update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 查询配置的触发历史趋势 -->
    <select id="selectTriggerTrend" resultType="map">
        SELECT
            DATE_FORMAT(l.executed_at, '%Y-%m-%d %H:00:00') as time_point,
            COUNT(*) as execution_count,
            SUM(CASE WHEN l.status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
            SUM(CASE WHEN l.status = 'FAILED' THEN 1 ELSE 0 END) as failure_count,
            AVG(l.execution_time) as avg_execution_time
        FROM acc_zapier_log l
        WHERE l.config_id = #{configId}
        AND l.executed_at BETWEEN #{startTime} AND #{endTime}
        GROUP BY DATE_FORMAT(l.executed_at, '%Y-%m-%d %H:00:00')
        ORDER BY time_point
    </select>

    <!-- 重置配置统计 -->
    <update id="resetStats">
        UPDATE acc_zapier_config
        SET trigger_count = 0,
            success_count = 0,
            failure_count = 0,
            last_triggered_at = NULL,
            last_error = NULL,
            last_error_at = NULL,
            retry_count = 0,
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 查询即将过期的配置 -->
    <select id="selectExpiringConfigs" resultMap="BaseResultMap">
        SELECT * FROM acc_zapier_config
        WHERE enabled = 1
        AND last_triggered_at IS NOT NULL
        AND last_triggered_at <= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        ORDER BY last_triggered_at ASC
    </select>

    <!-- 查询高频触发配置 -->
    <select id="selectHighFrequencyConfigs" resultMap="BaseResultMap">
        SELECT c.* FROM acc_zapier_config c
        WHERE c.group_id = #{groupId}
        AND c.enabled = 1
        AND (
            SELECT COUNT(*)
            FROM acc_zapier_log l
            WHERE l.config_id = c.id
            AND l.executed_at >= DATE_SUB(NOW(), INTERVAL #{hours} HOUR)
        ) >= #{threshold}
        ORDER BY c.trigger_count DESC
    </select>

    <!-- 查询配置健康状态 -->
    <select id="selectConfigHealth" resultType="map">
        SELECT
            c.id,
            c.name,
            c.enabled,
            c.status,
            c.trigger_count,
            c.success_count,
            c.failure_count,
            c.last_triggered_at,
            c.last_error_at,
            CASE
                WHEN c.trigger_count > 0 THEN ROUND(c.success_count * 100.0 / c.trigger_count, 2)
                ELSE 0
            END as success_rate,
            CASE
                WHEN c.last_triggered_at IS NULL THEN 'NEVER_TRIGGERED'
                WHEN c.last_triggered_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 'HEALTHY'
                WHEN c.last_triggered_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 'WARNING'
                ELSE 'CRITICAL'
            END as health_status,
            (
                SELECT COUNT(*)
                FROM acc_zapier_log l
                WHERE l.config_id = c.id
                AND l.status = 'FAILED'
                AND l.executed_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            ) as recent_failures,
            (
                SELECT AVG(l.execution_time)
                FROM acc_zapier_log l
                WHERE l.config_id = c.id
                AND l.executed_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            ) as avg_execution_time_24h
        FROM acc_zapier_config c
        WHERE c.id = #{id}
    </select>

    <!-- 查询配置的触发器条件 -->
    <select id="selectTriggerConditions" resultType="map">
        SELECT
            id,
            name,
            trigger_type,
            trigger_conditions,
            enabled,
            status
        FROM acc_zapier_config
        WHERE group_id = #{groupId}
        AND trigger_type = #{triggerType}
        AND enabled = 1
        ORDER BY priority DESC
    </select>

    <!-- 查询配置的数据映射规则 -->
    <select id="selectDataMappingRules" resultType="map">
        SELECT
            id,
            name,
            data_mapping,
            filter_rules
        FROM acc_zapier_config
        WHERE id = #{configId}
    </select>

    <!-- 更新配置的最后执行时间 -->
    <update id="updateLastTriggered">
        UPDATE acc_zapier_config
        SET last_triggered_at = #{lastTriggered},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 增加配置的重试次数 -->
    <update id="incrementRetryCount">
        UPDATE acc_zapier_config
        SET retry_count = retry_count + 1,
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 查询需要重试的配置 -->
    <select id="selectRetryConfigs" resultMap="BaseResultMap">
        SELECT c.* FROM acc_zapier_config c
        INNER JOIN acc_zapier_log l ON c.id = l.config_id
        WHERE l.status = 'FAILED'
        AND l.retry_count &lt; c.max_retries
        AND l.executed_at &lt;= DATE_SUB(NOW(), INTERVAL c.retry_interval MINUTE)
        AND c.enabled = 1
        GROUP BY c.id
        ORDER BY l.executed_at ASC
    </select>

    <!-- 查询配置的执行频率统计 -->
    <select id="selectExecutionFrequency" resultType="map">
        SELECT
            c.id,
            c.name,
            COUNT(l.id) as execution_count,
            COUNT(DISTINCT DATE(l.executed_at)) as active_days,
            CASE
                WHEN COUNT(DISTINCT DATE(l.executed_at)) > 0
                THEN ROUND(COUNT(l.id) / COUNT(DISTINCT DATE(l.executed_at)), 2)
                ELSE 0
            END as avg_executions_per_day
        FROM acc_zapier_config c
        LEFT JOIN acc_zapier_log l ON c.id = l.config_id
            AND l.executed_at >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        WHERE c.group_id = #{groupId}
        GROUP BY c.id, c.name
        ORDER BY execution_count DESC
    </select>

    <!-- 查询配置的标签统计 -->
    <select id="selectTagStats" resultType="map">
        SELECT
            tag,
            COUNT(*) as config_count,
            SUM(CASE WHEN enabled = 1 THEN 1 ELSE 0 END) as enabled_count,
            SUM(trigger_count) as total_triggers
        FROM (
            SELECT
                JSON_UNQUOTE(JSON_EXTRACT(tags, CONCAT('$[', numbers.n, ']'))) as tag,
                enabled,
                trigger_count
            FROM acc_zapier_config
            CROSS JOIN (
                SELECT 0 as n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4
                UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9
            ) numbers
            WHERE group_id = #{groupId}
            AND JSON_LENGTH(tags) > numbers.n
            AND tags IS NOT NULL
        ) tag_data
        WHERE tag IS NOT NULL
        GROUP BY tag
        ORDER BY config_count DESC
    </select>

</mapper>
