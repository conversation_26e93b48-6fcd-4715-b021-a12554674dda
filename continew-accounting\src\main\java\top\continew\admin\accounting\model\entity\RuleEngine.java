package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.admin.common.model.entity.BaseEntity;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 规则引擎实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("acc_rule_engine")
public class RuleEngine extends BaseEntity {

    /**
     * 规则ID
     */
    @TableId
    private Long ruleId;

    /**
     * 群组ID
     */
    private Long groupId;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则描述
     */
    private String ruleDescription;

    /**
     * 规则类型
     */
    private String ruleType;

    /**
     * 规则状态
     */
    private String ruleStatus;

    /**
     * 规则优先级
     */
    private Integer priority;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 版本号
     */
    private String version;

    /**
     * 触发条件配置JSON
     */
    private String triggerConditionJson;

    /**
     * 执行动作配置JSON
     */
    private String executionActionsJson;

    /**
     * 调度配置JSON
     */
    private String scheduleConfigJson;

    /**
     * 通知配置JSON
     */
    private String notificationConfigJson;

    /**
     * 标签JSON
     */
    private String tagsJson;

    /**
     * 扩展属性JSON
     */
    private String attributesJson;

    /**
     * 触发类型
     */
    private String triggerType;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 执行次数
     */
    private Integer executionCount;

    /**
     * 成功次数
     */
    private Integer successCount;

    /**
     * 失败次数
     */
    private Integer failureCount;

    /**
     * 成功率
     */
    private Double successRate;

    /**
     * 平均执行时间（毫秒）
     */
    private Long avgExecutionTime;

    /**
     * 最大执行时间（毫秒）
     */
    private Long maxExecutionTime;

    /**
     * 最小执行时间（毫秒）
     */
    private Long minExecutionTime;

    /**
     * 最后执行时间
     */
    private LocalDateTime lastExecutionTime;

    /**
     * 最后执行状态
     */
    private String lastExecutionStatus;

    /**
     * 最后执行结果
     */
    private String lastExecutionResult;

    /**
     * 下次执行时间
     */
    private LocalDateTime nextExecutionTime;

    /**
     * 规则复杂度
     */
    private String complexity;

    /**
     * 预估匹配率
     */
    private Double estimatedMatchRate;

    /**
     * 实际匹配率
     */
    private Double actualMatchRate;

    /**
     * 错误率
     */
    private Double errorRate;

    /**
     * 最后错误信息
     */
    private String lastErrorMessage;

    /**
     * 最后错误时间
     */
    private LocalDateTime lastErrorTime;

    /**
     * 是否有调度配置
     */
    private Boolean hasSchedule;

    /**
     * 调度状态
     */
    private String scheduleStatus;

    /**
     * 是否有通知配置
     */
    private Boolean hasNotification;

    /**
     * 通知状态
     */
    private String notificationStatus;

    /**
     * 最后通知时间
     */
    private LocalDateTime lastNotificationTime;

    /**
     * 通知发送次数
     */
    private Integer notificationCount;

    /**
     * 配置哈希值
     */
    private String configHash;

    /**
     * 是否已发布
     */
    private Boolean published;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 发布人ID
     */
    private Long publishedBy;

    /**
     * 是否系统规则
     */
    private Boolean isSystemRule;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 备注
     */
    private String remark;

    /**
     * 标签列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<String> tags;

    /**
     * 扩展属性（非数据库字段）
     */
    @TableField(exist = false)
    private Object attributes;

    /**
     * 触发条件配置（非数据库字段）
     */
    @TableField(exist = false)
    private Object triggerCondition;

    /**
     * 执行动作配置（非数据库字段）
     */
    @TableField(exist = false)
    private Object executionActions;

    /**
     * 调度配置（非数据库字段）
     */
    @TableField(exist = false)
    private Object scheduleConfig;

    /**
     * 通知配置（非数据库字段）
     */
    @TableField(exist = false)
    private Object notificationConfig;

    /**
     * 创建人姓名（非数据库字段）
     */
    @TableField(exist = false)
    private String createdByName;

    /**
     * 更新人姓名（非数据库字段）
     */
    @TableField(exist = false)
    private String updatedByName;

    /**
     * 发布人姓名（非数据库字段）
     */
    @TableField(exist = false)
    private String publishedByName;

    /**
     * 今日执行次数（非数据库字段）
     */
    @TableField(exist = false)
    private Integer todayExecutions;

    /**
     * 本周执行次数（非数据库字段）
     */
    @TableField(exist = false)
    private Integer weekExecutions;

    /**
     * 本月执行次数（非数据库字段）
     */
    @TableField(exist = false)
    private Integer monthExecutions;

    /**
     * 今日成功次数（非数据库字段）
     */
    @TableField(exist = false)
    private Integer todaySuccesses;

    /**
     * 本周成功次数（非数据库字段）
     */
    @TableField(exist = false)
    private Integer weekSuccesses;

    /**
     * 本月成功次数（非数据库字段）
     */
    @TableField(exist = false)
    private Integer monthSuccesses;

    /**
     * 今日成功率（非数据库字段）
     */
    @TableField(exist = false)
    private Double todaySuccessRate;

    /**
     * 本周成功率（非数据库字段）
     */
    @TableField(exist = false)
    private Double weekSuccessRate;

    /**
     * 本月成功率（非数据库字段）
     */
    @TableField(exist = false)
    private Double monthSuccessRate;

    /**
     * 执行趋势（非数据库字段）
     */
    @TableField(exist = false)
    private String executionTrend;

    /**
     * 是否活跃（非数据库字段）
     */
    @TableField(exist = false)
    private Boolean isActive;

    /**
     * 性能等级（非数据库字段）
     */
    @TableField(exist = false)
    private String performanceGrade;

    /**
     * 性能评分（非数据库字段）
     */
    @TableField(exist = false)
    private Integer performanceScore;

    /**
     * 资源使用率（非数据库字段）
     */
    @TableField(exist = false)
    private Object resourceUsage;

    /**
     * 响应时间分布（非数据库字段）
     */
    @TableField(exist = false)
    private Object responseTimeDistribution;

    /**
     * 吞吐量（次/分钟）（非数据库字段）
     */
    @TableField(exist = false)
    private Double throughput;
}
