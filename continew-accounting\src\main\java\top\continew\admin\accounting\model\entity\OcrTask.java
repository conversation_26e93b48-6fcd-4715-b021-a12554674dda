package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.admin.common.model.entity.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * OCR任务实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("acc_ocr_task")
public class OcrTask extends BaseEntity {

    /**
     * 任务ID
     */
    @TableId(value = "task_id", type = IdType.AUTO)
    private Long taskId;

    /**
     * 群组ID
     */
    @TableField("group_id")
    private Long groupId;

    /**
     * 任务编号
     */
    @TableField("task_number")
    private String taskNumber;

    /**
     * 任务状态
     */
    @TableField("status")
    private String status;

    /**
     * OCR引擎类型
     */
    @TableField("ocr_engine")
    private String ocrEngine;

    /**
     * 识别模式
     */
    @TableField("recognition_mode")
    private String recognitionMode;

    /**
     * 语言
     */
    @TableField("language")
    private String language;

    /**
     * 识别精度
     */
    @TableField("accuracy")
    private String accuracy;

    /**
     * 原始文件名
     */
    @TableField("original_file_name")
    private String originalFileName;

    /**
     * 文件大小（字节）
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 图片宽度
     */
    @TableField("image_width")
    private Integer imageWidth;

    /**
     * 图片高度
     */
    @TableField("image_height")
    private Integer imageHeight;

    /**
     * 图片格式
     */
    @TableField("image_format")
    private String imageFormat;

    /**
     * 存储路径
     */
    @TableField("storage_path")
    private String storagePath;

    /**
     * 访问URL
     */
    @TableField("access_url")
    private String accessUrl;

    /**
     * 缩略图URL
     */
    @TableField("thumbnail_url")
    private String thumbnailUrl;

    /**
     * 识别开始时间
     */
    @TableField("recognition_start_time")
    private LocalDateTime recognitionStartTime;

    /**
     * 识别结束时间
     */
    @TableField("recognition_end_time")
    private LocalDateTime recognitionEndTime;

    /**
     * 处理时间（毫秒）
     */
    @TableField("processing_time")
    private Long processingTime;

    /**
     * 识别置信度
     */
    @TableField("confidence")
    private Double confidence;

    /**
     * 商家名称
     */
    @TableField("merchant_name")
    private String merchantName;

    /**
     * 商家地址
     */
    @TableField("merchant_address")
    private String merchantAddress;

    /**
     * 商家电话
     */
    @TableField("merchant_phone")
    private String merchantPhone;

    /**
     * 交易时间
     */
    @TableField("transaction_time")
    private LocalDateTime transactionTime;

    /**
     * 总金额
     */
    @TableField("total_amount")
    private BigDecimal totalAmount;

    /**
     * 税额
     */
    @TableField("tax_amount")
    private BigDecimal taxAmount;

    /**
     * 小费
     */
    @TableField("tip_amount")
    private BigDecimal tipAmount;

    /**
     * 折扣金额
     */
    @TableField("discount_amount")
    private BigDecimal discountAmount;

    /**
     * 货币类型
     */
    @TableField("currency")
    private String currency;

    /**
     * 支付方式
     */
    @TableField("payment_method")
    private String paymentMethod;

    /**
     * 卡号后四位
     */
    @TableField("card_last_four")
    private String cardLastFour;

    /**
     * 收据编号
     */
    @TableField("receipt_number")
    private String receiptNumber;

    /**
     * 订单编号
     */
    @TableField("order_number")
    private String orderNumber;

    /**
     * 识别的分类
     */
    @TableField("recognized_category")
    private String recognizedCategory;

    /**
     * 识别的标签（JSON）
     */
    @TableField("recognized_tags_json")
    private String recognizedTagsJson;

    /**
     * 商品明细（JSON）
     */
    @TableField("items_json")
    private String itemsJson;

    /**
     * 置信度详情（JSON）
     */
    @TableField("confidence_details_json")
    private String confidenceDetailsJson;

    /**
     * 原始OCR结果（JSON）
     */
    @TableField("raw_result_json")
    private String rawResultJson;

    /**
     * 是否自动创建账单
     */
    @TableField("auto_create_transaction")
    private Boolean autoCreateTransaction;

    /**
     * 是否创建了账单
     */
    @TableField("transaction_created")
    private Boolean transactionCreated;

    /**
     * 创建的账单ID
     */
    @TableField("transaction_id")
    private Long transactionId;

    /**
     * 默认分类ID
     */
    @TableField("default_category_id")
    private Long defaultCategoryId;

    /**
     * 默认标签（JSON）
     */
    @TableField("default_tags_json")
    private String defaultTagsJson;

    /**
     * 默认备注
     */
    @TableField("default_remark")
    private String defaultRemark;

    /**
     * 是否启用智能分类
     */
    @TableField("enable_smart_category")
    private Boolean enableSmartCategory;

    /**
     * 是否启用智能标签
     */
    @TableField("enable_smart_tags")
    private Boolean enableSmartTags;

    /**
     * 置信度阈值
     */
    @TableField("confidence_threshold")
    private Double confidenceThreshold;

    /**
     * 是否保存原始图片
     */
    @TableField("save_original_image")
    private Boolean saveOriginalImage;

    /**
     * 图片压缩质量
     */
    @TableField("image_quality")
    private Integer imageQuality;

    /**
     * 最大图片尺寸
     */
    @TableField("max_image_size")
    private Integer maxImageSize;

    /**
     * 是否启用表格识别
     */
    @TableField("enable_table_recognition")
    private Boolean enableTableRecognition;

    /**
     * 是否启用手写识别
     */
    @TableField("enable_handwriting_recognition")
    private Boolean enableHandwritingRecognition;

    /**
     * 超时时间（秒）
     */
    @TableField("timeout_seconds")
    private Integer timeoutSeconds;

    /**
     * 重试次数
     */
    @TableField("retry_count")
    private Integer retryCount;

    /**
     * 实际重试次数
     */
    @TableField("actual_retry_count")
    private Integer actualRetryCount;

    /**
     * 回调URL
     */
    @TableField("callback_url")
    private String callbackUrl;

    /**
     * 是否异步处理
     */
    @TableField("async_processing")
    private Boolean asyncProcessing;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 错误代码
     */
    @TableField("error_code")
    private String errorCode;

    /**
     * 错误详情（JSON）
     */
    @TableField("error_details_json")
    private String errorDetailsJson;

    /**
     * 扩展参数（JSON）
     */
    @TableField("extra_params_json")
    private String extraParamsJson;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    // ==================== 非数据库字段 ====================

    /**
     * 创建人姓名
     */
    @TableField(exist = false)
    private String createdByName;

    /**
     * 更新人姓名
     */
    @TableField(exist = false)
    private String updatedByName;

    /**
     * 识别的标签列表
     */
    @TableField(exist = false)
    private java.util.List<String> recognizedTags;

    /**
     * 默认标签列表
     */
    @TableField(exist = false)
    private java.util.List<String> defaultTags;

    /**
     * 商品明细列表
     */
    @TableField(exist = false)
    private java.util.List<Object> items;

    /**
     * 置信度详情
     */
    @TableField(exist = false)
    private java.util.Map<String, Double> confidenceDetails;

    /**
     * 原始OCR结果
     */
    @TableField(exist = false)
    private Object rawResult;

    /**
     * 错误详情
     */
    @TableField(exist = false)
    private java.util.Map<String, Object> errorDetails;

    /**
     * 扩展参数
     */
    @TableField(exist = false)
    private java.util.Map<String, Object> extraParams;

    /**
     * 是否成功
     */
    @TableField(exist = false)
    private Boolean isSuccess;

    /**
     * 状态描述
     */
    @TableField(exist = false)
    private String statusDescription;

    /**
     * 处理进度
     */
    @TableField(exist = false)
    private Integer progress;
}
