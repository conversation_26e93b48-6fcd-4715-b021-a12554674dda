package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.admin.accounting.enums.TransactionType;
import top.continew.starter.data.mybatis.plus.base.BaseEntity;

/**
 * 分类实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("acc_category")
@Schema(description = "分类信息")
public class CategoryDO extends BaseEntity {

    /**
     * 群组ID
     */
    @Schema(description = "群组ID")
    private Long groupId;

    /**
     * 分类名称
     */
    @Schema(description = "分类名称")
    private String name;

    /**
     * 父分类ID
     */
    @Schema(description = "父分类ID")
    private Long parentId;

    /**
     * 分类类型
     */
    @Schema(description = "分类类型")
    private TransactionType type;

    /**
     * 图标
     */
    @Schema(description = "图标")
    private String icon;

    /**
     * 颜色
     */
    @Schema(description = "颜色")
    private String color;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;

    /**
     * 祖先路径
     */
    @Schema(description = "祖先路径")
    private String ancestors;

    /**
     * 是否为系统分类
     */
    @Schema(description = "是否为系统分类")
    private Boolean isSystem;

    /**
     * 是否为默认分类
     */
    @Schema(description = "是否为默认分类")
    private Boolean isDefault;
}
