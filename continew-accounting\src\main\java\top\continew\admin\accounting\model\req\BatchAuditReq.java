package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 批量审核请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "批量审核请求")
public class BatchAuditReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 审核ID列表
     */
    @Schema(description = "审核ID列表", example = "[1, 2, 3]")
    @NotEmpty(message = "审核ID列表不能为空")
    private List<Long> auditIds;

    /**
     * 批量操作类型
     */
    @Schema(description = "批量操作类型", example = "BATCH_APPROVE", allowableValues = {"BATCH_APPROVE", "BATCH_REJECT", "BATCH_RETURN", "BATCH_FORWARD", "BATCH_ESCALATE"})
    @NotBlank(message = "批量操作类型不能为空")
    private String batchOperation;

    /**
     * 统一审核结果
     */
    @Schema(description = "统一审核结果", example = "APPROVED", allowableValues = {"APPROVED", "REJECTED", "RETURNED", "PENDING"})
    private String uniformResult;

    /**
     * 统一审核意见
     */
    @Schema(description = "统一审核意见", example = "批量审核通过")
    @Size(max = 1000, message = "统一审核意见长度不能超过1000个字符")
    private String uniformComment;

    /**
     * 个别审核详情
     */
    @Schema(description = "个别审核详情")
    private List<IndividualAuditDetail> individualDetails;

    /**
     * 批量审核规则
     */
    @Schema(description = "批量审核规则")
    private BatchAuditRules batchRules;

    /**
     * 异常处理策略
     */
    @Schema(description = "异常处理策略", example = "SKIP_ERROR", allowableValues = {"SKIP_ERROR", "STOP_ON_ERROR", "ROLLBACK_ON_ERROR"})
    private String errorHandlingStrategy = "SKIP_ERROR";

    /**
     * 是否发送通知
     */
    @Schema(description = "是否发送通知", example = "true")
    private Boolean sendNotification = true;

    /**
     * 通知模板
     */
    @Schema(description = "通知模板", example = "BATCH_AUDIT_COMPLETION")
    private String notificationTemplate;

    /**
     * 扩展属性
     */
    @Schema(description = "扩展属性")
    private Map<String, Object> attributes;

    /**
     * 个别审核详情
     */
    @Data
    @Schema(description = "个别审核详情")
    public static class IndividualAuditDetail implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 审核ID
         */
        @Schema(description = "审核ID", example = "1")
        @NotNull(message = "审核ID不能为空")
        private Long auditId;

        /**
         * 审核结果
         */
        @Schema(description = "审核结果", example = "APPROVED", allowableValues = {"APPROVED", "REJECTED", "RETURNED", "PENDING"})
        private String result;

        /**
         * 审核意见
         */
        @Schema(description = "审核意见", example = "特殊情况，单独处理")
        @Size(max = 500, message = "审核意见长度不能超过500个字符")
        private String comment;

        /**
         * 是否跳过
         */
        @Schema(description = "是否跳过", example = "false")
        private Boolean skip = false;

        /**
         * 跳过原因
         */
        @Schema(description = "跳过原因", example = "需要额外审核")
        @Size(max = 200, message = "跳过原因长度不能超过200个字符")
        private String skipReason;

        /**
         * 特殊处理标记
         */
        @Schema(description = "特殊处理标记")
        private List<String> specialFlags;

        /**
         * 个别属性
         */
        @Schema(description = "个别属性")
        private Map<String, Object> individualAttributes;
    }

    /**
     * 批量审核规则
     */
    @Data
    @Schema(description = "批量审核规则")
    public static class BatchAuditRules implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 最大批量数量
         */
        @Schema(description = "最大批量数量", example = "100")
        @Min(value = 1, message = "最大批量数量不能小于1")
        @Max(value = 1000, message = "最大批量数量不能大于1000")
        private Integer maxBatchSize = 100;

        /**
         * 金额阈值检查
         */
        @Schema(description = "金额阈值检查")
        private AmountThresholdCheck amountThreshold;

        /**
         * 风险等级限制
         */
        @Schema(description = "风险等级限制", example = "[\"LOW\", \"MEDIUM\"]")
        private List<String> allowedRiskLevels;

        /**
         * 分类限制
         */
        @Schema(description = "分类限制")
        private List<Long> allowedCategoryIds;

        /**
         * 时间范围限制
         */
        @Schema(description = "时间范围限制")
        private TimeRangeLimit timeRange;

        /**
         * 自动验证规则
         */
        @Schema(description = "自动验证规则")
        private List<ValidationRule> validationRules;

        /**
         * 是否需要二次确认
         */
        @Schema(description = "是否需要二次确认", example = "false")
        private Boolean requireSecondaryConfirmation = false;

        @Data
        @Schema(description = "金额阈值检查")
        public static class AmountThresholdCheck implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 是否启用
             */
            @Schema(description = "是否启用", example = "true")
            private Boolean enabled = true;

            /**
             * 单笔最大金额
             */
            @Schema(description = "单笔最大金额", example = "10000.00")
            private BigDecimal maxSingleAmount;

            /**
             * 批量总金额限制
             */
            @Schema(description = "批量总金额限制", example = "100000.00")
            private BigDecimal maxTotalAmount;

            /**
             * 超限处理方式
             */
            @Schema(description = "超限处理方式", example = "REQUIRE_APPROVAL", allowableValues = {"REQUIRE_APPROVAL", "AUTO_REJECT", "SPLIT_BATCH"})
            private String exceedAction = "REQUIRE_APPROVAL";
        }

        @Data
        @Schema(description = "时间范围限制")
        public static class TimeRangeLimit implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 是否启用
             */
            @Schema(description = "是否启用", example = "true")
            private Boolean enabled = true;

            /**
             * 开始日期
             */
            @Schema(description = "开始日期", example = "2025-01-01")
            private String startDate;

            /**
             * 结束日期
             */
            @Schema(description = "结束日期", example = "2025-01-31")
            private String endDate;

            /**
             * 最大跨度天数
             */
            @Schema(description = "最大跨度天数", example = "90")
            private Integer maxSpanDays;
        }

        @Data
        @Schema(description = "验证规则")
        public static class ValidationRule implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 规则名称
             */
            @Schema(description = "规则名称", example = "AMOUNT_CONSISTENCY")
            private String ruleName;

            /**
             * 规则类型
             */
            @Schema(description = "规则类型", example = "BUSINESS_RULE")
            private String ruleType;

            /**
             * 规则表达式
             */
            @Schema(description = "规则表达式", example = "amount > 0 && amount < 10000")
            private String ruleExpression;

            /**
             * 错误消息
             */
            @Schema(description = "错误消息", example = "金额必须在0-10000之间")
            private String errorMessage;

            /**
             * 是否阻断
             */
            @Schema(description = "是否阻断", example = "true")
            private Boolean blocking = true;

            /**
             * 规则参数
             */
            @Schema(description = "规则参数")
            private Map<String, Object> ruleParameters;
        }
    }
}
