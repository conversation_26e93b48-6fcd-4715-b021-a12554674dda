name: "\U0001F680 新 Feature 建议"
description: 我希望增加 xxx 功能；现有的 xxx 功能不好用...
title: "[Feature] "
labels: ["feature"]
body:
  - type: markdown
    attributes:
      value: |
        感谢您使用 ContiNew Admin！请您花些时间填写这份 Feature 调查。
  - type: checkboxes
    id: checkboxes
    attributes:
      label: 请您确认
      description: 在提交 Feature 之前，请确认执行过以下操作。
      options:
        - label: 尝试了最新 dev 分支代码（演示环境），仍没有该功能
          required: true
        - label: 查阅过 [使用指南](https://continew.top/admin/backend/structure.html) 和 [常见问题](https://continew.top/admin/faq.html) ，仍然认为很有必要
          required: true
        - label: 查阅过 [需求墙](https://continew.top/admin/other/feature.html)，仍没有该功能计划
          required: true
        - label: 搜索了项目 Issues，没有其他人提交过类似的 Feature（如果对应 Feature 尚未实现，您可以先订阅关注该 Issue，为了方便后来者查找问题解决方法，请避免创建重复的 Issue）
          required: true
        - label: 【后端】确认不是依赖组件相关的需求，例如：sa-token、mybatis-plus、snail-job、crane4j、cosid等（如有此类组件相关的需求，请提交至对应组件仓库）
          required: true
        - label: 【前端】确认不是基础组件类需求，例如：GiTable、GiForm、基础布局、纯前端组件（锁屏、引导）等（如有此类组件相关的需求，请提交至 [gi-demo](https://gitee.com/lin0716/gi-demo) 或对应组件仓库）
          required: true
        - label: 是否愿意为您提出的 Feature 提交 PR？
          required: false
    validations:
      required: true
  - type: textarea
    id: feature-description
    attributes:
      label: Feature 描述
      description: 清楚而简洁地描述您的 Feature。另外，非常欢迎您对此 Feature 提交 PR。
      placeholder: 例如：我希望增加 xxx 功能；现有的 xxx 功能不好用...
    validations:
      required: true
  - type: textarea
    id: suggested-solution
    attributes:
      label: 描述一下您想要的解决方案
      description: 清楚而简洁地描述您想要的解决方案。
    validations:
      required: true
  - type: textarea
    id: alternative
    attributes:
      label: 描述一下您考虑过的替代方案
      description: 清楚而简洁地描述您考虑过的任何替代解决方案或功能。
  - type: textarea
    id: additional-context
    attributes:
      label: 额外补充
      description: 添加您在其他框架或场景遇见的效果截图或链接，以及一切能帮助理解 Feature 的信息。
