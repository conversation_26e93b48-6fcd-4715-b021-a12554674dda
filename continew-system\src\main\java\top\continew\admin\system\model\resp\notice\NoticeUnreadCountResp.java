/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.system.model.resp.notice;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 未读公告数量响应参数
 *
 * <AUTHOR>
 * @since 2025/5/22 22:15
 */
@Data
@NoArgsConstructor
@Schema(description = "未读公告数量响应参数")
public class NoticeUnreadCountResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 未读公告数量
     */
    @Schema(description = "未读公告数量", example = "1")
    private Integer total;

    public NoticeUnreadCountResp(Integer total) {
        this.total = total;
    }
}