package top.continew.admin.accounting.service.sync.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.continew.admin.accounting.service.sync.DataSourceAdapter;
import top.continew.starter.core.exception.BusinessException;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Webhook适配器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WebhookAdapter implements DataSourceAdapter {

    /**
     * Webhook数据缓存
     */
    private final Map<String, List<Map<String, Object>>> webhookDataCache = new ConcurrentHashMap<>();

    /**
     * Webhook配置缓存
     */
    private final Map<String, Map<String, Object>> webhookConfigCache = new ConcurrentHashMap<>();

    @Override
    public String getAdapterType() {
        return "WEBHOOK";
    }

    @Override
    public Map<String, Object> testConnection(Map<String, Object> config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String webhookUrl = (String) config.get("webhookUrl");
            String secret = (String) config.get("secret");
            
            if (StrUtil.isBlank(webhookUrl)) {
                throw new BusinessException("Webhook URL不能为空");
            }
            
            // 发送测试Webhook
            Map<String, Object> testPayload = new HashMap<>();
            testPayload.put("type", "test");
            testPayload.put("timestamp", LocalDateTime.now());
            testPayload.put("message", "Webhook连接测试");
            
            HttpRequest request = HttpUtil.createPost(webhookUrl)
                    .body(JSONUtil.toJsonStr(testPayload))
                    .contentType("application/json");
            
            // 添加签名验证
            if (StrUtil.isNotBlank(secret)) {
                String signature = generateSignature(JSONUtil.toJsonStr(testPayload), secret);
                request.header("X-Webhook-Signature", signature);
            }
            
            HttpResponse response = request.execute();
            
            if (response.isOk()) {
                result.put("success", true);
                result.put("message", "Webhook连接测试成功");
                result.put("statusCode", response.getStatus());
                result.put("responseTime", response.getTime());
            } else {
                result.put("success", false);
                result.put("message", "Webhook连接测试失败: HTTP " + response.getStatus());
                result.put("statusCode", response.getStatus());
            }
            
            result.put("testTime", LocalDateTime.now());
            
        } catch (Exception e) {
            log.error("Webhook连接测试失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "Webhook连接测试失败: " + e.getMessage());
            result.put("testTime", LocalDateTime.now());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> validateConfig(Map<String, Object> config) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        
        String webhookUrl = (String) config.get("webhookUrl");
        String listenPath = (String) config.get("listenPath");
        
        if (StrUtil.isBlank(webhookUrl) && StrUtil.isBlank(listenPath)) {
            errors.add("Webhook URL或监听路径至少需要配置一个");
        }
        
        if (StrUtil.isNotBlank(webhookUrl) && !isValidUrl(webhookUrl)) {
            errors.add("Webhook URL格式不正确");
        }
        
        if (StrUtil.isNotBlank(listenPath) && !listenPath.startsWith("/")) {
            errors.add("监听路径必须以/开头");
        }
        
        result.put("valid", errors.isEmpty());
        result.put("errors", errors);
        result.put("validateTime", LocalDateTime.now());
        
        return result;
    }

    @Override
    public Map<String, Object> getDataStructure(Map<String, Object> config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String configId = (String) config.get("configId");
            
            // 从缓存中获取样本数据
            List<Map<String, Object>> sampleData = webhookDataCache.get(configId);
            
            if (CollUtil.isNotEmpty(sampleData)) {
                Map<String, Object> sampleRecord = sampleData.get(0);
                List<Map<String, Object>> fields = analyzeFields(sampleRecord);
                
                result.put("fields", fields);
                result.put("sampleCount", sampleData.size());
                result.put("lastReceived", LocalDateTime.now());
            } else {
                // 返回默认结构
                List<Map<String, Object>> fields = new ArrayList<>();
                
                Map<String, Object> timestampField = new HashMap<>();
                timestampField.put("name", "timestamp");
                timestampField.put("type", "TIMESTAMP");
                timestampField.put("description", "事件时间戳");
                fields.add(timestampField);
                
                Map<String, Object> eventTypeField = new HashMap<>();
                eventTypeField.put("name", "eventType");
                eventTypeField.put("type", "STRING");
                eventTypeField.put("description", "事件类型");
                fields.add(eventTypeField);
                
                Map<String, Object> dataField = new HashMap<>();
                dataField.put("name", "data");
                dataField.put("type", "OBJECT");
                dataField.put("description", "事件数据");
                fields.add(dataField);
                
                result.put("fields", fields);
                result.put("sampleCount", 0);
                result.put("message", "暂无样本数据，显示默认结构");
            }
            
            result.put("success", true);
            
        } catch (Exception e) {
            log.error("获取Webhook数据结构失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    @Override
    public List<Map<String, Object>> readData(Map<String, Object> config,
                                               Map<String, Object> fieldMapping,
                                               Map<String, Object> filterCondition,
                                               LocalDateTime lastSyncTime,
                                               Integer batchSize) {
        try {
            String configId = (String) config.get("configId");
            
            // 从缓存中获取数据
            List<Map<String, Object>> data = webhookDataCache.getOrDefault(configId, new ArrayList<>());
            
            // 应用时间过滤
            if (lastSyncTime != null) {
                data = data.stream()
                        .filter(record -> {
                            Object timestamp = record.get("timestamp");
                            if (timestamp instanceof LocalDateTime) {
                                return ((LocalDateTime) timestamp).isAfter(lastSyncTime);
                            }
                            return true;
                        })
                        .collect(ArrayList::new, (list, item) -> list.add(item), ArrayList::addAll);
            }
            
            // 应用过滤条件
            if (filterCondition != null && !filterCondition.isEmpty()) {
                data = applyFilter(data, filterCondition);
            }
            
            // 应用批量限制
            if (batchSize != null && data.size() > batchSize) {
                data = data.subList(0, batchSize);
            }
            
            // 应用字段映射
            if (fieldMapping != null && !fieldMapping.isEmpty()) {
                data = applyFieldMapping(data, fieldMapping);
            }
            
            log.info("Webhook读取数据成功: configId={}, count={}", configId, data.size());
            return data;
            
        } catch (Exception e) {
            log.error("Webhook读取数据失败: {}", e.getMessage(), e);
            throw new BusinessException("Webhook读取数据失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> writeData(Map<String, Object> config,
                                         Map<String, Object> fieldMapping,
                                         List<Map<String, Object>> data,
                                         String operationType) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String webhookUrl = (String) config.get("webhookUrl");
            String secret = (String) config.get("secret");
            
            if (StrUtil.isBlank(webhookUrl)) {
                throw new BusinessException("Webhook URL不能为空");
            }
            
            int successCount = 0;
            int failedCount = 0;
            List<String> errors = new ArrayList<>();
            
            // 应用字段映射
            if (fieldMapping != null && !fieldMapping.isEmpty()) {
                data = reverseFieldMapping(data, fieldMapping);
            }
            
            // 检查是否支持批量操作
            Boolean supportBatch = (Boolean) config.getOrDefault("supportBatch", true);
            
            if (supportBatch) {
                // 批量发送
                try {
                    sendBatchWebhook(webhookUrl, secret, data, operationType);
                    successCount = data.size();
                } catch (Exception e) {
                    failedCount = data.size();
                    errors.add("批量Webhook发送失败: " + e.getMessage());
                }
            } else {
                // 逐条发送
                for (Map<String, Object> record : data) {
                    try {
                        sendWebhook(webhookUrl, secret, record, operationType);
                        successCount++;
                    } catch (Exception e) {
                        failedCount++;
                        errors.add("Webhook发送失败: " + e.getMessage());
                        log.error("Webhook发送失败: record={}, error={}", record, e.getMessage());
                    }
                }
            }
            
            result.put("success", true);
            result.put("totalCount", data.size());
            result.put("successCount", successCount);
            result.put("failedCount", failedCount);
            result.put("errors", errors);
            
            log.info("Webhook发送数据完成: url={}, total={}, success={}, failed={}", 
                    webhookUrl, data.size(), successCount, failedCount);
            
        } catch (Exception e) {
            log.error("Webhook发送数据失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> detectChanges(Map<String, Object> config, LocalDateTime lastSyncTime) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String configId = (String) config.get("configId");
            
            // 检查缓存中的新数据
            List<Map<String, Object>> data = webhookDataCache.getOrDefault(configId, new ArrayList<>());
            
            long changeCount = 0;
            if (lastSyncTime != null) {
                changeCount = data.stream()
                        .filter(record -> {
                            Object timestamp = record.get("timestamp");
                            if (timestamp instanceof LocalDateTime) {
                                return ((LocalDateTime) timestamp).isAfter(lastSyncTime);
                            }
                            return true;
                        })
                        .count();
            } else {
                changeCount = data.size();
            }
            
            result.put("hasChanges", changeCount > 0);
            result.put("changeCount", changeCount);
            result.put("lastCheckTime", LocalDateTime.now());
            
        } catch (Exception e) {
            log.error("检测Webhook数据变更失败: {}", e.getMessage(), e);
            result.put("hasChanges", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    @Override
    public Long getDataCount(Map<String, Object> config, Map<String, Object> filterCondition) {
        try {
            String configId = (String) config.get("configId");
            
            List<Map<String, Object>> data = webhookDataCache.getOrDefault(configId, new ArrayList<>());
            
            if (filterCondition != null && !filterCondition.isEmpty()) {
                data = applyFilter(data, filterCondition);
            }
            
            return (long) data.size();
            
        } catch (Exception e) {
            log.error("获取Webhook数据总数失败: {}", e.getMessage(), e);
            return 0L;
        }
    }

    @Override
    public Map<String, Object> suggestFieldMapping(Map<String, Object> sourceConfig, Map<String, Object> targetConfig) {
        Map<String, Object> mapping = new HashMap<>();
        
        try {
            Map<String, Object> sourceStructure = getDataStructure(sourceConfig);
            Map<String, Object> targetStructure = getDataStructure(targetConfig);
            
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> sourceFields = (List<Map<String, Object>>) sourceStructure.get("fields");
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> targetFields = (List<Map<String, Object>>) targetStructure.get("fields");
            
            if (sourceFields != null && targetFields != null) {
                Map<String, String> suggestions = new HashMap<>();
                
                for (Map<String, Object> sourceField : sourceFields) {
                    String sourceFieldName = (String) sourceField.get("name");
                    
                    for (Map<String, Object> targetField : targetFields) {
                        String targetFieldName = (String) targetField.get("name");
                        
                        if (sourceFieldName.equals(targetFieldName)) {
                            suggestions.put(sourceFieldName, targetFieldName);
                            break;
                        }
                        
                        if (sourceFieldName.toLowerCase().equals(targetFieldName.toLowerCase())) {
                            suggestions.put(sourceFieldName, targetFieldName);
                            break;
                        }
                    }
                }
                
                mapping.put("suggestions", suggestions);
            }
            
        } catch (Exception e) {
            log.error("生成Webhook字段映射建议失败: {}", e.getMessage(), e);
            mapping.put("error", e.getMessage());
        }
        
        return mapping;
    }

    @Override
    public List<Map<String, Object>> executeCustomQuery(Map<String, Object> config, String query, Map<String, Object> params) {
        // Webhook不支持自定义查询，返回空结果
        log.warn("Webhook不支持自定义查询: query={}", query);
        return new ArrayList<>();
    }

    @Override
    public Map<String, Object> getSyncProgress(Map<String, Object> config, String syncId) {
        Map<String, Object> progress = new HashMap<>();
        progress.put("syncId", syncId);
        progress.put("progress", 100);
        progress.put("message", "Webhook同步完成");
        return progress;
    }

    @Override
    public void cleanup(Map<String, Object> config, String syncId) {
        String configId = (String) config.get("configId");
        
        // 清理缓存数据
        if (StrUtil.isNotBlank(configId)) {
            webhookDataCache.remove(configId);
            webhookConfigCache.remove(configId);
        }
        
        log.debug("Webhook适配器清理完成: syncId={}, configId={}", syncId, configId);
    }

    /**
     * 接收Webhook数据
     */
    public void receiveWebhookData(String configId, Map<String, Object> data) {
        try {
            // 添加时间戳
            data.put("timestamp", LocalDateTime.now());
            
            // 存储到缓存
            webhookDataCache.computeIfAbsent(configId, k -> new ArrayList<>()).add(data);
            
            // 限制缓存大小
            List<Map<String, Object>> cachedData = webhookDataCache.get(configId);
            if (cachedData.size() > 1000) {
                cachedData.remove(0); // 移除最旧的数据
            }
            
            log.debug("接收Webhook数据: configId={}, data={}", configId, data);
            
        } catch (Exception e) {
            log.error("接收Webhook数据失败: configId={}, error={}", configId, e.getMessage(), e);
        }
    }

    /**
     * 验证URL格式
     */
    private boolean isValidUrl(String url) {
        return StrUtil.isNotBlank(url) && (url.startsWith("http://") || url.startsWith("https://"));
    }

    /**
     * 分析字段结构
     */
    private List<Map<String, Object>> analyzeFields(Map<String, Object> sampleRecord) {
        List<Map<String, Object>> fields = new ArrayList<>();
        
        for (Map.Entry<String, Object> entry : sampleRecord.entrySet()) {
            Map<String, Object> field = new HashMap<>();
            field.put("name", entry.getKey());
            field.put("type", getFieldType(entry.getValue()));
            field.put("sample", entry.getValue());
            fields.add(field);
        }
        
        return fields;
    }

    /**
     * 获取字段类型
     */
    private String getFieldType(Object value) {
        if (value == null) {
            return "NULL";
        } else if (value instanceof String) {
            return "STRING";
        } else if (value instanceof Number) {
            return "NUMBER";
        } else if (value instanceof Boolean) {
            return "BOOLEAN";
        } else if (value instanceof LocalDateTime) {
            return "TIMESTAMP";
        } else if (value instanceof List) {
            return "ARRAY";
        } else if (value instanceof Map) {
            return "OBJECT";
        } else {
            return "UNKNOWN";
        }
    }

    /**
     * 应用过滤条件
     */
    private List<Map<String, Object>> applyFilter(List<Map<String, Object>> data, Map<String, Object> filterCondition) {
        return data.stream()
                .filter(record -> {
                    for (Map.Entry<String, Object> filter : filterCondition.entrySet()) {
                        String field = filter.getKey();
                        Object expectedValue = filter.getValue();
                        Object actualValue = record.get(field);
                        
                        if (!Objects.equals(expectedValue, actualValue)) {
                            return false;
                        }
                    }
                    return true;
                })
                .collect(ArrayList::new, (list, item) -> list.add(item), ArrayList::addAll);
    }

    /**
     * 发送单个Webhook
     */
    private void sendWebhook(String webhookUrl, String secret, Map<String, Object> data, String operationType) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("operation", operationType);
        payload.put("timestamp", LocalDateTime.now());
        payload.put("data", data);
        
        String payloadJson = JSONUtil.toJsonStr(payload);
        
        HttpRequest request = HttpUtil.createPost(webhookUrl)
                .body(payloadJson)
                .contentType("application/json");
        
        if (StrUtil.isNotBlank(secret)) {
            String signature = generateSignature(payloadJson, secret);
            request.header("X-Webhook-Signature", signature);
        }
        
        HttpResponse response = request.execute();
        
        if (!response.isOk()) {
            throw new BusinessException("Webhook发送失败: HTTP " + response.getStatus());
        }
    }

    /**
     * 批量发送Webhook
     */
    private void sendBatchWebhook(String webhookUrl, String secret, List<Map<String, Object>> data, String operationType) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("operation", operationType);
        payload.put("timestamp", LocalDateTime.now());
        payload.put("batch", true);
        payload.put("data", data);
        
        String payloadJson = JSONUtil.toJsonStr(payload);
        
        HttpRequest request = HttpUtil.createPost(webhookUrl)
                .body(payloadJson)
                .contentType("application/json");
        
        if (StrUtil.isNotBlank(secret)) {
            String signature = generateSignature(payloadJson, secret);
            request.header("X-Webhook-Signature", signature);
        }
        
        HttpResponse response = request.execute();
        
        if (!response.isOk()) {
            throw new BusinessException("批量Webhook发送失败: HTTP " + response.getStatus());
        }
    }

    /**
     * 生成签名
     */
    private String generateSignature(String payload, String secret) {
        // TODO: 实现HMAC-SHA256签名
        // 这里简化处理，实际应该使用HMAC-SHA256
        return "sha256=" + payload.hashCode();
    }

    /**
     * 应用字段映射
     */
    private List<Map<String, Object>> applyFieldMapping(List<Map<String, Object>> data, Map<String, Object> fieldMapping) {
        if (CollUtil.isEmpty(data) || fieldMapping == null || fieldMapping.isEmpty()) {
            return data;
        }
        
        List<Map<String, Object>> mappedData = new ArrayList<>();
        
        for (Map<String, Object> record : data) {
            Map<String, Object> mappedRecord = new HashMap<>();
            
            for (Map.Entry<String, Object> entry : record.entrySet()) {
                String sourceField = entry.getKey();
                Object value = entry.getValue();
                
                String targetField = (String) fieldMapping.get(sourceField);
                if (targetField != null) {
                    mappedRecord.put(targetField, value);
                } else {
                    mappedRecord.put(sourceField, value);
                }
            }
            
            mappedData.add(mappedRecord);
        }
        
        return mappedData;
    }

    /**
     * 反向字段映射
     */
    private List<Map<String, Object>> reverseFieldMapping(List<Map<String, Object>> data, Map<String, Object> fieldMapping) {
        if (CollUtil.isEmpty(data) || fieldMapping == null || fieldMapping.isEmpty()) {
            return data;
        }
        
        Map<String, String> reverseMapping = new HashMap<>();
        for (Map.Entry<String, Object> entry : fieldMapping.entrySet()) {
            reverseMapping.put((String) entry.getValue(), entry.getKey());
        }
        
        return applyFieldMapping(data, new HashMap<>(reverseMapping));
    }
}
