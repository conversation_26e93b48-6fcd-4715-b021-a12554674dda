package top.continew.admin.accounting.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.starter.extension.crud.model.query.PageQuery;

import java.io.Serial;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 报表模板查询条件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "报表模板查询条件")
public class ReportTemplateQuery extends PageQuery {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 模板名称（模糊查询）
     */
    @Schema(description = "模板名称", example = "财务")
    private String templateName;

    /**
     * 模板类型
     */
    @Schema(description = "模板类型", example = "FINANCIAL_OVERVIEW", allowableValues = {"FINANCIAL_OVERVIEW", "CATEGORY_ANALYSIS", "MEMBER_ANALYSIS", "TREND_ANALYSIS", "CUSTOM_REPORT"})
    private String templateType;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID", example = "1")
    private Long createdBy;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    /**
     * 是否公开
     */
    @Schema(description = "是否公开", example = "false")
    private Boolean isPublic;

    /**
     * 标签列表（包含任一标签）
     */
    @Schema(description = "标签列表", example = "[\"财务\", \"月报\"]")
    private List<String> tags;

    /**
     * 创建时间范围 - 开始
     */
    @Schema(description = "创建时间范围 - 开始", example = "2025-01-01 00:00:00")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间范围 - 结束
     */
    @Schema(description = "创建时间范围 - 结束", example = "2025-01-31 23:59:59")
    private LocalDateTime createTimeEnd;

    /**
     * 更新时间范围 - 开始
     */
    @Schema(description = "更新时间范围 - 开始", example = "2025-01-01 00:00:00")
    private LocalDateTime updateTimeStart;

    /**
     * 更新时间范围 - 结束
     */
    @Schema(description = "更新时间范围 - 结束", example = "2025-01-31 23:59:59")
    private LocalDateTime updateTimeEnd;

    /**
     * 使用次数范围 - 最小值
     */
    @Schema(description = "使用次数范围 - 最小值", example = "0")
    private Integer usageCountMin;

    /**
     * 使用次数范围 - 最大值
     */
    @Schema(description = "使用次数范围 - 最大值", example = "100")
    private Integer usageCountMax;

    /**
     * 最后使用时间范围 - 开始
     */
    @Schema(description = "最后使用时间范围 - 开始", example = "2025-01-01 00:00:00")
    private LocalDateTime lastUsedTimeStart;

    /**
     * 最后使用时间范围 - 结束
     */
    @Schema(description = "最后使用时间范围 - 结束", example = "2025-01-31 23:59:59")
    private LocalDateTime lastUsedTimeEnd;

    /**
     * 是否有调度配置
     */
    @Schema(description = "是否有调度配置", example = "true")
    private Boolean hasSchedule;

    /**
     * 调度状态
     */
    @Schema(description = "调度状态", example = "ACTIVE", allowableValues = {"ACTIVE", "PAUSED", "STOPPED", "ERROR"})
    private String scheduleStatus;

    /**
     * 权限范围
     */
    @Schema(description = "权限范围", example = "GROUP", allowableValues = {"ALL", "GROUP", "DEPARTMENT", "SELF"})
    private String permissionScope;

    /**
     * 模板状态
     */
    @Schema(description = "模板状态", example = "ACTIVE", allowableValues = {"DRAFT", "ACTIVE", "ARCHIVED", "DELETED"})
    private String templateStatus;

    /**
     * 版本号
     */
    @Schema(description = "版本号", example = "1.0")
    private String version;

    /**
     * 是否为系统模板
     */
    @Schema(description = "是否为系统模板", example = "false")
    private Boolean isSystemTemplate;

    /**
     * 支持的导出格式
     */
    @Schema(description = "支持的导出格式", example = "[\"PDF\", \"EXCEL\"]")
    private List<String> supportedFormats;

    /**
     * 数据源类型
     */
    @Schema(description = "数据源类型", example = "DATABASE", allowableValues = {"DATABASE", "API", "FILE", "CUSTOM"})
    private String dataSourceType;

    /**
     * 布局类型
     */
    @Schema(description = "布局类型", example = "GRID", allowableValues = {"GRID", "FLEX", "ABSOLUTE", "FLOW"})
    private String layoutType;

    /**
     * 包含图表类型
     */
    @Schema(description = "包含图表类型", example = "[\"LINE\", \"BAR\"]")
    private List<String> chartTypes;

    /**
     * 关键字搜索（搜索名称、描述、标签）
     */
    @Schema(description = "关键字搜索", example = "财务分析")
    private String keyword;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段", example = "createTime", allowableValues = {"createTime", "updateTime", "usageCount", "lastUsedTime", "templateName"})
    private String sortField = "createTime";

    /**
     * 排序方向
     */
    @Schema(description = "排序方向", example = "DESC", allowableValues = {"ASC", "DESC"})
    private String sortDirection = "DESC";

    /**
     * 是否包含已删除
     */
    @Schema(description = "是否包含已删除", example = "false")
    private Boolean includeDeleted = false;

    /**
     * 收藏状态（当前用户）
     */
    @Schema(description = "收藏状态", example = "true")
    private Boolean isFavorite;

    /**
     * 最近使用（天数）
     */
    @Schema(description = "最近使用天数", example = "30")
    private Integer recentUsedDays;

    /**
     * 模板复杂度
     */
    @Schema(description = "模板复杂度", example = "MEDIUM", allowableValues = {"SIMPLE", "MEDIUM", "COMPLEX"})
    private String complexity;

    /**
     * 执行时间范围（毫秒）- 最小值
     */
    @Schema(description = "执行时间范围 - 最小值", example = "0")
    private Long executionTimeMin;

    /**
     * 执行时间范围（毫秒）- 最大值
     */
    @Schema(description = "执行时间范围 - 最大值", example = "10000")
    private Long executionTimeMax;

    /**
     * 数据量范围 - 最小值
     */
    @Schema(description = "数据量范围 - 最小值", example = "0")
    private Integer dataVolumeMin;

    /**
     * 数据量范围 - 最大值
     */
    @Schema(description = "数据量范围 - 最大值", example = "10000")
    private Integer dataVolumeMax;

    /**
     * 错误率范围 - 最小值（百分比）
     */
    @Schema(description = "错误率范围 - 最小值", example = "0.0")
    private Double errorRateMin;

    /**
     * 错误率范围 - 最大值（百分比）
     */
    @Schema(description = "错误率范围 - 最大值", example = "5.0")
    private Double errorRateMax;

    /**
     * 用户评分范围 - 最小值
     */
    @Schema(description = "用户评分范围 - 最小值", example = "1")
    private Integer ratingMin;

    /**
     * 用户评分范围 - 最大值
     */
    @Schema(description = "用户评分范围 - 最大值", example = "5")
    private Integer ratingMax;
}
