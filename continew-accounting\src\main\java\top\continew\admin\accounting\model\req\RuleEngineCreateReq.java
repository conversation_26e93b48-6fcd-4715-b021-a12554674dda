package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.common.model.req.BaseReq;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 规则引擎创建请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "规则引擎创建请求")
public class RuleEngineCreateReq extends BaseReq {

    /**
     * 规则名称
     */
    @Schema(description = "规则名称", example = "自动分类规则")
    @NotBlank(message = "规则名称不能为空")
    private String ruleName;

    /**
     * 规则描述
     */
    @Schema(description = "规则描述", example = "根据关键词自动分类交易")
    private String ruleDescription;

    /**
     * 规则类型
     */
    @Schema(description = "规则类型", example = "AUTO_CATEGORY")
    @NotBlank(message = "规则类型不能为空")
    private String ruleType;

    /**
     * 规则优先级
     */
    @Schema(description = "规则优先级", example = "100")
    @NotNull(message = "规则优先级不能为空")
    private Integer priority;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled = true;

    /**
     * 触发条件配置
     */
    @Schema(description = "触发条件配置")
    @Valid
    @NotNull(message = "触发条件配置不能为空")
    private TriggerCondition triggerCondition;

    /**
     * 执行动作配置
     */
    @Schema(description = "执行动作配置")
    @Valid
    @NotEmpty(message = "执行动作配置不能为空")
    private List<ExecutionAction> executionActions;

    /**
     * 调度配置
     */
    @Schema(description = "调度配置")
    @Valid
    private ScheduleConfig scheduleConfig;

    /**
     * 通知配置
     */
    @Schema(description = "通知配置")
    @Valid
    private NotificationConfig notificationConfig;

    /**
     * 扩展属性
     */
    @Schema(description = "扩展属性")
    private Map<String, Object> attributes;

    /**
     * 触发条件配置
     */
    @Data
    @Schema(description = "触发条件配置")
    public static class TriggerCondition {

        /**
         * 触发类型
         */
        @Schema(description = "触发类型", example = "EVENT")
        @NotBlank(message = "触发类型不能为空")
        private String triggerType;

        /**
         * 事件类型
         */
        @Schema(description = "事件类型", example = "TRANSACTION_CREATED")
        private String eventType;

        /**
         * 条件表达式
         */
        @Schema(description = "条件表达式")
        @Valid
        @NotEmpty(message = "条件表达式不能为空")
        private List<ConditionExpression> conditions;

        /**
         * 条件逻辑
         */
        @Schema(description = "条件逻辑", example = "AND")
        private String conditionLogic = "AND";

        /**
         * 时间条件
         */
        @Schema(description = "时间条件")
        @Valid
        private TimeCondition timeCondition;

        /**
         * 频率限制
         */
        @Schema(description = "频率限制")
        @Valid
        private FrequencyLimit frequencyLimit;
    }

    /**
     * 条件表达式
     */
    @Data
    @Schema(description = "条件表达式")
    public static class ConditionExpression {

        /**
         * 字段名称
         */
        @Schema(description = "字段名称", example = "description")
        @NotBlank(message = "字段名称不能为空")
        private String fieldName;

        /**
         * 操作符
         */
        @Schema(description = "操作符", example = "CONTAINS")
        @NotBlank(message = "操作符不能为空")
        private String operator;

        /**
         * 比较值
         */
        @Schema(description = "比较值", example = "餐饮")
        private Object value;

        /**
         * 数据类型
         */
        @Schema(description = "数据类型", example = "STRING")
        private String dataType = "STRING";

        /**
         * 是否忽略大小写
         */
        @Schema(description = "是否忽略大小写", example = "true")
        private Boolean ignoreCase = true;

        /**
         * 正则表达式模式
         */
        @Schema(description = "正则表达式模式")
        private String regexPattern;

        /**
         * 范围条件
         */
        @Schema(description = "范围条件")
        @Valid
        private RangeCondition rangeCondition;
    }

    /**
     * 范围条件
     */
    @Data
    @Schema(description = "范围条件")
    public static class RangeCondition {

        /**
         * 最小值
         */
        @Schema(description = "最小值")
        private BigDecimal minValue;

        /**
         * 最大值
         */
        @Schema(description = "最大值")
        private BigDecimal maxValue;

        /**
         * 是否包含最小值
         */
        @Schema(description = "是否包含最小值", example = "true")
        private Boolean includeMin = true;

        /**
         * 是否包含最大值
         */
        @Schema(description = "是否包含最大值", example = "true")
        private Boolean includeMax = true;
    }

    /**
     * 时间条件
     */
    @Data
    @Schema(description = "时间条件")
    public static class TimeCondition {

        /**
         * 开始时间
         */
        @Schema(description = "开始时间")
        private LocalDateTime startTime;

        /**
         * 结束时间
         */
        @Schema(description = "结束时间")
        private LocalDateTime endTime;

        /**
         * 时间范围类型
         */
        @Schema(description = "时间范围类型", example = "DAILY")
        private String timeRangeType;

        /**
         * 星期几
         */
        @Schema(description = "星期几")
        private List<Integer> daysOfWeek;

        /**
         * 小时范围
         */
        @Schema(description = "小时范围")
        private List<Integer> hoursOfDay;

        /**
         * 时区
         */
        @Schema(description = "时区", example = "Asia/Shanghai")
        private String timezone = "Asia/Shanghai";
    }

    /**
     * 频率限制
     */
    @Data
    @Schema(description = "频率限制")
    public static class FrequencyLimit {

        /**
         * 最大执行次数
         */
        @Schema(description = "最大执行次数", example = "10")
        private Integer maxExecutions;

        /**
         * 时间窗口（秒）
         */
        @Schema(description = "时间窗口（秒）", example = "3600")
        private Integer timeWindowSeconds;

        /**
         * 冷却时间（秒）
         */
        @Schema(description = "冷却时间（秒）", example = "300")
        private Integer cooldownSeconds;

        /**
         * 是否启用去重
         */
        @Schema(description = "是否启用去重", example = "true")
        private Boolean enableDeduplication = false;

        /**
         * 去重字段
         */
        @Schema(description = "去重字段")
        private List<String> deduplicationFields;
    }

    /**
     * 执行动作配置
     */
    @Data
    @Schema(description = "执行动作配置")
    public static class ExecutionAction {

        /**
         * 动作类型
         */
        @Schema(description = "动作类型", example = "SET_CATEGORY")
        @NotBlank(message = "动作类型不能为空")
        private String actionType;

        /**
         * 动作参数
         */
        @Schema(description = "动作参数")
        @NotNull(message = "动作参数不能为空")
        private Map<String, Object> actionParams;

        /**
         * 执行顺序
         */
        @Schema(description = "执行顺序", example = "1")
        private Integer executionOrder = 1;

        /**
         * 是否异步执行
         */
        @Schema(description = "是否异步执行", example = "false")
        private Boolean asyncExecution = false;

        /**
         * 重试配置
         */
        @Schema(description = "重试配置")
        @Valid
        private RetryConfig retryConfig;

        /**
         * 条件执行
         */
        @Schema(description = "条件执行")
        @Valid
        private ConditionalExecution conditionalExecution;
    }

    /**
     * 重试配置
     */
    @Data
    @Schema(description = "重试配置")
    public static class RetryConfig {

        /**
         * 最大重试次数
         */
        @Schema(description = "最大重试次数", example = "3")
        private Integer maxRetries = 0;

        /**
         * 重试间隔（秒）
         */
        @Schema(description = "重试间隔（秒）", example = "60")
        private Integer retryIntervalSeconds = 60;

        /**
         * 重试策略
         */
        @Schema(description = "重试策略", example = "EXPONENTIAL_BACKOFF")
        private String retryStrategy = "FIXED_INTERVAL";

        /**
         * 最大重试间隔（秒）
         */
        @Schema(description = "最大重试间隔（秒）", example = "3600")
        private Integer maxRetryIntervalSeconds = 3600;
    }

    /**
     * 条件执行
     */
    @Data
    @Schema(description = "条件执行")
    public static class ConditionalExecution {

        /**
         * 执行条件
         */
        @Schema(description = "执行条件")
        @Valid
        private List<ConditionExpression> conditions;

        /**
         * 条件逻辑
         */
        @Schema(description = "条件逻辑", example = "AND")
        private String conditionLogic = "AND";

        /**
         * 失败时是否继续
         */
        @Schema(description = "失败时是否继续", example = "true")
        private Boolean continueOnFailure = true;
    }

    /**
     * 调度配置
     */
    @Data
    @Schema(description = "调度配置")
    public static class ScheduleConfig {

        /**
         * 调度类型
         */
        @Schema(description = "调度类型", example = "CRON")
        private String scheduleType;

        /**
         * Cron表达式
         */
        @Schema(description = "Cron表达式", example = "0 0 * * * ?")
        private String cronExpression;

        /**
         * 固定间隔（秒）
         */
        @Schema(description = "固定间隔（秒）", example = "3600")
        private Integer fixedIntervalSeconds;

        /**
         * 初始延迟（秒）
         */
        @Schema(description = "初始延迟（秒）", example = "0")
        private Integer initialDelaySeconds = 0;

        /**
         * 是否启用
         */
        @Schema(description = "是否启用", example = "true")
        private Boolean enabled = false;

        /**
         * 开始时间
         */
        @Schema(description = "开始时间")
        private LocalDateTime startTime;

        /**
         * 结束时间
         */
        @Schema(description = "结束时间")
        private LocalDateTime endTime;

        /**
         * 时区
         */
        @Schema(description = "时区", example = "Asia/Shanghai")
        private String timezone = "Asia/Shanghai";
    }

    /**
     * 通知配置
     */
    @Data
    @Schema(description = "通知配置")
    public static class NotificationConfig {

        /**
         * 是否启用通知
         */
        @Schema(description = "是否启用通知", example = "true")
        private Boolean enabled = false;

        /**
         * 通知渠道
         */
        @Schema(description = "通知渠道")
        private List<String> channels;

        /**
         * 通知模板
         */
        @Schema(description = "通知模板")
        private String template;

        /**
         * 通知接收人
         */
        @Schema(description = "通知接收人")
        private List<Long> recipients;

        /**
         * 通知条件
         */
        @Schema(description = "通知条件")
        private String notificationCondition;

        /**
         * 通知频率限制
         */
        @Schema(description = "通知频率限制")
        @Valid
        private FrequencyLimit frequencyLimit;
    }
}
