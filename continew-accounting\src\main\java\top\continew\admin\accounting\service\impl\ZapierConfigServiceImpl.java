package top.continew.admin.accounting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.accounting.mapper.ZapierConfigMapper;
import top.continew.admin.accounting.model.entity.ZapierConfigDO;
import top.continew.admin.accounting.model.query.ZapierConfigQuery;
import top.continew.admin.accounting.model.req.ZapierConfigReq;
import top.continew.admin.accounting.model.req.ZapierConfigUpdateReq;
import top.continew.admin.accounting.model.resp.ZapierConfigResp;
import top.continew.admin.accounting.service.ZapierConfigService;
import top.continew.admin.accounting.service.ZapierLogService;
import top.continew.starter.extension.crud.service.impl.BaseServiceImpl;
import top.continew.starter.core.util.validate.CheckUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Zapier配置服务实现
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ZapierConfigServiceImpl extends BaseServiceImpl<ZapierConfigMapper, ZapierConfigDO, ZapierConfigResp, ZapierConfigResp, ZapierConfigQuery, ZapierConfigReq, ZapierConfigUpdateReq> implements ZapierConfigService {

    private final ZapierLogService zapierLogService;

    @Override
    public List<ZapierConfigResp> listByGroupId(Long groupId) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        
        List<ZapierConfigDO> configList = baseMapper.selectByGroupId(groupId);
        return configList.stream()
                .map(this::convertToResp)
                .collect(Collectors.toList());
    }

    @Override
    public List<ZapierConfigDO> listEnabledConfigs(Long groupId, String triggerType) {
        return baseMapper.selectEnabledConfigs(groupId, triggerType);
    }

    @Override
    public List<ZapierConfigDO> listAllEnabledConfigs() {
        return baseMapper.selectAllEnabledConfigs();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEnabled(Long id, Boolean enabled) {
        CheckUtils.throwIfNull(id, "配置ID不能为空");
        CheckUtils.throwIfNull(enabled, "启用状态不能为空");
        
        ZapierConfigDO config = super.getById(id);
        CheckUtils.throwIfNull(config, "配置不存在");
        
        config.setEnabled(enabled);
        config.setStatus(enabled ? "ACTIVE" : "INACTIVE");
        super.updateById(config);
        
        log.info("更新Zapier配置启用状态: configId={}, enabled={}", id, enabled);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(Long id, String status) {
        CheckUtils.throwIfNull(id, "配置ID不能为空");
        CheckUtils.throwIfBlank(status, "状态不能为空");
        
        int result = baseMapper.updateStatus(id, status);
        CheckUtils.throwIf(result == 0, "更新配置状态失败");
        
        log.info("更新Zapier配置状态: configId={}, status={}", id, status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTriggerStats(Long id, Long triggerCount, Long successCount, Long failureCount, LocalDateTime lastTriggered) {
        CheckUtils.throwIfNull(id, "配置ID不能为空");
        
        int result = baseMapper.updateTriggerStats(id, triggerCount, successCount, failureCount, lastTriggered);
        CheckUtils.throwIf(result == 0, "更新触发统计失败");
        
        log.debug("更新Zapier配置触发统计: configId={}, triggerCount={}, successCount={}, failureCount={}", 
                id, triggerCount, successCount, failureCount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateError(Long id, String error, LocalDateTime errorTime) {
        CheckUtils.throwIfNull(id, "配置ID不能为空");

        int result = baseMapper.updateError(id, error, errorTime);
        CheckUtils.throwIf(result == 0, "更新错误信息失败");

        log.warn("更新Zapier配置错误信息: configId={}, error={}", id, error);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTriggerStats(Long id, Long triggerCount, Long successCount, Long failureCount, LocalDateTime lastTriggeredAt) {
        CheckUtils.throwIfNull(id, "配置ID不能为空");

        int result = baseMapper.updateTriggerStats(id, triggerCount, successCount, failureCount, lastTriggeredAt);
        CheckUtils.throwIf(result == 0, "更新触发统计失败");

        log.debug("更新Zapier配置触发统计: configId={}, triggerCount={}, successCount={}, failureCount={}",
                id, triggerCount, successCount, failureCount);
    }

    @Override
    public Map<String, Object> testConnection(Long id) {
        CheckUtils.throwIfNull(id, "配置ID不能为空");
        
        ZapierConfigDO config = super.getById(id);
        CheckUtils.throwIfNull(config, "配置不存在");
        
        Map<String, Object> testData = new HashMap<>();
        testData.put("test", true);
        testData.put("timestamp", System.currentTimeMillis());
        testData.put("configId", id);
        
        return testWebhook(config.getWebhookUrl(), testData);
    }

    @Override
    public Map<String, Object> testWebhook(String webhookUrl, Map<String, Object> testData) {
        CheckUtils.throwIfBlank(webhookUrl, "Webhook URL不能为空");
        
        Map<String, Object> result = new HashMap<>();
        long startTime = System.currentTimeMillis();
        
        try {
            HttpResponse response = HttpRequest.post(webhookUrl)
                    .header("Content-Type", "application/json")
                    .header("User-Agent", "ContiNew-Zapier/1.0")
                    .body(JSONUtil.toJsonStr(testData))
                    .timeout(30000)
                    .execute();
            
            long executionTime = System.currentTimeMillis() - startTime;
            
            result.put("success", true);
            result.put("httpStatus", response.getStatus());
            result.put("responseBody", response.body());
            result.put("executionTime", executionTime);
            result.put("message", "连接测试成功");
            
            log.info("Webhook测试成功: url={}, status={}, time={}ms", webhookUrl, response.getStatus(), executionTime);
            
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("executionTime", executionTime);
            result.put("message", "连接测试失败: " + e.getMessage());
            
            log.error("Webhook测试失败: url={}, error={}", webhookUrl, e.getMessage(), e);
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> triggerExecution(Long id, String eventType, Long businessId, Map<String, Object> data) {
        CheckUtils.throwIfNull(id, "配置ID不能为空");
        CheckUtils.throwIfBlank(eventType, "事件类型不能为空");
        
        ZapierConfigDO config = super.getById(id);
        CheckUtils.throwIfNull(config, "配置不存在");
        CheckUtils.throwIf(!config.getEnabled(), "配置未启用");
        
        Map<String, Object> result = new HashMap<>();
        long startTime = System.currentTimeMillis();
        String status = "SUCCESS";
        String errorMessage = null;
        String errorCode = null;
        Integer httpStatus = null;
        Map<String, Object> responseData = null;
        
        try {
            // 应用过滤规则
            if (!applyFilterRules(config, data)) {
                result.put("success", false);
                result.put("message", "数据不满足过滤条件");
                return result;
            }
            
            // 应用数据映射
            Map<String, Object> mappedData = applyDataMapping(config, data);
            
            // 发送请求
            HttpRequest request = HttpRequest.post(config.getWebhookUrl())
                    .header("Content-Type", "application/json")
                    .header("User-Agent", "ContiNew-Zapier/1.0")
                    .body(JSONUtil.toJsonStr(mappedData))
                    .timeout(config.getTimeoutSeconds() * 1000);
            
            // 添加自定义请求头
            if (StrUtil.isNotBlank(config.getHeaders())) {
                Map<String, String> headers = JSONUtil.toBean(config.getHeaders(), Map.class);
                headers.forEach(request::header);
            }
            
            HttpResponse response = request.execute();
            httpStatus = response.getStatus();
            
            if (httpStatus >= 200 && httpStatus < 300) {
                responseData = Map.of("body", response.body(), "headers", response.headers());
                result.put("success", true);
                result.put("message", "执行成功");
            } else {
                status = "FAILED";
                errorMessage = "HTTP错误: " + httpStatus;
                errorCode = "HTTP_ERROR";
                result.put("success", false);
                result.put("message", errorMessage);
            }
            
        } catch (Exception e) {
            status = "FAILED";
            errorMessage = e.getMessage();
            errorCode = "EXECUTION_ERROR";
            result.put("success", false);
            result.put("message", "执行失败: " + e.getMessage());
            
            log.error("Zapier配置执行失败: configId={}, error={}", id, e.getMessage(), e);
        }
        
        long executionTime = System.currentTimeMillis() - startTime;
        result.put("executionTime", executionTime);
        
        // 记录执行日志
        zapierLogService.recordLog(id, config.getGroupId(), config.getTriggerType(), eventType,
                businessId, getBusinessType(businessId), data, responseData, httpStatus,
                status, executionTime, errorMessage, errorCode);
        
        // 更新配置统计
        updateConfigStats(config, status.equals("SUCCESS"));
        
        return result;
    }

    @Override
    public List<Map<String, Object>> batchTriggerExecution(Long groupId, String triggerType, String eventType, Long businessId, Map<String, Object> data) {
        List<ZapierConfigDO> configs = listEnabledConfigs(groupId, triggerType);
        
        return configs.stream()
                .map(config -> {
                    Map<String, Object> result = triggerExecution(config.getId(), eventType, businessId, data);
                    result.put("configId", config.getId());
                    result.put("configName", config.getName());
                    return result;
                })
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> getConfigStats(Long groupId) {
        return baseMapper.selectConfigStats(groupId);
    }

    @Override
    public List<Map<String, Object>> getTriggerTypeStats(Long groupId) {
        return baseMapper.selectTriggerTypeStats(groupId);
    }

    @Override
    public List<Map<String, Object>> getActiveConfigRanking(Long groupId, Integer limit) {
        return baseMapper.selectActiveConfigRanking(groupId, limit);
    }

    @Override
    public List<Map<String, Object>> getPerformanceStats(Long groupId, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.selectPerformanceStats(groupId, startTime, endTime);
    }

    @Override
    public List<ZapierConfigResp> getErrorConfigs(Long groupId, Integer hours) {
        List<ZapierConfigDO> configs = baseMapper.selectErrorConfigs(groupId, hours);
        return configs.stream()
                .map(this::convertToResp)
                .collect(Collectors.toList());
    }

    @Override
    public List<ZapierConfigResp> getInactiveConfigs(Long groupId, Integer days) {
        List<ZapierConfigDO> configs = baseMapper.selectInactiveConfigs(groupId, days);
        return configs.stream()
                .map(this::convertToResp)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateStatus(List<Long> ids, String status) {
        CheckUtils.throwIf(CollUtil.isEmpty(ids), "配置ID列表不能为空");
        CheckUtils.throwIfBlank(status, "状态不能为空");
        
        int result = baseMapper.batchUpdateStatus(ids, status);
        CheckUtils.throwIf(result == 0, "批量更新配置状态失败");
        
        log.info("批量更新Zapier配置状态: ids={}, status={}", ids, status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateEnabled(List<Long> ids, Boolean enabled) {
        CheckUtils.throwIf(CollUtil.isEmpty(ids), "配置ID列表不能为空");
        CheckUtils.throwIfNull(enabled, "启用状态不能为空");
        
        int result = baseMapper.batchUpdateEnabled(ids, enabled);
        CheckUtils.throwIf(result == 0, "批量更新配置启用状态失败");
        
        log.info("批量更新Zapier配置启用状态: ids={}, enabled={}", ids, enabled);
    }

    /**
     * 转换为响应对象
     */
    private ZapierConfigResp convertToResp(ZapierConfigDO config) {
        ZapierConfigResp resp = BeanUtil.copyProperties(config, ZapierConfigResp.class);
        
        // 设置触发器类型名称
        resp.setTriggerTypeName(getTriggerTypeName(config.getTriggerType()));
        
        // 设置状态名称
        resp.setStatusName(getStatusName(config.getStatus()));
        
        // 计算成功率
        if (config.getTriggerCount() != null && config.getTriggerCount() > 0) {
            double successRate = (double) config.getSuccessCount() / config.getTriggerCount() * 100;
            resp.setSuccessRate(Math.round(successRate * 100.0) / 100.0);
        } else {
            resp.setSuccessRate(0.0);
        }
        
        // 解析JSON字段
        if (StrUtil.isNotBlank(config.getTriggerConditions())) {
            resp.setTriggerConditions(JSONUtil.toBean(config.getTriggerConditions(), Map.class));
        }
        
        if (StrUtil.isNotBlank(config.getHeaders())) {
            resp.setHeaders(JSONUtil.toBean(config.getHeaders(), Map.class));
        }
        
        if (StrUtil.isNotBlank(config.getTags())) {
            resp.setTags(JSONUtil.toList(config.getTags(), String.class));
        }
        
        return resp;
    }

    /**
     * 应用过滤规则
     */
    private boolean applyFilterRules(ZapierConfigDO config, Map<String, Object> data) {
        if (StrUtil.isBlank(config.getFilterRules())) {
            return true;
        }
        
        try {
            List<Map<String, Object>> rules = JSONUtil.toList(config.getFilterRules(), Map.class);
            // TODO: 实现过滤规则逻辑
            return true;
        } catch (Exception e) {
            log.error("应用过滤规则失败: configId={}, error={}", config.getId(), e.getMessage());
            return false;
        }
    }

    /**
     * 应用数据映射
     */
    private Map<String, Object> applyDataMapping(ZapierConfigDO config, Map<String, Object> data) {
        if (StrUtil.isBlank(config.getDataMapping())) {
            return data;
        }
        
        try {
            Map<String, Object> mapping = JSONUtil.toBean(config.getDataMapping(), Map.class);
            // TODO: 实现数据映射逻辑
            return data;
        } catch (Exception e) {
            log.error("应用数据映射失败: configId={}, error={}", config.getId(), e.getMessage());
            return data;
        }
    }

    /**
     * 更新配置统计
     */
    private void updateConfigStats(ZapierConfigDO config, boolean success) {
        Long triggerCount = (config.getTriggerCount() != null ? config.getTriggerCount() : 0) + 1;
        Long successCount = config.getSuccessCount() != null ? config.getSuccessCount() : 0;
        Long failureCount = config.getFailureCount() != null ? config.getFailureCount() : 0;
        
        if (success) {
            successCount++;
        } else {
            failureCount++;
        }
        
        updateTriggerStats(config.getId(), triggerCount, successCount, failureCount, LocalDateTime.now());
    }

    /**
     * 获取业务类型
     */
    private String getBusinessType(Long businessId) {
        // TODO: 根据业务ID判断业务类型
        return "UNKNOWN";
    }

    /**
     * 获取触发器类型名称
     */
    private String getTriggerTypeName(String triggerType) {
        Map<String, String> typeNames = Map.of(
                "TRANSACTION_CREATED", "交易创建",
                "TRANSACTION_UPDATED", "交易更新",
                "TRANSACTION_DELETED", "交易删除",
                "WALLET_UPDATED", "钱包更新",
                "REPORT_GENERATED", "报表生成"
        );
        return typeNames.getOrDefault(triggerType, triggerType);
    }

    /**
     * 获取状态名称
     */
    private String getStatusName(String status) {
        Map<String, String> statusNames = Map.of(
                "ACTIVE", "活跃",
                "INACTIVE", "非活跃",
                "ERROR", "错误",
                "DISABLED", "已禁用"
        );
        return statusNames.getOrDefault(status, status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long copyConfig(Long id, String name) {
        CheckUtils.throwIfNull(id, "配置ID不能为空");
        CheckUtils.throwIfBlank(name, "配置名称不能为空");

        ZapierConfigDO original = super.getById(id);
        CheckUtils.throwIfNull(original, "原配置不存在");

        ZapierConfigDO copy = BeanUtil.copyProperties(original, ZapierConfigDO.class);
        copy.setId(null);
        copy.setName(name);
        copy.setEnabled(false); // 复制的配置默认禁用
        copy.setStatus("INACTIVE");
        copy.setTriggerCount(0L);
        copy.setSuccessCount(0L);
        copy.setFailureCount(0L);
        copy.setLastTriggeredAt(null);
        copy.setLastError(null);
        copy.setLastErrorAt(null);
        copy.setRetryCount(0);

        super.save(copy);

        log.info("复制Zapier配置: originalId={}, newId={}, name={}", id, copy.getId(), name);
        return copy.getId();
    }

    @Override
    public Map<String, Object> exportConfigs(List<Long> ids) {
        CheckUtils.throwIf(CollUtil.isEmpty(ids), "配置ID列表不能为空");

        List<ZapierConfigDO> configs = super.listByIds(ids);
        Map<String, Object> result = new HashMap<>();
        result.put("configs", configs);
        result.put("exportTime", LocalDateTime.now());
        result.put("version", "1.0");

        log.info("导出Zapier配置: ids={}, count={}", ids, configs.size());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> importConfigs(Long groupId, Map<String, Object> configData) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        CheckUtils.throwIfNull(configData, "配置数据不能为空");

        List<Map<String, Object>> configs = (List<Map<String, Object>>) configData.get("configs");
        CheckUtils.throwIf(CollUtil.isEmpty(configs), "配置列表不能为空");

        List<Long> importedIds = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;

        for (Map<String, Object> configMap : configs) {
            try {
                ZapierConfigDO config = BeanUtil.toBean(configMap, ZapierConfigDO.class);
                config.setId(null);
                config.setGroupId(groupId);
                config.setEnabled(false); // 导入的配置默认禁用
                config.setStatus("INACTIVE");
                config.setTriggerCount(0L);
                config.setSuccessCount(0L);
                config.setFailureCount(0L);
                config.setLastTriggeredAt(null);
                config.setLastError(null);
                config.setLastErrorAt(null);
                config.setRetryCount(0);

                super.save(config);
                importedIds.add(config.getId());
                successCount++;
            } catch (Exception e) {
                failureCount++;
                log.error("导入Zapier配置失败: config={}, error={}", configMap, e.getMessage());
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("importedIds", importedIds);
        result.put("successCount", successCount);
        result.put("failureCount", failureCount);
        result.put("totalCount", configs.size());

        log.info("导入Zapier配置完成: groupId={}, success={}, failure={}", groupId, successCount, failureCount);
        return result;
    }

    @Override
    public List<Map<String, Object>> getTriggerTrend(Long configId, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.selectTriggerTrend(configId, startTime, endTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetStats(Long id) {
        CheckUtils.throwIfNull(id, "配置ID不能为空");

        int result = baseMapper.resetStats(id);
        CheckUtils.throwIf(result == 0, "重置配置统计失败");

        log.info("重置Zapier配置统计: configId={}", id);
    }

    @Override
    public List<ZapierConfigResp> getExpiringConfigs(Integer days) {
        List<ZapierConfigDO> configs = baseMapper.selectExpiringConfigs(days);
        return configs.stream()
                .map(this::convertToResp)
                .collect(Collectors.toList());
    }

    @Override
    public List<ZapierConfigResp> getHighFrequencyConfigs(Long groupId, Long threshold, Integer hours) {
        List<ZapierConfigDO> configs = baseMapper.selectHighFrequencyConfigs(groupId, threshold, hours);
        return configs.stream()
                .map(this::convertToResp)
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> getConfigHealth(Long id) {
        return baseMapper.selectConfigHealth(id);
    }

    @Override
    public Map<String, Object> validateConfig(ZapierConfigReq config) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();

        // 验证Webhook URL
        if (StrUtil.isBlank(config.getWebhookUrl())) {
            errors.add("Webhook URL不能为空");
        } else if (!config.getWebhookUrl().startsWith("http")) {
            errors.add("Webhook URL格式不正确");
        }

        // 验证触发器类型
        if (StrUtil.isBlank(config.getTriggerType())) {
            errors.add("触发器类型不能为空");
        }

        // 验证超时时间
        if (config.getTimeoutSeconds() != null && (config.getTimeoutSeconds() < 5 || config.getTimeoutSeconds() > 300)) {
            errors.add("超时时间必须在5-300秒之间");
        }

        // 验证重试次数
        if (config.getMaxRetries() != null && (config.getMaxRetries() < 0 || config.getMaxRetries() > 10)) {
            errors.add("最大重试次数必须在0-10之间");
        }

        result.put("valid", errors.isEmpty());
        result.put("errors", errors);

        return result;
    }

    @Override
    public List<Map<String, Object>> getSupportedTriggerTypes() {
        return Arrays.asList(
                Map.of("type", "TRANSACTION_CREATED", "name", "交易创建", "description", "当创建新交易时触发"),
                Map.of("type", "TRANSACTION_UPDATED", "name", "交易更新", "description", "当更新交易时触发"),
                Map.of("type", "TRANSACTION_DELETED", "name", "交易删除", "description", "当删除交易时触发"),
                Map.of("type", "WALLET_UPDATED", "name", "钱包更新", "description", "当钱包余额变化时触发"),
                Map.of("type", "REPORT_GENERATED", "name", "报表生成", "description", "当生成报表时触发")
        );
    }

    @Override
    public Map<String, Object> getDataMappingTemplate(String triggerType) {
        Map<String, Object> template = new HashMap<>();

        switch (triggerType) {
            case "TRANSACTION_CREATED":
            case "TRANSACTION_UPDATED":
                template.put("fieldMapping", Map.of(
                        "id", "transaction_id",
                        "amount", "amount",
                        "description", "description",
                        "categoryName", "category",
                        "createTime", "created_at"
                ));
                break;
            case "WALLET_UPDATED":
                template.put("fieldMapping", Map.of(
                        "id", "wallet_id",
                        "balance", "balance",
                        "currency", "currency",
                        "updateTime", "updated_at"
                ));
                break;
            default:
                template.put("fieldMapping", Map.of());
        }

        template.put("transformRules", Map.of());
        template.put("defaultValues", Map.of());
        template.put("includeMetadata", false);
        template.put("dateFormat", "yyyy-MM-dd HH:mm:ss");
        template.put("numberFormat", "#,##0.00");

        return template;
    }

    @Override
    public Map<String, Object> previewDataMapping(ZapierConfigReq config, Map<String, Object> testData) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 应用数据映射
            Map<String, Object> mappedData = applyDataMappingPreview(config.getDataMapping(), testData);

            result.put("success", true);
            result.put("originalData", testData);
            result.put("mappedData", mappedData);
            result.put("message", "数据映射预览成功");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("message", "数据映射预览失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 应用数据映射预览
     */
    private Map<String, Object> applyDataMappingPreview(ZapierConfigReq.DataMapping dataMapping, Map<String, Object> data) {
        if (dataMapping == null) {
            return data;
        }

        Map<String, Object> result = new HashMap<>();

        // 应用字段映射
        if (dataMapping.getFieldMapping() != null) {
            dataMapping.getFieldMapping().forEach((sourceField, targetField) -> {
                if (data.containsKey(sourceField)) {
                    result.put(targetField, data.get(sourceField));
                }
            });
        }

        // 应用默认值
        if (dataMapping.getDefaultValues() != null) {
            dataMapping.getDefaultValues().forEach((field, value) -> {
                if (!result.containsKey(field)) {
                    result.put(field, value);
                }
            });
        }

        // 添加元数据
        if (Boolean.TRUE.equals(dataMapping.getIncludeMetadata())) {
            result.put("_metadata", Map.of(
                    "timestamp", System.currentTimeMillis(),
                    "source", "ContiNew-Accounting",
                    "version", "1.0"
            ));
        }

        return result;
    }
}
