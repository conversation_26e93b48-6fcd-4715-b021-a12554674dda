package top.continew.admin.accounting.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.continew.admin.accounting.mapper.ReportMapper;
import top.continew.admin.accounting.model.query.ReportQuery;
import top.continew.admin.accounting.model.resp.*;
import top.continew.admin.accounting.service.GroupService;
import top.continew.admin.accounting.service.ReportService;
import top.continew.starter.core.util.validate.CheckUtils;
import top.continew.starter.excel.util.ExcelUtils;
import top.continew.starter.security.context.SecurityContextHolder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 报表统计业务实现
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReportServiceImpl implements ReportService {

    private final ReportMapper reportMapper;
    private final GroupService groupService;

    @Override
    public ReportOverviewResp getOverview(ReportQuery query) {
        this.validateQueryAndPermission(query);
        this.setDefaultDateRange(query);
        
        ReportOverviewResp overview = reportMapper.selectReportOverview(query);
        if (overview == null) {
            overview = new ReportOverviewResp();
            overview.setTotalIncome(BigDecimal.ZERO);
            overview.setTotalExpense(BigDecimal.ZERO);
            overview.setNetIncome(BigDecimal.ZERO);
            overview.setTransactionCount(0);
            overview.setAvgTransactionAmount(BigDecimal.ZERO);
            overview.setMaxExpense(BigDecimal.ZERO);
            overview.setMaxIncome(BigDecimal.ZERO);
            overview.setActiveDays(0);
            overview.setAvgDailyExpense(BigDecimal.ZERO);
            overview.setAvgDailyIncome(BigDecimal.ZERO);
        }
        
        // 计算增长率（需要查询上期数据）
        this.calculateGrowthRates(overview, query);
        
        // 获取趋势数据
        ReportQuery trendQuery = this.copyQuery(query);
        trendQuery.setDimension("DAILY");
        trendQuery.setLimit(30); // 最近30天
        overview.setTrendData(this.getTrend(trendQuery));
        
        // 获取Top分类
        ReportQuery categoryQuery = this.copyQuery(query);
        categoryQuery.setLimit(5);
        categoryQuery.setOrderBy("amount");
        categoryQuery.setOrderDirection("DESC");
        overview.setTopCategories(this.getCategoryStats(categoryQuery));
        
        // 获取钱包统计
        ReportQuery walletQuery = this.copyQuery(query);
        walletQuery.setLimit(10);
        overview.setWalletStats(this.getWalletStats(walletQuery));
        
        return overview;
    }

    @Override
    public List<ReportTrendResp> getTrend(ReportQuery query) {
        this.validateQueryAndPermission(query);
        this.setDefaultDateRange(query);
        
        // 设置默认维度
        if (StrUtil.isBlank(query.getDimension())) {
            query.setDimension("DAILY");
        }
        
        List<ReportTrendResp> trendList = reportMapper.selectReportTrend(query);
        
        // 填充缺失的日期数据
        if ("DAILY".equals(query.getDimension())) {
            trendList = this.fillMissingDates(trendList, query.getStartDate(), query.getEndDate());
        }
        
        return trendList;
    }

    @Override
    public List<ReportCategoryResp> getCategoryStats(ReportQuery query) {
        this.validateQueryAndPermission(query);
        this.setDefaultDateRange(query);
        
        List<ReportCategoryResp> categoryList = reportMapper.selectReportCategory(query);
        
        // 计算占比
        if (CollUtil.isNotEmpty(categoryList)) {
            BigDecimal totalAmount = categoryList.stream()
                    .map(ReportCategoryResp::getTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                categoryList.forEach(category -> {
                    BigDecimal percentage = NumberUtil.div(category.getTotalAmount(), totalAmount, 4)
                            .multiply(BigDecimal.valueOf(100))
                            .setScale(2, RoundingMode.HALF_UP);
                    category.setPercentage(percentage);
                });
            }
        }
        
        return categoryList;
    }

    @Override
    public List<ReportWalletResp> getWalletStats(ReportQuery query) {
        this.validateQueryAndPermission(query);
        this.setDefaultDateRange(query);
        
        List<ReportWalletResp> walletList = reportMapper.selectReportWallet(query);
        
        // 计算占比
        if (CollUtil.isNotEmpty(walletList)) {
            BigDecimal totalAmount = walletList.stream()
                    .map(wallet -> wallet.getIncomeAmount().add(wallet.getExpenseAmount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                walletList.forEach(wallet -> {
                    BigDecimal walletTotal = wallet.getIncomeAmount().add(wallet.getExpenseAmount());
                    BigDecimal percentage = NumberUtil.div(walletTotal, totalAmount, 4)
                            .multiply(BigDecimal.valueOf(100))
                            .setScale(2, RoundingMode.HALF_UP);
                    wallet.setPercentage(percentage);
                });
            }
        }
        
        return walletList;
    }

    @Override
    public List<ReportMemberResp> getMemberStats(ReportQuery query) {
        this.validateQueryAndPermission(query);
        this.setDefaultDateRange(query);
        
        List<ReportMemberResp> memberList = reportMapper.selectReportMember(query);
        
        // 计算占比
        if (CollUtil.isNotEmpty(memberList)) {
            BigDecimal totalAmount = memberList.stream()
                    .map(ReportMemberResp::getTotalContributionAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                memberList.forEach(member -> {
                    BigDecimal percentage = NumberUtil.div(member.getTotalContributionAmount(), totalAmount, 4)
                            .multiply(BigDecimal.valueOf(100))
                            .setScale(2, RoundingMode.HALF_UP);
                    member.setPercentage(percentage);
                });
            }
        }
        
        return memberList;
    }

    @Override
    public List<ReportTagResp> getTagStats(ReportQuery query) {
        this.validateQueryAndPermission(query);
        this.setDefaultDateRange(query);
        
        List<ReportTagResp> tagList = reportMapper.selectReportTag(query);
        
        // 计算占比
        if (CollUtil.isNotEmpty(tagList)) {
            BigDecimal totalAmount = tagList.stream()
                    .map(tag -> tag.getIncomeAmount().add(tag.getExpenseAmount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                tagList.forEach(tag -> {
                    BigDecimal tagTotal = tag.getIncomeAmount().add(tag.getExpenseAmount());
                    BigDecimal percentage = NumberUtil.div(tagTotal, totalAmount, 4)
                            .multiply(BigDecimal.valueOf(100))
                            .setScale(2, RoundingMode.HALF_UP);
                    tag.setPercentage(percentage);
                });
            }
        }
        
        return tagList;
    }

    @Override
    public ReportMapper.IncomeExpenseCompareResp getIncomeExpenseCompare(Long groupId, LocalDate startDate, LocalDate endDate) {
        Long userId = SecurityContextHolder.getUserId();
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组成员");
        
        return reportMapper.selectIncomeExpenseCompare(groupId, startDate, endDate);
    }

    @Override
    public List<ReportMapper.MonthlyStatResp> getMonthlyStats(Long groupId, Integer year) {
        Long userId = SecurityContextHolder.getUserId();
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组成员");
        
        return reportMapper.selectMonthlyStats(groupId, year);
    }

    @Override
    public List<ReportMapper.YearlyStatResp> getYearlyStats(Long groupId, Integer startYear, Integer endYear) {
        Long userId = SecurityContextHolder.getUserId();
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组成员");
        
        return reportMapper.selectYearlyStats(groupId, startYear, endYear);
    }

    @Override
    public List<ReportMapper.BudgetExecutionResp> getBudgetExecution(Long groupId, String month) {
        Long userId = SecurityContextHolder.getUserId();
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组成员");
        
        return reportMapper.selectBudgetExecution(groupId, month);
    }

    @Override
    public ReportMapper.TransactionFrequencyResp getTransactionFrequency(ReportQuery query) {
        this.validateQueryAndPermission(query);
        this.setDefaultDateRange(query);
        
        return reportMapper.selectTransactionFrequency(query);
    }

    @Override
    public List<ReportMapper.TimeSlotAnalysisResp> getTimeSlotAnalysis(ReportQuery query) {
        this.validateQueryAndPermission(query);
        this.setDefaultDateRange(query);
        
        return reportMapper.selectTimeSlotAnalysis(query);
    }

    @Override
    public ReportMapper.PeriodCompareResp getPeriodCompare(Long groupId, LocalDate currentStart, LocalDate currentEnd,
                                                          LocalDate previousStart, LocalDate previousEnd) {
        Long userId = SecurityContextHolder.getUserId();
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组成员");
        
        return reportMapper.selectPeriodCompare(groupId, currentStart, currentEnd, previousStart, previousEnd);
    }

    /**
     * 验证查询条件和权限
     */
    private void validateQueryAndPermission(ReportQuery query) {
        CheckUtils.throwIfNull(query.getGroupId(), "群组ID不能为空");
        
        Long userId = SecurityContextHolder.getUserId();
        CheckUtils.throwIf(!groupService.isMember(query.getGroupId(), userId), "您不是该群组成员");
    }

    /**
     * 设置默认日期范围
     */
    private void setDefaultDateRange(ReportQuery query) {
        if (query.getStartDate() == null || query.getEndDate() == null) {
            LocalDate endDate = LocalDate.now();
            LocalDate startDate = endDate.minusDays(30); // 默认最近30天
            
            if (query.getStartDate() == null) {
                query.setStartDate(startDate);
            }
            if (query.getEndDate() == null) {
                query.setEndDate(endDate);
            }
        }
    }

    /**
     * 复制查询条件
     */
    private ReportQuery copyQuery(ReportQuery source) {
        ReportQuery target = new ReportQuery();
        target.setGroupId(source.getGroupId());
        target.setStartDate(source.getStartDate());
        target.setEndDate(source.getEndDate());
        target.setType(source.getType());
        target.setCategoryIds(source.getCategoryIds());
        target.setTags(source.getTags());
        target.setWalletIds(source.getWalletIds());
        target.setMemberIds(source.getMemberIds());
        target.setCurrency(source.getCurrency());
        target.setIncludeSubCategories(source.getIncludeSubCategories());
        target.setMinAmount(source.getMinAmount());
        target.setMaxAmount(source.getMaxAmount());
        return target;
    }

    /**
     * 计算增长率
     */
    private void calculateGrowthRates(ReportOverviewResp overview, ReportQuery query) {
        // 计算上期日期范围
        LocalDate currentStart = query.getStartDate();
        LocalDate currentEnd = query.getEndDate();
        long daysBetween = currentEnd.toEpochDay() - currentStart.toEpochDay() + 1;
        
        LocalDate previousStart = currentStart.minusDays(daysBetween);
        LocalDate previousEnd = currentStart.minusDays(1);
        
        // 查询上期数据
        ReportQuery previousQuery = this.copyQuery(query);
        previousQuery.setStartDate(previousStart);
        previousQuery.setEndDate(previousEnd);
        
        ReportOverviewResp previousOverview = reportMapper.selectReportOverview(previousQuery);
        if (previousOverview != null) {
            // 计算收入增长率
            if (previousOverview.getTotalIncome().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal incomeGrowth = NumberUtil.div(
                        overview.getTotalIncome().subtract(previousOverview.getTotalIncome()),
                        previousOverview.getTotalIncome(),
                        4
                ).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
                overview.setIncomeGrowthRate(incomeGrowth);
            } else {
                overview.setIncomeGrowthRate(BigDecimal.valueOf(100));
            }
            
            // 计算支出增长率
            if (previousOverview.getTotalExpense().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal expenseGrowth = NumberUtil.div(
                        overview.getTotalExpense().subtract(previousOverview.getTotalExpense()),
                        previousOverview.getTotalExpense(),
                        4
                ).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
                overview.setExpenseGrowthRate(expenseGrowth);
            } else {
                overview.setExpenseGrowthRate(BigDecimal.valueOf(100));
            }
        } else {
            overview.setIncomeGrowthRate(BigDecimal.valueOf(100));
            overview.setExpenseGrowthRate(BigDecimal.valueOf(100));
        }
    }

    /**
     * 填充缺失的日期数据
     */
    private List<ReportTrendResp> fillMissingDates(List<ReportTrendResp> trendList, LocalDate startDate, LocalDate endDate) {
        if (CollUtil.isEmpty(trendList)) {
            return new ArrayList<>();
        }
        
        List<ReportTrendResp> result = new ArrayList<>();
        LocalDate currentDate = startDate;
        
        while (!currentDate.isAfter(endDate)) {
            String dateStr = currentDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            
            ReportTrendResp trend = trendList.stream()
                    .filter(t -> dateStr.equals(t.getDate()))
                    .findFirst()
                    .orElse(null);
            
            if (trend == null) {
                trend = new ReportTrendResp();
                trend.setDate(dateStr);
                trend.setIncomeAmount(BigDecimal.ZERO);
                trend.setExpenseAmount(BigDecimal.ZERO);
                trend.setNetAmount(BigDecimal.ZERO);
                trend.setTransactionCount(0);
                trend.setIncomeCount(0);
                trend.setExpenseCount(0);
            }
            
            result.add(trend);
            currentDate = currentDate.plusDays(1);
        }
        
        return result;
    }

    @Override
    public void exportFinancialReport(ReportQuery query, HttpServletResponse response) {
        this.validateQueryAndPermission(query);
        this.setDefaultDateRange(query);

        // 获取报表数据
        ReportOverviewResp overview = this.getOverview(query);
        List<ReportTrendResp> trendList = this.getTrend(query);
        List<ReportCategoryResp> categoryList = this.getCategoryStats(query);

        // 构建导出数据
        List<FinancialReportExportResp> exportData = new ArrayList<>();

        // 添加总览数据
        FinancialReportExportResp overviewRow = new FinancialReportExportResp();
        overviewRow.setType("总览");
        overviewRow.setName("汇总");
        overviewRow.setIncomeAmount(overview.getTotalIncome());
        overviewRow.setExpenseAmount(overview.getTotalExpense());
        overviewRow.setNetAmount(overview.getNetIncome());
        overviewRow.setTransactionCount(overview.getTransactionCount());
        overviewRow.setAvgAmount(overview.getAvgTransactionAmount());
        exportData.add(overviewRow);

        // 添加分类数据
        if (CollUtil.isNotEmpty(categoryList)) {
            categoryList.forEach(category -> {
                FinancialReportExportResp categoryRow = new FinancialReportExportResp();
                categoryRow.setType("分类");
                categoryRow.setName(category.getCategoryName());
                categoryRow.setIncomeAmount(BigDecimal.ZERO);
                categoryRow.setExpenseAmount(category.getTotalAmount());
                categoryRow.setNetAmount(category.getTotalAmount().negate());
                categoryRow.setTransactionCount(category.getTransactionCount());
                categoryRow.setAvgAmount(category.getAvgAmount());
                categoryRow.setPercentage(category.getPercentage());
                exportData.add(categoryRow);
            });
        }

        String fileName = String.format("财务报表_%s_%s.xlsx",
                query.getStartDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                query.getEndDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));

        ExcelUtils.export(exportData, "财务报表", FinancialReportExportResp.class, fileName, response);
    }

    @Override
    public void exportCategoryReport(ReportQuery query, HttpServletResponse response) {
        this.validateQueryAndPermission(query);
        this.setDefaultDateRange(query);

        List<ReportCategoryResp> categoryList = this.getCategoryStats(query);

        String fileName = String.format("分类统计报表_%s_%s.xlsx",
                query.getStartDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                query.getEndDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));

        ExcelUtils.export(categoryList, "分类统计", ReportCategoryResp.class, fileName, response);
    }

    @Override
    public void exportMemberReport(ReportQuery query, HttpServletResponse response) {
        this.validateQueryAndPermission(query);
        this.setDefaultDateRange(query);

        List<ReportMemberResp> memberList = this.getMemberStats(query);

        String fileName = String.format("成员统计报表_%s_%s.xlsx",
                query.getStartDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                query.getEndDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));

        ExcelUtils.export(memberList, "成员统计", ReportMemberResp.class, fileName, response);
    }

    @Override
    public CustomReportResp generateCustomReport(ReportQuery query) {
        this.validateQueryAndPermission(query);
        this.setDefaultDateRange(query);

        CustomReportResp customReport = new CustomReportResp();
        customReport.setTitle("自定义财务报表");
        customReport.setDescription(String.format("时间范围：%s 至 %s",
                query.getStartDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                query.getEndDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));

        // 根据查询条件获取相应数据
        if (StrUtil.isNotBlank(query.getDimension())) {
            customReport.setTrendData(this.getTrend(query));
        }

        if (CollUtil.isEmpty(query.getCategoryIds()) || query.getCategoryIds().size() > 1) {
            customReport.setCategoryData(this.getCategoryStats(query));
        }

        if (CollUtil.isEmpty(query.getWalletIds()) || query.getWalletIds().size() > 1) {
            customReport.setWalletData(this.getWalletStats(query));
        }

        if (CollUtil.isEmpty(query.getMemberIds()) || query.getMemberIds().size() > 1) {
            customReport.setMemberData(this.getMemberStats(query));
        }

        if (CollUtil.isNotEmpty(query.getTags())) {
            customReport.setTagData(this.getTagStats(query));
        }

        return customReport;
    }

    @Override
    public DashboardResp getDashboard(Long groupId) {
        Long userId = SecurityContextHolder.getUserId();
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组成员");

        DashboardResp dashboard = new DashboardResp();

        // 构建查询条件（最近30天）
        ReportQuery query = new ReportQuery();
        query.setGroupId(groupId);
        query.setStartDate(LocalDate.now().minusDays(30));
        query.setEndDate(LocalDate.now());

        // 获取总览数据
        dashboard.setOverview(this.getOverview(query));

        // 获取最近7天趋势
        ReportQuery trendQuery = this.copyQuery(query);
        trendQuery.setStartDate(LocalDate.now().minusDays(7));
        trendQuery.setDimension("DAILY");
        dashboard.setRecentTrend(this.getTrend(trendQuery));

        // 获取Top5分类
        ReportQuery categoryQuery = this.copyQuery(query);
        categoryQuery.setLimit(5);
        categoryQuery.setOrderBy("amount");
        categoryQuery.setOrderDirection("DESC");
        dashboard.setTopCategories(this.getCategoryStats(categoryQuery));

        // 获取钱包汇总
        dashboard.setWalletSummary(this.getWalletStats(query));

        // 获取成员活跃度
        ReportQuery memberQuery = this.copyQuery(query);
        memberQuery.setLimit(10);
        memberQuery.setOrderBy("activity");
        memberQuery.setOrderDirection("DESC");
        dashboard.setMemberActivity(this.getMemberStats(memberQuery));

        return dashboard;
    }

    @Override
    public QuickStatsResp getQuickStats(Long groupId, Integer days) {
        Long userId = SecurityContextHolder.getUserId();
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组成员");

        if (days == null || days <= 0) {
            days = 7; // 默认7天
        }

        ReportQuery query = new ReportQuery();
        query.setGroupId(groupId);
        query.setStartDate(LocalDate.now().minusDays(days));
        query.setEndDate(LocalDate.now());

        ReportOverviewResp overview = this.getOverview(query);

        QuickStatsResp quickStats = new QuickStatsResp();
        quickStats.setTotalIncome(overview.getTotalIncome());
        quickStats.setTotalExpense(overview.getTotalExpense());
        quickStats.setNetIncome(overview.getNetIncome());
        quickStats.setTransactionCount(overview.getTransactionCount());
        quickStats.setAvgDailyExpense(overview.getAvgDailyExpense());

        // 获取Top分类和钱包
        if (CollUtil.isNotEmpty(overview.getTopCategories())) {
            quickStats.setTopCategory(overview.getTopCategories().get(0).getCategoryName());
        }

        if (CollUtil.isNotEmpty(overview.getWalletStats())) {
            quickStats.setTopWallet(overview.getWalletStats().get(0).getWalletName());
        }

        return quickStats;
    }

    /**
     * 财务报表导出响应类
     */
    public static class FinancialReportExportResp {
        @com.alibaba.excel.annotation.ExcelProperty("类型")
        private String type;
        @com.alibaba.excel.annotation.ExcelProperty("名称")
        private String name;
        @com.alibaba.excel.annotation.ExcelProperty("收入金额")
        private BigDecimal incomeAmount;
        @com.alibaba.excel.annotation.ExcelProperty("支出金额")
        private BigDecimal expenseAmount;
        @com.alibaba.excel.annotation.ExcelProperty("净收入")
        private BigDecimal netAmount;
        @com.alibaba.excel.annotation.ExcelProperty("交易笔数")
        private Integer transactionCount;
        @com.alibaba.excel.annotation.ExcelProperty("平均金额")
        private BigDecimal avgAmount;
        @com.alibaba.excel.annotation.ExcelProperty("占比(%)")
        private BigDecimal percentage;

        // Getters and Setters
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public BigDecimal getIncomeAmount() { return incomeAmount; }
        public void setIncomeAmount(BigDecimal incomeAmount) { this.incomeAmount = incomeAmount; }
        public BigDecimal getExpenseAmount() { return expenseAmount; }
        public void setExpenseAmount(BigDecimal expenseAmount) { this.expenseAmount = expenseAmount; }
        public BigDecimal getNetAmount() { return netAmount; }
        public void setNetAmount(BigDecimal netAmount) { this.netAmount = netAmount; }
        public Integer getTransactionCount() { return transactionCount; }
        public void setTransactionCount(Integer transactionCount) { this.transactionCount = transactionCount; }
        public BigDecimal getAvgAmount() { return avgAmount; }
        public void setAvgAmount(BigDecimal avgAmount) { this.avgAmount = avgAmount; }
        public BigDecimal getPercentage() { return percentage; }
        public void setPercentage(BigDecimal percentage) { this.percentage = percentage; }
    }
}
