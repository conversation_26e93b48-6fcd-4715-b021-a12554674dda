-- API密钥表
CREATE TABLE `acc_api_key` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `app_name` VARCHAR(50) NOT NULL COMMENT '应用名称',
    `api_key` VARCHAR(64) NOT NULL COMMENT 'API密钥',
    `api_secret` VARCHAR(128) NOT NULL COMMENT 'API密钥（加密后）',
    `description` VARCHAR(200) COMMENT '描述',
    `scopes` JSON COMMENT '权限范围（JSON格式）',
    `ip_whitelist` JSON COMMENT 'IP白名单（JSON格式）',
    `rate_limit` INT DEFAULT 100 COMMENT '速率限制（每分钟请求数）',
    `is_active` TINYINT(1) DEFAULT 1 COMMENT '是否激活',
    `expires_at` DATETIME COMMENT '过期时间',
    `last_used_at` DATETIME COMMENT '最后使用时间',
    `usage_count` BIGINT DEFAULT 0 COMMENT '使用次数',
    `create_user` BIGINT COMMENT '创建人',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` BIGINT COMMENT '修改人',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_api_key` (`api_key`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_app_name` (`app_name`),
    KEY `idx_is_active` (`is_active`),
    KEY `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API密钥表';

-- API调用日志表
CREATE TABLE `acc_api_call_log` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `api_key_id` BIGINT NOT NULL COMMENT 'API密钥ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `request_path` VARCHAR(255) NOT NULL COMMENT '请求路径',
    `request_method` VARCHAR(10) NOT NULL COMMENT '请求方法',
    `client_ip` VARCHAR(45) NOT NULL COMMENT '客户端IP',
    `user_agent` VARCHAR(500) COMMENT '用户代理',
    `response_status` INT COMMENT '响应状态码',
    `response_time` BIGINT COMMENT '响应时间（毫秒）',
    `request_size` BIGINT COMMENT '请求大小（字节）',
    `response_size` BIGINT COMMENT '响应大小（字节）',
    `error_message` TEXT COMMENT '错误信息',
    `call_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '调用时间',
    `create_user` BIGINT COMMENT '创建人',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` BIGINT COMMENT '修改人',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_api_key_id` (`api_key_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_call_time` (`call_time`),
    KEY `idx_request_path` (`request_path`),
    KEY `idx_client_ip` (`client_ip`),
    KEY `idx_response_status` (`response_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API调用日志表';

-- API限流记录表
CREATE TABLE `acc_api_rate_limit` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `api_key_id` BIGINT NOT NULL COMMENT 'API密钥ID',
    `client_ip` VARCHAR(45) NOT NULL COMMENT '客户端IP',
    `window_start` DATETIME NOT NULL COMMENT '时间窗口开始时间',
    `request_count` INT DEFAULT 0 COMMENT '请求次数',
    `last_request_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '最后请求时间',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_api_key_ip_window` (`api_key_id`, `client_ip`, `window_start`),
    KEY `idx_window_start` (`window_start`),
    KEY `idx_last_request_time` (`last_request_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API限流记录表';

-- 插入默认权限范围数据
INSERT INTO `acc_api_key` (`id`, `user_id`, `app_name`, `api_key`, `api_secret`, `description`, `scopes`, `rate_limit`, `is_active`, `create_user`, `create_time`) VALUES
(1, 1, '系统默认', 'ak_default_key_123456789', '$2a$10$encrypted_secret_hash', '系统默认API密钥，用于测试', 
 JSON_ARRAY('groups:read', 'groups:write', 'transactions:read', 'transactions:write', 'statistics:read'), 
 1000, 1, 1, NOW());

-- 创建索引优化查询性能
CREATE INDEX `idx_api_call_log_composite` ON `acc_api_call_log` (`api_key_id`, `call_time`, `response_status`);
CREATE INDEX `idx_api_rate_limit_cleanup` ON `acc_api_rate_limit` (`window_start`, `last_request_time`);

-- 添加外键约束
ALTER TABLE `acc_api_key` ADD CONSTRAINT `fk_api_key_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE;
ALTER TABLE `acc_api_call_log` ADD CONSTRAINT `fk_api_call_log_api_key` FOREIGN KEY (`api_key_id`) REFERENCES `acc_api_key` (`id`) ON DELETE CASCADE;
ALTER TABLE `acc_api_call_log` ADD CONSTRAINT `fk_api_call_log_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE;
ALTER TABLE `acc_api_rate_limit` ADD CONSTRAINT `fk_api_rate_limit_api_key` FOREIGN KEY (`api_key_id`) REFERENCES `acc_api_key` (`id`) ON DELETE CASCADE;
