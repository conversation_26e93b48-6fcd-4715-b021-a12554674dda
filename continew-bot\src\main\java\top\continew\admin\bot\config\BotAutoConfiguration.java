package top.continew.admin.bot.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.telegram.telegrambots.meta.TelegramBotsApi;
import org.telegram.telegrambots.meta.exceptions.TelegramApiException;
import org.telegram.telegrambots.updatesreceivers.DefaultBotSession;
import top.continew.admin.bot.telegram.TelegramBotService;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * 机器人自动配置
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties({TelegramBotConfig.class, DiscordBotConfig.class})
public class BotAutoConfiguration {

    private final TelegramBotConfig telegramBotConfig;
    private final DiscordBotConfig discordBotConfig;
    private TelegramBotsApi telegramBotsApi;

    @PostConstruct
    public void init() {
        log.info("机器人模块初始化开始...");
        
        // 初始化Telegram机器人
        if (telegramBotConfig.getEnabled()) {
            initTelegramBot();
        }
        
        // 初始化Discord机器人
        if (discordBotConfig.getEnabled()) {
            initDiscordBot();
        }
        
        log.info("机器人模块初始化完成");
    }

    /**
     * 初始化Telegram机器人
     */
    private void initTelegramBot() {
        try {
            telegramBotsApi = new TelegramBotsApi(DefaultBotSession.class);
            log.info("Telegram机器人API初始化成功");
        } catch (TelegramApiException e) {
            log.error("Telegram机器人API初始化失败", e);
        }
    }

    /**
     * 初始化Discord机器人
     */
    private void initDiscordBot() {
        log.info("Discord机器人初始化...");
        // Discord机器人的初始化在DiscordBotService中处理
        log.info("Discord机器人初始化准备完成");
    }

    /**
     * 注册Telegram机器人
     */
    @Bean
    @ConditionalOnProperty(prefix = "bot.telegram", name = "enabled", havingValue = "true")
    public TelegramBotService telegramBotService() {
        // TelegramBotService会通过依赖注入自动创建
        return null; // 实际的Bean由Spring自动管理
    }

    /**
     * 注册Discord机器人
     */
    @Bean
    @ConditionalOnProperty(prefix = "bot.discord", name = "enabled", havingValue = "true")
    public top.continew.admin.bot.discord.DiscordBotService discordBotService() {
        // DiscordBotService会通过依赖注入自动创建
        return null; // 实际的Bean由Spring自动管理
    }

    /**
     * 注册Telegram机器人到API
     */
    @PostConstruct
    public void registerTelegramBot() {
        if (telegramBotConfig.getEnabled() && telegramBotsApi != null) {
            try {
                // 注册机器人会在TelegramBotService的@PostConstruct中处理
                log.info("Telegram机器人注册准备完成");
            } catch (Exception e) {
                log.error("Telegram机器人注册失败", e);
            }
        }
    }

    @PreDestroy
    public void destroy() {
        log.info("机器人模块销毁...");
        
        // 清理Telegram机器人资源
        if (telegramBotsApi != null) {
            try {
                // 清理资源
                log.info("Telegram机器人资源清理完成");
            } catch (Exception e) {
                log.error("Telegram机器人资源清理失败", e);
            }
        }
        
        log.info("机器人模块销毁完成");
    }
}
