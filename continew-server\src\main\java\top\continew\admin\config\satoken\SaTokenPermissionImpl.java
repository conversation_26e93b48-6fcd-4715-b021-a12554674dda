/*
 * Copyright (c) 2022-present <PERSON><PERSON><PERSON> Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.config.satoken;

import cn.dev33.satoken.stp.StpInterface;
import top.continew.admin.common.context.UserContext;
import top.continew.admin.common.context.UserContextHolder;

import java.util.ArrayList;
import java.util.List;

/**
 * Sa-Token 权限认证实现
 *
 * <AUTHOR>
 * @since 2023/3/1 22:28
 */
public class SaTokenPermissionImpl implements StpInterface {

    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        UserContext userContext = UserContextHolder.getContext();
        return new ArrayList<>(userContext.getPermissions());
    }

    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        UserContext userContext = UserContextHolder.getContext();
        return new ArrayList<>(userContext.getRoleCodes());
    }
}
