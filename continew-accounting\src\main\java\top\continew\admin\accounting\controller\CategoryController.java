package top.continew.admin.accounting.controller;

import cn.hutool.core.lang.tree.Tree;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.accounting.model.query.CategoryQuery;
import top.continew.admin.accounting.model.req.CategoryCreateReq;
import top.continew.admin.accounting.model.req.CategoryUpdateReq;
import top.continew.admin.accounting.model.resp.CategoryDetailResp;
import top.continew.admin.accounting.model.resp.CategoryListResp;
import top.continew.admin.accounting.service.CategoryService;
import top.continew.admin.common.base.controller.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.R;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 分类管理 API
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "分类管理 API")
@RestController
@RequiredArgsConstructor
@Validated
@CrudRequestMapping(value = "/accounting/category", api = {Api.PAGE, Api.LIST, Api.GET, Api.CREATE, Api.UPDATE, Api.DELETE, Api.EXPORT, Api.TREE, Api.DICT_TREE})
public class CategoryController extends BaseController<CategoryService, CategoryListResp, CategoryDetailResp, CategoryQuery, CategoryCreateReq> {

    @Operation(summary = "更新分类", description = "更新分类信息")
    @PutMapping("/{id}")
    public R<Void> update(@Parameter(description = "分类ID", example = "1") @PathVariable Long id,
                         @Validated @RequestBody CategoryUpdateReq req) {
        baseService.update(req, id);
        return R.ok();
    }

    @Operation(summary = "查询群组分类列表", description = "查询指定群组的分类列表")
    @GetMapping("/group/{groupId}")
    public R<List<CategoryListResp>> getGroupCategories(
            @Parameter(description = "群组ID", example = "1") @PathVariable Long groupId,
            @Parameter(description = "分类类型", example = "EXPENSE") @RequestParam(required = false) String type) {
        return R.ok(baseService.getGroupCategories(groupId, type));
    }

    @Operation(summary = "查询群组分类树", description = "查询指定群组的分类树结构")
    @GetMapping("/group/{groupId}/tree")
    public R<List<Tree<Long>>> getGroupCategoryTree(
            @Parameter(description = "群组ID", example = "1") @PathVariable Long groupId,
            @Parameter(description = "分类类型", example = "EXPENSE") @RequestParam(required = false) String type) {
        return R.ok(baseService.getGroupCategoryTree(groupId, type));
    }

    @Operation(summary = "创建默认分类", description = "为群组创建默认分类")
    @PostMapping("/group/{groupId}/default")
    public R<Void> createDefaultCategories(
            @Parameter(description = "群组ID", example = "1") @PathVariable Long groupId) {
        baseService.createDefaultCategories(groupId);
        return R.ok();
    }

    @Operation(summary = "移动分类", description = "移动分类到新的父分类下")
    @PutMapping("/{id}/move")
    public R<Void> moveCategory(
            @Parameter(description = "分类ID", example = "1") @PathVariable Long id,
            @Parameter(description = "新父分类ID", example = "2") @RequestParam Long newParentId) {
        baseService.moveCategory(id, newParentId);
        return R.ok();
    }

    @Operation(summary = "复制分类", description = "复制分类到其他群组")
    @PostMapping("/{id}/copy")
    public R<Long> copyToGroup(
            @Parameter(description = "分类ID", example = "1") @PathVariable Long id,
            @Parameter(description = "目标群组ID", example = "2") @RequestParam Long targetGroupId) {
        Long newCategoryId = baseService.copyToGroup(id, targetGroupId, getCurrentUserId());
        return R.ok(newCategoryId);
    }

    @Operation(summary = "批量导入分类", description = "批量导入分类")
    @PostMapping("/group/{groupId}/import")
    public R<CategoryService.ImportResult> batchImport(
            @Parameter(description = "群组ID", example = "1") @PathVariable Long groupId,
            @Valid @RequestBody @NotEmpty(message = "导入数据不能为空") List<CategoryCreateReq> categories) {
        CategoryService.ImportResult result = baseService.batchImport(groupId, categories, getCurrentUserId());
        return R.ok(result);
    }

    @Operation(summary = "检查分类是否可删除", description = "检查分类是否可以删除")
    @GetMapping("/{id}/can-delete")
    public R<Boolean> canDelete(
            @Parameter(description = "分类ID", example = "1") @PathVariable Long id) {
        return R.ok(baseService.canDelete(id));
    }

    @Operation(summary = "级联删除分类", description = "级联删除分类及其子分类")
    @DeleteMapping("/cascade")
    public R<Void> cascadeDelete(
            @Parameter(description = "分类ID列表") @RequestBody @NotEmpty(message = "分类ID列表不能为空") List<Long> categoryIds) {
        baseService.cascadeDelete(categoryIds);
        return R.ok();
    }

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        return top.continew.starter.security.context.SecurityContextHolder.getUserId();
    }
}
