package top.continew.admin.accounting.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 预算查询条件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "预算查询条件")
public class BudgetQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    @NotNull(message = "群组ID不能为空")
    private Long groupId;

    /**
     * 预算名称（模糊查询）
     */
    @Schema(description = "预算名称", example = "年度预算")
    private String budgetName;

    /**
     * 预算类型
     */
    @Schema(description = "预算类型", example = "ANNUAL", allowableValues = {"ANNUAL", "QUARTERLY", "MONTHLY", "WEEKLY", "CUSTOM"})
    private String budgetType;

    /**
     * 预算周期
     */
    @Schema(description = "预算周期", example = "YEARLY", allowableValues = {"YEARLY", "QUARTERLY", "MONTHLY", "WEEKLY", "DAILY"})
    private String budgetPeriod;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "ACTIVE", allowableValues = {"DRAFT", "ACTIVE", "EXPIRED", "SUSPENDED"})
    private String status;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    /**
     * 开始日期范围 - 开始
     */
    @Schema(description = "开始日期范围 - 开始", example = "2025-01-01")
    private LocalDate startDateFrom;

    /**
     * 开始日期范围 - 结束
     */
    @Schema(description = "开始日期范围 - 结束", example = "2025-12-31")
    private LocalDate startDateTo;

    /**
     * 结束日期范围 - 开始
     */
    @Schema(description = "结束日期范围 - 开始", example = "2025-01-01")
    private LocalDate endDateFrom;

    /**
     * 结束日期范围 - 结束
     */
    @Schema(description = "结束日期范围 - 结束", example = "2025-12-31")
    private LocalDate endDateTo;

    /**
     * 总预算金额范围 - 最小值
     */
    @Schema(description = "总预算金额范围 - 最小值", example = "10000.00")
    private BigDecimal minTotalAmount;

    /**
     * 总预算金额范围 - 最大值
     */
    @Schema(description = "总预算金额范围 - 最大值", example = "100000.00")
    private BigDecimal maxTotalAmount;

    /**
     * 币种
     */
    @Schema(description = "币种", example = "CNY")
    private String currency;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID", example = "1")
    private Long createdBy;

    /**
     * 创建时间范围 - 开始
     */
    @Schema(description = "创建时间范围 - 开始", example = "2025-01-01")
    private LocalDate createdFrom;

    /**
     * 创建时间范围 - 结束
     */
    @Schema(description = "创建时间范围 - 结束", example = "2025-12-31")
    private LocalDate createdTo;

    /**
     * 分配类型列表
     */
    @Schema(description = "分配类型列表", example = "[\"CATEGORY\", \"MEMBER\"]")
    private List<String> allocationTypes;

    /**
     * 分配目标ID列表
     */
    @Schema(description = "分配目标ID列表", example = "[1, 2, 3]")
    private List<Long> targetIds;

    /**
     * 预算执行状态
     */
    @Schema(description = "预算执行状态", example = "NORMAL", allowableValues = {"NORMAL", "WARNING", "EXCEEDED"})
    private String executionStatus;

    /**
     * 预算使用率范围 - 最小值
     */
    @Schema(description = "预算使用率范围 - 最小值", example = "0.50")
    private BigDecimal minUsageRate;

    /**
     * 预算使用率范围 - 最大值
     */
    @Schema(description = "预算使用率范围 - 最大值", example = "0.90")
    private BigDecimal maxUsageRate;

    /**
     * 是否有预警
     */
    @Schema(description = "是否有预警", example = "true")
    private Boolean hasAlert;

    /**
     * 是否需要审批
     */
    @Schema(description = "是否需要审批", example = "true")
    private Boolean requiresApproval;

    /**
     * 标签列表
     */
    @Schema(description = "标签列表", example = "[\"重要\", \"年度\"]")
    private List<String> tags;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段", example = "createdAt", allowableValues = {"budgetName", "totalAmount", "usageRate", "createdAt", "updatedAt"})
    private String sortBy;

    /**
     * 排序方向
     */
    @Schema(description = "排序方向", example = "DESC", allowableValues = {"ASC", "DESC"})
    private String sortDirection;
}
