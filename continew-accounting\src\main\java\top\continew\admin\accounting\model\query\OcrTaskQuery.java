package top.continew.admin.accounting.model.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.common.model.query.PageQuery;

import java.time.LocalDateTime;
import java.util.List;

/**
 * OCR任务查询条件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "OCR任务查询条件")
public class OcrTaskQuery extends PageQuery {

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 任务ID
     */
    @Schema(description = "任务ID", example = "ocr_20250101_001")
    private String taskId;

    /**
     * 任务状态
     */
    @Schema(description = "任务状态", example = "SUCCESS", allowableValues = {"PROCESSING", "SUCCESS", "FAILED", "TIMEOUT"})
    private String status;

    /**
     * OCR引擎类型
     */
    @Schema(description = "OCR引擎类型", example = "BAIDU", allowableValues = {"BAIDU", "TENCENT", "ALIYUN", "GOOGLE", "AZURE"})
    private String ocrEngine;

    /**
     * 识别模式
     */
    @Schema(description = "识别模式", example = "RECEIPT", allowableValues = {"RECEIPT", "INVOICE", "GENERAL"})
    private String recognitionMode;

    /**
     * 是否自动创建账单
     */
    @Schema(description = "是否自动创建账单", example = "true")
    private Boolean autoCreateTransaction;

    /**
     * 是否创建了账单
     */
    @Schema(description = "是否创建了账单", example = "true")
    private Boolean transactionCreated;

    /**
     * 创建的账单ID
     */
    @Schema(description = "创建的账单ID", example = "123")
    private Long transactionId;

    /**
     * 商家名称
     */
    @Schema(description = "商家名称", example = "星巴克")
    private String merchantName;

    /**
     * 识别的分类
     */
    @Schema(description = "识别的分类", example = "餐饮")
    private String recognizedCategory;

    /**
     * 识别的标签
     */
    @Schema(description = "识别的标签")
    private List<String> recognizedTags;

    /**
     * 最小置信度
     */
    @Schema(description = "最小置信度", example = "0.8")
    private Double minConfidence;

    /**
     * 最大置信度
     */
    @Schema(description = "最大置信度", example = "1.0")
    private Double maxConfidence;

    /**
     * 最小处理时间（毫秒）
     */
    @Schema(description = "最小处理时间（毫秒）", example = "1000")
    private Long minProcessingTime;

    /**
     * 最大处理时间（毫秒）
     */
    @Schema(description = "最大处理时间（毫秒）", example = "5000")
    private Long maxProcessingTime;

    /**
     * 识别时间开始
     */
    @Schema(description = "识别时间开始", example = "2025-01-01 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime recognitionTimeStart;

    /**
     * 识别时间结束
     */
    @Schema(description = "识别时间结束", example = "2025-01-31 23:59:59")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime recognitionTimeEnd;

    /**
     * 创建时间开始
     */
    @Schema(description = "创建时间开始", example = "2025-01-01 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    @Schema(description = "创建时间结束", example = "2025-01-31 23:59:59")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeEnd;

    /**
     * 创建人
     */
    @Schema(description = "创建人", example = "1")
    private Long createdBy;

    /**
     * 原始文件名
     */
    @Schema(description = "原始文件名", example = "receipt.jpg")
    private String originalFileName;

    /**
     * 图片格式
     */
    @Schema(description = "图片格式", example = "JPEG", allowableValues = {"JPEG", "PNG", "BMP", "WEBP", "TIFF"})
    private String imageFormat;

    /**
     * 最小文件大小（字节）
     */
    @Schema(description = "最小文件大小（字节）", example = "1024")
    private Long minFileSize;

    /**
     * 最大文件大小（字节）
     */
    @Schema(description = "最大文件大小（字节）", example = "10485760")
    private Long maxFileSize;

    /**
     * 错误代码
     */
    @Schema(description = "错误代码", example = "OCR_001")
    private String errorCode;

    /**
     * 是否有错误
     */
    @Schema(description = "是否有错误", example = "false")
    private Boolean hasError;

    /**
     * 关键字搜索
     */
    @Schema(description = "关键字搜索", example = "星巴克")
    private String keyword;

    /**
     * 是否异步处理
     */
    @Schema(description = "是否异步处理", example = "false")
    private Boolean asyncProcessing;

    /**
     * 语言
     */
    @Schema(description = "语言", example = "zh-CN", allowableValues = {"zh-CN", "en-US", "ja-JP", "ko-KR"})
    private String language;

    /**
     * 识别精度
     */
    @Schema(description = "识别精度", example = "HIGH", allowableValues = {"LOW", "MEDIUM", "HIGH"})
    private String accuracy;

    /**
     * 是否启用智能分类
     */
    @Schema(description = "是否启用智能分类", example = "true")
    private Boolean enableSmartCategory;

    /**
     * 是否启用智能标签
     */
    @Schema(description = "是否启用智能标签", example = "true")
    private Boolean enableSmartTags;

    /**
     * 是否保存原始图片
     */
    @Schema(description = "是否保存原始图片", example = "true")
    private Boolean saveOriginalImage;

    /**
     * 是否启用表格识别
     */
    @Schema(description = "是否启用表格识别", example = "false")
    private Boolean enableTableRecognition;

    /**
     * 是否启用手写识别
     */
    @Schema(description = "是否启用手写识别", example = "false")
    private Boolean enableHandwritingRecognition;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段", example = "recognitionTime", allowableValues = {
            "taskId", "status", "confidence", "processingTime", "recognitionTime", "createTime"
    })
    private String sortField;

    /**
     * 排序方向
     */
    @Schema(description = "排序方向", example = "DESC", allowableValues = {"ASC", "DESC"})
    private String sortOrder = "DESC";
}
