package top.continew.admin.accounting.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.accounting.model.resp.UsageStatisticsResp;
import top.continew.admin.accounting.service.UsageStatisticsService;
import top.continew.starter.core.util.response.R;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.time.LocalDate;
import java.util.List;

/**
 * 使用量统计管理 API
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "使用量统计管理 API")
@RestController
@RequestMapping("/accounting/usage-statistics")
@RequiredArgsConstructor
@Validated
public class UsageStatisticsController {

    private final UsageStatisticsService usageStatisticsService;

    @Operation(summary = "记录交易使用量", description = "记录群组的交易使用量")
    @PostMapping("/group/{groupId}/transaction")
    public R<Void> recordTransactionUsage(
            @Parameter(description = "群组ID", example = "1") 
            @PathVariable Long groupId) {
        usageStatisticsService.recordTransactionUsage(groupId);
        return R.ok();
    }

    @Operation(summary = "记录OCR使用量", description = "记录群组的OCR使用量")
    @PostMapping("/group/{groupId}/ocr")
    public R<Void> recordOcrUsage(
            @Parameter(description = "群组ID", example = "1") 
            @PathVariable Long groupId) {
        usageStatisticsService.recordOcrUsage(groupId);
        return R.ok();
    }

    @Operation(summary = "记录API使用量", description = "记录群组的API调用使用量")
    @PostMapping("/group/{groupId}/api")
    public R<Void> recordApiUsage(
            @Parameter(description = "群组ID", example = "1") 
            @PathVariable Long groupId) {
        usageStatisticsService.recordApiUsage(groupId);
        return R.ok();
    }

    @Operation(summary = "记录存储使用量", description = "记录群组的存储使用量")
    @PostMapping("/group/{groupId}/storage")
    public R<Void> recordStorageUsage(
            @Parameter(description = "群组ID", example = "1") 
            @PathVariable Long groupId,
            @Parameter(description = "存储字节数", example = "1048576") 
            @RequestParam @NotNull @Positive Long storageBytes) {
        usageStatisticsService.recordStorageUsage(groupId, storageBytes);
        return R.ok();
    }

    @Operation(summary = "记录导出使用量", description = "记录群组的导出使用量")
    @PostMapping("/group/{groupId}/export")
    public R<Void> recordExportUsage(
            @Parameter(description = "群组ID", example = "1") 
            @PathVariable Long groupId) {
        usageStatisticsService.recordExportUsage(groupId);
        return R.ok();
    }

    @Operation(summary = "记录Webhook使用量", description = "记录群组的Webhook使用量")
    @PostMapping("/group/{groupId}/webhook")
    public R<Void> recordWebhookUsage(
            @Parameter(description = "群组ID", example = "1") 
            @PathVariable Long groupId) {
        usageStatisticsService.recordWebhookUsage(groupId);
        return R.ok();
    }

    @Operation(summary = "更新活跃用户数", description = "更新群组的活跃用户数")
    @PutMapping("/group/{groupId}/active-users")
    public R<Void> updateActiveUsers(
            @Parameter(description = "群组ID", example = "1") 
            @PathVariable Long groupId,
            @Parameter(description = "活跃用户数", example = "10") 
            @RequestParam @NotNull @Positive Integer activeUsers) {
        usageStatisticsService.updateActiveUsers(groupId, activeUsers);
        return R.ok();
    }

    @Operation(summary = "获取当月使用统计", description = "获取群组当月的使用统计")
    @GetMapping("/group/{groupId}/current-month")
    public R<UsageStatisticsResp> getCurrentMonthUsage(
            @Parameter(description = "群组ID", example = "1") 
            @PathVariable Long groupId) {
        return R.ok(usageStatisticsService.getCurrentMonthUsage(groupId));
    }

    @Operation(summary = "获取使用趋势", description = "获取群组指定时间段的使用趋势")
    @GetMapping("/group/{groupId}/trend")
    public R<List<UsageStatisticsResp>> getUsageTrend(
            @Parameter(description = "群组ID", example = "1") 
            @PathVariable Long groupId,
            @Parameter(description = "开始日期", example = "2025-01-01") 
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期", example = "2025-01-31") 
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        return R.ok(usageStatisticsService.getUsageTrend(groupId, startDate, endDate));
    }

    @Operation(summary = "获取总使用量", description = "获取群组指定时间段的总使用量")
    @GetMapping("/group/{groupId}/total")
    public R<UsageStatisticsResp> getTotalUsage(
            @Parameter(description = "群组ID", example = "1") 
            @PathVariable Long groupId,
            @Parameter(description = "开始日期", example = "2025-01-01") 
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期", example = "2025-01-31") 
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        return R.ok(usageStatisticsService.getTotalUsage(groupId, startDate, endDate));
    }

    @Operation(summary = "检查使用限制", description = "检查群组是否超出使用限制")
    @GetMapping("/group/{groupId}/limit/{limitType}/exceeded")
    public R<Boolean> isUsageLimitExceeded(
            @Parameter(description = "群组ID", example = "1") 
            @PathVariable Long groupId,
            @Parameter(description = "限制类型", example = "maxTransactionsPerMonth") 
            @PathVariable String limitType) {
        return R.ok(usageStatisticsService.isUsageLimitExceeded(groupId, limitType));
    }

    @Operation(summary = "获取剩余使用量", description = "获取群组剩余的使用量")
    @GetMapping("/group/{groupId}/limit/{limitType}/remaining")
    public R<Integer> getRemainingUsage(
            @Parameter(description = "群组ID", example = "1") 
            @PathVariable Long groupId,
            @Parameter(description = "限制类型", example = "maxTransactionsPerMonth") 
            @PathVariable String limitType) {
        return R.ok(usageStatisticsService.getRemainingUsage(groupId, limitType));
    }

    @Operation(summary = "获取使用率", description = "获取群组的使用率百分比")
    @GetMapping("/group/{groupId}/limit/{limitType}/rate")
    public R<Double> getUsageRate(
            @Parameter(description = "群组ID", example = "1") 
            @PathVariable Long groupId,
            @Parameter(description = "限制类型", example = "maxTransactionsPerMonth") 
            @PathVariable String limitType) {
        return R.ok(usageStatisticsService.getUsageRate(groupId, limitType));
    }

    @Operation(summary = "查询超限群组", description = "查询超出指定限制的群组列表")
    @GetMapping("/over-limit/{limitType}")
    public R<List<Long>> getOverLimitGroups(
            @Parameter(description = "限制类型", example = "maxTransactionsPerMonth") 
            @PathVariable String limitType) {
        return R.ok(usageStatisticsService.getOverLimitGroups(limitType));
    }

    @Operation(summary = "重置月度统计", description = "重置月度统计数据（系统调用）")
    @PostMapping("/reset-monthly")
    public R<Void> resetMonthlyStatistics() {
        usageStatisticsService.resetMonthlyStatistics();
        return R.ok();
    }

    @Operation(summary = "清理过期统计", description = "清理过期的统计数据（系统调用）")
    @DeleteMapping("/cleanup")
    public R<Void> cleanupExpiredStatistics(
            @Parameter(description = "保留天数", example = "90") 
            @RequestParam @NotNull @Positive Integer retentionDays) {
        usageStatisticsService.cleanupExpiredStatistics(retentionDays);
        return R.ok();
    }

    @Operation(summary = "生成使用报告", description = "生成群组的使用报告")
    @GetMapping("/group/{groupId}/report")
    public R<UsageStatisticsService.UsageReportResp> generateUsageReport(
            @Parameter(description = "群组ID", example = "1") 
            @PathVariable Long groupId,
            @Parameter(description = "开始日期", example = "2025-01-01") 
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期", example = "2025-01-31") 
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        return R.ok(usageStatisticsService.generateUsageReport(groupId, startDate, endDate));
    }
}
