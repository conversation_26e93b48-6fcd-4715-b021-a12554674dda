package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.extension.crud.model.entity.BaseDO;

import java.time.LocalDateTime;

/**
 * 数据同步计划实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("acc_data_sync_schedule")
public class DataSyncScheduleDO extends BaseDO {

    /**
     * 配置ID
     */
    @TableField("config_id")
    private Long configId;

    /**
     * 计划名称
     */
    @TableField("schedule_name")
    private String scheduleName;

    /**
     * 计划类型
     */
    @TableField("schedule_type")
    private String scheduleType;

    /**
     * Cron表达式
     */
    @TableField("cron_expression")
    private String cronExpression;

    /**
     * 间隔秒数
     */
    @TableField("interval_seconds")
    private Integer intervalSeconds;

    /**
     * 计划执行时间(一次性)
     */
    @TableField("scheduled_time")
    private LocalDateTime scheduledTime;

    /**
     * 时区
     */
    @TableField("timezone")
    private String timezone;

    /**
     * 是否启用
     */
    @TableField("enabled")
    private Boolean enabled;

    /**
     * 最大重试次数
     */
    @TableField("max_retry_count")
    private Integer maxRetryCount;

    /**
     * 重试间隔秒数
     */
    @TableField("retry_interval_seconds")
    private Integer retryIntervalSeconds;

    /**
     * 超时时间(秒)
     */
    @TableField("timeout_seconds")
    private Integer timeoutSeconds;

    /**
     * 最后执行时间
     */
    @TableField("last_execution_time")
    private LocalDateTime lastExecutionTime;

    /**
     * 下次执行时间
     */
    @TableField("next_execution_time")
    private LocalDateTime nextExecutionTime;

    /**
     * 执行次数
     */
    @TableField("execution_count")
    private Integer executionCount;

    /**
     * 成功次数
     */
    @TableField("success_count")
    private Integer successCount;

    /**
     * 失败次数
     */
    @TableField("failure_count")
    private Integer failureCount;

    /**
     * 最后执行状态
     */
    @TableField("last_execution_status")
    private String lastExecutionStatus;

    /**
     * 最后错误信息
     */
    @TableField("last_error_message")
    private String lastErrorMessage;

}
