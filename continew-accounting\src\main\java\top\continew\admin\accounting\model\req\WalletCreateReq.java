package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 钱包创建请求参数
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "钱包创建请求参数")
public class WalletCreateReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    @NotNull(message = "群组ID不能为空")
    private Long groupId;

    /**
     * 币种
     */
    @Schema(description = "币种", example = "CNY")
    @NotBlank(message = "币种不能为空")
    @Pattern(regexp = "^[A-Z]{3}$", message = "币种格式不正确")
    private String currency;

    /**
     * 初始余额
     */
    @Schema(description = "初始余额", example = "0.00")
    @DecimalMin(value = "0.00", message = "初始余额不能为负数")
    @Digits(integer = 15, fraction = 2, message = "余额格式不正确")
    private BigDecimal balance = BigDecimal.ZERO;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "初始化钱包")
    @Size(max = 200, message = "备注长度不能超过200个字符")
    private String remark;
}
