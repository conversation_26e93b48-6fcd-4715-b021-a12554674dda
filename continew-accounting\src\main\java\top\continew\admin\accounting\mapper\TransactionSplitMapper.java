package top.continew.admin.accounting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import top.continew.admin.accounting.model.entity.TransactionSplitDO;
import top.continew.admin.accounting.model.resp.TransactionDetailResp;

import java.util.List;

/**
 * 账单分摊 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface TransactionSplitMapper extends BaseMapper<TransactionSplitDO> {

    /**
     * 获取账单的分摊参与者列表
     *
     * @param transactionId 账单ID
     * @return 分摊参与者列表
     */
    @Select("""
        SELECT ts.*, u.nickname as user_name
        FROM acc_transaction_split ts
        LEFT JOIN sys_user u ON ts.user_id = u.id
        WHERE ts.transaction_id = #{transactionId} AND ts.status = 1
        ORDER BY ts.amount DESC
        """)
    List<TransactionDetailResp.SplitParticipantResp> selectByTransactionId(@Param("transactionId") Long transactionId);

    /**
     * 删除账单的所有分摊记录
     *
     * @param transactionId 账单ID
     * @return 删除数量
     */
    int deleteByTransactionId(@Param("transactionId") Long transactionId);
}
