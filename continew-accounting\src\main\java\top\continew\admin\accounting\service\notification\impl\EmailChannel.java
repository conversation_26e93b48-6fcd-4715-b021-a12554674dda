package top.continew.admin.accounting.service.notification.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.continew.admin.accounting.enums.NotificationChannelEnum;
import top.continew.admin.accounting.model.entity.NotificationDO;
import top.continew.admin.accounting.service.notification.AbstractNotificationChannel;
import top.continew.admin.system.model.entity.UserDO;
import top.continew.starter.messaging.mail.util.MailUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 邮件通知渠道
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EmailChannel extends AbstractNotificationChannel {

    @Override
    public NotificationChannelEnum getChannelType() {
        return NotificationChannelEnum.EMAIL;
    }

    @Override
    public boolean supports(String notificationType) {
        // 邮件支持大部分通知类型，除了实时性要求很高的
        return !List.of("WEBSOCKET").contains(notificationType);
    }

    @Override
    protected Map<String, Object> doSendNotification(NotificationDO notification, List<Long> targetUsers) {
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failedCount = 0;
        StringBuilder errorMessages = new StringBuilder();
        
        try {
            for (Long userId : targetUsers) {
                try {
                    // 获取用户邮箱
                    String email = getUserContact(userId);
                    if (StrUtil.isBlank(email)) {
                        log.warn("用户邮箱为空，跳过发送: userId={}", userId);
                        failedCount++;
                        continue;
                    }
                    
                    // 格式化邮件内容
                    Map<String, String> formatted = formatMessage(notification, userId);
                    String subject = formatted.get("title");
                    String content = formatted.get("content");
                    String htmlContent = formatted.get("htmlContent");
                    
                    // 发送邮件
                    if (StrUtil.isNotBlank(htmlContent)) {
                        MailUtils.sendHtml(email, subject, htmlContent);
                    } else {
                        MailUtils.sendText(email, subject, content);
                    }
                    
                    successCount++;
                    log.debug("邮件发送成功: userId={}, email={}", userId, email);
                    
                } catch (Exception e) {
                    failedCount++;
                    String errorMsg = String.format("用户%d发送失败: %s", userId, e.getMessage());
                    errorMessages.append(errorMsg).append("; ");
                    log.error("邮件发送失败: userId={}", userId, e);
                }
            }
            
            result.put("success", failedCount == 0);
            result.put("totalCount", targetUsers.size());
            result.put("successCount", successCount);
            result.put("failedCount", failedCount);
            result.put("channel", getChannelType().getCode());
            
            if (failedCount > 0) {
                result.put("errorMessage", errorMessages.toString());
            }
            
            log.info("邮件批量发送完成: notificationId={}, total={}, success={}, failed={}", 
                    notification.getId(), targetUsers.size(), successCount, failedCount);
            
        } catch (Exception e) {
            log.error("邮件发送异常: notificationId={}", notification.getId(), e);
            result.put("success", false);
            result.put("errorMessage", e.getMessage());
        }
        
        return result;
    }

    @Override
    protected Map<String, String> doFormatMessage(NotificationDO notification, Long targetUser) {
        Map<String, String> result = new HashMap<>();
        
        // 获取用户信息
        UserDO user = userService.getById(targetUser);
        String userName = user != null ? user.getNickname() : "用户";
        
        // 格式化邮件标题
        String subject = formatEmailSubject(notification, userName);
        
        // 格式化邮件内容
        String textContent = formatEmailContent(notification, userName);
        String htmlContent = formatEmailHtmlContent(notification, userName);
        
        result.put("title", subject);
        result.put("content", textContent);
        result.put("htmlContent", htmlContent);
        
        return result;
    }

    @Override
    public Map<String, Object> testConnection(Map<String, Object> config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 测试邮件配置
            String testEmail = (String) config.get("testEmail");
            if (StrUtil.isBlank(testEmail)) {
                testEmail = "<EMAIL>";
            }
            
            // 发送测试邮件
            MailUtils.sendText(testEmail, "邮件渠道测试", "这是一封测试邮件，用于验证邮件渠道配置是否正确。");
            
            result.put("success", true);
            result.put("message", "邮件渠道测试成功");
            result.put("testEmail", testEmail);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("errorMessage", e.getMessage());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getChannelStatus() {
        Map<String, Object> status = new HashMap<>();
        
        status.put("channel", getChannelType().getCode());
        status.put("channelName", getChannelType().getName());
        status.put("enabled", isEnabled());
        status.put("description", "邮件通知渠道");
        
        // 检查邮件配置状态
        try {
            // TODO: 检查邮件配置是否正确
            status.put("configStatus", "OK");
        } catch (Exception e) {
            status.put("configStatus", "ERROR");
            status.put("configError", e.getMessage());
        }
        
        return status;
    }

    @Override
    public Map<String, Object> validateConfig(Map<String, Object> config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 验证邮件配置
            String host = (String) config.get("host");
            Integer port = (Integer) config.get("port");
            String username = (String) config.get("username");
            String password = (String) config.get("password");
            
            if (StrUtil.isBlank(host)) {
                result.put("valid", false);
                result.put("message", "邮件服务器地址不能为空");
                return result;
            }
            
            if (port == null || port <= 0) {
                result.put("valid", false);
                result.put("message", "邮件服务器端口无效");
                return result;
            }
            
            if (StrUtil.isBlank(username)) {
                result.put("valid", false);
                result.put("message", "邮件用户名不能为空");
                return result;
            }
            
            if (StrUtil.isBlank(password)) {
                result.put("valid", false);
                result.put("message", "邮件密码不能为空");
                return result;
            }
            
            result.put("valid", true);
            result.put("message", "邮件配置验证通过");
            
        } catch (Exception e) {
            result.put("valid", false);
            result.put("message", "邮件配置验证失败: " + e.getMessage());
        }
        
        return result;
    }

    @Override
    public String getUserContact(Long userId) {
        UserDO user = userService.getById(userId);
        return user != null ? user.getEmail() : null;
    }

    @Override
    public boolean isEnabled() {
        // TODO: 从配置中读取邮件渠道是否启用
        return true;
    }

    @Override
    public Map<String, Object> getSendLimits() {
        Map<String, Object> limits = new HashMap<>();
        
        // 邮件发送限制
        limits.put("maxBatchSize", 100);
        limits.put("maxDailyCount", 1000);
        limits.put("rateLimit", 10); // 每分钟10封
        limits.put("maxAttachmentSize", 10 * 1024 * 1024); // 10MB
        
        return limits;
    }

    @Override
    protected int getBatchSize() {
        return 20; // 邮件批次大小
    }

    @Override
    protected long getBatchDelay() {
        return 2000; // 邮件批次延迟2秒
    }

    // ==================== 私有方法 ====================

    /**
     * 格式化邮件标题
     */
    private String formatEmailSubject(NotificationDO notification, String userName) {
        String subject = notification.getTitle();
        
        // 添加系统名称前缀
        return String.format("[ContiNew记账] %s", subject);
    }

    /**
     * 格式化邮件文本内容
     */
    private String formatEmailContent(NotificationDO notification, String userName) {
        StringBuilder content = new StringBuilder();
        
        content.append("亲爱的 ").append(userName).append("：\n\n");
        content.append(notification.getContent()).append("\n\n");
        content.append("---\n");
        content.append("此邮件由ContiNew记账系统自动发送，请勿回复。\n");
        content.append("如有疑问，请联系系统管理员。");
        
        return content.toString();
    }

    /**
     * 格式化邮件HTML内容
     */
    private String formatEmailHtmlContent(NotificationDO notification, String userName) {
        StringBuilder html = new StringBuilder();
        
        html.append("<!DOCTYPE html>");
        html.append("<html><head><meta charset='UTF-8'></head><body>");
        html.append("<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>");
        html.append("<h2 style='color: #333;'>").append(notification.getTitle()).append("</h2>");
        html.append("<p>亲爱的 <strong>").append(userName).append("</strong>：</p>");
        html.append("<div style='background: #f9f9f9; padding: 15px; border-left: 4px solid #007bff;'>");
        html.append(notification.getContent().replace("\n", "<br>"));
        html.append("</div>");
        html.append("<hr style='margin: 20px 0; border: none; border-top: 1px solid #eee;'>");
        html.append("<p style='color: #666; font-size: 12px;'>");
        html.append("此邮件由ContiNew记账系统自动发送，请勿回复。<br>");
        html.append("如有疑问，请联系系统管理员。");
        html.append("</p>");
        html.append("</div></body></html>");
        
        return html.toString();
    }

}
