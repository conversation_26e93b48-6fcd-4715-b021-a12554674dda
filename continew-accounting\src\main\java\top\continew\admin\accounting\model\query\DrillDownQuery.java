package top.continew.admin.accounting.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 数据钻取查询条件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "数据钻取查询条件")
public class DrillDownQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    @NotNull(message = "群组ID不能为空")
    private Long groupId;

    /**
     * 钻取路径
     */
    @Schema(description = "钻取路径", example = "[\"category\", \"subcategory\", \"transaction\"]")
    private List<String> drillPath;

    /**
     * 当前钻取级别
     */
    @Schema(description = "当前钻取级别", example = "1")
    private Integer currentLevel;

    /**
     * 钻取方向
     */
    @Schema(description = "钻取方向", example = "DOWN", allowableValues = {"DOWN", "UP", "ACROSS"})
    @NotBlank(message = "钻取方向不能为空")
    private String drillDirection;

    /**
     * 钻取维度
     */
    @Schema(description = "钻取维度", example = "category")
    @NotBlank(message = "钻取维度不能为空")
    private String drillDimension;

    /**
     * 钻取值
     */
    @Schema(description = "钻取值", example = "餐饮")
    private String drillValue;

    /**
     * 父级过滤条件
     */
    @Schema(description = "父级过滤条件")
    private Map<String, Object> parentFilters;

    /**
     * 当前过滤条件
     */
    @Schema(description = "当前过滤条件")
    private Map<String, Object> currentFilters;

    /**
     * 开始日期
     */
    @Schema(description = "开始日期", example = "2024-01-01")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @Schema(description = "结束日期", example = "2024-12-31")
    private LocalDate endDate;

    /**
     * 度量字段
     */
    @Schema(description = "度量字段", example = "amount")
    private String measureField;

    /**
     * 聚合函数
     */
    @Schema(description = "聚合函数", example = "SUM", allowableValues = {"SUM", "COUNT", "AVG", "MAX", "MIN"})
    private String aggregationFunction;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段", example = "amount")
    private String orderBy;

    /**
     * 排序方向
     */
    @Schema(description = "排序方向", example = "DESC", allowableValues = {"ASC", "DESC"})
    private String orderDirection;

    /**
     * 限制数量
     */
    @Schema(description = "限制数量", example = "50")
    private Integer limit;

    /**
     * 是否显示层次结构
     */
    @Schema(description = "是否显示层次结构", example = "true")
    private Boolean showHierarchy;

    /**
     * 是否包含汇总
     */
    @Schema(description = "是否包含汇总", example = "true")
    private Boolean includeSummary;

    /**
     * 钻取配置
     */
    @Schema(description = "钻取配置")
    private Map<String, Object> drillConfig;

    /**
     * 上下文信息
     */
    @Schema(description = "上下文信息")
    private Map<String, Object> context;
}
