package top.continew.admin.api.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.data.mybatis.base.BaseEntity;

import java.time.LocalDateTime;

/**
 * API密钥实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("acc_api_key")
public class ApiKey extends BaseEntity {

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 应用名称
     */
    @TableField("app_name")
    private String appName;

    /**
     * API密钥
     */
    @TableField("api_key")
    private String apiKey;

    /**
     * API密钥（加密后）
     */
    @TableField("api_secret")
    private String apiSecret;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 权限范围（JSON格式）
     */
    @TableField("scopes")
    private String scopes;

    /**
     * IP白名单（JSON格式）
     */
    @TableField("ip_whitelist")
    private String ipWhitelist;

    /**
     * 速率限制（每分钟请求数）
     */
    @TableField("rate_limit")
    private Integer rateLimit;

    /**
     * 是否激活
     */
    @TableField("is_active")
    private Boolean isActive;

    /**
     * 过期时间
     */
    @TableField("expires_at")
    private LocalDateTime expiresAt;

    /**
     * 最后使用时间
     */
    @TableField("last_used_at")
    private LocalDateTime lastUsedAt;

    /**
     * 使用次数
     */
    @TableField("usage_count")
    private Long usageCount;
}
