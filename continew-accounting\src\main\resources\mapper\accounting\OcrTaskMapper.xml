<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.accounting.mapper.OcrTaskMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="top.continew.admin.accounting.model.entity.OcrTask">
        <id column="task_id" property="taskId" />
        <result column="group_id" property="groupId" />
        <result column="task_number" property="taskNumber" />
        <result column="status" property="status" />
        <result column="ocr_engine" property="ocrEngine" />
        <result column="recognition_mode" property="recognitionMode" />
        <result column="language" property="language" />
        <result column="accuracy" property="accuracy" />
        <result column="original_file_name" property="originalFileName" />
        <result column="file_size" property="fileSize" />
        <result column="image_width" property="imageWidth" />
        <result column="image_height" property="imageHeight" />
        <result column="image_format" property="imageFormat" />
        <result column="storage_path" property="storagePath" />
        <result column="access_url" property="accessUrl" />
        <result column="thumbnail_url" property="thumbnailUrl" />
        <result column="recognition_start_time" property="recognitionStartTime" />
        <result column="recognition_end_time" property="recognitionEndTime" />
        <result column="processing_time" property="processingTime" />
        <result column="confidence" property="confidence" />
        <result column="merchant_name" property="merchantName" />
        <result column="merchant_address" property="merchantAddress" />
        <result column="merchant_phone" property="merchantPhone" />
        <result column="transaction_time" property="transactionTime" />
        <result column="total_amount" property="totalAmount" />
        <result column="tax_amount" property="taxAmount" />
        <result column="tip_amount" property="tipAmount" />
        <result column="discount_amount" property="discountAmount" />
        <result column="currency" property="currency" />
        <result column="payment_method" property="paymentMethod" />
        <result column="card_last_four" property="cardLastFour" />
        <result column="receipt_number" property="receiptNumber" />
        <result column="order_number" property="orderNumber" />
        <result column="recognized_category" property="recognizedCategory" />
        <result column="recognized_tags_json" property="recognizedTagsJson" />
        <result column="items_json" property="itemsJson" />
        <result column="confidence_details_json" property="confidenceDetailsJson" />
        <result column="raw_result_json" property="rawResultJson" />
        <result column="auto_create_transaction" property="autoCreateTransaction" />
        <result column="transaction_created" property="transactionCreated" />
        <result column="transaction_id" property="transactionId" />
        <result column="default_category_id" property="defaultCategoryId" />
        <result column="default_tags_json" property="defaultTagsJson" />
        <result column="default_remark" property="defaultRemark" />
        <result column="enable_smart_category" property="enableSmartCategory" />
        <result column="enable_smart_tags" property="enableSmartTags" />
        <result column="confidence_threshold" property="confidenceThreshold" />
        <result column="save_original_image" property="saveOriginalImage" />
        <result column="image_quality" property="imageQuality" />
        <result column="max_image_size" property="maxImageSize" />
        <result column="enable_table_recognition" property="enableTableRecognition" />
        <result column="enable_handwriting_recognition" property="enableHandwritingRecognition" />
        <result column="timeout_seconds" property="timeoutSeconds" />
        <result column="retry_count" property="retryCount" />
        <result column="actual_retry_count" property="actualRetryCount" />
        <result column="callback_url" property="callbackUrl" />
        <result column="async_processing" property="asyncProcessing" />
        <result column="error_message" property="errorMessage" />
        <result column="error_code" property="errorCode" />
        <result column="error_details_json" property="errorDetailsJson" />
        <result column="extra_params_json" property="extraParamsJson" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="created_by" property="createdBy" />
        <result column="update_time" property="updateTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 详情结果映射（包含用户姓名） -->
    <resultMap id="DetailResultMap" type="top.continew.admin.accounting.model.entity.OcrTask" extends="BaseResultMap">
        <result column="created_by_name" property="createdByName" />
        <result column="updated_by_name" property="updatedByName" />
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        task_id, group_id, task_number, status, ocr_engine, recognition_mode, language, accuracy,
        original_file_name, file_size, image_width, image_height, image_format, storage_path,
        access_url, thumbnail_url, recognition_start_time, recognition_end_time, processing_time,
        confidence, merchant_name, merchant_address, merchant_phone, transaction_time, total_amount,
        tax_amount, tip_amount, discount_amount, currency, payment_method, card_last_four,
        receipt_number, order_number, recognized_category, recognized_tags_json, items_json,
        confidence_details_json, raw_result_json, auto_create_transaction, transaction_created,
        transaction_id, default_category_id, default_tags_json, default_remark, enable_smart_category,
        enable_smart_tags, confidence_threshold, save_original_image, image_quality, max_image_size,
        enable_table_recognition, enable_handwriting_recognition, timeout_seconds, retry_count,
        actual_retry_count, callback_url, async_processing, error_message, error_code,
        error_details_json, extra_params_json, remark, create_time, created_by, update_time,
        updated_by, deleted
    </sql>

    <!-- 根据任务编号查询任务详情 -->
    <select id="selectDetailByTaskNumber" resultMap="DetailResultMap">
        SELECT
            t.*,
            cu.nickname AS created_by_name,
            uu.nickname AS updated_by_name
        FROM acc_ocr_task t
        LEFT JOIN sys_user cu ON t.created_by = cu.id
        LEFT JOIN sys_user uu ON t.updated_by = uu.id
        WHERE t.task_number = #{taskNumber}
          AND t.deleted = 0
    </select>

    <!-- 分页查询任务列表（包含创建人姓名） -->
    <select id="selectPageWithUserName" resultMap="DetailResultMap">
        SELECT
            t.*,
            cu.nickname AS created_by_name
        FROM acc_ocr_task t
        LEFT JOIN sys_user cu ON t.created_by = cu.id
        WHERE t.deleted = 0
        <if test="groupId != null">
            AND t.group_id = #{groupId}
        </if>
        <if test="status != null and status != ''">
            AND t.status = #{status}
        </if>
        <if test="ocrEngine != null and ocrEngine != ''">
            AND t.ocr_engine = #{ocrEngine}
        </if>
        <if test="startTime != null">
            AND t.create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND t.create_time &lt;= #{endTime}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (t.merchant_name LIKE CONCAT('%', #{keyword}, '%')
                OR t.recognized_category LIKE CONCAT('%', #{keyword}, '%')
                OR t.original_file_name LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        ORDER BY t.create_time DESC
    </select>

    <!-- 查询群组的任务列表 -->
    <select id="selectByGroupId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM acc_ocr_task
        WHERE group_id = #{groupId}
          AND deleted = 0
        ORDER BY create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询用户的任务列表 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM acc_ocr_task
        WHERE created_by = #{userId}
          AND deleted = 0
        ORDER BY create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询处理中的任务 -->
    <select id="selectProcessingTasks" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM acc_ocr_task
        WHERE status = 'PROCESSING'
          AND deleted = 0
          AND recognition_start_time &lt; DATE_SUB(NOW(), INTERVAL #{timeoutMinutes} MINUTE)
        ORDER BY recognition_start_time ASC
    </select>

    <!-- 查询失败的任务 -->
    <select id="selectFailedTasks" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM acc_ocr_task
        WHERE status = 'FAILED'
          AND deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        <if test="days != null">
            AND create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 统计任务数量 -->
    <select id="countTasks" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM acc_ocr_task
        WHERE deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
    </select>

    <!-- 统计识别成功率 -->
    <select id="selectSuccessRateStats" resultType="java.util.Map">
        SELECT
            COUNT(*) AS total_tasks,
            SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) AS success_tasks,
            SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) AS failed_tasks,
            ROUND(SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) AS success_rate,
            ROUND(AVG(CASE WHEN status = 'SUCCESS' THEN processing_time END), 0) AS avg_processing_time,
            ROUND(AVG(CASE WHEN status = 'SUCCESS' THEN confidence END), 4) AS avg_confidence
        FROM acc_ocr_task
        WHERE deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        <if test="days != null">
            AND create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        </if>
    </select>

    <!-- 统计引擎性能 -->
    <select id="selectEnginePerformanceStats" resultType="java.util.Map">
        SELECT
            ocr_engine,
            COUNT(*) AS total_tasks,
            SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) AS success_tasks,
            ROUND(SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) AS success_rate,
            ROUND(AVG(CASE WHEN status = 'SUCCESS' THEN processing_time END), 0) AS avg_processing_time,
            ROUND(AVG(CASE WHEN status = 'SUCCESS' THEN confidence END), 4) AS avg_confidence,
            MIN(CASE WHEN status = 'SUCCESS' THEN processing_time END) AS min_processing_time,
            MAX(CASE WHEN status = 'SUCCESS' THEN processing_time END) AS max_processing_time
        FROM acc_ocr_task
        WHERE deleted = 0
          AND status IN ('SUCCESS', 'FAILED')
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        <if test="days != null">
            AND create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        </if>
        GROUP BY ocr_engine
        ORDER BY success_rate DESC, avg_processing_time ASC
    </select>

    <!-- 统计处理时间分布 -->
    <select id="selectProcessingTimeDistribution" resultType="java.util.Map">
        SELECT
            CASE
                WHEN processing_time &lt; 1000 THEN '&lt;1s'
                WHEN processing_time &lt; 3000 THEN '1-3s'
                WHEN processing_time &lt; 5000 THEN '3-5s'
                WHEN processing_time &lt; 10000 THEN '5-10s'
                ELSE '&gt;10s'
            END AS time_range,
            COUNT(*) AS task_count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM acc_ocr_task WHERE deleted = 0 AND status = 'SUCCESS'
                <if test="groupId != null">AND group_id = #{groupId}</if>
                <if test="days != null">AND create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)</if>
            ), 2) AS percentage
        FROM acc_ocr_task
        WHERE deleted = 0
          AND status = 'SUCCESS'
          AND processing_time IS NOT NULL
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        <if test="days != null">
            AND create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        </if>
        GROUP BY
            CASE
                WHEN processing_time &lt; 1000 THEN '&lt;1s'
                WHEN processing_time &lt; 3000 THEN '1-3s'
                WHEN processing_time &lt; 5000 THEN '3-5s'
                WHEN processing_time &lt; 10000 THEN '5-10s'
                ELSE '&gt;10s'
            END
        ORDER BY MIN(processing_time)
    </select>

    <!-- 统计置信度分布 -->
    <select id="selectConfidenceDistribution" resultType="java.util.Map">
        SELECT
            CASE
                WHEN confidence &lt; 0.5 THEN '&lt;50%'
                WHEN confidence &lt; 0.7 THEN '50-70%'
                WHEN confidence &lt; 0.8 THEN '70-80%'
                WHEN confidence &lt; 0.9 THEN '80-90%'
                WHEN confidence &lt; 0.95 THEN '90-95%'
                ELSE '&gt;=95%'
            END AS confidence_range,
            COUNT(*) AS task_count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM acc_ocr_task WHERE deleted = 0 AND status = 'SUCCESS'
                <if test="groupId != null">AND group_id = #{groupId}</if>
                <if test="days != null">AND create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)</if>
            ), 2) AS percentage
        FROM acc_ocr_task
        WHERE deleted = 0
          AND status = 'SUCCESS'
          AND confidence IS NOT NULL
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        <if test="days != null">
            AND create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        </if>
        GROUP BY
            CASE
                WHEN confidence &lt; 0.5 THEN '&lt;50%'
                WHEN confidence &lt; 0.7 THEN '50-70%'
                WHEN confidence &lt; 0.8 THEN '70-80%'
                WHEN confidence &lt; 0.9 THEN '80-90%'
                WHEN confidence &lt; 0.95 THEN '90-95%'
                ELSE '&gt;=95%'
            END
        ORDER BY MIN(confidence)
    </select>

    <!-- 统计商家识别排行 -->
    <select id="selectMerchantRecognitionRanking" resultType="java.util.Map">
        SELECT
            merchant_name,
            COUNT(*) AS recognition_count,
            ROUND(AVG(confidence), 4) AS avg_confidence,
            SUM(total_amount) AS total_amount,
            COUNT(DISTINCT DATE(create_time)) AS active_days
        FROM acc_ocr_task
        WHERE deleted = 0
          AND status = 'SUCCESS'
          AND merchant_name IS NOT NULL
          AND merchant_name != ''
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        <if test="days != null">
            AND create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        </if>
        GROUP BY merchant_name
        ORDER BY recognition_count DESC, avg_confidence DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 统计分类识别情况 -->
    <select id="selectCategoryRecognitionStats" resultType="java.util.Map">
        SELECT
            COALESCE(recognized_category, '未分类') AS category,
            COUNT(*) AS recognition_count,
            ROUND(AVG(confidence), 4) AS avg_confidence,
            SUM(total_amount) AS total_amount,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM acc_ocr_task WHERE deleted = 0 AND status = 'SUCCESS'
                <if test="groupId != null">AND group_id = #{groupId}</if>
                <if test="days != null">AND create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)</if>
            ), 2) AS percentage
        FROM acc_ocr_task
        WHERE deleted = 0
          AND status = 'SUCCESS'
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        <if test="days != null">
            AND create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        </if>
        GROUP BY recognized_category
        ORDER BY recognition_count DESC
    </select>

    <!-- 统计识别趋势 -->
    <select id="selectRecognitionTrend" resultType="java.util.Map">
        SELECT
            DATE(create_time) AS date,
            COUNT(*) AS total_tasks,
            SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) AS success_tasks,
            SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) AS failed_tasks,
            ROUND(SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) AS success_rate,
            ROUND(AVG(CASE WHEN status = 'SUCCESS' THEN processing_time END), 0) AS avg_processing_time,
            ROUND(AVG(CASE WHEN status = 'SUCCESS' THEN confidence END), 4) AS avg_confidence
        FROM acc_ocr_task
        WHERE deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        <if test="days != null">
            AND create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        </if>
        GROUP BY DATE(create_time)
        ORDER BY date DESC
    </select>

    <!-- 统计错误类型分布 -->
    <select id="selectErrorTypeDistribution" resultType="java.util.Map">
        SELECT
            COALESCE(error_code, 'UNKNOWN') AS error_code,
            COUNT(*) AS error_count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM acc_ocr_task WHERE deleted = 0 AND status = 'FAILED'
                <if test="groupId != null">AND group_id = #{groupId}</if>
                <if test="days != null">AND create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)</if>
            ), 2) AS percentage,
            GROUP_CONCAT(DISTINCT LEFT(error_message, 100) SEPARATOR '; ') AS sample_messages
        FROM acc_ocr_task
        WHERE deleted = 0
          AND status = 'FAILED'
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        <if test="days != null">
            AND create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        </if>
        GROUP BY error_code
        ORDER BY error_count DESC
    </select>

    <!-- 统计每日识别量 -->
    <select id="selectDailyRecognitionCount" resultType="java.util.Map">
        SELECT
            DATE(create_time) AS date,
            COUNT(*) AS total_count,
            SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) AS success_count,
            SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) AS failed_count,
            COUNT(DISTINCT created_by) AS active_users,
            COUNT(DISTINCT ocr_engine) AS used_engines
        FROM acc_ocr_task
        WHERE deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        <if test="days != null">
            AND create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        </if>
        GROUP BY DATE(create_time)
        ORDER BY date DESC
    </select>

    <!-- 统计用户识别活跃度 -->
    <select id="selectUserRecognitionActivity" resultType="java.util.Map">
        SELECT
            t.created_by AS user_id,
            u.nickname AS user_name,
            COUNT(*) AS total_tasks,
            SUM(CASE WHEN t.status = 'SUCCESS' THEN 1 ELSE 0 END) AS success_tasks,
            ROUND(SUM(CASE WHEN t.status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) AS success_rate,
            ROUND(AVG(CASE WHEN t.status = 'SUCCESS' THEN t.processing_time END), 0) AS avg_processing_time,
            ROUND(AVG(CASE WHEN t.status = 'SUCCESS' THEN t.confidence END), 4) AS avg_confidence,
            COUNT(DISTINCT DATE(t.create_time)) AS active_days,
            MAX(t.create_time) AS last_activity_time
        FROM acc_ocr_task t
        LEFT JOIN sys_user u ON t.created_by = u.id
        WHERE t.deleted = 0
        <if test="groupId != null">
            AND t.group_id = #{groupId}
        </if>
        <if test="days != null">
            AND t.create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        </if>
        GROUP BY t.created_by, u.nickname
        ORDER BY total_tasks DESC, success_rate DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 批量更新任务状态 -->
    <update id="batchUpdateStatus">
        UPDATE acc_ocr_task
        SET status = #{status},
            updated_by = #{updatedBy},
            update_time = NOW()
        WHERE task_number IN
        <foreach collection="taskNumbers" item="taskNumber" open="(" separator="," close=")">
            #{taskNumber}
        </foreach>
        AND deleted = 0
    </update>

    <!-- 更新任务处理结果 -->
    <update id="updateProcessingResult">
        UPDATE acc_ocr_task
        SET status = #{status},
            confidence = #{confidence},
            processing_time = #{processingTime},
            recognition_end_time = NOW(),
            error_message = #{errorMessage},
            error_code = #{errorCode},
            updated_by = #{updatedBy},
            update_time = NOW()
        WHERE task_number = #{taskNumber}
          AND deleted = 0
    </update>

    <!-- 更新任务识别结果 -->
    <update id="updateRecognitionResult">
        UPDATE acc_ocr_task
        SET merchant_name = #{merchantName},
            total_amount = #{totalAmount},
            recognized_category = #{recognizedCategory},
            recognized_tags_json = #{recognizedTagsJson},
            updated_by = #{updatedBy},
            update_time = NOW()
        WHERE task_number = #{taskNumber}
          AND deleted = 0
    </update>

    <!-- 更新账单创建状态 -->
    <update id="updateTransactionStatus">
        UPDATE acc_ocr_task
        SET transaction_created = #{transactionCreated},
            transaction_id = #{transactionId},
            updated_by = #{updatedBy},
            update_time = NOW()
        WHERE task_number = #{taskNumber}
          AND deleted = 0
    </update>

    <!-- 清理过期的处理中任务 -->
    <update id="cleanupTimeoutTasks">
        UPDATE acc_ocr_task
        SET status = 'TIMEOUT',
            error_message = '任务处理超时',
            error_code = 'TIMEOUT',
            recognition_end_time = NOW(),
            updated_by = #{updatedBy},
            update_time = NOW()
        WHERE status = 'PROCESSING'
          AND deleted = 0
          AND recognition_start_time &lt; DATE_SUB(NOW(), INTERVAL #{timeoutMinutes} MINUTE)
    </update>

    <!-- 清理历史数据 -->
    <update id="cleanupHistoryData">
        UPDATE acc_ocr_task
        SET deleted = 1,
            update_time = NOW()
        WHERE deleted = 0
          AND create_time &lt; DATE_SUB(NOW(), INTERVAL #{days} DAY)
          AND status IN ('SUCCESS', 'FAILED', 'TIMEOUT', 'CANCELLED')
    </update>

    <!-- 归档历史数据 -->
    <update id="archiveHistoryData">
        <!-- 这里可以实现将数据移动到归档表的逻辑 -->
        UPDATE acc_ocr_task
        SET remark = CONCAT(COALESCE(remark, ''), '[已归档]'),
            update_time = NOW()
        WHERE deleted = 0
          AND create_time &lt; DATE_SUB(NOW(), INTERVAL #{days} DAY)
          AND status IN ('SUCCESS', 'FAILED', 'TIMEOUT', 'CANCELLED')
          AND remark NOT LIKE '%[已归档]%'
    </update>

    <!-- 检查任务编号是否存在 -->
    <select id="existsByTaskNumber" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM acc_ocr_task
        WHERE task_number = #{taskNumber}
          AND deleted = 0
    </select>

    <!-- 获取下一个任务编号序号 -->
    <select id="getNextTaskSequence" resultType="java.lang.Integer">
        SELECT COALESCE(MAX(CAST(SUBSTRING(task_number, LENGTH(#{prefix}) + 1) AS UNSIGNED)), 0) + 1
        FROM acc_ocr_task
        WHERE task_number LIKE CONCAT(#{prefix}, '%')
          AND deleted = 0
    </select>

    <!-- 查询重复的任务 -->
    <select id="selectDuplicateTasks" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM acc_ocr_task
        WHERE group_id = #{groupId}
          AND deleted = 0
          AND extra_params_json LIKE CONCAT('%"fileHash":"', #{fileHash}, '"%')
        <if test="days != null">
            AND create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 查询可重试的失败任务 -->
    <select id="selectRetryableTasks" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM acc_ocr_task
        WHERE status = 'FAILED'
          AND deleted = 0
          AND COALESCE(actual_retry_count, 0) &lt; #{maxRetryCount}
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        ORDER BY create_time ASC
    </select>

</mapper>
