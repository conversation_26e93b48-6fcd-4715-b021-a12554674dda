package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.data.mybatis.base.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 债务实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("acc_debt")
@Schema(description = "债务")
public class DebtDO extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID")
    private Long groupId;

    /**
     * 债权人ID
     */
    @Schema(description = "债权人ID")
    private Long creditorId;

    /**
     * 债务人ID
     */
    @Schema(description = "债务人ID")
    private Long debtorId;

    /**
     * 金额
     */
    @Schema(description = "金额")
    private BigDecimal amount;

    /**
     * 币种
     */
    @Schema(description = "币种")
    private String currency;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 到期时间
     */
    @Schema(description = "到期时间")
    private LocalDateTime dueDate;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;

    /**
     * 关联交易ID
     */
    @Schema(description = "关联交易ID")
    private Long transactionId;

    /**
     * 债务类型
     */
    @Schema(description = "债务类型")
    private String debtType;

    /**
     * 利率（年化）
     */
    @Schema(description = "利率")
    private BigDecimal interestRate;

    /**
     * 已还金额
     */
    @Schema(description = "已还金额")
    private BigDecimal paidAmount;

    /**
     * 剩余金额
     */
    @Schema(description = "剩余金额")
    private BigDecimal remainingAmount;

    /**
     * 最后还款时间
     */
    @Schema(description = "最后还款时间")
    private LocalDateTime lastPaymentDate;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}
