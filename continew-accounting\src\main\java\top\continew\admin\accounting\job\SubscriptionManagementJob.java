package top.continew.admin.accounting.job;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.accounting.service.SubscriptionService;
import top.continew.admin.accounting.service.UsageStatisticsService;
import top.continew.starter.core.constant.PropertiesConstants;
import top.continew.starter.data.core.annotation.TenantIgnore;

/**
 * 订阅管理定时任务
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SubscriptionManagementJob {

    private final SubscriptionService subscriptionService;
    private final UsageStatisticsService usageStatisticsService;

    /**
     * 处理即将过期的订阅（未启用 Snail Job 则使用它）
     */
    @Component
    @ConditionalOnProperty(prefix = "snail-job", name = PropertiesConstants.ENABLED, havingValue = "false")
    public static class Scheduler {

        private final SubscriptionService subscriptionService;
        private final UsageStatisticsService usageStatisticsService;

        public Scheduler(SubscriptionService subscriptionService, UsageStatisticsService usageStatisticsService) {
            this.subscriptionService = subscriptionService;
            this.usageStatisticsService = usageStatisticsService;
        }

        /**
         * 处理即将过期的订阅 - 每天上午9点执行
         */
        @TenantIgnore
        @Scheduled(cron = "0 0 9 * * ?")
        @Transactional(rollbackFor = Exception.class)
        public void handleExpiringSubscriptions() {
            log.info("定时任务 [处理即将过期的订阅] 开始执行。");
            try {
                subscriptionService.handleExpiringSubscriptions();
                log.info("定时任务 [处理即将过期的订阅] 执行成功。");
            } catch (Exception e) {
                log.error("定时任务 [处理即将过期的订阅] 执行失败: {}", e.getMessage(), e);
            }
        }

        /**
         * 处理已过期的订阅 - 每天凌晨1点执行
         */
        @TenantIgnore
        @Scheduled(cron = "0 0 1 * * ?")
        @Transactional(rollbackFor = Exception.class)
        public void handleExpiredSubscriptions() {
            log.info("定时任务 [处理已过期的订阅] 开始执行。");
            try {
                subscriptionService.handleExpiredSubscriptions();
                log.info("定时任务 [处理已过期的订阅] 执行成功。");
            } catch (Exception e) {
                log.error("定时任务 [处理已过期的订阅] 执行失败: {}", e.getMessage(), e);
            }
        }

        /**
         * 自动续费处理 - 每天凌晨2点执行
         */
        @TenantIgnore
        @Scheduled(cron = "0 0 2 * * ?")
        @Transactional(rollbackFor = Exception.class)
        public void processAutoRenewal() {
            log.info("定时任务 [自动续费处理] 开始执行。");
            try {
                subscriptionService.processAutoRenewal();
                log.info("定时任务 [自动续费处理] 执行成功。");
            } catch (Exception e) {
                log.error("定时任务 [自动续费处理] 执行失败: {}", e.getMessage(), e);
            }
        }

        /**
         * 重置月度使用量统计 - 每月1号凌晨3点执行
         */
        @TenantIgnore
        @Scheduled(cron = "0 0 3 1 * ?")
        @Transactional(rollbackFor = Exception.class)
        public void resetMonthlyUsageStatistics() {
            log.info("定时任务 [重置月度使用量统计] 开始执行。");
            try {
                usageStatisticsService.resetMonthlyStatistics();
                log.info("定时任务 [重置月度使用量统计] 执行成功。");
            } catch (Exception e) {
                log.error("定时任务 [重置月度使用量统计] 执行失败: {}", e.getMessage(), e);
            }
        }

        /**
         * 清理过期使用量统计 - 每周日凌晨4点执行
         */
        @TenantIgnore
        @Scheduled(cron = "0 0 4 * * 0")
        @Transactional(rollbackFor = Exception.class)
        public void cleanupExpiredUsageStatistics() {
            log.info("定时任务 [清理过期使用量统计] 开始执行。");
            try {
                // 保留90天的统计数据
                usageStatisticsService.cleanupExpiredStatistics(90);
                log.info("定时任务 [清理过期使用量统计] 执行成功。");
            } catch (Exception e) {
                log.error("定时任务 [清理过期使用量统计] 执行失败: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 处理即将过期的订阅 - 每天上午9点执行（Snail Job版本）
     */
    public void handleExpiringSubscriptionsWithSnailJob() {
        log.info("Snail Job [处理即将过期的订阅] 开始执行。");
        try {
            subscriptionService.handleExpiringSubscriptions();
            log.info("Snail Job [处理即将过期的订阅] 执行成功。");
        } catch (Exception e) {
            log.error("Snail Job [处理即将过期的订阅] 执行失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理已过期的订阅 - 每天凌晨1点执行（Snail Job版本）
     */
    public void handleExpiredSubscriptionsWithSnailJob() {
        log.info("Snail Job [处理已过期的订阅] 开始执行。");
        try {
            subscriptionService.handleExpiredSubscriptions();
            log.info("Snail Job [处理已过期的订阅] 执行成功。");
        } catch (Exception e) {
            log.error("Snail Job [处理已过期的订阅] 执行失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 自动续费处理 - 每天凌晨2点执行（Snail Job版本）
     */
    public void processAutoRenewalWithSnailJob() {
        log.info("Snail Job [自动续费处理] 开始执行。");
        try {
            subscriptionService.processAutoRenewal();
            log.info("Snail Job [自动续费处理] 执行成功。");
        } catch (Exception e) {
            log.error("Snail Job [自动续费处理] 执行失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 重置月度使用量统计 - 每月1号凌晨3点执行（Snail Job版本）
     */
    public void resetMonthlyUsageStatisticsWithSnailJob() {
        log.info("Snail Job [重置月度使用量统计] 开始执行。");
        try {
            usageStatisticsService.resetMonthlyStatistics();
            log.info("Snail Job [重置月度使用量统计] 执行成功。");
        } catch (Exception e) {
            log.error("Snail Job [重置月度使用量统计] 执行失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 清理过期使用量统计 - 每周日凌晨4点执行（Snail Job版本）
     */
    public void cleanupExpiredUsageStatisticsWithSnailJob() {
        log.info("Snail Job [清理过期使用量统计] 开始执行。");
        try {
            // 保留90天的统计数据
            usageStatisticsService.cleanupExpiredStatistics(90);
            log.info("Snail Job [清理过期使用量统计] 执行成功。");
        } catch (Exception e) {
            log.error("Snail Job [清理过期使用量统计] 执行失败: {}", e.getMessage(), e);
            throw e;
        }
    }
}
