package top.continew.admin.accounting.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import top.continew.admin.accounting.model.query.ReportTemplateQuery;
import top.continew.admin.accounting.model.req.DynamicReportGenerateReq;
import top.continew.admin.accounting.model.req.ReportTemplateCreateReq;
import top.continew.admin.accounting.model.req.ReportTemplateUpdateReq;
import top.continew.admin.accounting.model.resp.DynamicReportResp;
import top.continew.admin.accounting.model.resp.ReportTemplateDetailResp;
import top.continew.admin.accounting.model.resp.ReportTemplateResp;

import java.util.List;
import java.util.Map;

/**
 * 报表引擎服务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface ReportEngineService {

    // ==================== 报表模板管理 ====================

    /**
     * 创建报表模板
     *
     * @param createReq 创建请求
     * @return 模板ID
     */
    Long createTemplate(ReportTemplateCreateReq createReq);

    /**
     * 更新报表模板
     *
     * @param templateId 模板ID
     * @param updateReq  更新请求
     * @return 是否成功
     */
    Boolean updateTemplate(Long templateId, ReportTemplateUpdateReq updateReq);

    /**
     * 删除报表模板
     *
     * @param templateId 模板ID
     * @return 是否成功
     */
    Boolean deleteTemplate(Long templateId);

    /**
     * 批量删除报表模板
     *
     * @param templateIds 模板ID列表
     * @return 是否成功
     */
    Boolean deleteTemplates(List<Long> templateIds);

    /**
     * 获取报表模板详情
     *
     * @param templateId 模板ID
     * @return 模板详情
     */
    ReportTemplateDetailResp getTemplateDetail(Long templateId);

    /**
     * 分页查询报表模板
     *
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<ReportTemplateResp> pageTemplates(ReportTemplateQuery query);

    /**
     * 查询报表模板列表
     *
     * @param query 查询条件
     * @return 模板列表
     */
    List<ReportTemplateResp> listTemplates(ReportTemplateQuery query);

    /**
     * 复制报表模板
     *
     * @param templateId 源模板ID
     * @param newName    新模板名称
     * @return 新模板ID
     */
    Long copyTemplate(Long templateId, String newName);

    /**
     * 启用/禁用报表模板
     *
     * @param templateId 模板ID
     * @param enabled    是否启用
     * @return 是否成功
     */
    Boolean toggleTemplateStatus(Long templateId, Boolean enabled);

    /**
     * 发布报表模板
     *
     * @param templateId 模板ID
     * @return 是否成功
     */
    Boolean publishTemplate(Long templateId);

    /**
     * 撤销发布报表模板
     *
     * @param templateId 模板ID
     * @return 是否成功
     */
    Boolean unpublishTemplate(Long templateId);

    // ==================== 模板版本管理 ====================

    /**
     * 创建模板版本
     *
     * @param templateId  模板ID
     * @param versionNote 版本说明
     * @return 版本号
     */
    String createTemplateVersion(Long templateId, String versionNote);

    /**
     * 回滚到指定版本
     *
     * @param templateId 模板ID
     * @param version    版本号
     * @return 是否成功
     */
    Boolean rollbackToVersion(Long templateId, String version);

    /**
     * 获取模板版本历史
     *
     * @param templateId 模板ID
     * @return 版本历史列表
     */
    List<ReportTemplateDetailResp.VersionHistory> getTemplateVersionHistory(Long templateId);

    /**
     * 比较模板版本
     *
     * @param templateId 模板ID
     * @param version1   版本1
     * @param version2   版本2
     * @return 版本差异
     */
    Map<String, Object> compareTemplateVersions(Long templateId, String version1, String version2);

    // ==================== 动态报表生成 ====================

    /**
     * 生成动态报表
     *
     * @param generateReq 生成请求
     * @return 报表响应
     */
    DynamicReportResp generateReport(DynamicReportGenerateReq generateReq);

    /**
     * 异步生成动态报表
     *
     * @param generateReq 生成请求
     * @return 任务ID
     */
    String generateReportAsync(DynamicReportGenerateReq generateReq);

    /**
     * 获取报表生成状态
     *
     * @param taskId 任务ID
     * @return 报表状态
     */
    DynamicReportResp getReportStatus(String taskId);

    /**
     * 取消报表生成
     *
     * @param taskId 任务ID
     * @return 是否成功
     */
    Boolean cancelReportGeneration(String taskId);

    /**
     * 重新生成报表
     *
     * @param reportId 报表ID
     * @return 报表响应
     */
    DynamicReportResp regenerateReport(Long reportId);

    /**
     * 下载报表文件
     *
     * @param reportId 报表ID
     * @return 文件字节数组
     */
    byte[] downloadReport(Long reportId);

    /**
     * 获取报表下载URL
     *
     * @param reportId 报表ID
     * @return 下载URL
     */
    String getReportDownloadUrl(Long reportId);

    // ==================== 报表调度管理 ====================

    /**
     * 创建报表调度任务
     *
     * @param templateId     模板ID
     * @param scheduleConfig 调度配置
     * @return 调度任务ID
     */
    String createReportSchedule(Long templateId, Map<String, Object> scheduleConfig);

    /**
     * 更新报表调度任务
     *
     * @param scheduleId     调度任务ID
     * @param scheduleConfig 调度配置
     * @return 是否成功
     */
    Boolean updateReportSchedule(String scheduleId, Map<String, Object> scheduleConfig);

    /**
     * 删除报表调度任务
     *
     * @param scheduleId 调度任务ID
     * @return 是否成功
     */
    Boolean deleteReportSchedule(String scheduleId);

    /**
     * 启用/禁用报表调度任务
     *
     * @param scheduleId 调度任务ID
     * @param enabled    是否启用
     * @return 是否成功
     */
    Boolean toggleScheduleStatus(String scheduleId, Boolean enabled);

    /**
     * 立即执行调度任务
     *
     * @param scheduleId 调度任务ID
     * @return 执行结果
     */
    DynamicReportResp executeScheduleNow(String scheduleId);

    /**
     * 获取调度任务列表
     *
     * @param templateId 模板ID（可选）
     * @return 调度任务列表
     */
    List<Map<String, Object>> listReportSchedules(Long templateId);

    // ==================== 报表统计分析 ====================

    /**
     * 获取模板使用统计
     *
     * @param templateId 模板ID
     * @param days       统计天数
     * @return 使用统计
     */
    Map<String, Object> getTemplateUsageStats(Long templateId, Integer days);

    /**
     * 获取模板性能统计
     *
     * @param templateId 模板ID
     * @param days       统计天数
     * @return 性能统计
     */
    Map<String, Object> getTemplatePerformanceStats(Long templateId, Integer days);

    /**
     * 获取报表生成趋势
     *
     * @param groupId 群组ID（可选）
     * @param days    统计天数
     * @return 生成趋势
     */
    Map<String, Object> getReportGenerationTrend(Long groupId, Integer days);

    /**
     * 获取热门模板排行
     *
     * @param groupId 群组ID（可选）
     * @param days    统计天数
     * @param limit   返回数量
     * @return 热门模板列表
     */
    List<Map<String, Object>> getPopularTemplates(Long groupId, Integer days, Integer limit);

    /**
     * 获取用户报表使用分析
     *
     * @param userId 用户ID
     * @param days   统计天数
     * @return 用户使用分析
     */
    Map<String, Object> getUserReportUsageAnalysis(Long userId, Integer days);

    // ==================== 模板权限管理 ====================

    /**
     * 检查模板访问权限
     *
     * @param templateId 模板ID
     * @param userId     用户ID
     * @param permission 权限类型
     * @return 是否有权限
     */
    Boolean checkTemplatePermission(Long templateId, Long userId, String permission);

    /**
     * 授权模板访问权限
     *
     * @param templateId  模板ID
     * @param userId      用户ID
     * @param permissions 权限列表
     * @return 是否成功
     */
    Boolean grantTemplatePermission(Long templateId, Long userId, List<String> permissions);

    /**
     * 撤销模板访问权限
     *
     * @param templateId  模板ID
     * @param userId      用户ID
     * @param permissions 权限列表
     * @return 是否成功
     */
    Boolean revokeTemplatePermission(Long templateId, Long userId, List<String> permissions);

    /**
     * 获取模板权限列表
     *
     * @param templateId 模板ID
     * @return 权限列表
     */
    List<Map<String, Object>> getTemplatePermissions(Long templateId);

    // ==================== 模板收藏管理 ====================

    /**
     * 收藏/取消收藏模板
     *
     * @param templateId 模板ID
     * @param favorite   是否收藏
     * @return 是否成功
     */
    Boolean toggleTemplateFavorite(Long templateId, Boolean favorite);

    /**
     * 获取用户收藏的模板
     *
     * @param userId 用户ID
     * @return 收藏模板列表
     */
    List<ReportTemplateResp> getUserFavoriteTemplates(Long userId);

    /**
     * 模板评分
     *
     * @param templateId 模板ID
     * @param rating     评分（1-5）
     * @param comment    评价内容
     * @return 是否成功
     */
    Boolean rateTemplate(Long templateId, Integer rating, String comment);

    /**
     * 获取模板评价列表
     *
     * @param templateId 模板ID
     * @return 评价列表
     */
    List<ReportTemplateDetailResp.UserRating> getTemplateRatings(Long templateId);

    // ==================== 缓存管理 ====================

    /**
     * 清除模板缓存
     *
     * @param templateId 模板ID
     * @return 是否成功
     */
    Boolean clearTemplateCache(Long templateId);

    /**
     * 清除所有报表缓存
     *
     * @return 是否成功
     */
    Boolean clearAllReportCache();

    /**
     * 预热模板缓存
     *
     * @param templateId 模板ID
     * @return 是否成功
     */
    Boolean warmupTemplateCache(Long templateId);
}
