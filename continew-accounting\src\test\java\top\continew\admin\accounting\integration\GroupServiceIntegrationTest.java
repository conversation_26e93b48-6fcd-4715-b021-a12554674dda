package top.continew.admin.accounting.integration;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import top.continew.admin.accounting.base.BaseIntegrationTest;
import top.continew.admin.accounting.model.req.GroupCreateReq;
import top.continew.admin.accounting.model.req.GroupQueryReq;
import top.continew.admin.accounting.model.resp.GroupResp;
import top.continew.admin.accounting.service.GroupService;
import top.continew.admin.accounting.util.TestDataUtil;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 群组服务集成测试
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
class GroupServiceIntegrationTest extends BaseIntegrationTest {

    @Autowired
    private GroupService groupService;

    @Test
    void testGroupLifecycle() {
        // 1. 创建群组
        GroupCreateReq createReq = TestDataUtil.createTestGroupCreateReq();
        Long groupId = groupService.add(createReq);
        assertNotNull(groupId);

        // 2. 查询群组
        GroupResp group = groupService.get(groupId);
        assertNotNull(group);
        assertEquals(createReq.getName(), group.getName());
        assertEquals(createReq.getDescription(), group.getDescription());
        assertEquals(createReq.getCurrency(), group.getCurrency());

        // 3. 分页查询
        GroupQueryReq queryReq = new GroupQueryReq();
        queryReq.setName(createReq.getName());
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPage(1);
        pageQuery.setSize(10);

        PageResp<GroupResp> pageResp = groupService.page(queryReq, pageQuery);
        assertNotNull(pageResp);
        assertTrue(pageResp.getTotal() > 0);
        assertFalse(pageResp.getList().isEmpty());

        // 4. 列表查询
        List<GroupResp> groups = groupService.list(queryReq);
        assertNotNull(groups);
        assertFalse(groups.isEmpty());

        // 5. 生成邀请码
        String inviteCode = groupService.generateInviteCode(groupId);
        assertNotNull(inviteCode);
        assertFalse(inviteCode.isEmpty());

        // 6. 通过邀请码加入群组
        Long joinedGroupId = groupService.joinByInviteCode(inviteCode, 2L);
        assertEquals(groupId, joinedGroupId);

        // 7. 添加成员
        assertDoesNotThrow(() -> groupService.addMember(groupId, 3L, "MEMBER"));

        // 8. 移除成员
        assertDoesNotThrow(() -> groupService.removeMember(groupId, 3L));

        // 9. 获取统计信息
        var statistics = groupService.getStatistics(groupId);
        assertNotNull(statistics);

        // 10. 删除群组
        assertDoesNotThrow(() -> groupService.delete(List.of(groupId)));
    }

    @Test
    void testGroupMemberManagement() {
        // 创建测试群组
        GroupCreateReq createReq = TestDataUtil.createTestGroupCreateReq();
        Long groupId = groupService.add(createReq);

        // 添加多个成员
        assertDoesNotThrow(() -> {
            groupService.addMember(groupId, 2L, "MEMBER");
            groupService.addMember(groupId, 3L, "ADMIN");
            groupService.addMember(groupId, 4L, "MEMBER");
        });

        // 验证群组信息更新
        GroupResp group = groupService.get(groupId);
        assertTrue(group.getMemberCount() > 1);

        // 移除成员
        assertDoesNotThrow(() -> {
            groupService.removeMember(groupId, 2L);
            groupService.removeMember(groupId, 4L);
        });

        // 验证成员数量减少
        GroupResp updatedGroup = groupService.get(groupId);
        assertTrue(updatedGroup.getMemberCount() < group.getMemberCount());
    }

    @Test
    void testInviteCodeGeneration() {
        // 创建测试群组
        GroupCreateReq createReq = TestDataUtil.createTestGroupCreateReq();
        Long groupId = groupService.add(createReq);

        // 生成多个邀请码
        String inviteCode1 = groupService.generateInviteCode(groupId);
        String inviteCode2 = groupService.generateInviteCode(groupId);

        // 验证邀请码不同
        assertNotEquals(inviteCode1, inviteCode2);

        // 验证邀请码有效性
        Long joinedGroupId1 = groupService.joinByInviteCode(inviteCode1, 2L);
        Long joinedGroupId2 = groupService.joinByInviteCode(inviteCode2, 3L);

        assertEquals(groupId, joinedGroupId1);
        assertEquals(groupId, joinedGroupId2);
    }

    @Test
    void testGroupSearch() {
        // 创建多个测试群组
        GroupCreateReq createReq1 = TestDataUtil.createTestGroupCreateReq();
        createReq1.setName("测试群组A");
        createReq1.setDescription("这是群组A的描述");

        GroupCreateReq createReq2 = TestDataUtil.createTestGroupCreateReq();
        createReq2.setName("测试群组B");
        createReq2.setDescription("这是群组B的描述");

        Long groupId1 = groupService.add(createReq1);
        Long groupId2 = groupService.add(createReq2);

        // 按名称搜索
        GroupQueryReq queryReq = new GroupQueryReq();
        queryReq.setName("群组A");
        List<GroupResp> groups = groupService.list(queryReq);
        assertEquals(1, groups.size());
        assertEquals("测试群组A", groups.get(0).getName());

        // 按描述搜索
        queryReq = new GroupQueryReq();
        queryReq.setDescription("群组B");
        groups = groupService.list(queryReq);
        assertEquals(1, groups.size());
        assertEquals("测试群组B", groups.get(0).getName());

        // 搜索所有测试群组
        queryReq = new GroupQueryReq();
        queryReq.setName("测试群组");
        groups = groupService.list(queryReq);
        assertTrue(groups.size() >= 2);
    }

}
