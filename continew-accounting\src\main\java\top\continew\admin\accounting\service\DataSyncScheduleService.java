package top.continew.admin.accounting.service;

import top.continew.admin.accounting.model.entity.DataSyncScheduleDO;
import top.continew.admin.accounting.model.query.DataSyncScheduleQuery;
import top.continew.admin.accounting.model.req.DataSyncScheduleCreateReq;
import top.continew.admin.accounting.model.req.DataSyncScheduleUpdateReq;
import top.continew.admin.accounting.model.resp.DataSyncScheduleDetailResp;
import top.continew.admin.accounting.model.resp.DataSyncScheduleListResp;
import top.continew.starter.extension.crud.service.BaseService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 数据同步计划服务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface DataSyncScheduleService extends BaseService<DataSyncScheduleListResp, DataSyncScheduleDetailResp, DataSyncScheduleQuery, DataSyncScheduleCreateReq, DataSyncScheduleUpdateReq, DataSyncScheduleDO> {

    // ==================== 计划管理 ====================

    /**
     * 启用计划
     *
     * @param scheduleId 计划ID
     */
    void enableSchedule(Long scheduleId);

    /**
     * 禁用计划
     *
     * @param scheduleId 计划ID
     */
    void disableSchedule(Long scheduleId);

    /**
     * 立即执行计划
     *
     * @param scheduleId 计划ID
     * @return 执行结果
     */
    Map<String, Object> executeScheduleNow(Long scheduleId);

    /**
     * 批量启用/禁用计划
     *
     * @param scheduleIds 计划ID列表
     * @param enabled 是否启用
     */
    void batchUpdateEnabled(List<Long> scheduleIds, Boolean enabled);

    // ==================== 计划调度 ====================

    /**
     * 获取待执行的计划
     *
     * @param currentTime 当前时间
     * @param limit 限制数量
     * @return 待执行计划列表
     */
    List<DataSyncScheduleDO> getPendingSchedules(LocalDateTime currentTime, Integer limit);

    /**
     * 更新计划执行状态
     *
     * @param scheduleId 计划ID
     * @param status 执行状态
     * @param errorMessage 错误信息
     */
    void updateExecutionStatus(Long scheduleId, String status, String errorMessage);

    /**
     * 更新下次执行时间
     *
     * @param scheduleId 计划ID
     * @param nextExecutionTime 下次执行时间
     */
    void updateNextExecutionTime(Long scheduleId, LocalDateTime nextExecutionTime);

    /**
     * 计算下次执行时间
     *
     * @param schedule 计划配置
     * @param currentTime 当前时间
     * @return 下次执行时间
     */
    LocalDateTime calculateNextExecutionTime(DataSyncScheduleDO schedule, LocalDateTime currentTime);

    // ==================== 执行历史 ====================

    /**
     * 获取执行历史
     *
     * @param scheduleId 计划ID
     * @param limit 限制数量
     * @return 执行历史
     */
    List<Map<String, Object>> getExecutionHistory(Long scheduleId, Integer limit);

    /**
     * 获取执行统计
     *
     * @param scheduleId 计划ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 执行统计
     */
    Map<String, Object> getExecutionStatistics(Long scheduleId, String startDate, String endDate);

    /**
     * 清理执行历史
     *
     * @param scheduleId 计划ID
     * @param retentionDays 保留天数
     * @return 清理数量
     */
    Integer cleanupExecutionHistory(Long scheduleId, Integer retentionDays);

    // ==================== 监控告警 ====================

    /**
     * 获取计划健康状态
     *
     * @param groupId 群组ID
     * @return 健康状态列表
     */
    List<Map<String, Object>> getScheduleHealthStatus(Long groupId);

    /**
     * 获取失败计划
     *
     * @param groupId 群组ID
     * @param hours 小时数
     * @return 失败计划列表
     */
    List<Map<String, Object>> getFailedSchedules(Long groupId, Integer hours);

    /**
     * 获取超时计划
     *
     * @param groupId 群组ID
     * @return 超时计划列表
     */
    List<Map<String, Object>> getTimeoutSchedules(Long groupId);

    /**
     * 发送告警通知
     *
     * @param scheduleId 计划ID
     * @param alertType 告警类型
     * @param message 告警消息
     */
    void sendAlert(Long scheduleId, String alertType, String message);

    // ==================== 配置验证 ====================

    /**
     * 验证Cron表达式
     *
     * @param cronExpression Cron表达式
     * @return 验证结果
     */
    Map<String, Object> validateCronExpression(String cronExpression);

    /**
     * 预览执行时间
     *
     * @param scheduleType 计划类型
     * @param cronExpression Cron表达式
     * @param intervalSeconds 间隔秒数
     * @param timezone 时区
     * @param count 预览数量
     * @return 执行时间列表
     */
    List<LocalDateTime> previewExecutionTimes(String scheduleType, String cronExpression, 
                                            Integer intervalSeconds, String timezone, Integer count);

    // ==================== 统计分析 ====================

    /**
     * 获取计划统计
     *
     * @param groupId 群组ID
     * @return 统计信息
     */
    Map<String, Object> getScheduleStatistics(Long groupId);

    /**
     * 获取执行趋势
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param groupBy 分组方式
     * @return 趋势数据
     */
    List<Map<String, Object>> getExecutionTrends(Long groupId, String startDate, String endDate, String groupBy);

    /**
     * 获取性能分析
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 性能分析数据
     */
    List<Map<String, Object>> getPerformanceAnalysis(Long groupId, String startDate, String endDate);

}
