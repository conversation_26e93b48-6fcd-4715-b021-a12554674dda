package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 使用量统计实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("acc_usage_statistics")
public class UsageStatisticsDO extends BaseIdDO {

    /**
     * 群组ID
     */
    private Long groupId;

    /**
     * 统计日期
     */
    private LocalDate statDate;

    /**
     * 交易数量
     */
    private Integer transactionCount;

    /**
     * OCR识别次数
     */
    private Integer ocrCount;

    /**
     * API调用次数
     */
    private Integer apiCalls;

    /**
     * 存储使用量(字节)
     */
    private Long storageUsed;

    /**
     * 导出次数
     */
    private Integer exportCount;

    /**
     * Webhook调用次数
     */
    private Integer webhookCalls;

    /**
     * 活跃用户数
     */
    private Integer activeUsers;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
