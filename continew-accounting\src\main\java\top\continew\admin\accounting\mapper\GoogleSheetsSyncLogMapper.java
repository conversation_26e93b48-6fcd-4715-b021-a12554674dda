package top.continew.admin.accounting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.accounting.model.entity.GoogleSheetsSyncLog;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Google Sheets同步日志Mapper接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface GoogleSheetsSyncLogMapper extends BaseMapper<GoogleSheetsSyncLog> {

    // ==================== 基础查询 ====================

    /**
     * 根据同步ID查询日志
     *
     * @param syncId 同步ID
     * @return 同步日志
     */
    GoogleSheetsSyncLog selectBySyncId(@Param("syncId") String syncId);

    /**
     * 查询配置的同步日志
     *
     * @param configId 配置ID
     * @param limit    限制数量
     * @return 同步日志列表
     */
    List<GoogleSheetsSyncLog> selectByConfigId(@Param("configId") Long configId, @Param("limit") Integer limit);

    /**
     * 查询群组的同步日志
     *
     * @param groupId 群组ID
     * @param limit   限制数量
     * @return 同步日志列表
     */
    List<GoogleSheetsSyncLog> selectByGroupId(@Param("groupId") Long groupId, @Param("limit") Integer limit);

    /**
     * 查询最近的同步日志
     *
     * @param configId 配置ID
     * @return 最近的同步日志
     */
    GoogleSheetsSyncLog selectLatestByConfigId(@Param("configId") Long configId);

    /**
     * 查询正在进行的同步任务
     *
     * @param configId 配置ID
     * @return 同步日志列表
     */
    List<GoogleSheetsSyncLog> selectRunningSyncTasks(@Param("configId") Long configId);

    // ==================== 状态查询 ====================

    /**
     * 查询指定状态的同步日志
     *
     * @param syncStatus 同步状态
     * @param groupId    群组ID
     * @param limit      限制数量
     * @return 同步日志列表
     */
    List<GoogleSheetsSyncLog> selectBySyncStatus(@Param("syncStatus") String syncStatus,
                                                 @Param("groupId") Long groupId,
                                                 @Param("limit") Integer limit);

    /**
     * 查询失败的同步日志
     *
     * @param configId  配置ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 同步日志列表
     */
    List<GoogleSheetsSyncLog> selectFailedSyncLogs(@Param("configId") Long configId,
                                                   @Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 查询超时的同步任务
     *
     * @param timeoutMinutes 超时分钟数
     * @return 同步日志列表
     */
    List<GoogleSheetsSyncLog> selectTimeoutSyncTasks(@Param("timeoutMinutes") Integer timeoutMinutes);

    // ==================== 统计查询 ====================

    /**
     * 统计同步日志数量
     *
     * @param groupId 群组ID
     * @return 统计结果
     */
    Map<String, Object> countSyncLogsByGroupId(@Param("groupId") Long groupId);

    /**
     * 统计配置的同步日志数量
     *
     * @param configId 配置ID
     * @return 统计结果
     */
    Map<String, Object> countSyncLogsByConfigId(@Param("configId") Long configId);

    /**
     * 统计同步状态分布
     *
     * @param groupId   群组ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计结果
     */
    List<Map<String, Object>> countSyncLogsBySyncStatus(@Param("groupId") Long groupId,
                                                        @Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 统计同步类型分布
     *
     * @param groupId   群组ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计结果
     */
    List<Map<String, Object>> countSyncLogsBySyncType(@Param("groupId") Long groupId,
                                                      @Param("startTime") LocalDateTime startTime,
                                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 统计触发方式分布
     *
     * @param groupId   群组ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计结果
     */
    List<Map<String, Object>> countSyncLogsByTriggerType(@Param("groupId") Long groupId,
                                                         @Param("startTime") LocalDateTime startTime,
                                                         @Param("endTime") LocalDateTime endTime);

    /**
     * 统计同步趋势
     *
     * @param groupId   群组ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param interval  时间间隔（hour/day/week/month）
     * @return 统计结果
     */
    List<Map<String, Object>> countSyncTrend(@Param("groupId") Long groupId,
                                             @Param("startTime") LocalDateTime startTime,
                                             @Param("endTime") LocalDateTime endTime,
                                             @Param("interval") String interval);

    // ==================== 性能统计 ====================

    /**
     * 统计同步性能指标
     *
     * @param configId  配置ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 性能指标
     */
    Map<String, Object> selectSyncPerformanceMetrics(@Param("configId") Long configId,
                                                      @Param("startTime") LocalDateTime startTime,
                                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 统计平均同步耗时
     *
     * @param groupId   群组ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 平均耗时统计
     */
    List<Map<String, Object>> selectAvgSyncDuration(@Param("groupId") Long groupId,
                                                    @Param("startTime") LocalDateTime startTime,
                                                    @Param("endTime") LocalDateTime endTime);

    /**
     * 统计同步成功率
     *
     * @param groupId   群组ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 成功率统计
     */
    List<Map<String, Object>> selectSyncSuccessRate(@Param("groupId") Long groupId,
                                                    @Param("startTime") LocalDateTime startTime,
                                                    @Param("endTime") LocalDateTime endTime);

    /**
     * 统计处理记录数分布
     *
     * @param groupId   群组ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 记录数分布
     */
    List<Map<String, Object>> selectProcessedRecordsDistribution(@Param("groupId") Long groupId,
                                                                 @Param("startTime") LocalDateTime startTime,
                                                                 @Param("endTime") LocalDateTime endTime);

    // ==================== 错误分析 ====================

    /**
     * 统计错误类型分布
     *
     * @param groupId   群组ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 错误类型统计
     */
    List<Map<String, Object>> countErrorsByType(@Param("groupId") Long groupId,
                                                @Param("startTime") LocalDateTime startTime,
                                                @Param("endTime") LocalDateTime endTime);

    /**
     * 统计错误频率排行
     *
     * @param groupId   群组ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param limit     限制数量
     * @return 错误频率排行
     */
    List<Map<String, Object>> selectTopErrorsByFrequency(@Param("groupId") Long groupId,
                                                         @Param("startTime") LocalDateTime startTime,
                                                         @Param("endTime") LocalDateTime endTime,
                                                         @Param("limit") Integer limit);

    /**
     * 查询重复错误
     *
     * @param configId      配置ID
     * @param errorCode     错误代码
     * @param hours         小时数
     * @return 重复错误列表
     */
    List<GoogleSheetsSyncLog> selectRepeatedErrors(@Param("configId") Long configId,
                                                   @Param("errorCode") String errorCode,
                                                   @Param("hours") Integer hours);

    // ==================== 排行榜查询 ====================

    /**
     * 查询最活跃的配置
     *
     * @param groupId   群组ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param limit     限制数量
     * @return 配置排行
     */
    List<Map<String, Object>> selectTopConfigsBySyncCount(@Param("groupId") Long groupId,
                                                          @Param("startTime") LocalDateTime startTime,
                                                          @Param("endTime") LocalDateTime endTime,
                                                          @Param("limit") Integer limit);

    /**
     * 查询处理记录数最多的配置
     *
     * @param groupId   群组ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param limit     限制数量
     * @return 配置排行
     */
    List<Map<String, Object>> selectTopConfigsByProcessedRecords(@Param("groupId") Long groupId,
                                                                 @Param("startTime") LocalDateTime startTime,
                                                                 @Param("endTime") LocalDateTime endTime,
                                                                 @Param("limit") Integer limit);

    /**
     * 查询耗时最长的同步任务
     *
     * @param groupId   群组ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param limit     限制数量
     * @return 同步任务排行
     */
    List<Map<String, Object>> selectTopSyncTasksByDuration(@Param("groupId") Long groupId,
                                                           @Param("startTime") LocalDateTime startTime,
                                                           @Param("endTime") LocalDateTime endTime,
                                                           @Param("limit") Integer limit);

    // ==================== 数据清理 ====================

    /**
     * 删除过期日志
     *
     * @param retentionDays 保留天数
     * @return 删除数量
     */
    Integer deleteExpiredLogs(@Param("retentionDays") Integer retentionDays);

    /**
     * 删除配置的所有日志
     *
     * @param configId 配置ID
     * @return 删除数量
     */
    Integer deleteLogsByConfigId(@Param("configId") Long configId);

    /**
     * 删除群组的所有日志
     *
     * @param groupId 群组ID
     * @return 删除数量
     */
    Integer deleteLogsByGroupId(@Param("groupId") Long groupId);

    /**
     * 清理失败日志
     *
     * @param configId      配置ID
     * @param retentionDays 保留天数
     * @return 删除数量
     */
    Integer cleanupFailedLogs(@Param("configId") Long configId, @Param("retentionDays") Integer retentionDays);

    /**
     * 压缩历史日志
     *
     * @param compressDays 压缩天数
     * @return 压缩数量
     */
    Integer compressHistoryLogs(@Param("compressDays") Integer compressDays);

    // ==================== 批量操作 ====================

    /**
     * 批量更新同步状态
     *
     * @param syncIds    同步ID列表
     * @param syncStatus 同步状态
     * @return 更新数量
     */
    Integer batchUpdateSyncStatus(@Param("syncIds") List<String> syncIds, @Param("syncStatus") String syncStatus);

    /**
     * 批量更新结束时间
     *
     * @param syncIds 同步ID列表
     * @param endTime 结束时间
     * @return 更新数量
     */
    Integer batchUpdateEndTime(@Param("syncIds") List<String> syncIds, @Param("endTime") LocalDateTime endTime);

    /**
     * 批量标记为已处理
     *
     * @param logIds 日志ID列表
     * @return 更新数量
     */
    Integer batchMarkAsProcessed(@Param("logIds") List<Long> logIds);

    // ==================== 监控查询 ====================

    /**
     * 查询需要监控的同步任务
     *
     * @param groupId 群组ID
     * @return 同步任务列表
     */
    List<GoogleSheetsSyncLog> selectSyncTasksForMonitoring(@Param("groupId") Long groupId);

    /**
     * 查询异常同步任务
     *
     * @param groupId 群组ID
     * @return 异常任务列表
     */
    List<GoogleSheetsSyncLog> selectAbnormalSyncTasks(@Param("groupId") Long groupId);

    /**
     * 查询性能异常的同步任务
     *
     * @param groupId           群组ID
     * @param maxDurationSeconds 最大耗时秒数
     * @return 性能异常任务列表
     */
    List<GoogleSheetsSyncLog> selectPerformanceAbnormalTasks(@Param("groupId") Long groupId,
                                                             @Param("maxDurationSeconds") Integer maxDurationSeconds);

    // ==================== 报表查询 ====================

    /**
     * 生成同步报表数据
     *
     * @param configId  配置ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 报表数据
     */
    Map<String, Object> generateSyncReport(@Param("configId") Long configId,
                                           @Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);

    /**
     * 生成性能报表数据
     *
     * @param groupId   群组ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 性能报表数据
     */
    Map<String, Object> generatePerformanceReport(@Param("groupId") Long groupId,
                                                  @Param("startTime") LocalDateTime startTime,
                                                  @Param("endTime") LocalDateTime endTime);

    /**
     * 生成错误报表数据
     *
     * @param groupId   群组ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 错误报表数据
     */
    Map<String, Object> generateErrorReport(@Param("groupId") Long groupId,
                                           @Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);
}
