package top.continew.admin.accounting.base;

import org.junit.jupiter.api.BeforeEach;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.containers.MySQLContainer;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

/**
 * 集成测试基类
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@Testcontainers
@Transactional
public abstract class BaseIntegrationTest extends BaseTest {

    @Container
    static MySQLContainer<?> mysql = new MySQLContainer<>("mysql:8.0")
            .withDatabaseName("continew_admin_test")
            .withUsername("test")
            .withPassword("test")
            .withReuse(true);

    @Container
    static GenericContainer<?> redis = new GenericContainer<>("redis:7-alpine")
            .withExposedPorts(6379)
            .withReuse(true);

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        // MySQL配置
        registry.add("spring.datasource.url", mysql::getJdbcUrl);
        registry.add("spring.datasource.username", mysql::getUsername);
        registry.add("spring.datasource.password", mysql::getPassword);
        registry.add("spring.datasource.driver-class-name", mysql::getDriverClassName);

        // Redis配置
        registry.add("spring.redis.host", redis::getHost);
        registry.add("spring.redis.port", redis::getFirstMappedPort);
        
        // JetCache Redis配置
        registry.add("jetcache.remote.default.host", redis::getHost);
        registry.add("jetcache.remote.default.port", redis::getFirstMappedPort);
    }

    @BeforeEach
    @Override
    protected void beforeEach() {
        super.beforeEach();
        // 集成测试前置操作
        setupIntegrationTestData();
    }

    /**
     * 设置集成测试数据
     */
    protected void setupIntegrationTestData() {
        // 子类可重写此方法设置测试数据
    }

    /**
     * 清理集成测试数据
     */
    protected void cleanupIntegrationTestData() {
        // 子类可重写此方法清理测试数据
    }

}
