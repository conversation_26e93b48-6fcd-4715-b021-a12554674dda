package top.continew.admin.bot.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import top.continew.admin.accounting.enums.TransactionType;
import top.continew.admin.bot.model.dto.ParsedCommand;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 命令解析器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
public class CommandParser {

    // 金额表达式正则：支持 +100, -50, 100*2, +100*3 等格式
    private static final Pattern AMOUNT_PATTERN = 
        Pattern.compile("([+-]?)([0-9]+(?:\\.[0-9]+)?)(?:\\*([0-9]+(?:\\.[0-9]+)?))?");
    
    // 标签正则：#标签名
    private static final Pattern TAG_PATTERN = Pattern.compile("#([\\w\\u4e00-\\u9fa5]+)");
    
    // 分类正则：@分类名
    private static final Pattern CATEGORY_PATTERN = Pattern.compile("@([\\w\\u4e00-\\u9fa5]+)");
    
    // 币种正则：$USD, ¥CNY, €EUR 等
    private static final Pattern CURRENCY_PATTERN = Pattern.compile("([¥$€£₹])([A-Z]{3})?");
    
    // 时间正则：支持多种时间格式
    private static final Pattern TIME_PATTERN = 
        Pattern.compile("(\\d{4}-\\d{2}-\\d{2}(?:\\s+\\d{2}:\\d{2}(?::\\d{2})?)?|\\d{2}:\\d{2}(?::\\d{2})?|昨天|今天|明天)");
    
    // 分摊正则：/split @user1 @user2 或 /split 3
    private static final Pattern SPLIT_PATTERN =
        Pattern.compile("/split(?:\\s+(@\\w+(?:\\s+@\\w+)*)|\\s+(\\d+))?");

    // 钱包正则：钱包:支付宝 或 wallet:alipay
    private static final Pattern WALLET_PATTERN =
        Pattern.compile("(?:钱包|wallet)[:：]([\\w\\u4e00-\\u9fa5]+)");

    // 自然语言金额表达式：花了100、赚了500、收入1000等
    private static final Pattern NATURAL_AMOUNT_PATTERN =
        Pattern.compile("(?:花了|花费|支出|赚了|收入|收到|转账)\\s*([0-9]+(?:\\.[0-9]+)?)");

    // 自然语言时间表达式：上周、这个月、去年等
    private static final Pattern NATURAL_TIME_PATTERN =
        Pattern.compile("(上周|这周|本周|下周|上个月|这个月|本月|下个月|去年|今年|明年|前天|昨天|今天|明天|后天)");

    // 转账模式：从A到B、A转B等
    private static final Pattern TRANSFER_PATTERN =
        Pattern.compile("(?:从|由)\\s*([\\w\\u4e00-\\u9fa5]+)\\s*(?:到|转|给)\\s*([\\w\\u4e00-\\u9fa5]+)");

    // 重复交易模式：每天、每周、每月等
    private static final Pattern RECURRING_PATTERN =
        Pattern.compile("(每天|每周|每月|每年|每个月|每个星期)");

    /**
     * 解析命令文本
     *
     * @param text 原始文本
     * @return 解析结果
     */
    public ParsedCommand parseCommand(String text) {
        if (StringUtils.isBlank(text)) {
            return null;
        }

        ParsedCommand command = new ParsedCommand();
        command.setOriginalText(text);

        try {
            // 解析金额和交易类型
            parseAmountAndType(text, command);
            
            // 解析标签
            command.setTags(extractTags(text));
            
            // 解析分类
            command.setCategory(extractCategory(text));
            
            // 解析币种
            command.setCurrency(extractCurrency(text));
            
            // 解析时间
            command.setTransactionTime(extractTime(text));
            
            // 解析分摊信息
            parseSplitInfo(text, command);

            // 解析钱包信息
            command.setWallet(extractWallet(text));

            // 解析转账信息
            parseTransferInfo(text, command);

            // 解析重复交易信息
            parseRecurringInfo(text, command);

            // 如果基础解析失败，尝试自然语言解析
            if (command.getAmount() == null) {
                parseNaturalLanguage(text, command);
            }

            // 解析描述（去除已解析的部分）
            command.setDescription(extractDescription(text, command));

            // 智能推荐
            if (command.getCategory() == null) {
                String suggestedCategory = suggestCategory(command.getDescription());
                command.setSuggestedCategory(suggestedCategory);
            }

            if (command.getTags() == null || command.getTags().isEmpty()) {
                List<String> suggestedTags = suggestTags(command.getDescription());
                command.setSuggestedTags(suggestedTags);
            }

            // 计算置信度
            command.setConfidenceScore(calculateConfidenceScore(command));

            // 验证命令完整性
            validateCommand(command);
            
        } catch (Exception e) {
            log.error("解析命令失败: {}", text, e);
            command.setValid(false);
            command.setErrorMessage("命令解析失败: " + e.getMessage());
        }

        return command;
    }

    /**
     * 解析金额和交易类型
     */
    private void parseAmountAndType(String text, ParsedCommand command) {
        Matcher matcher = AMOUNT_PATTERN.matcher(text);
        if (matcher.find()) {
            String sign = matcher.group(1);
            BigDecimal amount = new BigDecimal(matcher.group(2));
            String multiplier = matcher.group(3);

            // 处理乘法
            if (multiplier != null) {
                amount = amount.multiply(new BigDecimal(multiplier));
            }

            command.setAmount(amount);
            
            // 根据符号确定交易类型
            if ("+".equals(sign)) {
                command.setType(TransactionType.INCOME);
            } else if ("-".equals(sign)) {
                command.setType(TransactionType.EXPENSE);
            } else {
                // 默认为支出
                command.setType(TransactionType.EXPENSE);
            }
        }
    }

    /**
     * 提取标签
     */
    private List<String> extractTags(String text) {
        List<String> tags = new ArrayList<>();
        Matcher matcher = TAG_PATTERN.matcher(text);
        while (matcher.find()) {
            tags.add(matcher.group(1));
        }
        return tags;
    }

    /**
     * 提取分类
     */
    private String extractCategory(String text) {
        Matcher matcher = CATEGORY_PATTERN.matcher(text);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    /**
     * 提取币种
     */
    private String extractCurrency(String text) {
        Matcher matcher = CURRENCY_PATTERN.matcher(text);
        if (matcher.find()) {
            String symbol = matcher.group(1);
            String code = matcher.group(2);
            
            if (code != null) {
                return code;
            }
            
            // 根据符号推断币种
            return switch (symbol) {
                case "¥" -> "CNY";
                case "$" -> "USD";
                case "€" -> "EUR";
                case "£" -> "GBP";
                case "₹" -> "INR";
                default -> "CNY"; // 默认人民币
            };
        }
        return "CNY"; // 默认人民币
    }

    /**
     * 提取时间
     */
    private LocalDateTime extractTime(String text) {
        Matcher matcher = TIME_PATTERN.matcher(text);
        if (matcher.find()) {
            String timeStr = matcher.group(1);
            
            try {
                // 处理相对时间
                if ("昨天".equals(timeStr)) {
                    return LocalDateTime.now().minusDays(1);
                } else if ("今天".equals(timeStr)) {
                    return LocalDateTime.now();
                } else if ("明天".equals(timeStr)) {
                    return LocalDateTime.now().plusDays(1);
                }
                
                // 处理绝对时间
                if (timeStr.contains("-")) {
                    // 日期格式
                    if (timeStr.length() > 10) {
                        return LocalDateTime.parse(timeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    } else {
                        return LocalDateTime.parse(timeStr + " 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    }
                } else if (timeStr.contains(":")) {
                    // 时间格式，使用今天的日期
                    String today = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    return LocalDateTime.parse(today + " " + timeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                }
                
            } catch (DateTimeParseException e) {
                log.warn("时间解析失败: {}", timeStr, e);
            }
        }
        
        return LocalDateTime.now(); // 默认当前时间
    }

    /**
     * 解析分摊信息
     */
    private void parseSplitInfo(String text, ParsedCommand command) {
        Matcher matcher = SPLIT_PATTERN.matcher(text);
        if (matcher.find()) {
            command.setSplitEnabled(true);
            
            String users = matcher.group(1);
            String count = matcher.group(2);
            
            if (users != null) {
                // 解析用户列表
                List<String> splitUsers = new ArrayList<>();
                String[] userArray = users.split("\\s+");
                for (String user : userArray) {
                    if (user.startsWith("@")) {
                        splitUsers.add(user.substring(1));
                    }
                }
                command.setSplitUsers(splitUsers);
            } else if (count != null) {
                // 设置分摊人数
                command.setSplitCount(Integer.parseInt(count));
            }
        }
    }

    /**
     * 提取描述（去除已解析的部分）
     */
    private String extractDescription(String text, ParsedCommand command) {
        String description = text;

        // 移除金额表达式
        description = AMOUNT_PATTERN.matcher(description).replaceAll("");
        description = NATURAL_AMOUNT_PATTERN.matcher(description).replaceAll("");

        // 移除标签
        description = TAG_PATTERN.matcher(description).replaceAll("");

        // 移除分类
        description = CATEGORY_PATTERN.matcher(description).replaceAll("");

        // 移除币种
        description = CURRENCY_PATTERN.matcher(description).replaceAll("");

        // 移除时间
        description = TIME_PATTERN.matcher(description).replaceAll("");
        description = NATURAL_TIME_PATTERN.matcher(description).replaceAll("");

        // 移除分摊信息
        description = SPLIT_PATTERN.matcher(description).replaceAll("");

        // 移除钱包信息
        description = WALLET_PATTERN.matcher(description).replaceAll("");

        // 移除转账信息
        description = TRANSFER_PATTERN.matcher(description).replaceAll("");

        // 移除重复模式
        description = RECURRING_PATTERN.matcher(description).replaceAll("");

        // 清理多余空格
        description = description.replaceAll("\\s+", " ").trim();

        return StringUtils.isBlank(description) ? "记账" : description;
    }

    /**
     * 验证命令完整性
     */
    private void validateCommand(ParsedCommand command) {
        if (command.getAmount() == null || command.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            command.setValid(false);
            command.setErrorMessage("金额必须大于0");
            return;
        }
        
        if (command.getType() == null) {
            command.setValid(false);
            command.setErrorMessage("无法识别交易类型");
            return;
        }
        
        command.setValid(true);
    }

    /**
     * 提取钱包信息
     */
    private String extractWallet(String text) {
        Matcher matcher = WALLET_PATTERN.matcher(text);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    /**
     * 解析转账信息
     */
    private void parseTransferInfo(String text, ParsedCommand command) {
        Matcher matcher = TRANSFER_PATTERN.matcher(text);
        if (matcher.find()) {
            command.setType(TransactionType.TRANSFER);
            command.setFromWallet(matcher.group(1));
            command.setToWallet(matcher.group(2));
        }
    }

    /**
     * 解析重复交易信息
     */
    private void parseRecurringInfo(String text, ParsedCommand command) {
        Matcher matcher = RECURRING_PATTERN.matcher(text);
        if (matcher.find()) {
            String pattern = matcher.group(1);
            command.setRecurringEnabled(true);
            command.setRecurringPattern(pattern);
        }
    }

    /**
     * 自然语言解析
     */
    private void parseNaturalLanguage(String text, ParsedCommand command) {
        // 解析自然语言金额表达式
        Matcher amountMatcher = NATURAL_AMOUNT_PATTERN.matcher(text);
        if (amountMatcher.find()) {
            BigDecimal amount = new BigDecimal(amountMatcher.group(1));
            command.setAmount(amount);

            String verb = amountMatcher.group(0).split("\\s*[0-9]")[0];
            TransactionType type = switch (verb) {
                case "花了", "花费", "支出" -> TransactionType.EXPENSE;
                case "赚了", "收入", "收到" -> TransactionType.INCOME;
                case "转账" -> TransactionType.TRANSFER;
                default -> TransactionType.EXPENSE;
            };
            command.setType(type);
        }

        // 解析自然语言时间表达式
        Matcher timeMatcher = NATURAL_TIME_PATTERN.matcher(text);
        if (timeMatcher.find()) {
            String timeStr = timeMatcher.group(1);
            LocalDateTime time = parseNaturalTime(timeStr);
            if (time != null) {
                command.setTransactionTime(time);
            }
        }
    }

    /**
     * 解析自然语言时间
     */
    private LocalDateTime parseNaturalTime(String timeStr) {
        LocalDateTime now = LocalDateTime.now();

        return switch (timeStr) {
            case "前天" -> now.minusDays(2);
            case "昨天" -> now.minusDays(1);
            case "今天" -> now;
            case "明天" -> now.plusDays(1);
            case "后天" -> now.plusDays(2);
            case "上周" -> now.minusWeeks(1);
            case "这周", "本周" -> now;
            case "下周" -> now.plusWeeks(1);
            case "上个月" -> now.minusMonths(1);
            case "这个月", "本月" -> now;
            case "下个月" -> now.plusMonths(1);
            case "去年" -> now.minusYears(1);
            case "今年" -> now;
            case "明年" -> now.plusYears(1);
            default -> null;
        };
    }

    /**
     * 智能分类推荐
     */
    public String suggestCategory(String description) {
        if (description == null) return null;

        String desc = description.toLowerCase();

        // 餐饮类
        if (desc.contains("午餐") || desc.contains("晚餐") || desc.contains("早餐") ||
            desc.contains("咖啡") || desc.contains("奶茶") || desc.contains("餐厅") ||
            desc.contains("外卖") || desc.contains("聚餐")) {
            return "餐饮";
        }

        // 交通类
        if (desc.contains("打车") || desc.contains("地铁") || desc.contains("公交") ||
            desc.contains("出租车") || desc.contains("滴滴") || desc.contains("油费") ||
            desc.contains("停车")) {
            return "交通";
        }

        // 购物类
        if (desc.contains("购物") || desc.contains("淘宝") || desc.contains("京东") ||
            desc.contains("超市") || desc.contains("商场") || desc.contains("买")) {
            return "购物";
        }

        // 娱乐类
        if (desc.contains("电影") || desc.contains("游戏") || desc.contains("ktv") ||
            desc.contains("旅游") || desc.contains("娱乐")) {
            return "娱乐";
        }

        // 医疗类
        if (desc.contains("医院") || desc.contains("药") || desc.contains("看病") ||
            desc.contains("体检") || desc.contains("医疗")) {
            return "医疗";
        }

        // 工作收入类
        if (desc.contains("工资") || desc.contains("奖金") || desc.contains("提成") ||
            desc.contains("兼职") || desc.contains("收入")) {
            return "工作收入";
        }

        return null;
    }

    /**
     * 智能标签推荐
     */
    public List<String> suggestTags(String description) {
        List<String> tags = new ArrayList<>();
        if (description == null) return tags;

        String desc = description.toLowerCase();

        // 时间标签
        if (desc.contains("早餐")) tags.add("早餐");
        if (desc.contains("午餐")) tags.add("午餐");
        if (desc.contains("晚餐")) tags.add("晚餐");

        // 场景标签
        if (desc.contains("工作")) tags.add("工作");
        if (desc.contains("生活")) tags.add("生活");
        if (desc.contains("学习")) tags.add("学习");
        if (desc.contains("健康")) tags.add("健康");

        // 频率标签
        if (desc.contains("日常")) tags.add("日常");
        if (desc.contains("必需")) tags.add("必需");
        if (desc.contains("临时")) tags.add("临时");

        return tags;
    }

    /**
     * 计算置信度分数
     */
    private Double calculateConfidenceScore(ParsedCommand command) {
        double score = 0.0;

        // 基础信息完整性
        if (command.getAmount() != null && command.getAmount().compareTo(BigDecimal.ZERO) > 0) {
            score += 30.0; // 金额有效 +30分
        }

        if (command.getType() != null) {
            score += 20.0; // 类型明确 +20分
        }

        if (StringUtils.isNotBlank(command.getDescription()) && !"记账".equals(command.getDescription())) {
            score += 15.0; // 有具体描述 +15分
        }

        // 详细信息完整性
        if (StringUtils.isNotBlank(command.getCategory())) {
            score += 10.0; // 有分类 +10分
        }

        if (command.getTags() != null && !command.getTags().isEmpty()) {
            score += 10.0; // 有标签 +10分
        }

        if (command.getTransactionTime() != null) {
            score += 5.0; // 有时间 +5分
        }

        if (StringUtils.isNotBlank(command.getWallet())) {
            score += 5.0; // 有钱包 +5分
        }

        // 智能推荐加分
        if (StringUtils.isNotBlank(command.getSuggestedCategory())) {
            score += 3.0; // 智能分类推荐 +3分
        }

        if (command.getSuggestedTags() != null && !command.getSuggestedTags().isEmpty()) {
            score += 2.0; // 智能标签推荐 +2分
        }

        return Math.min(score, 100.0); // 最高100分
    }

    /**
     * 批量解析命令
     */
    public List<ParsedCommand> parseCommands(List<String> texts) {
        return texts.stream()
                .map(this::parseCommand)
                .filter(cmd -> cmd != null && cmd.getValid())
                .toList();
    }

    /**
     * 验证命令语法
     */
    public boolean isValidCommandSyntax(String text) {
        if (StringUtils.isBlank(text)) {
            return false;
        }

        // 检查是否包含金额
        boolean hasAmount = AMOUNT_PATTERN.matcher(text).find() ||
                           NATURAL_AMOUNT_PATTERN.matcher(text).find();

        // 检查是否为系统命令
        boolean isSystemCommand = text.startsWith("/");

        return hasAmount || isSystemCommand;
    }

    /**
     * 获取命令建议
     */
    public List<String> getCommandSuggestions(String partialText) {
        List<String> suggestions = new ArrayList<>();

        if (StringUtils.isBlank(partialText)) {
            suggestions.add("+100 午餐");
            suggestions.add("-50 打车");
            suggestions.add("花了30买咖啡");
            suggestions.add("/help");
            return suggestions;
        }

        String text = partialText.toLowerCase();

        // 根据输入内容提供建议
        if (text.contains("吃") || text.contains("餐")) {
            suggestions.add("-30 午餐 @餐饮 #日常");
            suggestions.add("-15 咖啡 @餐饮 #工作");
            suggestions.add("-80 聚餐 @餐饮 #社交 /split 3");
        }

        if (text.contains("车") || text.contains("交通")) {
            suggestions.add("-20 地铁 @交通 #通勤");
            suggestions.add("-35 打车 @交通 #临时");
            suggestions.add("-300 油费 @交通 #汽车");
        }

        if (text.contains("买") || text.contains("购")) {
            suggestions.add("-200 购物 @生活用品 #必需");
            suggestions.add("-500 衣服 @服装 #季节");
        }

        if (text.contains("工资") || text.contains("收入")) {
            suggestions.add("+5000 工资 @工作收入 #每月");
            suggestions.add("+1000 奖金 @工作收入 #临时");
        }

        return suggestions;
    }

    /**
     * 获取帮助信息
     */
    public String getHelpMessage() {
        return """
            📝 记账命令格式：

            基础格式：
            • +100 午餐 - 收入100元
            • -50 打车 - 支出50元
            • 100 购物 - 支出100元（默认）

            高级格式：
            • -50 @餐饮 #午餐 - 支出50元，分类餐饮，标签午餐
            • +1000 ¥CNY 工资 - 收入1000人民币
            • -20*3 咖啡 - 支出60元（20*3）
            • -100 昨天 购物 - 昨天支出100元

            自然语言格式：
            • 花了50块钱买咖啡 - 支出50元
            • 今天赚了1000工资 - 收入1000元
            • 昨天花费100购物 - 昨天支出100元

            钱包和转账：
            • -100 午餐 钱包:支付宝 - 从支付宝支出
            • 从支付宝到银行卡转账500 - 转账500元

            分摊功能：
            • -100 聚餐 /split @张三 @李四 - 与张三李四分摊
            • -150 聚餐 /split 3 - 3人平均分摊

            重复交易：
            • -50 每天 咖啡 - 每天重复的咖啡支出
            • +5000 每月 工资 - 每月重复的工资收入

            其他命令：
            • /help - 显示帮助
            • /balance - 查看余额
            • /history - 查看历史记录
            • /suggest - 获取智能建议
            """;
    }
}
