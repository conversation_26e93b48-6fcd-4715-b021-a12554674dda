package top.continew.admin.bot.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import top.continew.admin.accounting.enums.PlatformType;
import top.continew.admin.accounting.enums.TransactionType;
import top.continew.admin.accounting.service.TransactionService;
import top.continew.admin.bot.model.dto.CommandExecutionResult;
import top.continew.admin.bot.model.dto.ParsedCommand;

import java.math.BigDecimal;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 命令处理引擎测试
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@ExtendWith(MockitoExtension.class)
class CommandProcessingEngineTest {

    @Mock
    private CommandParser commandParser;

    @Mock
    private TransactionService transactionService;

    @Mock
    private CommandHistoryService commandHistoryService;

    @InjectMocks
    private CommandProcessingEngine commandProcessingEngine;

    private ParsedCommand validCommand;
    private ParsedCommand invalidCommand;

    @BeforeEach
    void setUp() {
        // 设置有效命令
        validCommand = new ParsedCommand();
        validCommand.setValid(true);
        validCommand.setType(TransactionType.EXPENSE);
        validCommand.setAmount(new BigDecimal("50.00"));
        validCommand.setCurrency("CNY");
        validCommand.setDescription("午餐");
        validCommand.setCategory("餐饮");
        validCommand.setTags(List.of("日常"));

        // 设置无效命令
        invalidCommand = new ParsedCommand();
        invalidCommand.setValid(false);
        invalidCommand.setErrorMessage("金额必须大于0");
    }

    @Test
    void testProcessValidCommand() {
        // Given
        String commandText = "-50 午餐 @餐饮 #日常";
        PlatformType platform = PlatformType.TELEGRAM;
        Long groupId = 1L;
        Long userId = 1L;

        when(commandParser.parseCommand(commandText)).thenReturn(validCommand);
        when(transactionService.add(any())).thenReturn(123L);

        // When
        CommandExecutionResult result = commandProcessingEngine.processCommand(commandText, platform, groupId, userId);

        // Then
        assertTrue(result.getSuccess());
        assertNotNull(result.getMessage());
        assertEquals(123L, result.getBusinessId());
        verify(commandHistoryService).recordCommandExecution(eq(commandText), eq(validCommand), eq(result), eq(platform), eq(groupId), eq(userId));
    }

    @Test
    void testProcessInvalidCommand() {
        // Given
        String commandText = "invalid command";
        PlatformType platform = PlatformType.TELEGRAM;
        Long groupId = 1L;
        Long userId = 1L;

        when(commandParser.parseCommand(commandText)).thenReturn(invalidCommand);

        // When
        CommandExecutionResult result = commandProcessingEngine.processCommand(commandText, platform, groupId, userId);

        // Then
        assertFalse(result.getSuccess());
        assertEquals("金额必须大于0", result.getMessage());
        assertNull(result.getBusinessId());
        verify(commandHistoryService).recordCommandExecution(eq(commandText), eq(invalidCommand), eq(result), eq(platform), eq(groupId), eq(userId));
    }

    @Test
    void testProcessNullCommand() {
        // Given
        String commandText = "";
        PlatformType platform = PlatformType.TELEGRAM;
        Long groupId = 1L;
        Long userId = 1L;

        when(commandParser.parseCommand(commandText)).thenReturn(null);

        // When
        CommandExecutionResult result = commandProcessingEngine.processCommand(commandText, platform, groupId, userId);

        // Then
        assertFalse(result.getSuccess());
        assertEquals("无法解析命令", result.getMessage());
        verify(commandHistoryService).recordCommandExecution(eq(commandText), isNull(), eq(result), eq(platform), eq(groupId), eq(userId));
    }

    @Test
    void testProcessCommandsAsync() {
        // Given
        String commandText = "-50 午餐";
        PlatformType platform = PlatformType.TELEGRAM;
        Long groupId = 1L;
        Long userId = 1L;

        when(commandParser.parseCommand(commandText)).thenReturn(validCommand);
        when(transactionService.add(any())).thenReturn(123L);

        // When
        var future = commandProcessingEngine.processCommandAsync(commandText, platform, groupId, userId);

        // Then
        assertNotNull(future);
        CommandExecutionResult result = future.join();
        assertTrue(result.getSuccess());
    }

    @Test
    void testProcessBatchCommands() {
        // Given
        List<String> commands = List.of("-50 午餐", "-30 咖啡", "+1000 工资");
        PlatformType platform = PlatformType.TELEGRAM;
        Long groupId = 1L;
        Long userId = 1L;

        when(commandParser.parseCommand(anyString())).thenReturn(validCommand);
        when(transactionService.add(any())).thenReturn(123L);

        // When
        List<CommandExecutionResult> results = commandProcessingEngine.processCommands(commands, platform, groupId, userId);

        // Then
        assertEquals(3, results.size());
        assertTrue(results.stream().allMatch(CommandExecutionResult::getSuccess));
        verify(commandParser, times(3)).parseCommand(anyString());
        verify(transactionService, times(3)).add(any());
    }

    @Test
    void testProcessSystemCommandHelp() {
        // Given
        String command = "/help";
        PlatformType platform = PlatformType.TELEGRAM;
        Long groupId = 1L;
        Long userId = 1L;

        when(commandParser.getHelpMessage()).thenReturn("帮助信息");

        // When
        CommandExecutionResult result = commandProcessingEngine.processSystemCommand(command, platform, groupId, userId);

        // Then
        assertTrue(result.getSuccess());
        assertEquals("帮助信息", result.getMessage());
    }

    @Test
    void testProcessSystemCommandBalance() {
        // Given
        String command = "/balance";
        PlatformType platform = PlatformType.TELEGRAM;
        Long groupId = 1L;
        Long userId = 1L;

        // When
        CommandExecutionResult result = commandProcessingEngine.processSystemCommand(command, platform, groupId, userId);

        // Then
        assertTrue(result.getSuccess());
        assertNotNull(result.getMessage());
    }

    @Test
    void testProcessSystemCommandUnknown() {
        // Given
        String command = "/unknown";
        PlatformType platform = PlatformType.TELEGRAM;
        Long groupId = 1L;
        Long userId = 1L;

        // When
        CommandExecutionResult result = commandProcessingEngine.processSystemCommand(command, platform, groupId, userId);

        // Then
        assertFalse(result.getSuccess());
        assertTrue(result.getMessage().contains("未知命令"));
    }

    @Test
    void testHasPermission() {
        // Given
        String command = "-50 午餐";
        Long userId = 1L;
        Long groupId = 1L;

        // When
        boolean hasPermission = commandProcessingEngine.hasPermission(command, userId, groupId);

        // Then
        assertTrue(hasPermission); // 当前实现总是返回true
    }

    @Test
    void testGetExecutionHistory() {
        // Given
        Long userId = 1L;
        Long groupId = 1L;
        int limit = 10;

        // When
        List<CommandExecutionResult> history = commandProcessingEngine.getExecutionHistory(userId, groupId, limit);

        // Then
        assertNotNull(history);
        verify(commandHistoryService).getUserCommandHistory(userId, groupId, limit);
    }

    @Test
    void testProcessCommandWithException() {
        // Given
        String commandText = "-50 午餐";
        PlatformType platform = PlatformType.TELEGRAM;
        Long groupId = 1L;
        Long userId = 1L;

        when(commandParser.parseCommand(commandText)).thenThrow(new RuntimeException("解析错误"));

        // When
        CommandExecutionResult result = commandProcessingEngine.processCommand(commandText, platform, groupId, userId);

        // Then
        assertFalse(result.getSuccess());
        assertTrue(result.getMessage().contains("命令处理失败"));
    }
}
