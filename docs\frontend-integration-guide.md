# ContiNew Admin 群组记账系统 - 前端开发对接文档

## 📋 目录

1. [项目开发总结](#1-项目开发总结)
2. [API接口文档](#2-api接口文档)
3. [数据模型文档](#3-数据模型文档)
4. [前端集成指南](#4-前端集成指南)
5. [开发规范和示例](#5-开发规范和示例)
6. [环境配置说明](#6-环境配置说明)
7. [错误处理和调试](#7-错误处理和调试)

---

## 1. 项目开发总结

### 1.1 已完成功能模块清单

#### 🏢 核心业务模块

| 模块 | 功能描述 | 完成状态 | 核心功能点 |
|------|----------|----------|------------|
| **群组管理** | 群组创建、配置、成员管理 | ✅ 完成 | CRUD操作、成员管理、邀请码系统、权限控制、统计分析 |
| **交易管理** | 账单记录、分摊、历史查询 | ✅ 完成 | CRUD操作、批量操作、统计分析、趋势分析、模板系统 |
| **钱包管理** | 多币种钱包、余额管理 | ✅ 完成 | 多币种支持、余额管理、转账功能、汇率转换、历史记录 |
| **分类管理** | 收支分类、树形结构 | ✅ 完成 | 树形分类、默认分类、智能推荐、使用统计 |
| **标签管理** | 标签创建、管理、统计 | ✅ 完成 | 标签CRUD、使用统计、自动建议、热门标签 |
| **债务管理** | 个人间债务跟踪 | ✅ 完成 | 债务记录、还款跟踪、结算计算、提醒功能 |
| **报表统计** | 财务报表、数据分析 | ✅ 完成 | 多维度统计、图表分析、自定义报表、数据导出 |

#### 🤖 机器人集成模块

| 模块 | 功能描述 | 完成状态 | 支持平台 |
|------|----------|----------|----------|
| **Telegram机器人** | Telegram Bot集成 | ✅ 完成 | 命令解析、消息处理、群组管理 |
| **Discord机器人** | Discord Bot集成 | ✅ 完成 | Slash Commands、交互式界面 |
| **命令处理引擎** | 通用命令解析 | ✅ 完成 | 智能语法解析、多语言支持 |
| **异步消息处理** | 消息队列处理 | ✅ 完成 | RabbitMQ集成、异步处理 |

#### 🌐 Web后台管理

| 模块 | 功能描述 | 完成状态 | 核心特性 |
|------|----------|----------|----------|
| **数据分析服务** | 高级数据分析 | ✅ 完成 | 多维钻取、自定义报表、数据聚合 |
| **财务管理服务** | 财务管理后端 | ✅ 完成 | 预算管理、账单审核、财务预警 |
| **报表引擎** | 可配置报表 | ✅ 完成 | 动态查询、报表调度、模板管理 |
| **智能规则引擎** | 自动化任务 | ✅ 完成 | 自动分类、智能提醒、定时任务 |

#### 🔧 高级功能与集成

| 模块 | 功能描述 | 完成状态 | 集成能力 |
|------|----------|----------|----------|
| **OCR收据识别** | 收据自动识别 | ✅ 完成 | 图像识别、数据提取、账单生成 |
| **RESTful API** | 第三方API接口 | ✅ 完成 | 完整REST API、认证授权、限流保护 |
| **Google Sheets集成** | 表格同步 | ✅ 完成 | 实时双向同步、数据映射 |
| **Zapier集成** | 第三方应用连接 | ✅ 完成 | Webhook支持、自动化工作流 |
| **订阅管理系统** | 套餐计费 | ✅ 完成 | 多套餐支持、功能限制、计费统计 |
| **数据同步服务** | 多数据源同步 | ✅ 完成 | 增量同步、冲突解决、多适配器 |
| **通知推送服务** | 统一通知 | ✅ 完成 | 多渠道通知、模板管理、批量处理 |
| **文件存储服务** | 文件管理 | ✅ 完成 | 多存储后端、CDN加速、权限控制 |
| **缓存优化服务** | 性能优化 | ✅ 完成 | 多级缓存、智能失效、性能监控 |

#### 🧪 测试与部署

| 模块 | 功能描述 | 完成状态 | 覆盖率 |
|------|----------|----------|--------|
| **单元测试** | 全面单元测试 | ✅ 完成 | >80% 代码覆盖率 |
| **集成测试** | 系统集成测试 | 🔄 进行中 | TestContainers支持 |
| **性能优化** | 性能测试优化 | 📋 待开始 | 响应时间优化 |
| **生产部署** | CI/CD部署 | 📋 待开始 | 自动化部署 |

### 1.2 技术架构概览

#### 后端技术栈
- **框架**: Spring Boot 3.2.x + Java 17 LTS
- **数据库**: MySQL 8.0.x + Redis 7.x
- **ORM**: MyBatis Plus 3.5.x
- **认证**: SaToken 1.37.x
- **缓存**: JetCache (Caffeine + Redisson)
- **消息队列**: RabbitMQ
- **文档**: Swagger/OpenAPI 3.0

#### 数据库设计
- **核心表**: 群组、用户、交易、钱包、分类、标签、债务
- **扩展表**: 通知、文件、缓存、同步、API密钥
- **索引优化**: 查询性能优化、复合索引设计
- **数据完整性**: 外键约束、数据验证

---

## 2. API接口文档

### 2.1 API文档访问方式

#### 2.1.1 在线API文档
本系统集成了 **Swagger/OpenAPI 3.0** 和 **Knife4j** 提供完整的API文档：

| 文档类型 | 访问地址 | 描述 |
|----------|----------|------|
| **Knife4j增强文档** | `http://localhost:8080/doc.html` | 推荐使用，功能更丰富 |
| **标准Swagger UI** | `http://localhost:8080/swagger-ui` | 标准OpenAPI文档界面 |
| **OpenAPI JSON** | `http://localhost:8080/v3/api-docs` | 原始API规范文件 |

#### 2.1.2 API文档分组
系统按功能模块对API进行分组管理：

- **全部接口** - 所有可用的API接口
- **通用接口** - 验证码、仪表盘等通用功能
- **系统管理** - 用户、角色、菜单管理
- **记账核心** - 群组、交易、钱包等核心功能
- **能力开放** - 第三方应用集成接口

#### 2.1.3 接口基础信息

**环境配置:**
```yaml
# 开发环境
Base URL: http://localhost:8080
API Prefix: /api/v1

# 测试环境
Base URL: https://api-test.continew.top
API Prefix: /api/v1

# 生产环境
Base URL: https://api.continew.top
API Prefix: /api/v1
```

**请求头配置:**
```http
Content-Type: application/json
Authorization: Bearer {your_jwt_token}
X-API-Key: {your_api_key}  # 可选，用于API密钥认证
Accept: application/json
```

### 2.2 认证授权机制

#### 2.2.1 认证方式
系统支持多种认证方式：

**1. JWT Token认证（推荐）**
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**2. API密钥认证**
```http
X-API-Key: your_api_key_here
```

**3. 签名认证（高安全场景）**
```http
# 请求参数中包含签名
POST /api/v1/accounting/transaction?sign=abc123&timestamp=**********
```

#### 2.2.2 获取访问令牌

**登录接口:**
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "encrypted_password",
  "clientId": "web_client",
  "authType": "ACCOUNT"
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_here",
    "expiresIn": 86400,
    "tokenType": "Bearer",
    "userInfo": {
      "id": 1,
      "username": "admin",
      "nickname": "管理员",
      "permissions": ["accounting:group:create", "accounting:transaction:view"]
    }
  },
  "timestamp": "2025-01-01T12:00:00Z"
}
```

#### 2.2.3 Token刷新
```http
POST /api/v1/auth/refresh
Authorization: Bearer {refresh_token}
```

### 2.3 统一响应格式

#### 2.3.1 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "示例数据"
  },
  "timestamp": "2025-01-01T12:00:00Z"
}
```

#### 2.3.2 分页响应
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "total": 100,
    "list": [
      {"id": 1, "name": "项目1"},
      {"id": 2, "name": "项目2"}
    ],
    "page": 1,
    "size": 10,
    "pages": 10
  },
  "timestamp": "2025-01-01T12:00:00Z"
}
```

#### 2.3.3 错误响应
```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": {
    "field": "amount",
    "error": "金额必须大于0",
    "details": "validation failed"
  },
  "timestamp": "2025-01-01T12:00:00Z"
}
```

### 2.4 限流和配额

#### 2.4.1 限流规则
系统实施多层限流保护：

| 限流类型 | 默认限制 | 时间窗口 | 说明 |
|----------|----------|----------|------|
| **IP限流** | 100次/分钟 | 60秒 | 基于客户端IP |
| **用户限流** | 200次/分钟 | 60秒 | 基于登录用户 |
| **API密钥限流** | 1000次/小时 | 3600秒 | 基于API密钥 |
| **特殊接口限流** | 自定义 | 自定义 | 如验证码发送 |

#### 2.4.2 限流响应
当触发限流时，返回HTTP 429状态码：
```json
{
  "code": 429,
  "message": "请求过于频繁，请稍后再试",
  "data": {
    "retryAfter": 60,
    "limit": 100,
    "remaining": 0,
    "resetTime": "2025-01-01T12:01:00Z"
  },
  "timestamp": "2025-01-01T12:00:00Z"
}
```

#### 2.4.3 订阅配额限制
不同订阅套餐有不同的使用配额：

| 套餐类型 | 月交易数限制 | 群组数限制 | API调用限制 |
|----------|-------------|-----------|------------|
| **试用版** | 100 | 1 | 1000/天 |
| **专业版** | 1000 | 5 | 10000/天 |
| **商业版** | 10000 | 20 | 100000/天 |
| **企业版** | 无限制 | 无限制 | 无限制 |

### 2.5 核心API接口详解

#### 2.5.1 群组管理API

**基础CRUD操作:**

| 接口 | 方法 | URL | 描述 | 权限要求 |
|------|------|-----|------|----------|
| 创建群组 | POST | `/accounting/group` | 创建新群组 | `accounting:group:create` |
| 查询群组列表 | GET | `/accounting/group/page` | 分页查询群组 | `accounting:group:view` |
| 获取群组详情 | GET | `/accounting/group/{id}` | 获取群组详细信息 | `accounting:group:view` |
| 更新群组 | PUT | `/accounting/group/{id}` | 更新群组信息 | `accounting:group:update` |
| 删除群组 | DELETE | `/accounting/group/{id}` | 删除群组 | `accounting:group:delete` |
| 获取用户群组 | GET | `/accounting/group/user` | 获取当前用户的群组列表 | 已登录 |

**创建群组请求示例:**
```http
POST /api/v1/accounting/group
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "家庭记账群",
  "description": "家庭日常开支记录",
  "platform": "TELEGRAM",
  "platformGroupId": "*********",
  "ownerId": 1,
  "defaultCurrency": "CNY",
  "timezone": "Asia/Shanghai"
}
```

**字段说明:**
- `name`: 群组名称（必填，1-100字符）
- `description`: 群组描述（可选，最大500字符）
- `platform`: 平台类型（必填，枚举值：TELEGRAM, DISCORD, WECHAT, QQ）
- `platformGroupId`: 平台群组ID（必填，最大100字符）
- `ownerId`: 群主用户ID（必填，正整数）
- `defaultCurrency`: 默认币种（可选，3位大写字母，默认CNY）
- `timezone`: 时区（可选，默认Asia/Shanghai）

**群组详情响应示例:**
```json
{
  "code": 200,
  "data": {
    "id": 1,
    "name": "家庭记账群",
    "description": "家庭日常开支记录",
    "platform": "TELEGRAM",
    "platformGroupId": "*********",
    "ownerId": 1,
    "memberCount": 5,
    "totalTransactions": 150,
    "totalAmount": "15000.00",
    "defaultCurrency": "CNY",
    "timezone": "Asia/Shanghai",
    "createdAt": "2025-01-01T10:00:00Z",
    "settings": {
      "allowMemberInvite": true,
      "requireApproval": false,
      "autoSplit": true
    }
  }
}
```

**成员管理操作:**

| 接口 | 方法 | URL | 描述 | 权限要求 |
|------|------|-----|------|----------|
| 添加成员 | POST | `/accounting/group/{id}/member` | 添加群组成员 | `accounting:group:member:add` |
| 移除成员 | DELETE | `/accounting/group/{id}/member/{userId}` | 移除群组成员 | `accounting:group:member:remove` |
| 更新成员角色 | PUT | `/accounting/group/{id}/member/{userId}/role` | 更新成员角色 | `accounting:group:member:update` |
| 检查用户权限 | GET | `/accounting/group/{id}/permission` | 检查用户权限 | `accounting:group:checkPermission` |

**邀请码管理:**

| 接口 | 方法 | URL | 描述 | 权限要求 |
|------|------|-----|------|----------|
| 生成邀请码 | POST | `/accounting/group/{id}/invite` | 生成群组邀请码 | `accounting:group:invite:create` |
| 通过邀请码加入 | POST | `/accounting/group/join` | 通过邀请码加入群组 | 已登录 |
| 验证邀请码 | GET | `/accounting/group/invite/{code}/validate` | 验证邀请码有效性 | 无需认证 |

**添加成员示例:**
```http
POST /api/v1/accounting/group/1/member
Authorization: Bearer {token}
Content-Type: application/json

{
  "userId": 123,
  "role": "MEMBER"
}
```

**生成邀请码示例:**
```http
POST /api/v1/accounting/group/1/invite
Authorization: Bearer {token}
Content-Type: application/json

{
  "expiresIn": 86400,
  "maxUses": 10,
  "role": "MEMBER"
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "邀请码生成成功",
  "data": {
    "inviteCode": "ABC123DEF",
    "expiresAt": "2025-01-02T12:00:00Z",
    "maxUses": 10,
    "usedCount": 0
  }
}
```

### 2.3 交易管理API

#### 2.3.1 交易CRUD操作

| 接口 | 方法 | URL | 描述 |
|------|------|-----|------|
| 创建交易 | POST | `/accounting/transaction` | 创建新交易记录 |
| 查询交易列表 | GET | `/accounting/transaction/page` | 分页查询交易 |
| 获取交易详情 | GET | `/accounting/transaction/{id}` | 获取交易详细信息 |
| 更新交易 | PUT | `/accounting/transaction/{id}` | 更新交易信息 |
| 删除交易 | DELETE | `/accounting/transaction/{id}` | 删除交易记录 |

**创建交易请求示例:**
```json
POST /accounting/transaction
{
  "groupId": 1,
  "type": "EXPENSE",
  "amount": 50.00,
  "currency": "CNY",
  "description": "午餐费用",
  "category": "餐饮",
  "tags": "工作,午餐",
  "transactionDate": "2025-01-01T12:00:00",
  "location": "公司附近餐厅",
  "splitInfo": {
    "splitType": "EQUAL",
    "participants": [
      {"userId": 1, "amount": 25.00},
      {"userId": 2, "amount": 25.00}
    ]
  }
}
```

#### 2.3.2 交易统计分析

| 接口 | 方法 | URL | 描述 |
|------|------|-----|------|
| 获取交易统计 | GET | `/accounting/transaction/statistics` | 获取交易统计数据 |
| 获取月度趋势 | GET | `/accounting/transaction/monthly-trend` | 获取月度趋势分析 |
| 获取分类统计 | GET | `/accounting/transaction/category-statistics` | 获取分类统计 |
| 获取用户统计 | GET | `/accounting/transaction/user-statistics` | 获取用户统计 |

#### 2.3.3 批量操作

| 接口 | 方法 | URL | 描述 |
|------|------|-----|------|
| 批量创建交易 | POST | `/accounting/transaction/batch` | 批量创建交易 |
| 批量导入交易 | POST | `/accounting/transaction/import` | 从文件导入交易 |
| 导出交易数据 | GET | `/accounting/transaction/export` | 导出交易数据 |

### 2.4 钱包管理API

#### 2.4.1 钱包基础操作

| 接口 | 方法 | URL | 描述 |
|------|------|-----|------|
| 创建钱包 | POST | `/accounting/wallet` | 创建新钱包 |
| 查询钱包列表 | GET | `/accounting/wallet/page` | 分页查询钱包 |
| 获取钱包详情 | GET | `/accounting/wallet/{id}` | 获取钱包详细信息 |
| 更新钱包 | PUT | `/accounting/wallet/{id}` | 更新钱包信息 |
| 删除钱包 | DELETE | `/accounting/wallet/{id}` | 删除钱包 |

#### 2.4.2 钱包业务操作

| 接口 | 方法 | URL | 描述 |
|------|------|-----|------|
| 获取群组钱包 | GET | `/accounting/wallet/group/{groupId}` | 获取群组所有钱包 |
| 获取用户钱包 | GET | `/accounting/wallet/user/{userId}` | 获取用户钱包列表 |
| 钱包转账 | POST | `/accounting/wallet/transfer` | 钱包间转账 |
| 更新余额 | PUT | `/accounting/wallet/{id}/balance` | 更新钱包余额 |
| 获取余额汇总 | GET | `/accounting/wallet/summary` | 获取余额汇总 |

### 2.5 分类管理API

| 接口 | 方法 | URL | 描述 |
|------|------|-----|------|
| 创建分类 | POST | `/accounting/category` | 创建新分类 |
| 获取分类树 | GET | `/accounting/category/tree` | 获取分类树结构 |
| 获取分类列表 | GET | `/accounting/category/page` | 分页查询分类 |
| 更新分类 | PUT | `/accounting/category/{id}` | 更新分类信息 |
| 删除分类 | DELETE | `/accounting/category/{id}` | 删除分类 |
| 获取推荐分类 | GET | `/accounting/category/recommend` | 获取推荐分类 |

### 2.6 标签管理API

| 接口 | 方法 | URL | 描述 |
|------|------|-----|------|
| 创建标签 | POST | `/accounting/tag` | 创建新标签 |
| 查询标签列表 | GET | `/accounting/tag/page` | 分页查询标签 |
| 获取标签详情 | GET | `/accounting/tag/{id}` | 获取标签详细信息 |
| 更新标签 | PUT | `/accounting/tag/{id}` | 更新标签信息 |
| 删除标签 | DELETE | `/accounting/tag/{id}` | 删除标签 |
| 获取热门标签 | GET | `/accounting/tag/popular` | 获取热门标签 |

### 2.7 债务管理API

| 接口 | 方法 | URL | 描述 |
|------|------|-----|------|
| 创建债务记录 | POST | `/accounting/debt` | 创建债务记录 |
| 查询债务列表 | GET | `/accounting/debt/page` | 分页查询债务 |
| 获取债务详情 | GET | `/accounting/debt/{id}` | 获取债务详细信息 |
| 更新债务 | PUT | `/accounting/debt/{id}` | 更新债务信息 |
| 删除债务 | DELETE | `/accounting/debt/{id}` | 删除债务记录 |
| 记录还款 | POST | `/accounting/debt/{id}/payment` | 记录还款 |
| 结算债务 | POST | `/accounting/debt/{id}/settle` | 结算债务 |

### 2.8 报表统计API

| 接口 | 方法 | URL | 描述 |
|------|------|-----|------|
| 获取财务概览 | GET | `/accounting/report/overview` | 获取财务概览 |
| 获取收支报表 | GET | `/accounting/report/income-expense` | 获取收支报表 |
| 获取分类报表 | GET | `/accounting/report/category` | 获取分类报表 |
| 获取趋势分析 | GET | `/accounting/report/trend` | 获取趋势分析 |
| 获取对比分析 | GET | `/accounting/report/comparison` | 获取对比分析 |
| 生成自定义报表 | POST | `/accounting/report/custom` | 生成自定义报表 |

### 2.9 通知服务API

| 接口 | 方法 | URL | 描述 |
|------|------|-----|------|
| 发送通知 | POST | `/notification/send` | 发送通知消息 |
| 批量发送通知 | POST | `/notification/batch-send` | 批量发送通知 |
| 获取通知历史 | GET | `/notification/history` | 获取通知历史 |
| 获取通知模板 | GET | `/notification/template` | 获取通知模板 |
| 创建通知模板 | POST | `/notification/template` | 创建通知模板 |

### 2.10 文件存储API

| 接口 | 方法 | URL | 描述 |
|------|------|-----|------|
| 上传文件 | POST | `/file/upload` | 上传文件 |
| 下载文件 | GET | `/file/download/{id}` | 下载文件 |
| 获取文件信息 | GET | `/file/{id}` | 获取文件信息 |
| 删除文件 | DELETE | `/file/{id}` | 删除文件 |
| 获取文件列表 | GET | `/file/page` | 分页查询文件 |

### 2.11 错误码定义

| 错误码 | 描述 | 说明 |
|--------|------|------|
| 200 | 成功 | 请求处理成功 |
| 400 | 请求参数错误 | 请求参数格式或内容错误 |
| 401 | 未授权 | 未登录或token无效 |
| 403 | 禁止访问 | 权限不足 |
| 404 | 资源不存在 | 请求的资源不存在 |
| 409 | 资源冲突 | 资源已存在或状态冲突 |
| 429 | 请求过于频繁 | 触发限流 |
| 500 | 服务器内部错误 | 服务器处理异常 |

**错误响应示例:**
```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": {
    "field": "amount",
    "error": "金额必须大于0"
  },
  "timestamp": "2025-01-01T12:00:00Z"
}
```

---

## 3. 数据模型文档

### 3.1 核心实体模型

#### 3.1.1 群组相关模型

**GroupCreateReq - 群组创建请求**
```typescript
interface GroupCreateReq {
  name: string;                    // 群组名称 (必填, 最大100字符)
  description?: string;            // 群组描述 (可选, 最大500字符)
  platform: PlatformType;         // 平台类型 (必填)
  platformGroupId: string;        // 平台群组ID (必填, 最大100字符)
  ownerId: number;                 // 群主用户ID (必填)
  defaultCurrency?: string;        // 默认币种 (可选, 默认CNY)
  timezone?: string;               // 时区 (可选, 默认Asia/Shanghai)
}
```

**GroupDetailResp - 群组详情响应**
```typescript
interface GroupDetailResp {
  id: number;                      // 群组ID
  name: string;                    // 群组名称
  description?: string;            // 群组描述
  platform: PlatformType;         // 平台类型
  platformGroupId: string;        // 平台群组ID
  ownerId: number;                 // 群主用户ID
  memberCount: number;             // 成员数量
  totalTransactions: number;       // 总交易数
  totalAmount: string;             // 总金额
  defaultCurrency: string;         // 默认币种
  timezone: string;                // 时区
  isActive: boolean;               // 是否激活
  createdAt: string;               // 创建时间
  updatedAt: string;               // 更新时间
  settings: GroupSettings;         // 群组设置
  members?: GroupMember[];         // 成员列表
  statistics?: GroupStatistics;    // 统计信息
}
```

**GroupSettings - 群组设置**
```typescript
interface GroupSettings {
  allowMemberInvite: boolean;      // 允许成员邀请
  requireApproval: boolean;        // 需要审批
  autoSplit: boolean;              // 自动分摊
  defaultSplitType: SplitType;     // 默认分摊类型
  maxMembers: number;              // 最大成员数
  allowGuestView: boolean;         // 允许访客查看
  enableNotification: boolean;     // 启用通知
  notificationChannels: string[];  // 通知渠道
}
```

#### 3.1.2 交易相关模型

**TransactionCreateReq - 交易创建请求**
```typescript
interface TransactionCreateReq {
  groupId: number;                 // 群组ID (必填)
  type: TransactionType;           // 交易类型 (必填)
  amount: number;                  // 金额 (必填, 最小0.01)
  currency: string;                // 币种 (必填, 3位大写字母)
  description: string;             // 描述 (必填, 最大200字符)
  category?: string;               // 分类 (可选, 最大50字符)
  tags?: string;                   // 标签 (可选, 最大200字符)
  transactionDate?: string;        // 交易时间 (可选, ISO格式)
  location?: string;               // 位置 (可选, 最大100字符)
  attachments?: string[];          // 附件 (可选)
  splitInfo?: SplitInfo;           // 分摊信息 (可选)
  recurringInfo?: RecurringInfo;   // 循环信息 (可选)
}
```

**TransactionDetailResp - 交易详情响应**
```typescript
interface TransactionDetailResp {
  id: number;                      // 交易ID
  groupId: number;                 // 群组ID
  userId: number;                  // 用户ID
  type: TransactionType;           // 交易类型
  amount: number;                  // 金额
  currency: string;                // 币种
  description: string;             // 描述
  category?: string;               // 分类
  tags?: string;                   // 标签
  transactionDate: string;         // 交易时间
  location?: string;               // 位置
  attachments?: FileInfo[];        // 附件信息
  splitInfo?: SplitInfo;           // 分摊信息
  recurringInfo?: RecurringInfo;   // 循环信息
  status: string;                  // 状态
  createdAt: string;               // 创建时间
  updatedAt: string;               // 更新时间
  createdBy: UserInfo;             // 创建者信息
}
```

#### 3.1.3 钱包相关模型

**WalletCreateReq - 钱包创建请求**
```typescript
interface WalletCreateReq {
  groupId: number;                 // 群组ID (必填)
  currency: string;                // 币种 (必填, 3位大写字母)
  balance?: number;                // 初始余额 (可选, 默认0.00)
  remark?: string;                 // 备注 (可选, 最大200字符)
}
```

**WalletDetailResp - 钱包详情响应**
```typescript
interface WalletDetailResp {
  id: number;                      // 钱包ID
  groupId: number;                 // 群组ID
  currency: string;                // 币种
  balance: number;                 // 余额
  frozenAmount: number;            // 冻结金额
  availableAmount: number;         // 可用金额
  remark?: string;                 // 备注
  isActive: boolean;               // 是否激活
  createdAt: string;               // 创建时间
  updatedAt: string;               // 更新时间
  transactions?: TransactionSummary[]; // 相关交易
}
```

### 3.2 枚举类型定义

#### 3.2.1 平台类型 (PlatformType)
```typescript
enum PlatformType {
  TELEGRAM = 'TELEGRAM',          // Telegram
  DISCORD = 'DISCORD',            // Discord
  WECHAT = 'WECHAT',              // 微信
  QQ = 'QQ',                      // QQ
  DINGTALK = 'DINGTALK',          // 钉钉
  FEISHU = 'FEISHU'               // 飞书
}
```

#### 3.2.2 交易类型 (TransactionType)
```typescript
enum TransactionType {
  INCOME = 'INCOME',              // 收入
  EXPENSE = 'EXPENSE',            // 支出
  TRANSFER = 'TRANSFER'           // 转账
}
```

#### 3.2.3 群组角色 (GroupRole)
```typescript
enum GroupRole {
  OWNER = 'OWNER',                // 群主
  ADMIN = 'ADMIN',                // 管理员
  ACCOUNTANT = 'ACCOUNTANT',      // 会计
  MEMBER = 'MEMBER',              // 成员
  AUDITOR = 'AUDITOR'             // 审计员
}
```

#### 3.2.4 分摊类型 (SplitType)
```typescript
enum SplitType {
  EQUAL = 'EQUAL',                // 平均分摊
  RATIO = 'RATIO',                // 按比例分摊
  AMOUNT = 'AMOUNT',              // 按金额分摊
  CUSTOM = 'CUSTOM'               // 自定义分摊
}
```

#### 3.2.5 订阅套餐 (SubscriptionPlan)
```typescript
enum SubscriptionPlan {
  TRIAL = 'TRIAL',                // 试用版
  PRO = 'PRO',                    // 专业版
  BUSINESS = 'BUSINESS',          // 商业版
  ENTERPRISE = 'ENTERPRISE'       // 企业版
}
```

### 3.3 复杂数据结构

#### 3.3.1 分摊信息 (SplitInfo)
```typescript
interface SplitInfo {
  splitType: SplitType;            // 分摊类型
  participants: SplitParticipant[]; // 参与者列表
  completed: boolean;              // 是否完成
  createdAt: string;               // 创建时间
  completedAt?: string;            // 完成时间
}

interface SplitParticipant {
  userId: number;                  // 用户ID
  userName: string;                // 用户名
  amount: number;                  // 分摊金额
  ratio?: number;                  // 分摊比例 (百分比)
  paid: boolean;                   // 是否已支付
  paidAt?: string;                 // 支付时间
}
```

#### 3.3.2 循环交易信息 (RecurringInfo)
```typescript
interface RecurringInfo {
  enabled: boolean;                // 是否启用
  frequency: RecurringFrequency;   // 频率
  interval: number;                // 间隔
  endDate?: string;                // 结束日期
  maxOccurrences?: number;         // 最大次数
  nextDate: string;                // 下次执行日期
  executedCount: number;           // 已执行次数
}

enum RecurringFrequency {
  DAILY = 'DAILY',                 // 每日
  WEEKLY = 'WEEKLY',               // 每周
  MONTHLY = 'MONTHLY',             // 每月
  YEARLY = 'YEARLY'                // 每年
}
```

#### 3.3.3 文件信息 (FileInfo)
```typescript
interface FileInfo {
  id: number;                      // 文件ID
  fileName: string;                // 文件名
  fileSize: number;                // 文件大小 (字节)
  fileType: string;                // 文件类型
  url: string;                     // 访问URL
  thumbnailUrl?: string;           // 缩略图URL
  uploadedAt: string;              // 上传时间
  uploadedBy: number;              // 上传者ID
}
```

#### 3.3.4 统计信息模型

**GroupStatistics - 群组统计**
```typescript
interface GroupStatistics {
  totalMembers: number;            // 总成员数
  activeMembers: number;           // 活跃成员数
  totalTransactions: number;       // 总交易数
  totalIncome: number;             // 总收入
  totalExpense: number;            // 总支出
  netAmount: number;               // 净额
  avgTransactionAmount: number;    // 平均交易金额
  topCategories: CategoryStat[];   // 热门分类
  monthlyTrend: MonthlyTrendData[]; // 月度趋势
}
```

**TransactionStatistics - 交易统计**
```typescript
interface TransactionStatistics {
  totalCount: number;              // 总数量
  totalAmount: number;             // 总金额
  avgAmount: number;               // 平均金额
  maxAmount: number;               // 最大金额
  minAmount: number;               // 最小金额
  byType: TypeStatistics[];        // 按类型统计
  byCategory: CategoryStatistics[]; // 按分类统计
  byUser: UserStatistics[];        // 按用户统计
  byMonth: MonthlyStatistics[];    // 按月统计
}
```

### 3.4 查询参数模型

#### 3.4.1 分页查询基础模型
```typescript
interface PageQuery {
  page: number;                    // 页码 (从1开始)
  size: number;                    // 每页大小 (默认10)
  sort?: string[];                 // 排序字段
}

interface PageResp<T> {
  total: number;                   // 总记录数
  list: T[];                       // 数据列表
  page: number;                    // 当前页码
  size: number;                    // 每页大小
  pages: number;                   // 总页数
}
```

#### 3.4.2 群组查询参数
```typescript
interface GroupQuery extends PageQuery {
  name?: string;                   // 群组名称 (模糊查询)
  platform?: PlatformType;        // 平台类型
  ownerId?: number;                // 群主ID
  isActive?: boolean;              // 是否激活
  createdDateStart?: string;       // 创建开始日期
  createdDateEnd?: string;         // 创建结束日期
}
```

#### 3.4.3 交易查询参数
```typescript
interface TransactionQuery extends PageQuery {
  groupId?: number;                // 群组ID
  userId?: number;                 // 用户ID
  type?: TransactionType;          // 交易类型
  category?: string;               // 分类
  tags?: string;                   // 标签
  amountMin?: number;              // 最小金额
  amountMax?: number;              // 最大金额
  currency?: string;               // 币种
  dateStart?: string;              // 开始日期
  dateEnd?: string;                // 结束日期
  keyword?: string;                // 关键词搜索
}
```

### 3.5 数据验证规则

#### 3.5.1 字段验证规则
```typescript
interface ValidationRules {
  // 群组相关
  groupName: {
    required: true,
    maxLength: 100,
    pattern: /^[\u4e00-\u9fa5a-zA-Z0-9\s]+$/
  },

  // 交易相关
  amount: {
    required: true,
    min: 0.01,
    max: 999999999.99,
    decimal: 2
  },

  currency: {
    required: true,
    pattern: /^[A-Z]{3}$/,
    enum: ['CNY', 'USD', 'EUR', 'JPY', 'GBP']
  },

  // 通用规则
  description: {
    required: true,
    maxLength: 200,
    minLength: 1
  }
}
```

---

## 4. 前端集成指南

### 4.1 认证授权机制详解

#### 4.1.1 SaToken框架介绍

本系统使用 **SaToken** 作为认证授权框架，具有以下特点：
- **轻量级**: 零依赖，简单易用
- **功能丰富**: 登录认证、权限验证、Session会话、踢人下线、模拟他人账号等
- **高性能**: 基于Redis的分布式会话管理
- **安全性**: 支持JWT、自动续签、并发登录控制等

#### 4.1.2 认证流程详解

**完整登录流程:**
```typescript
// 1. 获取RSA公钥（用于密码加密）
const getPublicKey = async () => {
  const response = await fetch('/api/v1/auth/public-key');
  const result = await response.json();
  return result.data.publicKey;
};

// 2. 加密密码
const encryptPassword = (password: string, publicKey: string) => {
  // 使用RSA公钥加密密码
  const encrypt = new JSEncrypt();
  encrypt.setPublicKey(publicKey);
  return encrypt.encrypt(password);
};

// 3. 执行登录
const login = async (username: string, password: string) => {
  try {
    // 获取公钥并加密密码
    const publicKey = await getPublicKey();
    const encryptedPassword = encryptPassword(password, publicKey);

    // 发送登录请求
    const response = await fetch('/api/v1/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: username,
        password: encryptedPassword,
        clientId: 'web_client',
        authType: 'ACCOUNT',
        captcha: captchaCode, // 如果启用验证码
        uuid: captchaUuid     // 验证码标识
      })
    });

    const result = await response.json();

    if (result.code === 200) {
      // 保存认证信息
      const { token, refreshToken, userInfo } = result.data;
      localStorage.setItem('satoken', token);
      localStorage.setItem('refreshToken', refreshToken);
      localStorage.setItem('userInfo', JSON.stringify(userInfo));

      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('登录失败:', error);
    throw error;
  }
};
```

#### 4.1.3 多种认证方式

**1. 账号密码登录**
```typescript
const accountLogin = {
  username: "admin",
  password: "encrypted_password",
  clientId: "web_client",
  authType: "ACCOUNT",
  captcha: "1234",
  uuid: "captcha_uuid"
};
```

**2. 手机号登录**
```typescript
const phoneLogin = {
  phone: "***********",
  code: "123456",
  clientId: "mobile_client",
  authType: "PHONE"
};
```

**3. 邮箱登录**
```typescript
const emailLogin = {
  email: "<EMAIL>",
  code: "123456",
  clientId: "web_client",
  authType: "EMAIL"
};
```

#### 4.1.4 Token管理最佳实践

**Token存储管理:**
```typescript
class TokenManager {
  private static readonly TOKEN_KEY = 'satoken';
  private static readonly REFRESH_TOKEN_KEY = 'refreshToken';
  private static readonly USER_INFO_KEY = 'userInfo';

  // 设置Token（支持过期时间）
  static setToken(token: string, expiresIn?: number): void {
    const tokenData = {
      token,
      expiresAt: expiresIn ? Date.now() + expiresIn * 1000 : null
    };
    localStorage.setItem(this.TOKEN_KEY, JSON.stringify(tokenData));
  }

  // 获取Token（自动检查过期）
  static getToken(): string | null {
    const tokenStr = localStorage.getItem(this.TOKEN_KEY);
    if (!tokenStr) return null;

    try {
      const tokenData = JSON.parse(tokenStr);

      // 检查是否过期
      if (tokenData.expiresAt && Date.now() > tokenData.expiresAt) {
        this.removeToken();
        return null;
      }

      return tokenData.token;
    } catch {
      this.removeToken();
      return null;
    }
  }

  // 检查Token是否即将过期（提前5分钟刷新）
  static isTokenExpiringSoon(): boolean {
    const tokenStr = localStorage.getItem(this.TOKEN_KEY);
    if (!tokenStr) return true;

    try {
      const tokenData = JSON.parse(tokenStr);
      if (!tokenData.expiresAt) return false;

      const fiveMinutes = 5 * 60 * 1000;
      return Date.now() > (tokenData.expiresAt - fiveMinutes);
    } catch {
      return true;
    }
  }

  // 清除所有认证信息
  static removeToken(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    localStorage.removeItem(this.USER_INFO_KEY);
  }
}
```

**HTTP请求拦截器:**
```typescript
// 创建带认证的HTTP客户端
class ApiClient {
  private static baseURL = '/api/v1';

  static async request(url: string, options: RequestInit = {}): Promise<Response> {
    const token = TokenManager.getToken();

    // 检查token是否即将过期，自动刷新
    if (token && TokenManager.isTokenExpiringSoon()) {
      try {
        await this.refreshToken();
      } catch (error) {
        // 刷新失败，跳转到登录页
        this.redirectToLogin();
        throw error;
      }
    }

    const headers = {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
      ...options.headers
    };

    const response = await fetch(`${this.baseURL}${url}`, {
      ...options,
      headers
    });

    // 处理401未授权响应
    if (response.status === 401) {
      TokenManager.removeToken();
      this.redirectToLogin();
      throw new Error('Unauthorized');
    }

    return response;
  }

  // 自动刷新Token
  private static async refreshToken(): Promise<void> {
    const refreshToken = localStorage.getItem('refreshToken');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await fetch('/api/v1/auth/refresh', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${refreshToken}`,
        'Content-Type': 'application/json'
      }
    });

    const result = await response.json();

    if (result.code === 200) {
      const { token, refreshToken: newRefreshToken, expiresIn } = result.data;
      TokenManager.setToken(token, expiresIn);
      localStorage.setItem('refreshToken', newRefreshToken);
    } else {
      TokenManager.removeToken();
      throw new Error(result.message || 'Token refresh failed');
    }
  }

  private static redirectToLogin(): void {
    window.location.href = '/login';
  }
}
```

**使用示例:**
```typescript
// GET请求
const getUserInfo = async () => {
  const response = await ApiClient.request('/user/info');
  return response.json();
};

// POST请求
const createGroup = async (groupData: any) => {
  const response = await ApiClient.request('/accounting/group', {
    method: 'POST',
    body: JSON.stringify(groupData)
  });
  return response.json();
};
```

#### 4.1.5 权限验证机制

**前端权限检查:**
```typescript
class PermissionManager {
  // 检查用户是否有指定权限
  static hasPermission(permission: string): boolean {
    const userInfo = TokenManager.getUserInfo();
    if (!userInfo || !userInfo.permissions) return false;

    return userInfo.permissions.includes(permission);
  }

  // 检查用户是否有任一权限
  static hasAnyPermission(permissions: string[]): boolean {
    return permissions.some(permission => this.hasPermission(permission));
  }

  // 检查用户是否有所有权限
  static hasAllPermissions(permissions: string[]): boolean {
    return permissions.every(permission => this.hasPermission(permission));
  }

  // 检查用户角色
  static hasRole(role: string): boolean {
    const userInfo = TokenManager.getUserInfo();
    if (!userInfo || !userInfo.roles) return false;

    return userInfo.roles.includes(role);
  }
}

// 权限装饰器（用于Vue组件）
const requirePermission = (permission: string) => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;

    descriptor.value = function (...args: any[]) {
      if (!PermissionManager.hasPermission(permission)) {
        throw new Error(`权限不足: ${permission}`);
      }
      return originalMethod.apply(this, args);
    };
  };
};
```

**Vue路由守卫:**
```typescript
// router/index.ts
import { PermissionManager } from '@/utils/permission';

router.beforeEach((to, from, next) => {
  const token = TokenManager.getToken();

  // 检查是否需要登录
  if (to.meta.requiresAuth && !token) {
    next('/login');
    return;
  }

  // 检查权限
  if (to.meta.permissions) {
    const permissions = Array.isArray(to.meta.permissions)
      ? to.meta.permissions
      : [to.meta.permissions];

    if (!PermissionManager.hasAnyPermission(permissions)) {
      next('/403'); // 无权限页面
      return;
    }
  }

  next();
});
```
    // 刷新失败，跳转到登录页
    localStorage.removeItem('satoken');
    localStorage.removeItem('refreshToken');
    window.location.href = '/login';
  }
};
```

#### 4.1.2 权限检查

**角色权限检查:**
```typescript
// 检查用户权限
const checkPermission = (permission: string): boolean => {
  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  return userInfo.permissions?.includes(permission) || false;
};

// 检查群组角色权限
const checkGroupRole = (groupId: number, requiredRole: GroupRole): boolean => {
  const userGroups = JSON.parse(localStorage.getItem('userGroups') || '[]');
  const group = userGroups.find(g => g.groupId === groupId);

  if (!group) return false;

  const roleHierarchy = {
    [GroupRole.OWNER]: 5,
    [GroupRole.ADMIN]: 4,
    [GroupRole.ACCOUNTANT]: 3,
    [GroupRole.MEMBER]: 2,
    [GroupRole.AUDITOR]: 1
  };

  return roleHierarchy[group.role] >= roleHierarchy[requiredRole];
};
```

### 4.2 分页查询标准格式

#### 4.2.1 分页参数
```typescript
interface PaginationParams {
  page: number;        // 页码，从1开始
  size: number;        // 每页大小，默认10
  sort?: string[];     // 排序字段，格式: ['field,asc', 'field2,desc']
}

// 使用示例
const getTransactions = async (params: TransactionQuery & PaginationParams) => {
  const queryString = new URLSearchParams({
    page: params.page.toString(),
    size: params.size.toString(),
    ...(params.sort && { sort: params.sort.join(',') }),
    ...(params.groupId && { groupId: params.groupId.toString() }),
    ...(params.type && { type: params.type }),
    ...(params.dateStart && { dateStart: params.dateStart }),
    ...(params.dateEnd && { dateEnd: params.dateEnd })
  }).toString();

  return apiRequest(`/api/accounting/transaction/page?${queryString}`);
};
```

#### 4.2.2 排序格式
```typescript
// 排序示例
const sortOptions = [
  'createdAt,desc',      // 按创建时间降序
  'amount,asc',          // 按金额升序
  'transactionDate,desc' // 按交易时间降序
];

// 多字段排序
const multiSort = [
  'type,asc',           // 先按类型升序
  'amount,desc'         // 再按金额降序
];
```

#### 4.2.3 筛选格式
```typescript
// 高级筛选示例
const advancedFilter = {
  // 日期范围筛选
  dateRange: {
    start: '2025-01-01',
    end: '2025-01-31'
  },

  // 金额范围筛选
  amountRange: {
    min: 0,
    max: 1000
  },

  // 多选筛选
  categories: ['餐饮', '交通', '购物'],
  types: [TransactionType.EXPENSE],

  // 关键词搜索
  keyword: '午餐'
};
```

### 4.3 文件上传下载处理

#### 4.3.1 文件上传
```typescript
// 单文件上传
const uploadFile = async (file: File): Promise<FileInfo> => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await fetch('/api/file/upload', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('satoken')}`
    },
    body: formData
  });

  const result = await response.json();
  if (result.code === 200) {
    return result.data;
  } else {
    throw new Error(result.message);
  }
};

// 多文件上传
const uploadMultipleFiles = async (files: FileList): Promise<FileInfo[]> => {
  const uploadPromises = Array.from(files).map(file => uploadFile(file));
  return Promise.all(uploadPromises);
};

// 带进度的文件上传
const uploadFileWithProgress = (
  file: File,
  onProgress: (progress: number) => void
): Promise<FileInfo> => {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    const formData = new FormData();
    formData.append('file', file);

    xhr.upload.addEventListener('progress', (e) => {
      if (e.lengthComputable) {
        const progress = (e.loaded / e.total) * 100;
        onProgress(progress);
      }
    });

    xhr.addEventListener('load', () => {
      if (xhr.status === 200) {
        const result = JSON.parse(xhr.responseText);
        resolve(result.data);
      } else {
        reject(new Error('Upload failed'));
      }
    });

    xhr.addEventListener('error', () => reject(new Error('Upload error')));

    xhr.open('POST', '/api/file/upload');
    xhr.setRequestHeader('Authorization', `Bearer ${localStorage.getItem('satoken')}`);
    xhr.send(formData);
  });
};
```

#### 4.3.2 文件下载
```typescript
// 文件下载
const downloadFile = async (fileId: number, fileName: string) => {
  const response = await fetch(`/api/file/download/${fileId}`, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('satoken')}`
    }
  });

  if (response.ok) {
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  } else {
    throw new Error('Download failed');
  }
};

// 批量下载
const downloadMultipleFiles = async (fileIds: number[]) => {
  const response = await fetch('/api/file/batch-download', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('satoken')}`
    },
    body: JSON.stringify({ fileIds })
  });

  if (response.ok) {
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'files.zip';
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }
};
```

### 4.4 WebSocket连接和实时通知

#### 4.4.1 WebSocket连接
```typescript
class NotificationWebSocket {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 5000;

  connect() {
    const token = localStorage.getItem('satoken');
    const wsUrl = `ws://localhost:8080/ws/notification?token=${token}`;

    this.ws = new WebSocket(wsUrl);

    this.ws.onopen = () => {
      console.log('WebSocket connected');
      this.reconnectAttempts = 0;
    };

    this.ws.onmessage = (event) => {
      const notification = JSON.parse(event.data);
      this.handleNotification(notification);
    };

    this.ws.onclose = () => {
      console.log('WebSocket disconnected');
      this.reconnect();
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  }

  private reconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(`Reconnecting... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect();
      }, this.reconnectInterval);
    }
  }

  private handleNotification(notification: any) {
    // 处理不同类型的通知
    switch (notification.type) {
      case 'TRANSACTION_CREATED':
        this.showTransactionNotification(notification);
        break;
      case 'GROUP_MEMBER_ADDED':
        this.showMemberNotification(notification);
        break;
      case 'DEBT_REMINDER':
        this.showDebtReminder(notification);
        break;
      default:
        this.showGenericNotification(notification);
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}

// 使用示例
const notificationWS = new NotificationWebSocket();
notificationWS.connect();
```

#### 4.4.2 实时通知处理
```typescript
// 通知管理器
class NotificationManager {
  private notifications: Notification[] = [];
  private maxNotifications = 5;

  showNotification(notification: NotificationData) {
    // 添加到通知列表
    this.notifications.unshift(notification);

    // 限制通知数量
    if (this.notifications.length > this.maxNotifications) {
      this.notifications = this.notifications.slice(0, this.maxNotifications);
    }

    // 显示浏览器通知
    if (Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        tag: notification.id
      });
    }

    // 更新UI
    this.updateNotificationUI();
  }

  markAsRead(notificationId: string) {
    const notification = this.notifications.find(n => n.id === notificationId);
    if (notification) {
      notification.read = true;
      this.updateNotificationUI();

      // 发送已读状态到服务器
      fetch(`/api/notification/${notificationId}/read`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('satoken')}`
        }
      });
    }
  }

  private updateNotificationUI() {
    // 更新通知图标上的未读数量
    const unreadCount = this.notifications.filter(n => !n.read).length;
    const badge = document.querySelector('.notification-badge');
    if (badge) {
      badge.textContent = unreadCount > 0 ? unreadCount.toString() : '';
      badge.style.display = unreadCount > 0 ? 'block' : 'none';
    }
  }
}
```

---

## 5. 限流控制和分页机制

### 5.1 API限流详解

#### 5.1.1 限流规则说明

系统实施多层限流保护，确保API服务的稳定性和公平性：

**全局限流规则:**
```yaml
# 默认限流配置
api:
  rate-limit:
    enabled: true
    default-rule:
      window-size: 60        # 时间窗口（秒）
      max-requests: 100      # 最大请求数
      enable-ip-limit: true  # 启用IP限流
      enable-user-limit: true # 启用用户限流
      enable-api-key-limit: true # 启用API密钥限流
```

**分层限流策略:**

| 限流层级 | 限制类型 | 默认配置 | 说明 |
|----------|----------|----------|------|
| **IP层** | 基于客户端IP | 100次/分钟 | 防止单个IP过度请求 |
| **用户层** | 基于登录用户 | 200次/分钟 | 基于用户身份的限制 |
| **API密钥层** | 基于API密钥 | 1000次/小时 | 第三方应用集成限制 |
| **接口层** | 特定接口 | 自定义 | 敏感接口特殊限制 |

#### 5.1.2 特殊接口限流

**验证码接口限流:**
```typescript
// 邮箱验证码限流规则
const emailCaptchaLimits = {
  sameEmailSameTemplate: {
    '1min': 2,    // 同邮箱同模板1分钟2条
    '1hour': 8,   // 同邮箱同模板1小时8条
    '24hour': 20  // 同邮箱同模板24小时20条
  },
  sameEmailAllTemplates: {
    '24hour': 100 // 同邮箱所有模板24小时100条
  },
  sameIP: {
    '1min': 30    // 同IP每分钟30条
  }
};

// 短信验证码限流规则
const smsCaptchaLimits = {
  samePhoneSameTemplate: {
    '1min': 2,    // 同手机号同模板1分钟2条
    '1hour': 8,   // 同手机号同模板1小时8条
    '24hour': 20  // 同手机号同模板24小时20条
  },
  samePhoneAllTemplates: {
    '24hour': 100 // 同手机号所有模板24小时100条
  },
  sameIP: {
    '1min': 30    // 同IP每分钟30条
  }
};
```

#### 5.1.3 限流响应处理

**限流响应格式:**
```json
{
  "code": 429,
  "message": "请求过于频繁，请稍后再试",
  "data": {
    "retryAfter": 60,
    "limit": 100,
    "remaining": 0,
    "resetTime": "2025-01-01T12:01:00Z",
    "limitType": "IP_LIMIT"
  },
  "timestamp": "2025-01-01T12:00:00Z"
}
```

**前端限流处理:**
```typescript
class RateLimitHandler {
  private static retryQueue: Map<string, number> = new Map();

  // 处理限流响应
  static async handleRateLimit(response: Response, url: string): Promise<void> {
    if (response.status === 429) {
      const result = await response.json();
      const retryAfter = result.data?.retryAfter || 60;

      // 记录重试时间
      this.retryQueue.set(url, Date.now() + retryAfter * 1000);

      // 显示用户友好的提示
      this.showRateLimitMessage(result.message, retryAfter);

      throw new RateLimitError(result.message, retryAfter);
    }
  }

  // 检查是否可以发起请求
  static canMakeRequest(url: string): boolean {
    const retryTime = this.retryQueue.get(url);
    if (!retryTime) return true;

    if (Date.now() >= retryTime) {
      this.retryQueue.delete(url);
      return true;
    }

    return false;
  }

  // 显示限流提示
  private static showRateLimitMessage(message: string, retryAfter: number): void {
    const minutes = Math.ceil(retryAfter / 60);
    const displayMessage = `${message}，请等待 ${minutes} 分钟后重试`;

    // 这里可以集成你的通知组件
    console.warn(displayMessage);
  }
}

// 限流错误类
class RateLimitError extends Error {
  constructor(message: string, public retryAfter: number) {
    super(message);
    this.name = 'RateLimitError';
  }
}
```

### 5.2 分页机制详解

#### 5.2.1 分页参数标准

**标准分页参数:**
```typescript
interface PageQuery {
  page: number;     // 页码，从1开始
  size: number;     // 每页条数，范围1-1000
  sort?: string;    // 排序字段
  order?: 'asc' | 'desc'; // 排序方向
}

// 使用示例
const pageQuery: PageQuery = {
  page: 1,
  size: 20,
  sort: 'createTime',
  order: 'desc'
};
```

**分页请求示例:**
```http
GET /api/v1/accounting/transaction/page?page=1&size=20&sort=createTime&order=desc
Authorization: Bearer {token}
```

#### 5.2.2 分页响应格式

**标准分页响应:**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "total": 1250,           // 总记录数
    "list": [                // 当前页数据
      {
        "id": 1,
        "amount": 100.00,
        "description": "午餐费用"
      }
    ],
    "page": 1,               // 当前页码
    "size": 20,              // 每页条数
    "pages": 63,             // 总页数
    "hasNext": true,         // 是否有下一页
    "hasPrevious": false     // 是否有上一页
  },
  "timestamp": "2025-01-01T12:00:00Z"
}
```

#### 5.2.3 前端分页组件

**Vue分页组件示例:**
```typescript
// PaginationComponent.vue
<template>
  <div class="pagination-wrapper">
    <div class="pagination-info">
      共 {{ total }} 条记录，第 {{ currentPage }} / {{ totalPages }} 页
    </div>

    <div class="pagination-controls">
      <button
        :disabled="!hasPrevious"
        @click="goToPage(currentPage - 1)"
      >
        上一页
      </button>

      <select v-model="pageSize" @change="onPageSizeChange">
        <option value="10">10条/页</option>
        <option value="20">20条/页</option>
        <option value="50">50条/页</option>
        <option value="100">100条/页</option>
      </select>

      <button
        :disabled="!hasNext"
        @click="goToPage(currentPage + 1)"
      >
        下一页
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface PaginationProps {
  total: number;
  currentPage: number;
  pageSize: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

interface PaginationEmits {
  (e: 'page-change', page: number): void;
  (e: 'size-change', size: number): void;
}

const props = defineProps<PaginationProps>();
const emit = defineEmits<PaginationEmits>();

const totalPages = computed(() => Math.ceil(props.total / props.pageSize));

const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    emit('page-change', page);
  }
};

const onPageSizeChange = () => {
  emit('size-change', props.pageSize);
  emit('page-change', 1); // 重置到第一页
};
</script>
```

---

## 6. 开发规范和示例

### 6.1 API调用标准代码示例

#### 5.1.1 TypeScript/JavaScript示例

**基础API客户端:**
```typescript
// api-client.ts
class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.token = localStorage.getItem('satoken');
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      ...(this.token && { 'Authorization': `Bearer ${this.token}` }),
      ...options.headers
    };

    try {
      const response = await fetch(url, {
        ...options,
        headers
      });

      const data = await response.json();

      if (!response.ok) {
        throw new ApiError(data.code, data.message, data);
      }

      return data;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, 'Network error', error);
    }
  }

  // GET请求
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    const queryString = params ? `?${new URLSearchParams(params).toString()}` : '';
    return this.request<T>(`${endpoint}${queryString}`);
  }

  // POST请求
  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  // PUT请求
  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  // DELETE请求
  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

// 错误类定义
class ApiError extends Error {
  constructor(
    public code: number,
    public message: string,
    public data?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// 响应类型定义
interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
  timestamp: string;
}
```

**业务API封装:**
```typescript
// group-api.ts
class GroupApi {
  constructor(private client: ApiClient) {}

  // 创建群组
  async createGroup(data: GroupCreateReq): Promise<GroupDetailResp> {
    const response = await this.client.post<GroupDetailResp>('/accounting/group', data);
    return response.data;
  }

  // 获取群组列表
  async getGroups(query: GroupQuery): Promise<PageResp<GroupListResp>> {
    const response = await this.client.get<PageResp<GroupListResp>>('/accounting/group/page', query);
    return response.data;
  }

  // 获取群组详情
  async getGroupDetail(id: number): Promise<GroupDetailResp> {
    const response = await this.client.get<GroupDetailResp>(`/accounting/group/${id}`);
    return response.data;
  }

  // 更新群组
  async updateGroup(id: number, data: GroupUpdateReq): Promise<GroupDetailResp> {
    const response = await this.client.put<GroupDetailResp>(`/accounting/group/${id}`, data);
    return response.data;
  }

  // 删除群组
  async deleteGroup(id: number): Promise<void> {
    await this.client.delete(`/accounting/group/${id}`);
  }

  // 添加成员
  async addMember(groupId: number, userId: number, role: GroupRole): Promise<void> {
    await this.client.post(`/accounting/group/${groupId}/member`, { userId, role });
  }

  // 生成邀请码
  async generateInviteCode(groupId: number, expiresIn?: number): Promise<string> {
    const response = await this.client.post<{ inviteCode: string }>(
      `/accounting/group/${groupId}/invite`,
      { expiresIn }
    );
    return response.data.inviteCode;
  }
}
```

### 6.2 错误处理最佳实践

#### 5.2.1 统一错误处理
```typescript
// error-handler.ts
class ErrorHandler {
  static handle(error: any): void {
    if (error instanceof ApiError) {
      this.handleApiError(error);
    } else if (error instanceof ValidationError) {
      this.handleValidationError(error);
    } else {
      this.handleUnknownError(error);
    }
  }

  private static handleApiError(error: ApiError): void {
    switch (error.code) {
      case 401:
        // 未授权，跳转登录
        this.redirectToLogin();
        break;
      case 403:
        // 权限不足
        this.showError('权限不足，请联系管理员');
        break;
      case 404:
        // 资源不存在
        this.showError('请求的资源不存在');
        break;
      case 409:
        // 资源冲突
        this.showError(error.message || '操作冲突，请刷新后重试');
        break;
      case 429:
        // 请求过于频繁
        this.showError('请求过于频繁，请稍后再试');
        break;
      case 500:
        // 服务器错误
        this.showError('服务器内部错误，请稍后再试');
        break;
      default:
        this.showError(error.message || '操作失败');
    }
  }

  private static handleValidationError(error: ValidationError): void {
    // 显示表单验证错误
    const errorMessages = Object.entries(error.errors)
      .map(([field, message]) => `${field}: ${message}`)
      .join('\n');
    this.showError(errorMessages);
  }

  private static handleUnknownError(error: any): void {
    console.error('Unknown error:', error);
    this.showError('发生未知错误，请稍后再试');
  }

  private static showError(message: string): void {
    // 这里可以集成你的通知组件
    alert(message); // 简单示例
  }

  private static redirectToLogin(): void {
    localStorage.removeItem('satoken');
    localStorage.removeItem('refreshToken');
    window.location.href = '/login';
  }
}

// 使用示例
try {
  const groups = await groupApi.getGroups(query);
  // 处理成功结果
} catch (error) {
  ErrorHandler.handle(error);
}
```

#### 5.2.2 表单验证
```typescript
// validation.ts
class Validator {
  static validateGroupCreateReq(data: GroupCreateReq): ValidationResult {
    const errors: Record<string, string> = {};

    // 群组名称验证
    if (!data.name || data.name.trim().length === 0) {
      errors.name = '群组名称不能为空';
    } else if (data.name.length > 100) {
      errors.name = '群组名称长度不能超过100个字符';
    }

    // 平台类型验证
    if (!data.platform) {
      errors.platform = '平台类型不能为空';
    } else if (!Object.values(PlatformType).includes(data.platform)) {
      errors.platform = '无效的平台类型';
    }

    // 平台群组ID验证
    if (!data.platformGroupId || data.platformGroupId.trim().length === 0) {
      errors.platformGroupId = '平台群组ID不能为空';
    } else if (data.platformGroupId.length > 100) {
      errors.platformGroupId = '平台群组ID长度不能超过100个字符';
    }

    // 群主用户ID验证
    if (!data.ownerId || data.ownerId <= 0) {
      errors.ownerId = '群主用户ID不能为空';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  static validateTransactionCreateReq(data: TransactionCreateReq): ValidationResult {
    const errors: Record<string, string> = {};

    // 群组ID验证
    if (!data.groupId || data.groupId <= 0) {
      errors.groupId = '群组ID不能为空';
    }

    // 交易类型验证
    if (!data.type) {
      errors.type = '交易类型不能为空';
    } else if (!Object.values(TransactionType).includes(data.type)) {
      errors.type = '无效的交易类型';
    }

    // 金额验证
    if (!data.amount || data.amount <= 0) {
      errors.amount = '金额必须大于0';
    } else if (data.amount > 999999999.99) {
      errors.amount = '金额不能超过999,999,999.99';
    }

    // 币种验证
    if (!data.currency) {
      errors.currency = '币种不能为空';
    } else if (!/^[A-Z]{3}$/.test(data.currency)) {
      errors.currency = '币种格式不正确';
    }

    // 描述验证
    if (!data.description || data.description.trim().length === 0) {
      errors.description = '描述不能为空';
    } else if (data.description.length > 200) {
      errors.description = '描述长度不能超过200个字符';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }
}

interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

class ValidationError extends Error {
  constructor(public errors: Record<string, string>) {
    super('Validation failed');
    this.name = 'ValidationError';
  }
}
```

### 6.3 数据格式化规范

#### 5.3.1 日期时间格式化
```typescript
// date-utils.ts
class DateUtils {
  // 格式化日期时间
  static formatDateTime(date: string | Date, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
    const d = typeof date === 'string' ? new Date(date) : date;

    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');

    return format
      .replace('YYYY', year.toString())
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds);
  }

  // 相对时间
  static formatRelativeTime(date: string | Date): string {
    const d = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diff = now.getTime() - d.getTime();

    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}天前`;
    if (hours > 0) return `${hours}小时前`;
    if (minutes > 0) return `${minutes}分钟前`;
    return '刚刚';
  }

  // 日期范围格式化
  static formatDateRange(start: string | Date, end: string | Date): string {
    const startStr = this.formatDateTime(start, 'MM-DD');
    const endStr = this.formatDateTime(end, 'MM-DD');
    return `${startStr} 至 ${endStr}`;
  }
}
```

#### 5.3.2 金额格式化
```typescript
// currency-utils.ts
class CurrencyUtils {
  // 格式化金额
  static formatAmount(amount: number, currency: string = 'CNY', locale: string = 'zh-CN'): string {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  }

  // 格式化数字（千分位分隔）
  static formatNumber(num: number, decimals: number = 2): string {
    return new Intl.NumberFormat('zh-CN', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    }).format(num);
  }

  // 简化大数字显示
  static formatLargeNumber(num: number): string {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  }

  // 百分比格式化
  static formatPercentage(value: number, total: number): string {
    if (total === 0) return '0%';
    const percentage = (value / total) * 100;
    return `${percentage.toFixed(1)}%`;
  }
}
```

### 6.4 国际化支持说明

#### 5.4.1 多语言配置
```typescript
// i18n.ts
interface I18nMessages {
  [key: string]: string | I18nMessages;
}

const messages: Record<string, I18nMessages> = {
  'zh-CN': {
    common: {
      save: '保存',
      cancel: '取消',
      delete: '删除',
      edit: '编辑',
      add: '添加',
      search: '搜索',
      loading: '加载中...',
      noData: '暂无数据'
    },
    group: {
      title: '群组管理',
      create: '创建群组',
      name: '群组名称',
      description: '群组描述',
      platform: '平台类型',
      members: '成员',
      settings: '设置'
    },
    transaction: {
      title: '交易管理',
      create: '创建交易',
      amount: '金额',
      type: '类型',
      description: '描述',
      category: '分类',
      date: '日期'
    }
  },
  'en-US': {
    common: {
      save: 'Save',
      cancel: 'Cancel',
      delete: 'Delete',
      edit: 'Edit',
      add: 'Add',
      search: 'Search',
      loading: 'Loading...',
      noData: 'No Data'
    },
    group: {
      title: 'Group Management',
      create: 'Create Group',
      name: 'Group Name',
      description: 'Description',
      platform: 'Platform',
      members: 'Members',
      settings: 'Settings'
    },
    transaction: {
      title: 'Transaction Management',
      create: 'Create Transaction',
      amount: 'Amount',
      type: 'Type',
      description: 'Description',
      category: 'Category',
      date: 'Date'
    }
  }
};

class I18n {
  private locale: string = 'zh-CN';
  private messages: I18nMessages;

  constructor() {
    this.locale = localStorage.getItem('locale') || 'zh-CN';
    this.messages = messages[this.locale] || messages['zh-CN'];
  }

  t(key: string, params?: Record<string, any>): string {
    const keys = key.split('.');
    let value: any = this.messages;

    for (const k of keys) {
      value = value[k];
      if (value === undefined) {
        return key; // 返回key作为fallback
      }
    }

    if (typeof value !== 'string') {
      return key;
    }

    // 参数替换
    if (params) {
      return value.replace(/\{(\w+)\}/g, (match, paramKey) => {
        return params[paramKey] || match;
      });
    }

    return value;
  }

  setLocale(locale: string): void {
    this.locale = locale;
    this.messages = messages[locale] || messages['zh-CN'];
    localStorage.setItem('locale', locale);
  }

  getLocale(): string {
    return this.locale;
  }
}

// 全局实例
export const i18n = new I18n();
```

---

## 7. 实际集成示例

### 7.1 Vue 3 + TypeScript 集成示例

#### 7.1.1 项目结构
```
src/
├── api/                 # API接口定义
│   ├── accounting.ts   # 记账相关API
│   ├── auth.ts         # 认证相关API
│   └── types.ts        # 类型定义
├── composables/        # Vue组合式函数
│   ├── useAuth.ts      # 认证相关
│   ├── useApi.ts       # API调用
│   └── usePagination.ts # 分页处理
├── components/         # 组件
│   ├── GroupList.vue   # 群组列表
│   ├── TransactionForm.vue # 交易表单
│   └── Pagination.vue  # 分页组件
└── stores/             # Pinia状态管理
    ├── auth.ts         # 认证状态
    └── accounting.ts   # 记账状态
```

#### 7.1.2 API服务封装
```typescript
// api/client.ts
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { TokenManager } from '@/utils/token';

class ApiClient {
  private instance: AxiosInstance;

  constructor() {
    this.instance = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL || '/api/v1',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        const token = TokenManager.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        const { code, data, message } = response.data;

        if (code === 200) {
          return { ...response, data };
        } else {
          throw new ApiError(message, code, data);
        }
      },
      async (error) => {
        if (error.response?.status === 401) {
          // Token过期，尝试刷新
          try {
            await TokenManager.refreshToken();
            // 重新发起原请求
            return this.instance.request(error.config);
          } catch (refreshError) {
            // 刷新失败，跳转登录
            TokenManager.removeToken();
            window.location.href = '/login';
            return Promise.reject(refreshError);
          }
        } else if (error.response?.status === 429) {
          // 处理限流
          const retryAfter = error.response.headers['retry-after'] || 60;
          throw new RateLimitError('请求过于频繁', retryAfter);
        }

        return Promise.reject(error);
      }
    );
  }

  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.get(url, config);
    return response.data;
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.post(url, data, config);
    return response.data;
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.put(url, data, config);
    return response.data;
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.delete(url, config);
    return response.data;
  }
}

export const apiClient = new ApiClient();
```

#### 7.1.3 记账API封装
```typescript
// api/accounting.ts
import { apiClient } from './client';
import type {
  GroupCreateReq,
  GroupResp,
  PageQuery,
  PageResp,
  TransactionCreateReq,
  TransactionResp
} from './types';

export class AccountingApi {
  // 群组管理
  static async createGroup(data: GroupCreateReq): Promise<GroupResp> {
    return apiClient.post<GroupResp>('/accounting/group', data);
  }

  static async getGroupList(query: PageQuery): Promise<PageResp<GroupResp>> {
    return apiClient.get<PageResp<GroupResp>>('/accounting/group/page', {
      params: query
    });
  }

  static async getUserGroups(): Promise<GroupResp[]> {
    return apiClient.get<GroupResp[]>('/accounting/group/user');
  }

  static async updateGroup(id: number, data: Partial<GroupCreateReq>): Promise<GroupResp> {
    return apiClient.put<GroupResp>(`/accounting/group/${id}`, data);
  }

  static async deleteGroup(id: number): Promise<void> {
    return apiClient.delete<void>(`/accounting/group/${id}`);
  }

  // 交易管理
  static async createTransaction(data: TransactionCreateReq): Promise<TransactionResp> {
    return apiClient.post<TransactionResp>('/accounting/transaction', data);
  }

  static async getTransactionList(query: PageQuery & { groupId?: number }): Promise<PageResp<TransactionResp>> {
    return apiClient.get<PageResp<TransactionResp>>('/accounting/transaction/page', {
      params: query
    });
  }

  static async updateTransaction(id: number, data: Partial<TransactionCreateReq>): Promise<TransactionResp> {
    return apiClient.put<TransactionResp>(`/accounting/transaction/${id}`, data);
  }

  static async deleteTransaction(id: number): Promise<void> {
    return apiClient.delete<void>(`/accounting/transaction/${id}`);
  }
}
```

#### 7.1.4 Vue组合式函数
```typescript
// composables/useAuth.ts
import { ref, computed } from 'vue';
import { TokenManager } from '@/utils/token';
import { AuthApi } from '@/api/auth';

export function useAuth() {
  const user = ref(TokenManager.getUserInfo());
  const isLoggedIn = computed(() => !!user.value);

  const login = async (credentials: LoginCredentials) => {
    try {
      const result = await AuthApi.login(credentials);

      TokenManager.setToken(result.token, result.expiresIn);
      localStorage.setItem('refreshToken', result.refreshToken);
      TokenManager.setUserInfo(result.userInfo);

      user.value = result.userInfo;

      return result;
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      await AuthApi.logout();
    } catch (error) {
      console.error('登出失败:', error);
    } finally {
      TokenManager.removeToken();
      user.value = null;
    }
  };

  const hasPermission = (permission: string): boolean => {
    return user.value?.permissions?.includes(permission) || false;
  };

  return {
    user: readonly(user),
    isLoggedIn,
    login,
    logout,
    hasPermission
  };
}
```

#### 7.1.5 分页处理组合式函数
```typescript
// composables/usePagination.ts
import { ref, reactive, computed } from 'vue';

export function usePagination<T>(
  fetchFn: (query: PageQuery) => Promise<PageResp<T>>,
  initialQuery: Partial<PageQuery> = {}
) {
  const loading = ref(false);
  const data = ref<T[]>([]);
  const error = ref<string | null>(null);

  const pagination = reactive({
    page: 1,
    size: 20,
    total: 0,
    pages: 0,
    ...initialQuery
  });

  const hasNext = computed(() => pagination.page < pagination.pages);
  const hasPrevious = computed(() => pagination.page > 1);

  const fetchData = async (resetPage = false) => {
    if (resetPage) {
      pagination.page = 1;
    }

    loading.value = true;
    error.value = null;

    try {
      const result = await fetchFn({
        page: pagination.page,
        size: pagination.size
      });

      data.value = result.list;
      pagination.total = result.total;
      pagination.pages = result.pages;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取数据失败';
      console.error('分页数据获取失败:', err);
    } finally {
      loading.value = false;
    }
  };

  const goToPage = (page: number) => {
    if (page >= 1 && page <= pagination.pages) {
      pagination.page = page;
      fetchData();
    }
  };

  const changePageSize = (size: number) => {
    pagination.size = size;
    fetchData(true);
  };

  const refresh = () => {
    fetchData();
  };

  return {
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),
    pagination: readonly(pagination),
    hasNext,
    hasPrevious,
    fetchData,
    goToPage,
    changePageSize,
    refresh
  };
}
```

### 7.2 React + TypeScript 集成示例

#### 7.2.1 自定义Hook
```typescript
// hooks/useApi.ts
import { useState, useEffect, useCallback } from 'react';
import { apiClient } from '@/api/client';

export function useApi<T>(
  apiCall: () => Promise<T>,
  dependencies: any[] = []
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const execute = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await apiCall();
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : '请求失败');
    } finally {
      setLoading(false);
    }
  }, dependencies);

  useEffect(() => {
    execute();
  }, [execute]);

  return { data, loading, error, refetch: execute };
}

// hooks/useAuth.ts
import { createContext, useContext, useState, useEffect } from 'react';
import { TokenManager } from '@/utils/token';

interface AuthContextType {
  user: UserInfo | null;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  hasPermission: (permission: string) => boolean;
}

const AuthContext = createContext<AuthContextType | null>(null);

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<UserInfo | null>(TokenManager.getUserInfo());

  const login = async (credentials: LoginCredentials) => {
    const result = await AuthApi.login(credentials);

    TokenManager.setToken(result.token, result.expiresIn);
    localStorage.setItem('refreshToken', result.refreshToken);
    TokenManager.setUserInfo(result.userInfo);

    setUser(result.userInfo);
  };

  const logout = () => {
    TokenManager.removeToken();
    setUser(null);
  };

  const hasPermission = (permission: string): boolean => {
    return user?.permissions?.includes(permission) || false;
  };

  return (
    <AuthContext.Provider value={{ user, login, logout, hasPermission }}>
      {children}
    </AuthContext.Provider>
  );
}
```

#### 7.2.2 React组件示例
```typescript
// components/GroupList.tsx
import React from 'react';
import { useApi } from '@/hooks/useApi';
import { usePagination } from '@/hooks/usePagination';
import { AccountingApi } from '@/api/accounting';
import { GroupResp } from '@/api/types';

export function GroupList() {
  const {
    data: groups,
    loading,
    error,
    pagination,
    goToPage,
    changePageSize,
    refresh
  } = usePagination<GroupResp>(AccountingApi.getGroupList);

  useEffect(() => {
    refresh();
  }, []);

  if (loading) return <div>加载中...</div>;
  if (error) return <div>错误: {error}</div>;

  return (
    <div className="group-list">
      <div className="group-list-header">
        <h2>群组列表</h2>
        <button onClick={refresh}>刷新</button>
      </div>

      <div className="group-list-content">
        {groups?.map(group => (
          <div key={group.id} className="group-item">
            <h3>{group.name}</h3>
            <p>{group.description}</p>
            <span>成员数: {group.memberCount}</span>
          </div>
        ))}
      </div>

      <Pagination
        current={pagination.page}
        total={pagination.total}
        pageSize={pagination.size}
        onChange={goToPage}
        onPageSizeChange={changePageSize}
      />
    </div>
  );
}
```

---

## 8. 环境配置说明

### 8.1 开发环境配置

#### 6.1.1 API地址配置
```typescript
// config.ts
interface EnvironmentConfig {
  apiBaseUrl: string;
  wsBaseUrl: string;
  fileBaseUrl: string;
  enableMock: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
}

const configs: Record<string, EnvironmentConfig> = {
  development: {
    apiBaseUrl: 'http://localhost:8080/api/v1',
    wsBaseUrl: 'ws://localhost:8080/ws',
    fileBaseUrl: 'http://localhost:8080/files',
    enableMock: false,
    logLevel: 'debug'
  },
  testing: {
    apiBaseUrl: 'http://test-api.example.com/api/v1',
    wsBaseUrl: 'ws://test-api.example.com/ws',
    fileBaseUrl: 'http://test-api.example.com/files',
    enableMock: false,
    logLevel: 'info'
  },
  production: {
    apiBaseUrl: 'https://api.example.com/api/v1',
    wsBaseUrl: 'wss://api.example.com/ws',
    fileBaseUrl: 'https://cdn.example.com/files',
    enableMock: false,
    logLevel: 'error'
  }
};

export const getConfig = (): EnvironmentConfig => {
  const env = process.env.NODE_ENV || 'development';
  return configs[env] || configs.development;
};
```

#### 6.1.2 跨域配置说明

**开发环境代理配置 (Vite):**
```typescript
// vite.config.ts
import { defineConfig } from 'vite';

export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '/api')
      },
      '/ws': {
        target: 'ws://localhost:8080',
        ws: true,
        changeOrigin: true
      }
    }
  }
});
```

**开发环境代理配置 (Webpack):**
```javascript
// webpack.config.js
module.exports = {
  devServer: {
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        pathRewrite: {
          '^/api': '/api'
        }
      }
    }
  }
};
```

**生产环境CORS配置:**
```yaml
# application-prod.yml (后端配置)
spring:
  web:
    cors:
      allowed-origins:
        - "https://your-frontend-domain.com"
        - "https://admin.your-domain.com"
      allowed-methods:
        - GET
        - POST
        - PUT
        - DELETE
        - OPTIONS
      allowed-headers:
        - "*"
      allow-credentials: true
      max-age: 3600
```

### 6.2 测试环境配置

#### 6.2.1 Mock数据配置
```typescript
// mock-config.ts
interface MockConfig {
  enabled: boolean;
  delay: number; // 模拟网络延迟
  errorRate: number; // 错误率 (0-1)
}

const mockConfig: MockConfig = {
  enabled: process.env.NODE_ENV === 'development' && process.env.ENABLE_MOCK === 'true',
  delay: 500,
  errorRate: 0.1
};

// Mock数据生成器
class MockDataGenerator {
  static generateGroup(): GroupDetailResp {
    return {
      id: Math.floor(Math.random() * 1000),
      name: `测试群组${Math.floor(Math.random() * 100)}`,
      description: '这是一个测试群组',
      platform: PlatformType.TELEGRAM,
      platformGroupId: `group_${Math.random().toString(36).substr(2, 9)}`,
      ownerId: 1,
      memberCount: Math.floor(Math.random() * 20) + 1,
      totalTransactions: Math.floor(Math.random() * 1000),
      totalAmount: (Math.random() * 100000).toFixed(2),
      defaultCurrency: 'CNY',
      timezone: 'Asia/Shanghai',
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      settings: {
        allowMemberInvite: true,
        requireApproval: false,
        autoSplit: true,
        defaultSplitType: SplitType.EQUAL,
        maxMembers: 50,
        allowGuestView: false,
        enableNotification: true,
        notificationChannels: ['email', 'push']
      }
    };
  }

  static generateTransaction(): TransactionDetailResp {
    const types = Object.values(TransactionType);
    const categories = ['餐饮', '交通', '购物', '娱乐', '医疗', '教育'];

    return {
      id: Math.floor(Math.random() * 1000),
      groupId: Math.floor(Math.random() * 10) + 1,
      userId: Math.floor(Math.random() * 10) + 1,
      type: types[Math.floor(Math.random() * types.length)],
      amount: parseFloat((Math.random() * 1000).toFixed(2)),
      currency: 'CNY',
      description: `测试交易${Math.floor(Math.random() * 100)}`,
      category: categories[Math.floor(Math.random() * categories.length)],
      tags: '测试,自动生成',
      transactionDate: new Date().toISOString(),
      location: '测试地点',
      status: 'COMPLETED',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: {
        id: 1,
        username: 'testuser',
        nickname: '测试用户'
      }
    };
  }
}
```

### 6.3 调试工具和测试方法

#### 6.3.1 API调试工具
```typescript
// debug-utils.ts
class ApiDebugger {
  private static logs: ApiLog[] = [];

  static logRequest(url: string, method: string, data?: any): void {
    if (getConfig().logLevel === 'debug') {
      const log: ApiLog = {
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        type: 'request',
        method,
        url,
        data
      };

      this.logs.push(log);
      console.group(`🚀 API Request: ${method} ${url}`);
      console.log('Data:', data);
      console.groupEnd();
    }
  }

  static logResponse(url: string, method: string, response: any, duration: number): void {
    if (getConfig().logLevel === 'debug') {
      const log: ApiLog = {
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        type: 'response',
        method,
        url,
        data: response,
        duration
      };

      this.logs.push(log);
      console.group(`✅ API Response: ${method} ${url} (${duration}ms)`);
      console.log('Response:', response);
      console.groupEnd();
    }
  }

  static logError(url: string, method: string, error: any): void {
    const log: ApiLog = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      type: 'error',
      method,
      url,
      data: error
    };

    this.logs.push(log);
    console.group(`❌ API Error: ${method} ${url}`);
    console.error('Error:', error);
    console.groupEnd();
  }

  static exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }

  static clearLogs(): void {
    this.logs = [];
  }
}

interface ApiLog {
  id: string;
  timestamp: string;
  type: 'request' | 'response' | 'error';
  method: string;
  url: string;
  data?: any;
  duration?: number;
}
```

#### 6.3.2 性能监控
```typescript
// performance-monitor.ts
class PerformanceMonitor {
  private static metrics: PerformanceMetric[] = [];

  static startTiming(name: string): string {
    const id = `${name}_${Date.now()}`;
    performance.mark(`${id}_start`);
    return id;
  }

  static endTiming(id: string): number {
    performance.mark(`${id}_end`);
    performance.measure(id, `${id}_start`, `${id}_end`);

    const measure = performance.getEntriesByName(id)[0];
    const duration = measure.duration;

    this.metrics.push({
      name: id,
      duration,
      timestamp: new Date().toISOString()
    });

    // 清理性能标记
    performance.clearMarks(`${id}_start`);
    performance.clearMarks(`${id}_end`);
    performance.clearMeasures(id);

    return duration;
  }

  static getMetrics(): PerformanceMetric[] {
    return this.metrics;
  }

  static getAverageTime(name: string): number {
    const filtered = this.metrics.filter(m => m.name.startsWith(name));
    if (filtered.length === 0) return 0;

    const total = filtered.reduce((sum, m) => sum + m.duration, 0);
    return total / filtered.length;
  }
}

interface PerformanceMetric {
  name: string;
  duration: number;
  timestamp: string;
}
```

---

## 9. API测试指南

### 9.1 使用Swagger UI测试

#### 9.1.1 访问Swagger文档
系统提供了两种API文档界面：

**Knife4j增强文档（推荐）:**
- 访问地址: `http://localhost:8080/doc.html`
- 功能特点: 界面美观、功能丰富、支持在线调试

**标准Swagger UI:**
- 访问地址: `http://localhost:8080/swagger-ui`
- 功能特点: 标准OpenAPI界面、兼容性好

#### 9.1.2 在线测试步骤

**1. 获取访问令牌:**
```http
# 首先调用登录接口获取token
POST /api/v1/auth/login
{
  "username": "admin",
  "password": "encrypted_password",
  "clientId": "web_client",
  "authType": "ACCOUNT"
}

# 响应中获取token
{
  "code": 200,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_here"
  }
}
```

**2. 配置认证信息:**
- 点击页面右上角的 "Authorize" 按钮
- 在弹出的对话框中输入: `Bearer {your_token}`
- 点击 "Authorize" 确认

**3. 测试API接口:**
- 选择要测试的接口
- 填写必要的参数
- 点击 "Try it out" 按钮
- 查看响应结果

#### 9.1.3 常用测试场景

**测试群组创建:**
```json
POST /api/v1/accounting/group
{
  "name": "测试群组",
  "description": "这是一个测试群组",
  "platform": "TELEGRAM",
  "platformGroupId": "*********",
  "ownerId": 1,
  "defaultCurrency": "CNY",
  "timezone": "Asia/Shanghai"
}
```

**测试分页查询:**
```http
GET /api/v1/accounting/group/page?page=1&size=10&sort=createTime&order=desc
```

**测试权限验证:**
```http
# 不带token的请求应该返回401
GET /api/v1/accounting/group/1

# 带无效token的请求应该返回401
Authorization: Bearer invalid_token
GET /api/v1/accounting/group/1
```

### 9.2 使用Postman测试

#### 9.2.1 环境配置
创建Postman环境变量：

```json
{
  "baseUrl": "http://localhost:8080/api/v1",
  "token": "",
  "refreshToken": ""
}
```

#### 9.2.2 认证脚本
在登录请求的 "Tests" 标签页添加脚本：

```javascript
// 自动保存token到环境变量
if (pm.response.code === 200) {
    const responseJson = pm.response.json();
    if (responseJson.code === 200) {
        pm.environment.set("token", responseJson.data.token);
        pm.environment.set("refreshToken", responseJson.data.refreshToken);
        console.log("Token saved:", responseJson.data.token);
    }
}
```

#### 9.2.3 请求预处理脚本
在需要认证的请求的 "Pre-request Script" 中添加：

```javascript
// 自动添加Authorization头
const token = pm.environment.get("token");
if (token) {
    pm.request.headers.add({
        key: "Authorization",
        value: `Bearer ${token}`
    });
}
```

#### 9.2.4 Postman集合示例
```json
{
  "info": {
    "name": "ContiNew Admin API",
    "description": "ContiNew Admin 系统API测试集合"
  },
  "item": [
    {
      "name": "认证",
      "item": [
        {
          "name": "登录",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"username\": \"admin\",\n  \"password\": \"encrypted_password\",\n  \"clientId\": \"web_client\",\n  \"authType\": \"ACCOUNT\"\n}"
            },
            "url": {
              "raw": "{{baseUrl}}/auth/login",
              "host": ["{{baseUrl}}"],
              "path": ["auth", "login"]
            }
          }
        }
      ]
    },
    {
      "name": "群组管理",
      "item": [
        {
          "name": "创建群组",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              },
              {
                "key": "Authorization",
                "value": "Bearer {{token}}"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"name\": \"测试群组\",\n  \"description\": \"这是一个测试群组\",\n  \"platform\": \"TELEGRAM\",\n  \"platformGroupId\": \"*********\",\n  \"ownerId\": 1,\n  \"defaultCurrency\": \"CNY\",\n  \"timezone\": \"Asia/Shanghai\"\n}"
            },
            "url": {
              "raw": "{{baseUrl}}/accounting/group",
              "host": ["{{baseUrl}}"],
              "path": ["accounting", "group"]
            }
          }
        }
      ]
    }
  ]
}
```

### 9.3 使用curl命令测试

#### 9.3.1 基础命令示例

**登录获取token:**
```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "encrypted_password",
    "clientId": "web_client",
    "authType": "ACCOUNT"
  }'
```

**使用token调用API:**
```bash
# 设置token变量
TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

# 创建群组
curl -X POST http://localhost:8080/api/v1/accounting/group \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "name": "测试群组",
    "description": "这是一个测试群组",
    "platform": "TELEGRAM",
    "platformGroupId": "*********",
    "ownerId": 1,
    "defaultCurrency": "CNY",
    "timezone": "Asia/Shanghai"
  }'

# 查询群组列表
curl -X GET "http://localhost:8080/api/v1/accounting/group/page?page=1&size=10" \
  -H "Authorization: Bearer $TOKEN"

# 获取群组详情
curl -X GET http://localhost:8080/api/v1/accounting/group/1 \
  -H "Authorization: Bearer $TOKEN"
```

#### 9.3.2 批量测试脚本
```bash
#!/bin/bash

# API测试脚本
BASE_URL="http://localhost:8080/api/v1"
USERNAME="admin"
PASSWORD="encrypted_password"

# 1. 登录获取token
echo "正在登录..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d "{
    \"username\": \"$USERNAME\",
    \"password\": \"$PASSWORD\",
    \"clientId\": \"web_client\",
    \"authType\": \"ACCOUNT\"
  }")

TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.data.token')

if [ "$TOKEN" = "null" ]; then
  echo "登录失败: $LOGIN_RESPONSE"
  exit 1
fi

echo "登录成功，Token: ${TOKEN:0:20}..."

# 2. 测试创建群组
echo "正在创建群组..."
CREATE_RESPONSE=$(curl -s -X POST "$BASE_URL/accounting/group" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "name": "测试群组",
    "description": "这是一个测试群组",
    "platform": "TELEGRAM",
    "platformGroupId": "*********",
    "ownerId": 1,
    "defaultCurrency": "CNY",
    "timezone": "Asia/Shanghai"
  }')

GROUP_ID=$(echo $CREATE_RESPONSE | jq -r '.data.id')
echo "群组创建成功，ID: $GROUP_ID"

# 3. 测试查询群组
echo "正在查询群组列表..."
LIST_RESPONSE=$(curl -s -X GET "$BASE_URL/accounting/group/page?page=1&size=10" \
  -H "Authorization: Bearer $TOKEN")

TOTAL=$(echo $LIST_RESPONSE | jq -r '.data.total')
echo "查询成功，总数: $TOTAL"

# 4. 测试更新群组
echo "正在更新群组..."
UPDATE_RESPONSE=$(curl -s -X PUT "$BASE_URL/accounting/group/$GROUP_ID" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "name": "更新后的群组名称",
    "description": "更新后的描述"
  }')

echo "群组更新成功"

# 5. 测试删除群组
echo "正在删除群组..."
DELETE_RESPONSE=$(curl -s -X DELETE "$BASE_URL/accounting/group/$GROUP_ID" \
  -H "Authorization: Bearer $TOKEN")

echo "群组删除成功"
echo "所有测试完成！"
```

### 9.4 自动化测试

#### 9.4.1 Jest测试示例
```typescript
// tests/api/accounting.test.ts
import { AccountingApi } from '@/api/accounting';
import { AuthApi } from '@/api/auth';

describe('Accounting API Tests', () => {
  let authToken: string;
  let testGroupId: number;

  beforeAll(async () => {
    // 登录获取token
    const loginResult = await AuthApi.login({
      username: '<EMAIL>',
      password: 'test_password',
      clientId: 'test_client',
      authType: 'ACCOUNT'
    });

    authToken = loginResult.token;
    // 设置全局token
    localStorage.setItem('satoken', authToken);
  });

  afterAll(async () => {
    // 清理测试数据
    if (testGroupId) {
      await AccountingApi.deleteGroup(testGroupId);
    }
    localStorage.removeItem('satoken');
  });

  test('应该能够创建群组', async () => {
    const groupData = {
      name: '测试群组',
      description: '这是一个测试群组',
      platform: 'TELEGRAM' as const,
      platformGroupId: '*********',
      ownerId: 1,
      defaultCurrency: 'CNY',
      timezone: 'Asia/Shanghai'
    };

    const result = await AccountingApi.createGroup(groupData);

    expect(result).toBeDefined();
    expect(result.name).toBe(groupData.name);
    expect(result.id).toBeGreaterThan(0);

    testGroupId = result.id;
  });

  test('应该能够查询群组列表', async () => {
    const result = await AccountingApi.getGroupList({
      page: 1,
      size: 10
    });

    expect(result).toBeDefined();
    expect(result.list).toBeInstanceOf(Array);
    expect(result.total).toBeGreaterThanOrEqual(0);
    expect(result.page).toBe(1);
    expect(result.size).toBe(10);
  });

  test('应该能够更新群组', async () => {
    const updateData = {
      name: '更新后的群组名称',
      description: '更新后的描述'
    };

    const result = await AccountingApi.updateGroup(testGroupId, updateData);

    expect(result.name).toBe(updateData.name);
    expect(result.description).toBe(updateData.description);
  });

  test('应该正确处理权限错误', async () => {
    // 清除token模拟未授权
    localStorage.removeItem('satoken');

    await expect(AccountingApi.getGroupList({ page: 1, size: 10 }))
      .rejects
      .toThrow('Unauthorized');

    // 恢复token
    localStorage.setItem('satoken', authToken);
  });
});
```

---

## 10. 错误处理和调试

### 10.1 常见错误及解决方案

| 错误类型 | 错误信息 | 可能原因 | 解决方案 |
|----------|----------|----------|----------|
| 401 Unauthorized | Token无效或已过期 | 认证失败 | 重新登录或刷新token |
| 403 Forbidden | 权限不足 | 用户权限不够 | 检查用户角色和权限配置 |
| 404 Not Found | 资源不存在 | URL错误或资源已删除 | 检查API路径和资源ID |
| 409 Conflict | 资源冲突 | 数据重复或状态冲突 | 检查数据唯一性约束 |
| 422 Validation Error | 参数验证失败 | 请求参数格式错误 | 检查参数格式和必填字段 |
| 429 Too Many Requests | 请求过于频繁 | 触发限流 | 减少请求频率或实现重试机制 |
| 500 Internal Server Error | 服务器内部错误 | 后端异常 | 查看服务器日志，联系后端开发 |

### 10.2 调试检查清单

#### 10.2.1 网络请求调试
- [ ] 检查API地址是否正确
- [ ] 确认请求方法 (GET/POST/PUT/DELETE)
- [ ] 验证请求头设置 (Content-Type, Authorization)
- [ ] 检查请求参数格式和内容
- [ ] 确认网络连接状态
- [ ] 查看浏览器开发者工具Network面板

#### 10.2.2 认证授权调试
- [ ] 确认token是否存在且有效
- [ ] 检查token格式 (Bearer前缀)
- [ ] 验证用户权限和角色
- [ ] 确认API权限配置
- [ ] 检查token过期时间

#### 10.2.3 数据格式调试
- [ ] 验证JSON格式是否正确
- [ ] 检查字段名称和类型
- [ ] 确认必填字段是否提供
- [ ] 验证数据格式 (日期、金额等)
- [ ] 检查字符编码问题

---

## 📞 技术支持

如有任何技术问题或需要进一步的集成支持，请联系开发团队：

- **技术文档**: [项目Wiki](https://github.com/your-org/continew-admin/wiki)
- **问题反馈**: [GitHub Issues](https://github.com/your-org/continew-admin/issues)
- **开发交流**: 技术交流群
- **邮件支持**: <EMAIL>

---

*本文档最后更新时间: 2025-01-01*