package top.continew.admin.accounting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import top.continew.admin.accounting.model.entity.GroupMemberDO;

import java.util.List;

/**
 * 群组成员 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface GroupMemberMapper extends BaseMapper<GroupMemberDO> {

    /**
     * 获取群组成员列表
     *
     * @param groupId 群组ID
     * @return 成员列表
     */
    @Select("""
        SELECT gm.*, u.username, u.nickname as user_nickname 
        FROM acc_group_member gm 
        LEFT JOIN sys_user u ON gm.user_id = u.id 
        WHERE gm.group_id = #{groupId} AND gm.status = 1
        ORDER BY gm.role, gm.join_time
        """)
    List<GroupMemberDO> selectByGroupId(@Param("groupId") Long groupId);

    /**
     * 获取用户在群组中的角色
     *
     * @param groupId 群组ID
     * @param userId  用户ID
     * @return 成员信息
     */
    @Select("""
        SELECT * FROM acc_group_member 
        WHERE group_id = #{groupId} AND user_id = #{userId} AND status = 1
        """)
    GroupMemberDO selectByGroupIdAndUserId(@Param("groupId") Long groupId, @Param("userId") Long userId);

    /**
     * 获取群组成员数量
     *
     * @param groupId 群组ID
     * @return 成员数量
     */
    @Select("""
        SELECT COUNT(*) FROM acc_group_member 
        WHERE group_id = #{groupId} AND status = 1
        """)
    int countByGroupId(@Param("groupId") Long groupId);

    /**
     * 检查用户是否为群组成员
     *
     * @param groupId 群组ID
     * @param userId  用户ID
     * @return 是否为成员
     */
    @Select("""
        SELECT COUNT(*) > 0 FROM acc_group_member 
        WHERE group_id = #{groupId} AND user_id = #{userId} AND status = 1
        """)
    boolean existsByGroupIdAndUserId(@Param("groupId") Long groupId, @Param("userId") Long userId);
}
