package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.accounting.enums.GroupRole;
import top.continew.admin.accounting.enums.PlatformType;
import top.continew.admin.accounting.enums.SubscriptionPlan;

import java.time.LocalDateTime;

/**
 * 群组列表响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "群组列表响应")
public class GroupListResp {

    /**
     * 群组ID
     */
    @Schema(description = "群组ID")
    private Long id;

    /**
     * 群组名称
     */
    @Schema(description = "群组名称")
    private String name;

    /**
     * 群组描述
     */
    @Schema(description = "群组描述")
    private String description;

    /**
     * 平台类型
     */
    @Schema(description = "平台类型")
    private PlatformType platform;

    /**
     * 订阅套餐
     */
    @Schema(description = "订阅套餐")
    private SubscriptionPlan subscriptionPlan;

    /**
     * 默认币种
     */
    @Schema(description = "默认币种")
    private String defaultCurrency;

    /**
     * 成员数量
     */
    @Schema(description = "成员数量")
    private Integer memberCount;

    /**
     * 当前用户角色
     */
    @Schema(description = "当前用户角色")
    private GroupRole userRole;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;
}
