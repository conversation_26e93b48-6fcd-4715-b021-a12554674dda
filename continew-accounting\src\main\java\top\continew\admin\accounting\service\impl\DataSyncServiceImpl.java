package top.continew.admin.accounting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.accounting.mapper.DataSyncConfigMapper;
import top.continew.admin.accounting.mapper.DataSyncLogMapper;
import top.continew.admin.accounting.model.entity.DataSyncConfigDO;
import top.continew.admin.accounting.model.entity.DataSyncLogDO;
import top.continew.admin.accounting.model.query.DataSyncConfigQuery;
import top.continew.admin.accounting.model.query.DataSyncLogQuery;
import top.continew.admin.accounting.model.req.DataSyncConfigCreateReq;
import top.continew.admin.accounting.model.req.DataSyncConfigUpdateReq;
import top.continew.admin.accounting.model.req.DataSyncExecuteReq;
import top.continew.admin.accounting.model.resp.DataSyncConfigDetailResp;
import top.continew.admin.accounting.model.resp.DataSyncConfigListResp;
import top.continew.admin.accounting.model.resp.DataSyncLogResp;
import top.continew.admin.accounting.model.resp.DataSyncResultResp;
import top.continew.admin.accounting.service.DataSyncService;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.core.util.validate.CheckUtils;
import top.continew.starter.extension.crud.service.impl.BaseServiceImpl;
import top.continew.starter.security.util.SecurityUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据同步服务实现
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataSyncServiceImpl extends BaseServiceImpl<DataSyncConfigMapper, DataSyncConfigDO, DataSyncConfigListResp, DataSyncConfigDetailResp, DataSyncConfigQuery, DataSyncConfigCreateReq> implements DataSyncService {

    private final DataSyncLogMapper dataSyncLogMapper;
    
    // 运行中的同步任务
    private final Map<String, DataSyncResultResp> runningSyncs = new ConcurrentHashMap<>();
    
    // 同步进度缓存
    private final Map<String, Map<String, Object>> syncProgress = new ConcurrentHashMap<>();

    @Override
    protected void beforeAdd(DataSyncConfigCreateReq req) {
        // 验证配置名称唯一性
        CheckUtils.throwIf(baseMapper.existsByConfigName(req.getGroupId(), req.getConfigName(), null),
                "配置名称已存在");
        
        // 验证数据源和目标配置
        validateDataSourceConfig(req.getSourceType(), req.getSourceConfig());
        validateDataSourceConfig(req.getTargetType(), req.getTargetConfig());
        
        // 验证字段映射
        if (req.getFieldMapping() != null && !req.getFieldMapping().isEmpty()) {
            validateFieldMapping(req.getFieldMapping());
        }
    }

    @Override
    protected void beforeUpdate(DataSyncConfigUpdateReq req, Long id) {
        DataSyncConfigDO existing = this.getById(id);
        CheckUtils.throwIfNull(existing, "配置不存在");
        
        // 验证配置名称唯一性
        if (StrUtil.isNotBlank(req.getConfigName())) {
            CheckUtils.throwIf(baseMapper.existsByConfigName(existing.getGroupId(), req.getConfigName(), id),
                    "配置名称已存在");
        }
        
        // 验证数据源配置
        if (req.getSourceConfig() != null) {
            validateDataSourceConfig(existing.getSourceType(), req.getSourceConfig());
        }
        if (req.getTargetConfig() != null) {
            validateDataSourceConfig(existing.getTargetType(), req.getTargetConfig());
        }
        
        // 验证字段映射
        if (req.getFieldMapping() != null && !req.getFieldMapping().isEmpty()) {
            validateFieldMapping(req.getFieldMapping());
        }
    }

    @Override
    protected DataSyncConfigDO getEntityByCreateReq(DataSyncConfigCreateReq req) {
        DataSyncConfigDO entity = BeanUtil.copyProperties(req, DataSyncConfigDO.class);
        
        // 转换JSON字段
        if (req.getSourceConfig() != null) {
            entity.setSourceConfigJson(JSONUtil.toJsonStr(req.getSourceConfig()));
        }
        if (req.getTargetConfig() != null) {
            entity.setTargetConfigJson(JSONUtil.toJsonStr(req.getTargetConfig()));
        }
        if (req.getFieldMapping() != null) {
            entity.setFieldMappingJson(JSONUtil.toJsonStr(req.getFieldMapping()));
        }
        if (req.getFilterCondition() != null) {
            entity.setFilterConditionJson(JSONUtil.toJsonStr(req.getFilterCondition()));
        }
        if (req.getTransformRules() != null) {
            entity.setTransformRulesJson(JSONUtil.toJsonStr(req.getTransformRules()));
        }
        if (req.getSyncSettings() != null) {
            entity.setSyncSettingsJson(JSONUtil.toJsonStr(req.getSyncSettings()));
        }
        if (req.getCustomTags() != null) {
            entity.setCustomTagsJson(JSONUtil.toJsonStr(req.getCustomTags()));
        }
        
        // 设置初始状态
        entity.setSyncStatus("IDLE");
        entity.setLastSyncResult("NONE");
        entity.setRetryCount(0);
        
        return entity;
    }

    @Override
    protected void updateEntityByUpdateReq(DataSyncConfigUpdateReq req, DataSyncConfigDO entity) {
        BeanUtil.copyProperties(req, entity, "id", "groupId", "sourceType", "targetType", "dataType");
        
        // 转换JSON字段
        if (req.getSourceConfig() != null) {
            entity.setSourceConfigJson(JSONUtil.toJsonStr(req.getSourceConfig()));
        }
        if (req.getTargetConfig() != null) {
            entity.setTargetConfigJson(JSONUtil.toJsonStr(req.getTargetConfig()));
        }
        if (req.getFieldMapping() != null) {
            entity.setFieldMappingJson(JSONUtil.toJsonStr(req.getFieldMapping()));
        }
        if (req.getFilterCondition() != null) {
            entity.setFilterConditionJson(JSONUtil.toJsonStr(req.getFilterCondition()));
        }
        if (req.getTransformRules() != null) {
            entity.setTransformRulesJson(JSONUtil.toJsonStr(req.getTransformRules()));
        }
        if (req.getSyncSettings() != null) {
            entity.setSyncSettingsJson(JSONUtil.toJsonStr(req.getSyncSettings()));
        }
        if (req.getCustomTags() != null) {
            entity.setCustomTagsJson(JSONUtil.toJsonStr(req.getCustomTags()));
        }
    }

    @Override
    protected DataSyncConfigDetailResp getDetailResp(DataSyncConfigDO entity) {
        DataSyncConfigDetailResp resp = BeanUtil.copyProperties(entity, DataSyncConfigDetailResp.class);
        
        // 转换JSON字段
        if (StrUtil.isNotBlank(entity.getSourceConfigJson())) {
            resp.setSourceConfig(JSONUtil.toBean(entity.getSourceConfigJson(), Map.class));
        }
        if (StrUtil.isNotBlank(entity.getTargetConfigJson())) {
            resp.setTargetConfig(JSONUtil.toBean(entity.getTargetConfigJson(), Map.class));
        }
        if (StrUtil.isNotBlank(entity.getFieldMappingJson())) {
            resp.setFieldMapping(JSONUtil.toBean(entity.getFieldMappingJson(), Map.class));
        }
        if (StrUtil.isNotBlank(entity.getFilterConditionJson())) {
            resp.setFilterCondition(JSONUtil.toBean(entity.getFilterConditionJson(), Map.class));
        }
        if (StrUtil.isNotBlank(entity.getTransformRulesJson())) {
            resp.setTransformRules(JSONUtil.toBean(entity.getTransformRulesJson(), Map.class));
        }
        if (StrUtil.isNotBlank(entity.getSyncSettingsJson())) {
            resp.setSyncSettings(JSONUtil.toBean(entity.getSyncSettingsJson(), Map.class));
        }
        if (StrUtil.isNotBlank(entity.getSyncStatsJson())) {
            resp.setSyncStats(JSONUtil.toBean(entity.getSyncStatsJson(), Map.class));
        }
        if (StrUtil.isNotBlank(entity.getCustomTagsJson())) {
            resp.setCustomTags(JSONUtil.toBean(entity.getCustomTagsJson(), Map.class));
        }
        
        return resp;
    }

    @Override
    public void updateConfig(DataSyncConfigUpdateReq req, Long id) {
        this.update(req, id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableConfig(Long id) {
        DataSyncConfigDO config = this.getById(id);
        CheckUtils.throwIfNull(config, "配置不存在");
        
        config.setEnabled(true);
        this.updateById(config);
        
        log.info("数据同步配置已启用: configId={}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableConfig(Long id) {
        DataSyncConfigDO config = this.getById(id);
        CheckUtils.throwIfNull(config, "配置不存在");
        
        config.setEnabled(false);
        this.updateById(config);
        
        log.info("数据同步配置已禁用: configId={}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long copyConfig(Long id, String newName) {
        DataSyncConfigDO original = this.getById(id);
        CheckUtils.throwIfNull(original, "原配置不存在");
        
        // 验证新名称唯一性
        CheckUtils.throwIf(baseMapper.existsByConfigName(original.getGroupId(), newName, null),
                "配置名称已存在");
        
        DataSyncConfigDO newConfig = BeanUtil.copyProperties(original, DataSyncConfigDO.class);
        newConfig.setId(null);
        newConfig.setConfigName(newName);
        newConfig.setConfigDescription(original.getConfigDescription() + " (副本)");
        newConfig.setEnabled(false);
        newConfig.setSyncStatus("IDLE");
        newConfig.setLastSyncResult("NONE");
        newConfig.setLastSyncTime(null);
        newConfig.setNextSyncTime(null);
        newConfig.setErrorMessage(null);
        newConfig.setRetryCount(0);
        newConfig.setSyncStatsJson(null);
        newConfig.setCreateTime(LocalDateTime.now());
        newConfig.setUpdateTime(LocalDateTime.now());
        
        this.save(newConfig);
        
        log.info("数据同步配置复制成功: originalId={}, newId={}, newName={}", id, newConfig.getId(), newName);
        return newConfig.getId();
    }

    @Override
    public Map<String, Object> testConnection(Long id) {
        DataSyncConfigDO config = this.getById(id);
        CheckUtils.throwIfNull(config, "配置不存在");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // TODO: 实现具体的连接测试逻辑
            // 根据数据源类型调用相应的适配器进行连接测试
            
            result.put("success", true);
            result.put("message", "连接测试成功");
            result.put("testTime", LocalDateTime.now());
            
        } catch (Exception e) {
            log.error("连接测试失败: configId={}, error={}", id, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "连接测试失败: " + e.getMessage());
            result.put("testTime", LocalDateTime.now());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> validateConfig(Long id) {
        DataSyncConfigDO config = this.getById(id);
        CheckUtils.throwIfNull(config, "配置不存在");
        
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        
        try {
            // 验证数据源配置
            Map<String, Object> sourceConfig = JSONUtil.toBean(config.getSourceConfigJson(), Map.class);
            Map<String, Object> targetConfig = JSONUtil.toBean(config.getTargetConfigJson(), Map.class);
            
            validateDataSourceConfig(config.getSourceType(), sourceConfig);
            validateDataSourceConfig(config.getTargetType(), targetConfig);
            
            // 验证字段映射
            if (StrUtil.isNotBlank(config.getFieldMappingJson())) {
                Map<String, Object> fieldMapping = JSONUtil.toBean(config.getFieldMappingJson(), Map.class);
                validateFieldMapping(fieldMapping);
            }
            
            // 验证同步设置
            if (StrUtil.isNotBlank(config.getSyncSettingsJson())) {
                Map<String, Object> syncSettings = JSONUtil.toBean(config.getSyncSettingsJson(), Map.class);
                validateSyncSettings(syncSettings);
            }
            
            result.put("valid", errors.isEmpty());
            result.put("errors", errors);
            result.put("warnings", warnings);
            result.put("validateTime", LocalDateTime.now());
            
        } catch (Exception e) {
            log.error("配置验证失败: configId={}, error={}", id, e.getMessage(), e);
            errors.add("配置验证异常: " + e.getMessage());
            result.put("valid", false);
            result.put("errors", errors);
            result.put("warnings", warnings);
            result.put("validateTime", LocalDateTime.now());
        }
        
        return result;
    }

    /**
     * 验证数据源配置
     */
    private void validateDataSourceConfig(String sourceType, Map<String, Object> config) {
        if (config == null || config.isEmpty()) {
            throw new BusinessException("数据源配置不能为空");
        }
        
        switch (sourceType) {
            case "DATABASE":
                validateDatabaseConfig(config);
                break;
            case "GOOGLE_SHEETS":
                validateGoogleSheetsConfig(config);
                break;
            case "API":
                validateApiConfig(config);
                break;
            case "WEBHOOK":
                validateWebhookConfig(config);
                break;
            default:
                throw new BusinessException("不支持的数据源类型: " + sourceType);
        }
    }

    /**
     * 验证数据库配置
     */
    private void validateDatabaseConfig(Map<String, Object> config) {
        CheckUtils.throwIfBlank((String) config.get("tableName"), "表名不能为空");
    }

    /**
     * 验证Google Sheets配置
     */
    private void validateGoogleSheetsConfig(Map<String, Object> config) {
        CheckUtils.throwIfBlank((String) config.get("spreadsheetId"), "电子表格ID不能为空");
        CheckUtils.throwIfBlank((String) config.get("sheetName"), "工作表名称不能为空");
    }

    /**
     * 验证API配置
     */
    private void validateApiConfig(Map<String, Object> config) {
        CheckUtils.throwIfBlank((String) config.get("url"), "API地址不能为空");
        CheckUtils.throwIfBlank((String) config.get("method"), "请求方法不能为空");
    }

    /**
     * 验证Webhook配置
     */
    private void validateWebhookConfig(Map<String, Object> config) {
        CheckUtils.throwIfBlank((String) config.get("url"), "Webhook地址不能为空");
    }

    /**
     * 验证字段映射
     */
    private void validateFieldMapping(Map<String, Object> fieldMapping) {
        if (fieldMapping == null || fieldMapping.isEmpty()) {
            return;
        }
        
        // TODO: 实现字段映射验证逻辑
        // 验证映射规则的正确性
    }

    /**
     * 验证同步设置
     */
    private void validateSyncSettings(Map<String, Object> syncSettings) {
        if (syncSettings == null || syncSettings.isEmpty()) {
            return;
        }

        // TODO: 实现同步设置验证逻辑
        // 验证批量大小、超时时间等参数的合理性
    }

    // ==================== 同步执行 ====================

    @Override
    public DataSyncResultResp executeSync(DataSyncExecuteReq req) {
        DataSyncConfigDO config = this.getById(req.getConfigId());
        CheckUtils.throwIfNull(config, "配置不存在");
        CheckUtils.throwIf(!config.getEnabled(), "配置未启用");

        // 检查是否有正在运行的同步
        List<DataSyncLogDO> runningLogs = dataSyncLogMapper.selectRunningSync(req.getConfigId());
        CheckUtils.throwIf(CollUtil.isNotEmpty(runningLogs), "存在正在运行的同步任务");

        String syncId = generateSyncId(req.getConfigId());

        if (req.getAsync() != null && req.getAsync()) {
            // 异步执行
            executeSyncAsync(req);

            DataSyncResultResp result = new DataSyncResultResp();
            result.setSyncId(syncId);
            result.setConfigId(req.getConfigId());
            result.setStatus("PENDING");
            result.setSyncType(req.getSyncType());
            result.setSyncDirection(req.getSyncDirection() != null ? req.getSyncDirection() : config.getSyncDirection());
            result.setStartTime(LocalDateTime.now());

            return result;
        } else {
            // 同步执行
            return doExecuteSync(config, req, syncId);
        }
    }

    @Override
    @Async("dataSyncExecutor")
    public String executeSyncAsync(DataSyncExecuteReq req) {
        DataSyncConfigDO config = this.getById(req.getConfigId());
        String syncId = generateSyncId(req.getConfigId());

        try {
            doExecuteSync(config, req, syncId);
        } catch (Exception e) {
            log.error("异步同步执行失败: configId={}, syncId={}, error={}", req.getConfigId(), syncId, e.getMessage(), e);
        }

        return syncId;
    }

    @Override
    public Boolean stopSync(String syncId) {
        try {
            // 从运行中的同步任务中移除
            DataSyncResultResp runningSync = runningSyncs.remove(syncId);
            if (runningSync != null) {
                // 更新同步状态为已取消
                dataSyncLogMapper.updateSyncStatus(syncId, "CANCELLED", LocalDateTime.now(),
                        System.currentTimeMillis() - runningSync.getStartTime().atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli(),
                        "用户手动取消", "USER_CANCELLED");

                log.info("同步任务已停止: syncId={}", syncId);
                return true;
            }

            return false;
        } catch (Exception e) {
            log.error("停止同步任务失败: syncId={}, error={}", syncId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public DataSyncResultResp retrySync(String syncId) {
        DataSyncLogDO originalLog = dataSyncLogMapper.selectBySyncId(syncId);
        CheckUtils.throwIfNull(originalLog, "原始同步记录不存在");

        DataSyncConfigDO config = this.getById(originalLog.getConfigId());
        CheckUtils.throwIfNull(config, "配置不存在");

        // 创建重试请求
        DataSyncExecuteReq retryReq = new DataSyncExecuteReq();
        retryReq.setConfigId(originalLog.getConfigId());
        retryReq.setSyncType(originalLog.getSyncType());
        retryReq.setSyncDirection(originalLog.getSyncDirection());
        retryReq.setForceSync(true);
        retryReq.setAsync(false);

        String newSyncId = generateSyncId(originalLog.getConfigId());
        DataSyncResultResp result = doExecuteSync(config, retryReq, newSyncId);

        // 标记为重试
        dataSyncLogMapper.updateSyncDetails(newSyncId, null, null, null, null);

        log.info("同步任务重试完成: originalSyncId={}, newSyncId={}", syncId, newSyncId);
        return result;
    }

    @Override
    public Map<String, Object> getSyncStatus(String syncId) {
        DataSyncLogDO log = dataSyncLogMapper.selectBySyncId(syncId);
        CheckUtils.throwIfNull(log, "同步记录不存在");

        Map<String, Object> status = new HashMap<>();
        status.put("syncId", syncId);
        status.put("configId", log.getConfigId());
        status.put("status", log.getStatus());
        status.put("startTime", log.getStartTime());
        status.put("endTime", log.getEndTime());
        status.put("durationMs", log.getDurationMs());
        status.put("recordsProcessed", log.getRecordsProcessed());
        status.put("recordsSuccess", log.getRecordsSuccess());
        status.put("recordsFailed", log.getRecordsFailed());
        status.put("errorMessage", log.getErrorMessage());

        return status;
    }

    @Override
    public Map<String, Object> getSyncProgress(String syncId) {
        Map<String, Object> progress = syncProgress.get(syncId);
        if (progress == null) {
            progress = new HashMap<>();
            progress.put("syncId", syncId);
            progress.put("progress", 0);
            progress.put("message", "同步进度信息不存在");
        }
        return progress;
    }

    /**
     * 执行同步
     */
    private DataSyncResultResp doExecuteSync(DataSyncConfigDO config, DataSyncExecuteReq req, String syncId) {
        LocalDateTime startTime = LocalDateTime.now();
        DataSyncResultResp result = new DataSyncResultResp();
        result.setSyncId(syncId);
        result.setConfigId(config.getId());
        result.setStatus("RUNNING");
        result.setSyncType(req.getSyncType());
        result.setSyncDirection(req.getSyncDirection() != null ? req.getSyncDirection() : config.getSyncDirection());
        result.setStartTime(startTime);

        // 添加到运行中的同步任务
        runningSyncs.put(syncId, result);

        // 创建同步日志
        DataSyncLogDO syncLog = createSyncLog(config, req, syncId, startTime);
        dataSyncLogMapper.insert(syncLog);

        try {
            // TODO: 实现具体的同步逻辑
            // 1. 根据同步类型和方向执行同步
            // 2. 处理数据映射和转换
            // 3. 处理冲突解决
            // 4. 更新同步进度

            // 模拟同步执行
            Thread.sleep(1000); // 模拟同步耗时

            // 更新结果
            LocalDateTime endTime = LocalDateTime.now();
            long duration = endTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli() -
                           startTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();

            result.setStatus("SUCCESS");
            result.setEndTime(endTime);
            result.setDurationMs(duration);
            result.setRecordsProcessed(100);
            result.setRecordsSuccess(95);
            result.setRecordsFailed(3);
            result.setRecordsSkipped(2);
            result.setRecordsConflict(0);
            result.setSuccessRate(95.0);

            // 更新同步日志
            dataSyncLogMapper.updateSyncStatus(syncId, "SUCCESS", endTime, duration, null, null);
            dataSyncLogMapper.updateSyncResult(syncId, 100, 95, 3, 2, 0);

            // 更新配置状态
            baseMapper.updateSyncStatus(config.getId(), "SUCCESS", "SUCCESS", LocalDateTime.now(),
                    calculateNextSyncTime(config), null, 0);

            log.info("同步执行成功: configId={}, syncId={}, duration={}ms", config.getId(), syncId, duration);

        } catch (Exception e) {
            log.error("同步执行失败: configId={}, syncId={}, error={}", config.getId(), syncId, e.getMessage(), e);

            LocalDateTime endTime = LocalDateTime.now();
            long duration = endTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli() -
                           startTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();

            result.setStatus("FAILED");
            result.setEndTime(endTime);
            result.setDurationMs(duration);
            result.setErrorMessage(e.getMessage());
            result.setErrorCode("SYNC_ERROR");

            // 更新同步日志
            dataSyncLogMapper.updateSyncStatus(syncId, "FAILED", endTime, duration, e.getMessage(), "SYNC_ERROR");

            // 更新配置状态
            baseMapper.updateSyncStatus(config.getId(), "FAILED", "FAILED", LocalDateTime.now(),
                    calculateNextSyncTime(config), e.getMessage(), config.getRetryCount() + 1);

        } finally {
            // 从运行中的同步任务中移除
            runningSyncs.remove(syncId);
            syncProgress.remove(syncId);
        }

        return result;
    }

    /**
     * 生成同步ID
     */
    private String generateSyncId(Long configId) {
        return "sync_" + configId + "_" + DateUtil.format(LocalDateTime.now(), "yyyyMMdd_HHmmss") + "_" + IdUtil.fastSimpleUUID().substring(0, 6);
    }

    /**
     * 创建同步日志
     */
    private DataSyncLogDO createSyncLog(DataSyncConfigDO config, DataSyncExecuteReq req, String syncId, LocalDateTime startTime) {
        DataSyncLogDO syncLog = new DataSyncLogDO();
        syncLog.setConfigId(config.getId());
        syncLog.setGroupId(config.getGroupId());
        syncLog.setSyncId(syncId);
        syncLog.setSyncType(req.getSyncType());
        syncLog.setSyncDirection(req.getSyncDirection() != null ? req.getSyncDirection() : config.getSyncDirection());
        syncLog.setOperationType("SYNC");
        syncLog.setDataType(config.getDataType());
        syncLog.setStatus("RUNNING");
        syncLog.setStartTime(startTime);
        syncLog.setTriggerType("MANUAL");
        syncLog.setTriggerUserId(SecurityUtils.getUserId());
        syncLog.setIsRetry(false);
        syncLog.setRetryCount(0);

        return syncLog;
    }

    /**
     * 计算下次同步时间
     */
    private LocalDateTime calculateNextSyncTime(DataSyncConfigDO config) {
        if (StrUtil.isBlank(config.getSyncFrequency()) || "MANUAL".equals(config.getSyncFrequency())) {
            return null;
        }

        LocalDateTime now = LocalDateTime.now();
        switch (config.getSyncFrequency()) {
            case "MINUTELY":
                return now.plusMinutes(1);
            case "HOURLY":
                return now.plusHours(1);
            case "DAILY":
                return now.plusDays(1);
            case "WEEKLY":
                return now.plusWeeks(1);
            case "MONTHLY":
                return now.plusMonths(1);
            default:
                return null;
        }
    }

    // ==================== 同步日志管理 ====================

    @Override
    public IPage<DataSyncLogResp> getSyncLogs(DataSyncLogQuery query) {
        return dataSyncLogMapper.selectPageList(query.toPage(), query);
    }

    @Override
    public DataSyncLogResp getSyncLogDetail(Long logId) {
        DataSyncLogDO log = dataSyncLogMapper.selectById(logId);
        CheckUtils.throwIfNull(log, "同步日志不存在");

        DataSyncLogResp resp = BeanUtil.copyProperties(log, DataSyncLogResp.class);

        // 转换JSON字段
        if (StrUtil.isNotBlank(log.getErrorDetailsJson())) {
            resp.setErrorDetails(JSONUtil.toBean(log.getErrorDetailsJson(), List.class));
        }
        if (StrUtil.isNotBlank(log.getSyncDetailsJson())) {
            resp.setSyncDetails(JSONUtil.toBean(log.getSyncDetailsJson(), Map.class));
        }
        if (StrUtil.isNotBlank(log.getConflictDetailsJson())) {
            resp.setConflictDetails(JSONUtil.toBean(log.getConflictDetailsJson(), List.class));
        }
        if (StrUtil.isNotBlank(log.getPerformanceMetricsJson())) {
            resp.setPerformanceMetrics(JSONUtil.toBean(log.getPerformanceMetricsJson(), Map.class));
        }

        return resp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSyncLogs(List<Long> logIds) {
        CheckUtils.throwIf(CollUtil.isEmpty(logIds), "日志ID列表不能为空");

        Integer deletedCount = dataSyncLogMapper.batchDeleteLogs(logIds);
        log.info("批量删除同步日志: count={}, logIds={}", deletedCount, logIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cleanExpiredLogs(Integer retentionDays) {
        if (retentionDays == null || retentionDays <= 0) {
            retentionDays = 30; // 默认保留30天
        }

        Integer deletedCount = dataSyncLogMapper.deleteExpiredLogs(retentionDays);
        log.info("清理过期同步日志: retentionDays={}, deletedCount={}", retentionDays, deletedCount);
    }

    // ==================== 统计分析 ====================

    @Override
    public Map<String, Object> getConfigStatistics(Long groupId) {
        return baseMapper.selectConfigStatistics(groupId);
    }

    @Override
    public Map<String, Object> getSyncStatistics(Long configId, String startDate, String endDate) {
        return dataSyncLogMapper.selectLogStatistics(configId, startDate, endDate);
    }

    @Override
    public List<Map<String, Object>> getSyncTrend(Long configId, String startDate, String endDate, String groupBy) {
        return dataSyncLogMapper.selectSyncTrend(configId, startDate, endDate, groupBy);
    }

    @Override
    public Map<String, Object> getPerformanceAnalysis(Long configId, String startDate, String endDate) {
        return dataSyncLogMapper.selectPerformanceAnalysis(configId, startDate, endDate);
    }

    @Override
    public List<Map<String, Object>> getErrorAnalysis(Long configId, String startDate, String endDate) {
        return dataSyncLogMapper.selectErrorAnalysis(configId, startDate, endDate);
    }

    @Override
    public Map<String, Object> getHealthStatus(Long configId) {
        return baseMapper.selectHealthStatus(configId);
    }

    @Override
    public Map<String, Object> getSyncReport(Long configId, String startDate, String endDate) {
        return dataSyncLogMapper.selectSyncReportData(configId, startDate, endDate);
    }

    // ==================== 批量操作 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchEnableConfigs(List<Long> configIds) {
        CheckUtils.throwIf(CollUtil.isEmpty(configIds), "配置ID列表不能为空");

        baseMapper.batchEnable(configIds);
        log.info("批量启用配置: configIds={}", configIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDisableConfigs(List<Long> configIds) {
        CheckUtils.throwIf(CollUtil.isEmpty(configIds), "配置ID列表不能为空");

        baseMapper.batchDisable(configIds);
        log.info("批量禁用配置: configIds={}", configIds);
    }

    @Override
    public List<DataSyncResultResp> batchExecuteSync(List<Long> configIds, String syncType) {
        CheckUtils.throwIf(CollUtil.isEmpty(configIds), "配置ID列表不能为空");

        List<DataSyncResultResp> results = new ArrayList<>();

        for (Long configId : configIds) {
            try {
                DataSyncExecuteReq req = new DataSyncExecuteReq();
                req.setConfigId(configId);
                req.setSyncType(syncType);
                req.setAsync(true);

                DataSyncResultResp result = this.executeSync(req);
                results.add(result);

            } catch (Exception e) {
                log.error("批量同步执行失败: configId={}, error={}", configId, e.getMessage(), e);

                DataSyncResultResp errorResult = new DataSyncResultResp();
                errorResult.setConfigId(configId);
                errorResult.setStatus("FAILED");
                errorResult.setErrorMessage(e.getMessage());
                results.add(errorResult);
            }
        }

        log.info("批量执行同步: configIds={}, results={}", configIds, results.size());
        return results;
    }

    // ==================== 增量同步 ====================

    @Override
    public DataSyncResultResp executeIncrementalSync(Long configId, LocalDateTime lastSyncTime) {
        DataSyncExecuteReq req = new DataSyncExecuteReq();
        req.setConfigId(configId);
        req.setSyncType("INCREMENTAL");
        req.setLastSyncTime(lastSyncTime);
        req.setAsync(false);

        return this.executeSync(req);
    }

    @Override
    public Map<String, Object> detectDataChanges(Long configId, LocalDateTime lastSyncTime) {
        DataSyncConfigDO config = this.getById(configId);
        CheckUtils.throwIfNull(config, "配置不存在");

        Map<String, Object> changes = new HashMap<>();

        try {
            // TODO: 实现数据变更检测逻辑
            // 根据数据源类型和最后同步时间检测数据变更

            changes.put("hasChanges", false);
            changes.put("changeCount", 0);
            changes.put("lastCheckTime", LocalDateTime.now());

        } catch (Exception e) {
            log.error("数据变更检测失败: configId={}, error={}", configId, e.getMessage(), e);
            changes.put("hasChanges", false);
            changes.put("error", e.getMessage());
        }

        return changes;
    }

    // ==================== 冲突解决 ====================

    @Override
    public List<Map<String, Object>> getConflicts(String syncId) {
        DataSyncLogDO log = dataSyncLogMapper.selectBySyncId(syncId);
        CheckUtils.throwIfNull(log, "同步记录不存在");

        if (StrUtil.isNotBlank(log.getConflictDetailsJson())) {
            return JSONUtil.toBean(log.getConflictDetailsJson(), List.class);
        }

        return new ArrayList<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean resolveConflict(String syncId, String conflictId, String resolution, Map<String, Object> resolvedData) {
        try {
            // TODO: 实现冲突解决逻辑
            // 根据解决策略处理冲突数据

            log.info("冲突解决成功: syncId={}, conflictId={}, resolution={}", syncId, conflictId, resolution);
            return true;

        } catch (Exception e) {
            log.error("冲突解决失败: syncId={}, conflictId={}, error={}", syncId, conflictId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchResolveConflicts(String syncId, String resolution) {
        try {
            // TODO: 实现批量冲突解决逻辑

            log.info("批量冲突解决成功: syncId={}, resolution={}", syncId, resolution);
            return 0;

        } catch (Exception e) {
            log.error("批量冲突解决失败: syncId={}, error={}", syncId, e.getMessage(), e);
            return 0;
        }
    }
}
