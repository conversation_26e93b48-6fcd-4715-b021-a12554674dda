package top.continew.admin.accounting.service;

import top.continew.admin.accounting.model.query.BudgetQuery;
import top.continew.admin.accounting.model.query.AuditQuery;
import top.continew.admin.accounting.model.query.AlertQuery;
import top.continew.admin.accounting.model.query.CostAnalysisQuery;
import top.continew.admin.accounting.model.req.*;
import top.continew.admin.accounting.model.resp.*;
import top.continew.starter.extension.crud.model.query.PageQuery;

import java.util.List;

/**
 * 财务管理服务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface FinancialManagementService {

    // ==================== 预算管理 ====================

    /**
     * 创建预算
     *
     * @param req 预算创建请求
     * @return 预算ID
     */
    String createBudget(BudgetCreateReq req);

    /**
     * 更新预算
     *
     * @param budgetId 预算ID
     * @param req      预算更新请求
     */
    void updateBudget(String budgetId, BudgetUpdateReq req);

    /**
     * 删除预算
     *
     * @param budgetId 预算ID
     */
    void deleteBudget(String budgetId);

    /**
     * 获取预算详情
     *
     * @param budgetId 预算ID
     * @return 预算详情
     */
    BudgetDetailResp getBudgetDetail(String budgetId);

    /**
     * 分页查询预算列表
     *
     * @param query     查询条件
     * @param pageQuery 分页条件
     * @return 预算列表
     */
    List<BudgetListResp> getBudgetList(BudgetQuery query, PageQuery pageQuery);

    /**
     * 获取预算执行情况
     *
     * @param budgetId 预算ID
     * @return 预算执行情况
     */
    BudgetExecutionResp getBudgetExecution(String budgetId);

    /**
     * 获取预算分析报告
     *
     * @param groupId 群组ID
     * @param period  分析周期
     * @return 预算分析报告
     */
    BudgetAnalysisResp getBudgetAnalysis(Long groupId, String period);

    // ==================== 账单审核 ====================

    /**
     * 提交账单审核
     *
     * @param req 审核提交请求
     * @return 审核ID
     */
    String submitAudit(AuditSubmitReq req);

    /**
     * 审核账单
     *
     * @param auditId 审核ID
     * @param req     审核请求
     */
    void auditTransaction(String auditId, AuditReq req);

    /**
     * 批量审核账单
     *
     * @param req 批量审核请求
     */
    void batchAuditTransactions(BatchAuditReq req);

    /**
     * 获取审核详情
     *
     * @param auditId 审核ID
     * @return 审核详情
     */
    AuditDetailResp getAuditDetail(String auditId);

    /**
     * 分页查询审核列表
     *
     * @param query     查询条件
     * @param pageQuery 分页条件
     * @return 审核列表
     */
    List<AuditListResp> getAuditList(AuditQuery query, PageQuery pageQuery);

    /**
     * 获取审核统计
     *
     * @param groupId 群组ID
     * @param period  统计周期
     * @return 审核统计
     */
    AuditStatisticsResp getAuditStatistics(Long groupId, String period);

    // ==================== 财务预警 ====================

    /**
     * 创建预警规则
     *
     * @param req 预警规则创建请求
     * @return 规则ID
     */
    String createAlertRule(AlertRuleCreateReq req);

    /**
     * 更新预警规则
     *
     * @param ruleId 规则ID
     * @param req    预警规则更新请求
     */
    void updateAlertRule(String ruleId, AlertRuleUpdateReq req);

    /**
     * 删除预警规则
     *
     * @param ruleId 规则ID
     */
    void deleteAlertRule(String ruleId);

    /**
     * 获取预警规则列表
     *
     * @param groupId 群组ID
     * @return 预警规则列表
     */
    List<AlertRuleResp> getAlertRules(Long groupId);

    /**
     * 获取预警列表
     *
     * @param query     查询条件
     * @param pageQuery 分页条件
     * @return 预警列表
     */
    List<AlertResp> getAlerts(AlertQuery query, PageQuery pageQuery);

    /**
     * 处理预警
     *
     * @param alertId 预警ID
     * @param req     处理请求
     */
    void handleAlert(String alertId, AlertHandleReq req);

    /**
     * 执行预警检查
     *
     * @param groupId 群组ID
     * @return 检查结果
     */
    AlertCheckResp executeAlertCheck(Long groupId);

    // ==================== 成本分析 ====================

    /**
     * 获取成本分析报告
     *
     * @param query 查询条件
     * @return 成本分析报告
     */
    CostAnalysisResp getCostAnalysis(CostAnalysisQuery query);

    /**
     * 获取成本趋势分析
     *
     * @param groupId 群组ID
     * @param period  分析周期
     * @return 成本趋势分析
     */
    CostTrendResp getCostTrend(Long groupId, String period);

    /**
     * 获取成本对比分析
     *
     * @param groupId     群组ID
     * @param compareType 对比类型
     * @param period      对比周期
     * @return 成本对比分析
     */
    CostComparisonResp getCostComparison(Long groupId, String compareType, String period);

    /**
     * 获取成本分解分析
     *
     * @param groupId   群组ID
     * @param dimension 分解维度
     * @param period    分析周期
     * @return 成本分解分析
     */
    CostBreakdownResp getCostBreakdown(Long groupId, String dimension, String period);

    /**
     * 获取成本效益分析
     *
     * @param groupId 群组ID
     * @param period  分析周期
     * @return 成本效益分析
     */
    CostBenefitResp getCostBenefit(Long groupId, String period);

    // ==================== 财务报表 ====================

    /**
     * 生成财务报表
     *
     * @param req 报表生成请求
     * @return 报表ID
     */
    String generateFinancialReport(FinancialReportReq req);

    /**
     * 获取财务报表
     *
     * @param reportId 报表ID
     * @return 财务报表
     */
    FinancialReportResp getFinancialReport(String reportId);

    /**
     * 获取财务报表列表
     *
     * @param groupId 群组ID
     * @return 报表列表
     */
    List<FinancialReportListResp> getFinancialReports(Long groupId);

    /**
     * 导出财务报表
     *
     * @param reportId 报表ID
     * @param format   导出格式
     * @return 导出文件路径
     */
    String exportFinancialReport(String reportId, String format);

    // ==================== 财务指标 ====================

    /**
     * 获取财务指标
     *
     * @param groupId 群组ID
     * @param period  统计周期
     * @return 财务指标
     */
    FinancialMetricsResp getFinancialMetrics(Long groupId, String period);

    /**
     * 获取财务健康度评分
     *
     * @param groupId 群组ID
     * @return 健康度评分
     */
    FinancialHealthResp getFinancialHealth(Long groupId);

    /**
     * 获取财务建议
     *
     * @param groupId 群组ID
     * @return 财务建议
     */
    List<FinancialAdviceResp> getFinancialAdvice(Long groupId);
}
