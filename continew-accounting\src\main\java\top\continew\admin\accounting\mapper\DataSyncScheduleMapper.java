package top.continew.admin.accounting.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.accounting.model.entity.DataSyncScheduleDO;
import top.continew.admin.accounting.model.query.DataSyncScheduleQuery;
import top.continew.admin.accounting.model.resp.DataSyncScheduleListResp;
import top.continew.starter.extension.crud.mapper.BaseMapper;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 数据同步计划 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface DataSyncScheduleMapper extends BaseMapper<DataSyncScheduleDO> {

    /**
     * 分页查询
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<DataSyncScheduleListResp> selectPageList(Page<DataSyncScheduleListResp> page, @Param("query") DataSyncScheduleQuery query);

    /**
     * 获取待执行的计划
     *
     * @param currentTime 当前时间
     * @param limit 限制数量
     * @return 待执行计划列表
     */
    List<DataSyncScheduleDO> selectPendingSchedules(@Param("currentTime") LocalDateTime currentTime, @Param("limit") Integer limit);

    /**
     * 根据配置ID查询计划
     *
     * @param configId 配置ID
     * @return 计划列表
     */
    List<DataSyncScheduleDO> selectByConfigId(@Param("configId") Long configId);

    /**
     * 更新执行状态
     *
     * @param scheduleId 计划ID
     * @param status 执行状态
     * @param errorMessage 错误信息
     * @param executionTime 执行时间
     */
    void updateExecutionStatus(@Param("scheduleId") Long scheduleId, 
                              @Param("status") String status, 
                              @Param("errorMessage") String errorMessage,
                              @Param("executionTime") LocalDateTime executionTime);

    /**
     * 更新下次执行时间
     *
     * @param scheduleId 计划ID
     * @param nextExecutionTime 下次执行时间
     */
    void updateNextExecutionTime(@Param("scheduleId") Long scheduleId, @Param("nextExecutionTime") LocalDateTime nextExecutionTime);

    /**
     * 批量更新启用状态
     *
     * @param scheduleIds 计划ID列表
     * @param enabled 是否启用
     */
    void batchUpdateEnabled(@Param("scheduleIds") List<Long> scheduleIds, @Param("enabled") Boolean enabled);

    /**
     * 获取计划健康状态
     *
     * @param groupId 群组ID
     * @return 健康状态列表
     */
    List<Map<String, Object>> selectScheduleHealthStatus(@Param("groupId") Long groupId);

    /**
     * 获取失败计划
     *
     * @param groupId 群组ID
     * @param hours 小时数
     * @return 失败计划列表
     */
    List<Map<String, Object>> selectFailedSchedules(@Param("groupId") Long groupId, @Param("hours") Integer hours);

    /**
     * 获取超时计划
     *
     * @param groupId 群组ID
     * @return 超时计划列表
     */
    List<Map<String, Object>> selectTimeoutSchedules(@Param("groupId") Long groupId);

    /**
     * 获取执行历史
     *
     * @param scheduleId 计划ID
     * @param limit 限制数量
     * @return 执行历史
     */
    List<Map<String, Object>> selectExecutionHistory(@Param("scheduleId") Long scheduleId, @Param("limit") Integer limit);

    /**
     * 获取执行统计
     *
     * @param scheduleId 计划ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 执行统计
     */
    Map<String, Object> selectExecutionStatistics(@Param("scheduleId") Long scheduleId, 
                                                  @Param("startDate") String startDate, 
                                                  @Param("endDate") String endDate);

    /**
     * 清理执行历史
     *
     * @param scheduleId 计划ID
     * @param retentionDays 保留天数
     * @return 清理数量
     */
    Integer cleanupExecutionHistory(@Param("scheduleId") Long scheduleId, @Param("retentionDays") Integer retentionDays);

    /**
     * 获取计划统计
     *
     * @param groupId 群组ID
     * @return 统计信息
     */
    Map<String, Object> selectScheduleStatistics(@Param("groupId") Long groupId);

    /**
     * 获取执行趋势
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param groupBy 分组方式
     * @return 趋势数据
     */
    List<Map<String, Object>> selectExecutionTrends(@Param("groupId") Long groupId, 
                                                    @Param("startDate") String startDate, 
                                                    @Param("endDate") String endDate, 
                                                    @Param("groupBy") String groupBy);

    /**
     * 获取性能分析
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 性能分析数据
     */
    List<Map<String, Object>> selectPerformanceAnalysis(@Param("groupId") Long groupId, 
                                                        @Param("startDate") String startDate, 
                                                        @Param("endDate") String endDate);

}
