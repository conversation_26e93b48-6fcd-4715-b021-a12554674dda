package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.accounting.enums.TransactionType;

import jakarta.validation.constraints.*;
import java.io.Serial;
import java.io.Serializable;

/**
 * 分类创建请求参数
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "分类创建请求参数")
public class CategoryCreateReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    @NotNull(message = "群组ID不能为空")
    private Long groupId;

    /**
     * 分类名称
     */
    @Schema(description = "分类名称", example = "餐饮")
    @NotBlank(message = "分类名称不能为空")
    @Size(max = 50, message = "分类名称长度不能超过50个字符")
    private String name;

    /**
     * 父分类ID
     */
    @Schema(description = "父分类ID", example = "1")
    private Long parentId;

    /**
     * 分类类型
     */
    @Schema(description = "分类类型", example = "EXPENSE")
    @NotNull(message = "分类类型不能为空")
    private TransactionType type;

    /**
     * 图标
     */
    @Schema(description = "图标", example = "icon-food")
    @Size(max = 100, message = "图标长度不能超过100个字符")
    private String icon;

    /**
     * 颜色
     */
    @Schema(description = "颜色", example = "#FF5722")
    @Pattern(regexp = "^#[0-9A-Fa-f]{6}$", message = "颜色格式不正确")
    private String color;

    /**
     * 描述
     */
    @Schema(description = "描述", example = "餐饮相关支出")
    @Size(max = 200, message = "描述长度不能超过200个字符")
    private String description;

    /**
     * 排序
     */
    @Schema(description = "排序", example = "1")
    @Min(value = 0, message = "排序不能小于0")
    @Max(value = 9999, message = "排序不能大于9999")
    private Integer sort = 0;

    /**
     * 是否为默认分类
     */
    @Schema(description = "是否为默认分类", example = "false")
    private Boolean isDefault = false;
}
