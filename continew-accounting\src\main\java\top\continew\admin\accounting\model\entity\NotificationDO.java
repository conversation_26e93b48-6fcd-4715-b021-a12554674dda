package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.data.mybatis.base.BaseEntity;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 通知实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "acc_notification", autoResultMap = true)
public class NotificationDO extends BaseEntity {

    /**
     * 群组ID
     */
    private Long groupId;

    /**
     * 通知标题
     */
    private String title;

    /**
     * 通知内容
     */
    private String content;

    /**
     * 通知类型
     */
    private String notificationType;

    /**
     * 优先级
     */
    private String priority;

    /**
     * 发送渠道列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> channels;

    /**
     * 目标用户列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Long> targetUsers;

    /**
     * 目标群组列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Long> targetGroups;

    /**
     * 目标角色列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> targetRoles;

    /**
     * 通知状态
     */
    private String status;

    /**
     * 计划发送时间
     */
    private LocalDateTime scheduledTime;

    /**
     * 实际发送时间
     */
    private LocalDateTime sentTime;

    /**
     * 过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;

    /**
     * 重试间隔（秒）
     */
    private Integer retryInterval;

    /**
     * 发送结果
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> sendResult;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 模板ID
     */
    private Long templateId;

    /**
     * 模板参数
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> templateParams;

    /**
     * 附件列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> attachments;

    /**
     * 扩展属性
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> extraData;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 备注
     */
    private String remark;

}
