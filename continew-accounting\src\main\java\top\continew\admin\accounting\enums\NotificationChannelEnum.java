package top.continew.admin.accounting.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 通知渠道枚举
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Getter
@RequiredArgsConstructor
public enum NotificationChannelEnum {

    /**
     * 系统消息
     */
    SYSTEM_MESSAGE("SYSTEM_MESSAGE", "系统消息"),

    /**
     * 邮件
     */
    EMAIL("EMAIL", "邮件"),

    /**
     * 短信
     */
    SMS("SMS", "短信"),

    /**
     * 推送通知
     */
    PUSH_NOTIFICATION("PUSH_NOTIFICATION", "推送通知"),

    /**
     * Telegram机器人
     */
    TELEGRAM_BOT("TELEGRAM_BOT", "Telegram机器人"),

    /**
     * Discord机器人
     */
    DISCORD_BOT("DISCORD_BOT", "Discord机器人"),

    /**
     * 微信机器人
     */
    WECHAT_BOT("WECHAT_BOT", "微信机器人"),

    /**
     * WebSocket实时通知
     */
    WEBSOCKET("WEBSOCKET", "WebSocket实时通知"),

    /**
     * 钉钉机器人
     */
    DINGTALK_BOT("DINGTALK_BOT", "钉钉机器人"),

    /**
     * 企业微信机器人
     */
    WEWORK_BOT("WEWORK_BOT", "企业微信机器人"),

    /**
     * Slack机器人
     */
    SLACK_BOT("SLACK_BOT", "Slack机器人");

    /**
     * 渠道代码
     */
    private final String code;

    /**
     * 渠道名称
     */
    private final String name;

    /**
     * 根据代码获取枚举
     *
     * @param code 渠道代码
     * @return 通知渠道枚举
     */
    public static NotificationChannelEnum getByCode(String code) {
        for (NotificationChannelEnum channel : values()) {
            if (channel.getCode().equals(code)) {
                return channel;
            }
        }
        return null;
    }

    /**
     * 是否为机器人渠道
     *
     * @return 是否为机器人渠道
     */
    public boolean isBotChannel() {
        return this == TELEGRAM_BOT || this == DISCORD_BOT || this == WECHAT_BOT 
               || this == DINGTALK_BOT || this == WEWORK_BOT || this == SLACK_BOT;
    }

    /**
     * 是否为即时通讯渠道
     *
     * @return 是否为即时通讯渠道
     */
    public boolean isInstantChannel() {
        return this == WEBSOCKET || this == SYSTEM_MESSAGE || isBotChannel();
    }

    /**
     * 是否需要外部配置
     *
     * @return 是否需要外部配置
     */
    public boolean requiresExternalConfig() {
        return this == EMAIL || this == SMS || isBotChannel();
    }

}
