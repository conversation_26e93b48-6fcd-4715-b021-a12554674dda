package top.continew.admin.bot.discord.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.dv8tion.jda.api.entities.Message;
import net.dv8tion.jda.api.events.interaction.command.SlashCommandInteractionEvent;
import net.dv8tion.jda.api.events.interaction.component.ButtonInteractionEvent;
import net.dv8tion.jda.api.events.interaction.component.StringSelectInteractionEvent;
import net.dv8tion.jda.api.events.message.MessageReceivedEvent;
import net.dv8tion.jda.api.interactions.commands.OptionMapping;
import net.dv8tion.jda.api.interactions.components.buttons.Button;
import net.dv8tion.jda.api.interactions.components.selections.StringSelectMenu;
import org.springframework.stereotype.Component;
import top.continew.admin.accounting.enums.PlatformType;
import top.continew.admin.accounting.model.entity.GroupDO;
import top.continew.admin.accounting.model.req.TransactionCreateReq;
import top.continew.admin.accounting.service.GroupService;
import top.continew.admin.accounting.service.TransactionService;
import top.continew.admin.bot.common.MessageFormatter;
import top.continew.admin.bot.discord.DiscordBotService;
import top.continew.admin.bot.model.dto.ParsedCommand;
import top.continew.admin.bot.service.CommandParser;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Discord命令处理器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DiscordCommandHandler {

    private final CommandParser commandParser;
    private final GroupService groupService;
    private final TransactionService transactionService;
    private final DiscordBotService discordBotService;
    private final MessageFormatter messageFormatter;

    /**
     * 处理Slash命令
     */
    public void handleSlashCommand(SlashCommandInteractionEvent event) {
        String commandName = event.getName();
        
        try {
            switch (commandName) {
                case "add" -> handleAddCommand(event);
                case "balance" -> handleBalanceCommand(event);
                case "history" -> handleHistoryCommand(event);
                case "stats" -> handleStatsCommand(event);
                case "group" -> handleGroupCommand(event);
                case "help" -> handleHelpCommand(event);
                case "settings" -> handleSettingsCommand(event);
                case "test" -> handleTestCommand(event);
                case "debug" -> handleDebugCommand(event);
                default -> {
                    event.reply("❌ 未知命令: " + commandName).setEphemeral(true).queue();
                }
            }
        } catch (Exception e) {
            log.error("处理Slash命令失败: {}", commandName, e);
            if (!event.isAcknowledged()) {
                event.reply("❌ 处理命令时发生错误: " + e.getMessage()).setEphemeral(true).queue();
            }
        }
    }

    /**
     * 处理文本命令
     */
    public void handleTextCommand(String channelId, Long userId, String username, String content, 
                                 boolean isPrivate, MessageReceivedEvent event) {
        try {
            // 移除命令前缀
            String command = content.substring(1);
            String[] parts = command.split(" ", 2);
            String commandName = parts[0].toLowerCase();
            
            switch (commandName) {
                case "help", "h" -> sendHelpMessage(channelId);
                case "ping" -> sendPingMessage(channelId);
                case "status" -> sendStatusMessage(channelId);
                default -> {
                    // 尝试解析为记账命令
                    handleAccountingMessage(channelId, userId, username, content, isPrivate, event);
                }
            }
        } catch (Exception e) {
            log.error("处理文本命令失败", e);
            discordBotService.sendMessage(channelId, "❌ 处理命令时发生错误: " + e.getMessage());
        }
    }

    /**
     * 处理记账消息
     */
    public void handleAccountingMessage(String channelId, Long userId, String username, String content, 
                                       boolean isPrivate, MessageReceivedEvent event) {
        try {
            // 解析记账命令
            ParsedCommand command = commandParser.parseCommand(content);
            if (command == null || !command.getValid()) {
                String errorMsg = command != null ? command.getErrorMessage() : "无法解析命令";
                discordBotService.sendMessage(channelId, 
                        messageFormatter.formatError(errorMsg, PlatformType.DISCORD) + "\n\n" + getQuickHelp());
                return;
            }

            // 验证群组
            GroupDO group = groupService.findByPlatformInfo(PlatformType.DISCORD, channelId);
            if (group == null) {
                sendGroupNotRegisteredMessage(channelId);
                return;
            }

            // 验证用户权限
            if (!groupService.isMember(group.getId(), userId)) {
                discordBotService.sendMessage(channelId, 
                        messageFormatter.formatError("您不是该群组成员，无法记账", PlatformType.DISCORD));
                return;
            }

            // 创建交易记录
            createTransaction(channelId, userId, username, command, group);

        } catch (Exception e) {
            log.error("处理记账消息失败", e);
            discordBotService.sendMessage(channelId, 
                    messageFormatter.formatError("处理记账消息时发生错误: " + e.getMessage(), PlatformType.DISCORD));
        }
    }

    /**
     * 处理按钮交互
     */
    public void handleButtonInteraction(ButtonInteractionEvent event) {
        String buttonId = event.getComponentId();
        String[] parts = buttonId.split(":");
        String action = parts[0];
        
        try {
            switch (action) {
                case "confirm_transaction" -> confirmTransaction(event, parts[1]);
                case "cancel_transaction" -> cancelTransaction(event, parts[1]);
                case "edit_transaction" -> editTransaction(event, parts[1]);
                case "delete_transaction" -> deleteTransaction(event, parts[1]);
                case "view_details" -> viewTransactionDetails(event, parts[1]);
                case "register_group" -> registerGroup(event);
                default -> {
                    event.reply("❌ 未知操作: " + action).setEphemeral(true).queue();
                }
            }
        } catch (Exception e) {
            log.error("处理按钮交互失败: {}", buttonId, e);
            if (!event.isAcknowledged()) {
                event.reply("❌ 处理操作时发生错误: " + e.getMessage()).setEphemeral(true).queue();
            }
        }
    }

    /**
     * 处理选择菜单交互
     */
    public void handleSelectMenuInteraction(StringSelectInteractionEvent event) {
        String selectId = event.getComponentId();
        List<String> values = event.getValues();
        
        try {
            switch (selectId) {
                case "category_select" -> handleCategorySelect(event, values);
                case "wallet_select" -> handleWalletSelect(event, values);
                case "period_select" -> handlePeriodSelect(event, values);
                default -> {
                    event.reply("❌ 未知选择: " + selectId).setEphemeral(true).queue();
                }
            }
        } catch (Exception e) {
            log.error("处理选择菜单交互失败: {}", selectId, e);
            if (!event.isAcknowledged()) {
                event.reply("❌ 处理操作时发生错误: " + e.getMessage()).setEphemeral(true).queue();
            }
        }
    }

    /**
     * 处理添加命令
     */
    private void handleAddCommand(SlashCommandInteractionEvent event) {
        OptionMapping amountOption = event.getOption("amount");
        OptionMapping descriptionOption = event.getOption("description");
        
        if (amountOption == null || descriptionOption == null) {
            event.reply("❌ 缺少必要参数").setEphemeral(true).queue();
            return;
        }

        String amountStr = amountOption.getAsString();
        String description = descriptionOption.getAsString();
        
        // 构建完整的记账命令
        StringBuilder commandBuilder = new StringBuilder();
        commandBuilder.append(amountStr).append(" ").append(description);
        
        // 添加可选参数
        OptionMapping categoryOption = event.getOption("category");
        if (categoryOption != null) {
            commandBuilder.append(" @").append(categoryOption.getAsString());
        }
        
        OptionMapping tagsOption = event.getOption("tags");
        if (tagsOption != null) {
            String[] tags = tagsOption.getAsString().split(",");
            for (String tag : tags) {
                commandBuilder.append(" #").append(tag.trim());
            }
        }
        
        OptionMapping walletOption = event.getOption("wallet");
        if (walletOption != null) {
            commandBuilder.append(" 钱包:").append(walletOption.getAsString());
        }

        // 处理记账命令
        String channelId = event.getChannel().getId();
        Long userId = event.getUser().getIdLong();
        String username = event.getUser().getName();
        
        // 先回复确认收到命令
        event.reply("⏳ 正在处理记账请求...").queue();
        
        // 异步处理记账逻辑
        handleAccountingMessage(channelId, userId, username, commandBuilder.toString(), false, null);
    }

    /**
     * 处理余额命令
     */
    private void handleBalanceCommand(SlashCommandInteractionEvent event) {
        event.reply("💰 余额查询功能开发中...").setEphemeral(true).queue();
    }

    /**
     * 处理历史命令
     */
    private void handleHistoryCommand(SlashCommandInteractionEvent event) {
        event.reply("📋 历史记录功能开发中...").setEphemeral(true).queue();
    }

    /**
     * 处理统计命令
     */
    private void handleStatsCommand(SlashCommandInteractionEvent event) {
        event.reply("📊 统计功能开发中...").setEphemeral(true).queue();
    }

    /**
     * 处理群组命令
     */
    private void handleGroupCommand(SlashCommandInteractionEvent event) {
        OptionMapping actionOption = event.getOption("action");
        if (actionOption == null) {
            event.reply("❌ 缺少操作参数").setEphemeral(true).queue();
            return;
        }

        String action = actionOption.getAsString();
        switch (action) {
            case "register" -> {
                event.reply("📝 群组注册功能开发中...").setEphemeral(true).queue();
            }
            case "settings" -> {
                event.reply("⚙️ 群组设置功能开发中...").setEphemeral(true).queue();
            }
            case "members" -> {
                event.reply("👥 成员管理功能开发中...").setEphemeral(true).queue();
            }
            case "permissions" -> {
                event.reply("🔐 权限管理功能开发中...").setEphemeral(true).queue();
            }
            default -> {
                event.reply("❌ 未知操作: " + action).setEphemeral(true).queue();
            }
        }
    }

    /**
     * 处理帮助命令
     */
    private void handleHelpCommand(SlashCommandInteractionEvent event) {
        String helpMessage = commandParser.getHelpMessage();
        event.reply(helpMessage).setEphemeral(true).queue();
    }

    /**
     * 处理设置命令
     */
    private void handleSettingsCommand(SlashCommandInteractionEvent event) {
        event.reply("⚙️ 设置功能开发中...").setEphemeral(true).queue();
    }

    /**
     * 处理测试命令
     */
    private void handleTestCommand(SlashCommandInteractionEvent event) {
        OptionMapping messageOption = event.getOption("message");
        String testMessage = messageOption != null ? messageOption.getAsString() : "测试消息";
        
        event.reply("✅ 测试成功: " + testMessage).setEphemeral(true).queue();
    }

    /**
     * 处理调试命令
     */
    private void handleDebugCommand(SlashCommandInteractionEvent event) {
        OptionMapping verboseOption = event.getOption("verbose");
        boolean verbose = verboseOption != null && verboseOption.getAsBoolean();
        
        StringBuilder debugInfo = new StringBuilder();
        debugInfo.append("🔧 **调试信息**\n\n");
        debugInfo.append("• 机器人状态: ").append(discordBotService.isOnline() ? "在线" : "离线").append("\n");
        debugInfo.append("• 服务器ID: ").append(event.getGuild() != null ? event.getGuild().getId() : "私聊").append("\n");
        debugInfo.append("• 频道ID: ").append(event.getChannel().getId()).append("\n");
        debugInfo.append("• 用户ID: ").append(event.getUser().getId()).append("\n");
        
        if (verbose) {
            debugInfo.append("• JDA版本: ").append(discordBotService.getJda().getGatewayPing()).append("ms\n");
            debugInfo.append("• 内存使用: ").append(Runtime.getRuntime().totalMemory() / 1024 / 1024).append("MB\n");
        }
        
        event.reply(debugInfo.toString()).setEphemeral(true).queue();
    }

    /**
     * 创建交易记录
     */
    private void createTransaction(String channelId, Long userId, String username, ParsedCommand command, GroupDO group) {
        // TODO: 实现交易创建逻辑
        String confirmMessage = messageFormatter.formatSuccess("记账成功！", PlatformType.DISCORD);
        
        // 创建操作按钮
        List<Button> buttons = List.of(
                Button.primary("view_details:123", "📋 查看详情"),
                Button.secondary("edit_transaction:123", "✏️ 编辑"),
                Button.danger("delete_transaction:123", "🗑️ 删除")
        );
        
        discordBotService.sendMessageWithButtons(channelId, confirmMessage, buttons.toArray(new Button[0]));
    }

    /**
     * 发送群组未注册消息
     */
    private void sendGroupNotRegisteredMessage(String channelId) {
        String message = messageFormatter.formatWarning("群组未注册，请先注册群组才能使用记账功能", PlatformType.DISCORD);
        
        Button registerButton = Button.primary("register_group", "📝 注册群组");
        discordBotService.sendMessageWithButtons(channelId, message, registerButton);
    }

    /**
     * 获取快速帮助
     */
    private String getQuickHelp() {
        return """
                💡 **快速帮助：**
                • `+100 午餐` - 收入
                • `-50 打车` - 支出
                • `/help` - 完整帮助
                """;
    }

    // 其他辅助方法...
    private void sendHelpMessage(String channelId) {
        discordBotService.sendMessage(channelId, commandParser.getHelpMessage());
    }

    private void sendPingMessage(String channelId) {
        long ping = discordBotService.getJda().getGatewayPing();
        discordBotService.sendMessage(channelId, "🏓 Pong! 延迟: " + ping + "ms");
    }

    private void sendStatusMessage(String channelId) {
        String status = discordBotService.isOnline() ? "✅ 在线" : "❌ 离线";
        discordBotService.sendMessage(channelId, "🤖 机器人状态: " + status);
    }

    // 交互处理方法（占位符）
    private void confirmTransaction(ButtonInteractionEvent event, String transactionId) {
        event.reply("✅ 交易已确认").setEphemeral(true).queue();
    }

    private void cancelTransaction(ButtonInteractionEvent event, String transactionId) {
        event.reply("❌ 交易已取消").setEphemeral(true).queue();
    }

    private void editTransaction(ButtonInteractionEvent event, String transactionId) {
        event.reply("✏️ 交易编辑功能开发中...").setEphemeral(true).queue();
    }

    private void deleteTransaction(ButtonInteractionEvent event, String transactionId) {
        event.reply("🗑️ 交易删除功能开发中...").setEphemeral(true).queue();
    }

    private void viewTransactionDetails(ButtonInteractionEvent event, String transactionId) {
        event.reply("📋 交易详情功能开发中...").setEphemeral(true).queue();
    }

    private void registerGroup(ButtonInteractionEvent event) {
        event.reply("📝 群组注册功能开发中...").setEphemeral(true).queue();
    }

    private void handleCategorySelect(StringSelectInteractionEvent event, List<String> values) {
        event.reply("📂 分类选择: " + String.join(", ", values)).setEphemeral(true).queue();
    }

    private void handleWalletSelect(StringSelectInteractionEvent event, List<String> values) {
        event.reply("💳 钱包选择: " + String.join(", ", values)).setEphemeral(true).queue();
    }

    private void handlePeriodSelect(StringSelectInteractionEvent event, List<String> values) {
        event.reply("📅 周期选择: " + String.join(", ", values)).setEphemeral(true).queue();
    }
}
