package top.continew.admin.accounting.service;

import top.continew.admin.accounting.model.req.NotificationBatchSendReq;
import top.continew.admin.accounting.model.req.NotificationSendReq;
import top.continew.admin.accounting.model.resp.NotificationSendResp;
import top.continew.admin.accounting.model.resp.NotificationStatisticsResp;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 通知推送服务
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface NotificationService {

    // ==================== 发送通知 ====================

    /**
     * 发送通知
     *
     * @param req 发送通知请求
     * @return 发送结果
     */
    NotificationSendResp sendNotification(NotificationSendReq req);

    /**
     * 异步发送通知
     *
     * @param req 发送通知请求
     * @return 异步发送结果
     */
    CompletableFuture<NotificationSendResp> sendNotificationAsync(NotificationSendReq req);

    /**
     * 批量发送通知
     *
     * @param req 批量发送请求
     * @return 批量发送结果
     */
    List<NotificationSendResp> batchSendNotifications(NotificationBatchSendReq req);

    /**
     * 异步批量发送通知
     *
     * @param req 批量发送请求
     * @return 异步批量发送结果
     */
    CompletableFuture<List<NotificationSendResp>> batchSendNotificationsAsync(NotificationBatchSendReq req);

    /**
     * 发送模板通知
     *
     * @param templateCode 模板代码
     * @param templateParams 模板参数
     * @param channels 发送渠道
     * @param targetUsers 目标用户
     * @return 发送结果
     */
    NotificationSendResp sendTemplateNotification(String templateCode, Map<String, Object> templateParams, 
                                                 List<String> channels, List<Long> targetUsers);

    // ==================== 计划发送 ====================

    /**
     * 计划发送通知
     *
     * @param req 发送通知请求
     * @param scheduledTime 计划发送时间
     * @return 通知ID
     */
    Long scheduleNotification(NotificationSendReq req, LocalDateTime scheduledTime);

    /**
     * 取消计划通知
     *
     * @param notificationId 通知ID
     * @return 是否成功
     */
    Boolean cancelScheduledNotification(Long notificationId);

    /**
     * 获取待发送的计划通知
     *
     * @param currentTime 当前时间
     * @param limit 限制数量
     * @return 待发送通知列表
     */
    List<Long> getPendingScheduledNotifications(LocalDateTime currentTime, Integer limit);

    /**
     * 执行计划通知
     *
     * @param notificationId 通知ID
     * @return 发送结果
     */
    NotificationSendResp executeScheduledNotification(Long notificationId);

    // ==================== 重试机制 ====================

    /**
     * 重试失败通知
     *
     * @param notificationId 通知ID
     * @return 重试结果
     */
    NotificationSendResp retryNotification(Long notificationId);

    /**
     * 批量重试失败通知
     *
     * @param notificationIds 通知ID列表
     * @return 重试结果列表
     */
    List<NotificationSendResp> batchRetryNotifications(List<Long> notificationIds);

    /**
     * 获取需要重试的通知
     *
     * @param currentTime 当前时间
     * @param limit 限制数量
     * @return 需要重试的通知ID列表
     */
    List<Long> getNotificationsForRetry(LocalDateTime currentTime, Integer limit);

    /**
     * 自动重试失败通知
     *
     * @return 重试数量
     */
    Integer autoRetryFailedNotifications();

    // ==================== 通知管理 ====================

    /**
     * 获取通知详情
     *
     * @param notificationId 通知ID
     * @return 通知详情
     */
    Map<String, Object> getNotificationDetail(Long notificationId);

    /**
     * 获取通知发送日志
     *
     * @param notificationId 通知ID
     * @return 发送日志列表
     */
    List<Map<String, Object>> getNotificationLogs(Long notificationId);

    /**
     * 更新通知状态
     *
     * @param notificationId 通知ID
     * @param status 新状态
     * @param errorMessage 错误信息
     */
    void updateNotificationStatus(Long notificationId, String status, String errorMessage);

    /**
     * 删除过期通知
     *
     * @param expiredBefore 过期时间点
     * @return 删除数量
     */
    Integer deleteExpiredNotifications(LocalDateTime expiredBefore);

    // ==================== 统计分析 ====================

    /**
     * 获取通知统计
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果
     */
    NotificationStatisticsResp getNotificationStatistics(Long groupId, String startDate, String endDate);

    /**
     * 获取渠道性能统计
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 渠道性能统计
     */
    Map<String, Object> getChannelPerformanceStatistics(Long groupId, String startDate, String endDate);

    /**
     * 获取发送趋势
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param groupBy 分组方式
     * @return 发送趋势数据
     */
    List<Map<String, Object>> getSendTrends(Long groupId, String startDate, String endDate, String groupBy);

    /**
     * 获取失败分析
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 失败分析数据
     */
    Map<String, Object> getFailureAnalysis(Long groupId, String startDate, String endDate);

    // ==================== 渠道管理 ====================

    /**
     * 测试渠道连接
     *
     * @param channel 渠道
     * @param config 配置参数
     * @return 测试结果
     */
    Map<String, Object> testChannelConnection(String channel, Map<String, Object> config);

    /**
     * 获取渠道状态
     *
     * @return 渠道状态列表
     */
    List<Map<String, Object>> getChannelStatus();

    /**
     * 启用/禁用渠道
     *
     * @param channel 渠道
     * @param enabled 是否启用
     */
    void updateChannelStatus(String channel, Boolean enabled);

    /**
     * 获取渠道配置
     *
     * @param channel 渠道
     * @return 渠道配置
     */
    Map<String, Object> getChannelConfig(String channel);

    /**
     * 更新渠道配置
     *
     * @param channel 渠道
     * @param config 配置参数
     */
    void updateChannelConfig(String channel, Map<String, Object> config);

    // ==================== 模板管理 ====================

    /**
     * 渲染模板
     *
     * @param templateCode 模板代码
     * @param channel 渠道
     * @param params 参数
     * @return 渲染结果
     */
    Map<String, String> renderTemplate(String templateCode, String channel, Map<String, Object> params);

    /**
     * 验证模板
     *
     * @param templateCode 模板代码
     * @param params 参数
     * @return 验证结果
     */
    Map<String, Object> validateTemplate(String templateCode, Map<String, Object> params);

    /**
     * 预览模板
     *
     * @param templateCode 模板代码
     * @param channel 渠道
     * @param params 参数
     * @return 预览结果
     */
    Map<String, String> previewTemplate(String templateCode, String channel, Map<String, Object> params);

}
