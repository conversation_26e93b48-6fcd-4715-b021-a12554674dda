package top.continew.admin.accounting.service.sync;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.continew.starter.core.exception.BusinessException;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 数据转换引擎
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataTransformEngine {

    /**
     * 转换数据
     *
     * @param sourceData 源数据
     * @param fieldMapping 字段映射配置
     * @param transformRules 转换规则
     * @return 转换后的数据
     */
    public List<Map<String, Object>> transformData(List<Map<String, Object>> sourceData,
                                                   Map<String, Object> fieldMapping,
                                                   Map<String, Object> transformRules) {
        if (CollUtil.isEmpty(sourceData)) {
            return new ArrayList<>();
        }
        
        log.info("开始数据转换: sourceCount={}", sourceData.size());
        
        List<Map<String, Object>> transformedData = new ArrayList<>();
        
        for (Map<String, Object> sourceRecord : sourceData) {
            try {
                Map<String, Object> transformedRecord = transformRecord(sourceRecord, fieldMapping, transformRules);
                transformedData.add(transformedRecord);
            } catch (Exception e) {
                log.error("转换记录失败: record={}, error={}", sourceRecord, e.getMessage(), e);
                // 可以选择跳过错误记录或抛出异常
                throw new BusinessException("数据转换失败: " + e.getMessage());
            }
        }
        
        log.info("数据转换完成: transformedCount={}", transformedData.size());
        return transformedData;
    }

    /**
     * 转换单条记录
     */
    private Map<String, Object> transformRecord(Map<String, Object> sourceRecord,
                                                Map<String, Object> fieldMapping,
                                                Map<String, Object> transformRules) {
        Map<String, Object> transformedRecord = new HashMap<>();
        
        // 应用字段映射
        if (fieldMapping != null && !fieldMapping.isEmpty()) {
            for (Map.Entry<String, Object> entry : fieldMapping.entrySet()) {
                String sourceField = entry.getKey();
                String targetField = (String) entry.getValue();
                
                if (sourceRecord.containsKey(sourceField)) {
                    Object value = sourceRecord.get(sourceField);
                    transformedRecord.put(targetField, value);
                }
            }
        } else {
            // 如果没有字段映射，直接复制所有字段
            transformedRecord.putAll(sourceRecord);
        }
        
        // 应用转换规则
        if (transformRules != null && !transformRules.isEmpty()) {
            transformedRecord = applyTransformRules(transformedRecord, transformRules);
        }
        
        return transformedRecord;
    }

    /**
     * 应用转换规则
     */
    private Map<String, Object> applyTransformRules(Map<String, Object> record, Map<String, Object> transformRules) {
        Map<String, Object> result = new HashMap<>(record);
        
        for (Map.Entry<String, Object> entry : transformRules.entrySet()) {
            String field = entry.getKey();
            @SuppressWarnings("unchecked")
            Map<String, Object> rule = (Map<String, Object>) entry.getValue();
            
            if (rule != null) {
                Object transformedValue = applyFieldTransform(field, result.get(field), rule);
                result.put(field, transformedValue);
            }
        }
        
        return result;
    }

    /**
     * 应用字段转换
     */
    private Object applyFieldTransform(String field, Object value, Map<String, Object> rule) {
        String transformType = (String) rule.get("type");
        
        if (transformType == null) {
            return value;
        }
        
        try {
            switch (transformType) {
                case "TYPE_CONVERT":
                    return applyTypeConvert(value, rule);
                case "FORMAT":
                    return applyFormat(value, rule);
                case "CALCULATE":
                    return applyCalculate(value, rule);
                case "LOOKUP":
                    return applyLookup(value, rule);
                case "REGEX":
                    return applyRegex(value, rule);
                case "CONDITIONAL":
                    return applyConditional(value, rule);
                case "AGGREGATE":
                    return applyAggregate(value, rule);
                case "CUSTOM":
                    return applyCustomTransform(field, value, rule);
                default:
                    log.warn("不支持的转换类型: {}", transformType);
                    return value;
            }
        } catch (Exception e) {
            log.error("字段转换失败: field={}, value={}, rule={}, error={}", field, value, rule, e.getMessage());
            
            // 根据错误处理策略决定是否抛出异常
            String errorHandling = (String) rule.get("errorHandling");
            if ("THROW".equals(errorHandling)) {
                throw new BusinessException("字段转换失败: " + e.getMessage());
            } else if ("DEFAULT".equals(errorHandling)) {
                return rule.get("defaultValue");
            } else {
                // SKIP - 返回原值
                return value;
            }
        }
    }

    /**
     * 类型转换
     */
    private Object applyTypeConvert(Object value, Map<String, Object> rule) {
        if (value == null) {
            return null;
        }
        
        String targetType = (String) rule.get("targetType");
        
        switch (targetType) {
            case "STRING":
                return Convert.toStr(value);
            case "INTEGER":
                return Convert.toInt(value);
            case "LONG":
                return Convert.toLong(value);
            case "DOUBLE":
                return Convert.toDouble(value);
            case "BIGDECIMAL":
                return Convert.toBigDecimal(value);
            case "BOOLEAN":
                return Convert.toBool(value);
            case "DATE":
                return convertToDate(value, rule);
            case "DATETIME":
                return convertToDateTime(value, rule);
            default:
                throw new BusinessException("不支持的目标类型: " + targetType);
        }
    }

    /**
     * 格式化转换
     */
    private Object applyFormat(Object value, Map<String, Object> rule) {
        if (value == null) {
            return null;
        }
        
        String formatType = (String) rule.get("formatType");
        String pattern = (String) rule.get("pattern");
        
        switch (formatType) {
            case "DATE":
                if (value instanceof LocalDateTime) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                    return ((LocalDateTime) value).format(formatter);
                }
                break;
            case "NUMBER":
                if (value instanceof Number) {
                    return NumberUtil.decimalFormat(pattern, (Number) value);
                }
                break;
            case "STRING":
                return String.format(pattern, value);
            default:
                throw new BusinessException("不支持的格式化类型: " + formatType);
        }
        
        return value;
    }

    /**
     * 计算转换
     */
    private Object applyCalculate(Object value, Map<String, Object> rule) {
        if (value == null) {
            return null;
        }
        
        String operation = (String) rule.get("operation");
        Object operand = rule.get("operand");
        
        if (!(value instanceof Number) || !(operand instanceof Number)) {
            throw new BusinessException("计算转换要求数值类型");
        }
        
        BigDecimal valueDecimal = new BigDecimal(value.toString());
        BigDecimal operandDecimal = new BigDecimal(operand.toString());
        
        switch (operation) {
            case "ADD":
                return valueDecimal.add(operandDecimal);
            case "SUBTRACT":
                return valueDecimal.subtract(operandDecimal);
            case "MULTIPLY":
                return valueDecimal.multiply(operandDecimal);
            case "DIVIDE":
                return valueDecimal.divide(operandDecimal, 2, BigDecimal.ROUND_HALF_UP);
            default:
                throw new BusinessException("不支持的计算操作: " + operation);
        }
    }

    /**
     * 查找转换
     */
    private Object applyLookup(Object value, Map<String, Object> rule) {
        if (value == null) {
            return null;
        }
        
        @SuppressWarnings("unchecked")
        Map<String, Object> lookupTable = (Map<String, Object>) rule.get("lookupTable");
        Object defaultValue = rule.get("defaultValue");
        
        if (lookupTable == null) {
            return defaultValue;
        }
        
        String key = value.toString();
        return lookupTable.getOrDefault(key, defaultValue);
    }

    /**
     * 正则表达式转换
     */
    private Object applyRegex(Object value, Map<String, Object> rule) {
        if (value == null) {
            return null;
        }
        
        String pattern = (String) rule.get("pattern");
        String replacement = (String) rule.get("replacement");
        String operation = (String) rule.get("operation");
        
        String stringValue = value.toString();
        
        switch (operation) {
            case "REPLACE":
                return stringValue.replaceAll(pattern, replacement);
            case "EXTRACT":
                Pattern regex = Pattern.compile(pattern);
                java.util.regex.Matcher matcher = regex.matcher(stringValue);
                if (matcher.find()) {
                    return matcher.group(1);
                }
                return rule.get("defaultValue");
            case "MATCH":
                return Pattern.matches(pattern, stringValue);
            default:
                throw new BusinessException("不支持的正则操作: " + operation);
        }
    }

    /**
     * 条件转换
     */
    private Object applyConditional(Object value, Map<String, Object> rule) {
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> conditions = (List<Map<String, Object>>) rule.get("conditions");
        Object defaultValue = rule.get("defaultValue");
        
        if (conditions == null) {
            return defaultValue;
        }
        
        for (Map<String, Object> condition : conditions) {
            if (evaluateCondition(value, condition)) {
                return condition.get("value");
            }
        }
        
        return defaultValue;
    }

    /**
     * 聚合转换
     */
    private Object applyAggregate(Object value, Map<String, Object> rule) {
        // 聚合转换通常用于多条记录的聚合，这里简化处理
        String operation = (String) rule.get("operation");
        
        if (value instanceof List) {
            @SuppressWarnings("unchecked")
            List<Object> values = (List<Object>) value;
            
            switch (operation) {
                case "SUM":
                    return values.stream()
                            .filter(v -> v instanceof Number)
                            .mapToDouble(v -> ((Number) v).doubleValue())
                            .sum();
                case "AVG":
                    return values.stream()
                            .filter(v -> v instanceof Number)
                            .mapToDouble(v -> ((Number) v).doubleValue())
                            .average()
                            .orElse(0.0);
                case "COUNT":
                    return values.size();
                case "MAX":
                    return values.stream()
                            .filter(v -> v instanceof Number)
                            .mapToDouble(v -> ((Number) v).doubleValue())
                            .max()
                            .orElse(0.0);
                case "MIN":
                    return values.stream()
                            .filter(v -> v instanceof Number)
                            .mapToDouble(v -> ((Number) v).doubleValue())
                            .min()
                            .orElse(0.0);
                default:
                    throw new BusinessException("不支持的聚合操作: " + operation);
            }
        }
        
        return value;
    }

    /**
     * 自定义转换
     */
    private Object applyCustomTransform(String field, Object value, Map<String, Object> rule) {
        String customFunction = (String) rule.get("function");
        
        // 这里可以实现自定义转换逻辑
        switch (customFunction) {
            case "GENERATE_ID":
                return generateId();
            case "CURRENT_TIME":
                return LocalDateTime.now();
            case "HASH":
                return value != null ? value.hashCode() : 0;
            case "UPPER_CASE":
                return value != null ? value.toString().toUpperCase() : null;
            case "LOWER_CASE":
                return value != null ? value.toString().toLowerCase() : null;
            case "TRIM":
                return value != null ? value.toString().trim() : null;
            default:
                log.warn("不支持的自定义函数: {}", customFunction);
                return value;
        }
    }

    /**
     * 转换为日期
     */
    private Object convertToDate(Object value, Map<String, Object> rule) {
        if (value == null) {
            return null;
        }
        
        String sourceFormat = (String) rule.get("sourceFormat");
        
        if (value instanceof String && StrUtil.isNotBlank(sourceFormat)) {
            return DateUtil.parse((String) value, sourceFormat);
        }
        
        return DateUtil.parse(value.toString());
    }

    /**
     * 转换为日期时间
     */
    private Object convertToDateTime(Object value, Map<String, Object> rule) {
        if (value == null) {
            return null;
        }
        
        String sourceFormat = (String) rule.get("sourceFormat");
        
        if (value instanceof String && StrUtil.isNotBlank(sourceFormat)) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(sourceFormat);
            return LocalDateTime.parse((String) value, formatter);
        }
        
        if (value instanceof String) {
            return LocalDateTime.parse((String) value);
        }
        
        return Convert.toLocalDateTime(value);
    }

    /**
     * 评估条件
     */
    private boolean evaluateCondition(Object value, Map<String, Object> condition) {
        String operator = (String) condition.get("operator");
        Object compareValue = condition.get("compareValue");
        
        if (value == null && compareValue == null) {
            return "EQUALS".equals(operator);
        }
        
        if (value == null || compareValue == null) {
            return "NOT_EQUALS".equals(operator);
        }
        
        switch (operator) {
            case "EQUALS":
                return Objects.equals(value, compareValue);
            case "NOT_EQUALS":
                return !Objects.equals(value, compareValue);
            case "GREATER_THAN":
                return compareNumbers(value, compareValue) > 0;
            case "GREATER_EQUAL":
                return compareNumbers(value, compareValue) >= 0;
            case "LESS_THAN":
                return compareNumbers(value, compareValue) < 0;
            case "LESS_EQUAL":
                return compareNumbers(value, compareValue) <= 0;
            case "CONTAINS":
                return value.toString().contains(compareValue.toString());
            case "STARTS_WITH":
                return value.toString().startsWith(compareValue.toString());
            case "ENDS_WITH":
                return value.toString().endsWith(compareValue.toString());
            case "REGEX":
                return Pattern.matches(compareValue.toString(), value.toString());
            default:
                return false;
        }
    }

    /**
     * 比较数值
     */
    private int compareNumbers(Object value1, Object value2) {
        if (value1 instanceof Number && value2 instanceof Number) {
            double num1 = ((Number) value1).doubleValue();
            double num2 = ((Number) value2).doubleValue();
            return Double.compare(num1, num2);
        }
        
        return value1.toString().compareTo(value2.toString());
    }

    /**
     * 生成ID
     */
    private String generateId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 验证转换后的数据
     *
     * @param transformedData 转换后的数据
     * @param validationRules 验证规则
     * @return 验证结果
     */
    public Map<String, Object> validateTransformedData(List<Map<String, Object>> transformedData,
                                                       Map<String, Object> validationRules) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        int validCount = 0;
        int invalidCount = 0;
        
        if (validationRules == null || validationRules.isEmpty()) {
            result.put("valid", true);
            result.put("validCount", transformedData.size());
            result.put("invalidCount", 0);
            result.put("errors", errors);
            return result;
        }
        
        for (Map<String, Object> record : transformedData) {
            List<String> recordErrors = validateRecord(record, validationRules);
            if (recordErrors.isEmpty()) {
                validCount++;
            } else {
                invalidCount++;
                errors.addAll(recordErrors);
            }
        }
        
        result.put("valid", invalidCount == 0);
        result.put("validCount", validCount);
        result.put("invalidCount", invalidCount);
        result.put("errors", errors);
        
        return result;
    }

    /**
     * 验证单条记录
     */
    private List<String> validateRecord(Map<String, Object> record, Map<String, Object> validationRules) {
        List<String> errors = new ArrayList<>();
        
        for (Map.Entry<String, Object> entry : validationRules.entrySet()) {
            String field = entry.getKey();
            @SuppressWarnings("unchecked")
            Map<String, Object> rules = (Map<String, Object>) entry.getValue();
            
            Object value = record.get(field);
            
            // 必填验证
            if (Boolean.TRUE.equals(rules.get("required")) && (value == null || StrUtil.isBlank(value.toString()))) {
                errors.add("字段 " + field + " 不能为空");
                continue;
            }
            
            if (value != null) {
                // 类型验证
                String expectedType = (String) rules.get("type");
                if (StrUtil.isNotBlank(expectedType) && !validateType(value, expectedType)) {
                    errors.add("字段 " + field + " 类型不匹配，期望: " + expectedType);
                }
                
                // 长度验证
                Integer maxLength = (Integer) rules.get("maxLength");
                if (maxLength != null && value.toString().length() > maxLength) {
                    errors.add("字段 " + field + " 长度超过限制: " + maxLength);
                }
                
                // 范围验证
                if (value instanceof Number) {
                    Number minValue = (Number) rules.get("minValue");
                    Number maxValue = (Number) rules.get("maxValue");
                    
                    if (minValue != null && ((Number) value).doubleValue() < minValue.doubleValue()) {
                        errors.add("字段 " + field + " 值小于最小值: " + minValue);
                    }
                    
                    if (maxValue != null && ((Number) value).doubleValue() > maxValue.doubleValue()) {
                        errors.add("字段 " + field + " 值大于最大值: " + maxValue);
                    }
                }
                
                // 正则验证
                String pattern = (String) rules.get("pattern");
                if (StrUtil.isNotBlank(pattern) && !Pattern.matches(pattern, value.toString())) {
                    errors.add("字段 " + field + " 格式不正确");
                }
            }
        }
        
        return errors;
    }

    /**
     * 验证类型
     */
    private boolean validateType(Object value, String expectedType) {
        switch (expectedType) {
            case "STRING":
                return value instanceof String;
            case "INTEGER":
                return value instanceof Integer;
            case "LONG":
                return value instanceof Long;
            case "DOUBLE":
                return value instanceof Double;
            case "BIGDECIMAL":
                return value instanceof BigDecimal;
            case "BOOLEAN":
                return value instanceof Boolean;
            case "DATE":
            case "DATETIME":
                return value instanceof LocalDateTime || value instanceof Date;
            default:
                return true;
        }
    }
}
