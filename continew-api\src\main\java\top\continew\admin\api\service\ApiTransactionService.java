package top.continew.admin.api.service;

import top.continew.admin.accounting.model.query.TransactionQuery;
import top.continew.admin.api.model.req.ApiTransactionCreateReq;
import top.continew.admin.api.model.req.ApiTransactionUpdateReq;
import top.continew.admin.api.model.resp.ApiTransactionResp;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * API交易服务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface ApiTransactionService {

    /**
     * 分页查询交易
     */
    PageResp<ApiTransactionResp> page(TransactionQuery query, PageQuery pageQuery);

    /**
     * 查询交易列表
     */
    List<ApiTransactionResp> list(TransactionQuery query);

    /**
     * 查询交易详情
     */
    ApiTransactionResp get(Long id);

    /**
     * 创建交易
     */
    Long create(ApiTransactionCreateReq req);

    /**
     * 更新交易
     */
    void update(ApiTransactionUpdateReq req, Long id);

    /**
     * 删除交易
     */
    void delete(List<Long> ids);

    /**
     * 批量创建交易
     */
    List<Long> batchCreate(List<ApiTransactionCreateReq> reqList);

    /**
     * 复制交易
     */
    Long copy(Long id);

    /**
     * 获取交易统计
     */
    ApiTransactionResp.TransactionStatistics getStatistics(Long groupId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取分类统计
     */
    List<ApiTransactionResp.CategoryStatistics> getCategoryStatistics(Long groupId, String type, LocalDate startDate, LocalDate endDate);

    /**
     * 获取月度趋势
     */
    List<ApiTransactionResp.MonthlyTrend> getMonthlyTrend(Long groupId, int months);

    /**
     * 获取用户统计
     */
    List<ApiTransactionResp.UserStatistics> getUserStatistics(Long groupId, LocalDate startDate, LocalDate endDate);

    /**
     * 搜索交易
     */
    List<ApiTransactionResp> search(Long groupId, String keyword, int limit);

    /**
     * 获取最近交易
     */
    List<ApiTransactionResp> getRecentTransactions(Long groupId, int limit);

    /**
     * 获取相似交易
     */
    List<ApiTransactionResp> getSimilarTransactions(Long groupId, String description, int limit);

    /**
     * 验证交易
     */
    ApiTransactionResp.ValidationResult validate(ApiTransactionCreateReq req);

    /**
     * 获取交易建议
     */
    List<ApiTransactionResp.TransactionSuggestion> getSuggestions(Long groupId, String description, BigDecimal amount);

    /**
     * 导出交易数据
     */
    String exportTransactions(Long groupId, String format, LocalDate startDate, LocalDate endDate);

    /**
     * 导入交易数据
     */
    ApiTransactionResp.ImportResult importTransactions(Long groupId, String fileUrl, boolean overwrite);

    /**
     * 获取交易模板
     */
    List<ApiTransactionResp.TransactionTemplate> getTemplates(Long groupId);

    /**
     * 保存为模板
     */
    Long saveAsTemplate(Long transactionId, String templateName);

    /**
     * 从模板创建交易
     */
    Long createFromTemplate(Long templateId, ApiTransactionCreateReq overrides);
}
