package top.continew.admin.accounting.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.accounting.mapper.UsageStatisticsMapper;
import top.continew.admin.accounting.model.entity.SubscriptionDO;
import top.continew.admin.accounting.model.entity.SubscriptionPlanDO;
import top.continew.admin.accounting.model.entity.UsageStatisticsDO;
import top.continew.admin.accounting.service.SubscriptionService;
import top.continew.admin.accounting.service.UsageStatisticsService;
import top.continew.admin.accounting.service.UsageTrackingService;

import java.time.LocalDate;
import java.util.concurrent.TimeUnit;

/**
 * 使用量跟踪服务实现
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UsageTrackingServiceImpl implements UsageTrackingService {

    private final UsageStatisticsMapper usageStatisticsMapper;
    private final UsageStatisticsService usageStatisticsService;
    private final SubscriptionService subscriptionService;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String USAGE_CACHE_PREFIX = "usage:";
    private static final String ACTIVE_USERS_PREFIX = "active_users:";
    private static final int CACHE_EXPIRE_HOURS = 25; // 缓存25小时，确保跨天统计

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordTransactionCreated(Long groupId) {
        try {
            incrementUsage(groupId, "transaction_count", 1);
            log.debug("记录交易创建: groupId={}", groupId);
        } catch (Exception e) {
            log.error("记录交易创建失败: groupId={}, error={}", groupId, e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordOcrUsage(Long groupId, int count) {
        try {
            incrementUsage(groupId, "ocr_count", count);
            log.debug("记录OCR使用: groupId={}, count={}", groupId, count);
        } catch (Exception e) {
            log.error("记录OCR使用失败: groupId={}, count={}, error={}", groupId, count, e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordApiCall(Long groupId, int count) {
        try {
            incrementUsage(groupId, "api_calls", count);
            log.debug("记录API调用: groupId={}, count={}", groupId, count);
        } catch (Exception e) {
            log.error("记录API调用失败: groupId={}, count={}, error={}", groupId, count, e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordStorageUsage(Long groupId, long sizeBytes) {
        try {
            incrementUsage(groupId, "storage_used", sizeBytes);
            log.debug("记录存储使用: groupId={}, sizeBytes={}", groupId, sizeBytes);
        } catch (Exception e) {
            log.error("记录存储使用失败: groupId={}, sizeBytes={}, error={}", groupId, sizeBytes, e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordExport(Long groupId) {
        try {
            incrementUsage(groupId, "export_count", 1);
            log.debug("记录导出操作: groupId={}", groupId);
        } catch (Exception e) {
            log.error("记录导出操作失败: groupId={}, error={}", groupId, e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordWebhookCall(Long groupId) {
        try {
            incrementUsage(groupId, "webhook_calls", 1);
            log.debug("记录Webhook调用: groupId={}", groupId);
        } catch (Exception e) {
            log.error("记录Webhook调用失败: groupId={}, error={}", groupId, e.getMessage(), e);
        }
    }

    @Override
    public void recordActiveUser(Long groupId, Long userId) {
        try {
            String key = ACTIVE_USERS_PREFIX + groupId + ":" + DateUtil.today();
            redisTemplate.opsForSet().add(key, userId);
            redisTemplate.expire(key, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
            
            // 异步更新活跃用户数
            updateActiveUserCount(groupId);
            
            log.debug("记录活跃用户: groupId={}, userId={}", groupId, userId);
        } catch (Exception e) {
            log.error("记录活跃用户失败: groupId={}, userId={}, error={}", groupId, userId, e.getMessage(), e);
        }
    }

    @Override
    public boolean isLimitExceeded(Long groupId, String limitType) {
        try {
            int currentUsage = getCurrentUsage(groupId, limitType);
            int limit = getLimit(groupId, limitType);
            
            // -1表示无限制
            if (limit == -1) {
                return false;
            }
            
            boolean exceeded = currentUsage >= limit;
            if (exceeded) {
                log.warn("使用量超出限制: groupId={}, limitType={}, current={}, limit={}", 
                        groupId, limitType, currentUsage, limit);
            }
            
            return exceeded;
        } catch (Exception e) {
            log.error("检查限制失败: groupId={}, limitType={}, error={}", groupId, limitType, e.getMessage(), e);
            return true; // 出错时默认认为超出限制
        }
    }

    @Override
    public int getCurrentUsage(Long groupId, String limitType) {
        try {
            LocalDate today = LocalDate.now();
            UsageStatisticsDO usage = usageStatisticsMapper.selectByGroupIdAndDate(groupId, today);
            
            if (usage == null) {
                return 0;
            }
            
            return switch (limitType) {
                case "maxTransactionsPerMonth" -> usage.getTransactionCount();
                case "maxOcrPerMonth" -> usage.getOcrCount();
                case "maxApiCallsPerMonth" -> usage.getApiCalls();
                case "maxExportsPerMonth" -> usage.getExportCount();
                case "maxWebhooksPerMonth" -> usage.getWebhookCalls();
                case "maxStorageGB" -> (int) (usage.getStorageUsed() / (1024 * 1024 * 1024)); // 转换为GB
                case "maxActiveUsers" -> usage.getActiveUsers();
                default -> 0;
            };
        } catch (Exception e) {
            log.error("获取当前使用量失败: groupId={}, limitType={}, error={}", groupId, limitType, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int getLimit(Long groupId, String limitType) {
        try {
            SubscriptionDO subscription = subscriptionService.getActiveSubscription(groupId);
            if (subscription == null) {
                return 0; // 无订阅时限制为0
            }
            
            SubscriptionPlanDO plan = subscriptionService.getSubscriptionPlan(subscription.getPlanId());
            if (plan == null || plan.getLimits() == null) {
                return -1; // 无限制
            }
            
            JSONObject limits = JSON.parseObject(plan.getLimits());
            return limits.getIntValue(limitType);
        } catch (Exception e) {
            log.error("获取限制值失败: groupId={}, limitType={}, error={}", groupId, limitType, e.getMessage(), e);
            return 0; // 出错时默认限制为0
        }
    }

    /**
     * 增加使用量
     */
    private void incrementUsage(Long groupId, String field, long increment) {
        LocalDate today = LocalDate.now();
        String cacheKey = USAGE_CACHE_PREFIX + groupId + ":" + today;
        
        try {
            // 先更新缓存
            redisTemplate.opsForHash().increment(cacheKey, field, increment);
            redisTemplate.expire(cacheKey, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
            
            // 异步更新数据库
            updateUsageInDatabase(groupId, field, increment);
            
        } catch (Exception e) {
            log.error("增加使用量失败: groupId={}, field={}, increment={}, error={}", 
                    groupId, field, increment, e.getMessage(), e);
        }
    }

    /**
     * 更新数据库中的使用量
     */
    private void updateUsageInDatabase(Long groupId, String field, long increment) {
        try {
            LocalDate today = LocalDate.now();
            UsageStatisticsDO usage = usageStatisticsMapper.selectByGroupIdAndDate(groupId, today);
            
            if (usage == null) {
                // 创建新记录
                usage = new UsageStatisticsDO();
                usage.setGroupId(groupId);
                usage.setStatDate(today);
                setFieldValue(usage, field, increment);
                usageStatisticsService.save(usage);
            } else {
                // 更新现有记录
                long currentValue = getFieldValue(usage, field);
                setFieldValue(usage, field, currentValue + increment);
                usageStatisticsService.updateById(usage);
            }
        } catch (Exception e) {
            log.error("更新数据库使用量失败: groupId={}, field={}, increment={}, error={}", 
                    groupId, field, increment, e.getMessage(), e);
        }
    }

    /**
     * 更新活跃用户数
     */
    private void updateActiveUserCount(Long groupId) {
        try {
            String key = ACTIVE_USERS_PREFIX + groupId + ":" + DateUtil.today();
            Long activeUserCount = redisTemplate.opsForSet().size(key);
            
            if (activeUserCount != null && activeUserCount > 0) {
                updateUsageInDatabase(groupId, "active_users", activeUserCount);
            }
        } catch (Exception e) {
            log.error("更新活跃用户数失败: groupId={}, error={}", groupId, e.getMessage(), e);
        }
    }

    /**
     * 获取字段值
     */
    private long getFieldValue(UsageStatisticsDO usage, String field) {
        return switch (field) {
            case "transaction_count" -> usage.getTransactionCount();
            case "ocr_count" -> usage.getOcrCount();
            case "api_calls" -> usage.getApiCalls();
            case "storage_used" -> usage.getStorageUsed();
            case "export_count" -> usage.getExportCount();
            case "webhook_calls" -> usage.getWebhookCalls();
            case "active_users" -> usage.getActiveUsers();
            default -> 0;
        };
    }

    /**
     * 设置字段值
     */
    private void setFieldValue(UsageStatisticsDO usage, String field, long value) {
        switch (field) {
            case "transaction_count" -> usage.setTransactionCount((int) value);
            case "ocr_count" -> usage.setOcrCount((int) value);
            case "api_calls" -> usage.setApiCalls((int) value);
            case "storage_used" -> usage.setStorageUsed(value);
            case "export_count" -> usage.setExportCount((int) value);
            case "webhook_calls" -> usage.setWebhookCalls((int) value);
            case "active_users" -> usage.setActiveUsers((int) value);
        }
    }
}
