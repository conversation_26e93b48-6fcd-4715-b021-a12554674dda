package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 创建订阅套餐请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "创建订阅套餐请求")
public class SubscriptionPlanCreateReq {

    /**
     * 套餐名称
     */
    @Schema(description = "套餐名称", example = "专业版")
    @NotBlank(message = "套餐名称不能为空")
    private String name;

    /**
     * 套餐代码
     */
    @Schema(description = "套餐代码", example = "PRO")
    @NotBlank(message = "套餐代码不能为空")
    private String code;

    /**
     * 套餐描述
     */
    @Schema(description = "套餐描述", example = "适合小团队使用的专业版套餐")
    private String description;

    /**
     * 价格
     */
    @Schema(description = "价格", example = "9.99")
    @NotNull(message = "价格不能为空")
    @Positive(message = "价格必须大于0")
    private BigDecimal price;

    /**
     * 币种
     */
    @Schema(description = "币种", example = "USD")
    private String currency = "USD";

    /**
     * 计费周期
     */
    @Schema(description = "计费周期", example = "MONTHLY")
    @NotBlank(message = "计费周期不能为空")
    private String billingCycle;

    /**
     * 功能特性
     */
    @Schema(description = "功能特性")
    private Map<String, Object> features;

    /**
     * 使用限制
     */
    @Schema(description = "使用限制")
    private Map<String, Object> limits;

    /**
     * 试用天数
     */
    @Schema(description = "试用天数", example = "7")
    private Integer trialDays = 0;

    /**
     * 是否热门
     */
    @Schema(description = "是否热门", example = "false")
    private Boolean isPopular = false;

    /**
     * 排序
     */
    @Schema(description = "排序", example = "1")
    private Integer sortOrder = 0;
}
