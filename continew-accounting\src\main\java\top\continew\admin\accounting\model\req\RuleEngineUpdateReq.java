package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.common.model.req.BaseReq;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 规则引擎更新请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "规则引擎更新请求")
public class RuleEngineUpdateReq extends BaseReq {

    /**
     * 规则名称
     */
    @Schema(description = "规则名称", example = "自动分类规则")
    private String ruleName;

    /**
     * 规则描述
     */
    @Schema(description = "规则描述", example = "根据关键词自动分类交易")
    private String ruleDescription;

    /**
     * 规则类型
     */
    @Schema(description = "规则类型", example = "AUTO_CATEGORY")
    private String ruleType;

    /**
     * 规则优先级
     */
    @Schema(description = "规则优先级", example = "100")
    private Integer priority;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    /**
     * 触发条件配置
     */
    @Schema(description = "触发条件配置")
    @Valid
    private RuleEngineCreateReq.TriggerCondition triggerCondition;

    /**
     * 执行动作配置
     */
    @Schema(description = "执行动作配置")
    @Valid
    private List<RuleEngineCreateReq.ExecutionAction> executionActions;

    /**
     * 调度配置
     */
    @Schema(description = "调度配置")
    @Valid
    private RuleEngineCreateReq.ScheduleConfig scheduleConfig;

    /**
     * 通知配置
     */
    @Schema(description = "通知配置")
    @Valid
    private RuleEngineCreateReq.NotificationConfig notificationConfig;

    /**
     * 扩展属性
     */
    @Schema(description = "扩展属性")
    private Map<String, Object> attributes;

    /**
     * 更新原因
     */
    @Schema(description = "更新原因", example = "优化规则条件")
    private String updateReason;

    /**
     * 版本说明
     */
    @Schema(description = "版本说明", example = "修复条件判断逻辑")
    private String versionNote;
}
