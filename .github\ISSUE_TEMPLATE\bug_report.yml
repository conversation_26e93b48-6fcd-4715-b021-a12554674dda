name: "\U0001F41E Bug 报告"
description: 在使用 xxx 功能时出现异常
title: "[Bug] "
labels: ["bug"]
body:
  - type: markdown
    attributes:
      value: |
        感谢您使用 ContiNew Admin！请您花些时间填写这份 Bug 报告。
  - type: checkboxes
    id: checkboxes
    attributes:
      label: 请您确认
      description: 在提交 Bug 之前，请确认执行过以下操作。
      options:
        - label: 重启项目和 IDE 后，仍然能够复现此问题
          required: true
        - label: 查阅过 [使用指南](https://continew.top/admin/backend/structure.html) 和 [常见问题](https://continew.top/admin/faq.html) ，仍无解决方法
          required: true
        - label: 查看过控制台是否有报错，如果有报错，下拉控制台到最下查找过 Caused 提示（如果有 Caused 关键字，一般其后都有直观提示，请自行翻译英文），并百度或 Google 后，仍无法解决
          required: true
        - label: 尝试了最新 dev 分支代码（演示环境），仍有相同问题
          required: true
        - label: 搜索了项目 Issues，没有其他人提交过类似的 Bug（如果对应 Bug 尚未解决，您可以先订阅关注该 Issue，为了方便后来者查找问题解决方法，请避免创建重复的 Issue）
          required: true
        - label: 【后端】确认不是依赖组件相关的问题，例如：sa-token、mybatis-plus、snail-job、crane4j、cosid等（如有此类组件相关的问题，请提交至对应组件仓库）
          required: true
        - label: 【前端】确认不是 gi-demo 前端模板相关的组件问题，例如：GiTable、GiForm、基础布局和配置等（如有此类组件相关的问题，请提交至 [gi-demo](https://gitee.com/lin0716/gi-demo) 或对应组件仓库）
          required: true
        - label: 阅读了源码并在 IDE 中进行断点调试
          required: false
        - label: 是否愿意为您提出的 Bug 提交 PR？
          required: false
    validations:
      required: true
  - type: textarea
    id: bug-description
    attributes:
      label: Bug 描述
      description: 清楚而简洁地描述您遇到的 Bug。另外，非常欢迎您对此 Bug 提交 PR。
      placeholder: 例如：在使用 xxx 功能时出现异常
    validations:
      required: true
  - type: textarea
    id: reproduction
    attributes:
      label: 复现步骤
      description: 条理清晰的步骤及图片或演示视频可以帮助快速定位问题。
      placeholder: 例如：1、xxx; 2、xxx;
    validations:
      required: true
  - type: textarea
    id: expected
    attributes:
      label: 预期结果
      description: 清楚而简洁地描述您期望的结果。
      placeholder: 预期结果
    validations:
      required: true
  - type: textarea
    id: environment-info
    attributes:
      label: 版本信息
      description: 请务必填写版本信息（项目启动后在控制台可以查看到），否则视为无效问题。
      value: |
        NPM 版本：
        PNPM 版本：
        ContiNew Admin 版本：
    validations:
      required: true
  - type: textarea
    id: additional-context
    attributes:
      label: 额外补充
      description: 添加您的完整报错信息或屏幕截图，以及一切能帮助定位问题的信息。
