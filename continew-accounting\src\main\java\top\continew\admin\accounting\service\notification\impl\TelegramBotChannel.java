package top.continew.admin.accounting.service.notification.impl;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.continew.admin.accounting.enums.NotificationChannelEnum;
import top.continew.admin.accounting.model.entity.NotificationDO;
import top.continew.admin.accounting.service.notification.AbstractNotificationChannel;
import top.continew.admin.bot.common.BotMessage;
import top.continew.admin.bot.enums.BotPlatformEnum;
import top.continew.admin.bot.service.NotificationService;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Telegram机器人通知渠道
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TelegramBotChannel extends AbstractNotificationChannel {

    private final NotificationService botNotificationService;

    @Override
    public NotificationChannelEnum getChannelType() {
        return NotificationChannelEnum.TELEGRAM_BOT;
    }

    @Override
    public boolean supports(String notificationType) {
        // Telegram机器人支持大部分通知类型
        return !List.of("MARKETING").contains(notificationType);
    }

    @Override
    protected Map<String, Object> doSendNotification(NotificationDO notification, List<Long> targetUsers) {
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failedCount = 0;
        StringBuilder errorMessages = new StringBuilder();
        
        try {
            for (Long userId : targetUsers) {
                try {
                    // 获取用户Telegram聊天ID
                    String chatId = getUserContact(userId);
                    if (StrUtil.isBlank(chatId)) {
                        log.warn("用户Telegram聊天ID为空，跳过发送: userId={}", userId);
                        failedCount++;
                        continue;
                    }
                    
                    // 格式化消息内容
                    Map<String, String> formatted = formatMessage(notification, userId);
                    String content = formatted.get("content");
                    
                    // 创建机器人消息
                    BotMessage botMessage = BotMessage.createNotificationMessage(
                            BotPlatformEnum.TELEGRAM, chatId, content);
                    botMessage.setTimestamp(LocalDateTime.now());
                    
                    // 设置额外属性
                    if (notification.getExtraData() != null) {
                        botMessage.setExtraData(notification.getExtraData());
                    }
                    
                    // 发送通知
                    botNotificationService.sendTelegramNotification(botMessage);
                    
                    successCount++;
                    log.debug("Telegram通知发送成功: userId={}, chatId={}", userId, chatId);
                    
                } catch (Exception e) {
                    failedCount++;
                    String errorMsg = String.format("用户%d发送失败: %s", userId, e.getMessage());
                    errorMessages.append(errorMsg).append("; ");
                    log.error("Telegram通知发送失败: userId={}", userId, e);
                }
            }
            
            result.put("success", failedCount == 0);
            result.put("totalCount", targetUsers.size());
            result.put("successCount", successCount);
            result.put("failedCount", failedCount);
            result.put("channel", getChannelType().getCode());
            
            if (failedCount > 0) {
                result.put("errorMessage", errorMessages.toString());
            }
            
            log.info("Telegram批量通知发送完成: notificationId={}, total={}, success={}, failed={}", 
                    notification.getId(), targetUsers.size(), successCount, failedCount);
            
        } catch (Exception e) {
            log.error("Telegram通知发送异常: notificationId={}", notification.getId(), e);
            result.put("success", false);
            result.put("errorMessage", e.getMessage());
        }
        
        return result;
    }

    @Override
    protected Map<String, String> doFormatMessage(NotificationDO notification, Long targetUser) {
        Map<String, String> result = new HashMap<>();
        
        // 格式化Telegram消息
        String content = formatTelegramMessage(notification);
        
        result.put("content", content);
        result.put("parseMode", "Markdown");
        
        return result;
    }

    @Override
    public Map<String, Object> testConnection(Map<String, Object> config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 测试Telegram机器人配置
            String testChatId = (String) config.get("testChatId");
            if (StrUtil.isBlank(testChatId)) {
                result.put("success", false);
                result.put("errorMessage", "测试聊天ID不能为空");
                return result;
            }
            
            // 发送测试消息
            BotMessage testMessage = BotMessage.createNotificationMessage(
                    BotPlatformEnum.TELEGRAM, testChatId, "🤖 Telegram机器人渠道测试消息");
            testMessage.setTimestamp(LocalDateTime.now());
            
            botNotificationService.sendTelegramNotification(testMessage);
            
            result.put("success", true);
            result.put("message", "Telegram机器人渠道测试成功");
            result.put("testChatId", testChatId);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("errorMessage", e.getMessage());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getChannelStatus() {
        Map<String, Object> status = new HashMap<>();
        
        status.put("channel", getChannelType().getCode());
        status.put("channelName", getChannelType().getName());
        status.put("enabled", isEnabled());
        status.put("description", "Telegram机器人通知渠道");
        
        // 检查机器人服务状态
        try {
            status.put("botServiceStatus", botNotificationService != null ? "OK" : "ERROR");
        } catch (Exception e) {
            status.put("botServiceStatus", "ERROR");
            status.put("botServiceError", e.getMessage());
        }
        
        return status;
    }

    @Override
    public Map<String, Object> validateConfig(Map<String, Object> config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 验证Telegram机器人配置
            String botToken = (String) config.get("botToken");
            String botUsername = (String) config.get("botUsername");
            
            if (StrUtil.isBlank(botToken)) {
                result.put("valid", false);
                result.put("message", "机器人Token不能为空");
                return result;
            }
            
            if (StrUtil.isBlank(botUsername)) {
                result.put("valid", false);
                result.put("message", "机器人用户名不能为空");
                return result;
            }
            
            result.put("valid", true);
            result.put("message", "Telegram机器人配置验证通过");
            
        } catch (Exception e) {
            result.put("valid", false);
            result.put("message", "Telegram机器人配置验证失败: " + e.getMessage());
        }
        
        return result;
    }

    @Override
    public String getUserContact(Long userId) {
        // TODO: 从用户扩展信息中获取Telegram聊天ID
        // 这里需要实现获取用户Telegram聊天ID的逻辑
        return null;
    }

    @Override
    public boolean isEnabled() {
        // TODO: 从配置中读取Telegram机器人渠道是否启用
        return true;
    }

    @Override
    public Map<String, Object> getSendLimits() {
        Map<String, Object> limits = new HashMap<>();
        
        // Telegram API限制
        limits.put("maxBatchSize", 30);
        limits.put("maxDailyCount", 1000);
        limits.put("rateLimit", 20); // 每分钟20条
        limits.put("maxMessageLength", 4096);
        
        return limits;
    }

    @Override
    protected int getBatchSize() {
        return 10; // Telegram批次大小
    }

    @Override
    protected long getBatchDelay() {
        return 3000; // Telegram批次延迟3秒
    }

    // ==================== 私有方法 ====================

    /**
     * 格式化Telegram消息
     */
    private String formatTelegramMessage(NotificationDO notification) {
        StringBuilder message = new StringBuilder();
        
        // 添加图标和标题
        String icon = getNotificationIcon(notification.getNotificationType());
        message.append(icon).append(" *").append(notification.getTitle()).append("*\n\n");
        
        // 添加内容
        message.append(notification.getContent()).append("\n\n");
        
        // 添加时间戳
        message.append("📅 ").append(LocalDateTime.now().toString()).append("\n");
        
        // 添加来源
        message.append("🤖 来自 ContiNew记账系统");
        
        return message.toString();
    }

    /**
     * 获取通知类型图标
     */
    private String getNotificationIcon(String notificationType) {
        return switch (notificationType) {
            case "TRANSACTION" -> "💰";
            case "DEBT_REMINDER" -> "💳";
            case "BUDGET_WARNING" -> "⚠️";
            case "SUBSCRIPTION" -> "📋";
            case "REPORT" -> "📊";
            case "SYNC" -> "🔄";
            case "SECURITY" -> "🔒";
            case "SYSTEM" -> "🔔";
            default -> "📢";
        };
    }

}
