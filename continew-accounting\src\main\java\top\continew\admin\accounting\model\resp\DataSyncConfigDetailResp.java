package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 数据同步配置详情响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "数据同步配置详情响应")
public class DataSyncConfigDetailResp {

    /**
     * ID
     */
    @Schema(description = "ID", example = "1")
    private Long id;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 配置名称
     */
    @Schema(description = "配置名称", example = "交易数据同步到Google Sheets")
    private String configName;

    /**
     * 配置描述
     */
    @Schema(description = "配置描述", example = "将交易数据实时同步到Google Sheets进行分析")
    private String configDescription;

    /**
     * 数据源类型
     */
    @Schema(description = "数据源类型", example = "DATABASE")
    private String sourceType;

    /**
     * 目标类型
     */
    @Schema(description = "目标类型", example = "GOOGLE_SHEETS")
    private String targetType;

    /**
     * 同步方向
     */
    @Schema(description = "同步方向", example = "TO_TARGET")
    private String syncDirection;

    /**
     * 同步模式
     */
    @Schema(description = "同步模式", example = "INCREMENTAL")
    private String syncMode;

    /**
     * 数据类型
     */
    @Schema(description = "数据类型", example = "TRANSACTION")
    private String dataType;

    /**
     * 源配置
     */
    @Schema(description = "源配置")
    private Map<String, Object> sourceConfig;

    /**
     * 目标配置
     */
    @Schema(description = "目标配置")
    private Map<String, Object> targetConfig;

    /**
     * 字段映射
     */
    @Schema(description = "字段映射")
    private Map<String, Object> fieldMapping;

    /**
     * 过滤条件
     */
    @Schema(description = "过滤条件")
    private Map<String, Object> filterCondition;

    /**
     * 转换规则
     */
    @Schema(description = "转换规则")
    private Map<String, Object> transformRules;

    /**
     * 冲突解决策略
     */
    @Schema(description = "冲突解决策略", example = "LOCAL_WINS")
    private String conflictResolution;

    /**
     * 同步频率
     */
    @Schema(description = "同步频率", example = "HOURLY")
    private String syncFrequency;

    /**
     * 同步设置
     */
    @Schema(description = "同步设置")
    private Map<String, Object> syncSettings;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    /**
     * 最后同步时间
     */
    @Schema(description = "最后同步时间", example = "2025-01-01 12:00:00")
    private LocalDateTime lastSyncTime;

    /**
     * 下次同步时间
     */
    @Schema(description = "下次同步时间", example = "2025-01-01 13:00:00")
    private LocalDateTime nextSyncTime;

    /**
     * 同步状态
     */
    @Schema(description = "同步状态", example = "SUCCESS")
    private String syncStatus;

    /**
     * 最后同步结果
     */
    @Schema(description = "最后同步结果", example = "SUCCESS")
    private String lastSyncResult;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 重试次数
     */
    @Schema(description = "重试次数", example = "0")
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    @Schema(description = "最大重试次数", example = "3")
    private Integer maxRetryCount;

    /**
     * 同步统计
     */
    @Schema(description = "同步统计")
    private Map<String, Object> syncStats;

    /**
     * 自定义标签
     */
    @Schema(description = "自定义标签")
    private Map<String, Object> customTags;

    /**
     * 排序
     */
    @Schema(description = "排序", example = "1")
    private Integer sortOrder;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-01-01 10:00:00")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2025-01-01 11:00:00")
    private LocalDateTime updateTime;
}
