-- 群组记账机器人系统核心数据表
-- 基于ContiNew Admin框架的数据库设计

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ================================
-- 群组管理表
-- ================================

-- 群组信息表
DROP TABLE IF EXISTS `acc_group`;
CREATE TABLE `acc_group` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name` varchar(100) NOT NULL COMMENT '群组名称',
    `description` varchar(500) DEFAULT NULL COMMENT '群组描述',
    `platform` varchar(20) NOT NULL COMMENT '平台类型(TELEGRAM/DISCORD)',
    `platform_group_id` varchar(100) NOT NULL COMMENT '平台群组ID',
    `owner_id` bigint NOT NULL COMMENT '群主用户ID',
    `subscription_plan` varchar(20) NOT NULL DEFAULT 'TRIAL' COMMENT '订阅套餐(TRIAL/PRO/BUSINESS/ENTERPRISE)',
    `subscription_expires` datetime DEFAULT NULL COMMENT '订阅到期时间',
    `default_currency` varchar(10) NOT NULL DEFAULT 'USD' COMMENT '默认币种',
    `timezone` varchar(50) NOT NULL DEFAULT 'UTC' COMMENT '时区',
    `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    `settings` json DEFAULT NULL COMMENT '群组设置',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除 1:已删除)',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_platform_group` (`platform`, `platform_group_id`),
    KEY `idx_owner_id` (`owner_id`),
    KEY `idx_platform` (`platform`),
    KEY `idx_subscription_plan` (`subscription_plan`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='群组信息表';

-- 群组成员表
DROP TABLE IF EXISTS `acc_group_member`;
CREATE TABLE `acc_group_member` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `group_id` bigint NOT NULL COMMENT '群组ID',
    `user_id` bigint NOT NULL COMMENT '用户ID',
    `platform_user_id` varchar(100) NOT NULL COMMENT '平台用户ID',
    `nickname` varchar(100) DEFAULT NULL COMMENT '群内昵称',
    `role` varchar(20) NOT NULL DEFAULT 'MEMBER' COMMENT '角色(OWNER/ACCOUNTANT/MEMBER/AUDITOR)',
    `joined_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
    `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(0:已退出 1:正常)',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除 1:已删除)',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_group_user` (`group_id`, `user_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_platform_user_id` (`platform_user_id`),
    KEY `idx_role` (`role`),
    KEY `idx_status` (`status`),
    CONSTRAINT `fk_group_member_group` FOREIGN KEY (`group_id`) REFERENCES `acc_group` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='群组成员表';

-- ================================
-- 钱包管理表
-- ================================

-- 钱包表
DROP TABLE IF EXISTS `acc_wallet`;
CREATE TABLE `acc_wallet` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `group_id` bigint NOT NULL COMMENT '群组ID',
    `name` varchar(100) NOT NULL COMMENT '钱包名称',
    `currency` varchar(10) NOT NULL COMMENT '币种',
    `balance` decimal(20,8) NOT NULL DEFAULT 0.00000000 COMMENT '余额',
    `frozen_amount` decimal(20,8) NOT NULL DEFAULT 0.00000000 COMMENT '冻结金额',
    `description` varchar(500) DEFAULT NULL COMMENT '描述',
    `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除 1:已删除)',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_group_currency` (`group_id`, `currency`),
    KEY `idx_currency` (`currency`),
    KEY `idx_status` (`status`),
    CONSTRAINT `fk_wallet_group` FOREIGN KEY (`group_id`) REFERENCES `acc_group` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='钱包表';

-- 钱包历史记录表
DROP TABLE IF EXISTS `acc_wallet_history`;
CREATE TABLE `acc_wallet_history` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `group_id` bigint NOT NULL COMMENT '群组ID',
    `wallet_id` bigint NOT NULL COMMENT '钱包ID',
    `currency` varchar(10) NOT NULL COMMENT '币种',
    `operation_type` varchar(50) NOT NULL COMMENT '操作类型',
    `amount` decimal(20,8) NOT NULL COMMENT '金额',
    `balance_before` decimal(20,8) NOT NULL COMMENT '操作前余额',
    `balance_after` decimal(20,8) NOT NULL COMMENT '操作后余额',
    `frozen_amount_before` decimal(20,8) DEFAULT 0.00000000 COMMENT '操作前冻结金额',
    `frozen_amount_after` decimal(20,8) DEFAULT 0.00000000 COMMENT '操作后冻结金额',
    `description` varchar(500) DEFAULT NULL COMMENT '描述',
    `operator_id` bigint DEFAULT NULL COMMENT '操作人ID',
    `operate_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    `business_id` bigint DEFAULT NULL COMMENT '关联业务ID',
    `business_type` varchar(50) DEFAULT NULL COMMENT '业务类型',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除 1:已删除)',
    PRIMARY KEY (`id`),
    KEY `idx_group_id` (`group_id`),
    KEY `idx_wallet_id` (`wallet_id`),
    KEY `idx_operation_type` (`operation_type`),
    KEY `idx_operate_time` (`operate_time`),
    KEY `idx_business` (`business_id`, `business_type`),
    CONSTRAINT `fk_wallet_history_group` FOREIGN KEY (`group_id`) REFERENCES `acc_group` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_wallet_history_wallet` FOREIGN KEY (`wallet_id`) REFERENCES `acc_wallet` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='钱包历史记录表';

-- ================================
-- 交易管理表
-- ================================

-- 交易表
DROP TABLE IF EXISTS `acc_transaction`;
CREATE TABLE `acc_transaction` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `group_id` bigint NOT NULL COMMENT '群组ID',
    `user_id` bigint NOT NULL COMMENT '记账用户ID',
    `type` varchar(20) NOT NULL COMMENT '交易类型(INCOME/EXPENSE)',
    `amount` decimal(20,8) NOT NULL COMMENT '金额',
    `currency` varchar(10) NOT NULL COMMENT '币种',
    `description` varchar(500) NOT NULL COMMENT '描述',
    `tags` json DEFAULT NULL COMMENT '标签列表',
    `category` varchar(100) DEFAULT NULL COMMENT '分类',
    `transaction_date` datetime NOT NULL COMMENT '交易时间',
    `attachments` json DEFAULT NULL COMMENT '附件列表',
    `split_info` json DEFAULT NULL COMMENT '分摊信息',
    `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(0:已删除 1:正常)',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除 1:已删除)',
    PRIMARY KEY (`id`),
    KEY `idx_group_id` (`group_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_type` (`type`),
    KEY `idx_currency` (`currency`),
    KEY `idx_category` (`category`),
    KEY `idx_transaction_date` (`transaction_date`),
    KEY `idx_status` (`status`),
    CONSTRAINT `fk_transaction_group` FOREIGN KEY (`group_id`) REFERENCES `acc_group` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易表';

-- 交易分摊表
DROP TABLE IF EXISTS `acc_transaction_split`;
CREATE TABLE `acc_transaction_split` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `transaction_id` bigint NOT NULL COMMENT '交易ID',
    `user_id` bigint NOT NULL COMMENT '用户ID',
    `amount` decimal(20,8) NOT NULL COMMENT '分摊金额',
    `percentage` decimal(5,2) DEFAULT NULL COMMENT '分摊比例',
    `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(0:未支付 1:已支付)',
    `paid_at` datetime DEFAULT NULL COMMENT '支付时间',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除 1:已删除)',
    PRIMARY KEY (`id`),
    KEY `idx_transaction_id` (`transaction_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
    CONSTRAINT `fk_transaction_split_transaction` FOREIGN KEY (`transaction_id`) REFERENCES `acc_transaction` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易分摊表';

-- ================================
-- 分类管理表
-- ================================

-- 分类表
DROP TABLE IF EXISTS `acc_category`;
CREATE TABLE `acc_category` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `group_id` bigint NOT NULL COMMENT '群组ID',
    `parent_id` bigint DEFAULT NULL COMMENT '父分类ID',
    `name` varchar(100) NOT NULL COMMENT '分类名称',
    `icon` varchar(100) DEFAULT NULL COMMENT '图标',
    `color` varchar(20) DEFAULT NULL COMMENT '颜色',
    `type` varchar(20) NOT NULL COMMENT '类型(INCOME/EXPENSE)',
    `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
    `description` varchar(500) DEFAULT NULL COMMENT '描述',
    `is_system` tinyint NOT NULL DEFAULT 0 COMMENT '是否系统分类(0:否 1:是)',
    `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除 1:已删除)',
    PRIMARY KEY (`id`),
    KEY `idx_group_id` (`group_id`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_type` (`type`),
    KEY `idx_sort` (`sort`),
    KEY `idx_status` (`status`),
    CONSTRAINT `fk_category_group` FOREIGN KEY (`group_id`) REFERENCES `acc_group` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_category_parent` FOREIGN KEY (`parent_id`) REFERENCES `acc_category` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类表';

-- ================================
-- 标签管理表
-- ================================

-- 标签表
DROP TABLE IF EXISTS `acc_tag`;
CREATE TABLE `acc_tag` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `group_id` bigint NOT NULL COMMENT '群组ID',
    `name` varchar(50) NOT NULL COMMENT '标签名称',
    `color` varchar(20) DEFAULT NULL COMMENT '颜色',
    `description` varchar(200) DEFAULT NULL COMMENT '描述',
    `usage_count` bigint NOT NULL DEFAULT 0 COMMENT '使用次数',
    `last_used_at` datetime DEFAULT NULL COMMENT '最后使用时间',
    `is_auto_created` tinyint NOT NULL DEFAULT 0 COMMENT '是否自动创建(0:否 1:是)',
    `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除 1:已删除)',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_group_name` (`group_id`, `name`),
    KEY `idx_usage_count` (`usage_count`),
    KEY `idx_last_used_at` (`last_used_at`),
    KEY `idx_status` (`status`),
    CONSTRAINT `fk_tag_group` FOREIGN KEY (`group_id`) REFERENCES `acc_group` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签表';

-- ================================
-- 债务管理表
-- ================================

-- 债务表
DROP TABLE IF EXISTS `acc_debt`;
CREATE TABLE `acc_debt` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `group_id` bigint NOT NULL COMMENT '群组ID',
    `creditor_id` bigint NOT NULL COMMENT '债权人ID',
    `debtor_id` bigint NOT NULL COMMENT '债务人ID',
    `amount` decimal(20,8) NOT NULL COMMENT '债务金额',
    `currency` varchar(10) NOT NULL COMMENT '币种',
    `description` varchar(500) DEFAULT NULL COMMENT '债务描述',
    `due_date` datetime DEFAULT NULL COMMENT '到期时间',
    `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '状态(PENDING/PARTIAL/SETTLED/CANCELLED)',
    `settled_amount` decimal(20,8) NOT NULL DEFAULT 0.00000000 COMMENT '已还金额',
    `settled_at` datetime DEFAULT NULL COMMENT '结算时间',
    `related_transaction_id` bigint DEFAULT NULL COMMENT '关联交易ID',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除 1:已删除)',
    PRIMARY KEY (`id`),
    KEY `idx_group_id` (`group_id`),
    KEY `idx_creditor_id` (`creditor_id`),
    KEY `idx_debtor_id` (`debtor_id`),
    KEY `idx_status` (`status`),
    KEY `idx_due_date` (`due_date`),
    KEY `idx_related_transaction` (`related_transaction_id`),
    CONSTRAINT `fk_debt_group` FOREIGN KEY (`group_id`) REFERENCES `acc_group` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_debt_transaction` FOREIGN KEY (`related_transaction_id`) REFERENCES `acc_transaction` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='债务表';

-- 债务支付记录表
DROP TABLE IF EXISTS `acc_debt_payment`;
CREATE TABLE `acc_debt_payment` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `debt_id` bigint NOT NULL COMMENT '债务ID',
    `amount` decimal(20,8) NOT NULL COMMENT '支付金额',
    `currency` varchar(10) NOT NULL COMMENT '币种',
    `payment_method` varchar(50) DEFAULT NULL COMMENT '支付方式',
    `description` varchar(500) DEFAULT NULL COMMENT '支付描述',
    `payment_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '支付时间',
    `related_transaction_id` bigint DEFAULT NULL COMMENT '关联交易ID',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除 1:已删除)',
    PRIMARY KEY (`id`),
    KEY `idx_debt_id` (`debt_id`),
    KEY `idx_payment_date` (`payment_date`),
    KEY `idx_related_transaction` (`related_transaction_id`),
    CONSTRAINT `fk_debt_payment_debt` FOREIGN KEY (`debt_id`) REFERENCES `acc_debt` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_debt_payment_transaction` FOREIGN KEY (`related_transaction_id`) REFERENCES `acc_transaction` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='债务支付记录表';

-- ================================
-- 报表统计表
-- ================================

-- 报表模板表
DROP TABLE IF EXISTS `acc_report_template`;
CREATE TABLE `acc_report_template` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `group_id` bigint DEFAULT NULL COMMENT '群组ID(NULL表示系统模板)',
    `name` varchar(100) NOT NULL COMMENT '模板名称',
    `type` varchar(50) NOT NULL COMMENT '报表类型',
    `description` varchar(500) DEFAULT NULL COMMENT '模板描述',
    `config` json NOT NULL COMMENT '报表配置',
    `query_sql` text DEFAULT NULL COMMENT '查询SQL',
    `chart_config` json DEFAULT NULL COMMENT '图表配置',
    `is_system` tinyint NOT NULL DEFAULT 0 COMMENT '是否系统模板(0:否 1:是)',
    `is_public` tinyint NOT NULL DEFAULT 0 COMMENT '是否公开(0:否 1:是)',
    `usage_count` bigint NOT NULL DEFAULT 0 COMMENT '使用次数',
    `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除 1:已删除)',
    PRIMARY KEY (`id`),
    KEY `idx_group_id` (`group_id`),
    KEY `idx_type` (`type`),
    KEY `idx_is_system` (`is_system`),
    KEY `idx_is_public` (`is_public`),
    KEY `idx_status` (`status`),
    CONSTRAINT `fk_report_template_group` FOREIGN KEY (`group_id`) REFERENCES `acc_group` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='报表模板表';

-- 报表实例表
DROP TABLE IF EXISTS `acc_report_instance`;
CREATE TABLE `acc_report_instance` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `template_id` bigint NOT NULL COMMENT '模板ID',
    `group_id` bigint NOT NULL COMMENT '群组ID',
    `name` varchar(100) NOT NULL COMMENT '报表名称',
    `parameters` json DEFAULT NULL COMMENT '报表参数',
    `result_data` json DEFAULT NULL COMMENT '报表结果数据',
    `file_path` varchar(500) DEFAULT NULL COMMENT '文件路径',
    `file_size` bigint DEFAULT NULL COMMENT '文件大小',
    `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '状态(PENDING/GENERATING/COMPLETED/FAILED)',
    `generated_at` datetime DEFAULT NULL COMMENT '生成时间',
    `expires_at` datetime DEFAULT NULL COMMENT '过期时间',
    `error_message` text DEFAULT NULL COMMENT '错误信息',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除 1:已删除)',
    PRIMARY KEY (`id`),
    KEY `idx_template_id` (`template_id`),
    KEY `idx_group_id` (`group_id`),
    KEY `idx_status` (`status`),
    KEY `idx_generated_at` (`generated_at`),
    KEY `idx_expires_at` (`expires_at`),
    CONSTRAINT `fk_report_instance_template` FOREIGN KEY (`template_id`) REFERENCES `acc_report_template` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_report_instance_group` FOREIGN KEY (`group_id`) REFERENCES `acc_group` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='报表实例表';

-- ================================
-- 规则引擎表
-- ================================

-- 规则表
DROP TABLE IF EXISTS `acc_rule`;
CREATE TABLE `acc_rule` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `group_id` bigint NOT NULL COMMENT '群组ID',
    `name` varchar(100) NOT NULL COMMENT '规则名称',
    `description` varchar(500) DEFAULT NULL COMMENT '规则描述',
    `type` varchar(50) NOT NULL COMMENT '规则类型',
    `trigger_type` varchar(50) NOT NULL COMMENT '触发类型',
    `conditions` json NOT NULL COMMENT '触发条件',
    `actions` json NOT NULL COMMENT '执行动作',
    `priority` int NOT NULL DEFAULT 0 COMMENT '优先级',
    `version` varchar(20) NOT NULL DEFAULT '1.0.0' COMMENT '版本号',
    `enabled` tinyint NOT NULL DEFAULT 1 COMMENT '是否启用(0:否 1:是)',
    `execution_count` bigint NOT NULL DEFAULT 0 COMMENT '执行次数',
    `last_executed_at` datetime DEFAULT NULL COMMENT '最后执行时间',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除 1:已删除)',
    PRIMARY KEY (`id`),
    KEY `idx_group_id` (`group_id`),
    KEY `idx_type` (`type`),
    KEY `idx_trigger_type` (`trigger_type`),
    KEY `idx_enabled` (`enabled`),
    KEY `idx_priority` (`priority`),
    CONSTRAINT `fk_rule_group` FOREIGN KEY (`group_id`) REFERENCES `acc_group` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='规则表';

-- 规则执行日志表
DROP TABLE IF EXISTS `acc_rule_execution_log`;
CREATE TABLE `acc_rule_execution_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `rule_id` bigint NOT NULL COMMENT '规则ID',
    `group_id` bigint NOT NULL COMMENT '群组ID',
    `trigger_data` json DEFAULT NULL COMMENT '触发数据',
    `execution_result` json DEFAULT NULL COMMENT '执行结果',
    `status` varchar(20) NOT NULL COMMENT '执行状态(SUCCESS/FAILED/SKIPPED)',
    `execution_time` bigint DEFAULT NULL COMMENT '执行耗时(毫秒)',
    `error_message` text DEFAULT NULL COMMENT '错误信息',
    `executed_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '执行时间',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除 1:已删除)',
    PRIMARY KEY (`id`),
    KEY `idx_rule_id` (`rule_id`),
    KEY `idx_group_id` (`group_id`),
    KEY `idx_status` (`status`),
    KEY `idx_executed_at` (`executed_at`),
    CONSTRAINT `fk_rule_execution_log_rule` FOREIGN KEY (`rule_id`) REFERENCES `acc_rule` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_rule_execution_log_group` FOREIGN KEY (`group_id`) REFERENCES `acc_group` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='规则执行日志表';

SET FOREIGN_KEY_CHECKS = 1;
