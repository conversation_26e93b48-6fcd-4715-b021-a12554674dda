package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.extension.crud.model.entity.BaseDO;

import java.time.LocalDateTime;

/**
 * 数据同步配置实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("acc_data_sync_config")
public class DataSyncConfigDO extends BaseDO {

    /**
     * 群组ID
     */
    @TableField("group_id")
    private Long groupId;

    /**
     * 配置名称
     */
    @TableField("config_name")
    private String configName;

    /**
     * 配置描述
     */
    @TableField("config_description")
    private String configDescription;

    /**
     * 数据源类型
     */
    @TableField("source_type")
    private String sourceType;

    /**
     * 目标类型
     */
    @TableField("target_type")
    private String targetType;

    /**
     * 同步方向
     */
    @TableField("sync_direction")
    private String syncDirection;

    /**
     * 同步模式
     */
    @TableField("sync_mode")
    private String syncMode;

    /**
     * 数据类型
     */
    @TableField("data_type")
    private String dataType;

    /**
     * 源配置JSON
     */
    @TableField("source_config_json")
    private String sourceConfigJson;

    /**
     * 目标配置JSON
     */
    @TableField("target_config_json")
    private String targetConfigJson;

    /**
     * 字段映射JSON
     */
    @TableField("field_mapping_json")
    private String fieldMappingJson;

    /**
     * 过滤条件JSON
     */
    @TableField("filter_condition_json")
    private String filterConditionJson;

    /**
     * 转换规则JSON
     */
    @TableField("transform_rules_json")
    private String transformRulesJson;

    /**
     * 冲突解决策略
     */
    @TableField("conflict_resolution")
    private String conflictResolution;

    /**
     * 同步频率
     */
    @TableField("sync_frequency")
    private String syncFrequency;

    /**
     * 同步设置JSON
     */
    @TableField("sync_settings_json")
    private String syncSettingsJson;

    /**
     * 是否启用
     */
    @TableField("enabled")
    private Boolean enabled;

    /**
     * 最后同步时间
     */
    @TableField("last_sync_time")
    private LocalDateTime lastSyncTime;

    /**
     * 下次同步时间
     */
    @TableField("next_sync_time")
    private LocalDateTime nextSyncTime;

    /**
     * 同步状态
     */
    @TableField("sync_status")
    private String syncStatus;

    /**
     * 最后同步结果
     */
    @TableField("last_sync_result")
    private String lastSyncResult;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 重试次数
     */
    @TableField("retry_count")
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    @TableField("max_retry_count")
    private Integer maxRetryCount;

    /**
     * 同步统计JSON
     */
    @TableField("sync_stats_json")
    private String syncStatsJson;

    /**
     * 自定义标签JSON
     */
    @TableField("custom_tags_json")
    private String customTagsJson;

    /**
     * 排序
     */
    @TableField("sort_order")
    private Integer sortOrder;
}
