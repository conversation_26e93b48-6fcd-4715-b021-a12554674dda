package top.continew.admin.accounting.aspect;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import top.continew.admin.accounting.service.UsageTrackingService;
import top.continew.starter.security.util.SecurityUtils;

/**
 * 使用量跟踪切面
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class UsageTrackingAspect {

    private final UsageTrackingService usageTrackingService;

    /**
     * 跟踪交易创建
     */
    @AfterReturning("execution(* top.continew.admin.accounting.service.TransactionService.create(..))")
    @Async("usageTrackingExecutor")
    public void trackTransactionCreation(JoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            if (args.length > 0 && args[0] != null) {
                // 假设第一个参数包含groupId
                Long groupId = extractGroupId(args[0]);
                if (groupId != null) {
                    usageTrackingService.recordTransactionCreated(groupId);
                    recordActiveUser(groupId);
                }
            }
        } catch (Exception e) {
            log.error("跟踪交易创建失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 跟踪导出操作
     */
    @AfterReturning("execution(* top.continew.admin.accounting.service.*.export*(..))")
    @Async("usageTrackingExecutor")
    public void trackExportOperation(JoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            if (args.length > 0 && args[0] != null) {
                Long groupId = extractGroupId(args[0]);
                if (groupId != null) {
                    usageTrackingService.recordExport(groupId);
                    recordActiveUser(groupId);
                }
            }
        } catch (Exception e) {
            log.error("跟踪导出操作失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 跟踪API调用
     */
    @AfterReturning("execution(* top.continew.admin.api.controller.*.*(..))")
    @Async("usageTrackingExecutor")
    public void trackApiCall(JoinPoint joinPoint) {
        try {
            // 从请求上下文中获取groupId
            Long groupId = getCurrentGroupId();
            if (groupId != null) {
                usageTrackingService.recordApiCall(groupId, 1);
                recordActiveUser(groupId);
            }
        } catch (Exception e) {
            log.error("跟踪API调用失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 跟踪Webhook调用
     */
    @AfterReturning("execution(* top.continew.admin.accounting.service.ZapierWebhookService.executeWebhook(..))")
    @Async("usageTrackingExecutor")
    public void trackWebhookCall(JoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            if (args.length > 0 && args[0] != null) {
                Long groupId = extractGroupId(args[0]);
                if (groupId != null) {
                    usageTrackingService.recordWebhookCall(groupId);
                }
            }
        } catch (Exception e) {
            log.error("跟踪Webhook调用失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 跟踪OCR使用
     */
    @AfterReturning("execution(* top.continew.admin.accounting.service.OcrService.recognizeReceipt(..))")
    @Async("usageTrackingExecutor")
    public void trackOcrUsage(JoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            if (args.length > 0 && args[0] != null) {
                Long groupId = extractGroupId(args[0]);
                if (groupId != null) {
                    usageTrackingService.recordOcrUsage(groupId, 1);
                    recordActiveUser(groupId);
                }
            }
        } catch (Exception e) {
            log.error("跟踪OCR使用失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 跟踪文件上传（存储使用）
     */
    @AfterReturning(value = "execution(* top.continew.admin.accounting.service.*.upload*(..))", returning = "result")
    @Async("usageTrackingExecutor")
    public void trackFileUpload(JoinPoint joinPoint, Object result) {
        try {
            Object[] args = joinPoint.getArgs();
            if (args.length > 0 && args[0] != null) {
                Long groupId = extractGroupId(args[0]);
                if (groupId != null) {
                    // 假设返回结果包含文件大小信息
                    long fileSize = extractFileSize(result);
                    if (fileSize > 0) {
                        usageTrackingService.recordStorageUsage(groupId, fileSize);
                        recordActiveUser(groupId);
                    }
                }
            }
        } catch (Exception e) {
            log.error("跟踪文件上传失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 记录活跃用户
     */
    private void recordActiveUser(Long groupId) {
        try {
            Long userId = SecurityUtils.getUserId();
            if (userId != null) {
                usageTrackingService.recordActiveUser(groupId, userId);
            }
        } catch (Exception e) {
            log.debug("记录活跃用户失败: {}", e.getMessage());
        }
    }

    /**
     * 从参数中提取groupId
     */
    private Long extractGroupId(Object arg) {
        try {
            // 使用反射获取groupId字段
            if (arg != null) {
                Class<?> clazz = arg.getClass();
                try {
                    var field = clazz.getDeclaredField("groupId");
                    field.setAccessible(true);
                    Object value = field.get(arg);
                    if (value instanceof Long) {
                        return (Long) value;
                    }
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    // 尝试getter方法
                    try {
                        var method = clazz.getMethod("getGroupId");
                        Object value = method.invoke(arg);
                        if (value instanceof Long) {
                            return (Long) value;
                        }
                    } catch (Exception ex) {
                        log.debug("无法提取groupId: {}", ex.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            log.debug("提取groupId失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取当前请求的groupId
     */
    private Long getCurrentGroupId() {
        try {
            // TODO: 从请求上下文或会话中获取当前操作的groupId
            // 这里需要根据实际的业务逻辑来实现
            // 可能需要从HTTP请求头、路径参数或用户会话中获取
            return null;
        } catch (Exception e) {
            log.debug("获取当前groupId失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从结果中提取文件大小
     */
    private long extractFileSize(Object result) {
        try {
            if (result != null) {
                Class<?> clazz = result.getClass();
                try {
                    var field = clazz.getDeclaredField("fileSize");
                    field.setAccessible(true);
                    Object value = field.get(result);
                    if (value instanceof Long) {
                        return (Long) value;
                    } else if (value instanceof Integer) {
                        return ((Integer) value).longValue();
                    }
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    // 尝试getter方法
                    try {
                        var method = clazz.getMethod("getFileSize");
                        Object value = method.invoke(result);
                        if (value instanceof Long) {
                            return (Long) value;
                        } else if (value instanceof Integer) {
                            return ((Integer) value).longValue();
                        }
                    } catch (Exception ex) {
                        log.debug("无法提取文件大小: {}", ex.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            log.debug("提取文件大小失败: {}", e.getMessage());
        }
        return 0;
    }
}
