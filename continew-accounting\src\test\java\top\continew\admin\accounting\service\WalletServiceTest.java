package top.continew.admin.accounting.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import top.continew.admin.accounting.base.BaseServiceTest;
import top.continew.admin.accounting.mapper.WalletMapper;
import top.continew.admin.accounting.model.entity.WalletDO;
import top.continew.admin.accounting.model.req.WalletCreateReq;
import top.continew.admin.accounting.model.req.WalletQueryReq;
import top.continew.admin.accounting.model.req.WalletUpdateReq;
import top.continew.admin.accounting.model.resp.WalletResp;
import top.continew.admin.accounting.service.impl.WalletServiceImpl;
import top.continew.admin.accounting.util.TestDataUtil;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 钱包服务测试
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@ExtendWith(MockitoExtension.class)
class WalletServiceTest extends BaseServiceTest {

    @Mock
    private WalletMapper walletMapper;

    @InjectMocks
    private WalletServiceImpl walletService;

    private WalletDO testWallet;
    private WalletCreateReq createReq;
    private WalletUpdateReq updateReq;

    @BeforeEach
    @Override
    protected void beforeEach() {
        super.beforeEach();
        testWallet = TestDataUtil.createTestWallet();
        createReq = TestDataUtil.createTestWalletCreateReq();
        
        updateReq = new WalletUpdateReq();
        updateReq.setName("更新后的钱包");
        updateReq.setBalance(new BigDecimal("2000.00"));
        updateReq.setColor("#FF0000");
    }

    @Test
    void testAdd() {
        // Given
        when(walletMapper.insert(any(WalletDO.class))).thenReturn(1);
        when(walletMapper.selectById(anyLong())).thenReturn(testWallet);

        // When
        Long walletId = walletService.add(createReq);

        // Then
        assertNotNull(walletId);
        verify(walletMapper).insert(any(WalletDO.class));
    }

    @Test
    void testGetById() {
        // Given
        when(walletMapper.selectById(1L)).thenReturn(testWallet);

        // When
        WalletResp result = walletService.get(1L);

        // Then
        assertNotNull(result);
        assertEquals(testWallet.getName(), result.getName());
        assertEquals(testWallet.getBalance(), result.getBalance());
        assertEquals(testWallet.getCurrency(), result.getCurrency());
        verify(walletMapper).selectById(1L);
    }

    @Test
    void testUpdate() {
        // Given
        when(walletMapper.selectById(1L)).thenReturn(testWallet);
        when(walletMapper.updateById(any(WalletDO.class))).thenReturn(1);

        // When
        assertDoesNotThrow(() -> walletService.update(updateReq, 1L));

        // Then
        verify(walletMapper).selectById(1L);
        verify(walletMapper).updateById(any(WalletDO.class));
    }

    @Test
    void testDelete() {
        // Given
        when(walletMapper.selectById(1L)).thenReturn(testWallet);
        when(walletMapper.deleteById(1L)).thenReturn(1);

        // When
        assertDoesNotThrow(() -> walletService.delete(Arrays.asList(1L)));

        // Then
        verify(walletMapper).selectById(1L);
        verify(walletMapper).deleteById(1L);
    }

    @Test
    void testPage() {
        // Given
        WalletQueryReq queryReq = new WalletQueryReq();
        queryReq.setGroupId(1L);
        queryReq.setUserId(1L);
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPage(1);
        pageQuery.setSize(10);

        List<WalletDO> wallets = Arrays.asList(testWallet);
        when(walletMapper.selectPage(any(), any())).thenReturn(wallets);
        when(walletMapper.selectCount(any())).thenReturn(1L);

        // When
        PageResp<WalletResp> result = walletService.page(queryReq, pageQuery);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getList().size());
        assertEquals(testWallet.getName(), result.getList().get(0).getName());
    }

    @Test
    void testList() {
        // Given
        WalletQueryReq queryReq = new WalletQueryReq();
        queryReq.setGroupId(1L);
        List<WalletDO> wallets = Arrays.asList(testWallet);
        when(walletMapper.selectList(any())).thenReturn(wallets);

        // When
        List<WalletResp> result = walletService.list(queryReq);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testWallet.getName(), result.get(0).getName());
    }

    @Test
    void testUpdateBalance() {
        // Given
        when(walletMapper.selectById(1L)).thenReturn(testWallet);
        when(walletMapper.updateById(any(WalletDO.class))).thenReturn(1);

        // When
        assertDoesNotThrow(() -> walletService.updateBalance(1L, new BigDecimal("100.00")));

        // Then
        verify(walletMapper).selectById(1L);
        verify(walletMapper).updateById(any(WalletDO.class));
    }

    @Test
    void testTransfer() {
        // Given
        WalletDO fromWallet = TestDataUtil.createTestWallet();
        fromWallet.setId(1L);
        fromWallet.setBalance(new BigDecimal("1000.00"));
        
        WalletDO toWallet = TestDataUtil.createTestWallet();
        toWallet.setId(2L);
        toWallet.setBalance(new BigDecimal("500.00"));

        when(walletMapper.selectById(1L)).thenReturn(fromWallet);
        when(walletMapper.selectById(2L)).thenReturn(toWallet);
        when(walletMapper.updateById(any(WalletDO.class))).thenReturn(1);

        // When
        assertDoesNotThrow(() -> walletService.transfer(1L, 2L, new BigDecimal("200.00"), "转账测试"));

        // Then
        verify(walletMapper).selectById(1L);
        verify(walletMapper).selectById(2L);
        verify(walletMapper, times(2)).updateById(any(WalletDO.class));
    }

    @Test
    void testGetBalance() {
        // Given
        when(walletMapper.selectById(1L)).thenReturn(testWallet);

        // When
        BigDecimal balance = walletService.getBalance(1L);

        // Then
        assertNotNull(balance);
        assertEquals(testWallet.getBalance(), balance);
        verify(walletMapper).selectById(1L);
    }

    @Test
    void testGetTotalBalance() {
        // Given
        when(walletMapper.selectTotalBalance(1L, 1L, "CNY")).thenReturn(new BigDecimal("5000.00"));

        // When
        BigDecimal totalBalance = walletService.getTotalBalance(1L, 1L, "CNY");

        // Then
        assertNotNull(totalBalance);
        assertEquals(new BigDecimal("5000.00"), totalBalance);
        verify(walletMapper).selectTotalBalance(1L, 1L, "CNY");
    }

    @Test
    void testGetStatistics() {
        // Given
        when(walletMapper.selectWalletCount(1L, 1L)).thenReturn(3L);
        when(walletMapper.selectTotalBalance(1L, 1L, null)).thenReturn(new BigDecimal("10000.00"));

        // When
        var statistics = walletService.getStatistics(1L, 1L);

        // Then
        assertNotNull(statistics);
        verify(walletMapper).selectWalletCount(1L, 1L);
        verify(walletMapper).selectTotalBalance(1L, 1L, null);
    }

    @Test
    void testSetDefault() {
        // Given
        when(walletMapper.selectById(1L)).thenReturn(testWallet);
        when(walletMapper.updateById(any(WalletDO.class))).thenReturn(1);
        when(walletMapper.updateDefaultStatus(1L, 1L, false)).thenReturn(1);

        // When
        assertDoesNotThrow(() -> walletService.setDefault(1L, 1L));

        // Then
        verify(walletMapper).selectById(1L);
        verify(walletMapper).updateDefaultStatus(1L, 1L, false);
        verify(walletMapper).updateById(any(WalletDO.class));
    }

}
