# ContiNew Admin 群组记账机器人 API

## 概述

ContiNew Admin 群组记账机器人 API 提供了完整的 RESTful API 接口，支持第三方应用集成群组记账功能。

## 功能特性

- 🔐 **安全认证**: 基于 API Key 的认证机制，支持权限范围控制
- 🚀 **高性能**: 内置限流、缓存机制，确保 API 稳定性
- 📊 **完整功能**: 覆盖群组管理、交易记录、统计分析等核心功能
- 📖 **详细文档**: 提供 Swagger/OpenAPI 文档，便于集成开发
- 🔍 **监控统计**: 提供 API 调用统计和日志记录

## 快速开始

### 1. 获取 API 密钥

首先需要在系统中创建 API 密钥：

```bash
POST /api/v1/auth/api-keys
Content-Type: application/json

{
  "appName": "我的记账应用",
  "description": "用于移动端应用的API访问",
  "scopes": ["groups:read", "transactions:write"],
  "rateLimit": 100
}
```

### 2. 认证方式

API 支持多种认证方式：

#### 方式一：Header 认证（推荐）
```bash
curl -H "X-API-Key: your_api_key" \
     -H "X-API-Secret: your_api_secret" \
     https://api.continew.top/api/v1/groups
```

#### 方式二：Bearer Token
```bash
curl -H "Authorization: Bearer your_api_key" \
     https://api.continew.top/api/v1/groups
```

#### 方式三：查询参数
```bash
curl "https://api.continew.top/api/v1/groups?api_key=your_api_key&api_secret=your_api_secret"
```

### 3. 基本使用示例

#### 获取群组列表
```bash
GET /api/v1/groups
X-API-Key: your_api_key
X-API-Secret: your_api_secret
```

#### 创建交易记录
```bash
POST /api/v1/transactions
Content-Type: application/json
X-API-Key: your_api_key
X-API-Secret: your_api_secret

{
  "groupId": 1,
  "type": "EXPENSE",
  "amount": 100.50,
  "description": "午餐费用",
  "categoryId": 1,
  "walletId": 1
}
```

#### 获取交易统计
```bash
GET /api/v1/transactions/statistics?groupId=1&startDate=2024-01-01&endDate=2024-01-31
X-API-Key: your_api_key
X-API-Secret: your_api_secret
```

## API 接口概览

### 群组管理 API
- `GET /api/v1/groups` - 获取群组列表
- `POST /api/v1/groups` - 创建群组
- `GET /api/v1/groups/{id}` - 获取群组详情
- `PUT /api/v1/groups/{id}` - 更新群组
- `DELETE /api/v1/groups/{id}` - 删除群组
- `POST /api/v1/groups/{id}/join` - 加入群组
- `POST /api/v1/groups/{id}/leave` - 离开群组

### 交易管理 API
- `GET /api/v1/transactions` - 获取交易列表
- `POST /api/v1/transactions` - 创建交易
- `GET /api/v1/transactions/{id}` - 获取交易详情
- `PUT /api/v1/transactions/{id}` - 更新交易
- `DELETE /api/v1/transactions/{id}` - 删除交易
- `GET /api/v1/transactions/statistics` - 获取统计数据

### 认证管理 API
- `POST /api/v1/auth/api-keys` - 创建 API 密钥
- `GET /api/v1/auth/api-keys` - 获取 API 密钥列表
- `PUT /api/v1/auth/api-keys/{id}` - 更新 API 密钥
- `DELETE /api/v1/auth/api-keys/{id}` - 删除 API 密钥

## 权限范围

API 支持细粒度的权限控制，可用的权限范围包括：

| 权限范围 | 描述 |
|---------|------|
| `groups:read` | 读取群组信息 |
| `groups:write` | 创建和修改群组 |
| `groups:delete` | 删除群组 |
| `transactions:read` | 读取交易记录 |
| `transactions:write` | 创建和修改交易 |
| `transactions:delete` | 删除交易记录 |
| `statistics:read` | 读取统计数据 |
| `users:read` | 读取用户信息 |

## 限流机制

API 实现了多层限流机制：

- **API 密钥级别**: 每个 API 密钥可设置独立的限流规则
- **IP 级别**: 基于客户端 IP 的限流保护
- **用户级别**: 基于用户的限流控制

默认限流规则：
- 每分钟 100 次请求
- 每小时 1000 次请求
- 每天 10000 次请求

## 错误处理

API 使用标准的 HTTP 状态码和统一的错误响应格式：

```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": null,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

常见错误码：
- `400` - 请求参数错误
- `401` - 认证失败
- `403` - 权限不足
- `404` - 资源不存在
- `429` - 请求频率超限
- `500` - 服务器内部错误

## SDK 和工具

### JavaScript/Node.js
```javascript
const ContiNewAPI = require('@continew/api-client');

const client = new ContiNewAPI({
  apiKey: 'your_api_key',
  apiSecret: 'your_api_secret',
  baseURL: 'https://api.continew.top'
});

// 获取群组列表
const groups = await client.groups.list();

// 创建交易
const transaction = await client.transactions.create({
  groupId: 1,
  type: 'EXPENSE',
  amount: 100.50,
  description: '午餐费用'
});
```

### Python
```python
from continew_api import ContiNewAPI

client = ContiNewAPI(
    api_key='your_api_key',
    api_secret='your_api_secret',
    base_url='https://api.continew.top'
)

# 获取群组列表
groups = client.groups.list()

# 创建交易
transaction = client.transactions.create({
    'groupId': 1,
    'type': 'EXPENSE',
    'amount': 100.50,
    'description': '午餐费用'
})
```

## 最佳实践

1. **安全性**
   - 妥善保管 API 密钥，不要在客户端代码中硬编码
   - 使用 HTTPS 进行所有 API 调用
   - 定期轮换 API 密钥

2. **性能优化**
   - 合理使用分页参数，避免一次性获取大量数据
   - 利用缓存机制，减少重复请求
   - 使用批量操作接口处理大量数据

3. **错误处理**
   - 实现重试机制处理临时性错误
   - 记录 API 调用日志便于问题排查
   - 监控 API 调用状态和性能指标

## 支持与反馈

- 📖 [API 文档](https://api.continew.top/docs)
- 🐛 [问题反馈](https://github.com/continew-org/continew-admin/issues)
- 💬 [技术交流群](https://continew.top/contact)
- 📧 [邮件支持](mailto:<EMAIL>)

## 更新日志

### v1.0.0 (2024-01-01)
- 🎉 首次发布
- ✨ 支持群组管理 API
- ✨ 支持交易管理 API
- ✨ 支持认证管理 API
- ✨ 支持统计分析 API
- 🔐 实现 API 密钥认证
- 🚀 实现限流和监控机制
