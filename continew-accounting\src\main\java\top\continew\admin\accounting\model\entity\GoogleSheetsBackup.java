package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.admin.common.model.entity.BaseEntity;

import java.time.LocalDateTime;

/**
 * Google Sheets备份实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("acc_google_sheets_backup")
@Schema(description = "Google Sheets备份实体")
public class GoogleSheetsBackup extends BaseEntity {

    /**
     * 备份ID
     */
    @Schema(description = "备份ID", example = "BACKUP_20250101_001")
    @TableField("backup_id")
    private String backupId;

    /**
     * 配置ID
     */
    @Schema(description = "配置ID", example = "1")
    @TableField("config_id")
    private Long configId;

    /**
     * 配置名称
     */
    @Schema(description = "配置名称", example = "我的记账表格")
    @TableField("config_name")
    private String configName;

    /**
     * 同步ID
     */
    @Schema(description = "同步ID", example = "SYNC_20250101_001")
    @TableField("sync_id")
    private String syncId;

    /**
     * Google Sheets ID
     */
    @Schema(description = "Google Sheets ID", example = "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms")
    @TableField("spreadsheet_id")
    private String spreadsheetId;

    /**
     * 工作表名称
     */
    @Schema(description = "工作表名称", example = "账单记录")
    @TableField("sheet_name")
    private String sheetName;

    /**
     * 备份类型
     */
    @Schema(description = "备份类型", example = "BEFORE_SYNC")
    @TableField("backup_type")
    private String backupType;

    /**
     * 备份范围
     */
    @Schema(description = "备份范围", example = "FULL")
    @TableField("backup_scope")
    private String backupScope;

    /**
     * 备份状态
     */
    @Schema(description = "备份状态", example = "SUCCESS")
    @TableField("backup_status")
    private String backupStatus;

    /**
     * 备份文件路径
     */
    @Schema(description = "备份文件路径", example = "/backups/sheets_backup_20250101_100000.xlsx")
    @TableField("backup_file_path")
    private String backupFilePath;

    /**
     * 备份文件名
     */
    @Schema(description = "备份文件名", example = "sheets_backup_20250101_100000.xlsx")
    @TableField("backup_file_name")
    private String backupFileName;

    /**
     * 备份文件大小（字节）
     */
    @Schema(description = "备份文件大小（字节）", example = "1048576")
    @TableField("backup_file_size")
    private Long backupFileSize;

    /**
     * 备份文件格式
     */
    @Schema(description = "备份文件格式", example = "XLSX")
    @TableField("backup_file_format")
    private String backupFileFormat;

    /**
     * 备份记录数
     */
    @Schema(description = "备份记录数", example = "1000")
    @TableField("backup_record_count")
    private Integer backupRecordCount;

    /**
     * 备份开始时间
     */
    @Schema(description = "备份开始时间", example = "2025-01-01T09:59:00")
    @TableField("backup_start_time")
    private LocalDateTime backupStartTime;

    /**
     * 备份结束时间
     */
    @Schema(description = "备份结束时间", example = "2025-01-01T09:59:30")
    @TableField("backup_end_time")
    private LocalDateTime backupEndTime;

    /**
     * 备份耗时（秒）
     */
    @Schema(description = "备份耗时（秒）", example = "30")
    @TableField("backup_duration")
    private Integer backupDuration;

    /**
     * 备份过期时间
     */
    @Schema(description = "备份过期时间", example = "2025-01-31T09:59:30")
    @TableField("backup_expire_time")
    private LocalDateTime backupExpireTime;

    /**
     * 是否自动清理
     */
    @Schema(description = "是否自动清理", example = "true")
    @TableField("auto_cleanup")
    private Boolean autoCleanup;

    /**
     * 备份压缩率
     */
    @Schema(description = "备份压缩率", example = "75.5")
    @TableField("compression_ratio")
    private Double compressionRatio;

    /**
     * 备份校验和
     */
    @Schema(description = "备份校验和", example = "md5:a1b2c3d4e5f6...")
    @TableField("backup_checksum")
    private String backupChecksum;

    /**
     * 备份加密状态
     */
    @Schema(description = "备份加密状态", example = "ENCRYPTED")
    @TableField("encryption_status")
    private String encryptionStatus;

    /**
     * 备份存储位置
     */
    @Schema(description = "备份存储位置", example = "LOCAL")
    @TableField("storage_location")
    private String storageLocation;

    /**
     * 云存储路径
     */
    @Schema(description = "云存储路径", example = "s3://bucket/backups/...")
    @TableField("cloud_storage_path")
    private String cloudStoragePath;

    /**
     * 错误代码
     */
    @Schema(description = "错误代码", example = "STORAGE_ERROR")
    @TableField("error_code")
    private String errorCode;

    /**
     * 错误消息
     */
    @Schema(description = "错误消息", example = "存储空间不足")
    @TableField("error_message")
    private String errorMessage;

    /**
     * 执行人ID
     */
    @Schema(description = "执行人ID", example = "1")
    @TableField("executed_by")
    private Long executedBy;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    @TableField("group_id")
    private Long groupId;

    /**
     * 备份配置JSON
     */
    @Schema(description = "备份配置JSON")
    @TableField("backup_config_json")
    private String backupConfigJson;

    /**
     * 备份元数据JSON
     */
    @Schema(description = "备份元数据JSON")
    @TableField("backup_metadata_json")
    private String backupMetadataJson;

    /**
     * 恢复次数
     */
    @Schema(description = "恢复次数", example = "0")
    @TableField("restore_count")
    private Integer restoreCount;

    /**
     * 最后恢复时间
     */
    @Schema(description = "最后恢复时间", example = "2025-01-01T10:30:00")
    @TableField("last_restore_time")
    private LocalDateTime lastRestoreTime;

    /**
     * 最后恢复人ID
     */
    @Schema(description = "最后恢复人ID", example = "1")
    @TableField("last_restore_by")
    private Long lastRestoreBy;

    /**
     * 备份标签JSON
     */
    @Schema(description = "备份标签JSON")
    @TableField("backup_tags_json")
    private String backupTagsJson;

    /**
     * 扩展属性JSON
     */
    @Schema(description = "扩展属性JSON")
    @TableField("extra_properties_json")
    private String extraPropertiesJson;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "同步前自动备份")
    @TableField("remark")
    private String remark;
}
