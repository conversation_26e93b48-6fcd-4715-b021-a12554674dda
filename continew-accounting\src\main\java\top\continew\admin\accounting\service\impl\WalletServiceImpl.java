package top.continew.admin.accounting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.accounting.enums.TransactionType;
import top.continew.admin.accounting.mapper.WalletMapper;
import top.continew.admin.accounting.model.entity.WalletDO;
import top.continew.admin.accounting.model.query.WalletQuery;
import top.continew.admin.accounting.model.req.WalletCreateReq;
import top.continew.admin.accounting.model.req.WalletUpdateReq;
import top.continew.admin.accounting.model.resp.WalletDetailResp;
import top.continew.admin.accounting.model.resp.WalletListResp;
import top.continew.admin.accounting.model.resp.WalletSummaryResp;
import top.continew.admin.accounting.model.resp.WalletHistoryResp;
import top.continew.admin.accounting.model.entity.WalletHistoryDO;
import top.continew.admin.accounting.mapper.WalletHistoryMapper;
import top.continew.admin.accounting.event.WalletUpdatedEvent;
import top.continew.admin.accounting.service.GroupService;
import top.continew.admin.accounting.service.WalletService;
import top.continew.admin.common.base.service.impl.BaseServiceImpl;
import top.continew.starter.core.util.validate.CheckUtils;
import top.continew.starter.security.context.SecurityContextHolder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 钱包管理业务实现
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WalletServiceImpl extends BaseServiceImpl<WalletMapper, WalletDO, WalletListResp, WalletDetailResp, WalletQuery, WalletCreateReq> implements WalletService {

    private final GroupService groupService;
    private final WalletHistoryMapper walletHistoryMapper;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public void beforeCreate(WalletCreateReq req) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查群组权限
        CheckUtils.throwIf(!groupService.isAdmin(req.getGroupId(), userId), "您没有权限创建钱包");
        
        // 检查钱包是否已存在
        WalletDO existingWallet = baseMapper.selectByGroupIdAndCurrency(req.getGroupId(), req.getCurrency());
        CheckUtils.throwIfNotNull(existingWallet, "该币种的钱包已存在");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(WalletCreateReq req) {
        WalletDO wallet = new WalletDO();
        BeanUtil.copyProperties(req, wallet);
        wallet.setFrozenAmount(BigDecimal.ZERO);
        wallet.setLastUpdateTime(LocalDateTime.now());
        
        super.save(wallet);
        
        log.info("创建钱包成功，群组: {}, 币种: {}, 初始余额: {}", req.getGroupId(), req.getCurrency(), req.getBalance());
        return wallet.getId();
    }

    @Override
    public void beforeUpdate(WalletUpdateReq req, Long id) {
        Long userId = SecurityContextHolder.getUserId();
        WalletDO wallet = super.getById(id);
        CheckUtils.throwIfNull(wallet, "钱包不存在");
        
        // 检查群组权限
        CheckUtils.throwIf(!groupService.isAdmin(wallet.getGroupId(), userId), "您没有权限修改钱包");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(WalletUpdateReq req, Long id) {
        WalletDO wallet = super.getById(id);
        CheckUtils.throwIfNull(wallet, "钱包不存在");
        
        // 更新钱包信息
        if (req.getBalance() != null) {
            wallet.setBalance(req.getBalance());
        }
        if (req.getFrozenAmount() != null) {
            wallet.setFrozenAmount(req.getFrozenAmount());
        }
        wallet.setLastUpdateTime(LocalDateTime.now());
        
        super.updateById(wallet);
        
        log.info("更新钱包成功，ID: {}", id);
    }

    @Override
    public List<WalletListResp> getGroupWallets(Long groupId) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查群组权限
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组成员");
        
        return baseMapper.selectGroupWallets(groupId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WalletDO getOrCreateWallet(Long groupId, String currency) {
        WalletDO wallet = baseMapper.selectByGroupIdAndCurrency(groupId, currency);
        
        if (wallet == null) {
            // 自动创建钱包
            wallet = new WalletDO();
            wallet.setGroupId(groupId);
            wallet.setCurrency(currency);
            wallet.setBalance(BigDecimal.ZERO);
            wallet.setFrozenAmount(BigDecimal.ZERO);
            wallet.setLastUpdateTime(LocalDateTime.now());
            
            super.save(wallet);
            log.info("自动创建钱包，群组: {}, 币种: {}", groupId, currency);
        }
        
        return wallet;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBalance(Long groupId, String currency, BigDecimal amount, TransactionType transactionType) {
        WalletDO wallet = getOrCreateWallet(groupId, currency);
        BigDecimal oldBalance = wallet.getBalance();

        int updateCount;
        if (TransactionType.INCOME.equals(transactionType)) {
            // 收入：增加余额
            updateCount = baseMapper.addBalance(wallet.getId(), amount);
        } else {
            // 支出：减少余额
            updateCount = baseMapper.subtractBalance(wallet.getId(), amount);
        }

        CheckUtils.throwIf(updateCount == 0, "余额不足或钱包状态异常");

        // 获取更新后的余额
        WalletDO updatedWallet = super.getById(wallet.getId());
        BigDecimal newBalance = updatedWallet.getBalance();

        // 记录钱包操作历史
        this.recordWalletHistory(
                groupId,
                wallet.getId(),
                currency,
                transactionType.name(),
                amount,
                oldBalance,
                newBalance,
                "余额更新 - " + transactionType.getDescription(),
                null,
                "BALANCE_UPDATE"
        );

        // 发布钱包更新事件
        WalletUpdatedEvent event = new WalletUpdatedEvent(
                this,
                wallet.getId(),
                groupId,
                currency,
                oldBalance,
                newBalance,
                amount,
                transactionType.name(),
                LocalDateTime.now(),
                SecurityContextHolder.getUserId(),
                "余额更新"
        );
        eventPublisher.publishEvent(event);

        log.info("更新钱包余额成功，群组: {}, 币种: {}, 金额: {}, 类型: {}",
                groupId, currency, amount, transactionType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void freezeAmount(Long groupId, String currency, BigDecimal amount) {
        WalletDO wallet = getOrCreateWallet(groupId, currency);
        BigDecimal oldFrozenAmount = wallet.getFrozenAmount();

        int updateCount = baseMapper.freezeAmount(wallet.getId(), amount);
        CheckUtils.throwIf(updateCount == 0, "可用余额不足");

        // 获取更新后的钱包信息
        WalletDO updatedWallet = super.getById(wallet.getId());
        BigDecimal newFrozenAmount = updatedWallet.getFrozenAmount();

        // 记录钱包操作历史
        this.recordWalletHistory(
                groupId,
                wallet.getId(),
                currency,
                "FREEZE",
                amount,
                wallet.getBalance(),
                updatedWallet.getBalance(),
                "冻结金额",
                null,
                "AMOUNT_FREEZE"
        );

        log.info("冻结金额成功，群组: {}, 币种: {}, 金额: {}", groupId, currency, amount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unfreezeAmount(Long groupId, String currency, BigDecimal amount) {
        WalletDO wallet = getOrCreateWallet(groupId, currency);
        BigDecimal oldFrozenAmount = wallet.getFrozenAmount();

        int updateCount = baseMapper.unfreezeAmount(wallet.getId(), amount);
        CheckUtils.throwIf(updateCount == 0, "冻结金额不足");

        // 获取更新后的钱包信息
        WalletDO updatedWallet = super.getById(wallet.getId());
        BigDecimal newFrozenAmount = updatedWallet.getFrozenAmount();

        // 记录钱包操作历史
        this.recordWalletHistory(
                groupId,
                wallet.getId(),
                currency,
                "UNFREEZE",
                amount,
                wallet.getBalance(),
                updatedWallet.getBalance(),
                "解冻金额",
                null,
                "AMOUNT_UNFREEZE"
        );

        log.info("解冻金额成功，群组: {}, 币种: {}, 金额: {}", groupId, currency, amount);
    }

    @Override
    public BigDecimal getBalance(Long groupId, String currency) {
        BigDecimal balance = baseMapper.getBalance(groupId, currency);
        return balance != null ? balance : BigDecimal.ZERO;
    }

    @Override
    public BigDecimal getAvailableBalance(Long groupId, String currency) {
        BigDecimal availableBalance = baseMapper.getAvailableBalance(groupId, currency);
        return availableBalance != null ? availableBalance : BigDecimal.ZERO;
    }

    @Override
    public boolean hasEnoughBalance(Long groupId, String currency, BigDecimal amount) {
        return baseMapper.hasEnoughBalance(groupId, currency, amount);
    }

    @Override
    public WalletSummaryResp getGroupWalletSummary(Long groupId) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查群组权限
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组成员");
        
        List<WalletListResp> wallets = baseMapper.selectGroupWallets(groupId);
        
        WalletSummaryResp summary = new WalletSummaryResp();
        summary.setGroupId(groupId);
        summary.setTotalWallets(wallets.size());
        
        // 按币种统计
        Map<String, BigDecimal> totalBalances = new HashMap<>();
        Map<String, BigDecimal> totalFrozenAmounts = new HashMap<>();
        Map<String, BigDecimal> totalAvailableBalances = new HashMap<>();
        
        for (WalletListResp wallet : wallets) {
            String currency = wallet.getCurrency();
            totalBalances.merge(currency, wallet.getBalance(), BigDecimal::add);
            totalFrozenAmounts.merge(currency, wallet.getFrozenAmount(), BigDecimal::add);
            totalAvailableBalances.merge(currency, wallet.getAvailableBalance(), BigDecimal::add);
        }
        
        summary.setTotalBalances(totalBalances);
        summary.setTotalFrozenAmounts(totalFrozenAmounts);
        summary.setTotalAvailableBalances(totalAvailableBalances);
        
        // 转换钱包列表
        List<WalletSummaryResp.WalletSummaryItem> summaryItems = wallets.stream()
                .map(wallet -> {
                    WalletSummaryResp.WalletSummaryItem item = new WalletSummaryResp.WalletSummaryItem();
                    item.setId(wallet.getId());
                    item.setCurrency(wallet.getCurrency());
                    item.setBalance(wallet.getBalance());
                    item.setFrozenAmount(wallet.getFrozenAmount());
                    item.setAvailableBalance(wallet.getAvailableBalance());
                    item.setLastUpdateTime(wallet.getLastUpdateTime());
                    return item;
                })
                .collect(Collectors.toList());
        
        summary.setWallets(summaryItems);
        
        return summary;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transfer(Long fromGroupId, Long toGroupId, String currency, BigDecimal amount, String description, Long operatorId) {
        // 检查操作权限
        CheckUtils.throwIf(!groupService.isAdmin(fromGroupId, operatorId), "您没有权限进行转账操作");
        CheckUtils.throwIf(!groupService.isAdmin(toGroupId, operatorId), "您没有权限向目标群组转账");

        // 检查余额
        CheckUtils.throwIf(!hasEnoughBalance(fromGroupId, currency, amount), "转出群组余额不足");

        // 执行转账
        updateBalance(fromGroupId, currency, amount, TransactionType.EXPENSE);
        updateBalance(toGroupId, currency, amount, TransactionType.INCOME);

        log.info("转账成功，从群组 {} 转至群组 {}，币种: {}, 金额: {}, 操作人: {}",
                fromGroupId, toGroupId, currency, amount, operatorId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetBalance(Long groupId, String currency, BigDecimal newBalance, Long operatorId, String reason) {
        // 检查操作权限
        CheckUtils.throwIf(!groupService.isAdmin(groupId, operatorId), "您没有权限重置钱包余额");

        WalletDO wallet = getOrCreateWallet(groupId, currency);
        BigDecimal oldBalance = wallet.getBalance();

        // 更新余额
        baseMapper.updateBalance(wallet.getId(), newBalance);

        // 记录钱包操作历史
        this.recordWalletHistory(
                groupId,
                wallet.getId(),
                currency,
                "RESET",
                newBalance.subtract(oldBalance),
                oldBalance,
                newBalance,
                "管理员重置余额 - " + reason,
                null,
                "BALANCE_RESET"
        );

        log.warn("重置钱包余额，群组: {}, 币种: {}, 原余额: {}, 新余额: {}, 操作人: {}, 原因: {}",
                groupId, currency, oldBalance, newBalance, operatorId, reason);
    }

    @Override
    public List<WalletHistoryResp> getWalletHistory(Long groupId, String currency, Integer limit) {
        Long userId = SecurityContextHolder.getUserId();

        // 检查群组权限
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组成员");

        // 查询钱包历史记录（带操作人信息）
        List<Map<String, Object>> historyMaps = walletHistoryMapper.selectWalletHistoryWithOperator(groupId, currency, limit);

        return historyMaps.stream().map(this::convertToWalletHistoryResp).collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> getWalletOperationStats(Long groupId, String currency,
                                                      LocalDateTime startTime, LocalDateTime endTime) {
        Long userId = SecurityContextHolder.getUserId();

        // 检查群组权限
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组成员");

        return walletHistoryMapper.countWalletOperations(groupId, currency, startTime, endTime);
    }

    @Override
    public List<Map<String, Object>> getWalletBalanceTrend(Long groupId, String currency,
                                                          LocalDateTime startTime, LocalDateTime endTime) {
        Long userId = SecurityContextHolder.getUserId();

        // 检查群组权限
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组成员");

        return walletHistoryMapper.selectBalanceTrend(groupId, currency, startTime, endTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordWalletHistory(Long groupId, Long walletId, String currency, String operationType,
                                   BigDecimal amount, BigDecimal balanceBefore, BigDecimal balanceAfter,
                                   String description, Long businessId, String businessType) {
        Long userId = SecurityContextHolder.getUserId();

        WalletHistoryDO history = new WalletHistoryDO();
        history.setGroupId(groupId);
        history.setWalletId(walletId);
        history.setCurrency(currency);
        history.setOperationType(operationType);
        history.setAmount(amount);
        history.setBalanceBefore(balanceBefore);
        history.setBalanceAfter(balanceAfter);
        history.setDescription(description);
        history.setOperatorId(userId);
        history.setOperateTime(LocalDateTime.now());
        history.setBusinessId(businessId);
        history.setBusinessType(businessType);

        walletHistoryMapper.insert(history);
    }

    /**
     * 转换Map为WalletHistoryResp
     */
    private WalletHistoryResp convertToWalletHistoryResp(Map<String, Object> map) {
        WalletHistoryResp resp = new WalletHistoryResp();
        resp.setId((Long) map.get("id"));
        resp.setOperationType((String) map.get("operation_type"));
        resp.setAmount((BigDecimal) map.get("amount"));
        resp.setBalanceBefore((BigDecimal) map.get("balance_before"));
        resp.setBalanceAfter((BigDecimal) map.get("balance_after"));
        resp.setDescription((String) map.get("description"));
        resp.setOperatorId((Long) map.get("operator_id"));
        resp.setOperatorName((String) map.get("operator_name"));
        resp.setOperateTime((LocalDateTime) map.get("operate_time"));
        resp.setBusinessId((Long) map.get("business_id"));
        resp.setBusinessType((String) map.get("business_type"));
        resp.setCurrency((String) map.get("currency"));
        resp.setRemark((String) map.get("remark"));
        return resp;
    }
}
