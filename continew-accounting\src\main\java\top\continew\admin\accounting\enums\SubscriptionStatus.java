package top.continew.admin.accounting.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 订阅状态枚举
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Getter
@RequiredArgsConstructor
public enum SubscriptionStatus {

    /**
     * 活跃
     */
    ACTIVE("ACTIVE", "活跃"),

    /**
     * 已过期
     */
    EXPIRED("EXPIRED", "已过期"),

    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消"),

    /**
     * 已暂停
     */
    SUSPENDED("SUSPENDED", "已暂停"),

    /**
     * 试用中
     */
    TRIAL("TRIAL", "试用中");

    private final String value;
    private final String description;
}
