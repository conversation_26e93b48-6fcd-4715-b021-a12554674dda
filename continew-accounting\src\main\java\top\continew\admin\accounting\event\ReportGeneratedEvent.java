package top.continew.admin.accounting.event;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.ApplicationEvent;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 报表生成事件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportGeneratedEvent extends ApplicationEvent {

    private Long reportId;
    private Long groupId;
    private String reportType;
    private String reportName;
    private LocalDate startDate;
    private LocalDate endDate;
    private BigDecimal totalIncome;
    private BigDecimal totalExpense;
    private BigDecimal netAmount;
    private Integer transactionCount;
    private LocalDateTime generateTime;
    private Long generateUser;
    private String fileUrl;
    private String format;

    public ReportGeneratedEvent(Object source, Long reportId, Long groupId, String reportType, 
                                String reportName, LocalDate startDate, LocalDate endDate, 
                                BigDecimal totalIncome, BigDecimal totalExpense, BigDecimal netAmount, 
                                Integer transactionCount, LocalDateTime generateTime, Long generateUser, 
                                String fileUrl, String format) {
        super(source);
        this.reportId = reportId;
        this.groupId = groupId;
        this.reportType = reportType;
        this.reportName = reportName;
        this.startDate = startDate;
        this.endDate = endDate;
        this.totalIncome = totalIncome;
        this.totalExpense = totalExpense;
        this.netAmount = netAmount;
        this.transactionCount = transactionCount;
        this.generateTime = generateTime;
        this.generateUser = generateUser;
        this.fileUrl = fileUrl;
        this.format = format;
    }
}

/**
 * 用户加入群组事件
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserJoinedGroupEvent extends ApplicationEvent {

    private Long userId;
    private Long groupId;
    private String username;
    private String nickname;
    private String email;
    private String role;
    private LocalDateTime joinTime;
    private Long invitedBy;
    private String inviteCode;

    public UserJoinedGroupEvent(Object source, Long userId, Long groupId, String username, 
                                String nickname, String email, String role, LocalDateTime joinTime, 
                                Long invitedBy, String inviteCode) {
        super(source);
        this.userId = userId;
        this.groupId = groupId;
        this.username = username;
        this.nickname = nickname;
        this.email = email;
        this.role = role;
        this.joinTime = joinTime;
        this.invitedBy = invitedBy;
        this.inviteCode = inviteCode;
    }
}

/**
 * 用户离开群组事件
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserLeftGroupEvent extends ApplicationEvent {

    private Long userId;
    private Long groupId;
    private String username;
    private String nickname;
    private String role;
    private LocalDateTime leaveTime;
    private String leaveReason;
    private Boolean isKicked;
    private Long kickedBy;

    public UserLeftGroupEvent(Object source, Long userId, Long groupId, String username, 
                              String nickname, String role, LocalDateTime leaveTime, 
                              String leaveReason, Boolean isKicked, Long kickedBy) {
        super(source);
        this.userId = userId;
        this.groupId = groupId;
        this.username = username;
        this.nickname = nickname;
        this.role = role;
        this.leaveTime = leaveTime;
        this.leaveReason = leaveReason;
        this.isKicked = isKicked;
        this.kickedBy = kickedBy;
    }
}

/**
 * 债务创建事件
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DebtCreatedEvent extends ApplicationEvent {

    private Long debtId;
    private Long groupId;
    private Long creditorId;
    private String creditorName;
    private Long debtorId;
    private String debtorName;
    private BigDecimal amount;
    private String description;
    private Long relatedTransactionId;
    private LocalDateTime createTime;
    private LocalDate dueDate;
    private String status;

    public DebtCreatedEvent(Object source, Long debtId, Long groupId, Long creditorId, 
                            String creditorName, Long debtorId, String debtorName, 
                            BigDecimal amount, String description, Long relatedTransactionId, 
                            LocalDateTime createTime, LocalDate dueDate, String status) {
        super(source);
        this.debtId = debtId;
        this.groupId = groupId;
        this.creditorId = creditorId;
        this.creditorName = creditorName;
        this.debtorId = debtorId;
        this.debtorName = debtorName;
        this.amount = amount;
        this.description = description;
        this.relatedTransactionId = relatedTransactionId;
        this.createTime = createTime;
        this.dueDate = dueDate;
        this.status = status;
    }
}

/**
 * 债务结算事件
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DebtSettledEvent extends ApplicationEvent {

    private Long debtId;
    private Long groupId;
    private Long creditorId;
    private String creditorName;
    private Long debtorId;
    private String debtorName;
    private BigDecimal originalAmount;
    private BigDecimal settledAmount;
    private LocalDateTime settleTime;
    private String settleMethod;
    private String settleNote;
    private Long relatedTransactionId;

    public DebtSettledEvent(Object source, Long debtId, Long groupId, Long creditorId, 
                            String creditorName, Long debtorId, String debtorName, 
                            BigDecimal originalAmount, BigDecimal settledAmount, 
                            LocalDateTime settleTime, String settleMethod, String settleNote, 
                            Long relatedTransactionId) {
        super(source);
        this.debtId = debtId;
        this.groupId = groupId;
        this.creditorId = creditorId;
        this.creditorName = creditorName;
        this.debtorId = debtorId;
        this.debtorName = debtorName;
        this.originalAmount = originalAmount;
        this.settledAmount = settledAmount;
        this.settleTime = settleTime;
        this.settleMethod = settleMethod;
        this.settleNote = settleNote;
        this.relatedTransactionId = relatedTransactionId;
    }
}
