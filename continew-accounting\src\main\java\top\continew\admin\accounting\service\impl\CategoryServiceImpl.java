package top.continew.admin.accounting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.accounting.enums.TransactionType;
import top.continew.admin.accounting.mapper.CategoryMapper;
import top.continew.admin.accounting.model.entity.CategoryDO;
import top.continew.admin.accounting.model.query.CategoryQuery;
import top.continew.admin.accounting.model.req.CategoryCreateReq;
import top.continew.admin.accounting.model.req.CategoryUpdateReq;
import top.continew.admin.accounting.model.resp.CategoryDetailResp;
import top.continew.admin.accounting.model.resp.CategoryListResp;
import top.continew.admin.accounting.service.CategoryService;
import top.continew.admin.accounting.service.GroupService;
import top.continew.admin.common.base.service.impl.BaseServiceImpl;
import top.continew.starter.core.util.validate.CheckUtils;
import top.continew.starter.extension.crud.annotation.TreeField;
import top.continew.starter.extension.crud.autoconfigure.CrudProperties;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.security.context.SecurityContextHolder;

import java.util.ArrayList;
import java.util.List;

/**
 * 分类管理业务实现
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CategoryServiceImpl extends BaseServiceImpl<CategoryMapper, CategoryDO, CategoryListResp, CategoryDetailResp, CategoryQuery, CategoryCreateReq> implements CategoryService {

    private final GroupService groupService;
    private final CrudProperties crudProperties;

    @Override
    public void beforeCreate(CategoryCreateReq req) {
        Long userId = SecurityContextHolder.getUserId();

        // 检查群组权限
        CheckUtils.throwIf(!groupService.isAdmin(req.getGroupId(), userId), "您没有权限创建分类");

        // 检查分类名称是否重复
        CheckUtils.throwIf(this.isNameExists(req.getName(), req.getParentId(), req.getGroupId(), null),
                "同级分类下已存在相同名称的分类");

        // 设置默认值
        if (req.getSort() == null) {
            req.setSort(0);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(CategoryCreateReq req) {
        CategoryDO category = new CategoryDO();
        BeanUtil.copyProperties(req, category);
        category.setAncestors(this.getAncestors(req.getParentId()));
        
        super.save(category);
        
        log.info("创建分类成功，群组: {}, 名称: {}, 类型: {}", req.getGroupId(), req.getName(), req.getType());
        return category.getId();
    }

    @Override
    public void beforeUpdate(CategoryUpdateReq req, Long id) {
        Long userId = SecurityContextHolder.getUserId();
        CategoryDO category = super.getById(id);
        CheckUtils.throwIfNull(category, "分类不存在");
        
        // 检查群组权限
        CheckUtils.throwIf(!groupService.isAdmin(category.getGroupId(), userId), "您没有权限修改分类");
        
        // 检查系统分类
        CheckUtils.throwIf(Boolean.TRUE.equals(category.getIsSystem()), "系统分类不允许修改");
        
        // 检查分类名称是否重复
        if (StrUtil.isNotBlank(req.getName())) {
            Long parentId = req.getParentId() != null ? req.getParentId() : category.getParentId();
            CheckUtils.throwIf(this.isNameExists(req.getName(), parentId, category.getGroupId(), id), 
                    "同级分类下已存在相同名称的分类");
        }
        
        // 检查父分类变更
        if (req.getParentId() != null && !req.getParentId().equals(category.getParentId())) {
            // 不能将分类移动到自己的子分类下
            List<Long> childrenIds = this.getChildrenIds(id);
            CheckUtils.throwIf(childrenIds.contains(req.getParentId()), "不能将分类移动到自己的子分类下");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(CategoryUpdateReq req, Long id) {
        CategoryDO category = super.getById(id);
        CheckUtils.throwIfNull(category, "分类不存在");
        
        String oldAncestors = category.getAncestors();
        
        // 更新分类信息
        BeanUtil.copyProperties(req, category, "id", "groupId", "type", "isSystem", "createUser", "createTime");
        
        // 处理父分类变更
        if (req.getParentId() != null && !req.getParentId().equals(category.getParentId())) {
            category.setParentId(req.getParentId());
            String newAncestors = this.getAncestors(req.getParentId());
            category.setAncestors(newAncestors);
            
            // 更新子分类的祖先路径
            this.updateChildrenAncestors(id, newAncestors, oldAncestors);
        }
        
        super.updateById(category);
        
        log.info("更新分类成功，ID: {}", id);
    }

    @Override
    public List<Tree<Long>> tree(CategoryQuery query, SortQuery sortQuery, boolean isSimple) {
        List<CategoryDO> categoryList = this.list(query, sortQuery);
        
        TreeField treeField = CategoryDetailResp.class.getDeclaredAnnotation(TreeField.class);
        if (treeField == null) {
            // 如果没有TreeField注解，使用默认配置
            TreeNodeConfig config = new TreeNodeConfig();
            config.setIdKey("id");
            config.setParentIdKey("parentId");
            config.setNameKey("name");
            config.setWeightKey("sort");
            config.setChildrenKey("children");
            
            return TreeUtil.build(categoryList, 0L, config, (category, tree) -> {
                tree.setId(category.getId());
                tree.setParentId(category.getParentId());
                tree.setName(category.getName());
                tree.setWeight(category.getSort());
                if (!isSimple) {
                    tree.putExtra("type", category.getType());
                    tree.putExtra("icon", category.getIcon());
                    tree.putExtra("color", category.getColor());
                    tree.putExtra("description", category.getDescription());
                    tree.putExtra("isSystem", category.getIsSystem());
                    tree.putExtra("isDefault", category.getIsDefault());
                    tree.putExtra("status", category.getStatus());
                }
            });
        }
        
        TreeNodeConfig treeNodeConfig = crudProperties.getTree().genTreeNodeConfig(treeField);
        return TreeUtil.build(categoryList, treeField.rootId(), treeNodeConfig, (category, tree) -> {
            tree.setId(category.getId());
            tree.setParentId(category.getParentId());
            tree.setName(category.getName());
            tree.setWeight(category.getSort());
            if (!isSimple) {
                tree.putExtra("type", category.getType());
                tree.putExtra("icon", category.getIcon());
                tree.putExtra("color", category.getColor());
                tree.putExtra("description", category.getDescription());
                tree.putExtra("isSystem", category.getIsSystem());
                tree.putExtra("isDefault", category.getIsDefault());
                tree.putExtra("status", category.getStatus());
            }
        });
    }

    @Override
    public List<CategoryListResp> getGroupCategories(Long groupId, String type) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查群组权限
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组成员");
        
        TransactionType transactionType = StrUtil.isNotBlank(type) ? TransactionType.valueOf(type) : null;
        return baseMapper.selectGroupCategories(groupId, transactionType);
    }

    @Override
    public List<Tree<Long>> getGroupCategoryTree(Long groupId, String type) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查群组权限
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组成员");
        
        TransactionType transactionType = StrUtil.isNotBlank(type) ? TransactionType.valueOf(type) : null;
        List<CategoryDO> categories = baseMapper.selectForTree(groupId, transactionType);
        
        TreeNodeConfig config = new TreeNodeConfig();
        config.setIdKey("id");
        config.setParentIdKey("parentId");
        config.setNameKey("name");
        config.setWeightKey("sort");
        config.setChildrenKey("children");
        
        return TreeUtil.build(categories, 0L, config, (category, tree) -> {
            tree.setId(category.getId());
            tree.setParentId(category.getParentId());
            tree.setName(category.getName());
            tree.setWeight(category.getSort());
            tree.putExtra("type", category.getType());
            tree.putExtra("icon", category.getIcon());
            tree.putExtra("color", category.getColor());
            tree.putExtra("description", category.getDescription());
            tree.putExtra("isSystem", category.getIsSystem());
            tree.putExtra("isDefault", category.getIsDefault());
            tree.putExtra("status", category.getStatus());
        });
    }

    @Override
    public CategoryDO getDefaultCategory(Long groupId, String type) {
        TransactionType transactionType = TransactionType.valueOf(type);
        return baseMapper.selectDefaultCategory(groupId, transactionType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createDefaultCategories(Long groupId) {
        // 创建默认收入分类
        this.createDefaultIncomeCategories(groupId);

        // 创建默认支出分类
        this.createDefaultExpenseCategories(groupId);

        log.info("创建默认分类成功，群组: {}", groupId);
    }

    @Override
    public boolean isNameExists(String name, Long parentId, Long groupId, Long id) {
        return baseMapper.existsByName(name, parentId, groupId, id);
    }

    @Override
    public List<Long> getChildrenIds(Long categoryId) {
        return baseMapper.selectChildrenIds(categoryId);
    }

    @Override
    public String getAncestors(Long parentId) {
        if (parentId == null || parentId == 0) {
            return "0";
        }

        String ancestors = baseMapper.selectAncestors(parentId);
        return StrUtil.isNotBlank(ancestors) ? ancestors : "0";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateChildrenAncestors(Long categoryId, String newAncestors, String oldAncestors) {
        if (StrUtil.isBlank(oldAncestors) || StrUtil.isBlank(newAncestors)) {
            return;
        }

        baseMapper.updateChildrenAncestors(categoryId, newAncestors, oldAncestors);
    }

    @Override
    public boolean canDelete(Long categoryId) {
        // 检查是否有子分类
        int childrenCount = baseMapper.countChildren(categoryId);
        if (childrenCount > 0) {
            return false;
        }

        // 检查是否被使用
        int usageCount = baseMapper.countUsage(categoryId);
        return usageCount == 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cascadeDelete(List<Long> categoryIds) {
        if (CollUtil.isEmpty(categoryIds)) {
            return;
        }

        // 获取所有需要删除的分类ID（包含子分类）
        List<Long> allDeleteIds = new ArrayList<>();
        for (Long categoryId : categoryIds) {
            List<Long> childrenIds = this.getChildrenIds(categoryId);
            allDeleteIds.addAll(childrenIds);
        }

        // 去重
        allDeleteIds = CollUtil.distinct(allDeleteIds);

        // 检查是否可以删除
        for (Long categoryId : allDeleteIds) {
            CategoryDO category = super.getById(categoryId);
            CheckUtils.throwIfNull(category, "分类不存在");
            CheckUtils.throwIf(Boolean.TRUE.equals(category.getIsSystem()), "系统分类不允许删除");

            int usageCount = baseMapper.countUsage(categoryId);
            CheckUtils.throwIf(usageCount > 0, "分类 [{}] 已被使用，不允许删除", category.getName());
        }

        // 执行删除
        baseMapper.batchDelete(allDeleteIds);

        log.info("级联删除分类成功，删除数量: {}", allDeleteIds.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void moveCategory(Long categoryId, Long newParentId) {
        CategoryDO category = super.getById(categoryId);
        CheckUtils.throwIfNull(category, "分类不存在");

        Long userId = SecurityContextHolder.getUserId();
        CheckUtils.throwIf(!groupService.isAdmin(category.getGroupId(), userId), "您没有权限移动分类");
        CheckUtils.throwIf(Boolean.TRUE.equals(category.getIsSystem()), "系统分类不允许移动");

        // 不能移动到自己的子分类下
        List<Long> childrenIds = this.getChildrenIds(categoryId);
        CheckUtils.throwIf(childrenIds.contains(newParentId), "不能将分类移动到自己的子分类下");

        String oldAncestors = category.getAncestors();
        String newAncestors = this.getAncestors(newParentId);

        category.setParentId(newParentId);
        category.setAncestors(newAncestors);
        super.updateById(category);

        // 更新子分类的祖先路径
        this.updateChildrenAncestors(categoryId, newAncestors, oldAncestors);

        log.info("移动分类成功，分类: {}, 新父分类: {}", categoryId, newParentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long copyToGroup(Long categoryId, Long targetGroupId, Long operatorId) {
        CategoryDO sourceCategory = super.getById(categoryId);
        CheckUtils.throwIfNull(sourceCategory, "源分类不存在");

        // 检查权限
        CheckUtils.throwIf(!groupService.isAdmin(sourceCategory.getGroupId(), operatorId), "您没有权限复制分类");
        CheckUtils.throwIf(!groupService.isAdmin(targetGroupId, operatorId), "您没有权限向目标群组复制分类");

        // 创建新分类
        CategoryDO newCategory = new CategoryDO();
        BeanUtil.copyProperties(sourceCategory, newCategory, "id", "groupId", "createUser", "createTime", "updateUser", "updateTime");
        newCategory.setGroupId(targetGroupId);
        newCategory.setIsSystem(false); // 复制的分类不是系统分类
        newCategory.setIsDefault(false); // 复制的分类不是默认分类

        // 检查名称冲突
        if (this.isNameExists(newCategory.getName(), newCategory.getParentId(), targetGroupId, null)) {
            newCategory.setName(newCategory.getName() + "_复制");
        }

        super.save(newCategory);

        log.info("复制分类成功，源分类: {}, 目标群组: {}, 新分类: {}", categoryId, targetGroupId, newCategory.getId());
        return newCategory.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResult batchImport(Long groupId, List<CategoryCreateReq> categories, Long operatorId) {
        CheckUtils.throwIf(!groupService.isAdmin(groupId, operatorId), "您没有权限导入分类");

        ImportResult result = new ImportResult();
        result.setTotalCount(categories.size());
        result.setSuccessCount(0);
        result.setFailureCount(0);
        result.setErrorMessages(new ArrayList<>());

        for (CategoryCreateReq req : categories) {
            try {
                req.setGroupId(groupId);
                this.create(req);
                result.setSuccessCount(result.getSuccessCount() + 1);
            } catch (Exception e) {
                result.setFailureCount(result.getFailureCount() + 1);
                result.getErrorMessages().add("分类 [" + req.getName() + "] 导入失败: " + e.getMessage());
                log.warn("导入分类失败，名称: {}, 错误: {}", req.getName(), e.getMessage());
            }
        }

        log.info("批量导入分类完成，群组: {}, 总数: {}, 成功: {}, 失败: {}",
                groupId, result.getTotalCount(), result.getSuccessCount(), result.getFailureCount());

        return result;
    }

    /**
     * 创建默认收入分类
     */
    private void createDefaultIncomeCategories(Long groupId) {
        List<CategoryCreateReq> incomeCategories = List.of(
            createCategoryReq(groupId, "工资收入", null, TransactionType.INCOME, "icon-salary", "#4CAF50", "工资、奖金等劳动收入", 1, true),
            createCategoryReq(groupId, "投资收益", null, TransactionType.INCOME, "icon-investment", "#2196F3", "股票、基金、理财等投资收益", 2, false),
            createCategoryReq(groupId, "兼职收入", null, TransactionType.INCOME, "icon-part-time", "#FF9800", "兼职、副业等收入", 3, false),
            createCategoryReq(groupId, "其他收入", null, TransactionType.INCOME, "icon-other", "#9E9E9E", "其他类型收入", 99, false)
        );

        for (CategoryCreateReq req : incomeCategories) {
            CategoryDO category = new CategoryDO();
            BeanUtil.copyProperties(req, category);
            category.setAncestors("0");
            category.setIsSystem(true);
            super.save(category);
        }
    }

    /**
     * 创建默认支出分类
     */
    private void createDefaultExpenseCategories(Long groupId) {
        List<CategoryCreateReq> expenseCategories = List.of(
            createCategoryReq(groupId, "餐饮", null, TransactionType.EXPENSE, "icon-food", "#FF5722", "餐饮相关支出", 1, true),
            createCategoryReq(groupId, "交通", null, TransactionType.EXPENSE, "icon-transport", "#3F51B5", "交通出行费用", 2, false),
            createCategoryReq(groupId, "购物", null, TransactionType.EXPENSE, "icon-shopping", "#E91E63", "购物消费", 3, false),
            createCategoryReq(groupId, "娱乐", null, TransactionType.EXPENSE, "icon-entertainment", "#9C27B0", "娱乐休闲费用", 4, false),
            createCategoryReq(groupId, "医疗", null, TransactionType.EXPENSE, "icon-medical", "#F44336", "医疗健康费用", 5, false),
            createCategoryReq(groupId, "教育", null, TransactionType.EXPENSE, "icon-education", "#607D8B", "教育培训费用", 6, false),
            createCategoryReq(groupId, "住房", null, TransactionType.EXPENSE, "icon-housing", "#795548", "房租、物业等住房费用", 7, false),
            createCategoryReq(groupId, "其他支出", null, TransactionType.EXPENSE, "icon-other", "#9E9E9E", "其他类型支出", 99, false)
        );

        for (CategoryCreateReq req : expenseCategories) {
            CategoryDO category = new CategoryDO();
            BeanUtil.copyProperties(req, category);
            category.setAncestors("0");
            category.setIsSystem(true);
            super.save(category);
        }
    }

    /**
     * 创建分类请求对象
     */
    private CategoryCreateReq createCategoryReq(Long groupId, String name, Long parentId, TransactionType type,
                                               String icon, String color, String description, Integer sort, Boolean isDefault) {
        CategoryCreateReq req = new CategoryCreateReq();
        req.setGroupId(groupId);
        req.setName(name);
        req.setParentId(parentId);
        req.setType(type);
        req.setIcon(icon);
        req.setColor(color);
        req.setDescription(description);
        req.setSort(sort);
        req.setIsDefault(isDefault);
        return req;
    }
}
