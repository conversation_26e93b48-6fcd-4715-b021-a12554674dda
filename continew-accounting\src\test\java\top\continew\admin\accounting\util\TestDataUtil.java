package top.continew.admin.accounting.util;

import top.continew.admin.accounting.enums.TransactionType;
import top.continew.admin.accounting.model.entity.*;
import top.continew.admin.accounting.model.req.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 测试数据工具类
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public class TestDataUtil {

    /**
     * 创建测试群组
     */
    public static GroupDO createTestGroup() {
        GroupDO group = new GroupDO();
        group.setId(1L);
        group.setName("测试群组");
        group.setDescription("这是一个测试群组");
        group.setOwnerId(1L);
        group.setMemberCount(5);
        group.setCurrency("CNY");
        group.setTimezone("Asia/Shanghai");
        group.setLanguage("zh-CN");
        group.setIsPrivate(false);
        group.setInviteCode("TEST123");
        group.setCreateTime(LocalDateTime.now());
        group.setUpdateTime(LocalDateTime.now());
        group.setDeleted(false);
        return group;
    }

    /**
     * 创建测试群组请求
     */
    public static GroupCreateReq createTestGroupCreateReq() {
        GroupCreateReq req = new GroupCreateReq();
        req.setName("测试群组");
        req.setDescription("这是一个测试群组");
        req.setCurrency("CNY");
        req.setTimezone("Asia/Shanghai");
        req.setLanguage("zh-CN");
        req.setIsPrivate(false);
        return req;
    }

    /**
     * 创建测试交易
     */
    public static TransactionDO createTestTransaction() {
        TransactionDO transaction = new TransactionDO();
        transaction.setId(1L);
        transaction.setGroupId(1L);
        transaction.setUserId(1L);
        transaction.setType(TransactionType.EXPENSE);
        transaction.setAmount(new BigDecimal("50.00"));
        transaction.setCurrency("CNY");
        transaction.setDescription("午餐");
        transaction.setCategoryId(1L);
        transaction.setWalletId(1L);
        transaction.setTransactionTime(LocalDateTime.now());
        transaction.setCreateTime(LocalDateTime.now());
        transaction.setUpdateTime(LocalDateTime.now());
        transaction.setDeleted(false);
        return transaction;
    }

    /**
     * 创建测试交易请求
     */
    public static TransactionCreateReq createTestTransactionCreateReq() {
        TransactionCreateReq req = new TransactionCreateReq();
        req.setGroupId(1L);
        req.setType(TransactionType.EXPENSE);
        req.setAmount(new BigDecimal("50.00"));
        req.setCurrency("CNY");
        req.setDescription("午餐");
        req.setCategoryId(1L);
        req.setWalletId(1L);
        req.setTransactionTime(LocalDateTime.now());
        req.setTags(Arrays.asList("日常", "餐饮"));
        return req;
    }

    /**
     * 创建测试分类
     */
    public static CategoryDO createTestCategory() {
        CategoryDO category = new CategoryDO();
        category.setId(1L);
        category.setGroupId(1L);
        category.setName("餐饮");
        category.setIcon("🍽️");
        category.setColor("#FF6B6B");
        category.setType(TransactionType.EXPENSE);
        category.setParentId(0L);
        category.setSort(1);
        category.setIsDefault(true);
        category.setCreateTime(LocalDateTime.now());
        category.setUpdateTime(LocalDateTime.now());
        category.setDeleted(false);
        return category;
    }

    /**
     * 创建测试分类请求
     */
    public static CategoryCreateReq createTestCategoryCreateReq() {
        CategoryCreateReq req = new CategoryCreateReq();
        req.setGroupId(1L);
        req.setName("餐饮");
        req.setIcon("🍽️");
        req.setColor("#FF6B6B");
        req.setType(TransactionType.EXPENSE);
        req.setParentId(0L);
        req.setSort(1);
        return req;
    }

    /**
     * 创建测试钱包
     */
    public static WalletDO createTestWallet() {
        WalletDO wallet = new WalletDO();
        wallet.setId(1L);
        wallet.setGroupId(1L);
        wallet.setUserId(1L);
        wallet.setName("支付宝");
        wallet.setType("ALIPAY");
        wallet.setBalance(new BigDecimal("1000.00"));
        wallet.setCurrency("CNY");
        wallet.setIcon("💰");
        wallet.setColor("#1677FF");
        wallet.setIsDefault(true);
        wallet.setCreateTime(LocalDateTime.now());
        wallet.setUpdateTime(LocalDateTime.now());
        wallet.setDeleted(false);
        return wallet;
    }

    /**
     * 创建测试钱包请求
     */
    public static WalletCreateReq createTestWalletCreateReq() {
        WalletCreateReq req = new WalletCreateReq();
        req.setGroupId(1L);
        req.setName("支付宝");
        req.setType("ALIPAY");
        req.setBalance(new BigDecimal("1000.00"));
        req.setCurrency("CNY");
        req.setIcon("💰");
        req.setColor("#1677FF");
        req.setIsDefault(true);
        return req;
    }

    /**
     * 创建测试标签
     */
    public static TagDO createTestTag() {
        TagDO tag = new TagDO();
        tag.setId(1L);
        tag.setGroupId(1L);
        tag.setName("日常");
        tag.setColor("#52C41A");
        tag.setUsageCount(10L);
        tag.setCreateTime(LocalDateTime.now());
        tag.setUpdateTime(LocalDateTime.now());
        tag.setDeleted(false);
        return tag;
    }

    /**
     * 创建测试标签请求
     */
    public static TagCreateReq createTestTagCreateReq() {
        TagCreateReq req = new TagCreateReq();
        req.setGroupId(1L);
        req.setName("日常");
        req.setColor("#52C41A");
        return req;
    }

    /**
     * 创建测试债务
     */
    public static DebtDO createTestDebt() {
        DebtDO debt = new DebtDO();
        debt.setId(1L);
        debt.setGroupId(1L);
        debt.setCreditorId(1L);
        debt.setDebtorId(2L);
        debt.setAmount(new BigDecimal("100.00"));
        debt.setCurrency("CNY");
        debt.setDescription("借款");
        debt.setStatus("PENDING");
        debt.setCreateTime(LocalDateTime.now());
        debt.setUpdateTime(LocalDateTime.now());
        debt.setDeleted(false);
        return debt;
    }

    /**
     * 创建测试债务请求
     */
    public static DebtCreateReq createTestDebtCreateReq() {
        DebtCreateReq req = new DebtCreateReq();
        req.setGroupId(1L);
        req.setCreditorId(1L);
        req.setDebtorId(2L);
        req.setAmount(new BigDecimal("100.00"));
        req.setCurrency("CNY");
        req.setDescription("借款");
        return req;
    }

    /**
     * 创建测试用户ID列表
     */
    public static List<Long> createTestUserIds() {
        return Arrays.asList(1L, 2L, 3L, 4L, 5L);
    }

    /**
     * 创建测试金额列表
     */
    public static List<BigDecimal> createTestAmounts() {
        return Arrays.asList(
            new BigDecimal("10.00"),
            new BigDecimal("20.50"),
            new BigDecimal("35.75"),
            new BigDecimal("100.00"),
            new BigDecimal("250.25")
        );
    }

}
