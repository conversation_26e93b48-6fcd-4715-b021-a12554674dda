<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>top.continew.admin</groupId>
        <artifactId>continew-plugin</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>continew-plugin-schedule</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>任务调度插件</description>

    <dependencies>
        <!-- SnailJob（灵活，可靠和快速的分布式任务重试和分布式任务调度平台） -->
        <dependency>
            <groupId>com.aizuda</groupId>
            <artifactId>snail-job-client-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aizuda</groupId>
            <artifactId>snail-job-client-retry-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aizuda</groupId>
            <artifactId>snail-job-client-job-core</artifactId>
        </dependency>

        <!-- OpenFeign（一种基于 Spring Cloud 的声明式 REST 客户端，它简化了与 HTTP 服务交互的过程） -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
    </dependencies>
</project>
