package top.continew.admin.accounting.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import top.continew.admin.accounting.model.query.OcrTaskQuery;
import top.continew.admin.accounting.model.req.OcrReceiptReq;
import top.continew.admin.accounting.model.resp.OcrReceiptResp;
import top.continew.admin.common.model.resp.LabelValueResp;

import java.util.List;
import java.util.Map;

/**
 * OCR收据识别服务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface OcrReceiptService {

    // ==================== OCR识别 ====================

    /**
     * 识别收据
     *
     * @param request 识别请求
     * @return 识别结果
     */
    OcrReceiptResp recognizeReceipt(OcrReceiptReq request);

    /**
     * 异步识别收据
     *
     * @param request 识别请求
     * @return 任务ID
     */
    String recognizeReceiptAsync(OcrReceiptReq request);

    /**
     * 批量识别收据
     *
     * @param requests 识别请求列表
     * @return 识别结果列表
     */
    List<OcrReceiptResp> batchRecognizeReceipts(List<OcrReceiptReq> requests);

    /**
     * 批量异步识别收据
     *
     * @param requests 识别请求列表
     * @return 任务ID列表
     */
    List<String> batchRecognizeReceiptsAsync(List<OcrReceiptReq> requests);

    // ==================== 任务管理 ====================

    /**
     * 获取任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态
     */
    OcrReceiptResp getTaskStatus(String taskId);

    /**
     * 获取任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    OcrReceiptResp getTaskDetail(String taskId);

    /**
     * 分页查询任务
     *
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<OcrReceiptResp> pageTasks(OcrTaskQuery query);

    /**
     * 列表查询任务
     *
     * @param query 查询条件
     * @return 任务列表
     */
    List<OcrReceiptResp> listTasks(OcrTaskQuery query);

    /**
     * 取消任务
     *
     * @param taskId 任务ID
     */
    void cancelTask(String taskId);

    /**
     * 重新执行任务
     *
     * @param taskId 任务ID
     * @return 新任务ID
     */
    String retryTask(String taskId);

    /**
     * 删除任务
     *
     * @param taskId 任务ID
     */
    void deleteTask(String taskId);

    /**
     * 批量删除任务
     *
     * @param taskIds 任务ID列表
     */
    void deleteTasks(List<String> taskIds);

    // ==================== 结果处理 ====================

    /**
     * 手动创建账单
     *
     * @param taskId 任务ID
     * @param categoryId 分类ID
     * @param tags 标签
     * @param remark 备注
     * @return 账单ID
     */
    Long createTransactionFromTask(String taskId, Long categoryId, List<String> tags, String remark);

    /**
     * 更新识别结果
     *
     * @param taskId 任务ID
     * @param receiptInfo 收据信息
     */
    void updateRecognitionResult(String taskId, Map<String, Object> receiptInfo);

    /**
     * 标记结果为正确
     *
     * @param taskId 任务ID
     */
    void markResultAsCorrect(String taskId);

    /**
     * 标记结果为错误
     *
     * @param taskId 任务ID
     * @param feedback 反馈信息
     */
    void markResultAsIncorrect(String taskId, String feedback);

    /**
     * 导出识别结果
     *
     * @param query 查询条件
     * @param format 导出格式
     * @return 导出文件路径
     */
    String exportRecognitionResults(OcrTaskQuery query, String format);

    // ==================== 统计分析 ====================

    /**
     * 获取识别统计
     *
     * @param groupId 群组ID
     * @param days 统计天数
     * @return 统计结果
     */
    Map<String, Object> getRecognitionStats(Long groupId, Integer days);

    /**
     * 获取引擎性能统计
     *
     * @param groupId 群组ID
     * @param days 统计天数
     * @return 性能统计
     */
    Map<String, Object> getEnginePerformanceStats(Long groupId, Integer days);

    /**
     * 获取识别准确率统计
     *
     * @param groupId 群组ID
     * @param days 统计天数
     * @return 准确率统计
     */
    Map<String, Object> getAccuracyStats(Long groupId, Integer days);

    /**
     * 获取商家识别排行
     *
     * @param groupId 群组ID
     * @param days 统计天数
     * @param limit 返回数量
     * @return 商家排行
     */
    List<Map<String, Object>> getMerchantRecognitionRanking(Long groupId, Integer days, Integer limit);

    /**
     * 获取分类识别统计
     *
     * @param groupId 群组ID
     * @param days 统计天数
     * @return 分类统计
     */
    List<Map<String, Object>> getCategoryRecognitionStats(Long groupId, Integer days);

    /**
     * 获取识别趋势
     *
     * @param groupId 群组ID
     * @param days 统计天数
     * @return 趋势数据
     */
    List<Map<String, Object>> getRecognitionTrend(Long groupId, Integer days);

    /**
     * 获取错误分析
     *
     * @param groupId 群组ID
     * @param days 统计天数
     * @return 错误分析
     */
    Map<String, Object> getErrorAnalysis(Long groupId, Integer days);

    // ==================== 配置管理 ====================

    /**
     * 获取OCR引擎配置
     *
     * @param engine 引擎类型
     * @return 配置信息
     */
    Map<String, Object> getEngineConfig(String engine);

    /**
     * 更新OCR引擎配置
     *
     * @param engine 引擎类型
     * @param config 配置信息
     */
    void updateEngineConfig(String engine, Map<String, Object> config);

    /**
     * 测试OCR引擎连接
     *
     * @param engine 引擎类型
     * @return 测试结果
     */
    Map<String, Object> testEngineConnection(String engine);

    /**
     * 获取支持的引擎列表
     *
     * @return 引擎列表
     */
    List<LabelValueResp<String>> getSupportedEngines();

    /**
     * 获取支持的语言列表
     *
     * @param engine 引擎类型
     * @return 语言列表
     */
    List<LabelValueResp<String>> getSupportedLanguages(String engine);

    /**
     * 获取支持的识别模式
     *
     * @param engine 引擎类型
     * @return 识别模式列表
     */
    List<LabelValueResp<String>> getSupportedRecognitionModes(String engine);

    // ==================== 模板管理 ====================

    /**
     * 创建识别模板
     *
     * @param templateName 模板名称
     * @param config 模板配置
     * @return 模板ID
     */
    Long createRecognitionTemplate(String templateName, Map<String, Object> config);

    /**
     * 更新识别模板
     *
     * @param templateId 模板ID
     * @param config 模板配置
     */
    void updateRecognitionTemplate(Long templateId, Map<String, Object> config);

    /**
     * 删除识别模板
     *
     * @param templateId 模板ID
     */
    void deleteRecognitionTemplate(Long templateId);

    /**
     * 获取识别模板列表
     *
     * @param groupId 群组ID
     * @return 模板列表
     */
    List<Map<String, Object>> getRecognitionTemplates(Long groupId);

    /**
     * 应用识别模板
     *
     * @param templateId 模板ID
     * @param request 识别请求
     * @return 应用模板后的请求
     */
    OcrReceiptReq applyRecognitionTemplate(Long templateId, OcrReceiptReq request);

    // ==================== 辅助方法 ====================

    /**
     * 验证图片格式
     *
     * @param fileName 文件名
     * @return 是否支持
     */
    Boolean validateImageFormat(String fileName);

    /**
     * 验证图片大小
     *
     * @param fileSize 文件大小
     * @return 是否符合要求
     */
    Boolean validateImageSize(Long fileSize);

    /**
     * 获取任务状态选项
     *
     * @return 状态选项
     */
    List<LabelValueResp<String>> getTaskStatusOptions();

    /**
     * 获取识别模式选项
     *
     * @return 识别模式选项
     */
    List<LabelValueResp<String>> getRecognitionModeOptions();

    /**
     * 获取识别精度选项
     *
     * @return 识别精度选项
     */
    List<LabelValueResp<String>> getAccuracyOptions();

    /**
     * 检查任务是否存在
     *
     * @param taskId 任务ID
     * @return 是否存在
     */
    Boolean existsTask(String taskId);

    /**
     * 生成任务编号
     *
     * @return 任务编号
     */
    String generateTaskNumber();
}
