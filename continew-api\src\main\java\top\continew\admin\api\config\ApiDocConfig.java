package top.continew.admin.api.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * API文档配置
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Configuration
public class ApiDocConfig {

    @Bean
    public OpenAPI apiOpenAPI() {
        return new OpenAPI()
            .info(apiInfo())
            .servers(apiServers())
            .components(apiComponents())
            .addSecurityItem(new SecurityRequirement().addList("ApiKey"))
            .addSecurityItem(new SecurityRequirement().addList("Bearer"));
    }

    /**
     * API信息
     */
    private Info apiInfo() {
        return new Info()
            .title("ContiNew Admin 群组记账机器人 API")
            .description("提供群组记账功能的RESTful API接口，支持第三方应用集成")
            .version("1.0.0")
            .contact(new Contact()
                .name("ContiNew Team")
                .email("<EMAIL>")
                .url("https://continew.top"))
            .license(new License()
                .name("Apache 2.0")
                .url("https://www.apache.org/licenses/LICENSE-2.0"));
    }

    /**
     * API服务器
     */
    private List<Server> apiServers() {
        return List.of(
            new Server()
                .url("https://api.continew.top")
                .description("生产环境"),
            new Server()
                .url("https://api-test.continew.top")
                .description("测试环境"),
            new Server()
                .url("http://localhost:8080")
                .description("本地开发环境")
        );
    }

    /**
     * API组件
     */
    private Components apiComponents() {
        return new Components()
            .addSecuritySchemes("ApiKey", new SecurityScheme()
                .type(SecurityScheme.Type.APIKEY)
                .in(SecurityScheme.In.HEADER)
                .name("X-API-Key")
                .description("API密钥认证"))
            .addSecuritySchemes("Bearer", new SecurityScheme()
                .type(SecurityScheme.Type.HTTP)
                .scheme("bearer")
                .bearerFormat("JWT")
                .description("Bearer Token认证"));
    }
}
