package top.continew.admin.accounting.service;

import top.continew.admin.accounting.model.entity.SubscriptionDO;
import top.continew.admin.accounting.model.req.SubscriptionCreateReq;
import top.continew.admin.accounting.model.req.SubscriptionUpdateReq;
import top.continew.admin.accounting.model.resp.SubscriptionDetailResp;
import top.continew.admin.accounting.model.resp.SubscriptionListResp;
import top.continew.admin.accounting.model.query.SubscriptionQuery;
import top.continew.starter.extension.crud.service.BaseService;

import java.util.List;

/**
 * 订阅服务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface SubscriptionService extends BaseService<SubscriptionDO, SubscriptionListResp, SubscriptionDetailResp, SubscriptionQuery, SubscriptionCreateReq> {

    /**
     * 创建订阅
     */
    Long createSubscription(SubscriptionCreateReq req);

    /**
     * 更新订阅
     */
    void updateSubscription(SubscriptionUpdateReq req, Long id);

    /**
     * 取消订阅
     */
    void cancelSubscription(Long id, String reason);

    /**
     * 续费订阅
     */
    void renewSubscription(Long id);

    /**
     * 暂停订阅
     */
    void suspendSubscription(Long id, String reason);

    /**
     * 恢复订阅
     */
    void resumeSubscription(Long id);

    /**
     * 升级订阅
     */
    void upgradeSubscription(Long id, Long newPlanId);

    /**
     * 降级订阅
     */
    void downgradeSubscription(Long id, Long newPlanId);

    /**
     * 查询群组当前有效订阅
     */
    SubscriptionDetailResp getActiveSubscriptionByGroupId(Long groupId);

    /**
     * 查询用户订阅历史
     */
    List<SubscriptionListResp> getUserSubscriptionHistory(Long userId);

    /**
     * 查询群组订阅历史
     */
    List<SubscriptionListResp> getGroupSubscriptionHistory(Long groupId);

    /**
     * 检查群组是否有有效订阅
     */
    boolean hasActiveSubscription(Long groupId);

    /**
     * 检查功能权限
     */
    boolean hasFeaturePermission(Long groupId, String featureName);

    /**
     * 检查使用限制
     */
    boolean isUsageLimitExceeded(Long groupId, String limitType, Integer currentUsage);

    /**
     * 获取剩余使用量
     */
    Integer getRemainingUsage(Long groupId, String limitType, Integer currentUsage);

    /**
     * 处理即将过期的订阅
     */
    void handleExpiringSubscriptions();

    /**
     * 处理已过期的订阅
     */
    void handleExpiredSubscriptions();

    /**
     * 自动续费处理
     */
    void processAutoRenewal();

    /**
     * 生成订阅统计报告
     */
    SubscriptionStatsResp getSubscriptionStats();

    /**
     * 获取群组当前有效订阅
     *
     * @param groupId 群组ID
     * @return 订阅信息
     */
    SubscriptionDO getActiveSubscription(Long groupId);

    /**
     * 获取订阅套餐
     *
     * @param planId 套餐ID
     * @return 套餐信息
     */
    top.continew.admin.accounting.model.entity.SubscriptionPlanDO getSubscriptionPlan(Long planId);

    /**
     * 订阅统计响应
     */
    class SubscriptionStatsResp {
        private Long totalSubscriptions;
        private Long activeSubscriptions;
        private Long expiredSubscriptions;
        private Long cancelledSubscriptions;
        private java.math.BigDecimal totalRevenue;
        private java.math.BigDecimal monthlyRevenue;
        
        // getters and setters
        public Long getTotalSubscriptions() { return totalSubscriptions; }
        public void setTotalSubscriptions(Long totalSubscriptions) { this.totalSubscriptions = totalSubscriptions; }
        public Long getActiveSubscriptions() { return activeSubscriptions; }
        public void setActiveSubscriptions(Long activeSubscriptions) { this.activeSubscriptions = activeSubscriptions; }
        public Long getExpiredSubscriptions() { return expiredSubscriptions; }
        public void setExpiredSubscriptions(Long expiredSubscriptions) { this.expiredSubscriptions = expiredSubscriptions; }
        public Long getCancelledSubscriptions() { return cancelledSubscriptions; }
        public void setCancelledSubscriptions(Long cancelledSubscriptions) { this.cancelledSubscriptions = cancelledSubscriptions; }
        public java.math.BigDecimal getTotalRevenue() { return totalRevenue; }
        public void setTotalRevenue(java.math.BigDecimal totalRevenue) { this.totalRevenue = totalRevenue; }
        public java.math.BigDecimal getMonthlyRevenue() { return monthlyRevenue; }
        public void setMonthlyRevenue(java.math.BigDecimal monthlyRevenue) { this.monthlyRevenue = monthlyRevenue; }
    }
}
