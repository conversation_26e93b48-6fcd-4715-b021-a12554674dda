# Discord机器人设置指南

## 概述

ContiNew记账机器人支持Discord平台，提供完整的群组记账功能，包括Slash Commands、按钮交互、选择菜单等现代Discord机器人特性。

## 功能特性

### 核心功能
- 📝 **记账管理** - 支持收入、支出、转账记录
- 💰 **余额查询** - 实时查看钱包余额
- 📊 **统计报表** - 多维度财务分析
- 👥 **群组管理** - 多用户协作记账
- 🏷️ **标签分类** - 灵活的分类管理

### Discord特性
- ⚡ **Slash Commands** - 现代化的命令交互
- 🔘 **按钮交互** - 直观的操作界面
- 📋 **选择菜单** - 便捷的选项选择
- 🔔 **实时通知** - 即时的操作反馈
- 🎨 **富文本格式** - 美观的消息展示

## 机器人设置

### 1. 创建Discord应用

1. 访问 [Discord Developer Portal](https://discord.com/developers/applications)
2. 点击 "New Application" 创建新应用
3. 填写应用名称（如：ContiNew记账助手）
4. 在 "Bot" 页面创建机器人
5. 复制 Bot Token（保密！）

### 2. 配置机器人权限

在 "OAuth2" > "URL Generator" 页面：

**Scopes:**
- `bot`
- `applications.commands`

**Bot Permissions:**
- `Send Messages`
- `Use Slash Commands`
- `Read Message History`
- `Add Reactions`
- `Embed Links`
- `Attach Files`

### 3. 邀请机器人到服务器

1. 使用生成的OAuth2 URL邀请机器人
2. 选择目标服务器
3. 确认权限设置

## 应用配置

### 环境变量设置

```bash
# Discord Bot Token（必需）
DISCORD_BOT_TOKEN=your_discord_bot_token_here

# 测试服务器ID（可选，用于开发测试）
DISCORD_TEST_GUILD_ID=your_test_guild_id_here
```

### application-bot.yml配置

```yaml
bot:
  discord:
    enabled: true
    token: ${DISCORD_BOT_TOKEN}
    test-guild-id: ${DISCORD_TEST_GUILD_ID:}
    command-sync-mode: GLOBAL  # GLOBAL, GUILD, HYBRID
    
    # 活动状态配置
    activity:
      enabled: true
      type: PLAYING
      name: "记账助手 | /help"
    
    # 状态配置
    status:
      online-status: ONLINE
```

## 使用指南

### Slash Commands

#### 基础命令
- `/help` - 显示帮助信息
- `/add` - 添加收支记录
- `/balance` - 查询余额
- `/history` - 查看交易历史
- `/stats` - 查看统计报表

#### 群组管理
- `/group register` - 注册群组
- `/group settings` - 群组设置
- `/group members` - 成员管理
- `/group permissions` - 权限管理

#### 设置命令
- `/settings language` - 设置语言
- `/settings currency` - 设置币种
- `/settings timezone` - 设置时区
- `/settings notifications` - 通知设置

### 记账语法

#### 基本语法
```
/add amount:+100 description:午餐
/add amount:-50 description:打车 category:交通
/add amount:+1000 description:工资 tags:工作,收入 wallet:支付宝
```

#### 文本消息记账
```
+100 午餐 @餐饮 #日常
-50 打车费 @交通 #出行
转账 200 从支付宝到银行卡
```

### 交互功能

#### 按钮操作
- ✅ **确认** - 确认交易
- ❌ **取消** - 取消交易
- ✏️ **编辑** - 编辑交易
- 🗑️ **删除** - 删除交易
- 📋 **详情** - 查看详情

#### 选择菜单
- 📂 **分类选择** - 选择交易分类
- 💳 **钱包选择** - 选择钱包
- 📅 **时间周期** - 选择统计周期

## 开发指南

### 项目结构

```
continew-bot/src/main/java/top/continew/admin/bot/discord/
├── DiscordBotService.java          # 主服务类
├── handler/
│   └── DiscordCommandHandler.java  # 命令处理器
├── listener/
│   └── DiscordEventListener.java   # 事件监听器
└── config/
    └── DiscordBotConfig.java       # 配置类
```

### 添加新命令

1. 在 `DiscordBotService.registerCommands()` 中注册命令
2. 在 `DiscordCommandHandler.handleSlashCommand()` 中处理命令
3. 在 `DiscordEventListener` 中添加事件处理

### 自定义交互

```java
// 创建按钮
Button confirmButton = Button.primary("confirm_transaction:123", "确认");
Button cancelButton = Button.secondary("cancel_transaction:123", "取消");

// 发送带按钮的消息
discordBotService.sendMessageWithButtons(channelId, message, confirmButton, cancelButton);

// 处理按钮交互
public void handleButtonInteraction(ButtonInteractionEvent event) {
    String buttonId = event.getComponentId();
    // 处理逻辑...
}
```

## 故障排除

### 常见问题

1. **机器人无响应**
   - 检查Token是否正确
   - 确认机器人在线状态
   - 检查网络连接

2. **Slash Commands不显示**
   - 确认机器人有 `applications.commands` 权限
   - 检查命令注册是否成功
   - 等待Discord缓存更新（最多1小时）

3. **权限错误**
   - 检查机器人权限设置
   - 确认频道权限配置
   - 验证用户权限

### 日志调试

```yaml
logging:
  level:
    top.continew.admin.bot.discord: DEBUG
    net.dv8tion.jda: INFO
```

### 测试命令

使用 `/test` 和 `/debug` 命令进行基本功能测试。

## 安全注意事项

1. **Token安全**
   - 不要在代码中硬编码Token
   - 使用环境变量或配置文件
   - 定期轮换Token

2. **权限控制**
   - 最小权限原则
   - 定期审查权限设置
   - 监控异常活动

3. **数据保护**
   - 加密敏感数据
   - 定期备份数据
   - 遵守隐私法规

## 更新日志

### v1.0.0 (2025-01-01)
- ✨ 初始版本发布
- 🎯 支持基础Slash Commands
- 🔘 实现按钮交互功能
- 📋 添加选择菜单支持
- 🔔 集成消息队列处理
- 📊 完整的记账功能支持

## 支持与反馈

如有问题或建议，请通过以下方式联系：

- 📧 邮箱：<EMAIL>
- 🐛 Issue：[GitHub Issues](https://github.com/continew-org/continew-admin)
- 💬 讨论：[GitHub Discussions](https://github.com/continew-org/continew-admin/discussions)
