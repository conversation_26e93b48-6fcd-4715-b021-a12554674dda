<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.accounting.mapper.FileStorageMapper">

    <!-- 获取文件统计信息 -->
    <select id="getFileStatistics" resultType="map">
        SELECT 
            COUNT(*) as totalFileCount,
            COALESCE(SUM(file_size), 0) as totalFileSize,
            COUNT(CASE WHEN DATE(create_time) = CURDATE() THEN 1 END) as todayUploadCount,
            COALESCE(SUM(CASE WHEN DATE(create_time) = CURDATE() THEN file_size ELSE 0 END), 0) as todayUploadSize,
            COUNT(CASE WHEN YEAR(create_time) = YEAR(NOW()) AND MONTH(create_time) = MONTH(NOW()) THEN 1 END) as monthUploadCount,
            COALESCE(SUM(CASE WHEN YEAR(create_time) = YEAR(NOW()) AND MONTH(create_time) = MONTH(NOW()) THEN file_size ELSE 0 END), 0) as monthUploadSize
        FROM acc_file_storage 
        WHERE is_deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
    </select>

    <!-- 获取文件类型统计 -->
    <select id="getFileTypeStatistics" resultType="map">
        SELECT 
            file_type as fileType,
            COUNT(*) as fileCount,
            COALESCE(SUM(file_size), 0) as fileSize,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM acc_file_storage WHERE is_deleted = 0 
                <if test="groupId != null">AND group_id = #{groupId}</if>), 2) as percentage
        FROM acc_file_storage 
        WHERE is_deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        GROUP BY file_type
        ORDER BY fileCount DESC
    </select>

    <!-- 获取存储类型统计 -->
    <select id="getStorageTypeStatistics" resultType="map">
        SELECT 
            storage_type as storageType,
            COUNT(*) as fileCount,
            COALESCE(SUM(file_size), 0) as fileSize,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM acc_file_storage WHERE is_deleted = 0 
                <if test="groupId != null">AND group_id = #{groupId}</if>), 2) as percentage
        FROM acc_file_storage 
        WHERE is_deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        GROUP BY storage_type
        ORDER BY fileCount DESC
    </select>

    <!-- 获取访问权限统计 -->
    <select id="getAccessTypeStatistics" resultType="map">
        SELECT 
            access_type as accessType,
            COUNT(*) as fileCount,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM acc_file_storage WHERE is_deleted = 0 
                <if test="groupId != null">AND group_id = #{groupId}</if>), 2) as percentage
        FROM acc_file_storage 
        WHERE is_deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        GROUP BY access_type
        ORDER BY fileCount DESC
    </select>

    <!-- 获取上传趋势统计 -->
    <select id="getUploadTrendStatistics" resultType="map">
        SELECT 
            DATE(create_time) as date,
            COUNT(*) as uploadCount,
            COALESCE(SUM(file_size), 0) as uploadSize
        FROM acc_file_storage 
        WHERE is_deleted = 0
            AND create_time >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        GROUP BY DATE(create_time)
        ORDER BY date DESC
    </select>

    <!-- 获取热门文件排行 -->
    <select id="getPopularFileStatistics" resultType="map">
        SELECT 
            id as fileId,
            file_name as fileName,
            access_count as accessCount,
            download_count as downloadCount
        FROM acc_file_storage 
        WHERE is_deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        ORDER BY (access_count + download_count) DESC
        LIMIT #{limit}
    </select>

    <!-- 获取大文件排行 -->
    <select id="getLargeFileStatistics" resultType="map">
        SELECT 
            id as fileId,
            file_name as fileName,
            file_size as fileSize
        FROM acc_file_storage 
        WHERE is_deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        ORDER BY file_size DESC
        LIMIT #{limit}
    </select>

    <!-- 获取存储使用情况 -->
    <select id="getStorageUsage" resultType="map">
        SELECT 
            storage_type,
            COUNT(*) as fileCount,
            COALESCE(SUM(file_size), 0) as totalSize,
            AVG(file_size) as avgSize,
            MAX(file_size) as maxSize,
            MIN(file_size) as minSize
        FROM acc_file_storage 
        WHERE is_deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        GROUP BY storage_type
    </select>

    <!-- 清理过期文件 -->
    <update id="cleanExpiredFiles">
        UPDATE acc_file_storage 
        SET is_deleted = 1, delete_time = NOW()
        WHERE is_deleted = 0 
            AND expire_time IS NOT NULL 
            AND expire_time &lt; NOW()
    </update>

    <!-- 清理临时文件 -->
    <update id="cleanTemporaryFiles">
        UPDATE acc_file_storage 
        SET is_deleted = 1, delete_time = NOW()
        WHERE is_deleted = 0 
            AND is_temporary = 1 
            AND create_time &lt; DATE_SUB(NOW(), INTERVAL #{days} DAY)
    </update>

    <!-- 获取文件访问日志 -->
    <select id="getFileAccessLog" resultType="map">
        SELECT 
            access_type as accessType,
            access_time as accessTime,
            user_agent as userAgent,
            client_ip as clientIp,
            user_id as userId
        FROM acc_file_access_log 
        WHERE file_id = #{fileId}
        ORDER BY access_time DESC
        LIMIT #{limit}
    </select>

    <!-- 记录文件访问 -->
    <insert id="recordFileAccess">
        INSERT INTO acc_file_access_log (file_id, access_type, user_agent, client_ip, user_id, access_time)
        VALUES (#{fileId}, #{accessType}, #{userAgent}, #{clientIp}, #{userId}, NOW())
    </insert>

    <!-- 验证文件完整性 -->
    <select id="validateFileIntegrity" resultType="map">
        SELECT 
            id,
            file_name as fileName,
            file_md5 as fileMd5,
            file_sha256 as fileSha256,
            file_size as fileSize,
            file_path as filePath,
            storage_type as storageType
        FROM acc_file_storage 
        WHERE id = #{fileId} AND is_deleted = 0
    </select>

    <!-- 同步文件元数据 -->
    <update id="syncFileMetadata">
        UPDATE acc_file_storage 
        SET update_time = NOW()
        WHERE id = #{fileId}
    </update>

    <!-- 批量更新文件状态 -->
    <update id="batchUpdateFileStatus">
        UPDATE acc_file_storage 
        SET process_status = #{status}, update_time = NOW()
        WHERE id IN
        <foreach collection="fileIds" item="fileId" open="(" separator="," close=")">
            #{fileId}
        </foreach>
    </update>

    <!-- 获取重复文件 -->
    <select id="getDuplicateFiles" resultType="map">
        SELECT 
            file_md5 as fileMd5,
            file_sha256 as fileSha256,
            COUNT(*) as duplicateCount,
            GROUP_CONCAT(id) as fileIds,
            GROUP_CONCAT(file_name) as fileNames
        FROM acc_file_storage 
        WHERE is_deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        GROUP BY file_md5, file_sha256
        HAVING COUNT(*) > 1
        ORDER BY duplicateCount DESC
    </select>

    <!-- 获取孤儿文件 -->
    <select id="getOrphanFiles" resultType="map">
        SELECT 
            id,
            file_name as fileName,
            file_path as filePath,
            file_size as fileSize,
            create_time as createTime
        FROM acc_file_storage 
        WHERE is_deleted = 0
            AND business_id IS NULL
            AND business_type IS NULL
            AND is_temporary = 0
            AND create_time &lt; DATE_SUB(NOW(), INTERVAL 7 DAY)
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        ORDER BY create_time ASC
    </select>

    <!-- 获取文件大小分布 -->
    <select id="getFileSizeDistribution" resultType="map">
        SELECT 
            CASE 
                WHEN file_size &lt; 1024 THEN '&lt;1KB'
                WHEN file_size &lt; 1048576 THEN '1KB-1MB'
                WHEN file_size &lt; 10485760 THEN '1MB-10MB'
                WHEN file_size &lt; 104857600 THEN '10MB-100MB'
                ELSE '&gt;100MB'
            END as sizeRange,
            COUNT(*) as fileCount,
            COALESCE(SUM(file_size), 0) as totalSize
        FROM acc_file_storage 
        WHERE is_deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        GROUP BY 
            CASE 
                WHEN file_size &lt; 1024 THEN '&lt;1KB'
                WHEN file_size &lt; 1048576 THEN '1KB-1MB'
                WHEN file_size &lt; 10485760 THEN '1MB-10MB'
                WHEN file_size &lt; 104857600 THEN '10MB-100MB'
                ELSE '&gt;100MB'
            END
        ORDER BY 
            CASE 
                WHEN file_size &lt; 1024 THEN 1
                WHEN file_size &lt; 1048576 THEN 2
                WHEN file_size &lt; 10485760 THEN 3
                WHEN file_size &lt; 104857600 THEN 4
                ELSE 5
            END
    </select>

    <!-- 获取文件扩展名统计 -->
    <select id="getFileExtensionStatistics" resultType="map">
        SELECT 
            file_extension as fileExtension,
            COUNT(*) as fileCount,
            COALESCE(SUM(file_size), 0) as totalSize
        FROM acc_file_storage 
        WHERE is_deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        GROUP BY file_extension
        ORDER BY fileCount DESC
        LIMIT 20
    </select>

    <!-- 获取用户上传统计 -->
    <select id="getUserUploadStatistics" resultType="map">
        SELECT 
            upload_user_id as uploadUserId,
            COUNT(*) as uploadCount,
            COALESCE(SUM(file_size), 0) as totalSize
        FROM acc_file_storage 
        WHERE is_deleted = 0
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        GROUP BY upload_user_id
        ORDER BY uploadCount DESC
        LIMIT #{limit}
    </select>

</mapper>
