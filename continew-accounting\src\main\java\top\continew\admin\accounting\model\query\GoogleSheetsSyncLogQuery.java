package top.continew.admin.accounting.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.admin.common.model.query.PageQuery;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Google Sheets同步记录查询条件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "Google Sheets同步记录查询条件")
public class GoogleSheetsSyncLogQuery extends PageQuery {

    /**
     * 配置ID
     */
    @Schema(description = "配置ID", example = "1")
    private Long configId;

    /**
     * 配置ID列表
     */
    @Schema(description = "配置ID列表")
    private List<Long> configIds;

    /**
     * 同步ID
     */
    @Schema(description = "同步ID", example = "SYNC_20250101_001")
    private String syncId;

    /**
     * 同步类型
     */
    @Schema(description = "同步类型", allowableValues = {"FULL", "INCREMENTAL", "CUSTOM"})
    private String syncType;

    /**
     * 同步方向
     */
    @Schema(description = "同步方向", allowableValues = {"TO_SHEETS", "FROM_SHEETS", "BIDIRECTIONAL"})
    private String syncDirection;

    /**
     * 同步状态
     */
    @Schema(description = "同步状态", allowableValues = {"PENDING", "RUNNING", "SUCCESS", "FAILED", "CANCELLED", "TIMEOUT"})
    private String syncStatus;

    /**
     * 触发方式
     */
    @Schema(description = "触发方式", allowableValues = {"MANUAL", "SCHEDULED", "AUTO", "API"})
    private String triggerType;

    /**
     * 执行人
     */
    @Schema(description = "执行人", example = "1")
    private Long executedBy;

    /**
     * 开始时间范围 - 开始
     */
    @Schema(description = "开始时间范围 - 开始", example = "2025-01-01T00:00:00")
    private LocalDateTime startTimeBegin;

    /**
     * 开始时间范围 - 结束
     */
    @Schema(description = "开始时间范围 - 结束", example = "2025-01-31T23:59:59")
    private LocalDateTime startTimeEnd;

    /**
     * 结束时间范围 - 开始
     */
    @Schema(description = "结束时间范围 - 开始", example = "2025-01-01T00:00:00")
    private LocalDateTime endTimeBegin;

    /**
     * 结束时间范围 - 结束
     */
    @Schema(description = "结束时间范围 - 结束", example = "2025-01-31T23:59:59")
    private LocalDateTime endTimeEnd;

    /**
     * 执行耗时范围 - 最小值（秒）
     */
    @Schema(description = "执行耗时范围 - 最小值（秒）", example = "1")
    private Integer minDurationSeconds;

    /**
     * 执行耗时范围 - 最大值（秒）
     */
    @Schema(description = "执行耗时范围 - 最大值（秒）", example = "300")
    private Integer maxDurationSeconds;

    /**
     * 处理记录数范围 - 最小值
     */
    @Schema(description = "处理记录数范围 - 最小值", example = "1")
    private Integer minProcessedCount;

    /**
     * 处理记录数范围 - 最大值
     */
    @Schema(description = "处理记录数范围 - 最大值", example = "10000")
    private Integer maxProcessedCount;

    /**
     * 成功记录数范围 - 最小值
     */
    @Schema(description = "成功记录数范围 - 最小值", example = "1")
    private Integer minSuccessCount;

    /**
     * 成功记录数范围 - 最大值
     */
    @Schema(description = "成功记录数范围 - 最大值", example = "10000")
    private Integer maxSuccessCount;

    /**
     * 失败记录数范围 - 最小值
     */
    @Schema(description = "失败记录数范围 - 最小值", example = "0")
    private Integer minFailedCount;

    /**
     * 失败记录数范围 - 最大值
     */
    @Schema(description = "失败记录数范围 - 最大值", example = "100")
    private Integer maxFailedCount;

    /**
     * 错误代码
     */
    @Schema(description = "错误代码", example = "NETWORK_ERROR")
    private String errorCode;

    /**
     * 错误消息关键字
     */
    @Schema(description = "错误消息关键字", example = "timeout")
    private String errorMessageKeyword;

    /**
     * 群组ID列表
     */
    @Schema(description = "群组ID列表")
    private List<Long> groupIds;

    /**
     * 关键字搜索
     */
    @Schema(description = "关键字搜索", example = "同步")
    private String keyword;

    /**
     * 是否包含已删除
     */
    @Schema(description = "是否包含已删除", example = "false")
    private Boolean includeDeleted = false;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段", example = "startTime", allowableValues = {
            "startTime", "endTime", "durationSeconds", "processedCount", "successCount", "failedCount"
    })
    private String sortField = "startTime";

    /**
     * 排序方向
     */
    @Schema(description = "排序方向", example = "DESC", allowableValues = {"ASC", "DESC"})
    private String sortDirection = "DESC";

    /**
     * 高级过滤条件
     */
    @Schema(description = "高级过滤条件")
    private AdvancedFilter advancedFilter;

    /**
     * 高级过滤条件
     */
    @Data
    @Schema(description = "高级过滤条件")
    public static class AdvancedFilter {

        /**
         * 成功率范围 - 最小值
         */
        @Schema(description = "成功率范围 - 最小值", example = "0.8")
        private Double minSuccessRate;

        /**
         * 成功率范围 - 最大值
         */
        @Schema(description = "成功率范围 - 最大值", example = "1.0")
        private Double maxSuccessRate;

        /**
         * 是否有重试
         */
        @Schema(description = "是否有重试", example = "false")
        private Boolean hasRetry;

        /**
         * 重试次数范围 - 最小值
         */
        @Schema(description = "重试次数范围 - 最小值", example = "0")
        private Integer minRetryCount;

        /**
         * 重试次数范围 - 最大值
         */
        @Schema(description = "重试次数范围 - 最大值", example = "5")
        private Integer maxRetryCount;

        /**
         * 是否有冲突
         */
        @Schema(description = "是否有冲突", example = "false")
        private Boolean hasConflict;

        /**
         * 冲突解决策略
         */
        @Schema(description = "冲突解决策略", allowableValues = {"LOCAL_WINS", "REMOTE_WINS", "MERGE", "SKIP", "MANUAL"})
        private String conflictResolution;

        /**
         * 是否创建了备份
         */
        @Schema(description = "是否创建了备份", example = "true")
        private Boolean hasBackup;

        /**
         * 数据源类型
         */
        @Schema(description = "数据源类型", allowableValues = {"DATABASE", "SHEETS", "BOTH"})
        private String dataSource;

        /**
         * 是否异步执行
         */
        @Schema(description = "是否异步执行", example = "true")
        private Boolean isAsync;

        /**
         * 批量大小范围 - 最小值
         */
        @Schema(description = "批量大小范围 - 最小值", example = "10")
        private Integer minBatchSize;

        /**
         * 批量大小范围 - 最大值
         */
        @Schema(description = "批量大小范围 - 最大值", example = "1000")
        private Integer maxBatchSize;

        /**
         * 自定义标签
         */
        @Schema(description = "自定义标签")
        private List<String> customTags;
    }
}
