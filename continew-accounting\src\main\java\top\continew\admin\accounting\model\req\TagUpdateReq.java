package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.io.Serial;
import java.io.Serializable;

/**
 * 标签更新请求参数
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "标签更新请求参数")
public class TagUpdateReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 标签名称
     */
    @Schema(description = "标签名称", example = "工作")
    @Size(max = 30, message = "标签名称长度不能超过30个字符")
    private String name;

    /**
     * 标签颜色
     */
    @Schema(description = "标签颜色", example = "#FF5722")
    @Pattern(regexp = "^#[0-9A-Fa-f]{6}$", message = "颜色格式不正确")
    private String color;

    /**
     * 标签图标
     */
    @Schema(description = "标签图标", example = "icon-work")
    @Size(max = 100, message = "图标长度不能超过100个字符")
    private String icon;

    /**
     * 描述
     */
    @Schema(description = "描述", example = "工作相关标签")
    @Size(max = 200, message = "描述长度不能超过200个字符")
    private String description;

    /**
     * 排序
     */
    @Schema(description = "排序", example = "1")
    @Min(value = 0, message = "排序不能小于0")
    @Max(value = 9999, message = "排序不能大于9999")
    private Integer sort;

    /**
     * 是否为默认标签
     */
    @Schema(description = "是否为默认标签", example = "false")
    private Boolean isDefault;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "1")
    private Integer status;
}
