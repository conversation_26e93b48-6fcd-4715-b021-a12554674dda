package top.continew.admin.bot.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import top.continew.admin.bot.common.BotMessageQueue;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 消息监控服务
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MessageMonitorService {

    private final RabbitAdmin rabbitAdmin;
    private final AsyncMessageManager asyncMessageManager;
    
    // 监控指标
    private final Map<String, AtomicLong> queueMetrics = new ConcurrentHashMap<>();
    private final Map<String, LocalDateTime> lastHealthCheck = new ConcurrentHashMap<>();
    private LocalDateTime lastReportTime = LocalDateTime.now();

    /**
     * 监控队列健康状态
     */
    @Scheduled(fixedDelay = 60000) // 每分钟检查一次
    public void monitorQueueHealth() {
        try {
            checkQueueHealth(BotMessageQueue.QUEUE_BOT_MESSAGE);
            checkQueueHealth(BotMessageQueue.QUEUE_BOT_NOTIFICATION);
            checkQueueHealth(BotMessageQueue.QUEUE_BOT_COMMAND);
            
            log.debug("队列健康检查完成");
            
        } catch (Exception e) {
            log.error("队列健康检查失败", e);
        }
    }

    /**
     * 检查单个队列健康状态
     */
    private void checkQueueHealth(String queueName) {
        try {
            Properties queueProperties = rabbitAdmin.getQueueProperties(queueName);
            if (queueProperties != null) {
                // 获取队列消息数量
                Object messageCount = queueProperties.get("QUEUE_MESSAGE_COUNT");
                if (messageCount != null) {
                    long count = Long.parseLong(messageCount.toString());
                    queueMetrics.put(queueName + "_message_count", new AtomicLong(count));
                    
                    // 检查队列是否积压
                    if (count > 1000) {
                        log.warn("队列 {} 消息积压，当前消息数量: {}", queueName, count);
                        // 可以在这里触发告警
                    }
                }
                
                // 获取消费者数量
                Object consumerCount = queueProperties.get("QUEUE_CONSUMER_COUNT");
                if (consumerCount != null) {
                    long count = Long.parseLong(consumerCount.toString());
                    queueMetrics.put(queueName + "_consumer_count", new AtomicLong(count));
                    
                    // 检查是否有消费者
                    if (count == 0) {
                        log.warn("队列 {} 没有消费者", queueName);
                    }
                }
                
                lastHealthCheck.put(queueName, LocalDateTime.now());
                
            } else {
                log.warn("无法获取队列 {} 的属性信息", queueName);
            }
            
        } catch (Exception e) {
            log.error("检查队列 {} 健康状态失败", queueName, e);
        }
    }

    /**
     * 生成监控报告
     */
    @Scheduled(fixedDelay = 300000) // 每5分钟生成一次报告
    public void generateMonitoringReport() {
        try {
            StringBuilder report = new StringBuilder();
            report.append("\n=== 消息队列监控报告 ===\n");
            report.append("生成时间: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n\n");
            
            // 队列状态
            report.append("📊 队列状态:\n");
            queueMetrics.forEach((key, value) -> {
                report.append("  ").append(key).append(": ").append(value.get()).append("\n");
            });
            
            // 消息统计
            report.append("\n📈 消息统计:\n");
            Map<String, Object> messageStats = asyncMessageManager.getMessageStatistics();
            messageStats.forEach((key, value) -> {
                report.append("  ").append(key).append(": ").append(value).append("\n");
            });
            
            // 重试队列状态
            report.append("\n🔄 重试队列状态:\n");
            Map<String, Object> retryStatus = asyncMessageManager.getRetryQueueStatus();
            report.append("  队列大小: ").append(retryStatus.get("queueSize")).append("\n");
            
            // 健康检查状态
            report.append("\n💚 健康检查状态:\n");
            lastHealthCheck.forEach((queue, time) -> {
                report.append("  ").append(queue).append(": ").append(time.format(DateTimeFormatter.ofPattern("HH:mm:ss"))).append("\n");
            });
            
            report.append("\n=== 报告结束 ===\n");
            
            log.info(report.toString());
            lastReportTime = LocalDateTime.now();
            
        } catch (Exception e) {
            log.error("生成监控报告失败", e);
        }
    }

    /**
     * 检查系统性能
     */
    @Scheduled(fixedDelay = 120000) // 每2分钟检查一次
    public void checkSystemPerformance() {
        try {
            // 检查内存使用情况
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            double memoryUsagePercent = (double) usedMemory / totalMemory * 100;
            
            if (memoryUsagePercent > 80) {
                log.warn("内存使用率过高: {:.2f}%", memoryUsagePercent);
            }
            
            // 检查消息处理延迟
            Map<String, Object> messageStats = asyncMessageManager.getMessageStatistics();
            Object retryQueueSize = messageStats.get("retryQueueSize");
            if (retryQueueSize instanceof Integer && (Integer) retryQueueSize > 100) {
                log.warn("重试队列积压严重，大小: {}", retryQueueSize);
            }
            
            log.debug("系统性能检查完成 - 内存使用率: {:.2f}%", memoryUsagePercent);
            
        } catch (Exception e) {
            log.error("系统性能检查失败", e);
        }
    }

    /**
     * 清理过期监控数据
     */
    @Scheduled(fixedDelay = 3600000) // 每小时清理一次
    public void cleanupExpiredData() {
        try {
            LocalDateTime expireTime = LocalDateTime.now().minusHours(24);
            
            // 清理过期的健康检查记录
            lastHealthCheck.entrySet().removeIf(entry -> entry.getValue().isBefore(expireTime));
            
            log.info("过期监控数据清理完成");
            
        } catch (Exception e) {
            log.error("清理过期监控数据失败", e);
        }
    }

    /**
     * 获取实时监控数据
     */
    public Map<String, Object> getRealTimeMetrics() {
        Map<String, Object> metrics = new ConcurrentHashMap<>();
        
        // 队列指标
        Map<String, Object> queueData = new ConcurrentHashMap<>();
        queueMetrics.forEach((key, value) -> queueData.put(key, value.get()));
        metrics.put("queueMetrics", queueData);
        
        // 消息统计
        metrics.put("messageStatistics", asyncMessageManager.getMessageStatistics());
        
        // 重试队列状态
        metrics.put("retryQueueStatus", asyncMessageManager.getRetryQueueStatus());
        
        // 系统信息
        Runtime runtime = Runtime.getRuntime();
        Map<String, Object> systemInfo = new ConcurrentHashMap<>();
        systemInfo.put("totalMemory", runtime.totalMemory());
        systemInfo.put("freeMemory", runtime.freeMemory());
        systemInfo.put("usedMemory", runtime.totalMemory() - runtime.freeMemory());
        systemInfo.put("memoryUsagePercent", 
            (double) (runtime.totalMemory() - runtime.freeMemory()) / runtime.totalMemory() * 100);
        metrics.put("systemInfo", systemInfo);
        
        // 健康检查状态
        metrics.put("healthCheckStatus", lastHealthCheck);
        
        // 最后报告时间
        metrics.put("lastReportTime", lastReportTime);
        
        return metrics;
    }

    /**
     * 获取队列详细信息
     */
    public Map<String, Object> getQueueDetails(String queueName) {
        Map<String, Object> details = new ConcurrentHashMap<>();
        
        try {
            Properties queueProperties = rabbitAdmin.getQueueProperties(queueName);
            if (queueProperties != null) {
                queueProperties.forEach((key, value) -> details.put(key.toString(), value));
            }
            
            // 添加监控指标
            queueMetrics.entrySet().stream()
                .filter(entry -> entry.getKey().startsWith(queueName))
                .forEach(entry -> details.put(entry.getKey(), entry.getValue().get()));
                
        } catch (Exception e) {
            log.error("获取队列 {} 详细信息失败", queueName, e);
            details.put("error", e.getMessage());
        }
        
        return details;
    }

    /**
     * 触发手动健康检查
     */
    public Map<String, Object> performManualHealthCheck() {
        Map<String, Object> result = new ConcurrentHashMap<>();
        
        try {
            // 检查所有队列
            String[] queues = {
                BotMessageQueue.QUEUE_BOT_MESSAGE,
                BotMessageQueue.QUEUE_BOT_NOTIFICATION,
                BotMessageQueue.QUEUE_BOT_COMMAND
            };
            
            for (String queue : queues) {
                checkQueueHealth(queue);
                result.put(queue, "健康");
            }
            
            result.put("checkTime", LocalDateTime.now());
            result.put("status", "成功");
            
        } catch (Exception e) {
            log.error("手动健康检查失败", e);
            result.put("status", "失败");
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取告警信息
     */
    public Map<String, Object> getAlerts() {
        Map<String, Object> alerts = new ConcurrentHashMap<>();
        
        // 检查队列积压
        queueMetrics.entrySet().stream()
            .filter(entry -> entry.getKey().endsWith("_message_count"))
            .forEach(entry -> {
                if (entry.getValue().get() > 1000) {
                    alerts.put("queue_backlog_" + entry.getKey(), 
                        "队列积压: " + entry.getValue().get() + " 条消息");
                }
            });
        
        // 检查消费者数量
        queueMetrics.entrySet().stream()
            .filter(entry -> entry.getKey().endsWith("_consumer_count"))
            .forEach(entry -> {
                if (entry.getValue().get() == 0) {
                    alerts.put("no_consumer_" + entry.getKey(), 
                        "队列没有消费者");
                }
            });
        
        // 检查内存使用
        Runtime runtime = Runtime.getRuntime();
        double memoryUsage = (double) (runtime.totalMemory() - runtime.freeMemory()) / runtime.totalMemory() * 100;
        if (memoryUsage > 80) {
            alerts.put("high_memory_usage", String.format("内存使用率过高: %.2f%%", memoryUsage));
        }
        
        return alerts;
    }
}
