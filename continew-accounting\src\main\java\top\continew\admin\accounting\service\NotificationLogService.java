package top.continew.admin.accounting.service;

import top.continew.admin.accounting.model.entity.NotificationLogDO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 通知日志服务
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface NotificationLogService {

    // ==================== 日志记录 ====================

    /**
     * 记录发送日志
     *
     * @param notificationId 通知ID
     * @param channel 渠道
     * @param targetUser 目标用户
     * @param status 状态
     * @param responseData 响应数据
     * @param errorMessage 错误信息
     * @param duration 耗时
     * @return 日志ID
     */
    Long recordSendLog(Long notificationId, String channel, Long targetUser, String status,
                      Map<String, Object> responseData, String errorMessage, Long duration);

    /**
     * 批量记录发送日志
     *
     * @param logs 日志列表
     * @return 记录数量
     */
    Integer batchRecordSendLogs(List<NotificationLogDO> logs);

    /**
     * 更新日志状态
     *
     * @param logId 日志ID
     * @param status 新状态
     * @param responseData 响应数据
     * @param errorMessage 错误信息
     * @return 是否成功
     */
    Boolean updateLogStatus(Long logId, String status, Map<String, Object> responseData, String errorMessage);

    /**
     * 记录回调日志
     *
     * @param notificationId 通知ID
     * @param channel 渠道
     * @param callbackData 回调数据
     * @param externalMessageId 外部消息ID
     * @return 是否成功
     */
    Boolean recordCallbackLog(Long notificationId, String channel, Map<String, Object> callbackData, 
                             String externalMessageId);

    // ==================== 日志查询 ====================

    /**
     * 获取通知发送日志
     *
     * @param notificationId 通知ID
     * @return 日志列表
     */
    List<NotificationLogDO> getNotificationLogs(Long notificationId);

    /**
     * 获取用户通知日志
     *
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 日志列表
     */
    List<NotificationLogDO> getUserNotificationLogs(Long userId, String startDate, String endDate, Integer limit);

    /**
     * 获取渠道发送日志
     *
     * @param channel 渠道
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 日志列表
     */
    List<NotificationLogDO> getChannelSendLogs(String channel, String startDate, String endDate, Integer limit);

    /**
     * 分页查询日志
     *
     * @param groupId 群组ID
     * @param notificationId 通知ID
     * @param channel 渠道
     * @param status 状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    Map<String, Object> getLogPageList(Long groupId, Long notificationId, String channel, String status,
                                      String startDate, String endDate, Integer pageNum, Integer pageSize);

    /**
     * 获取失败日志
     *
     * @param groupId 群组ID
     * @param hours 小时数
     * @param limit 限制数量
     * @return 失败日志列表
     */
    List<NotificationLogDO> getFailedLogs(Long groupId, Integer hours, Integer limit);

    // ==================== 日志统计 ====================

    /**
     * 获取发送统计
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 发送统计
     */
    Map<String, Object> getSendStatistics(Long groupId, String startDate, String endDate);

    /**
     * 获取渠道统计
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 渠道统计
     */
    List<Map<String, Object>> getChannelStatistics(Long groupId, String startDate, String endDate);

    /**
     * 获取用户统计
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 用户统计
     */
    List<Map<String, Object>> getUserStatistics(Long groupId, String startDate, String endDate, Integer limit);

    /**
     * 获取错误统计
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 错误统计
     */
    List<Map<String, Object>> getErrorStatistics(Long groupId, String startDate, String endDate);

    /**
     * 获取性能统计
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 性能统计
     */
    Map<String, Object> getPerformanceStatistics(Long groupId, String startDate, String endDate);

    // ==================== 日志分析 ====================

    /**
     * 获取发送趋势
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param groupBy 分组方式
     * @return 趋势数据
     */
    List<Map<String, Object>> getSendTrends(Long groupId, String startDate, String endDate, String groupBy);

    /**
     * 获取成功率分析
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 成功率分析
     */
    Map<String, Object> getSuccessRateAnalysis(Long groupId, String startDate, String endDate);

    /**
     * 获取响应时间分析
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 响应时间分析
     */
    Map<String, Object> getResponseTimeAnalysis(Long groupId, String startDate, String endDate);

    /**
     * 获取失败原因分析
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 失败原因分析
     */
    List<Map<String, Object>> getFailureReasonAnalysis(Long groupId, String startDate, String endDate);

    // ==================== 日志清理 ====================

    /**
     * 清理过期日志
     *
     * @param expiredBefore 过期时间点
     * @return 清理数量
     */
    Integer cleanExpiredLogs(LocalDateTime expiredBefore);

    /**
     * 归档日志
     *
     * @param archiveBefore 归档时间点
     * @return 归档数量
     */
    Integer archiveLogs(LocalDateTime archiveBefore);

    /**
     * 压缩日志
     *
     * @param compressBefore 压缩时间点
     * @return 压缩数量
     */
    Integer compressLogs(LocalDateTime compressBefore);

    /**
     * 获取日志存储统计
     *
     * @return 存储统计
     */
    Map<String, Object> getLogStorageStatistics();

    // ==================== 日志导出 ====================

    /**
     * 导出日志
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param channels 渠道列表
     * @param format 导出格式
     * @return 导出结果
     */
    Map<String, Object> exportLogs(Long groupId, String startDate, String endDate, 
                                  List<String> channels, String format);

    /**
     * 生成日志报告
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param reportType 报告类型
     * @return 报告数据
     */
    Map<String, Object> generateLogReport(Long groupId, String startDate, String endDate, String reportType);

    // ==================== 实时监控 ====================

    /**
     * 获取实时发送状态
     *
     * @return 实时状态
     */
    Map<String, Object> getRealTimeSendStatus();

    /**
     * 获取渠道健康状态
     *
     * @return 渠道健康状态
     */
    List<Map<String, Object>> getChannelHealthStatus();

    /**
     * 获取异常告警
     *
     * @param hours 小时数
     * @return 异常告警列表
     */
    List<Map<String, Object>> getAbnormalAlerts(Integer hours);

    /**
     * 获取队列状态
     *
     * @return 队列状态
     */
    Map<String, Object> getQueueStatus();

}
