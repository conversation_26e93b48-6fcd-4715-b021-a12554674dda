package top.continew.admin.bot.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.accounting.enums.PlatformType;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 机器人消息
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "机器人消息")
public class BotMessage implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 消息ID
     */
    @Schema(description = "消息ID")
    private String messageId;

    /**
     * 平台类型
     */
    @Schema(description = "平台类型")
    private PlatformType platform;

    /**
     * 聊天ID
     */
    @Schema(description = "聊天ID")
    private String chatId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String username;

    /**
     * 消息类型
     */
    @Schema(description = "消息类型")
    private MessageType messageType;

    /**
     * 消息内容
     */
    @Schema(description = "消息内容")
    private String content;

    /**
     * 消息时间
     */
    @Schema(description = "消息时间")
    private LocalDateTime timestamp;

    /**
     * 是否为群组消息
     */
    @Schema(description = "是否为群组消息")
    private Boolean isGroupMessage;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID")
    private Long groupId;

    /**
     * 回复消息ID
     */
    @Schema(description = "回复消息ID")
    private String replyToMessageId;

    /**
     * 附件列表
     */
    @Schema(description = "附件列表")
    private java.util.List<String> attachments;

    /**
     * 扩展数据
     */
    @Schema(description = "扩展数据")
    private Map<String, Object> metadata;

    /**
     * 处理状态
     */
    @Schema(description = "处理状态")
    private ProcessStatus processStatus = ProcessStatus.PENDING;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 重试次数
     */
    @Schema(description = "重试次数")
    private Integer retryCount = 0;

    /**
     * 最大重试次数
     */
    @Schema(description = "最大重试次数")
    private Integer maxRetries = 3;

    /**
     * 优先级
     */
    @Schema(description = "优先级")
    private Priority priority = Priority.NORMAL;

    /**
     * 消息类型枚举
     */
    public enum MessageType {
        /**
         * 文本消息
         */
        TEXT,
        /**
         * 图片消息
         */
        IMAGE,
        /**
         * 语音消息
         */
        VOICE,
        /**
         * 视频消息
         */
        VIDEO,
        /**
         * 文件消息
         */
        FILE,
        /**
         * 位置消息
         */
        LOCATION,
        /**
         * 联系人消息
         */
        CONTACT,
        /**
         * 贴纸消息
         */
        STICKER,
        /**
         * 动画消息
         */
        ANIMATION,
        /**
         * 回调查询
         */
        CALLBACK_QUERY,
        /**
         * 内联查询
         */
        INLINE_QUERY,
        /**
         * 系统消息
         */
        SYSTEM,
        /**
         * 通知消息
         */
        NOTIFICATION,
        /**
         * 命令消息
         */
        COMMAND
    }

    /**
     * 处理状态枚举
     */
    public enum ProcessStatus {
        /**
         * 待处理
         */
        PENDING,
        /**
         * 处理中
         */
        PROCESSING,
        /**
         * 处理成功
         */
        SUCCESS,
        /**
         * 处理失败
         */
        FAILED,
        /**
         * 已忽略
         */
        IGNORED
    }

    /**
     * 优先级枚举
     */
    public enum Priority {
        /**
         * 低优先级
         */
        LOW(1),
        /**
         * 普通优先级
         */
        NORMAL(2),
        /**
         * 高优先级
         */
        HIGH(3),
        /**
         * 紧急优先级
         */
        URGENT(4);

        private final int level;

        Priority(int level) {
            this.level = level;
        }

        public int getLevel() {
            return level;
        }
    }

    /**
     * 创建文本消息
     */
    public static BotMessage createTextMessage(PlatformType platform, String chatId, Long userId, 
                                              String username, String content) {
        BotMessage message = new BotMessage();
        message.setPlatform(platform);
        message.setChatId(chatId);
        message.setUserId(userId);
        message.setUsername(username);
        message.setMessageType(MessageType.TEXT);
        message.setContent(content);
        message.setTimestamp(LocalDateTime.now());
        return message;
    }

    /**
     * 创建命令消息
     */
    public static BotMessage createCommandMessage(PlatformType platform, String chatId, Long userId, 
                                                 String username, String command) {
        BotMessage message = createTextMessage(platform, chatId, userId, username, command);
        message.setMessageType(MessageType.COMMAND);
        message.setPriority(Priority.HIGH);
        return message;
    }

    /**
     * 创建通知消息
     */
    public static BotMessage createNotificationMessage(PlatformType platform, String chatId, String content) {
        BotMessage message = new BotMessage();
        message.setPlatform(platform);
        message.setChatId(chatId);
        message.setMessageType(MessageType.NOTIFICATION);
        message.setContent(content);
        message.setTimestamp(LocalDateTime.now());
        message.setPriority(Priority.HIGH);
        return message;
    }

    /**
     * 创建系统消息
     */
    public static BotMessage createSystemMessage(PlatformType platform, String chatId, String content) {
        BotMessage message = new BotMessage();
        message.setPlatform(platform);
        message.setChatId(chatId);
        message.setMessageType(MessageType.SYSTEM);
        message.setContent(content);
        message.setTimestamp(LocalDateTime.now());
        message.setPriority(Priority.URGENT);
        return message;
    }

    /**
     * 是否可以重试
     */
    public boolean canRetry() {
        return retryCount < maxRetries && processStatus == ProcessStatus.FAILED;
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.retryCount++;
    }

    /**
     * 标记为处理成功
     */
    public void markAsSuccess() {
        this.processStatus = ProcessStatus.SUCCESS;
        this.errorMessage = null;
    }

    /**
     * 标记为处理失败
     */
    public void markAsFailed(String errorMessage) {
        this.processStatus = ProcessStatus.FAILED;
        this.errorMessage = errorMessage;
    }

    /**
     * 标记为处理中
     */
    public void markAsProcessing() {
        this.processStatus = ProcessStatus.PROCESSING;
    }

    /**
     * 标记为已忽略
     */
    public void markAsIgnored() {
        this.processStatus = ProcessStatus.IGNORED;
    }
}
