package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * Zapier日志响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "Zapier日志响应")
public class ZapierLogResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Schema(description = "ID", example = "1")
    private Long id;

    /**
     * 配置ID
     */
    @Schema(description = "配置ID", example = "1")
    private Long configId;

    /**
     * 配置名称
     */
    @Schema(description = "配置名称", example = "交易同步到Google Sheets")
    private String configName;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 群组名称
     */
    @Schema(description = "群组名称", example = "我的记账群")
    private String groupName;

    /**
     * 触发器类型
     */
    @Schema(description = "触发器类型", example = "TRANSACTION_CREATED")
    private String triggerType;

    /**
     * 触发器类型名称
     */
    @Schema(description = "触发器类型名称", example = "交易创建")
    private String triggerTypeName;

    /**
     * 事件类型
     */
    @Schema(description = "事件类型", example = "CREATE")
    private String eventType;

    /**
     * 事件类型名称
     */
    @Schema(description = "事件类型名称", example = "创建")
    private String eventTypeName;

    /**
     * 业务ID
     */
    @Schema(description = "业务ID", example = "123")
    private Long businessId;

    /**
     * 业务类型
     */
    @Schema(description = "业务类型", example = "TRANSACTION")
    private String businessType;

    /**
     * 业务类型名称
     */
    @Schema(description = "业务类型名称", example = "交易")
    private String businessTypeName;

    /**
     * 请求数据
     */
    @Schema(description = "请求数据")
    private Map<String, Object> requestData;

    /**
     * 响应数据
     */
    @Schema(description = "响应数据")
    private Map<String, Object> responseData;

    /**
     * 请求头
     */
    @Schema(description = "请求头")
    private Map<String, String> requestHeaders;

    /**
     * 响应头
     */
    @Schema(description = "响应头")
    private Map<String, String> responseHeaders;

    /**
     * HTTP状态码
     */
    @Schema(description = "HTTP状态码", example = "200")
    private Integer httpStatus;

    /**
     * HTTP状态描述
     */
    @Schema(description = "HTTP状态描述", example = "OK")
    private String httpStatusText;

    /**
     * 执行状态
     */
    @Schema(description = "执行状态", example = "SUCCESS")
    private String status;

    /**
     * 执行状态名称
     */
    @Schema(description = "执行状态名称", example = "成功")
    private String statusName;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息", example = "连接超时")
    private String errorMessage;

    /**
     * 错误代码
     */
    @Schema(description = "错误代码", example = "TIMEOUT")
    private String errorCode;

    /**
     * 执行时间
     */
    @Schema(description = "执行时间", example = "2025-01-01T12:00:00")
    private LocalDateTime executedAt;

    /**
     * 执行耗时（毫秒）
     */
    @Schema(description = "执行耗时（毫秒）", example = "1500")
    private Long executionTime;

    /**
     * 执行耗时描述
     */
    @Schema(description = "执行耗时描述", example = "1.5秒")
    private String executionTimeText;

    /**
     * 重试次数
     */
    @Schema(description = "重试次数", example = "2")
    private Integer retryCount;

    /**
     * 是否为重试
     */
    @Schema(description = "是否为重试", example = "false")
    private Boolean isRetry;

    /**
     * 原始日志ID
     */
    @Schema(description = "原始日志ID", example = "100")
    private Long originalLogId;

    /**
     * 用户代理
     */
    @Schema(description = "用户代理", example = "ContiNew-Zapier/1.0")
    private String userAgent;

    /**
     * IP地址
     */
    @Schema(description = "IP地址", example = "*************")
    private String ipAddress;

    /**
     * 请求大小（字节）
     */
    @Schema(description = "请求大小（字节）", example = "1024")
    private Long requestSize;

    /**
     * 请求大小描述
     */
    @Schema(description = "请求大小描述", example = "1.0 KB")
    private String requestSizeText;

    /**
     * 响应大小（字节）
     */
    @Schema(description = "响应大小（字节）", example = "512")
    private Long responseSize;

    /**
     * 响应大小描述
     */
    @Schema(description = "响应大小描述", example = "512 B")
    private String responseSizeText;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "自动重试成功")
    private String remark;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-01-01T12:00:00")
    private LocalDateTime createTime;
}
