package top.continew.admin.accounting.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import top.continew.admin.accounting.model.entity.DataSyncConfigDO;
import top.continew.admin.accounting.model.query.DataSyncConfigQuery;
import top.continew.admin.accounting.model.query.DataSyncLogQuery;
import top.continew.admin.accounting.model.req.DataSyncConfigCreateReq;
import top.continew.admin.accounting.model.req.DataSyncConfigUpdateReq;
import top.continew.admin.accounting.model.req.DataSyncExecuteReq;
import top.continew.admin.accounting.model.resp.DataSyncConfigDetailResp;
import top.continew.admin.accounting.model.resp.DataSyncConfigListResp;
import top.continew.admin.accounting.model.resp.DataSyncLogResp;
import top.continew.admin.accounting.model.resp.DataSyncResultResp;
import top.continew.starter.extension.crud.service.BaseService;

import java.util.List;
import java.util.Map;

/**
 * 数据同步服务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface DataSyncService extends BaseService<DataSyncConfigDO, DataSyncConfigListResp, DataSyncConfigDetailResp, DataSyncConfigQuery, DataSyncConfigCreateReq> {

    // ==================== 配置管理 ====================

    /**
     * 更新同步配置
     *
     * @param req 更新请求
     * @param id  配置ID
     */
    void updateConfig(DataSyncConfigUpdateReq req, Long id);

    /**
     * 启用配置
     *
     * @param id 配置ID
     */
    void enableConfig(Long id);

    /**
     * 禁用配置
     *
     * @param id 配置ID
     */
    void disableConfig(Long id);

    /**
     * 复制配置
     *
     * @param id      配置ID
     * @param newName 新配置名称
     * @return 新配置ID
     */
    Long copyConfig(Long id, String newName);

    /**
     * 测试配置连接
     *
     * @param id 配置ID
     * @return 测试结果
     */
    Map<String, Object> testConnection(Long id);

    /**
     * 验证配置
     *
     * @param id 配置ID
     * @return 验证结果
     */
    Map<String, Object> validateConfig(Long id);

    // ==================== 同步执行 ====================

    /**
     * 执行同步
     *
     * @param req 同步请求
     * @return 同步结果
     */
    DataSyncResultResp executeSync(DataSyncExecuteReq req);

    /**
     * 异步执行同步
     *
     * @param req 同步请求
     * @return 同步ID
     */
    String executeSyncAsync(DataSyncExecuteReq req);

    /**
     * 停止同步
     *
     * @param syncId 同步ID
     * @return 是否成功
     */
    Boolean stopSync(String syncId);

    /**
     * 重试同步
     *
     * @param syncId 同步ID
     * @return 同步结果
     */
    DataSyncResultResp retrySync(String syncId);

    /**
     * 获取同步状态
     *
     * @param syncId 同步ID
     * @return 同步状态
     */
    Map<String, Object> getSyncStatus(String syncId);

    /**
     * 获取同步进度
     *
     * @param syncId 同步ID
     * @return 同步进度
     */
    Map<String, Object> getSyncProgress(String syncId);

    // ==================== 增量同步 ====================

    /**
     * 执行增量同步
     *
     * @param configId 配置ID
     * @return 同步结果
     */
    DataSyncResultResp executeIncrementalSync(Long configId);

    /**
     * 获取增量数据
     *
     * @param configId      配置ID
     * @param lastSyncTime  最后同步时间
     * @param syncDirection 同步方向
     * @return 增量数据
     */
    Map<String, Object> getIncrementalData(Long configId, String lastSyncTime, String syncDirection);

    /**
     * 检测数据变更
     *
     * @param configId 配置ID
     * @return 变更检测结果
     */
    Map<String, Object> detectDataChanges(Long configId);

    // ==================== 冲突解决 ====================

    /**
     * 检测数据冲突
     *
     * @param configId   配置ID
     * @param sourceData 源数据
     * @param targetData 目标数据
     * @return 冲突检测结果
     */
    Map<String, Object> detectConflicts(Long configId, Map<String, Object> sourceData, Map<String, Object> targetData);

    /**
     * 解决数据冲突
     *
     * @param configId       配置ID
     * @param conflictData   冲突数据
     * @param resolutionType 解决方式
     * @return 解决结果
     */
    Map<String, Object> resolveConflicts(Long configId, Map<String, Object> conflictData, String resolutionType);

    /**
     * 获取冲突解决建议
     *
     * @param configId     配置ID
     * @param conflictData 冲突数据
     * @return 解决建议
     */
    List<Map<String, Object>> getConflictResolutionSuggestions(Long configId, Map<String, Object> conflictData);

    // ==================== 数据映射 ====================

    /**
     * 应用字段映射
     *
     * @param configId 配置ID
     * @param data     原始数据
     * @return 映射后数据
     */
    Map<String, Object> applyFieldMapping(Long configId, Map<String, Object> data);

    /**
     * 应用数据转换
     *
     * @param configId 配置ID
     * @param data     原始数据
     * @return 转换后数据
     */
    Map<String, Object> applyDataTransformation(Long configId, Map<String, Object> data);

    /**
     * 验证字段映射
     *
     * @param configId 配置ID
     * @return 验证结果
     */
    Map<String, Object> validateFieldMapping(Long configId);

    /**
     * 获取字段映射建议
     *
     * @param configId 配置ID
     * @return 映射建议
     */
    List<Map<String, Object>> getFieldMappingSuggestions(Long configId);

    // ==================== 同步日志 ====================

    /**
     * 分页查询同步日志
     *
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<DataSyncLogResp> pageSyncLogs(DataSyncLogQuery query);

    /**
     * 获取同步日志详情
     *
     * @param logId 日志ID
     * @return 日志详情
     */
    DataSyncLogResp getSyncLogDetail(Long logId);

    /**
     * 删除同步日志
     *
     * @param logId 日志ID
     */
    void deleteSyncLog(Long logId);

    /**
     * 批量删除同步日志
     *
     * @param logIds 日志ID列表
     */
    void deleteSyncLogs(List<Long> logIds);

    /**
     * 清理过期日志
     *
     * @param retentionDays 保留天数
     * @return 清理数量
     */
    Integer cleanupExpiredLogs(Integer retentionDays);

    // ==================== 定时同步 ====================

    /**
     * 启动定时同步
     *
     * @param configId 配置ID
     */
    void startScheduledSync(Long configId);

    /**
     * 停止定时同步
     *
     * @param configId 配置ID
     */
    void stopScheduledSync(Long configId);

    /**
     * 更新同步计划
     *
     * @param configId  配置ID
     * @param frequency 同步频率
     */
    void updateSyncSchedule(Long configId, String frequency);

    /**
     * 获取下次同步时间
     *
     * @param configId 配置ID
     * @return 下次同步时间
     */
    String getNextSyncTime(Long configId);

    // ==================== 监控统计 ====================

    /**
     * 获取同步统计
     *
     * @param configId 配置ID
     * @return 统计信息
     */
    Map<String, Object> getSyncStatistics(Long configId);

    /**
     * 获取同步性能指标
     *
     * @param configId 配置ID
     * @return 性能指标
     */
    Map<String, Object> getSyncPerformanceMetrics(Long configId);

    /**
     * 获取同步健康状态
     *
     * @param configId 配置ID
     * @return 健康状态
     */
    Map<String, Object> getSyncHealthStatus(Long configId);

    /**
     * 生成同步报告
     *
     * @param configId  配置ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 报告文件路径
     */
    String generateSyncReport(Long configId, String startDate, String endDate);

    // ==================== 数据源管理 ====================

    /**
     * 获取支持的数据源类型
     *
     * @return 数据源类型列表
     */
    List<Map<String, Object>> getSupportedDataSources();

    /**
     * 获取数据源配置模板
     *
     * @param sourceType 数据源类型
     * @return 配置模板
     */
    Map<String, Object> getDataSourceTemplate(String sourceType);

    /**
     * 测试数据源连接
     *
     * @param sourceType   数据源类型
     * @param sourceConfig 数据源配置
     * @return 测试结果
     */
    Map<String, Object> testDataSourceConnection(String sourceType, Map<String, Object> sourceConfig);
}
