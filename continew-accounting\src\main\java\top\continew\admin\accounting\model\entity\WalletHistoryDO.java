package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.data.mybatis.plus.base.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 钱包历史记录实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("acc_wallet_history")
@Schema(description = "钱包历史记录")
public class WalletHistoryDO extends BaseEntity {

    /**
     * 群组ID
     */
    @Schema(description = "群组ID")
    private Long groupId;

    /**
     * 钱包ID
     */
    @Schema(description = "钱包ID")
    private Long walletId;

    /**
     * 币种
     */
    @Schema(description = "币种")
    private String currency;

    /**
     * 操作类型
     */
    @Schema(description = "操作类型")
    private String operationType;

    /**
     * 金额
     */
    @Schema(description = "金额")
    private BigDecimal amount;

    /**
     * 操作前余额
     */
    @Schema(description = "操作前余额")
    private BigDecimal balanceBefore;

    /**
     * 操作后余额
     */
    @Schema(description = "操作后余额")
    private BigDecimal balanceAfter;

    /**
     * 操作前冻结金额
     */
    @Schema(description = "操作前冻结金额")
    private BigDecimal frozenAmountBefore;

    /**
     * 操作后冻结金额
     */
    @Schema(description = "操作后冻结金额")
    private BigDecimal frozenAmountAfter;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 操作人ID
     */
    @Schema(description = "操作人ID")
    private Long operatorId;

    /**
     * 操作时间
     */
    @Schema(description = "操作时间")
    private LocalDateTime operateTime;

    /**
     * 关联业务ID
     */
    @Schema(description = "关联业务ID")
    private Long businessId;

    /**
     * 业务类型
     */
    @Schema(description = "业务类型")
    private String businessType;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * IP地址
     */
    @Schema(description = "IP地址")
    private String ipAddress;

    /**
     * 用户代理
     */
    @Schema(description = "用户代理")
    private String userAgent;
}
