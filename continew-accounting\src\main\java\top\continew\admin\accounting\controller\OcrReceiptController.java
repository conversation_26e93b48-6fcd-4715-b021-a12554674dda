package top.continew.admin.accounting.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.accounting.model.query.OcrTaskQuery;
import top.continew.admin.accounting.model.req.OcrReceiptReq;
import top.continew.admin.accounting.model.resp.OcrReceiptResp;
import top.continew.admin.accounting.service.OcrReceiptService;
import top.continew.admin.common.model.resp.LabelValueResp;
import top.continew.admin.common.model.resp.R;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * OCR收据识别控制器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "OCR收据识别API")
@RestController
@RequiredArgsConstructor
@Validated
@RequestMapping("/accounting/ocr")
public class OcrReceiptController {

    private final OcrReceiptService ocrReceiptService;

    // ==================== OCR识别 ====================

    @Operation(summary = "识别收据", description = "同步识别收据并返回结果")
    @PostMapping("/recognize")
    public R<OcrReceiptResp> recognizeReceipt(@Valid @RequestBody OcrReceiptReq request) {
        return R.ok(ocrReceiptService.recognizeReceipt(request));
    }

    @Operation(summary = "异步识别收据", description = "异步识别收据并返回任务ID")
    @PostMapping("/recognize/async")
    public R<String> recognizeReceiptAsync(@Valid @RequestBody OcrReceiptReq request) {
        return R.ok(ocrReceiptService.recognizeReceiptAsync(request));
    }

    @Operation(summary = "批量识别收据", description = "批量同步识别收据")
    @PostMapping("/recognize/batch")
    public R<List<OcrReceiptResp>> batchRecognizeReceipts(@Valid @RequestBody List<OcrReceiptReq> requests) {
        return R.ok(ocrReceiptService.batchRecognizeReceipts(requests));
    }

    @Operation(summary = "批量异步识别收据", description = "批量异步识别收据")
    @PostMapping("/recognize/batch/async")
    public R<List<String>> batchRecognizeReceiptsAsync(@Valid @RequestBody List<OcrReceiptReq> requests) {
        return R.ok(ocrReceiptService.batchRecognizeReceiptsAsync(requests));
    }

    // ==================== 任务管理 ====================

    @Operation(summary = "获取任务状态", description = "根据任务ID获取任务状态")
    @GetMapping("/task/{taskId}/status")
    public R<OcrReceiptResp> getTaskStatus(@Parameter(description = "任务ID") @PathVariable @NotBlank String taskId) {
        return R.ok(ocrReceiptService.getTaskStatus(taskId));
    }

    @Operation(summary = "获取任务详情", description = "根据任务ID获取任务详情")
    @GetMapping("/task/{taskId}")
    public R<OcrReceiptResp> getTaskDetail(@Parameter(description = "任务ID") @PathVariable @NotBlank String taskId) {
        return R.ok(ocrReceiptService.getTaskDetail(taskId));
    }

    @Operation(summary = "分页查询任务", description = "分页查询OCR任务列表")
    @GetMapping("/tasks")
    public R<IPage<OcrReceiptResp>> pageTasks(@Valid OcrTaskQuery query) {
        return R.ok(ocrReceiptService.pageTasks(query));
    }

    @Operation(summary = "列表查询任务", description = "列表查询OCR任务")
    @GetMapping("/tasks/list")
    public R<List<OcrReceiptResp>> listTasks(@Valid OcrTaskQuery query) {
        return R.ok(ocrReceiptService.listTasks(query));
    }

    @Operation(summary = "取消任务", description = "取消正在处理的任务")
    @PostMapping("/task/{taskId}/cancel")
    public R<Void> cancelTask(@Parameter(description = "任务ID") @PathVariable @NotBlank String taskId) {
        ocrReceiptService.cancelTask(taskId);
        return R.ok();
    }

    @Operation(summary = "重新执行任务", description = "重新执行失败的任务")
    @PostMapping("/task/{taskId}/retry")
    public R<String> retryTask(@Parameter(description = "任务ID") @PathVariable @NotBlank String taskId) {
        return R.ok(ocrReceiptService.retryTask(taskId));
    }

    @Operation(summary = "删除任务", description = "删除指定任务")
    @DeleteMapping("/task/{taskId}")
    public R<Void> deleteTask(@Parameter(description = "任务ID") @PathVariable @NotBlank String taskId) {
        ocrReceiptService.deleteTask(taskId);
        return R.ok();
    }

    @Operation(summary = "批量删除任务", description = "批量删除指定任务")
    @DeleteMapping("/tasks")
    public R<Void> deleteTasks(@Parameter(description = "任务ID列表") @RequestBody @NotEmpty List<String> taskIds) {
        ocrReceiptService.deleteTasks(taskIds);
        return R.ok();
    }

    // ==================== 结果处理 ====================

    @Operation(summary = "手动创建账单", description = "从OCR识别结果手动创建账单")
    @PostMapping("/task/{taskId}/create-transaction")
    public R<Long> createTransactionFromTask(
            @Parameter(description = "任务ID") @PathVariable @NotBlank String taskId,
            @Parameter(description = "分类ID") @RequestParam(required = false) Long categoryId,
            @Parameter(description = "标签") @RequestParam(required = false) List<String> tags,
            @Parameter(description = "备注") @RequestParam(required = false) String remark) {
        return R.ok(ocrReceiptService.createTransactionFromTask(taskId, categoryId, tags, remark));
    }

    @Operation(summary = "更新识别结果", description = "手动更新OCR识别结果")
    @PutMapping("/task/{taskId}/result")
    public R<Void> updateRecognitionResult(
            @Parameter(description = "任务ID") @PathVariable @NotBlank String taskId,
            @Parameter(description = "收据信息") @RequestBody @NotNull Map<String, Object> receiptInfo) {
        ocrReceiptService.updateRecognitionResult(taskId, receiptInfo);
        return R.ok();
    }

    @Operation(summary = "标记结果为正确", description = "标记OCR识别结果为正确")
    @PostMapping("/task/{taskId}/mark-correct")
    public R<Void> markResultAsCorrect(@Parameter(description = "任务ID") @PathVariable @NotBlank String taskId) {
        ocrReceiptService.markResultAsCorrect(taskId);
        return R.ok();
    }

    @Operation(summary = "标记结果为错误", description = "标记OCR识别结果为错误")
    @PostMapping("/task/{taskId}/mark-incorrect")
    public R<Void> markResultAsIncorrect(
            @Parameter(description = "任务ID") @PathVariable @NotBlank String taskId,
            @Parameter(description = "反馈信息") @RequestParam @NotBlank String feedback) {
        ocrReceiptService.markResultAsIncorrect(taskId, feedback);
        return R.ok();
    }

    @Operation(summary = "导出识别结果", description = "导出OCR识别结果")
    @PostMapping("/export")
    public R<String> exportRecognitionResults(
            @Parameter(description = "查询条件") @RequestBody @Valid OcrTaskQuery query,
            @Parameter(description = "导出格式") @RequestParam(defaultValue = "EXCEL") String format) {
        return R.ok(ocrReceiptService.exportRecognitionResults(query, format));
    }

    // ==================== 统计分析 ====================

    @Operation(summary = "获取识别统计", description = "获取OCR识别统计信息")
    @GetMapping("/stats/recognition")
    public R<Map<String, Object>> getRecognitionStats(
            @Parameter(description = "群组ID") @RequestParam(required = false) Long groupId,
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days) {
        return R.ok(ocrReceiptService.getRecognitionStats(groupId, days));
    }

    @Operation(summary = "获取引擎性能统计", description = "获取OCR引擎性能统计")
    @GetMapping("/stats/engine-performance")
    public R<Map<String, Object>> getEnginePerformanceStats(
            @Parameter(description = "群组ID") @RequestParam(required = false) Long groupId,
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days) {
        return R.ok(ocrReceiptService.getEnginePerformanceStats(groupId, days));
    }

    @Operation(summary = "获取识别准确率统计", description = "获取OCR识别准确率统计")
    @GetMapping("/stats/accuracy")
    public R<Map<String, Object>> getAccuracyStats(
            @Parameter(description = "群组ID") @RequestParam(required = false) Long groupId,
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days) {
        return R.ok(ocrReceiptService.getAccuracyStats(groupId, days));
    }

    @Operation(summary = "获取商家识别排行", description = "获取商家识别排行榜")
    @GetMapping("/stats/merchant-ranking")
    public R<List<Map<String, Object>>> getMerchantRecognitionRanking(
            @Parameter(description = "群组ID") @RequestParam(required = false) Long groupId,
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days,
            @Parameter(description = "返回数量") @RequestParam(defaultValue = "10") Integer limit) {
        return R.ok(ocrReceiptService.getMerchantRecognitionRanking(groupId, days, limit));
    }

    @Operation(summary = "获取分类识别统计", description = "获取分类识别统计信息")
    @GetMapping("/stats/category")
    public R<List<Map<String, Object>>> getCategoryRecognitionStats(
            @Parameter(description = "群组ID") @RequestParam(required = false) Long groupId,
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days) {
        return R.ok(ocrReceiptService.getCategoryRecognitionStats(groupId, days));
    }

    @Operation(summary = "获取识别趋势", description = "获取OCR识别趋势数据")
    @GetMapping("/stats/trend")
    public R<List<Map<String, Object>>> getRecognitionTrend(
            @Parameter(description = "群组ID") @RequestParam(required = false) Long groupId,
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days) {
        return R.ok(ocrReceiptService.getRecognitionTrend(groupId, days));
    }

    @Operation(summary = "获取错误分析", description = "获取OCR错误分析数据")
    @GetMapping("/stats/error-analysis")
    public R<Map<String, Object>> getErrorAnalysis(
            @Parameter(description = "群组ID") @RequestParam(required = false) Long groupId,
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days) {
        return R.ok(ocrReceiptService.getErrorAnalysis(groupId, days));
    }

    // ==================== 配置管理 ====================

    @Operation(summary = "获取OCR引擎配置", description = "获取指定OCR引擎的配置信息")
    @GetMapping("/config/engine/{engine}")
    public R<Map<String, Object>> getEngineConfig(@Parameter(description = "引擎类型") @PathVariable @NotBlank String engine) {
        return R.ok(ocrReceiptService.getEngineConfig(engine));
    }

    @Operation(summary = "更新OCR引擎配置", description = "更新指定OCR引擎的配置")
    @PutMapping("/config/engine/{engine}")
    public R<Void> updateEngineConfig(
            @Parameter(description = "引擎类型") @PathVariable @NotBlank String engine,
            @Parameter(description = "配置信息") @RequestBody @NotNull Map<String, Object> config) {
        ocrReceiptService.updateEngineConfig(engine, config);
        return R.ok();
    }

    @Operation(summary = "测试OCR引擎连接", description = "测试指定OCR引擎的连接状态")
    @PostMapping("/config/engine/{engine}/test")
    public R<Map<String, Object>> testEngineConnection(@Parameter(description = "引擎类型") @PathVariable @NotBlank String engine) {
        return R.ok(ocrReceiptService.testEngineConnection(engine));
    }

    @Operation(summary = "获取支持的引擎列表", description = "获取系统支持的OCR引擎列表")
    @GetMapping("/config/engines")
    public R<List<LabelValueResp<String>>> getSupportedEngines() {
        return R.ok(ocrReceiptService.getSupportedEngines());
    }

    @Operation(summary = "获取支持的语言列表", description = "获取指定引擎支持的语言列表")
    @GetMapping("/config/engine/{engine}/languages")
    public R<List<LabelValueResp<String>>> getSupportedLanguages(@Parameter(description = "引擎类型") @PathVariable @NotBlank String engine) {
        return R.ok(ocrReceiptService.getSupportedLanguages(engine));
    }

    @Operation(summary = "获取支持的识别模式", description = "获取指定引擎支持的识别模式")
    @GetMapping("/config/engine/{engine}/modes")
    public R<List<LabelValueResp<String>>> getSupportedRecognitionModes(@Parameter(description = "引擎类型") @PathVariable @NotBlank String engine) {
        return R.ok(ocrReceiptService.getSupportedRecognitionModes(engine));
    }

    // ==================== 模板管理 ====================

    @Operation(summary = "创建识别模板", description = "创建OCR识别模板")
    @PostMapping("/template")
    public R<Long> createRecognitionTemplate(
            @Parameter(description = "模板名称") @RequestParam @NotBlank String templateName,
            @Parameter(description = "模板配置") @RequestBody @NotNull Map<String, Object> config) {
        return R.ok(ocrReceiptService.createRecognitionTemplate(templateName, config));
    }

    @Operation(summary = "更新识别模板", description = "更新OCR识别模板")
    @PutMapping("/template/{templateId}")
    public R<Void> updateRecognitionTemplate(
            @Parameter(description = "模板ID") @PathVariable @NotNull Long templateId,
            @Parameter(description = "模板配置") @RequestBody @NotNull Map<String, Object> config) {
        ocrReceiptService.updateRecognitionTemplate(templateId, config);
        return R.ok();
    }

    @Operation(summary = "删除识别模板", description = "删除OCR识别模板")
    @DeleteMapping("/template/{templateId}")
    public R<Void> deleteRecognitionTemplate(@Parameter(description = "模板ID") @PathVariable @NotNull Long templateId) {
        ocrReceiptService.deleteRecognitionTemplate(templateId);
        return R.ok();
    }

    @Operation(summary = "获取识别模板列表", description = "获取群组的OCR识别模板列表")
    @GetMapping("/templates")
    public R<List<Map<String, Object>>> getRecognitionTemplates(
            @Parameter(description = "群组ID") @RequestParam(required = false) Long groupId) {
        return R.ok(ocrReceiptService.getRecognitionTemplates(groupId));
    }

    @Operation(summary = "应用识别模板", description = "将识别模板应用到识别请求")
    @PostMapping("/template/{templateId}/apply")
    public R<OcrReceiptReq> applyRecognitionTemplate(
            @Parameter(description = "模板ID") @PathVariable @NotNull Long templateId,
            @Parameter(description = "识别请求") @RequestBody @Valid OcrReceiptReq request) {
        return R.ok(ocrReceiptService.applyRecognitionTemplate(templateId, request));
    }

    // ==================== 辅助方法 ====================

    @Operation(summary = "验证图片格式", description = "验证图片文件格式是否支持")
    @GetMapping("/validate/image-format")
    public R<Boolean> validateImageFormat(@Parameter(description = "文件名") @RequestParam @NotBlank String fileName) {
        return R.ok(ocrReceiptService.validateImageFormat(fileName));
    }

    @Operation(summary = "验证图片大小", description = "验证图片文件大小是否符合要求")
    @GetMapping("/validate/image-size")
    public R<Boolean> validateImageSize(@Parameter(description = "文件大小") @RequestParam @NotNull Long fileSize) {
        return R.ok(ocrReceiptService.validateImageSize(fileSize));
    }

    @Operation(summary = "获取任务状态选项", description = "获取任务状态下拉选项")
    @GetMapping("/options/task-status")
    public R<List<LabelValueResp<String>>> getTaskStatusOptions() {
        return R.ok(ocrReceiptService.getTaskStatusOptions());
    }

    @Operation(summary = "获取识别模式选项", description = "获取识别模式下拉选项")
    @GetMapping("/options/recognition-mode")
    public R<List<LabelValueResp<String>>> getRecognitionModeOptions() {
        return R.ok(ocrReceiptService.getRecognitionModeOptions());
    }

    @Operation(summary = "获取识别精度选项", description = "获取识别精度下拉选项")
    @GetMapping("/options/accuracy")
    public R<List<LabelValueResp<String>>> getAccuracyOptions() {
        return R.ok(ocrReceiptService.getAccuracyOptions());
    }

    @Operation(summary = "检查任务是否存在", description = "检查指定任务ID是否存在")
    @GetMapping("/task/{taskId}/exists")
    public R<Boolean> existsTask(@Parameter(description = "任务ID") @PathVariable @NotBlank String taskId) {
        return R.ok(ocrReceiptService.existsTask(taskId));
    }

    @Operation(summary = "生成任务编号", description = "生成新的任务编号")
    @GetMapping("/generate-task-number")
    public R<String> generateTaskNumber() {
        return R.ok(ocrReceiptService.generateTaskNumber());
    }
}
