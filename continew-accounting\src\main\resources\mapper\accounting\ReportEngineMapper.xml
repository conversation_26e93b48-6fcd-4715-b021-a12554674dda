<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.accounting.mapper.ReportEngineMapper">

    <!-- 报表模板结果映射 -->
    <resultMap id="ReportTemplateResultMap" type="top.continew.admin.accounting.model.entity.ReportTemplate">
        <id column="template_id" property="templateId"/>
        <result column="group_id" property="groupId"/>
        <result column="template_name" property="templateName"/>
        <result column="template_description" property="templateDescription"/>
        <result column="template_type" property="templateType"/>
        <result column="template_status" property="templateStatus"/>
        <result column="version" property="version"/>
        <result column="enabled" property="enabled"/>
        <result column="is_public" property="isPublic"/>
        <result column="is_system_template" property="isSystemTemplate"/>
        <result column="report_config_json" property="reportConfigJson"/>
        <result column="data_source_config_json" property="dataSourceConfigJson"/>
        <result column="layout_config_json" property="layoutConfigJson"/>
        <result column="chart_configs_json" property="chartConfigsJson"/>
        <result column="filter_configs_json" property="filterConfigsJson"/>
        <result column="export_config_json" property="exportConfigJson"/>
        <result column="schedule_config_json" property="scheduleConfigJson"/>
        <result column="permission_config_json" property="permissionConfigJson"/>
        <result column="tags_json" property="tagsJson"/>
        <result column="attributes_json" property="attributesJson"/>
        <result column="usage_count" property="usageCount"/>
        <result column="last_used_time" property="lastUsedTime"/>
        <result column="last_used_by" property="lastUsedBy"/>
        <result column="avg_execution_time" property="avgExecutionTime"/>
        <result column="max_execution_time" property="maxExecutionTime"/>
        <result column="min_execution_time" property="minExecutionTime"/>
        <result column="success_count" property="successCount"/>
        <result column="failure_count" property="failureCount"/>
        <result column="rating" property="rating"/>
        <result column="rating_count" property="ratingCount"/>
        <result column="favorite_count" property="favoriteCount"/>
        <result column="complexity" property="complexity"/>
        <result column="avg_data_volume" property="avgDataVolume"/>
        <result column="max_data_volume" property="maxDataVolume"/>
        <result column="min_data_volume" property="minDataVolume"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
    </resultMap>

    <!-- ==================== 报表模板基础操作 ==================== -->

    <!-- 检查模板名称是否存在 -->
    <select id="existsByNameAndGroup" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM acc_report_template
        WHERE template_name = #{templateName}
          AND group_id = #{groupId}
          AND deleted = 0
    </select>

    <!-- 检查模板名称是否存在（排除指定ID） -->
    <select id="existsByNameAndGroupExcludeId" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM acc_report_template
        WHERE template_name = #{templateName}
          AND group_id = #{groupId}
          AND template_id != #{excludeId}
          AND deleted = 0
    </select>

    <!-- 更新模板使用统计 -->
    <update id="updateTemplateUsageStats">
        UPDATE acc_report_template
        SET usage_count = usage_count + 1,
            last_used_time = NOW(),
            last_used_by = #{userId},
            <if test="success">
                success_count = success_count + 1,
            </if>
            <if test="!success">
                failure_count = failure_count + 1,
            </if>
            <if test="executionTime != null">
                avg_execution_time = CASE
                    WHEN avg_execution_time IS NULL THEN #{executionTime}
                    ELSE (avg_execution_time * (usage_count - 1) + #{executionTime}) / usage_count
                END,
                max_execution_time = CASE
                    WHEN max_execution_time IS NULL OR #{executionTime} > max_execution_time THEN #{executionTime}
                    ELSE max_execution_time
                END,
                min_execution_time = CASE
                    WHEN min_execution_time IS NULL OR #{executionTime} < min_execution_time THEN #{executionTime}
                    ELSE min_execution_time
                END,
            </if>
            <if test="dataVolume != null">
                avg_data_volume = CASE
                    WHEN avg_data_volume IS NULL THEN #{dataVolume}
                    ELSE (avg_data_volume * (usage_count - 1) + #{dataVolume}) / usage_count
                END,
                max_data_volume = CASE
                    WHEN max_data_volume IS NULL OR #{dataVolume} > max_data_volume THEN #{dataVolume}
                    ELSE max_data_volume
                END,
                min_data_volume = CASE
                    WHEN min_data_volume IS NULL OR #{dataVolume} < min_data_volume THEN #{dataVolume}
                    ELSE min_data_volume
                END,
            </if>
            update_time = NOW()
        WHERE template_id = #{templateId}
    </update>

    <!-- 更新模板评分 -->
    <update id="updateTemplateRating">
        UPDATE acc_report_template
        SET rating = #{rating},
            rating_count = rating_count + #{increment},
            update_time = NOW()
        WHERE template_id = #{templateId}
    </update>

    <!-- 更新模板收藏数 -->
    <update id="updateTemplateFavoriteCount">
        UPDATE acc_report_template
        SET favorite_count = favorite_count + #{increment},
            update_time = NOW()
        WHERE template_id = #{templateId}
    </update>

    <!-- ==================== 模板版本管理 ==================== -->

    <!-- 插入模板版本记录 -->
    <insert id="insertTemplateVersion">
        INSERT INTO acc_report_template_version (
            template_id, version, version_note, update_content, change_type,
            updated_by, create_time
        ) VALUES (
            #{templateId}, #{version}, #{versionNote}, #{updateContent}, #{changeType},
            #{updatedBy}, NOW()
        )
    </insert>

    <!-- 获取模板版本历史 -->
    <select id="selectTemplateVersionHistory" resultType="java.util.Map">
        SELECT
            version,
            version_note,
            change_type,
            updated_by,
            create_time,
            (SELECT nickname FROM sys_user WHERE user_id = rtv.updated_by) AS updated_by_name
        FROM acc_report_template_version rtv
        WHERE template_id = #{templateId}
        ORDER BY create_time DESC
    </select>

    <!-- 获取指定版本的模板配置 -->
    <select id="selectTemplateVersionConfig" resultType="java.util.Map">
        SELECT update_content
        FROM acc_report_template_version
        WHERE template_id = #{templateId}
          AND version = #{version}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 删除模板版本记录 -->
    <delete id="deleteTemplateVersions">
        DELETE FROM acc_report_template_version
        WHERE template_id = #{templateId}
    </delete>

    <!-- ==================== 报表生成记录 ==================== -->

    <!-- 插入报表生成记录 -->
    <insert id="insertReportRecord">
        INSERT INTO acc_report_record (
            report_id, template_id, group_id, report_name, report_type,
            generation_params, status, progress, current_step,
            created_by, create_time
        ) VALUES (
            #{record.reportId}, #{record.templateId}, #{record.groupId},
            #{record.reportName}, #{record.reportType}, #{record.generationParams},
            #{record.status}, #{record.progress}, #{record.currentStep},
            #{record.createdBy}, NOW()
        )
    </insert>

    <!-- 更新报表生成状态 -->
    <update id="updateReportStatus">
        UPDATE acc_report_record
        SET status = #{status},
            progress = #{progress},
            current_step = #{step},
            update_time = NOW()
        WHERE report_id = #{reportId}
    </update>

    <!-- 完成报表生成 -->
    <update id="completeReportGeneration">
        UPDATE acc_report_record
        SET status = #{status},
            progress = 100,
            file_path = #{filePath},
            file_size = #{fileSize},
            execution_time = #{executionTime},
            data_volume = #{dataVolume},
            error_message = #{errorMessage},
            complete_time = NOW(),
            update_time = NOW()
        WHERE report_id = #{reportId}
    </update>

    <!-- 获取模板执行历史 -->
    <select id="selectTemplateExecutionHistory" resultType="java.util.Map">
        SELECT
            report_id,
            report_name,
            status,
            execution_time,
            data_volume,
            file_size,
            create_time,
            complete_time,
            (SELECT nickname FROM sys_user WHERE user_id = rr.created_by) AS created_by_name
        FROM acc_report_record rr
        WHERE template_id = #{templateId}
          AND status IN ('COMPLETED', 'FAILED')
        ORDER BY create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 获取报表详情 -->
    <select id="selectReportDetail" resultType="java.util.Map">
        SELECT
            rr.*,
            rt.template_name,
            (SELECT nickname FROM sys_user WHERE user_id = rr.created_by) AS created_by_name
        FROM acc_report_record rr
        LEFT JOIN acc_report_template rt ON rr.template_id = rt.template_id
        WHERE rr.report_id = #{reportId}
    </select>

    <!-- 更新报表访问统计 -->
    <update id="updateReportAccessStats">
        UPDATE acc_report_record
        SET access_count = access_count + 1,
            last_access_time = NOW(),
            update_time = NOW()
        WHERE report_id = #{reportId}
    </update>

    <!-- 删除过期报表 -->
    <delete id="deleteExpiredReports">
        DELETE FROM acc_report_record
        WHERE create_time &lt; #{expireTime}
          AND status = 'COMPLETED'
    </delete>

    <!-- ==================== 调度任务管理 ==================== -->

    <!-- 插入调度任务 -->
    <insert id="insertScheduleTask">
        INSERT INTO acc_report_schedule (
            schedule_id, template_id, group_id, schedule_name, cron_expression,
            schedule_config, status, created_by, create_time
        ) VALUES (
            #{task.scheduleId}, #{task.templateId}, #{task.groupId},
            #{task.scheduleName}, #{task.cronExpression}, #{task.scheduleConfig},
            #{task.status}, #{task.createdBy}, NOW()
        )
    </insert>

    <!-- 更新调度任务 -->
    <update id="updateScheduleTask">
        UPDATE acc_report_schedule
        SET schedule_name = #{task.scheduleName},
            cron_expression = #{task.cronExpression},
            schedule_config = #{task.scheduleConfig},
            update_time = NOW()
        WHERE schedule_id = #{scheduleId}
    </update>

    <!-- 删除调度任务 -->
    <delete id="deleteScheduleTask">
        DELETE FROM acc_report_schedule
        WHERE schedule_id = #{scheduleId}
    </delete>

    <!-- 获取模板的调度任务列表 -->
    <select id="selectTemplateScheduleTasks" resultType="java.util.Map">
        SELECT
            schedule_id,
            schedule_name,
            cron_expression,
            status,
            last_execution_time,
            next_execution_time,
            execution_count,
            success_count,
            failure_count,
            create_time,
            (SELECT nickname FROM sys_user WHERE user_id = rs.created_by) AS created_by_name
        FROM acc_report_schedule rs
        WHERE template_id = #{templateId}
        ORDER BY create_time DESC
    </select>

    <!-- 获取活跃的调度任务 -->
    <select id="selectActiveScheduleTasks" resultType="java.util.Map">
        SELECT
            schedule_id,
            template_id,
            group_id,
            schedule_name,
            cron_expression,
            schedule_config,
            next_execution_time
        FROM acc_report_schedule
        WHERE status = 'ACTIVE'
          AND next_execution_time &lt;= NOW()
        ORDER BY next_execution_time ASC
    </select>

    <!-- 更新调度任务状态 -->
    <update id="updateScheduleTaskStatus">
        UPDATE acc_report_schedule
        SET status = #{status},
            update_time = NOW()
        WHERE schedule_id = #{scheduleId}
    </update>

    <!-- 更新调度任务执行时间 -->
    <update id="updateScheduleTaskExecutionTime">
        UPDATE acc_report_schedule
        SET last_execution_time = #{lastExecutionTime},
            next_execution_time = #{nextExecutionTime},
            execution_count = execution_count + 1,
            update_time = NOW()
        WHERE schedule_id = #{scheduleId}
    </update>

    <!-- ==================== 权限管理 ==================== -->

    <!-- 插入模板权限 -->
    <insert id="insertTemplatePermission">
        INSERT INTO acc_report_template_permission (
            template_id, user_id, permission, create_time
        ) VALUES (
            #{templateId}, #{userId}, #{permission}, NOW()
        )
    </insert>

    <!-- 删除模板权限 -->
    <delete id="deleteTemplatePermission">
        DELETE FROM acc_report_template_permission
        WHERE template_id = #{templateId}
          AND user_id = #{userId}
          AND permission = #{permission}
    </delete>

    <!-- 检查模板权限 -->
    <select id="checkTemplatePermission" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM acc_report_template_permission
        WHERE template_id = #{templateId}
          AND user_id = #{userId}
          AND permission = #{permission}
    </select>

    <!-- 获取模板权限列表 -->
    <select id="selectTemplatePermissions" resultType="java.util.Map">
        SELECT
            rtp.user_id,
            rtp.permission,
            rtp.create_time,
            su.nickname AS user_name
        FROM acc_report_template_permission rtp
        LEFT JOIN sys_user su ON rtp.user_id = su.user_id
        WHERE rtp.template_id = #{templateId}
        ORDER BY rtp.create_time DESC
    </select>

    <!-- 删除模板所有权限 -->
    <delete id="deleteTemplatePermissions">
        DELETE FROM acc_report_template_permission
        WHERE template_id = #{templateId}
    </delete>

    <!-- ==================== 收藏管理 ==================== -->

    <!-- 插入模板收藏 -->
    <insert id="insertTemplateFavorite">
        INSERT INTO acc_report_template_favorite (
            template_id, user_id, create_time
        ) VALUES (
            #{templateId}, #{userId}, NOW()
        )
    </insert>

    <!-- 删除模板收藏 -->
    <delete id="deleteTemplateFavorite">
        DELETE FROM acc_report_template_favorite
        WHERE template_id = #{templateId}
          AND user_id = #{userId}
    </delete>

    <!-- 检查用户是否收藏模板 -->
    <select id="checkUserFavoriteTemplate" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM acc_report_template_favorite
        WHERE template_id = #{templateId}
          AND user_id = #{userId}
    </select>

    <!-- 获取用户收藏的模板 -->
    <select id="selectUserFavoriteTemplates" resultType="java.util.Map">
        SELECT
            rt.template_id,
            rt.template_name,
            rt.template_description,
            rt.template_type,
            rt.rating,
            rt.usage_count,
            rtf.create_time AS favorite_time
        FROM acc_report_template_favorite rtf
        LEFT JOIN acc_report_template rt ON rtf.template_id = rt.template_id
        WHERE rtf.user_id = #{userId}
          AND rt.deleted = 0
        ORDER BY rtf.create_time DESC
    </select>

    <!-- 删除模板所有收藏 -->
    <delete id="deleteTemplateFavorites">
        DELETE FROM acc_report_template_favorite
        WHERE template_id = #{templateId}
    </delete>

    <!-- ==================== 评价管理 ==================== -->

    <!-- 插入模板评价 -->
    <insert id="insertTemplateRating">
        INSERT INTO acc_report_template_rating (
            template_id, user_id, rating, comment, create_time
        ) VALUES (
            #{templateId}, #{userId}, #{rating}, #{comment}, NOW()
        )
    </insert>

    <!-- 更新模板评价 -->
    <update id="updateTemplateRating">
        UPDATE acc_report_template_rating
        SET rating = #{rating},
            comment = #{comment},
            update_time = NOW()
        WHERE template_id = #{templateId}
          AND user_id = #{userId}
    </update>

    <!-- 删除模板评价 -->
    <delete id="deleteTemplateRating">
        DELETE FROM acc_report_template_rating
        WHERE template_id = #{templateId}
          AND user_id = #{userId}
    </delete>

    <!-- 获取模板评价列表 -->
    <select id="selectTemplateRatings" resultType="java.util.Map">
        SELECT
            rtr.user_id,
            rtr.rating,
            rtr.comment,
            rtr.create_time,
            rtr.update_time,
            su.nickname AS user_name,
            su.avatar AS user_avatar
        FROM acc_report_template_rating rtr
        LEFT JOIN sys_user su ON rtr.user_id = su.user_id
        WHERE rtr.template_id = #{templateId}
        ORDER BY rtr.create_time DESC
    </select>

    <!-- 获取模板平均评分 -->
    <select id="selectTemplateAverageRating" resultType="java.util.Map">
        SELECT
            AVG(rating) AS average_rating,
            COUNT(*) AS rating_count,
            COUNT(CASE WHEN rating = 5 THEN 1 END) AS five_star_count,
            COUNT(CASE WHEN rating = 4 THEN 1 END) AS four_star_count,
            COUNT(CASE WHEN rating = 3 THEN 1 END) AS three_star_count,
            COUNT(CASE WHEN rating = 2 THEN 1 END) AS two_star_count,
            COUNT(CASE WHEN rating = 1 THEN 1 END) AS one_star_count
        FROM acc_report_template_rating
        WHERE template_id = #{templateId}
    </select>

    <!-- 删除模板所有评价 -->
    <delete id="deleteTemplateRatings">
        DELETE FROM acc_report_template_rating
        WHERE template_id = #{templateId}
    </delete>

    <!-- ==================== 统计分析 ==================== -->

    <!-- 获取模板使用统计 -->
    <select id="selectTemplateUsageStats" resultType="java.util.Map">
        SELECT
            rt.usage_count,
            rt.success_count,
            rt.failure_count,
            rt.last_used_time,
            rt.favorite_count,
            rt.rating,
            rt.rating_count,
            COALESCE(daily_stats.daily_usage, 0) AS recent_daily_usage,
            COALESCE(weekly_stats.weekly_usage, 0) AS recent_weekly_usage,
            COALESCE(monthly_stats.monthly_usage, 0) AS recent_monthly_usage
        FROM acc_report_template rt
        LEFT JOIN (
            SELECT template_id, COUNT(*) AS daily_usage
            FROM acc_report_record
            WHERE template_id = #{templateId}
              AND create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
              AND DATE(create_time) = CURDATE()
            GROUP BY template_id
        ) daily_stats ON rt.template_id = daily_stats.template_id
        LEFT JOIN (
            SELECT template_id, COUNT(*) AS weekly_usage
            FROM acc_report_record
            WHERE template_id = #{templateId}
              AND create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            GROUP BY template_id
        ) weekly_stats ON rt.template_id = weekly_stats.template_id
        LEFT JOIN (
            SELECT template_id, COUNT(*) AS monthly_usage
            FROM acc_report_record
            WHERE template_id = #{templateId}
              AND create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY template_id
        ) monthly_stats ON rt.template_id = monthly_stats.template_id
        WHERE rt.template_id = #{templateId}
    </select>

    <!-- 获取模板性能统计 -->
    <select id="selectTemplatePerformanceStats" resultType="java.util.Map">
        SELECT
            rt.avg_execution_time,
            rt.max_execution_time,
            rt.min_execution_time,
            rt.avg_data_volume,
            rt.max_data_volume,
            rt.min_data_volume,
            COALESCE(recent_stats.recent_avg_execution_time, 0) AS recent_avg_execution_time,
            COALESCE(recent_stats.recent_max_execution_time, 0) AS recent_max_execution_time,
            COALESCE(recent_stats.recent_min_execution_time, 0) AS recent_min_execution_time,
            COALESCE(recent_stats.recent_avg_data_volume, 0) AS recent_avg_data_volume,
            COALESCE(recent_stats.recent_success_rate, 0) AS recent_success_rate
        FROM acc_report_template rt
        LEFT JOIN (
            SELECT
                template_id,
                AVG(execution_time) AS recent_avg_execution_time,
                MAX(execution_time) AS recent_max_execution_time,
                MIN(execution_time) AS recent_min_execution_time,
                AVG(data_volume) AS recent_avg_data_volume,
                (SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS recent_success_rate
            FROM acc_report_record
            WHERE template_id = #{templateId}
              AND create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
              AND status IN ('COMPLETED', 'FAILED')
            GROUP BY template_id
        ) recent_stats ON rt.template_id = recent_stats.template_id
        WHERE rt.template_id = #{templateId}
    </select>

    <!-- 获取报表生成趋势 -->
    <select id="selectReportGenerationTrend" resultType="java.util.Map">
        SELECT
            DATE(create_time) AS date,
            COUNT(*) AS total_count,
            SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) AS success_count,
            SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) AS failure_count,
            AVG(execution_time) AS avg_execution_time,
            SUM(data_volume) AS total_data_volume
        FROM acc_report_record
        WHERE create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        GROUP BY DATE(create_time)
        ORDER BY date DESC
    </select>

    <!-- 获取热门模板排行 -->
    <select id="selectPopularTemplates" resultType="java.util.Map">
        SELECT
            rt.template_id,
            rt.template_name,
            rt.template_type,
            rt.rating,
            rt.rating_count,
            rt.favorite_count,
            COALESCE(recent_usage.usage_count, 0) AS recent_usage_count,
            COALESCE(recent_usage.success_rate, 0) AS recent_success_rate
        FROM acc_report_template rt
        LEFT JOIN (
            SELECT
                template_id,
                COUNT(*) AS usage_count,
                (SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS success_rate
            FROM acc_report_record
            WHERE create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
            GROUP BY template_id
        ) recent_usage ON rt.template_id = recent_usage.template_id
        WHERE rt.group_id = #{groupId}
          AND rt.deleted = 0
          AND rt.enabled = 1
        ORDER BY recent_usage.usage_count DESC, rt.rating DESC, rt.favorite_count DESC
        LIMIT #{limit}
    </select>

    <!-- 获取用户报表使用分析 -->
    <select id="selectUserReportUsageAnalysis" resultType="java.util.Map">
        SELECT
            COUNT(*) AS total_reports,
            COUNT(DISTINCT template_id) AS unique_templates,
            SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) AS success_count,
            SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) AS failure_count,
            AVG(execution_time) AS avg_execution_time,
            SUM(data_volume) AS total_data_volume,
            (SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS success_rate,
            COUNT(CASE WHEN DATE(create_time) = CURDATE() THEN 1 END) AS today_count,
            COUNT(CASE WHEN create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) AS week_count,
            COUNT(CASE WHEN create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) AS month_count
        FROM acc_report_record
        WHERE created_by = #{userId}
          AND create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
    </select>

    <!-- 获取群组报表统计 -->
    <select id="selectGroupReportStats" resultType="java.util.Map">
        SELECT
            COUNT(DISTINCT rt.template_id) AS template_count,
            COUNT(DISTINCT rr.report_id) AS report_count,
            COUNT(DISTINCT rr.created_by) AS active_users,
            AVG(rt.rating) AS avg_template_rating,
            SUM(rt.usage_count) AS total_usage_count,
            SUM(rt.favorite_count) AS total_favorite_count,
            COUNT(CASE WHEN rt.template_status = 'PUBLISHED' THEN 1 END) AS published_templates,
            COUNT(CASE WHEN rt.template_status = 'DRAFT' THEN 1 END) AS draft_templates,
            COUNT(CASE WHEN rr.status = 'COMPLETED' THEN 1 END) AS completed_reports,
            COUNT(CASE WHEN rr.status = 'FAILED' THEN 1 END) AS failed_reports
        FROM acc_report_template rt
        LEFT JOIN acc_report_record rr ON rt.template_id = rr.template_id
        WHERE rt.group_id = #{groupId}
          AND rt.deleted = 0
    </select>

    <!-- 获取系统报表统计 -->
    <select id="selectSystemReportStats" resultType="java.util.Map">
        SELECT
            COUNT(DISTINCT rt.template_id) AS total_templates,
            COUNT(DISTINCT rr.report_id) AS total_reports,
            COUNT(DISTINCT rt.group_id) AS active_groups,
            COUNT(DISTINCT rr.created_by) AS active_users,
            AVG(rt.rating) AS avg_template_rating,
            SUM(rt.usage_count) AS total_usage_count,
            SUM(rt.favorite_count) AS total_favorite_count,
            COUNT(CASE WHEN rt.template_status = 'PUBLISHED' THEN 1 END) AS published_templates,
            COUNT(CASE WHEN rt.template_status = 'DRAFT' THEN 1 END) AS draft_templates,
            COUNT(CASE WHEN rt.is_system_template = 1 THEN 1 END) AS system_templates,
            COUNT(CASE WHEN rr.status = 'COMPLETED' THEN 1 END) AS completed_reports,
            COUNT(CASE WHEN rr.status = 'FAILED' THEN 1 END) AS failed_reports,
            COUNT(CASE WHEN rr.create_time >= CURDATE() THEN 1 END) AS today_reports,
            COUNT(CASE WHEN rr.create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) AS week_reports,
            COUNT(CASE WHEN rr.create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) AS month_reports
        FROM acc_report_template rt
        LEFT JOIN acc_report_record rr ON rt.template_id = rr.template_id
        WHERE rt.deleted = 0
    </select>

</mapper>
