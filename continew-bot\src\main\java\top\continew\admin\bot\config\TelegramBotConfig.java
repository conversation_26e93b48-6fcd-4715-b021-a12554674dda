package top.continew.admin.bot.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Telegram机器人配置
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "bot.telegram")
public class TelegramBotConfig {

    /**
     * 是否启用
     */
    private Boolean enabled = false;

    /**
     * Bot Token
     */
    private String token;

    /**
     * Webhook URL
     */
    private String webhookUrl;

    /**
     * 是否使用长轮询
     */
    private Boolean usePolling = true;

    /**
     * 长轮询超时时间（秒）
     */
    private Integer pollingTimeout = 30;

    /**
     * 最大连接数
     */
    private Integer maxConnections = 40;

    /**
     * 允许的更新类型
     */
    private String[] allowedUpdates = {"message", "callback_query", "inline_query"};

    /**
     * 是否删除Webhook
     */
    private Boolean dropPendingUpdates = false;

    /**
     * 代理配置
     */
    private ProxyConfig proxy = new ProxyConfig();

    /**
     * 代理配置
     */
    @Data
    public static class ProxyConfig {
        /**
         * 是否启用代理
         */
        private Boolean enabled = false;

        /**
         * 代理类型
         */
        private String type = "HTTP";

        /**
         * 代理主机
         */
        private String host;

        /**
         * 代理端口
         */
        private Integer port;

        /**
         * 代理用户名
         */
        private String username;

        /**
         * 代理密码
         */
        private String password;
    }
}
