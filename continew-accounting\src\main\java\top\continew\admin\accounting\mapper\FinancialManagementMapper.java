package top.continew.admin.accounting.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.accounting.model.query.BudgetQuery;
import top.continew.admin.accounting.model.query.CostAnalysisQuery;
import top.continew.admin.accounting.model.req.*;
import top.continew.admin.accounting.model.resp.*;
import top.continew.starter.extension.crud.model.resp.PageResp;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 财务管理Mapper接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface FinancialManagementMapper {

    // ==================== 预算管理 ====================

    /**
     * 创建预算
     *
     * @param req 预算创建请求
     * @return 预算ID
     */
    Long createBudget(BudgetCreateReq req);

    /**
     * 创建预算分配
     *
     * @param allocation 预算分配信息
     * @return 影响行数
     */
    int createBudgetAllocation(BudgetCreateReq.BudgetAllocation allocation);

    /**
     * 创建预算预警设置
     *
     * @param alertSettings 预警设置
     * @return 影响行数
     */
    int createBudgetAlert(BudgetCreateReq.AlertSettings alertSettings);

    /**
     * 创建预算审批设置
     *
     * @param approvalSettings 审批设置
     * @return 影响行数
     */
    int createBudgetApproval(BudgetCreateReq.ApprovalSettings approvalSettings);

    /**
     * 更新预算
     *
     * @param budgetId 预算ID
     * @param req      更新请求
     * @return 影响行数
     */
    int updateBudget(@Param("budgetId") Long budgetId, @Param("req") BudgetUpdateReq req);

    /**
     * 更新预算预警设置
     *
     * @param budgetId      预算ID
     * @param alertSettings 预警设置
     * @return 影响行数
     */
    int updateBudgetAlert(@Param("budgetId") Long budgetId, @Param("alertSettings") BudgetUpdateReq.AlertSettings alertSettings);

    /**
     * 更新预算审批设置
     *
     * @param budgetId         预算ID
     * @param approvalSettings 审批设置
     * @return 影响行数
     */
    int updateBudgetApproval(@Param("budgetId") Long budgetId, @Param("approvalSettings") BudgetUpdateReq.ApprovalSettings approvalSettings);

    /**
     * 删除预算
     *
     * @param budgetId 预算ID
     * @return 影响行数
     */
    int deleteBudget(@Param("budgetId") Long budgetId);

    /**
     * 删除预算分配
     *
     * @param budgetId 预算ID
     * @return 影响行数
     */
    int deleteBudgetAllocations(@Param("budgetId") Long budgetId);

    /**
     * 删除预算预警设置
     *
     * @param budgetId 预算ID
     * @return 影响行数
     */
    int deleteBudgetAlert(@Param("budgetId") Long budgetId);

    /**
     * 删除预算审批设置
     *
     * @param budgetId 预算ID
     * @return 影响行数
     */
    int deleteBudgetApproval(@Param("budgetId") Long budgetId);

    /**
     * 根据ID获取预算
     *
     * @param budgetId 预算ID
     * @return 预算信息
     */
    BudgetDetailResp getBudgetById(@Param("budgetId") Long budgetId);

    /**
     * 获取预算详情
     *
     * @param budgetId 预算ID
     * @return 预算详情
     */
    BudgetDetailResp getBudgetDetail(@Param("budgetId") Long budgetId);

    /**
     * 获取预算列表
     *
     * @param query 查询条件
     * @return 预算列表
     */
    PageResp<BudgetListResp> getBudgetList(BudgetQuery query);

    /**
     * 获取预算执行情况
     *
     * @param budgetId 预算ID
     * @return 预算执行情况
     */
    BudgetExecutionResp getBudgetExecution(@Param("budgetId") Long budgetId);

    /**
     * 检查预算权限
     *
     * @param budgetId 预算ID
     * @param userId   用户ID
     * @return 是否有权限
     */
    boolean hasBudgetPermission(@Param("budgetId") Long budgetId, @Param("userId") Long userId);

    /**
     * 检查预算是否正在使用
     *
     * @param budgetId 预算ID
     * @return 是否正在使用
     */
    boolean isBudgetInUse(@Param("budgetId") Long budgetId);

    /**
     * 检查预算是否存在
     *
     * @param groupId      群组ID
     * @param budgetName   预算名称
     * @param budgetPeriod 预算期间
     * @param excludeId    排除的ID
     * @return 是否存在
     */
    boolean isBudgetExists(@Param("groupId") Long groupId, @Param("budgetName") String budgetName,
                           @Param("budgetPeriod") String budgetPeriod, @Param("excludeId") Long excludeId);

    // ==================== 审核管理 ====================

    /**
     * 创建审核
     *
     * @param req 审核提交请求
     * @return 审核ID
     */
    Long createAudit(AuditSubmitReq req);

    /**
     * 创建审核规则
     *
     * @param auditRules 审核规则
     * @return 影响行数
     */
    int createAuditRules(AuditSubmitReq.AuditRules auditRules);

    /**
     * 创建审核通知设置
     *
     * @param notificationSettings 通知设置
     * @return 影响行数
     */
    int createAuditNotification(AuditSubmitReq.NotificationSettings notificationSettings);

    /**
     * 审核通过
     *
     * @param auditId 审核ID
     * @param req     审核请求
     * @return 影响行数
     */
    int approveAudit(@Param("auditId") Long auditId, @Param("req") AuditReq req);

    /**
     * 创建审核历史
     *
     * @param auditId       审核ID
     * @param userId        用户ID
     * @param operationType 操作类型
     * @param comment       备注
     * @return 影响行数
     */
    int createAuditHistory(@Param("auditId") Long auditId, @Param("userId") Long userId,
                           @Param("operationType") String operationType, @Param("comment") String comment);

    /**
     * 根据ID获取审核
     *
     * @param auditId 审核ID
     * @return 审核信息
     */
    AuditDetailResp getAuditById(@Param("auditId") Long auditId);

    /**
     * 检查审核权限
     *
     * @param auditId 审核ID
     * @param userId  用户ID
     * @return 是否有权限
     */
    boolean hasAuditPermission(@Param("auditId") Long auditId, @Param("userId") Long userId);

    /**
     * 检查审核是否待处理
     *
     * @param auditId 审核ID
     * @return 是否待处理
     */
    boolean isAuditPending(@Param("auditId") Long auditId);

    // ==================== 财务预警 ====================

    /**
     * 创建预警规则
     *
     * @param req 预警规则创建请求
     * @return 规则ID
     */
    Long createAlertRule(AlertRuleCreateReq req);

    /**
     * 创建预警
     *
     * @param groupId     群组ID
     * @param ruleName    规则名称
     * @param alertLevel  预警级别
     * @param message     预警消息
     * @param triggeredAt 触发时间
     * @return 影响行数
     */
    int createAlert(@Param("groupId") Long groupId, @Param("ruleName") String ruleName,
                    @Param("alertLevel") String alertLevel, @Param("message") String message,
                    @Param("triggeredAt") LocalDateTime triggeredAt);

    /**
     * 获取启用的预警规则
     *
     * @param groupId 群组ID
     * @return 预警规则列表
     */
    List<AlertRuleCreateReq> getEnabledAlertRules(@Param("groupId") Long groupId);

    /**
     * 检查预警规则名称是否存在
     *
     * @param groupId   群组ID
     * @param ruleName  规则名称
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isAlertRuleNameExists(@Param("groupId") Long groupId, @Param("ruleName") String ruleName,
                                  @Param("excludeId") Long excludeId);

    /**
     * 检查预算阈值
     *
     * @param rule 预警规则
     * @return 是否触发
     */
    boolean checkBudgetThreshold(AlertRuleCreateReq rule);

    /**
     * 检查金额限制
     *
     * @param rule 预警规则
     * @return 是否触发
     */
    boolean checkAmountLimit(AlertRuleCreateReq rule);

    /**
     * 检查频率限制
     *
     * @param rule 预警规则
     * @return 是否触发
     */
    boolean checkFrequencyLimit(AlertRuleCreateReq rule);

    /**
     * 检查异常检测
     *
     * @param rule 预警规则
     * @return 是否触发
     */
    boolean checkAnomalyDetection(AlertRuleCreateReq rule);

    /**
     * 检查趋势分析
     *
     * @param rule 预警规则
     * @return 是否触发
     */
    boolean checkTrendAnalysis(AlertRuleCreateReq rule);

    // ==================== 成本分析 ====================

    /**
     * 成本分析
     *
     * @param query 查询条件
     * @return 分析结果
     */
    CostAnalysisResp analyzeCost(CostAnalysisQuery query);

    /**
     * 获取成本趋势数据
     *
     * @param query 查询条件
     * @return 趋势数据
     */
    List<CostAnalysisResp.TrendAnalysisData.TrendDataPoint> getCostTrendData(CostAnalysisQuery query);

    /**
     * 获取成本对比数据
     *
     * @param query 查询条件
     * @return 对比数据
     */
    CostAnalysisResp.ComparisonAnalysisData getCostComparisonData(CostAnalysisQuery query);

    /**
     * 获取成本分解数据
     *
     * @param query 查询条件
     * @return 分解数据
     */
    CostAnalysisResp.BreakdownAnalysisData getCostBreakdownData(CostAnalysisQuery query);

    /**
     * 获取效益分析数据
     *
     * @param query 查询条件
     * @return 效益数据
     */
    CostAnalysisResp.BenefitAnalysisData getBenefitAnalysisData(CostAnalysisQuery query);

    // ==================== 财务报表 ====================

    /**
     * 生成财务报表
     *
     * @param req 报表请求
     * @return 报表响应
     */
    FinancialReportResp generateFinancialReport(FinancialReportReq req);

    /**
     * 获取财务报表列表
     *
     * @param groupId 群组ID
     * @return 报表列表
     */
    List<FinancialReportListResp> getFinancialReportList(@Param("groupId") Long groupId);

    // ==================== 财务指标 ====================

    /**
     * 计算财务指标
     *
     * @param groupId 群组ID
     * @return 财务指标
     */
    FinancialMetricsResp calculateFinancialMetrics(@Param("groupId") Long groupId);

    /**
     * 获取财务健康度
     *
     * @param groupId 群组ID
     * @return 财务健康度
     */
    FinancialHealthResp getFinancialHealth(@Param("groupId") Long groupId);

    /**
     * 获取财务建议
     *
     * @param groupId 群组ID
     * @return 财务建议
     */
    FinancialAdviceResp getFinancialAdvice(@Param("groupId") Long groupId);
}
