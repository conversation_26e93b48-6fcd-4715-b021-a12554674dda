<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.accounting.mapper.ReportMapper">

    <!-- 查询报表总览数据 -->
    <select id="selectReportOverview" resultType="top.continew.admin.accounting.model.resp.ReportOverviewResp">
        SELECT 
            COALESCE(SUM(CASE WHEN t.type = 'INCOME' THEN t.amount ELSE 0 END), 0) AS totalIncome,
            COALESCE(SUM(CASE WHEN t.type = 'EXPENSE' THEN t.amount ELSE 0 END), 0) AS totalExpense,
            COALESCE(SUM(CASE WHEN t.type = 'INCOME' THEN t.amount ELSE -t.amount END), 0) AS netIncome,
            COUNT(*) AS transactionCount,
            COALESCE(AVG(t.amount), 0) AS avgTransactionAmount,
            COALESCE(MAX(CASE WHEN t.type = 'EXPENSE' THEN t.amount END), 0) AS maxExpense,
            COALESCE(MAX(CASE WHEN t.type = 'INCOME' THEN t.amount END), 0) AS maxIncome,
            COUNT(DISTINCT DATE(t.transaction_date)) AS activeDays,
            COALESCE(SUM(CASE WHEN t.type = 'EXPENSE' THEN t.amount ELSE 0 END) / NULLIF(COUNT(DISTINCT DATE(t.transaction_date)), 0), 0) AS avgDailyExpense,
            COALESCE(SUM(CASE WHEN t.type = 'INCOME' THEN t.amount ELSE 0 END) / NULLIF(COUNT(DISTINCT DATE(t.transaction_date)), 0), 0) AS avgDailyIncome
        FROM acc_transaction t
        WHERE t.group_id = #{query.groupId}
          AND t.is_deleted = 0
          <if test="query.startDate != null">
              AND DATE(t.transaction_date) >= #{query.startDate}
          </if>
          <if test="query.endDate != null">
              AND DATE(t.transaction_date) <= #{query.endDate}
          </if>
          <if test="query.type != null and query.type != ''">
              AND t.type = #{query.type}
          </if>
          <if test="query.categoryIds != null and query.categoryIds.size() > 0">
              AND t.category_id IN
              <foreach collection="query.categoryIds" item="categoryId" open="(" separator="," close=")">
                  #{categoryId}
              </foreach>
          </if>
          <if test="query.walletIds != null and query.walletIds.size() > 0">
              AND t.wallet_id IN
              <foreach collection="query.walletIds" item="walletId" open="(" separator="," close=")">
                  #{walletId}
              </foreach>
          </if>
          <if test="query.memberIds != null and query.memberIds.size() > 0">
              AND (t.created_by IN
              <foreach collection="query.memberIds" item="memberId" open="(" separator="," close=")">
                  #{memberId}
              </foreach>
              OR JSON_OVERLAPS(t.participants, CAST(#{query.memberIds} AS JSON)))
          </if>
          <if test="query.tags != null and query.tags.size() > 0">
              AND JSON_OVERLAPS(t.tags, CAST(#{query.tags} AS JSON))
          </if>
          <if test="query.currency != null and query.currency != ''">
              AND t.currency = #{query.currency}
          </if>
          <if test="query.minAmount != null">
              AND t.amount >= #{query.minAmount}
          </if>
          <if test="query.maxAmount != null">
              AND t.amount <= #{query.maxAmount}
          </if>
    </select>

    <!-- 查询趋势数据 -->
    <select id="selectReportTrend" resultType="top.continew.admin.accounting.model.resp.ReportTrendResp">
        SELECT 
            <choose>
                <when test="query.dimension == 'DAILY'">
                    DATE_FORMAT(t.transaction_date, '%Y-%m-%d') AS date,
                </when>
                <when test="query.dimension == 'WEEKLY'">
                    DATE_FORMAT(DATE_SUB(t.transaction_date, INTERVAL WEEKDAY(t.transaction_date) DAY), '%Y-%m-%d') AS date,
                </when>
                <when test="query.dimension == 'MONTHLY'">
                    DATE_FORMAT(t.transaction_date, '%Y-%m') AS date,
                </when>
                <when test="query.dimension == 'YEARLY'">
                    DATE_FORMAT(t.transaction_date, '%Y') AS date,
                </when>
                <otherwise>
                    DATE_FORMAT(t.transaction_date, '%Y-%m-%d') AS date,
                </otherwise>
            </choose>
            COALESCE(SUM(CASE WHEN t.type = 'INCOME' THEN t.amount ELSE 0 END), 0) AS incomeAmount,
            COALESCE(SUM(CASE WHEN t.type = 'EXPENSE' THEN t.amount ELSE 0 END), 0) AS expenseAmount,
            COALESCE(SUM(CASE WHEN t.type = 'INCOME' THEN t.amount ELSE -t.amount END), 0) AS netAmount,
            COUNT(*) AS transactionCount,
            COUNT(CASE WHEN t.type = 'INCOME' THEN 1 END) AS incomeCount,
            COUNT(CASE WHEN t.type = 'EXPENSE' THEN 1 END) AS expenseCount
        FROM acc_transaction t
        WHERE t.group_id = #{query.groupId}
          AND t.is_deleted = 0
          <if test="query.startDate != null">
              AND DATE(t.transaction_date) >= #{query.startDate}
          </if>
          <if test="query.endDate != null">
              AND DATE(t.transaction_date) <= #{query.endDate}
          </if>
          <if test="query.type != null and query.type != ''">
              AND t.type = #{query.type}
          </if>
          <if test="query.categoryIds != null and query.categoryIds.size() > 0">
              AND t.category_id IN
              <foreach collection="query.categoryIds" item="categoryId" open="(" separator="," close=")">
                  #{categoryId}
              </foreach>
          </if>
          <if test="query.walletIds != null and query.walletIds.size() > 0">
              AND t.wallet_id IN
              <foreach collection="query.walletIds" item="walletId" open="(" separator="," close=")">
                  #{walletId}
              </foreach>
          </if>
          <if test="query.memberIds != null and query.memberIds.size() > 0">
              AND (t.created_by IN
              <foreach collection="query.memberIds" item="memberId" open="(" separator="," close=")">
                  #{memberId}
              </foreach>
              OR JSON_OVERLAPS(t.participants, CAST(#{query.memberIds} AS JSON)))
          </if>
          <if test="query.tags != null and query.tags.size() > 0">
              AND JSON_OVERLAPS(t.tags, CAST(#{query.tags} AS JSON))
          </if>
          <if test="query.currency != null and query.currency != ''">
              AND t.currency = #{query.currency}
          </if>
          <if test="query.minAmount != null">
              AND t.amount >= #{query.minAmount}
          </if>
          <if test="query.maxAmount != null">
              AND t.amount <= #{query.maxAmount}
          </if>
        GROUP BY 
            <choose>
                <when test="query.dimension == 'DAILY'">
                    DATE(t.transaction_date)
                </when>
                <when test="query.dimension == 'WEEKLY'">
                    YEARWEEK(t.transaction_date, 1)
                </when>
                <when test="query.dimension == 'MONTHLY'">
                    DATE_FORMAT(t.transaction_date, '%Y-%m')
                </when>
                <when test="query.dimension == 'YEARLY'">
                    YEAR(t.transaction_date)
                </when>
                <otherwise>
                    DATE(t.transaction_date)
                </otherwise>
            </choose>
        ORDER BY date
        <if test="query.limit != null and query.limit > 0">
            LIMIT #{query.limit}
        </if>
    </select>

    <!-- 查询分类统计数据 -->
    <select id="selectReportCategory" resultType="top.continew.admin.accounting.model.resp.ReportCategoryResp">
        SELECT 
            c.id AS categoryId,
            c.name AS categoryName,
            c.icon AS categoryIcon,
            c.color AS categoryColor,
            pc.name AS parentCategoryName,
            COALESCE(SUM(t.amount), 0) AS totalAmount,
            COUNT(t.id) AS transactionCount,
            COALESCE(AVG(t.amount), 0) AS avgAmount,
            COALESCE(MAX(t.amount), 0) AS maxAmount,
            COALESCE(MIN(t.amount), 0) AS minAmount
        FROM acc_category c
        LEFT JOIN acc_category pc ON c.parent_id = pc.id
        LEFT JOIN acc_transaction t ON c.id = t.category_id 
            AND t.group_id = #{query.groupId}
            AND t.is_deleted = 0
            <if test="query.startDate != null">
                AND DATE(t.transaction_date) >= #{query.startDate}
            </if>
            <if test="query.endDate != null">
                AND DATE(t.transaction_date) <= #{query.endDate}
            </if>
            <if test="query.type != null and query.type != ''">
                AND t.type = #{query.type}
            </if>
            <if test="query.walletIds != null and query.walletIds.size() > 0">
                AND t.wallet_id IN
                <foreach collection="query.walletIds" item="walletId" open="(" separator="," close=")">
                    #{walletId}
                </foreach>
            </if>
            <if test="query.memberIds != null and query.memberIds.size() > 0">
                AND (t.created_by IN
                <foreach collection="query.memberIds" item="memberId" open="(" separator="," close=")">
                    #{memberId}
                </foreach>
                OR JSON_OVERLAPS(t.participants, CAST(#{query.memberIds} AS JSON)))
            </if>
            <if test="query.tags != null and query.tags.size() > 0">
                AND JSON_OVERLAPS(t.tags, CAST(#{query.tags} AS JSON))
            </if>
            <if test="query.currency != null and query.currency != ''">
                AND t.currency = #{query.currency}
            </if>
            <if test="query.minAmount != null">
                AND t.amount >= #{query.minAmount}
            </if>
            <if test="query.maxAmount != null">
                AND t.amount <= #{query.maxAmount}
            </if>
        WHERE c.group_id = #{query.groupId}
          AND c.is_deleted = 0
          <if test="query.categoryIds != null and query.categoryIds.size() > 0">
              AND c.id IN
              <foreach collection="query.categoryIds" item="categoryId" open="(" separator="," close=")">
                  #{categoryId}
              </foreach>
          </if>
        GROUP BY c.id, c.name, c.icon, c.color, pc.name
        HAVING totalAmount > 0
        ORDER BY 
            <choose>
                <when test="query.orderBy == 'amount'">
                    totalAmount
                </when>
                <when test="query.orderBy == 'count'">
                    transactionCount
                </when>
                <when test="query.orderBy == 'avg'">
                    avgAmount
                </when>
                <otherwise>
                    totalAmount
                </otherwise>
            </choose>
            <choose>
                <when test="query.orderDirection == 'ASC'">
                    ASC
                </when>
                <otherwise>
                    DESC
                </otherwise>
            </choose>
        <if test="query.limit != null and query.limit > 0">
            LIMIT #{query.limit}
        </if>
    </select>

    <!-- 查询钱包统计数据 -->
    <select id="selectReportWallet" resultType="top.continew.admin.accounting.model.resp.ReportWalletResp">
        SELECT 
            w.id AS walletId,
            w.name AS walletName,
            w.icon AS walletIcon,
            w.color AS walletColor,
            w.currency AS currency,
            w.balance AS currentBalance,
            COALESCE(SUM(CASE WHEN t.type = 'INCOME' THEN t.amount ELSE 0 END), 0) AS incomeAmount,
            COALESCE(SUM(CASE WHEN t.type = 'EXPENSE' THEN t.amount ELSE 0 END), 0) AS expenseAmount,
            COALESCE(SUM(CASE WHEN t.type = 'INCOME' THEN t.amount ELSE -t.amount END), 0) AS netAmount,
            COUNT(t.id) AS transactionCount,
            COUNT(CASE WHEN t.type = 'INCOME' THEN 1 END) AS incomeCount,
            COUNT(CASE WHEN t.type = 'EXPENSE' THEN 1 END) AS expenseCount
        FROM acc_wallet w
        LEFT JOIN acc_transaction t ON w.id = t.wallet_id 
            AND t.group_id = #{query.groupId}
            AND t.is_deleted = 0
            <if test="query.startDate != null">
                AND DATE(t.transaction_date) >= #{query.startDate}
            </if>
            <if test="query.endDate != null">
                AND DATE(t.transaction_date) <= #{query.endDate}
            </if>
            <if test="query.type != null and query.type != ''">
                AND t.type = #{query.type}
            </if>
            <if test="query.categoryIds != null and query.categoryIds.size() > 0">
                AND t.category_id IN
                <foreach collection="query.categoryIds" item="categoryId" open="(" separator="," close=")">
                    #{categoryId}
                </foreach>
            </if>
            <if test="query.memberIds != null and query.memberIds.size() > 0">
                AND (t.created_by IN
                <foreach collection="query.memberIds" item="memberId" open="(" separator="," close=")">
                    #{memberId}
                </foreach>
                OR JSON_OVERLAPS(t.participants, CAST(#{query.memberIds} AS JSON)))
            </if>
            <if test="query.tags != null and query.tags.size() > 0">
                AND JSON_OVERLAPS(t.tags, CAST(#{query.tags} AS JSON))
            </if>
            <if test="query.currency != null and query.currency != ''">
                AND t.currency = #{query.currency}
            </if>
            <if test="query.minAmount != null">
                AND t.amount >= #{query.minAmount}
            </if>
            <if test="query.maxAmount != null">
                AND t.amount <= #{query.maxAmount}
            </if>
        WHERE w.group_id = #{query.groupId}
          AND w.is_deleted = 0
          <if test="query.walletIds != null and query.walletIds.size() > 0">
              AND w.id IN
              <foreach collection="query.walletIds" item="walletId" open="(" separator="," close=")">
                  #{walletId}
              </foreach>
          </if>
        GROUP BY w.id, w.name, w.icon, w.color, w.currency, w.balance
        ORDER BY 
            <choose>
                <when test="query.orderBy == 'amount'">
                    (incomeAmount + expenseAmount)
                </when>
                <when test="query.orderBy == 'count'">
                    transactionCount
                </when>
                <when test="query.orderBy == 'balance'">
                    currentBalance
                </when>
                <otherwise>
                    (incomeAmount + expenseAmount)
                </otherwise>
            </choose>
            <choose>
                <when test="query.orderDirection == 'ASC'">
                    ASC
                </when>
                <otherwise>
                    DESC
                </otherwise>
            </choose>
        <if test="query.limit != null and query.limit > 0">
            LIMIT #{query.limit}
        </if>
    </select>

    <!-- 查询成员统计数据 -->
    <select id="selectReportMember" resultType="top.continew.admin.accounting.model.resp.ReportMemberResp">
        SELECT
            u.id AS memberId,
            u.nickname AS memberName,
            u.avatar AS memberAvatar,
            gm.role AS role,
            COALESCE(SUM(CASE WHEN t.created_by = u.id AND t.type = 'INCOME' THEN t.amount ELSE 0 END), 0) AS recordedIncomeAmount,
            COALESCE(SUM(CASE WHEN t.created_by = u.id AND t.type = 'EXPENSE' THEN t.amount ELSE 0 END), 0) AS recordedExpenseAmount,
            COUNT(CASE WHEN t.created_by = u.id THEN 1 END) AS recordedTransactionCount,
            COALESCE(SUM(CASE WHEN JSON_CONTAINS(t.participants, CAST(u.id AS JSON)) AND t.type = 'INCOME' THEN t.amount ELSE 0 END), 0) AS participatedIncomeAmount,
            COALESCE(SUM(CASE WHEN JSON_CONTAINS(t.participants, CAST(u.id AS JSON)) AND t.type = 'EXPENSE' THEN t.amount ELSE 0 END), 0) AS participatedExpenseAmount,
            COUNT(CASE WHEN JSON_CONTAINS(t.participants, CAST(u.id AS JSON)) THEN 1 END) AS participatedTransactionCount,
            MAX(t.created_time) AS lastActiveTime,
            COUNT(DISTINCT DATE(t.transaction_date)) AS activeDays
        FROM sys_user u
        INNER JOIN acc_group_member gm ON u.id = gm.user_id AND gm.group_id = #{query.groupId} AND gm.is_deleted = 0
        LEFT JOIN acc_transaction t ON (t.created_by = u.id OR JSON_CONTAINS(t.participants, CAST(u.id AS JSON)))
            AND t.group_id = #{query.groupId}
            AND t.is_deleted = 0
            <if test="query.startDate != null">
                AND DATE(t.transaction_date) >= #{query.startDate}
            </if>
            <if test="query.endDate != null">
                AND DATE(t.transaction_date) <= #{query.endDate}
            </if>
            <if test="query.type != null and query.type != ''">
                AND t.type = #{query.type}
            </if>
            <if test="query.categoryIds != null and query.categoryIds.size() > 0">
                AND t.category_id IN
                <foreach collection="query.categoryIds" item="categoryId" open="(" separator="," close=")">
                    #{categoryId}
                </foreach>
            </if>
            <if test="query.walletIds != null and query.walletIds.size() > 0">
                AND t.wallet_id IN
                <foreach collection="query.walletIds" item="walletId" open="(" separator="," close=")">
                    #{walletId}
                </foreach>
            </if>
            <if test="query.tags != null and query.tags.size() > 0">
                AND JSON_OVERLAPS(t.tags, CAST(#{query.tags} AS JSON))
            </if>
            <if test="query.currency != null and query.currency != ''">
                AND t.currency = #{query.currency}
            </if>
            <if test="query.minAmount != null">
                AND t.amount >= #{query.minAmount}
            </if>
            <if test="query.maxAmount != null">
                AND t.amount <= #{query.maxAmount}
            </if>
        WHERE u.is_deleted = 0
          <if test="query.memberIds != null and query.memberIds.size() > 0">
              AND u.id IN
              <foreach collection="query.memberIds" item="memberId" open="(" separator="," close=")">
                  #{memberId}
              </foreach>
          </if>
        GROUP BY u.id, u.nickname, u.avatar, gm.role
        ORDER BY
            <choose>
                <when test="query.orderBy == 'activity'">
                    activeDays
                </when>
                <when test="query.orderBy == 'amount'">
                    (recordedIncomeAmount + recordedExpenseAmount + participatedIncomeAmount + participatedExpenseAmount)
                </when>
                <when test="query.orderBy == 'count'">
                    (recordedTransactionCount + participatedTransactionCount)
                </when>
                <otherwise>
                    (recordedIncomeAmount + recordedExpenseAmount + participatedIncomeAmount + participatedExpenseAmount)
                </otherwise>
            </choose>
            <choose>
                <when test="query.orderDirection == 'ASC'">
                    ASC
                </when>
                <otherwise>
                    DESC
                </otherwise>
            </choose>
        <if test="query.limit != null and query.limit > 0">
            LIMIT #{query.limit}
        </if>
    </select>

    <!-- 查询标签统计数据 -->
    <select id="selectReportTag" resultType="top.continew.admin.accounting.model.resp.ReportTagResp">
        SELECT
            tag_data.tagName,
            tag_info.color AS tagColor,
            tag_info.icon AS tagIcon,
            COALESCE(SUM(CASE WHEN t.type = 'INCOME' THEN t.amount ELSE 0 END), 0) AS incomeAmount,
            COALESCE(SUM(CASE WHEN t.type = 'EXPENSE' THEN t.amount ELSE 0 END), 0) AS expenseAmount,
            COALESCE(SUM(CASE WHEN t.type = 'INCOME' THEN t.amount ELSE -t.amount END), 0) AS netAmount,
            COUNT(t.id) AS transactionCount,
            COUNT(CASE WHEN t.type = 'INCOME' THEN 1 END) AS incomeCount,
            COUNT(CASE WHEN t.type = 'EXPENSE' THEN 1 END) AS expenseCount,
            COALESCE(AVG(t.amount), 0) AS avgAmount
        FROM (
            SELECT DISTINCT JSON_UNQUOTE(JSON_EXTRACT(t.tags, CONCAT('$[', numbers.n, ']'))) AS tagName
            FROM acc_transaction t
            CROSS JOIN (
                SELECT 0 AS n UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4
                UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9
            ) numbers
            WHERE t.group_id = #{query.groupId}
              AND t.is_deleted = 0
              AND JSON_LENGTH(t.tags) > numbers.n
              AND JSON_UNQUOTE(JSON_EXTRACT(t.tags, CONCAT('$[', numbers.n, ']'))) IS NOT NULL
              <if test="query.tags != null and query.tags.size() > 0">
                  AND JSON_UNQUOTE(JSON_EXTRACT(t.tags, CONCAT('$[', numbers.n, ']'))) IN
                  <foreach collection="query.tags" item="tag" open="(" separator="," close=")">
                      #{tag}
                  </foreach>
              </if>
        ) tag_data
        LEFT JOIN acc_tag tag_info ON tag_data.tagName = tag_info.name AND tag_info.group_id = #{query.groupId} AND tag_info.is_deleted = 0
        LEFT JOIN acc_transaction t ON JSON_CONTAINS(t.tags, JSON_QUOTE(tag_data.tagName))
            AND t.group_id = #{query.groupId}
            AND t.is_deleted = 0
            <if test="query.startDate != null">
                AND DATE(t.transaction_date) >= #{query.startDate}
            </if>
            <if test="query.endDate != null">
                AND DATE(t.transaction_date) <= #{query.endDate}
            </if>
            <if test="query.type != null and query.type != ''">
                AND t.type = #{query.type}
            </if>
            <if test="query.categoryIds != null and query.categoryIds.size() > 0">
                AND t.category_id IN
                <foreach collection="query.categoryIds" item="categoryId" open="(" separator="," close=")">
                    #{categoryId}
                </foreach>
            </if>
            <if test="query.walletIds != null and query.walletIds.size() > 0">
                AND t.wallet_id IN
                <foreach collection="query.walletIds" item="walletId" open="(" separator="," close=")">
                    #{walletId}
                </foreach>
            </if>
            <if test="query.memberIds != null and query.memberIds.size() > 0">
                AND (t.created_by IN
                <foreach collection="query.memberIds" item="memberId" open="(" separator="," close=")">
                    #{memberId}
                </foreach>
                OR JSON_OVERLAPS(t.participants, CAST(#{query.memberIds} AS JSON)))
            </if>
            <if test="query.currency != null and query.currency != ''">
                AND t.currency = #{query.currency}
            </if>
            <if test="query.minAmount != null">
                AND t.amount >= #{query.minAmount}
            </if>
            <if test="query.maxAmount != null">
                AND t.amount <= #{query.maxAmount}
            </if>
        GROUP BY tag_data.tagName, tag_info.color, tag_info.icon
        HAVING transactionCount > 0
        ORDER BY
            <choose>
                <when test="query.orderBy == 'amount'">
                    (incomeAmount + expenseAmount)
                </when>
                <when test="query.orderBy == 'count'">
                    transactionCount
                </when>
                <when test="query.orderBy == 'avg'">
                    avgAmount
                </when>
                <otherwise>
                    (incomeAmount + expenseAmount)
                </otherwise>
            </choose>
            <choose>
                <when test="query.orderDirection == 'ASC'">
                    ASC
                </when>
                <otherwise>
                    DESC
                </otherwise>
            </choose>
        <if test="query.limit != null and query.limit > 0">
            LIMIT #{query.limit}
        </if>
    </select>

    <!-- 查询收支对比数据 -->
    <select id="selectIncomeExpenseCompare" resultType="top.continew.admin.accounting.mapper.ReportMapper$IncomeExpenseCompareResp">
        SELECT
            COALESCE(SUM(CASE WHEN type = 'INCOME' THEN amount ELSE 0 END), 0) AS totalIncome,
            COALESCE(SUM(CASE WHEN type = 'EXPENSE' THEN amount ELSE 0 END), 0) AS totalExpense,
            COALESCE(SUM(CASE WHEN type = 'INCOME' THEN amount ELSE -amount END), 0) AS netIncome,
            COUNT(CASE WHEN type = 'INCOME' THEN 1 END) AS incomeCount,
            COUNT(CASE WHEN type = 'EXPENSE' THEN 1 END) AS expenseCount
        FROM acc_transaction
        WHERE group_id = #{groupId}
          AND is_deleted = 0
          AND DATE(transaction_date) BETWEEN #{startDate} AND #{endDate}
    </select>

    <!-- 查询月度统计数据 -->
    <select id="selectMonthlyStats" resultType="top.continew.admin.accounting.mapper.ReportMapper$MonthlyStatResp">
        SELECT
            DATE_FORMAT(transaction_date, '%Y-%m') AS month,
            COALESCE(SUM(CASE WHEN type = 'INCOME' THEN amount ELSE 0 END), 0) AS incomeAmount,
            COALESCE(SUM(CASE WHEN type = 'EXPENSE' THEN amount ELSE 0 END), 0) AS expenseAmount,
            COALESCE(SUM(CASE WHEN type = 'INCOME' THEN amount ELSE -amount END), 0) AS netAmount,
            COUNT(*) AS transactionCount
        FROM acc_transaction
        WHERE group_id = #{groupId}
          AND is_deleted = 0
          AND YEAR(transaction_date) = #{year}
        GROUP BY DATE_FORMAT(transaction_date, '%Y-%m')
        ORDER BY month
    </select>

    <!-- 查询年度统计数据 -->
    <select id="selectYearlyStats" resultType="top.continew.admin.accounting.mapper.ReportMapper$YearlyStatResp">
        SELECT
            YEAR(transaction_date) AS year,
            COALESCE(SUM(CASE WHEN type = 'INCOME' THEN amount ELSE 0 END), 0) AS incomeAmount,
            COALESCE(SUM(CASE WHEN type = 'EXPENSE' THEN amount ELSE 0 END), 0) AS expenseAmount,
            COALESCE(SUM(CASE WHEN type = 'INCOME' THEN amount ELSE -amount END), 0) AS netAmount,
            COUNT(*) AS transactionCount
        FROM acc_transaction
        WHERE group_id = #{groupId}
          AND is_deleted = 0
          AND YEAR(transaction_date) BETWEEN #{startYear} AND #{endYear}
        GROUP BY YEAR(transaction_date)
        ORDER BY year
    </select>

    <!-- 查询预算执行情况 -->
    <select id="selectBudgetExecution" resultType="top.continew.admin.accounting.mapper.ReportMapper$BudgetExecutionResp">
        SELECT
            c.id AS categoryId,
            c.name AS categoryName,
            COALESCE(b.amount, 0) AS budgetAmount,
            COALESCE(SUM(t.amount), 0) AS actualAmount,
            COALESCE(b.amount - SUM(t.amount), 0) AS remainingAmount,
            CASE
                WHEN b.amount > 0 THEN ROUND((SUM(t.amount) / b.amount) * 100, 2)
                ELSE 0
            END AS executionRate
        FROM acc_category c
        LEFT JOIN acc_budget b ON c.id = b.category_id
            AND b.group_id = #{groupId}
            AND b.is_deleted = 0
            AND DATE_FORMAT(b.period_start, '%Y-%m') = #{month}
        LEFT JOIN acc_transaction t ON c.id = t.category_id
            AND t.group_id = #{groupId}
            AND t.is_deleted = 0
            AND t.type = 'EXPENSE'
            AND DATE_FORMAT(t.transaction_date, '%Y-%m') = #{month}
        WHERE c.group_id = #{groupId}
          AND c.is_deleted = 0
          AND b.id IS NOT NULL
        GROUP BY c.id, c.name, b.amount
        ORDER BY executionRate DESC
    </select>

    <!-- 查询交易频率统计 -->
    <select id="selectTransactionFrequency" resultType="top.continew.admin.accounting.mapper.ReportMapper$TransactionFrequencyResp">
        SELECT
            COALESCE(AVG(daily_count), 0) AS avgDailyTransactions,
            COALESCE(MAX(daily_count), 0) AS maxDailyTransactions,
            COALESCE(MIN(daily_count), 0) AS minDailyTransactions,
            COUNT(DISTINCT transaction_date) AS activeDays,
            DATEDIFF(#{query.endDate}, #{query.startDate}) + 1 AS totalDays
        FROM (
            SELECT
                DATE(transaction_date) AS transaction_date,
                COUNT(*) AS daily_count
            FROM acc_transaction
            WHERE group_id = #{query.groupId}
              AND is_deleted = 0
              AND DATE(transaction_date) BETWEEN #{query.startDate} AND #{query.endDate}
              <if test="query.type != null and query.type != ''">
                  AND type = #{query.type}
              </if>
              <if test="query.categoryIds != null and query.categoryIds.size() > 0">
                  AND category_id IN
                  <foreach collection="query.categoryIds" item="categoryId" open="(" separator="," close=")">
                      #{categoryId}
                  </foreach>
              </if>
              <if test="query.walletIds != null and query.walletIds.size() > 0">
                  AND wallet_id IN
                  <foreach collection="query.walletIds" item="walletId" open="(" separator="," close=")">
                      #{walletId}
                  </foreach>
              </if>
            GROUP BY DATE(transaction_date)
        ) daily_stats
    </select>

    <!-- 查询时段分析数据 -->
    <select id="selectTimeSlotAnalysis" resultType="top.continew.admin.accounting.mapper.ReportMapper$TimeSlotAnalysisResp">
        SELECT
            CASE
                WHEN HOUR(transaction_date) BETWEEN 6 AND 11 THEN '上午(06:00-11:59)'
                WHEN HOUR(transaction_date) BETWEEN 12 AND 17 THEN '下午(12:00-17:59)'
                WHEN HOUR(transaction_date) BETWEEN 18 AND 23 THEN '晚上(18:00-23:59)'
                ELSE '凌晨(00:00-05:59)'
            END AS timeSlot,
            COALESCE(SUM(amount), 0) AS totalAmount,
            COUNT(*) AS transactionCount,
            COALESCE(AVG(amount), 0) AS avgAmount
        FROM acc_transaction
        WHERE group_id = #{query.groupId}
          AND is_deleted = 0
          AND DATE(transaction_date) BETWEEN #{query.startDate} AND #{query.endDate}
          <if test="query.type != null and query.type != ''">
              AND type = #{query.type}
          </if>
          <if test="query.categoryIds != null and query.categoryIds.size() > 0">
              AND category_id IN
              <foreach collection="query.categoryIds" item="categoryId" open="(" separator="," close=")">
                  #{categoryId}
              </foreach>
          </if>
          <if test="query.walletIds != null and query.walletIds.size() > 0">
              AND wallet_id IN
              <foreach collection="query.walletIds" item="walletId" open="(" separator="," close=")">
                  #{walletId}
              </foreach>
          </if>
        GROUP BY timeSlot
        ORDER BY
            CASE timeSlot
                WHEN '凌晨(00:00-05:59)' THEN 1
                WHEN '上午(06:00-11:59)' THEN 2
                WHEN '下午(12:00-17:59)' THEN 3
                WHEN '晚上(18:00-23:59)' THEN 4
            END
    </select>

    <!-- 查询期间对比数据 -->
    <select id="selectPeriodCompare" resultType="top.continew.admin.accounting.mapper.ReportMapper$PeriodCompareResp">
        SELECT
            COALESCE(SUM(CASE WHEN DATE(transaction_date) BETWEEN #{currentStart} AND #{currentEnd} AND type = 'INCOME' THEN amount ELSE 0 END), 0) AS currentIncome,
            COALESCE(SUM(CASE WHEN DATE(transaction_date) BETWEEN #{currentStart} AND #{currentEnd} AND type = 'EXPENSE' THEN amount ELSE 0 END), 0) AS currentExpense,
            COALESCE(SUM(CASE WHEN DATE(transaction_date) BETWEEN #{previousStart} AND #{previousEnd} AND type = 'INCOME' THEN amount ELSE 0 END), 0) AS previousIncome,
            COALESCE(SUM(CASE WHEN DATE(transaction_date) BETWEEN #{previousStart} AND #{previousEnd} AND type = 'EXPENSE' THEN amount ELSE 0 END), 0) AS previousExpense
        FROM acc_transaction
        WHERE group_id = #{groupId}
          AND is_deleted = 0
          AND (DATE(transaction_date) BETWEEN #{currentStart} AND #{currentEnd}
               OR DATE(transaction_date) BETWEEN #{previousStart} AND #{previousEnd})
    </select>

</mapper>
