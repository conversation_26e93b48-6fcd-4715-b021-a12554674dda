package top.continew.admin.accounting.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Google Sheets同步响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "Google Sheets同步响应")
public class GoogleSheetsSyncResp {

    /**
     * 同步ID
     */
    @Schema(description = "同步ID", example = "SYNC_20250101_001")
    private String syncId;

    /**
     * 配置ID
     */
    @Schema(description = "配置ID", example = "1")
    private Long configId;

    /**
     * 配置名称
     */
    @Schema(description = "配置名称", example = "我的记账表格")
    private String configName;

    /**
     * 同步类型
     */
    @Schema(description = "同步类型", example = "INCREMENTAL")
    private String syncType;

    /**
     * 同步类型描述
     */
    @Schema(description = "同步类型描述", example = "增量同步")
    private String syncTypeDesc;

    /**
     * 同步方向
     */
    @Schema(description = "同步方向", example = "TO_SHEETS")
    private String syncDirection;

    /**
     * 同步方向描述
     */
    @Schema(description = "同步方向描述", example = "同步到表格")
    private String syncDirectionDesc;

    /**
     * 同步状态
     */
    @Schema(description = "同步状态", example = "SUCCESS")
    private String syncStatus;

    /**
     * 同步状态描述
     */
    @Schema(description = "同步状态描述", example = "同步成功")
    private String syncStatusDesc;

    /**
     * 触发方式
     */
    @Schema(description = "触发方式", example = "MANUAL")
    private String triggerType;

    /**
     * 触发方式描述
     */
    @Schema(description = "触发方式描述", example = "手动触发")
    private String triggerTypeDesc;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2025-01-01T10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", example = "2025-01-01T10:02:30")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 执行耗时（秒）
     */
    @Schema(description = "执行耗时（秒）", example = "150")
    private Integer durationSeconds;

    /**
     * 执行耗时描述
     */
    @Schema(description = "执行耗时描述", example = "2分30秒")
    private String durationDesc;

    /**
     * 处理记录数
     */
    @Schema(description = "处理记录数", example = "100")
    private Integer processedCount;

    /**
     * 成功记录数
     */
    @Schema(description = "成功记录数", example = "98")
    private Integer successCount;

    /**
     * 失败记录数
     */
    @Schema(description = "失败记录数", example = "2")
    private Integer failedCount;

    /**
     * 跳过记录数
     */
    @Schema(description = "跳过记录数", example = "0")
    private Integer skippedCount;

    /**
     * 成功率
     */
    @Schema(description = "成功率", example = "98.00")
    private Double successRate;

    /**
     * 是否异步执行
     */
    @Schema(description = "是否异步执行", example = "true")
    private Boolean isAsync;

    /**
     * 批量大小
     */
    @Schema(description = "批量大小", example = "100")
    private Integer batchSize;

    /**
     * 批次数量
     */
    @Schema(description = "批次数量", example = "1")
    private Integer batchCount;

    /**
     * 重试次数
     */
    @Schema(description = "重试次数", example = "0")
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    @Schema(description = "最大重试次数", example = "3")
    private Integer maxRetryCount;

    /**
     * 冲突解决策略
     */
    @Schema(description = "冲突解决策略", example = "LOCAL_WINS")
    private String conflictResolution;

    /**
     * 冲突解决策略描述
     */
    @Schema(description = "冲突解决策略描述", example = "本地优先")
    private String conflictResolutionDesc;

    /**
     * 冲突记录数
     */
    @Schema(description = "冲突记录数", example = "0")
    private Integer conflictCount;

    /**
     * 错误代码
     */
    @Schema(description = "错误代码", example = "VALIDATION_ERROR")
    private String errorCode;

    /**
     * 错误消息
     */
    @Schema(description = "错误消息", example = "部分记录格式不正确")
    private String errorMessage;

    /**
     * 详细错误信息
     */
    @Schema(description = "详细错误信息")
    private List<ErrorDetail> errorDetails;

    /**
     * 警告信息
     */
    @Schema(description = "警告信息")
    private List<WarningDetail> warnings;

    /**
     * 同步结果摘要
     */
    @Schema(description = "同步结果摘要")
    private SyncSummary syncSummary;

    /**
     * 数据统计
     */
    @Schema(description = "数据统计")
    private DataStatistics dataStatistics;

    /**
     * 性能指标
     */
    @Schema(description = "性能指标")
    private PerformanceMetrics performanceMetrics;

    /**
     * 备份信息
     */
    @Schema(description = "备份信息")
    private BackupInfo backupInfo;

    /**
     * 执行人ID
     */
    @Schema(description = "执行人ID", example = "1")
    private Long executedBy;

    /**
     * 执行人姓名
     */
    @Schema(description = "执行人姓名", example = "张三")
    private String executedByName;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 群组名称
     */
    @Schema(description = "群组名称", example = "家庭记账群")
    private String groupName;

    /**
     * 同步范围
     */
    @Schema(description = "同步范围")
    private SyncScopeInfo syncScope;

    /**
     * 回调状态
     */
    @Schema(description = "回调状态", example = "SUCCESS")
    private String callbackStatus;

    /**
     * 回调消息
     */
    @Schema(description = "回调消息", example = "回调执行成功")
    private String callbackMessage;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-01-01T10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2025-01-01T10:02:30")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 扩展属性
     */
    @Schema(description = "扩展属性")
    private Map<String, Object> extraProperties;

    /**
     * 错误详情
     */
    @Data
    @Schema(description = "错误详情")
    public static class ErrorDetail {

        /**
         * 错误类型
         */
        @Schema(description = "错误类型", example = "VALIDATION_ERROR")
        private String errorType;

        /**
         * 错误代码
         */
        @Schema(description = "错误代码", example = "INVALID_AMOUNT")
        private String errorCode;

        /**
         * 错误消息
         */
        @Schema(description = "错误消息", example = "金额格式不正确")
        private String errorMessage;

        /**
         * 错误位置
         */
        @Schema(description = "错误位置", example = "第3行，金额列")
        private String errorLocation;

        /**
         * 错误数据
         */
        @Schema(description = "错误数据", example = "abc")
        private String errorData;

        /**
         * 建议修复方案
         */
        @Schema(description = "建议修复方案", example = "请输入有效的数字金额")
        private String suggestedFix;

        /**
         * 错误时间
         */
        @Schema(description = "错误时间", example = "2025-01-01T10:01:15")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime errorTime;
    }

    /**
     * 警告详情
     */
    @Data
    @Schema(description = "警告详情")
    public static class WarningDetail {

        /**
         * 警告类型
         */
        @Schema(description = "警告类型", example = "DATA_INCONSISTENCY")
        private String warningType;

        /**
         * 警告代码
         */
        @Schema(description = "警告代码", example = "DUPLICATE_RECORD")
        private String warningCode;

        /**
         * 警告消息
         */
        @Schema(description = "警告消息", example = "发现重复记录")
        private String warningMessage;

        /**
         * 警告位置
         */
        @Schema(description = "警告位置", example = "第5行")
        private String warningLocation;

        /**
         * 警告数据
         */
        @Schema(description = "警告数据")
        private String warningData;

        /**
         * 处理建议
         */
        @Schema(description = "处理建议", example = "建议检查数据重复性")
        private String suggestion;

        /**
         * 警告时间
         */
        @Schema(description = "警告时间", example = "2025-01-01T10:01:20")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime warningTime;
    }

    /**
     * 同步结果摘要
     */
    @Data
    @Schema(description = "同步结果摘要")
    public static class SyncSummary {

        /**
         * 总体状态
         */
        @Schema(description = "总体状态", example = "SUCCESS_WITH_WARNINGS")
        private String overallStatus;

        /**
         * 总体状态描述
         */
        @Schema(description = "总体状态描述", example = "同步成功但有警告")
        private String overallStatusDesc;

        /**
         * 主要成就
         */
        @Schema(description = "主要成就")
        private List<String> achievements;

        /**
         * 主要问题
         */
        @Schema(description = "主要问题")
        private List<String> issues;

        /**
         * 改进建议
         */
        @Schema(description = "改进建议")
        private List<String> recommendations;

        /**
         * 下次同步建议时间
         */
        @Schema(description = "下次同步建议时间", example = "2025-01-02T10:00:00")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime nextSyncRecommendedTime;
    }

    /**
     * 数据统计
     */
    @Data
    @Schema(description = "数据统计")
    public static class DataStatistics {

        /**
         * 新增记录数
         */
        @Schema(description = "新增记录数", example = "50")
        private Integer insertedCount;

        /**
         * 更新记录数
         */
        @Schema(description = "更新记录数", example = "30")
        private Integer updatedCount;

        /**
         * 删除记录数
         */
        @Schema(description = "删除记录数", example = "5")
        private Integer deletedCount;

        /**
         * 未变更记录数
         */
        @Schema(description = "未变更记录数", example = "15")
        private Integer unchangedCount;

        /**
         * 数据源记录总数
         */
        @Schema(description = "数据源记录总数", example = "1000")
        private Integer sourceRecordCount;

        /**
         * 目标记录总数
         */
        @Schema(description = "目标记录总数", example = "1095")
        private Integer targetRecordCount;

        /**
         * 同步前记录数
         */
        @Schema(description = "同步前记录数", example = "1000")
        private Integer beforeSyncCount;

        /**
         * 同步后记录数
         */
        @Schema(description = "同步后记录数", example = "1095")
        private Integer afterSyncCount;

        /**
         * 数据变化率
         */
        @Schema(description = "数据变化率", example = "9.50")
        private Double changeRate;
    }

    /**
     * 性能指标
     */
    @Data
    @Schema(description = "性能指标")
    public static class PerformanceMetrics {

        /**
         * 平均处理速度（记录/秒）
         */
        @Schema(description = "平均处理速度（记录/秒）", example = "66.67")
        private Double avgProcessingSpeed;

        /**
         * 峰值处理速度（记录/秒）
         */
        @Schema(description = "峰值处理速度（记录/秒）", example = "100.00")
        private Double peakProcessingSpeed;

        /**
         * 网络耗时（秒）
         */
        @Schema(description = "网络耗时（秒）", example = "30")
        private Integer networkDuration;

        /**
         * 数据处理耗时（秒）
         */
        @Schema(description = "数据处理耗时（秒）", example = "90")
        private Integer processingDuration;

        /**
         * 验证耗时（秒）
         */
        @Schema(description = "验证耗时（秒）", example = "20")
        private Integer validationDuration;

        /**
         * 写入耗时（秒）
         */
        @Schema(description = "写入耗时（秒）", example = "10")
        private Integer writeDuration;

        /**
         * 内存使用峰值（MB）
         */
        @Schema(description = "内存使用峰值（MB）", example = "256")
        private Integer peakMemoryUsage;

        /**
         * CPU使用率峰值（%）
         */
        @Schema(description = "CPU使用率峰值（%）", example = "75.5")
        private Double peakCpuUsage;
    }

    /**
     * 备份信息
     */
    @Data
    @Schema(description = "备份信息")
    public static class BackupInfo {

        /**
         * 是否创建备份
         */
        @Schema(description = "是否创建备份", example = "true")
        private Boolean backupCreated;

        /**
         * 备份文件路径
         */
        @Schema(description = "备份文件路径", example = "/backups/sheets_backup_20250101_100000.xlsx")
        private String backupFilePath;

        /**
         * 备份文件大小（字节）
         */
        @Schema(description = "备份文件大小（字节）", example = "1048576")
        private Long backupFileSize;

        /**
         * 备份文件大小描述
         */
        @Schema(description = "备份文件大小描述", example = "1.0 MB")
        private String backupFileSizeDesc;

        /**
         * 备份创建时间
         */
        @Schema(description = "备份创建时间", example = "2025-01-01T09:59:30")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime backupCreateTime;

        /**
         * 备份过期时间
         */
        @Schema(description = "备份过期时间", example = "2025-01-31T09:59:30")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime backupExpireTime;

        /**
         * 备份记录数
         */
        @Schema(description = "备份记录数", example = "1000")
        private Integer backupRecordCount;
    }

    /**
     * 同步范围信息
     */
    @Data
    @Schema(description = "同步范围信息")
    public static class SyncScopeInfo {

        /**
         * 日期范围
         */
        @Schema(description = "日期范围", example = "2025-01-01 至 2025-01-31")
        private String dateRange;

        /**
         * 分类范围
         */
        @Schema(description = "分类范围")
        private List<String> categoryNames;

        /**
         * 标签范围
         */
        @Schema(description = "标签范围")
        private List<String> tags;

        /**
         * 账单类型范围
         */
        @Schema(description = "账单类型范围")
        private List<String> transactionTypes;

        /**
         * 最大记录数限制
         */
        @Schema(description = "最大记录数限制", example = "1000")
        private Integer maxRecords;

        /**
         * 实际处理记录数
         */
        @Schema(description = "实际处理记录数", example = "100")
        private Integer actualProcessedRecords;

        /**
         * 范围描述
         */
        @Schema(description = "范围描述", example = "同步最近30天的餐饮和交通支出记录")
        private String scopeDescription;
    }
}
