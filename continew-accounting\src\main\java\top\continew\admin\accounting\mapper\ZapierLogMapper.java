package top.continew.admin.accounting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.accounting.model.entity.ZapierLogDO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Zapier日志 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface ZapierLogMapper extends BaseMapper<ZapierLogDO> {

    /**
     * 查询配置的执行日志
     *
     * @param configId 配置ID
     * @param limit    限制数量
     * @return 日志列表
     */
    List<ZapierLogDO> selectByConfigId(@Param("configId") Long configId, @Param("limit") Integer limit);

    /**
     * 查询群组的执行日志
     *
     * @param groupId 群组ID
     * @param limit   限制数量
     * @return 日志列表
     */
    List<ZapierLogDO> selectByGroupId(@Param("groupId") Long groupId, @Param("limit") Integer limit);

    /**
     * 查询失败的执行日志
     *
     * @param configId 配置ID
     * @param hours    小时数
     * @return 失败日志列表
     */
    List<ZapierLogDO> selectFailedLogs(@Param("configId") Long configId, @Param("hours") Integer hours);

    /**
     * 查询执行统计
     *
     * @param configId  配置ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 执行统计
     */
    Map<String, Object> selectExecutionStats(@Param("configId") Long configId,
                                             @Param("startTime") LocalDateTime startTime,
                                             @Param("endTime") LocalDateTime endTime);

    /**
     * 查询群组执行统计
     *
     * @param groupId   群组ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 群组执行统计
     */
    Map<String, Object> selectGroupExecutionStats(@Param("groupId") Long groupId,
                                                  @Param("startTime") LocalDateTime startTime,
                                                  @Param("endTime") LocalDateTime endTime);

    /**
     * 查询执行趋势
     *
     * @param configId  配置ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param interval  时间间隔（hour/day/week/month）
     * @return 执行趋势
     */
    List<Map<String, Object>> selectExecutionTrend(@Param("configId") Long configId,
                                                   @Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime,
                                                   @Param("interval") String interval);

    /**
     * 查询错误分布统计
     *
     * @param configId  配置ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 错误分布统计
     */
    List<Map<String, Object>> selectErrorDistribution(@Param("configId") Long configId,
                                                      @Param("startTime") LocalDateTime startTime,
                                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 查询性能统计
     *
     * @param configId  配置ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 性能统计
     */
    Map<String, Object> selectPerformanceStats(@Param("configId") Long configId,
                                               @Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime);

    /**
     * 查询慢执行日志
     *
     * @param configId        配置ID
     * @param thresholdMs     执行时间阈值（毫秒）
     * @param hours           小时数
     * @return 慢执行日志列表
     */
    List<ZapierLogDO> selectSlowExecutionLogs(@Param("configId") Long configId,
                                              @Param("thresholdMs") Long thresholdMs,
                                              @Param("hours") Integer hours);

    /**
     * 查询重试日志
     *
     * @param originalLogId 原始日志ID
     * @return 重试日志列表
     */
    List<ZapierLogDO> selectRetryLogs(@Param("originalLogId") Long originalLogId);

    /**
     * 查询业务相关日志
     *
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @return 日志列表
     */
    List<ZapierLogDO> selectByBusiness(@Param("businessType") String businessType,
                                       @Param("businessId") Long businessId);

    /**
     * 删除过期日志
     *
     * @param days 保留天数
     * @return 删除行数
     */
    int deleteExpiredLogs(@Param("days") Integer days);

    /**
     * 删除配置的所有日志
     *
     * @param configId 配置ID
     * @return 删除行数
     */
    int deleteByConfigId(@Param("configId") Long configId);

    /**
     * 删除群组的所有日志
     *
     * @param groupId 群组ID
     * @return 删除行数
     */
    int deleteByGroupId(@Param("groupId") Long groupId);

    /**
     * 查询日志详情（包含配置信息）
     *
     * @param id 日志ID
     * @return 日志详情
     */
    Map<String, Object> selectLogDetail(@Param("id") Long id);

    /**
     * 查询日志列表（包含配置信息）
     *
     * @param configId    配置ID
     * @param groupId     群组ID
     * @param status      执行状态
     * @param triggerType 触发器类型
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 日志列表
     */
    List<Map<String, Object>> selectLogList(@Param("configId") Long configId,
                                            @Param("groupId") Long groupId,
                                            @Param("status") String status,
                                            @Param("triggerType") String triggerType,
                                            @Param("startTime") LocalDateTime startTime,
                                            @Param("endTime") LocalDateTime endTime);

    /**
     * 统计日志数量
     *
     * @param configId  配置ID
     * @param status    执行状态
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 日志数量
     */
    Long countLogs(@Param("configId") Long configId,
                   @Param("status") String status,
                   @Param("startTime") LocalDateTime startTime,
                   @Param("endTime") LocalDateTime endTime);

    /**
     * 查询最近执行日志
     *
     * @param configId 配置ID
     * @param limit    限制数量
     * @return 最近执行日志
     */
    List<ZapierLogDO> selectRecentLogs(@Param("configId") Long configId, @Param("limit") Integer limit);

    /**
     * 查询异常日志
     *
     * @param groupId   群组ID
     * @param hours     小时数
     * @return 异常日志列表
     */
    List<ZapierLogDO> selectAnomalyLogs(@Param("groupId") Long groupId, @Param("hours") Integer hours);

    /**
     * 查询HTTP状态码分布
     *
     * @param configId  配置ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return HTTP状态码分布
     */
    List<Map<String, Object>> selectHttpStatusDistribution(@Param("configId") Long configId,
                                                           @Param("startTime") LocalDateTime startTime,
                                                           @Param("endTime") LocalDateTime endTime);

    /**
     * 查询数据传输统计
     *
     * @param configId  配置ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 数据传输统计
     */
    Map<String, Object> selectDataTransferStats(@Param("configId") Long configId,
                                                @Param("startTime") LocalDateTime startTime,
                                                @Param("endTime") LocalDateTime endTime);

    /**
     * 查询执行时间分布
     *
     * @param configId  配置ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 执行时间分布
     */
    List<Map<String, Object>> selectExecutionTimeDistribution(@Param("configId") Long configId,
                                                              @Param("startTime") LocalDateTime startTime,
                                                              @Param("endTime") LocalDateTime endTime);
}
