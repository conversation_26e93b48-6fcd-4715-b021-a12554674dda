package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * 债务详情响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "债务详情响应")
public class DebtDetailResp extends DebtListResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联交易ID
     */
    @Schema(description = "关联交易ID", example = "1")
    private Long transactionId;

    /**
     * 关联交易信息
     */
    @Schema(description = "关联交易信息")
    private TransactionSimpleResp transaction;

    /**
     * 还款记录列表
     */
    @Schema(description = "还款记录列表")
    private List<DebtPaymentResp> payments;

    /**
     * 还款计划列表
     */
    @Schema(description = "还款计划列表")
    private List<DebtPaymentPlanResp> paymentPlans;

    /**
     * 债务统计信息
     */
    @Schema(description = "债务统计信息")
    private DebtStatisticsResp statistics;

    /**
     * 债务历史记录
     */
    @Schema(description = "债务历史记录")
    private List<DebtHistoryResp> history;

    /**
     * 是否可以编辑
     */
    @Schema(description = "是否可以编辑", example = "true")
    private Boolean canEdit;

    /**
     * 是否可以删除
     */
    @Schema(description = "是否可以删除", example = "true")
    private Boolean canDelete;

    /**
     * 是否可以还款
     */
    @Schema(description = "是否可以还款", example = "true")
    private Boolean canPayment;

    /**
     * 是否可以确认还款
     */
    @Schema(description = "是否可以确认还款", example = "true")
    private Boolean canConfirmPayment;

    /**
     * 下次还款金额
     */
    @Schema(description = "下次还款金额", example = "100.00")
    private java.math.BigDecimal nextPaymentAmount;

    /**
     * 下次还款时间
     */
    @Schema(description = "下次还款时间", example = "2025-02-01 00:00:00")
    private java.time.LocalDateTime nextPaymentDate;

    /**
     * 累计利息
     */
    @Schema(description = "累计利息", example = "50.00")
    private java.math.BigDecimal totalInterest;

    /**
     * 还款进度（百分比）
     */
    @Schema(description = "还款进度", example = "30.0")
    private java.math.BigDecimal paymentProgress;
}
