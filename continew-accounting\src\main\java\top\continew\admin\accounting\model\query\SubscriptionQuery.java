package top.continew.admin.accounting.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.admin.accounting.enums.SubscriptionStatus;
import top.continew.starter.extension.crud.model.query.PageQuery;

import java.time.LocalDateTime;

/**
 * 订阅查询条件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "订阅查询条件")
public class SubscriptionQuery extends PageQuery {

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 套餐ID
     */
    @Schema(description = "套餐ID", example = "1")
    private Long planId;

    /**
     * 订阅用户ID
     */
    @Schema(description = "订阅用户ID", example = "1")
    private Long userId;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "ACTIVE")
    private SubscriptionStatus status;

    /**
     * 开始时间-开始
     */
    @Schema(description = "开始时间-开始", example = "2025-01-01 00:00:00")
    private LocalDateTime startDateStart;

    /**
     * 开始时间-结束
     */
    @Schema(description = "开始时间-结束", example = "2025-12-31 23:59:59")
    private LocalDateTime startDateEnd;

    /**
     * 结束时间-开始
     */
    @Schema(description = "结束时间-开始", example = "2025-01-01 00:00:00")
    private LocalDateTime endDateStart;

    /**
     * 结束时间-结束
     */
    @Schema(description = "结束时间-结束", example = "2025-12-31 23:59:59")
    private LocalDateTime endDateEnd;

    /**
     * 自动续费
     */
    @Schema(description = "自动续费", example = "true")
    private Boolean autoRenew;

    /**
     * 支付方式
     */
    @Schema(description = "支付方式", example = "STRIPE")
    private String paymentMethod;

    /**
     * 即将过期(天数)
     */
    @Schema(description = "即将过期天数", example = "7")
    private Integer expiringInDays;
}
