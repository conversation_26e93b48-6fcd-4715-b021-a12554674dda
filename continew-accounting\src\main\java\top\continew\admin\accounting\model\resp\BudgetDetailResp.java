package top.continew.admin.accounting.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 预算详情响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "预算详情响应")
public class BudgetDetailResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 预算ID
     */
    @Schema(description = "预算ID", example = "budget_123456")
    private String budgetId;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 群组名称
     */
    @Schema(description = "群组名称", example = "家庭账本")
    private String groupName;

    /**
     * 预算名称
     */
    @Schema(description = "预算名称", example = "2025年度预算")
    private String budgetName;

    /**
     * 预算描述
     */
    @Schema(description = "预算描述", example = "2025年度收支预算计划")
    private String description;

    /**
     * 预算类型
     */
    @Schema(description = "预算类型", example = "ANNUAL")
    private String budgetType;

    /**
     * 预算类型名称
     */
    @Schema(description = "预算类型名称", example = "年度预算")
    private String budgetTypeName;

    /**
     * 预算周期
     */
    @Schema(description = "预算周期", example = "YEARLY")
    private String budgetPeriod;

    /**
     * 预算周期名称
     */
    @Schema(description = "预算周期名称", example = "年度")
    private String budgetPeriodName;

    /**
     * 开始日期
     */
    @Schema(description = "开始日期", example = "2025-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @Schema(description = "结束日期", example = "2025-12-31")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    /**
     * 总预算金额
     */
    @Schema(description = "总预算金额", example = "100000.00")
    private BigDecimal totalAmount;

    /**
     * 已使用金额
     */
    @Schema(description = "已使用金额", example = "35000.00")
    private BigDecimal usedAmount;

    /**
     * 剩余金额
     */
    @Schema(description = "剩余金额", example = "65000.00")
    private BigDecimal remainingAmount;

    /**
     * 使用率
     */
    @Schema(description = "使用率", example = "0.35")
    private BigDecimal usageRate;

    /**
     * 币种
     */
    @Schema(description = "币种", example = "CNY")
    private String currency;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "ACTIVE")
    private String status;

    /**
     * 状态名称
     */
    @Schema(description = "状态名称", example = "生效中")
    private String statusName;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    /**
     * 预算分配列表
     */
    @Schema(description = "预算分配列表")
    private List<BudgetAllocationDetail> allocations;

    /**
     * 预警设置
     */
    @Schema(description = "预警设置")
    private AlertSettingsDetail alertSettings;

    /**
     * 审批设置
     */
    @Schema(description = "审批设置")
    private ApprovalSettingsDetail approvalSettings;

    /**
     * 执行统计
     */
    @Schema(description = "执行统计")
    private ExecutionStatistics executionStatistics;

    /**
     * 扩展属性
     */
    @Schema(description = "扩展属性")
    private Map<String, Object> attributes;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID", example = "1")
    private Long createdBy;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名", example = "张三")
    private String createdByName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-01-01 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID", example = "1")
    private Long updatedBy;

    /**
     * 更新人姓名
     */
    @Schema(description = "更新人姓名", example = "张三")
    private String updatedByName;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2025-01-01 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 预算分配详情
     */
    @Data
    @Schema(description = "预算分配详情")
    public static class BudgetAllocationDetail implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 分配ID
         */
        @Schema(description = "分配ID", example = "alloc_123456")
        private String allocationId;

        /**
         * 分配类型
         */
        @Schema(description = "分配类型", example = "CATEGORY")
        private String allocationType;

        /**
         * 分配类型名称
         */
        @Schema(description = "分配类型名称", example = "分类")
        private String allocationTypeName;

        /**
         * 分配目标ID
         */
        @Schema(description = "分配目标ID", example = "1")
        private Long targetId;

        /**
         * 分配目标名称
         */
        @Schema(description = "分配目标名称", example = "餐饮消费")
        private String targetName;

        /**
         * 分配金额
         */
        @Schema(description = "分配金额", example = "10000.00")
        private BigDecimal amount;

        /**
         * 已使用金额
         */
        @Schema(description = "已使用金额", example = "3500.00")
        private BigDecimal usedAmount;

        /**
         * 剩余金额
         */
        @Schema(description = "剩余金额", example = "6500.00")
        private BigDecimal remainingAmount;

        /**
         * 使用率
         */
        @Schema(description = "使用率", example = "0.35")
        private BigDecimal usageRate;

        /**
         * 分配比例
         */
        @Schema(description = "分配比例", example = "0.10")
        private BigDecimal percentage;

        /**
         * 是否允许超支
         */
        @Schema(description = "是否允许超支", example = "false")
        private Boolean allowOverspend;

        /**
         * 超支限额
         */
        @Schema(description = "超支限额", example = "1000.00")
        private BigDecimal overspendLimit;

        /**
         * 超支金额
         */
        @Schema(description = "超支金额", example = "0.00")
        private BigDecimal overspendAmount;

        /**
         * 状态
         */
        @Schema(description = "状态", example = "NORMAL")
        private String status;

        /**
         * 状态名称
         */
        @Schema(description = "状态名称", example = "正常")
        private String statusName;

        /**
         * 备注
         */
        @Schema(description = "备注", example = "餐饮消费预算")
        private String remark;
    }

    /**
     * 预警设置详情
     */
    @Data
    @Schema(description = "预警设置详情")
    public static class AlertSettingsDetail implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 是否启用预警
         */
        @Schema(description = "是否启用预警", example = "true")
        private Boolean enabled;

        /**
         * 预警阈值列表
         */
        @Schema(description = "预警阈值列表")
        private List<AlertThresholdDetail> thresholds;

        /**
         * 预警通知方式
         */
        @Schema(description = "预警通知方式")
        private List<String> notificationMethods;

        /**
         * 预警接收人
         */
        @Schema(description = "预警接收人")
        private List<RecipientDetail> recipients;

        @Data
        @Schema(description = "预警阈值详情")
        public static class AlertThresholdDetail implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 阈值类型
             */
            @Schema(description = "阈值类型", example = "PERCENTAGE")
            private String thresholdType;

            /**
             * 阈值类型名称
             */
            @Schema(description = "阈值类型名称", example = "百分比")
            private String thresholdTypeName;

            /**
             * 阈值
             */
            @Schema(description = "阈值", example = "0.80")
            private BigDecimal threshold;

            /**
             * 预警级别
             */
            @Schema(description = "预警级别", example = "WARNING")
            private String alertLevel;

            /**
             * 预警级别名称
             */
            @Schema(description = "预警级别名称", example = "警告")
            private String alertLevelName;

            /**
             * 预警消息
             */
            @Schema(description = "预警消息", example = "预算使用已达到80%")
            private String message;

            /**
             * 是否已触发
             */
            @Schema(description = "是否已触发", example = "false")
            private Boolean triggered;

            /**
             * 触发时间
             */
            @Schema(description = "触发时间", example = "2025-01-01 10:00:00")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private LocalDateTime triggeredAt;
        }

        @Data
        @Schema(description = "接收人详情")
        public static class RecipientDetail implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 用户ID
             */
            @Schema(description = "用户ID", example = "1")
            private Long userId;

            /**
             * 用户姓名
             */
            @Schema(description = "用户姓名", example = "张三")
            private String userName;

            /**
             * 用户昵称
             */
            @Schema(description = "用户昵称", example = "小张")
            private String nickname;

            /**
             * 邮箱
             */
            @Schema(description = "邮箱", example = "<EMAIL>")
            private String email;

            /**
             * 手机号
             */
            @Schema(description = "手机号", example = "13800138000")
            private String phone;
        }
    }

    /**
     * 审批设置详情
     */
    @Data
    @Schema(description = "审批设置详情")
    public static class ApprovalSettingsDetail implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 是否需要审批
         */
        @Schema(description = "是否需要审批", example = "true")
        private Boolean required;

        /**
         * 审批阈值
         */
        @Schema(description = "审批阈值", example = "1000.00")
        private BigDecimal approvalThreshold;

        /**
         * 审批流程
         */
        @Schema(description = "审批流程")
        private List<ApprovalStepDetail> approvalSteps;

        /**
         * 自动审批规则
         */
        @Schema(description = "自动审批规则")
        private Map<String, Object> autoApprovalRules;

        @Data
        @Schema(description = "审批步骤详情")
        public static class ApprovalStepDetail implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 步骤序号
             */
            @Schema(description = "步骤序号", example = "1")
            private Integer stepOrder;

            /**
             * 步骤名称
             */
            @Schema(description = "步骤名称", example = "部门主管审批")
            private String stepName;

            /**
             * 审批人类型
             */
            @Schema(description = "审批人类型", example = "USER")
            private String approverType;

            /**
             * 审批人类型名称
             */
            @Schema(description = "审批人类型名称", example = "用户")
            private String approverTypeName;

            /**
             * 审批人列表
             */
            @Schema(description = "审批人列表")
            private List<ApproverDetail> approvers;

            /**
             * 是否必须
             */
            @Schema(description = "是否必须", example = "true")
            private Boolean required;

            /**
             * 超时时间（小时）
             */
            @Schema(description = "超时时间（小时）", example = "24")
            private Integer timeoutHours;

            @Data
            @Schema(description = "审批人详情")
            public static class ApproverDetail implements Serializable {

                @Serial
                private static final long serialVersionUID = 1L;

                /**
                 * 审批人ID
                 */
                @Schema(description = "审批人ID", example = "1")
                private Long approverId;

                /**
                 * 审批人姓名
                 */
                @Schema(description = "审批人姓名", example = "李四")
                private String approverName;

                /**
                 * 审批人昵称
                 */
                @Schema(description = "审批人昵称", example = "小李")
                private String approverNickname;

                /**
                 * 部门名称
                 */
                @Schema(description = "部门名称", example = "财务部")
                private String departmentName;

                /**
                 * 职位名称
                 */
                @Schema(description = "职位名称", example = "财务经理")
                private String positionName;
            }
        }
    }

    /**
     * 执行统计
     */
    @Data
    @Schema(description = "执行统计")
    public static class ExecutionStatistics implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 总交易数
         */
        @Schema(description = "总交易数", example = "150")
        private Long totalTransactions;

        /**
         * 本月交易数
         */
        @Schema(description = "本月交易数", example = "25")
        private Long monthlyTransactions;

        /**
         * 平均每日支出
         */
        @Schema(description = "平均每日支出", example = "120.50")
        private BigDecimal avgDailyExpense;

        /**
         * 最大单笔支出
         */
        @Schema(description = "最大单笔支出", example = "2500.00")
        private BigDecimal maxSingleExpense;

        /**
         * 预算达成率
         */
        @Schema(description = "预算达成率", example = "0.75")
        private BigDecimal achievementRate;

        /**
         * 预计完成时间
         */
        @Schema(description = "预计完成时间", example = "2025-10-15")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate estimatedCompletionDate;

        /**
         * 趋势分析
         */
        @Schema(description = "趋势分析", example = "INCREASING")
        private String trend;

        /**
         * 趋势分析名称
         */
        @Schema(description = "趋势分析名称", example = "上升")
        private String trendName;

        /**
         * 风险等级
         */
        @Schema(description = "风险等级", example = "LOW")
        private String riskLevel;

        /**
         * 风险等级名称
         */
        @Schema(description = "风险等级名称", example = "低风险")
        private String riskLevelName;
    }
}
