package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 报表模板创建请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "报表模板创建请求")
public class ReportTemplateCreateReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    @NotNull(message = "群组ID不能为空")
    private Long groupId;

    /**
     * 模板名称
     */
    @Schema(description = "模板名称", example = "月度财务报表")
    @NotBlank(message = "模板名称不能为空")
    @Size(max = 100, message = "模板名称长度不能超过100个字符")
    private String templateName;

    /**
     * 模板描述
     */
    @Schema(description = "模板描述", example = "用于生成月度收支统计报表")
    @Size(max = 500, message = "模板描述长度不能超过500个字符")
    private String templateDescription;

    /**
     * 模板类型
     */
    @Schema(description = "模板类型", example = "FINANCIAL_OVERVIEW", allowableValues = {"FINANCIAL_OVERVIEW", "CATEGORY_ANALYSIS", "MEMBER_ANALYSIS", "TREND_ANALYSIS", "CUSTOM_REPORT"})
    @NotBlank(message = "模板类型不能为空")
    private String templateType;

    /**
     * 报表配置
     */
    @Schema(description = "报表配置")
    @NotNull(message = "报表配置不能为空")
    private ReportConfiguration reportConfig;

    /**
     * 数据源配置
     */
    @Schema(description = "数据源配置")
    @NotNull(message = "数据源配置不能为空")
    private DataSourceConfiguration dataSourceConfig;

    /**
     * 布局配置
     */
    @Schema(description = "布局配置")
    @NotNull(message = "布局配置不能为空")
    private LayoutConfiguration layoutConfig;

    /**
     * 图表配置
     */
    @Schema(description = "图表配置")
    private List<ChartConfiguration> chartConfigs;

    /**
     * 过滤器配置
     */
    @Schema(description = "过滤器配置")
    private List<FilterConfiguration> filterConfigs;

    /**
     * 导出配置
     */
    @Schema(description = "导出配置")
    private ExportConfiguration exportConfig;

    /**
     * 调度配置
     */
    @Schema(description = "调度配置")
    private ScheduleConfiguration scheduleConfig;

    /**
     * 权限配置
     */
    @Schema(description = "权限配置")
    private PermissionConfiguration permissionConfig;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled = true;

    /**
     * 是否公开
     */
    @Schema(description = "是否公开", example = "false")
    private Boolean isPublic = false;

    /**
     * 标签列表
     */
    @Schema(description = "标签列表", example = "[\"财务\", \"月报\"]")
    private List<String> tags;

    /**
     * 扩展属性
     */
    @Schema(description = "扩展属性")
    private Map<String, Object> attributes;

    /**
     * 报表配置
     */
    @Data
    @Schema(description = "报表配置")
    public static class ReportConfiguration implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 报表标题
         */
        @Schema(description = "报表标题", example = "月度财务分析报表")
        @NotBlank(message = "报表标题不能为空")
        private String title;

        /**
         * 报表副标题
         */
        @Schema(description = "报表副标题", example = "详细的收支分析和趋势预测")
        private String subtitle;

        /**
         * 报表格式
         */
        @Schema(description = "报表格式", example = "DASHBOARD", allowableValues = {"DASHBOARD", "TABLE", "CHART", "MIXED"})
        private String format = "DASHBOARD";

        /**
         * 页面大小
         */
        @Schema(description = "页面大小", example = "A4", allowableValues = {"A4", "A3", "LETTER", "CUSTOM"})
        private String pageSize = "A4";

        /**
         * 页面方向
         */
        @Schema(description = "页面方向", example = "PORTRAIT", allowableValues = {"PORTRAIT", "LANDSCAPE"})
        private String orientation = "PORTRAIT";

        /**
         * 主题样式
         */
        @Schema(description = "主题样式", example = "DEFAULT", allowableValues = {"DEFAULT", "MODERN", "CLASSIC", "MINIMAL"})
        private String theme = "DEFAULT";

        /**
         * 颜色方案
         */
        @Schema(description = "颜色方案", example = "BLUE", allowableValues = {"BLUE", "GREEN", "RED", "PURPLE", "ORANGE", "CUSTOM"})
        private String colorScheme = "BLUE";

        /**
         * 自定义CSS
         */
        @Schema(description = "自定义CSS")
        private String customCss;

        /**
         * 水印配置
         */
        @Schema(description = "水印配置")
        private WatermarkConfig watermark;

        /**
         * 页眉配置
         */
        @Schema(description = "页眉配置")
        private HeaderConfig header;

        /**
         * 页脚配置
         */
        @Schema(description = "页脚配置")
        private FooterConfig footer;

        @Data
        @Schema(description = "水印配置")
        public static class WatermarkConfig implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 是否启用水印
             */
            @Schema(description = "是否启用水印", example = "true")
            private Boolean enabled = false;

            /**
             * 水印文本
             */
            @Schema(description = "水印文本", example = "机密文档")
            private String text;

            /**
             * 水印透明度
             */
            @Schema(description = "水印透明度", example = "0.3")
            private Double opacity = 0.3;

            /**
             * 水印角度
             */
            @Schema(description = "水印角度", example = "-45")
            private Integer angle = -45;

            /**
             * 水印颜色
             */
            @Schema(description = "水印颜色", example = "#CCCCCC")
            private String color = "#CCCCCC";
        }

        @Data
        @Schema(description = "页眉配置")
        public static class HeaderConfig implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 是否显示页眉
             */
            @Schema(description = "是否显示页眉", example = "true")
            private Boolean enabled = true;

            /**
             * 页眉内容
             */
            @Schema(description = "页眉内容", example = "财务报表")
            private String content;

            /**
             * 页眉高度
             */
            @Schema(description = "页眉高度", example = "50")
            private Integer height = 50;

            /**
             * 对齐方式
             */
            @Schema(description = "对齐方式", example = "CENTER", allowableValues = {"LEFT", "CENTER", "RIGHT"})
            private String alignment = "CENTER";
        }

        @Data
        @Schema(description = "页脚配置")
        public static class FooterConfig implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 是否显示页脚
             */
            @Schema(description = "是否显示页脚", example = "true")
            private Boolean enabled = true;

            /**
             * 页脚内容
             */
            @Schema(description = "页脚内容", example = "第 {pageNumber} 页，共 {totalPages} 页")
            private String content;

            /**
             * 页脚高度
             */
            @Schema(description = "页脚高度", example = "30")
            private Integer height = 30;

            /**
             * 对齐方式
             */
            @Schema(description = "对齐方式", example = "CENTER", allowableValues = {"LEFT", "CENTER", "RIGHT"})
            private String alignment = "CENTER";

            /**
             * 是否显示页码
             */
            @Schema(description = "是否显示页码", example = "true")
            private Boolean showPageNumber = true;

            /**
             * 是否显示生成时间
             */
            @Schema(description = "是否显示生成时间", example = "true")
            private Boolean showGeneratedTime = true;
        }
    }

    /**
     * 数据源配置
     */
    @Data
    @Schema(description = "数据源配置")
    public static class DataSourceConfiguration implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 数据源类型
         */
        @Schema(description = "数据源类型", example = "DATABASE", allowableValues = {"DATABASE", "API", "FILE", "CUSTOM"})
        @NotBlank(message = "数据源类型不能为空")
        private String sourceType;

        /**
         * 主查询配置
         */
        @Schema(description = "主查询配置")
        @NotNull(message = "主查询配置不能为空")
        private QueryConfiguration mainQuery;

        /**
         * 子查询配置列表
         */
        @Schema(description = "子查询配置列表")
        private List<QueryConfiguration> subQueries;

        /**
         * 数据关联配置
         */
        @Schema(description = "数据关联配置")
        private List<DataJoinConfiguration> dataJoins;

        /**
         * 数据转换配置
         */
        @Schema(description = "数据转换配置")
        private DataTransformConfiguration dataTransform;

        /**
         * 缓存配置
         */
        @Schema(description = "缓存配置")
        private CacheConfiguration cacheConfig;

        @Data
        @Schema(description = "查询配置")
        public static class QueryConfiguration implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 查询名称
             */
            @Schema(description = "查询名称", example = "main_data")
            private String queryName;

            /**
             * 查询类型
             */
            @Schema(description = "查询类型", example = "SQL", allowableValues = {"SQL", "HQL", "NATIVE", "CRITERIA"})
            private String queryType = "SQL";

            /**
             * 查询语句
             */
            @Schema(description = "查询语句")
            private String queryStatement;

            /**
             * 查询参数
             */
            @Schema(description = "查询参数")
            private Map<String, Object> queryParams;

            /**
             * 结果映射
             */
            @Schema(description = "结果映射")
            private Map<String, String> resultMapping;

            /**
             * 分页配置
             */
            @Schema(description = "分页配置")
            private PaginationConfig pagination;

            /**
             * 排序配置
             */
            @Schema(description = "排序配置")
            private List<SortConfig> sortConfigs;

            @Data
            @Schema(description = "分页配置")
            public static class PaginationConfig implements Serializable {

                @Serial
                private static final long serialVersionUID = 1L;

                /**
                 * 是否启用分页
                 */
                @Schema(description = "是否启用分页", example = "false")
                private Boolean enabled = false;

                /**
                 * 页面大小
                 */
                @Schema(description = "页面大小", example = "100")
                private Integer pageSize = 100;

                /**
                 * 最大记录数
                 */
                @Schema(description = "最大记录数", example = "10000")
                private Integer maxRecords = 10000;
            }

            @Data
            @Schema(description = "排序配置")
            public static class SortConfig implements Serializable {

                @Serial
                private static final long serialVersionUID = 1L;

                /**
                 * 排序字段
                 */
                @Schema(description = "排序字段", example = "transaction_date")
                private String field;

                /**
                 * 排序方向
                 */
                @Schema(description = "排序方向", example = "DESC", allowableValues = {"ASC", "DESC"})
                private String direction = "ASC";

                /**
                 * 排序优先级
                 */
                @Schema(description = "排序优先级", example = "1")
                private Integer priority = 1;
            }
        }

        @Data
        @Schema(description = "数据关联配置")
        public static class DataJoinConfiguration implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 关联名称
             */
            @Schema(description = "关联名称", example = "category_join")
            private String joinName;

            /**
             * 关联类型
             */
            @Schema(description = "关联类型", example = "LEFT_JOIN", allowableValues = {"INNER_JOIN", "LEFT_JOIN", "RIGHT_JOIN", "FULL_JOIN"})
            private String joinType = "LEFT_JOIN";

            /**
             * 左表字段
             */
            @Schema(description = "左表字段", example = "category_id")
            private String leftField;

            /**
             * 右表字段
             */
            @Schema(description = "右表字段", example = "id")
            private String rightField;

            /**
             * 关联条件
             */
            @Schema(description = "关联条件")
            private String joinCondition;
        }

        @Data
        @Schema(description = "数据转换配置")
        public static class DataTransformConfiguration implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 字段转换规则
             */
            @Schema(description = "字段转换规则")
            private Map<String, String> fieldTransforms;

            /**
             * 数据聚合规则
             */
            @Schema(description = "数据聚合规则")
            private List<AggregationRule> aggregationRules;

            /**
             * 计算字段
             */
            @Schema(description = "计算字段")
            private List<CalculatedField> calculatedFields;

            @Data
            @Schema(description = "聚合规则")
            public static class AggregationRule implements Serializable {

                @Serial
                private static final long serialVersionUID = 1L;

                /**
                 * 分组字段
                 */
                @Schema(description = "分组字段")
                private List<String> groupByFields;

                /**
                 * 聚合函数
                 */
                @Schema(description = "聚合函数", example = "SUM", allowableValues = {"SUM", "COUNT", "AVG", "MAX", "MIN"})
                private String aggregateFunction;

                /**
                 * 聚合字段
                 */
                @Schema(description = "聚合字段", example = "amount")
                private String aggregateField;

                /**
                 * 结果字段名
                 */
                @Schema(description = "结果字段名", example = "total_amount")
                private String resultFieldName;
            }

            @Data
            @Schema(description = "计算字段")
            public static class CalculatedField implements Serializable {

                @Serial
                private static final long serialVersionUID = 1L;

                /**
                 * 字段名
                 */
                @Schema(description = "字段名", example = "profit_margin")
                private String fieldName;

                /**
                 * 计算表达式
                 */
                @Schema(description = "计算表达式", example = "(income - expense) / income * 100")
                private String expression;

                /**
                 * 数据类型
                 */
                @Schema(description = "数据类型", example = "DECIMAL", allowableValues = {"STRING", "INTEGER", "DECIMAL", "DATE", "BOOLEAN"})
                private String dataType = "DECIMAL";

                /**
                 * 格式化规则
                 */
                @Schema(description = "格式化规则", example = "%.2f%%")
                private String formatPattern;
            }
        }

        @Data
        @Schema(description = "缓存配置")
        public static class CacheConfiguration implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 是否启用缓存
             */
            @Schema(description = "是否启用缓存", example = "true")
            private Boolean enabled = true;

            /**
             * 缓存时间（分钟）
             */
            @Schema(description = "缓存时间（分钟）", example = "30")
            private Integer cacheDurationMinutes = 30;

            /**
             * 缓存键前缀
             */
            @Schema(description = "缓存键前缀", example = "report_template")
            private String cacheKeyPrefix = "report_template";

            /**
             * 缓存策略
             */
            @Schema(description = "缓存策略", example = "LRU", allowableValues = {"LRU", "LFU", "FIFO", "TTL"})
            private String cacheStrategy = "LRU";
        }
    }

    /**
     * 布局配置
     */
    @Data
    @Schema(description = "布局配置")
    public static class LayoutConfiguration implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 布局类型
         */
        @Schema(description = "布局类型", example = "GRID", allowableValues = {"GRID", "FLEX", "ABSOLUTE", "FLOW"})
        private String layoutType = "GRID";

        /**
         * 网格配置
         */
        @Schema(description = "网格配置")
        private GridConfig gridConfig;

        /**
         * 组件列表
         */
        @Schema(description = "组件列表")
        private List<ComponentConfig> components;

        /**
         * 响应式配置
         */
        @Schema(description = "响应式配置")
        private ResponsiveConfig responsiveConfig;

        @Data
        @Schema(description = "网格配置")
        public static class GridConfig implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 列数
             */
            @Schema(description = "列数", example = "12")
            private Integer columns = 12;

            /**
             * 行高
             */
            @Schema(description = "行高", example = "60")
            private Integer rowHeight = 60;

            /**
             * 间距
             */
            @Schema(description = "间距", example = "10")
            private Integer gap = 10;

            /**
             * 边距
             */
            @Schema(description = "边距")
            private MarginConfig margin;

            @Data
            @Schema(description = "边距配置")
            public static class MarginConfig implements Serializable {

                @Serial
                private static final long serialVersionUID = 1L;

                /**
                 * 上边距
                 */
                @Schema(description = "上边距", example = "20")
                private Integer top = 20;

                /**
                 * 右边距
                 */
                @Schema(description = "右边距", example = "20")
                private Integer right = 20;

                /**
                 * 下边距
                 */
                @Schema(description = "下边距", example = "20")
                private Integer bottom = 20;

                /**
                 * 左边距
                 */
                @Schema(description = "左边距", example = "20")
                private Integer left = 20;
            }
        }

        @Data
        @Schema(description = "组件配置")
        public static class ComponentConfig implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 组件ID
             */
            @Schema(description = "组件ID", example = "overview_card")
            private String componentId;

            /**
             * 组件类型
             */
            @Schema(description = "组件类型", example = "CARD", allowableValues = {"CARD", "TABLE", "CHART", "TEXT", "IMAGE", "DIVIDER"})
            private String componentType;

            /**
             * 组件标题
             */
            @Schema(description = "组件标题", example = "财务概览")
            private String title;

            /**
             * 位置配置
             */
            @Schema(description = "位置配置")
            private PositionConfig position;

            /**
             * 样式配置
             */
            @Schema(description = "样式配置")
            private StyleConfig style;

            /**
             * 数据绑定
             */
            @Schema(description = "数据绑定", example = "main_data")
            private String dataBinding;

            /**
             * 组件属性
             */
            @Schema(description = "组件属性")
            private Map<String, Object> properties;

            @Data
            @Schema(description = "位置配置")
            public static class PositionConfig implements Serializable {

                @Serial
                private static final long serialVersionUID = 1L;

                /**
                 * X坐标
                 */
                @Schema(description = "X坐标", example = "0")
                private Integer x = 0;

                /**
                 * Y坐标
                 */
                @Schema(description = "Y坐标", example = "0")
                private Integer y = 0;

                /**
                 * 宽度
                 */
                @Schema(description = "宽度", example = "6")
                private Integer width = 6;

                /**
                 * 高度
                 */
                @Schema(description = "高度", example = "4")
                private Integer height = 4;

                /**
                 * Z轴层级
                 */
                @Schema(description = "Z轴层级", example = "1")
                private Integer zIndex = 1;
            }

            @Data
            @Schema(description = "样式配置")
            public static class StyleConfig implements Serializable {

                @Serial
                private static final long serialVersionUID = 1L;

                /**
                 * 背景色
                 */
                @Schema(description = "背景色", example = "#FFFFFF")
                private String backgroundColor = "#FFFFFF";

                /**
                 * 边框色
                 */
                @Schema(description = "边框色", example = "#E0E0E0")
                private String borderColor = "#E0E0E0";

                /**
                 * 边框宽度
                 */
                @Schema(description = "边框宽度", example = "1")
                private Integer borderWidth = 1;

                /**
                 * 圆角半径
                 */
                @Schema(description = "圆角半径", example = "4")
                private Integer borderRadius = 4;

                /**
                 * 阴影
                 */
                @Schema(description = "阴影", example = "0 2px 4px rgba(0,0,0,0.1)")
                private String boxShadow;

                /**
                 * 内边距
                 */
                @Schema(description = "内边距", example = "16")
                private Integer padding = 16;

                /**
                 * 字体大小
                 */
                @Schema(description = "字体大小", example = "14")
                private Integer fontSize = 14;

                /**
                 * 字体颜色
                 */
                @Schema(description = "字体颜色", example = "#333333")
                private String fontColor = "#333333";

                /**
                 * 自定义CSS
                 */
                @Schema(description = "自定义CSS")
                private String customCss;
            }
        }

        @Data
        @Schema(description = "响应式配置")
        public static class ResponsiveConfig implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 是否启用响应式
             */
            @Schema(description = "是否启用响应式", example = "true")
            private Boolean enabled = true;

            /**
             * 断点配置
             */
            @Schema(description = "断点配置")
            private Map<String, Integer> breakpoints;

            /**
             * 布局变化规则
             */
            @Schema(description = "布局变化规则")
            private Map<String, Object> layoutRules;
        }
    }

    /**
     * 图表配置
     */
    @Data
    @Schema(description = "图表配置")
    public static class ChartConfiguration implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 图表ID
         */
        @Schema(description = "图表ID", example = "trend_chart")
        private String chartId;

        /**
         * 图表类型
         */
        @Schema(description = "图表类型", example = "LINE", allowableValues = {"LINE", "BAR", "PIE", "AREA", "SCATTER", "RADAR", "GAUGE", "FUNNEL"})
        private String chartType;

        /**
         * 图表标题
         */
        @Schema(description = "图表标题", example = "收支趋势图")
        private String title;

        /**
         * 数据配置
         */
        @Schema(description = "数据配置")
        private ChartDataConfig dataConfig;

        /**
         * 样式配置
         */
        @Schema(description = "样式配置")
        private ChartStyleConfig styleConfig;

        /**
         * 交互配置
         */
        @Schema(description = "交互配置")
        private ChartInteractionConfig interactionConfig;

        @Data
        @Schema(description = "图表数据配置")
        public static class ChartDataConfig implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * X轴字段
             */
            @Schema(description = "X轴字段", example = "date")
            private String xAxisField;

            /**
             * Y轴字段
             */
            @Schema(description = "Y轴字段", example = "amount")
            private String yAxisField;

            /**
             * 系列字段
             */
            @Schema(description = "系列字段", example = "type")
            private String seriesField;

            /**
             * 数据过滤
             */
            @Schema(description = "数据过滤")
            private Map<String, Object> dataFilters;

            /**
             * 数据排序
             */
            @Schema(description = "数据排序")
            private List<String> dataSorting;
        }

        @Data
        @Schema(description = "图表样式配置")
        public static class ChartStyleConfig implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 颜色配置
             */
            @Schema(description = "颜色配置")
            private List<String> colors;

            /**
             * 主题
             */
            @Schema(description = "主题", example = "default")
            private String theme = "default";

            /**
             * 图例配置
             */
            @Schema(description = "图例配置")
            private Map<String, Object> legend;

            /**
             * 坐标轴配置
             */
            @Schema(description = "坐标轴配置")
            private Map<String, Object> axis;

            /**
             * 网格配置
             */
            @Schema(description = "网格配置")
            private Map<String, Object> grid;
        }

        @Data
        @Schema(description = "图表交互配置")
        public static class ChartInteractionConfig implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 是否可缩放
             */
            @Schema(description = "是否可缩放", example = "true")
            private Boolean zoomable = true;

            /**
             * 是否可拖拽
             */
            @Schema(description = "是否可拖拽", example = "false")
            private Boolean draggable = false;

            /**
             * 工具提示配置
             */
            @Schema(description = "工具提示配置")
            private Map<String, Object> tooltip;

            /**
             * 点击事件
             */
            @Schema(description = "点击事件")
            private Map<String, Object> clickEvents;
        }
    }

    /**
     * 过滤器配置
     */
    @Data
    @Schema(description = "过滤器配置")
    public static class FilterConfiguration implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 过滤器ID
         */
        @Schema(description = "过滤器ID", example = "date_filter")
        private String filterId;

        /**
         * 过滤器类型
         */
        @Schema(description = "过滤器类型", example = "DATE_RANGE", allowableValues = {"DATE_RANGE", "SELECT", "MULTI_SELECT", "INPUT", "NUMBER_RANGE", "CHECKBOX"})
        private String filterType;

        /**
         * 过滤器标签
         */
        @Schema(description = "过滤器标签", example = "日期范围")
        private String label;

        /**
         * 绑定字段
         */
        @Schema(description = "绑定字段", example = "transaction_date")
        private String bindField;

        /**
         * 默认值
         */
        @Schema(description = "默认值")
        private Object defaultValue;

        /**
         * 选项配置
         */
        @Schema(description = "选项配置")
        private List<FilterOption> options;

        /**
         * 验证规则
         */
        @Schema(description = "验证规则")
        private Map<String, Object> validationRules;

        /**
         * 是否必填
         */
        @Schema(description = "是否必填", example = "false")
        private Boolean required = false;

        @Data
        @Schema(description = "过滤器选项")
        public static class FilterOption implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 选项值
             */
            @Schema(description = "选项值", example = "INCOME")
            private String value;

            /**
             * 选项标签
             */
            @Schema(description = "选项标签", example = "收入")
            private String label;

            /**
             * 是否默认选中
             */
            @Schema(description = "是否默认选中", example = "false")
            private Boolean selected = false;
        }
    }

    /**
     * 导出配置
     */
    @Data
    @Schema(description = "导出配置")
    public static class ExportConfiguration implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 支持的导出格式
         */
        @Schema(description = "支持的导出格式", example = "[\"PDF\", \"EXCEL\", \"CSV\"]")
        private List<String> supportedFormats;

        /**
         * 默认导出格式
         */
        @Schema(description = "默认导出格式", example = "PDF")
        private String defaultFormat = "PDF";

        /**
         * 文件名模板
         */
        @Schema(description = "文件名模板", example = "{templateName}_{date}")
        private String fileNameTemplate;

        /**
         * PDF配置
         */
        @Schema(description = "PDF配置")
        private PdfExportConfig pdfConfig;

        /**
         * Excel配置
         */
        @Schema(description = "Excel配置")
        private ExcelExportConfig excelConfig;

        @Data
        @Schema(description = "PDF导出配置")
        public static class PdfExportConfig implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 页面边距
             */
            @Schema(description = "页面边距")
            private Map<String, Integer> margins;

            /**
             * 字体配置
             */
            @Schema(description = "字体配置")
            private Map<String, String> fonts;

            /**
             * 图片质量
             */
            @Schema(description = "图片质量", example = "HIGH", allowableValues = {"LOW", "MEDIUM", "HIGH"})
            private String imageQuality = "HIGH";
        }

        @Data
        @Schema(description = "Excel导出配置")
        public static class ExcelExportConfig implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 工作表名称
             */
            @Schema(description = "工作表名称", example = "财务报表")
            private String sheetName;

            /**
             * 是否包含图表
             */
            @Schema(description = "是否包含图表", example = "true")
            private Boolean includeCharts = true;

            /**
             * 单元格样式
             */
            @Schema(description = "单元格样式")
            private Map<String, Object> cellStyles;
        }
    }

    /**
     * 调度配置
     */
    @Data
    @Schema(description = "调度配置")
    public static class ScheduleConfiguration implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 是否启用调度
         */
        @Schema(description = "是否启用调度", example = "false")
        private Boolean enabled = false;

        /**
         * 调度类型
         */
        @Schema(description = "调度类型", example = "CRON", allowableValues = {"CRON", "INTERVAL", "ONCE"})
        private String scheduleType;

        /**
         * Cron表达式
         */
        @Schema(description = "Cron表达式", example = "0 0 9 1 * ?")
        private String cronExpression;

        /**
         * 间隔时间（分钟）
         */
        @Schema(description = "间隔时间（分钟）", example = "60")
        private Integer intervalMinutes;

        /**
         * 执行时间
         */
        @Schema(description = "执行时间", example = "2025-01-01 09:00:00")
        private String executeTime;

        /**
         * 接收人列表
         */
        @Schema(description = "接收人列表")
        private List<Long> recipients;

        /**
         * 发送方式
         */
        @Schema(description = "发送方式", example = "[\"EMAIL\", \"SYSTEM\"]")
        private List<String> deliveryMethods;

        /**
         * 邮件配置
         */
        @Schema(description = "邮件配置")
        private EmailConfig emailConfig;

        @Data
        @Schema(description = "邮件配置")
        public static class EmailConfig implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 邮件主题模板
             */
            @Schema(description = "邮件主题模板", example = "财务报表 - {date}")
            private String subjectTemplate;

            /**
             * 邮件内容模板
             */
            @Schema(description = "邮件内容模板")
            private String contentTemplate;

            /**
             * 是否包含附件
             */
            @Schema(description = "是否包含附件", example = "true")
            private Boolean includeAttachment = true;

            /**
             * 附件格式
             */
            @Schema(description = "附件格式", example = "PDF")
            private String attachmentFormat = "PDF";
        }
    }

    /**
     * 权限配置
     */
    @Data
    @Schema(description = "权限配置")
    public static class PermissionConfiguration implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 可查看角色
         */
        @Schema(description = "可查看角色", example = "[\"ADMIN\", \"ACCOUNTANT\"]")
        private List<String> viewRoles;

        /**
         * 可编辑角色
         */
        @Schema(description = "可编辑角色", example = "[\"ADMIN\"]")
        private List<String> editRoles;

        /**
         * 可执行角色
         */
        @Schema(description = "可执行角色", example = "[\"ADMIN\", \"ACCOUNTANT\"]")
        private List<String> executeRoles;

        /**
         * 可查看用户
         */
        @Schema(description = "可查看用户")
        private List<Long> viewUsers;

        /**
         * 可编辑用户
         */
        @Schema(description = "可编辑用户")
        private List<Long> editUsers;

        /**
         * 可执行用户
         */
        @Schema(description = "可执行用户")
        private List<Long> executeUsers;

        /**
         * 数据权限范围
         */
        @Schema(description = "数据权限范围", example = "GROUP", allowableValues = {"ALL", "GROUP", "DEPARTMENT", "SELF"})
        private String dataScope = "GROUP";

        /**
         * 敏感数据脱敏
         */
        @Schema(description = "敏感数据脱敏", example = "false")
        private Boolean dataMasking = false;

        /**
         * 脱敏规则
         */
        @Schema(description = "脱敏规则")
        private Map<String, String> maskingRules;
    }
}
