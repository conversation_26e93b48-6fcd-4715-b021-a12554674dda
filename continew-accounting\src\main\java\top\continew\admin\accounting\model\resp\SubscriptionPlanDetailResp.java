package top.continew.admin.accounting.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 订阅套餐详情响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "订阅套餐详情响应")
public class SubscriptionPlanDetailResp {

    /**
     * ID
     */
    @Schema(description = "ID", example = "1")
    private Long id;

    /**
     * 套餐名称
     */
    @Schema(description = "套餐名称", example = "专业版")
    private String name;

    /**
     * 套餐代码
     */
    @Schema(description = "套餐代码", example = "PRO")
    private String code;

    /**
     * 套餐描述
     */
    @Schema(description = "套餐描述", example = "适合小团队使用的专业版套餐")
    private String description;

    /**
     * 价格
     */
    @Schema(description = "价格", example = "9.99")
    private BigDecimal price;

    /**
     * 币种
     */
    @Schema(description = "币种", example = "USD")
    private String currency;

    /**
     * 计费周期
     */
    @Schema(description = "计费周期", example = "MONTHLY")
    private String billingCycle;

    /**
     * 功能特性
     */
    @Schema(description = "功能特性")
    private Map<String, Object> features;

    /**
     * 使用限制
     */
    @Schema(description = "使用限制")
    private Map<String, Object> limits;

    /**
     * 试用天数
     */
    @Schema(description = "试用天数", example = "7")
    private Integer trialDays;

    /**
     * 是否热门
     */
    @Schema(description = "是否热门", example = "false")
    private Boolean isPopular;

    /**
     * 排序
     */
    @Schema(description = "排序", example = "1")
    private Integer sortOrder;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "1")
    private Integer status;

    /**
     * 订阅数量
     */
    @Schema(description = "订阅数量", example = "150")
    private Long subscriptionCount;

    /**
     * 活跃订阅数量
     */
    @Schema(description = "活跃订阅数量", example = "120")
    private Long activeSubscriptionCount;

    /**
     * 总收入
     */
    @Schema(description = "总收入", example = "1500.00")
    private BigDecimal totalRevenue;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-01-01 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2025-01-01 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
