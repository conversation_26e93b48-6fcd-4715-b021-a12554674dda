package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import top.continew.admin.accounting.enums.FileStorageTypeEnum;
import top.continew.admin.common.base.model.entity.BaseDO;
import top.continew.admin.common.enums.DisEnableStatusEnum;

import java.io.Serial;

/**
 * 文件存储配置实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@TableName("acc_file_storage_config")
public class FileStorageConfigDO extends BaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 配置名称
     */
    private String configName;

    /**
     * 配置代码
     */
    private String configCode;

    /**
     * 存储类型
     */
    private FileStorageTypeEnum storageType;

    /**
     * 访问密钥ID
     */
    private String accessKeyId;

    /**
     * 访问密钥Secret
     */
    private String accessKeySecret;

    /**
     * 端点地址
     */
    private String endpoint;

    /**
     * 存储桶名称
     */
    private String bucketName;

    /**
     * 存储区域
     */
    private String region;

    /**
     * 自定义域名
     */
    private String customDomain;

    /**
     * CDN域名
     */
    private String cdnDomain;

    /**
     * 是否启用HTTPS
     */
    private Boolean enableHttps;

    /**
     * 是否启用CDN
     */
    private Boolean enableCdn;

    /**
     * 是否为默认配置
     */
    private Boolean isDefault;

    /**
     * 最大文件大小（字节）
     */
    private Long maxFileSize;

    /**
     * 允许的文件类型
     */
    private String allowedFileTypes;

    /**
     * 禁止的文件类型
     */
    private String forbiddenFileTypes;

    /**
     * 上传路径前缀
     */
    private String pathPrefix;

    /**
     * 是否启用缩略图
     */
    private Boolean enableThumbnail;

    /**
     * 缩略图配置
     */
    private String thumbnailConfig;

    /**
     * 是否启用水印
     */
    private Boolean enableWatermark;

    /**
     * 水印配置
     */
    private String watermarkConfig;

    /**
     * 是否启用安全扫描
     */
    private Boolean enableSecurityScan;

    /**
     * 安全扫描配置
     */
    private String securityScanConfig;

    /**
     * 配置状态
     */
    private DisEnableStatusEnum status;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 配置描述
     */
    private String description;

    /**
     * 扩展配置（JSON格式）
     */
    private String extendConfig;

    /**
     * 群组ID
     */
    private Long groupId;

}
