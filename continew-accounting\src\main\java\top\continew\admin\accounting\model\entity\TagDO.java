package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.data.mybatis.plus.base.BaseEntity;

/**
 * 标签实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("acc_tag")
@Schema(description = "标签信息")
public class TagDO extends BaseEntity {

    /**
     * 群组ID
     */
    @Schema(description = "群组ID")
    private Long groupId;

    /**
     * 标签名称
     */
    @Schema(description = "标签名称")
    private String name;

    /**
     * 标签颜色
     */
    @Schema(description = "标签颜色")
    private String color;

    /**
     * 标签图标
     */
    @Schema(description = "标签图标")
    private String icon;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;

    /**
     * 是否为系统标签
     */
    @Schema(description = "是否为系统标签")
    private Boolean isSystem;

    /**
     * 是否为默认标签
     */
    @Schema(description = "是否为默认标签")
    private Boolean isDefault;
}
