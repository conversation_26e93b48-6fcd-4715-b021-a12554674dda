package top.continew.admin.api.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * API密钥响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "API密钥响应")
public class ApiKeyResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Schema(description = "API密钥ID", example = "1")
    private Long id;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", example = "1")
    private Long userId;

    /**
     * 应用名称
     */
    @Schema(description = "应用名称", example = "我的记账应用")
    private String appName;

    /**
     * API密钥
     */
    @Schema(description = "API密钥", example = "ak_1234567890abcdef")
    private String apiKey;

    /**
     * API密钥（仅在创建时返回）
     */
    @Schema(description = "API密钥（仅在创建时返回）", example = "sk_abcdef1234567890")
    private String apiSecret;

    /**
     * 描述
     */
    @Schema(description = "API密钥描述", example = "用于移动端应用的API访问")
    private String description;

    /**
     * 权限范围
     */
    @Schema(description = "权限范围列表")
    private List<String> scopes;

    /**
     * IP白名单
     */
    @Schema(description = "IP白名单")
    private List<String> ipWhitelist;

    /**
     * 速率限制
     */
    @Schema(description = "速率限制（每分钟请求数）", example = "100")
    private Integer rateLimit;

    /**
     * 是否激活
     */
    @Schema(description = "是否激活", example = "true")
    private Boolean isActive;

    /**
     * 过期时间
     */
    @Schema(description = "过期时间")
    private LocalDateTime expiresAt;

    /**
     * 最后使用时间
     */
    @Schema(description = "最后使用时间")
    private LocalDateTime lastUsedAt;

    /**
     * 使用次数
     */
    @Schema(description = "使用次数", example = "1250")
    private Long usageCount;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 验证结果
     */
    @Data
    @Schema(description = "验证结果")
    public static class ValidationResult implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 是否有效
         */
        @Schema(description = "是否有效", example = "true")
        private Boolean isValid;

        /**
         * 用户ID
         */
        @Schema(description = "用户ID", example = "1")
        private Long userId;

        /**
         * 应用名称
         */
        @Schema(description = "应用名称", example = "我的记账应用")
        private String appName;

        /**
         * 权限范围
         */
        @Schema(description = "权限范围列表")
        private List<String> scopes;

        /**
         * 剩余请求数
         */
        @Schema(description = "剩余请求数", example = "95")
        private Integer remainingRequests;

        /**
         * 重置时间
         */
        @Schema(description = "重置时间")
        private LocalDateTime resetTime;

        /**
         * 错误信息
         */
        @Schema(description = "错误信息")
        private String errorMessage;
    }

    /**
     * 使用统计
     */
    @Data
    @Schema(description = "使用统计")
    public static class UsageStatistics implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 总调用次数
         */
        @Schema(description = "总调用次数", example = "1250")
        private Long totalCalls;

        /**
         * 成功调用次数
         */
        @Schema(description = "成功调用次数", example = "1200")
        private Long successCalls;

        /**
         * 失败调用次数
         */
        @Schema(description = "失败调用次数", example = "50")
        private Long failedCalls;

        /**
         * 平均响应时间（毫秒）
         */
        @Schema(description = "平均响应时间（毫秒）", example = "150")
        private Double averageResponseTime;

        /**
         * 每日调用统计
         */
        @Schema(description = "每日调用统计")
        private List<DailyUsage> dailyUsage;

        /**
         * 热门接口
         */
        @Schema(description = "热门接口")
        private List<PopularEndpoint> popularEndpoints;

        /**
         * 每日使用量
         */
        @Data
        @Schema(description = "每日使用量")
        public static class DailyUsage implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 日期
             */
            @Schema(description = "日期", example = "2024-01-01")
            private String date;

            /**
             * 调用次数
             */
            @Schema(description = "调用次数", example = "150")
            private Long calls;

            /**
             * 成功次数
             */
            @Schema(description = "成功次数", example = "145")
            private Long success;

            /**
             * 失败次数
             */
            @Schema(description = "失败次数", example = "5")
            private Long failed;
        }

        /**
         * 热门接口
         */
        @Data
        @Schema(description = "热门接口")
        public static class PopularEndpoint implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 接口路径
             */
            @Schema(description = "接口路径", example = "/api/v1/transactions")
            private String path;

            /**
             * 请求方法
             */
            @Schema(description = "请求方法", example = "GET")
            private String method;

            /**
             * 调用次数
             */
            @Schema(description = "调用次数", example = "500")
            private Long calls;

            /**
             * 占比
             */
            @Schema(description = "占比", example = "0.4")
            private Double percentage;
        }
    }

    /**
     * 调用日志
     */
    @Data
    @Schema(description = "调用日志")
    public static class CallLog implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 日志ID
         */
        @Schema(description = "日志ID", example = "1")
        private Long id;

        /**
         * 请求路径
         */
        @Schema(description = "请求路径", example = "/api/v1/transactions")
        private String requestPath;

        /**
         * 请求方法
         */
        @Schema(description = "请求方法", example = "GET")
        private String requestMethod;

        /**
         * 客户端IP
         */
        @Schema(description = "客户端IP", example = "*************")
        private String clientIp;

        /**
         * 响应状态码
         */
        @Schema(description = "响应状态码", example = "200")
        private Integer responseStatus;

        /**
         * 响应时间（毫秒）
         */
        @Schema(description = "响应时间（毫秒）", example = "150")
        private Long responseTime;

        /**
         * 错误信息
         */
        @Schema(description = "错误信息")
        private String errorMessage;

        /**
         * 调用时间
         */
        @Schema(description = "调用时间")
        private LocalDateTime callTime;
    }

    /**
     * 权限范围
     */
    @Data
    @Schema(description = "权限范围")
    public static class Scope implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 范围代码
         */
        @Schema(description = "范围代码", example = "groups:read")
        private String code;

        /**
         * 范围名称
         */
        @Schema(description = "范围名称", example = "读取群组信息")
        private String name;

        /**
         * 范围描述
         */
        @Schema(description = "范围描述", example = "允许读取用户所属的群组信息")
        private String description;

        /**
         * 分类
         */
        @Schema(description = "分类", example = "群组管理")
        private String category;
    }
}
