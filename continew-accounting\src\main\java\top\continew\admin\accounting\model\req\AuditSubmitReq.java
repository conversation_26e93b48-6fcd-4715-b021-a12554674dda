package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 审核提交请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "审核提交请求")
public class AuditSubmitReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    @NotNull(message = "群组ID不能为空")
    private Long groupId;

    /**
     * 交易ID列表
     */
    @Schema(description = "交易ID列表", example = "[1, 2, 3]")
    @NotEmpty(message = "交易ID列表不能为空")
    private List<Long> transactionIds;

    /**
     * 审核类型
     */
    @Schema(description = "审核类型", example = "EXPENSE_AUDIT", allowableValues = {"EXPENSE_AUDIT", "INCOME_AUDIT", "TRANSFER_AUDIT", "BUDGET_AUDIT", "BATCH_AUDIT"})
    @NotBlank(message = "审核类型不能为空")
    private String auditType;

    /**
     * 审核标题
     */
    @Schema(description = "审核标题", example = "2025年1月餐饮费用审核")
    @NotBlank(message = "审核标题不能为空")
    @Size(max = 200, message = "审核标题长度不能超过200个字符")
    private String auditTitle;

    /**
     * 审核描述
     */
    @Schema(description = "审核描述", example = "请审核1月份的餐饮消费记录")
    @Size(max = 1000, message = "审核描述长度不能超过1000个字符")
    private String auditDescription;

    /**
     * 优先级
     */
    @Schema(description = "优先级", example = "NORMAL", allowableValues = {"LOW", "NORMAL", "HIGH", "URGENT"})
    private String priority = "NORMAL";

    /**
     * 审核人ID列表
     */
    @Schema(description = "审核人ID列表", example = "[1, 2]")
    @NotEmpty(message = "审核人ID列表不能为空")
    private List<Long> auditorIds;

    /**
     * 审核截止时间
     */
    @Schema(description = "审核截止时间", example = "2025-01-15")
    private String deadline;

    /**
     * 是否需要全部审核人通过
     */
    @Schema(description = "是否需要全部审核人通过", example = "false")
    private Boolean requireAllApproval = false;

    /**
     * 审核规则
     */
    @Schema(description = "审核规则")
    private AuditRules auditRules;

    /**
     * 通知设置
     */
    @Schema(description = "通知设置")
    private NotificationSettings notificationSettings;

    /**
     * 附件列表
     */
    @Schema(description = "附件列表")
    private List<String> attachments;

    /**
     * 扩展属性
     */
    @Schema(description = "扩展属性")
    private Map<String, Object> attributes;

    /**
     * 审核规则
     */
    @Data
    @Schema(description = "审核规则")
    public static class AuditRules implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 是否允许部分通过
         */
        @Schema(description = "是否允许部分通过", example = "true")
        private Boolean allowPartialApproval = true;

        /**
         * 是否允许修改
         */
        @Schema(description = "是否允许修改", example = "true")
        private Boolean allowModification = true;

        /**
         * 是否允许退回
         */
        @Schema(description = "是否允许退回", example = "true")
        private Boolean allowRejection = true;

        /**
         * 是否需要审核意见
         */
        @Schema(description = "是否需要审核意见", example = "false")
        private Boolean requireComment = false;

        /**
         * 自动审核规则
         */
        @Schema(description = "自动审核规则")
        private Map<String, Object> autoAuditRules;

        /**
         * 审核流程
         */
        @Schema(description = "审核流程", example = "SEQUENTIAL", allowableValues = {"SEQUENTIAL", "PARALLEL", "CONDITIONAL"})
        private String auditFlow = "PARALLEL";

        /**
         * 超时处理
         */
        @Schema(description = "超时处理", example = "AUTO_APPROVE", allowableValues = {"AUTO_APPROVE", "AUTO_REJECT", "ESCALATE", "NOTIFY_ONLY"})
        private String timeoutAction = "NOTIFY_ONLY";

        /**
         * 升级规则
         */
        @Schema(description = "升级规则")
        private List<EscalationRule> escalationRules;

        @Data
        @Schema(description = "升级规则")
        public static class EscalationRule implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 升级条件
             */
            @Schema(description = "升级条件", example = "TIMEOUT")
            private String condition;

            /**
             * 升级时间（小时）
             */
            @Schema(description = "升级时间（小时）", example = "24")
            private Integer escalationHours;

            /**
             * 升级到的审核人ID列表
             */
            @Schema(description = "升级到的审核人ID列表")
            private List<Long> escalationAuditorIds;

            /**
             * 升级消息
             */
            @Schema(description = "升级消息", example = "审核超时，已升级处理")
            private String escalationMessage;
        }
    }

    /**
     * 通知设置
     */
    @Data
    @Schema(description = "通知设置")
    public static class NotificationSettings implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 是否发送提交通知
         */
        @Schema(description = "是否发送提交通知", example = "true")
        private Boolean sendSubmitNotification = true;

        /**
         * 是否发送审核通知
         */
        @Schema(description = "是否发送审核通知", example = "true")
        private Boolean sendAuditNotification = true;

        /**
         * 是否发送完成通知
         */
        @Schema(description = "是否发送完成通知", example = "true")
        private Boolean sendCompletionNotification = true;

        /**
         * 是否发送超时提醒
         */
        @Schema(description = "是否发送超时提醒", example = "true")
        private Boolean sendTimeoutReminder = true;

        /**
         * 通知方式
         */
        @Schema(description = "通知方式", example = "[\"EMAIL\", \"SMS\", \"PUSH\"]")
        private List<String> notificationMethods;

        /**
         * 提醒间隔（小时）
         */
        @Schema(description = "提醒间隔（小时）", example = "24")
        private Integer reminderInterval = 24;

        /**
         * 最大提醒次数
         */
        @Schema(description = "最大提醒次数", example = "3")
        private Integer maxReminders = 3;

        /**
         * 额外通知人
         */
        @Schema(description = "额外通知人")
        private List<Long> additionalRecipients;
    }
}
