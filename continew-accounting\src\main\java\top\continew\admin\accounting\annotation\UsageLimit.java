package top.continew.admin.accounting.annotation;

import java.lang.annotation.*;

/**
 * 使用限制注解
 * 用于标记需要检查使用限制的API接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface UsageLimit {

    /**
     * 限制类型
     * 对应订阅套餐中的限制字段名
     * 例如：maxTransactionsPerMonth, maxOcrPerMonth, maxApiCallsPerMonth 等
     */
    String limitType();

    /**
     * 超出限制时的错误消息
     * 如果为空，则使用默认消息
     */
    String message() default "";

    /**
     * 是否启用限制检查
     * 默认启用
     */
    boolean enabled() default true;

    /**
     * 检查模式
     * STRICT: 严格模式，超出限制立即拒绝
     * WARNING: 警告模式，超出限制仅记录警告但允许通过
     */
    CheckMode mode() default CheckMode.STRICT;

    /**
     * 检查模式枚举
     */
    enum CheckMode {
        /**
         * 严格模式：超出限制立即拒绝请求
         */
        STRICT,
        
        /**
         * 警告模式：超出限制仅记录警告但允许通过
         */
        WARNING
    }
}
