package top.continew.admin.accounting.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.accounting.model.entity.ZapierConfigDO;
import top.continew.admin.accounting.model.entity.ZapierLogDO;
import top.continew.admin.accounting.service.ZapierConfigService;
import top.continew.admin.accounting.service.ZapierLogService;
import top.continew.admin.accounting.service.ZapierWebhookService;
import top.continew.starter.core.util.validate.CheckUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Zapier Webhook执行服务实现
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ZapierWebhookServiceImpl implements ZapierWebhookService {

    private final ZapierConfigService zapierConfigService;
    private final ZapierLogService zapierLogService;

    // 执行队列统计
    private final AtomicInteger pendingExecutions = new AtomicInteger(0);
    private final AtomicInteger activeExecutions = new AtomicInteger(0);
    private final Map<String, AtomicInteger> executionCounters = new ConcurrentHashMap<>();

    @Override
    public void triggerEvent(Long groupId, String triggerType, String eventType, Long businessId, String businessType, Map<String, Object> data) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");
        CheckUtils.throwIfBlank(triggerType, "触发器类型不能为空");
        CheckUtils.throwIfBlank(eventType, "事件类型不能为空");

        log.info("触发Zapier事件: groupId={}, triggerType={}, eventType={}, businessId={}", 
                groupId, triggerType, eventType, businessId);

        // 获取启用的配置
        List<ZapierConfigDO> configs = zapierConfigService.listEnabledConfigs(groupId, triggerType);
        if (CollUtil.isEmpty(configs)) {
            log.debug("未找到启用的Zapier配置: groupId={}, triggerType={}", groupId, triggerType);
            return;
        }

        // 执行所有匹配的配置
        for (ZapierConfigDO config : configs) {
            try {
                executeWebhook(config, eventType, businessId, businessType, data);
            } catch (Exception e) {
                log.error("执行Zapier配置失败: configId={}, error={}", config.getId(), e.getMessage(), e);
            }
        }
    }

    @Override
    @Async("zapierExecutor")
    public void triggerEventAsync(Long groupId, String triggerType, String eventType, Long businessId, String businessType, Map<String, Object> data) {
        pendingExecutions.incrementAndGet();
        try {
            triggerEvent(groupId, triggerType, eventType, businessId, businessType, data);
        } finally {
            pendingExecutions.decrementAndGet();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void retryExecution(Long logId) {
        CheckUtils.throwIfNull(logId, "日志ID不能为空");

        ZapierLogDO originalLog = zapierLogService.getById(logId);
        CheckUtils.throwIfNull(originalLog, "日志不存在");
        CheckUtils.throwIf(!"FAILED".equals(originalLog.getStatus()), "只能重试失败的执行");

        ZapierConfigDO config = zapierConfigService.getById(originalLog.getConfigId());
        CheckUtils.throwIfNull(config, "配置不存在");
        CheckUtils.throwIf(!config.getEnabled(), "配置未启用");

        // 检查重试次数限制
        if (originalLog.getRetryCount() >= config.getMaxRetries()) {
            log.warn("重试次数已达上限: logId={}, retryCount={}, maxRetries={}", 
                    logId, originalLog.getRetryCount(), config.getMaxRetries());
            return;
        }

        log.info("开始重试Zapier执行: logId={}, configId={}, retryCount={}", 
                logId, config.getId(), originalLog.getRetryCount() + 1);

        // 解析原始请求数据
        Map<String, Object> requestData = null;
        if (StrUtil.isNotBlank(originalLog.getRequestData())) {
            try {
                requestData = JSONUtil.toBean(originalLog.getRequestData(), Map.class);
            } catch (Exception e) {
                log.error("解析原始请求数据失败: logId={}, error={}", logId, e.getMessage());
                requestData = new HashMap<>();
            }
        }

        // 执行重试
        executeWebhookRetry(config, originalLog, requestData);
    }

    @Override
    public void batchRetryFailures(Long configId, Integer hours) {
        CheckUtils.throwIfNull(configId, "配置ID不能为空");
        CheckUtils.throwIfNull(hours, "时间范围不能为空");

        List<ZapierLogDO> failedLogs = zapierLogService.listFailedLogs(configId, hours);
        if (CollUtil.isEmpty(failedLogs)) {
            log.info("未找到需要重试的失败日志: configId={}, hours={}", configId, hours);
            return;
        }

        log.info("开始批量重试失败执行: configId={}, failedCount={}", configId, failedLogs.size());

        for (ZapierLogDO log : failedLogs) {
            try {
                retryExecution(log.getId());
            } catch (Exception e) {
                log.error("批量重试失败: logId={}, error={}", log.getId(), e.getMessage());
            }
        }
    }

    @Override
    @Scheduled(fixedDelay = 300000) // 每5分钟执行一次
    public void processScheduledRetries() {
        log.debug("开始处理定时重试任务");

        try {
            // 获取所有启用的配置
            List<ZapierConfigDO> configs = zapierConfigService.listAllEnabledConfigs();
            
            for (ZapierConfigDO config : configs) {
                try {
                    // 查找需要重试的失败日志
                    List<ZapierLogDO> retryLogs = zapierLogService.listFailedLogs(config.getId(), 24);
                    
                    for (ZapierLogDO log : retryLogs) {
                        // 检查是否到了重试时间
                        LocalDateTime nextRetryTime = log.getExecutedAt().plusMinutes(config.getRetryInterval());
                        if (LocalDateTime.now().isAfter(nextRetryTime) && log.getRetryCount() < config.getMaxRetries()) {
                            retryExecution(log.getId());
                        }
                    }
                } catch (Exception e) {
                    log.error("处理配置的定时重试失败: configId={}, error={}", config.getId(), e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("处理定时重试任务失败: error={}", e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> validateWebhookUrl(String webhookUrl) {
        CheckUtils.throwIfBlank(webhookUrl, "Webhook URL不能为空");

        Map<String, Object> result = new HashMap<>();
        long startTime = System.currentTimeMillis();

        try {
            // 发送测试请求
            Map<String, Object> testData = Map.of(
                    "test", true,
                    "timestamp", System.currentTimeMillis(),
                    "source", "ContiNew-Zapier-Validation"
            );

            HttpResponse response = HttpRequest.post(webhookUrl)
                    .header("Content-Type", "application/json")
                    .header("User-Agent", "ContiNew-Zapier/1.0")
                    .body(JSONUtil.toJsonStr(testData))
                    .timeout(10000)
                    .execute();

            long responseTime = System.currentTimeMillis() - startTime;

            result.put("valid", true);
            result.put("httpStatus", response.getStatus());
            result.put("responseTime", responseTime);
            result.put("responseBody", response.body());
            result.put("message", "Webhook URL验证成功");

            log.info("Webhook URL验证成功: url={}, status={}, time={}ms", 
                    webhookUrl, response.getStatus(), responseTime);

        } catch (Exception e) {
            long responseTime = System.currentTimeMillis() - startTime;

            result.put("valid", false);
            result.put("error", e.getMessage());
            result.put("responseTime", responseTime);
            result.put("message", "Webhook URL验证失败: " + e.getMessage());

            log.error("Webhook URL验证失败: url={}, error={}", webhookUrl, e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> getExecutionStatistics(Long groupId) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");

        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusDays(30);

        Map<String, Object> stats = zapierLogService.getGroupExecutionStats(groupId, startTime, endTime);
        
        // 添加实时统计
        stats.put("pendingExecutions", pendingExecutions.get());
        stats.put("activeExecutions", activeExecutions.get());
        stats.put("queueStatus", getExecutionQueueStatus());

        return stats;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer cleanupExpiredLogs(Integer retentionDays) {
        CheckUtils.throwIfNull(retentionDays, "保留天数不能为空");
        CheckUtils.throwIf(retentionDays < 1, "保留天数必须大于0");

        Integer deletedCount = zapierLogService.deleteExpiredLogs(retentionDays);
        
        log.info("清理过期Zapier日志完成: retentionDays={}, deletedCount={}", retentionDays, deletedCount);
        
        return deletedCount;
    }

    @Override
    public Map<String, Object> monitorWebhookHealth(Long groupId) {
        CheckUtils.throwIfNull(groupId, "群组ID不能为空");

        Map<String, Object> healthReport = new HashMap<>();
        
        // 获取配置统计
        Map<String, Object> configStats = zapierConfigService.getConfigStats(groupId);
        healthReport.put("configStats", configStats);

        // 获取错误配置
        List<ZapierConfigDO> errorConfigs = zapierConfigService.getErrorConfigs(groupId, 24);
        healthReport.put("errorConfigs", errorConfigs.size());

        // 获取长时间未触发的配置
        List<ZapierConfigDO> inactiveConfigs = zapierConfigService.getInactiveConfigs(groupId, 7);
        healthReport.put("inactiveConfigs", inactiveConfigs.size());

        // 获取最近24小时的异常日志
        List<ZapierLogDO> anomalyLogs = zapierLogService.listAnomalyLogs(groupId, 24);
        healthReport.put("anomalyLogs", anomalyLogs.size());

        // 计算健康评分
        double healthScore = calculateHealthScore(configStats, errorConfigs.size(), inactiveConfigs.size(), anomalyLogs.size());
        healthReport.put("healthScore", healthScore);
        healthReport.put("healthLevel", getHealthLevel(healthScore));

        return healthReport;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pauseConfig(Long configId, String reason) {
        CheckUtils.throwIfNull(configId, "配置ID不能为空");
        CheckUtils.throwIfBlank(reason, "暂停原因不能为空");

        zapierConfigService.updateStatus(configId, "PAUSED");
        zapierConfigService.updateError(configId, "手动暂停: " + reason, LocalDateTime.now());

        log.info("暂停Zapier配置: configId={}, reason={}", configId, reason);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resumeConfig(Long configId) {
        CheckUtils.throwIfNull(configId, "配置ID不能为空");

        zapierConfigService.updateStatus(configId, "ACTIVE");

        log.info("恢复Zapier配置: configId={}", configId);
    }

    @Override
    public Map<String, Object> getExecutionQueueStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("pendingExecutions", pendingExecutions.get());
        status.put("activeExecutions", activeExecutions.get());
        status.put("totalExecutions", executionCounters.values().stream().mapToInt(AtomicInteger::get).sum());
        status.put("executionCounters", executionCounters.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().get())));
        return status;
    }

    /**
     * 执行Webhook
     */
    private void executeWebhook(ZapierConfigDO config, String eventType, Long businessId, String businessType, Map<String, Object> data) {
        activeExecutions.incrementAndGet();
        String counterKey = config.getGroupId() + ":" + config.getTriggerType();
        executionCounters.computeIfAbsent(counterKey, k -> new AtomicInteger(0)).incrementAndGet();

        try {
            Map<String, Object> result = zapierConfigService.triggerExecution(config.getId(), eventType, businessId, data);
            
            if (Boolean.TRUE.equals(result.get("success"))) {
                log.debug("Zapier配置执行成功: configId={}, executionTime={}ms", 
                        config.getId(), result.get("executionTime"));
            } else {
                log.warn("Zapier配置执行失败: configId={}, message={}", 
                        config.getId(), result.get("message"));
            }
        } finally {
            activeExecutions.decrementAndGet();
        }
    }

    /**
     * 执行Webhook重试
     */
    private void executeWebhookRetry(ZapierConfigDO config, ZapierLogDO originalLog, Map<String, Object> requestData) {
        activeExecutions.incrementAndGet();

        try {
            long startTime = System.currentTimeMillis();
            String status = "SUCCESS";
            String errorMessage = null;
            String errorCode = null;
            Integer httpStatus = null;
            Map<String, Object> responseData = null;

            try {
                // 发送重试请求
                HttpRequest request = HttpRequest.post(config.getWebhookUrl())
                        .header("Content-Type", "application/json")
                        .header("User-Agent", "ContiNew-Zapier/1.0")
                        .header("X-Retry-Count", String.valueOf(originalLog.getRetryCount() + 1))
                        .body(JSONUtil.toJsonStr(requestData))
                        .timeout(config.getTimeoutSeconds() * 1000);

                // 添加自定义请求头
                if (StrUtil.isNotBlank(config.getHeaders())) {
                    Map<String, String> headers = JSONUtil.toBean(config.getHeaders(), Map.class);
                    headers.forEach(request::header);
                }

                HttpResponse response = request.execute();
                httpStatus = response.getStatus();

                if (httpStatus >= 200 && httpStatus < 300) {
                    responseData = Map.of("body", response.body(), "headers", response.headers());
                } else {
                    status = "FAILED";
                    errorMessage = "HTTP错误: " + httpStatus;
                    errorCode = "HTTP_ERROR";
                }

            } catch (Exception e) {
                status = "FAILED";
                errorMessage = e.getMessage();
                errorCode = "EXECUTION_ERROR";
            }

            long executionTime = System.currentTimeMillis() - startTime;

            // 记录重试日志
            zapierLogService.recordRetryLog(
                    originalLog.getId(),
                    config.getId(),
                    config.getGroupId(),
                    config.getTriggerType(),
                    originalLog.getEventType(),
                    originalLog.getBusinessId(),
                    originalLog.getBusinessType(),
                    requestData,
                    responseData,
                    httpStatus,
                    status,
                    executionTime,
                    errorMessage,
                    errorCode,
                    originalLog.getRetryCount() + 1
            );

            // 更新配置统计
            if ("SUCCESS".equals(status)) {
                zapierConfigService.updateTriggerStats(
                        config.getId(),
                        config.getTriggerCount() + 1,
                        config.getSuccessCount() + 1,
                        config.getFailureCount(),
                        LocalDateTime.now()
                );
            } else {
                zapierConfigService.updateTriggerStats(
                        config.getId(),
                        config.getTriggerCount() + 1,
                        config.getSuccessCount(),
                        config.getFailureCount() + 1,
                        LocalDateTime.now()
                );
                zapierConfigService.updateError(config.getId(), errorMessage, LocalDateTime.now());
            }

            log.info("Zapier重试执行完成: configId={}, originalLogId={}, status={}, executionTime={}ms",
                    config.getId(), originalLog.getId(), status, executionTime);

        } finally {
            activeExecutions.decrementAndGet();
        }
    }

    /**
     * 计算健康评分
     */
    private double calculateHealthScore(Map<String, Object> configStats, int errorConfigs, int inactiveConfigs, int anomalyLogs) {
        double score = 100.0;

        // 根据成功率扣分
        Object successRateObj = configStats.get("success_rate");
        if (successRateObj instanceof Number) {
            double successRate = ((Number) successRateObj).doubleValue();
            score -= (100 - successRate) * 0.5;
        }

        // 根据错误配置数量扣分
        score -= errorConfigs * 5;

        // 根据非活跃配置数量扣分
        score -= inactiveConfigs * 3;

        // 根据异常日志数量扣分
        score -= anomalyLogs * 2;

        return Math.max(0, Math.min(100, score));
    }

    /**
     * 获取健康等级
     */
    private String getHealthLevel(double healthScore) {
        if (healthScore >= 90) {
            return "EXCELLENT";
        } else if (healthScore >= 80) {
            return "GOOD";
        } else if (healthScore >= 70) {
            return "FAIR";
        } else if (healthScore >= 60) {
            return "POOR";
        } else {
            return "CRITICAL";
        }
    }
}
