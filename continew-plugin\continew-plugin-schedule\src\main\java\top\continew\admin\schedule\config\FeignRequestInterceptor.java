/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.schedule.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import top.continew.admin.schedule.api.JobClient;
import top.continew.admin.schedule.constant.JobConstants;

/**
 * Feign 请求拦截器
 *
 * <AUTHOR>
 * @since 2025/3/28 21:17
 */
@Component
@RequiredArgsConstructor
public class FeignRequestInterceptor implements RequestInterceptor {

    private final JobClient jobClient;

    @Value("${snail-job.namespace}")
    private String namespace;

    @Override
    public void apply(RequestTemplate requestTemplate) {
        requestTemplate.header(JobConstants.NAMESPACE_ID_HEADER, namespace);
        requestTemplate.header(JobConstants.AUTH_TOKEN_HEADER, jobClient.getToken());
    }
}