<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.accounting.mapper.SubscriptionPlanMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="top.continew.admin.accounting.model.entity.SubscriptionPlanDO">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="description" property="description" />
        <result column="price" property="price" />
        <result column="currency" property="currency" />
        <result column="billing_cycle" property="billingCycle" />
        <result column="features" property="features" typeHandler="top.continew.starter.data.mybatis.handler.JsonTypeHandler" />
        <result column="limits" property="limits" typeHandler="top.continew.starter.data.mybatis.handler.JsonTypeHandler" />
        <result column="trial_days" property="trialDays" />
        <result column="is_popular" property="isPopular" />
        <result column="sort_order" property="sortOrder" />
        <result column="status" property="status" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, name, code, description, price, currency, billing_cycle, features, limits,
        trial_days, is_popular, sort_order, status, create_user, create_time, update_user, update_time
    </sql>

    <!-- 查询启用的套餐 -->
    <select id="selectEnabledPlans" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM acc_subscription_plan 
        WHERE status = 1 
        ORDER BY sort_order ASC, create_time ASC
    </select>

    <!-- 根据代码查询套餐 -->
    <select id="selectByCode" parameterType="string" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM acc_subscription_plan 
        WHERE code = #{code} 
        AND status = 1
    </select>

    <!-- 查询热门套餐 -->
    <select id="selectPopularPlans" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM acc_subscription_plan 
        WHERE is_popular = 1 
        AND status = 1 
        ORDER BY sort_order ASC, create_time ASC
    </select>

    <!-- 更新状态 -->
    <update id="updateStatus">
        UPDATE acc_subscription_plan 
        SET status = #{status}, update_time = NOW() 
        WHERE id = #{id}
    </update>

    <!-- 批量更新排序 -->
    <update id="batchUpdateSortOrder" parameterType="list">
        UPDATE acc_subscription_plan
        SET sort_order =
        <foreach collection="list" item="item" open="CASE id " close=" END" separator=" ">
            WHEN #{item.id} THEN #{item.sortOrder}
        </foreach>,
        update_time = NOW()
        WHERE id IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item.id}
        </foreach>
    </update>

    <!-- 统计套餐订阅数量 -->
    <select id="countSubscriptions" parameterType="long" resultType="long">
        SELECT COUNT(*) 
        FROM acc_subscription 
        WHERE plan_id = #{planId}
    </select>

    <!-- 统计套餐活跃订阅数量 -->
    <select id="countActiveSubscriptions" parameterType="long" resultType="long">
        SELECT COUNT(*) 
        FROM acc_subscription 
        WHERE plan_id = #{planId} 
        AND status = 'ACTIVE' 
        AND end_date > NOW()
    </select>

    <!-- 计算套餐总收入 -->
    <select id="sumTotalRevenue" parameterType="long" resultType="decimal">
        SELECT COALESCE(SUM(amount_paid), 0) 
        FROM acc_subscription 
        WHERE plan_id = #{planId} 
        AND status IN ('ACTIVE', 'EXPIRED', 'CANCELLED')
    </select>

    <!-- 查询套餐详情（包含统计信息） -->
    <select id="selectPlanDetailWithStats" parameterType="long" resultType="top.continew.admin.accounting.model.resp.SubscriptionPlanDetailResp">
        SELECT 
            p.id,
            p.name,
            p.code,
            p.description,
            p.price,
            p.currency,
            p.billing_cycle,
            p.features,
            p.limits,
            p.trial_days,
            p.is_popular,
            p.sort_order,
            p.status,
            p.create_time,
            p.update_time,
            COALESCE(s.subscription_count, 0) AS subscriptionCount,
            COALESCE(s.active_subscription_count, 0) AS activeSubscriptionCount,
            COALESCE(s.total_revenue, 0) AS totalRevenue
        FROM acc_subscription_plan p
        LEFT JOIN (
            SELECT 
                plan_id,
                COUNT(*) AS subscription_count,
                COUNT(CASE WHEN status = 'ACTIVE' AND end_date > NOW() THEN 1 END) AS active_subscription_count,
                SUM(CASE WHEN status IN ('ACTIVE', 'EXPIRED', 'CANCELLED') THEN amount_paid ELSE 0 END) AS total_revenue
            FROM acc_subscription 
            GROUP BY plan_id
        ) s ON p.id = s.plan_id
        WHERE p.id = #{id}
    </select>

    <!-- 查询套餐列表（包含订阅数量） -->
    <select id="selectPlanListWithStats" resultType="top.continew.admin.accounting.model.resp.SubscriptionPlanListResp">
        SELECT 
            p.id,
            p.name,
            p.code,
            p.description,
            p.price,
            p.currency,
            p.billing_cycle,
            p.trial_days,
            p.is_popular,
            p.sort_order,
            p.status,
            p.create_time,
            COALESCE(s.subscription_count, 0) AS subscriptionCount
        FROM acc_subscription_plan p
        LEFT JOIN (
            SELECT 
                plan_id,
                COUNT(*) AS subscription_count
            FROM acc_subscription 
            GROUP BY plan_id
        ) s ON p.id = s.plan_id
        <where>
            <if test="query.name != null and query.name != ''">
                AND p.name LIKE CONCAT('%', #{query.name}, '%')
            </if>
            <if test="query.code != null and query.code != ''">
                AND p.code LIKE CONCAT('%', #{query.code}, '%')
            </if>
            <if test="query.billingCycle != null and query.billingCycle != ''">
                AND p.billing_cycle = #{query.billingCycle}
            </if>
            <if test="query.currency != null and query.currency != ''">
                AND p.currency = #{query.currency}
            </if>
            <if test="query.priceMin != null">
                AND p.price >= #{query.priceMin}
            </if>
            <if test="query.priceMax != null">
                AND p.price &lt;= #{query.priceMax}
            </if>
            <if test="query.isPopular != null">
                AND p.is_popular = #{query.isPopular}
            </if>
            <if test="query.status != null">
                AND p.status = #{query.status}
            </if>
        </where>
        ORDER BY p.sort_order ASC, p.create_time DESC
    </select>

    <!-- 查询套餐使用统计 -->
    <select id="selectPlanUsageStats" parameterType="long" resultType="map">
        SELECT 
            COUNT(*) AS totalSubscriptions,
            COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) AS activeSubscriptions,
            COUNT(CASE WHEN status = 'EXPIRED' THEN 1 END) AS expiredSubscriptions,
            COUNT(CASE WHEN status = 'CANCELLED' THEN 1 END) AS cancelledSubscriptions,
            AVG(DATEDIFF(COALESCE(cancelled_at, end_date), start_date)) AS avgSubscriptionDays,
            SUM(amount_paid) AS totalRevenue,
            AVG(amount_paid) AS avgRevenue
        FROM acc_subscription 
        WHERE plan_id = #{planId}
    </select>

    <!-- 查询套餐转换率统计 -->
    <select id="selectPlanConversionStats" parameterType="long" resultType="map">
        SELECT 
            COUNT(CASE WHEN trial_end_date IS NOT NULL THEN 1 END) AS trialUsers,
            COUNT(CASE WHEN trial_end_date IS NOT NULL AND status = 'ACTIVE' THEN 1 END) AS convertedUsers,
            CASE 
                WHEN COUNT(CASE WHEN trial_end_date IS NOT NULL THEN 1 END) > 0 
                THEN ROUND(COUNT(CASE WHEN trial_end_date IS NOT NULL AND status = 'ACTIVE' THEN 1 END) * 100.0 / COUNT(CASE WHEN trial_end_date IS NOT NULL THEN 1 END), 2)
                ELSE 0 
            END AS conversionRate
        FROM acc_subscription 
        WHERE plan_id = #{planId}
    </select>

</mapper>
