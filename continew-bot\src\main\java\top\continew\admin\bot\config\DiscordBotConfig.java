package top.continew.admin.bot.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Discord机器人配置
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "bot.discord")
public class DiscordBotConfig {

    /**
     * 是否启用
     */
    private Boolean enabled = false;

    /**
     * Bot Token
     */
    private String token;

    /**
     * 应用ID
     */
    private String applicationId;

    /**
     * 是否启用Slash Commands
     */
    private Boolean enableSlashCommands = true;

    /**
     * 是否启用消息命令
     */
    private Boolean enableMessageCommands = true;

    /**
     * 是否启用按钮交互
     */
    private Boolean enableButtonInteractions = true;

    /**
     * 是否启用选择菜单
     */
    private Boolean enableSelectMenus = true;

    /**
     * 是否启用模态框
     */
    private Boolean enableModals = true;

    /**
     * 命令同步模式
     */
    private CommandSyncMode commandSyncMode = CommandSyncMode.GLOBAL;

    /**
     * 测试服务器ID（用于开发环境）
     */
    private String testGuildId;

    /**
     * 是否启用开发者模式
     */
    private Boolean developerMode = false;

    /**
     * 活动配置
     */
    private ActivityConfig activity = new ActivityConfig();

    /**
     * 状态配置
     */
    private StatusConfig status = new StatusConfig();

    /**
     * 命令同步模式
     */
    public enum CommandSyncMode {
        /**
         * 全局命令
         */
        GLOBAL,
        /**
         * 服务器命令
         */
        GUILD,
        /**
         * 混合模式
         */
        HYBRID
    }

    /**
     * 活动配置
     */
    @Data
    public static class ActivityConfig {
        /**
         * 活动类型
         */
        private String type = "PLAYING";

        /**
         * 活动名称
         */
        private String name = "记账助手";

        /**
         * 活动URL（仅STREAMING类型）
         */
        private String url;

        /**
         * 是否启用
         */
        private Boolean enabled = true;
    }

    /**
     * 状态配置
     */
    @Data
    public static class StatusConfig {
        /**
         * 在线状态
         */
        private String onlineStatus = "ONLINE";

        /**
         * 是否显示为空闲
         */
        private Boolean idle = false;

        /**
         * 是否显示为勿扰
         */
        private Boolean doNotDisturb = false;
    }
}
