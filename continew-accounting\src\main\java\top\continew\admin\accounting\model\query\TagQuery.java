package top.continew.admin.accounting.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.starter.extension.crud.model.query.SortQuery;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 标签查询条件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "标签查询条件")
public class TagQuery extends SortQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 标签名称
     */
    @Schema(description = "标签名称", example = "工作")
    private String name;

    /**
     * 标签颜色
     */
    @Schema(description = "标签颜色", example = "#FF5722")
    private String color;

    /**
     * 是否为系统标签
     */
    @Schema(description = "是否为系统标签", example = "false")
    private Boolean isSystem;

    /**
     * 是否为默认标签
     */
    @Schema(description = "是否为默认标签", example = "false")
    private Boolean isDefault;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "1")
    private Integer status;

    /**
     * 排除的标签ID列表
     */
    @Schema(description = "排除的标签ID列表")
    private List<Long> excludeTagIds;

    /**
     * 关键词
     */
    @Schema(description = "关键词", example = "工作")
    private String keyword;
}
