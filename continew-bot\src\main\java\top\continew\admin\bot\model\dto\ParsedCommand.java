package top.continew.admin.bot.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.accounting.enums.TransactionType;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 解析后的命令
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "解析后的命令")
public class ParsedCommand {

    /**
     * 原始文本
     */
    @Schema(description = "原始文本")
    private String originalText;

    /**
     * 是否有效
     */
    @Schema(description = "是否有效")
    private Boolean valid = false;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型")
    private TransactionType type;

    /**
     * 金额
     */
    @Schema(description = "金额")
    private BigDecimal amount;

    /**
     * 币种
     */
    @Schema(description = "币种")
    private String currency = "CNY";

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 分类
     */
    @Schema(description = "分类")
    private String category;

    /**
     * 标签列表
     */
    @Schema(description = "标签列表")
    private List<String> tags;

    /**
     * 交易时间
     */
    @Schema(description = "交易时间")
    private LocalDateTime transactionTime;

    /**
     * 是否启用分摊
     */
    @Schema(description = "是否启用分摊")
    private Boolean splitEnabled = false;

    /**
     * 分摊用户列表
     */
    @Schema(description = "分摊用户列表")
    private List<String> splitUsers;

    /**
     * 分摊人数
     */
    @Schema(description = "分摊人数")
    private Integer splitCount;

    /**
     * 附件列表
     */
    @Schema(description = "附件列表")
    private List<String> attachments;

    /**
     * 钱包名称
     */
    @Schema(description = "钱包名称")
    private String wallet;

    /**
     * 转账源钱包
     */
    @Schema(description = "转账源钱包")
    private String fromWallet;

    /**
     * 转账目标钱包
     */
    @Schema(description = "转账目标钱包")
    private String toWallet;

    /**
     * 是否启用重复交易
     */
    @Schema(description = "是否启用重复交易")
    private Boolean recurringEnabled = false;

    /**
     * 重复模式
     */
    @Schema(description = "重复模式")
    private String recurringPattern;

    /**
     * 智能推荐的分类
     */
    @Schema(description = "智能推荐的分类")
    private String suggestedCategory;

    /**
     * 智能推荐的标签
     */
    @Schema(description = "智能推荐的标签")
    private List<String> suggestedTags;

    /**
     * 置信度分数
     */
    @Schema(description = "置信度分数")
    private Double confidenceScore;
}
