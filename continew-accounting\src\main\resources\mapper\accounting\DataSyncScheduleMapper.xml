<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.accounting.mapper.DataSyncScheduleMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="top.continew.admin.accounting.model.entity.DataSyncScheduleDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="config_id" property="configId" jdbcType="BIGINT"/>
        <result column="schedule_name" property="scheduleName" jdbcType="VARCHAR"/>
        <result column="schedule_type" property="scheduleType" jdbcType="VARCHAR"/>
        <result column="cron_expression" property="cronExpression" jdbcType="VARCHAR"/>
        <result column="interval_seconds" property="intervalSeconds" jdbcType="INTEGER"/>
        <result column="scheduled_time" property="scheduledTime" jdbcType="TIMESTAMP"/>
        <result column="timezone" property="timezone" jdbcType="VARCHAR"/>
        <result column="enabled" property="enabled" jdbcType="BOOLEAN"/>
        <result column="max_retry_count" property="maxRetryCount" jdbcType="INTEGER"/>
        <result column="retry_interval_seconds" property="retryIntervalSeconds" jdbcType="INTEGER"/>
        <result column="timeout_seconds" property="timeoutSeconds" jdbcType="INTEGER"/>
        <result column="last_execution_time" property="lastExecutionTime" jdbcType="TIMESTAMP"/>
        <result column="next_execution_time" property="nextExecutionTime" jdbcType="TIMESTAMP"/>
        <result column="execution_count" property="executionCount" jdbcType="INTEGER"/>
        <result column="success_count" property="successCount" jdbcType="INTEGER"/>
        <result column="failure_count" property="failureCount" jdbcType="INTEGER"/>
        <result column="last_execution_status" property="lastExecutionStatus" jdbcType="VARCHAR"/>
        <result column="last_error_message" property="lastErrorMessage" jdbcType="LONGTEXT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="BIGINT"/>
        <result column="update_by" property="updateBy" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, config_id, schedule_name, schedule_type, cron_expression, interval_seconds,
        scheduled_time, timezone, enabled, max_retry_count, retry_interval_seconds, timeout_seconds,
        last_execution_time, next_execution_time, execution_count, success_count, failure_count,
        last_execution_status, last_error_message, create_time, update_time, create_by, update_by
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageList" resultType="top.continew.admin.accounting.model.resp.DataSyncScheduleListResp">
        SELECT 
            s.id,
            s.config_id,
            c.config_name,
            s.schedule_name,
            s.schedule_type,
            s.cron_expression,
            s.interval_seconds,
            s.scheduled_time,
            s.timezone,
            s.enabled,
            s.last_execution_time,
            s.next_execution_time,
            s.execution_count,
            s.success_count,
            s.failure_count,
            CASE 
                WHEN s.execution_count > 0 THEN ROUND((s.success_count * 100.0 / s.execution_count), 2)
                ELSE 0 
            END as success_rate,
            s.last_execution_status,
            s.last_error_message,
            CASE 
                WHEN s.enabled = 0 THEN 'DISABLED'
                WHEN s.last_execution_status = 'FAILED' AND s.failure_count > 3 THEN 'CRITICAL'
                WHEN s.last_execution_status = 'FAILED' THEN 'WARNING'
                WHEN s.next_execution_time IS NULL OR s.next_execution_time < NOW() THEN 'WARNING'
                ELSE 'HEALTHY'
            END as health_status,
            s.create_time,
            s.update_time
        FROM acc_data_sync_schedule s
        LEFT JOIN acc_data_sync_config c ON s.config_id = c.id
        <where>
            <if test="query.configId != null">
                AND s.config_id = #{query.configId}
            </if>
            <if test="query.scheduleName != null and query.scheduleName != ''">
                AND s.schedule_name LIKE CONCAT('%', #{query.scheduleName}, '%')
            </if>
            <if test="query.scheduleType != null and query.scheduleType != ''">
                AND s.schedule_type = #{query.scheduleType}
            </if>
            <if test="query.enabled != null">
                AND s.enabled = #{query.enabled}
            </if>
            <if test="query.lastExecutionStatus != null and query.lastExecutionStatus != ''">
                AND s.last_execution_status = #{query.lastExecutionStatus}
            </if>
            <if test="query.createTimeStart != null">
                AND s.create_time >= #{query.createTimeStart}
            </if>
            <if test="query.createTimeEnd != null">
                AND s.create_time &lt;= #{query.createTimeEnd}
            </if>
            <if test="query.lastExecutionTimeStart != null">
                AND s.last_execution_time >= #{query.lastExecutionTimeStart}
            </if>
            <if test="query.lastExecutionTimeEnd != null">
                AND s.last_execution_time &lt;= #{query.lastExecutionTimeEnd}
            </if>
            <if test="query.nextExecutionTimeStart != null">
                AND s.next_execution_time >= #{query.nextExecutionTimeStart}
            </if>
            <if test="query.nextExecutionTimeEnd != null">
                AND s.next_execution_time &lt;= #{query.nextExecutionTimeEnd}
            </if>
        </where>
        ORDER BY s.create_time DESC
    </select>

    <!-- 获取待执行的计划 -->
    <select id="selectPendingSchedules" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM acc_data_sync_schedule
        WHERE enabled = 1
          AND (next_execution_time IS NULL OR next_execution_time &lt;= #{currentTime})
          AND (schedule_type != 'ONE_TIME' OR scheduled_time &lt;= #{currentTime})
        ORDER BY next_execution_time ASC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据配置ID查询计划 -->
    <select id="selectByConfigId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM acc_data_sync_schedule
        WHERE config_id = #{configId}
        ORDER BY create_time DESC
    </select>

    <!-- 更新执行状态 -->
    <update id="updateExecutionStatus">
        UPDATE acc_data_sync_schedule
        SET last_execution_time = #{executionTime},
            last_execution_status = #{status},
            last_error_message = #{errorMessage},
            execution_count = execution_count + 1,
            <choose>
                <when test="status == 'SUCCESS'">
                    success_count = success_count + 1
                </when>
                <otherwise>
                    failure_count = failure_count + 1
                </otherwise>
            </choose>
        WHERE id = #{scheduleId}
    </update>

    <!-- 更新下次执行时间 -->
    <update id="updateNextExecutionTime">
        UPDATE acc_data_sync_schedule
        SET next_execution_time = #{nextExecutionTime}
        WHERE id = #{scheduleId}
    </update>

    <!-- 批量更新启用状态 -->
    <update id="batchUpdateEnabled">
        UPDATE acc_data_sync_schedule
        SET enabled = #{enabled}
        WHERE id IN
        <foreach collection="scheduleIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 获取计划健康状态 -->
    <select id="selectScheduleHealthStatus" resultType="map">
        SELECT 
            s.id,
            s.schedule_name,
            s.config_id,
            c.config_name,
            s.enabled,
            s.last_execution_status,
            s.last_execution_time,
            s.next_execution_time,
            s.execution_count,
            s.success_count,
            s.failure_count,
            CASE 
                WHEN s.execution_count > 0 THEN ROUND((s.success_count * 100.0 / s.execution_count), 2)
                ELSE 0 
            END as success_rate,
            CASE 
                WHEN s.enabled = 0 THEN 'DISABLED'
                WHEN s.last_execution_status = 'FAILED' AND s.failure_count > 3 THEN 'CRITICAL'
                WHEN s.last_execution_status = 'FAILED' THEN 'WARNING'
                WHEN s.next_execution_time IS NULL OR s.next_execution_time < NOW() THEN 'WARNING'
                ELSE 'HEALTHY'
            END as health_status,
            s.last_error_message
        FROM acc_data_sync_schedule s
        LEFT JOIN acc_data_sync_config c ON s.config_id = c.id
        <where>
            <if test="groupId != null">
                AND c.group_id = #{groupId}
            </if>
        </where>
        ORDER BY 
            CASE 
                WHEN s.enabled = 0 THEN 3
                WHEN s.last_execution_status = 'FAILED' AND s.failure_count > 3 THEN 1
                WHEN s.last_execution_status = 'FAILED' THEN 2
                ELSE 4
            END,
            s.last_execution_time DESC
    </select>

    <!-- 获取失败计划 -->
    <select id="selectFailedSchedules" resultType="map">
        SELECT 
            s.id,
            s.schedule_name,
            s.config_id,
            c.config_name,
            s.last_execution_status,
            s.last_execution_time,
            s.failure_count,
            s.last_error_message
        FROM acc_data_sync_schedule s
        LEFT JOIN acc_data_sync_config c ON s.config_id = c.id
        WHERE s.enabled = 1
          AND s.last_execution_status = 'FAILED'
          <if test="groupId != null">
              AND c.group_id = #{groupId}
          </if>
          <if test="hours != null and hours > 0">
              AND s.last_execution_time >= DATE_SUB(NOW(), INTERVAL #{hours} HOUR)
          </if>
        ORDER BY s.last_execution_time DESC
    </select>

    <!-- 获取超时计划 -->
    <select id="selectTimeoutSchedules" resultType="map">
        SELECT 
            s.id,
            s.schedule_name,
            s.config_id,
            c.config_name,
            s.last_execution_time,
            s.timeout_seconds,
            TIMESTAMPDIFF(SECOND, s.last_execution_time, NOW()) as elapsed_seconds
        FROM acc_data_sync_schedule s
        LEFT JOIN acc_data_sync_config c ON s.config_id = c.id
        WHERE s.enabled = 1
          AND s.timeout_seconds IS NOT NULL
          AND s.last_execution_status = 'RUNNING'
          AND TIMESTAMPDIFF(SECOND, s.last_execution_time, NOW()) > s.timeout_seconds
          <if test="groupId != null">
              AND c.group_id = #{groupId}
          </if>
        ORDER BY elapsed_seconds DESC
    </select>

    <!-- 获取执行历史 -->
    <select id="selectExecutionHistory" resultType="map">
        SELECT 
            l.id,
            l.sync_id,
            l.sync_type,
            l.status,
            l.start_time,
            l.end_time,
            l.duration,
            l.total_count,
            l.success_count,
            l.failed_count,
            l.conflict_count,
            l.throughput,
            l.error_message
        FROM acc_data_sync_log l
        WHERE l.config_id = (SELECT config_id FROM acc_data_sync_schedule WHERE id = #{scheduleId})
        ORDER BY l.start_time DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 获取执行统计 -->
    <select id="selectExecutionStatistics" resultType="map">
        SELECT 
            COUNT(*) as total_executions,
            COUNT(CASE WHEN l.status = 'COMPLETED' THEN 1 END) as successful_executions,
            COUNT(CASE WHEN l.status = 'FAILED' THEN 1 END) as failed_executions,
            ROUND(AVG(l.duration), 2) as avg_duration,
            MAX(l.duration) as max_duration,
            MIN(l.duration) as min_duration,
            ROUND(AVG(l.throughput), 2) as avg_throughput,
            SUM(l.total_count) as total_records,
            SUM(l.success_count) as total_success_records,
            SUM(l.failed_count) as total_failed_records,
            SUM(l.conflict_count) as total_conflict_records
        FROM acc_data_sync_log l
        WHERE l.config_id = (SELECT config_id FROM acc_data_sync_schedule WHERE id = #{scheduleId})
          <if test="startDate != null and startDate != ''">
              AND DATE(l.start_time) >= #{startDate}
          </if>
          <if test="endDate != null and endDate != ''">
              AND DATE(l.start_time) &lt;= #{endDate}
          </if>
    </select>

    <!-- 清理执行历史 -->
    <delete id="cleanupExecutionHistory">
        DELETE FROM acc_data_sync_log
        WHERE config_id = (SELECT config_id FROM acc_data_sync_schedule WHERE id = #{scheduleId})
          AND create_time &lt; DATE_SUB(NOW(), INTERVAL #{retentionDays} DAY)
    </delete>

    <!-- 获取计划统计 -->
    <select id="selectScheduleStatistics" resultType="map">
        SELECT 
            COUNT(*) as total_schedules,
            COUNT(CASE WHEN enabled = 1 THEN 1 END) as enabled_schedules,
            COUNT(CASE WHEN enabled = 0 THEN 1 END) as disabled_schedules,
            COUNT(CASE WHEN last_execution_status = 'SUCCESS' THEN 1 END) as success_schedules,
            COUNT(CASE WHEN last_execution_status = 'FAILED' THEN 1 END) as failed_schedules,
            COUNT(CASE WHEN last_execution_status = 'RUNNING' THEN 1 END) as running_schedules,
            COUNT(CASE WHEN schedule_type = 'CRON' THEN 1 END) as cron_schedules,
            COUNT(CASE WHEN schedule_type = 'INTERVAL' THEN 1 END) as interval_schedules,
            COUNT(CASE WHEN schedule_type = 'ONE_TIME' THEN 1 END) as onetime_schedules
        FROM acc_data_sync_schedule s
        LEFT JOIN acc_data_sync_config c ON s.config_id = c.id
        <where>
            <if test="groupId != null">
                AND c.group_id = #{groupId}
            </if>
        </where>
    </select>

    <!-- 获取执行趋势 -->
    <select id="selectExecutionTrends" resultType="map">
        SELECT 
            <choose>
                <when test="groupBy == 'hour'">
                    DATE_FORMAT(l.start_time, '%Y-%m-%d %H:00:00') as time_period
                </when>
                <when test="groupBy == 'day'">
                    DATE(l.start_time) as time_period
                </when>
                <when test="groupBy == 'week'">
                    DATE_FORMAT(l.start_time, '%Y-%u') as time_period
                </when>
                <when test="groupBy == 'month'">
                    DATE_FORMAT(l.start_time, '%Y-%m') as time_period
                </when>
                <otherwise>
                    DATE(l.start_time) as time_period
                </otherwise>
            </choose>,
            COUNT(*) as total_executions,
            COUNT(CASE WHEN l.status = 'COMPLETED' THEN 1 END) as successful_executions,
            COUNT(CASE WHEN l.status = 'FAILED' THEN 1 END) as failed_executions,
            ROUND(AVG(l.duration), 2) as avg_duration,
            ROUND(AVG(l.throughput), 2) as avg_throughput,
            SUM(l.total_count) as total_records
        FROM acc_data_sync_log l
        LEFT JOIN acc_data_sync_config c ON l.config_id = c.id
        WHERE 1=1
          <if test="groupId != null">
              AND c.group_id = #{groupId}
          </if>
          <if test="startDate != null and startDate != ''">
              AND DATE(l.start_time) >= #{startDate}
          </if>
          <if test="endDate != null and endDate != ''">
              AND DATE(l.start_time) &lt;= #{endDate}
          </if>
        GROUP BY time_period
        ORDER BY time_period
    </select>

    <!-- 获取性能分析 -->
    <select id="selectPerformanceAnalysis" resultType="map">
        SELECT 
            s.id as schedule_id,
            s.schedule_name,
            c.config_name,
            COUNT(l.id) as total_executions,
            ROUND(AVG(l.duration), 2) as avg_duration,
            MAX(l.duration) as max_duration,
            MIN(l.duration) as min_duration,
            ROUND(AVG(l.throughput), 2) as avg_throughput,
            MAX(l.throughput) as max_throughput,
            SUM(l.total_count) as total_records,
            SUM(l.success_count) as total_success_records,
            SUM(l.failed_count) as total_failed_records,
            CASE 
                WHEN COUNT(l.id) > 0 THEN ROUND((COUNT(CASE WHEN l.status = 'COMPLETED' THEN 1 END) * 100.0 / COUNT(l.id)), 2)
                ELSE 0 
            END as success_rate
        FROM acc_data_sync_schedule s
        LEFT JOIN acc_data_sync_config c ON s.config_id = c.id
        LEFT JOIN acc_data_sync_log l ON c.id = l.config_id
        WHERE 1=1
          <if test="groupId != null">
              AND c.group_id = #{groupId}
          </if>
          <if test="startDate != null and startDate != ''">
              AND (l.start_time IS NULL OR DATE(l.start_time) >= #{startDate})
          </if>
          <if test="endDate != null and endDate != ''">
              AND (l.start_time IS NULL OR DATE(l.start_time) &lt;= #{endDate})
          </if>
        GROUP BY s.id, s.schedule_name, c.config_name
        ORDER BY avg_duration DESC
    </select>

</mapper>
