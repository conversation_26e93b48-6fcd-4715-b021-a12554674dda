package top.continew.admin.accounting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.accounting.model.entity.NotificationDO;
import top.continew.admin.accounting.model.resp.NotificationStatisticsResp;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 通知推送 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface NotificationMapper extends BaseMapper<NotificationDO> {

    // ==================== 计划通知查询 ====================

    /**
     * 查询待发送的计划通知
     *
     * @param currentTime 当前时间
     * @param limit 限制数量
     * @return 通知ID列表
     */
    List<Long> selectPendingScheduledNotifications(@Param("currentTime") LocalDateTime currentTime, 
                                                  @Param("limit") Integer limit);

    /**
     * 查询需要重试的通知
     *
     * @param currentTime 当前时间
     * @param limit 限制数量
     * @return 通知ID列表
     */
    List<Long> selectNotificationsForRetry(@Param("currentTime") LocalDateTime currentTime, 
                                          @Param("limit") Integer limit);

    /**
     * 删除过期通知
     *
     * @param expiredBefore 过期时间点
     * @return 删除数量
     */
    Integer deleteExpiredNotifications(@Param("expiredBefore") LocalDateTime expiredBefore);

    // ==================== 通知日志查询 ====================

    /**
     * 查询通知发送日志
     *
     * @param notificationId 通知ID
     * @return 发送日志列表
     */
    List<Map<String, Object>> selectNotificationLogs(@Param("notificationId") Long notificationId);

    /**
     * 查询通知发送详情
     *
     * @param notificationId 通知ID
     * @return 发送详情
     */
    Map<String, Object> selectNotificationSendDetail(@Param("notificationId") Long notificationId);

    // ==================== 统计分析查询 ====================

    /**
     * 查询通知统计
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果
     */
    NotificationStatisticsResp selectNotificationStatistics(@Param("groupId") Long groupId,
                                                           @Param("startDate") String startDate,
                                                           @Param("endDate") String endDate);

    /**
     * 查询渠道性能统计
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 渠道性能统计
     */
    Map<String, Object> selectChannelPerformanceStatistics(@Param("groupId") Long groupId,
                                                          @Param("startDate") String startDate,
                                                          @Param("endDate") String endDate);

    /**
     * 查询发送趋势
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param groupBy 分组方式
     * @return 发送趋势数据
     */
    List<Map<String, Object>> selectSendTrends(@Param("groupId") Long groupId,
                                             @Param("startDate") String startDate,
                                             @Param("endDate") String endDate,
                                             @Param("groupBy") String groupBy);

    /**
     * 查询失败分析
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 失败分析数据
     */
    Map<String, Object> selectFailureAnalysis(@Param("groupId") Long groupId,
                                             @Param("startDate") String startDate,
                                             @Param("endDate") String endDate);

    /**
     * 查询通知类型统计
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 类型统计
     */
    List<Map<String, Object>> selectNotificationTypeStatistics(@Param("groupId") Long groupId,
                                                              @Param("startDate") String startDate,
                                                              @Param("endDate") String endDate);

    /**
     * 查询渠道统计
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 渠道统计
     */
    List<Map<String, Object>> selectChannelStatistics(@Param("groupId") Long groupId,
                                                     @Param("startDate") String startDate,
                                                     @Param("endDate") String endDate);

    /**
     * 查询用户通知统计
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 用户通知统计
     */
    List<Map<String, Object>> selectUserNotificationStatistics(@Param("groupId") Long groupId,
                                                              @Param("startDate") String startDate,
                                                              @Param("endDate") String endDate,
                                                              @Param("limit") Integer limit);

    /**
     * 查询模板使用统计
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 模板使用统计
     */
    List<Map<String, Object>> selectTemplateUsageStatistics(@Param("groupId") Long groupId,
                                                           @Param("startDate") String startDate,
                                                           @Param("endDate") String endDate,
                                                           @Param("limit") Integer limit);

    // ==================== 性能分析查询 ====================

    /**
     * 查询发送性能分析
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 性能分析数据
     */
    Map<String, Object> selectSendPerformanceAnalysis(@Param("groupId") Long groupId,
                                                     @Param("startDate") String startDate,
                                                     @Param("endDate") String endDate);

    /**
     * 查询渠道响应时间统计
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 响应时间统计
     */
    List<Map<String, Object>> selectChannelResponseTimeStatistics(@Param("groupId") Long groupId,
                                                                 @Param("startDate") String startDate,
                                                                 @Param("endDate") String endDate);

    /**
     * 查询重试分析
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 重试分析数据
     */
    Map<String, Object> selectRetryAnalysis(@Param("groupId") Long groupId,
                                          @Param("startDate") String startDate,
                                          @Param("endDate") String endDate);

    // ==================== 健康监控查询 ====================

    /**
     * 查询通知健康状态
     *
     * @return 健康状态
     */
    Map<String, Object> selectNotificationHealthStatus();

    /**
     * 查询渠道健康状态
     *
     * @return 渠道健康状态列表
     */
    List<Map<String, Object>> selectChannelHealthStatus();

    /**
     * 查询异常通知
     *
     * @param hours 小时数
     * @param limit 限制数量
     * @return 异常通知列表
     */
    List<Map<String, Object>> selectAbnormalNotifications(@Param("hours") Integer hours,
                                                         @Param("limit") Integer limit);

    /**
     * 查询发送队列状态
     *
     * @return 队列状态
     */
    Map<String, Object> selectSendQueueStatus();

}
