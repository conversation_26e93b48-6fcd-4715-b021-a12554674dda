package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.accounting.enums.BillingCycle;
import top.continew.admin.accounting.enums.SubscriptionStatus;

/**
 * 更新订阅请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "更新订阅请求")
public class SubscriptionUpdateReq {

    /**
     * 套餐ID
     */
    @Schema(description = "套餐ID", example = "2")
    private Long planId;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "ACTIVE")
    private SubscriptionStatus status;

    /**
     * 计费周期
     */
    @Schema(description = "计费周期", example = "YEARLY")
    private BillingCycle billingCycle;

    /**
     * 自动续费
     */
    @Schema(description = "自动续费", example = "false")
    private Boolean autoRenew;

    /**
     * 支付方式
     */
    @Schema(description = "支付方式", example = "PAYPAL")
    private String paymentMethod;

    /**
     * 取消原因
     */
    @Schema(description = "取消原因", example = "用户主动取消")
    private String cancellationReason;
}
