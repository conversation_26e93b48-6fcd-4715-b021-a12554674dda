# 群组记账机器人系统架构设计

## 1. 整体架构概览

```mermaid
graph TB
    subgraph "客户端层"
        TG[Telegram Bot]
        DC[Discord Bot]
        WEB[Web Dashboard]
        API[API Client]
    end
    
    subgraph "网关层"
        GW[API Gateway]
        LB[Load Balancer]
    end
    
    subgraph "应用服务层"
        subgraph "ContiNew Admin Core"
            AUTH[认证服务]
            USER[用户管理]
            PERM[权限管理]
        end
        
        subgraph "业务服务层"
            ACC[记账服务]
            BOT[机器人服务]
            ANA[分析服务]
            INT[集成服务]
            SUB[订阅服务]
        end
    end
    
    subgraph "数据层"
        MYSQL[(MySQL)]
        REDIS[(Redis)]
        MQ[RabbitMQ]
        OSS[对象存储]
    end
    
    TG --> GW
    DC --> GW
    WEB --> GW
    API --> GW
    
    GW --> LB
    LB --> AUTH
    LB --> ACC
    LB --> BOT
    LB --> ANA
    LB --> INT
    LB --> SUB
    
    AUTH --> USER
    AUTH --> PERM
    
    ACC --> MYSQL
    ACC --> REDIS
    BOT --> MQ
    ANA --> MYSQL
    INT --> OSS
```

## 2. 技术栈选型

### 2.1 后端技术栈
- **框架**: Spring Boot 3.2.x (基于ContiNew Admin)
- **Java版本**: Java 17 LTS
- **ORM**: MyBatis Plus 3.5.x
- **权限**: SaToken 1.37.x (继承现有)
- **缓存**: Redis 7.x + JetCache
- **消息队列**: RabbitMQ 3.12.x
- **数据库**: MySQL 8.0.x
- **文档**: Knife4j (Swagger)

### 2.2 机器人集成
- **Telegram**: Telegram Bot API + Spring Boot Starter
- **Discord**: Discord JDA (Java Discord API)
- **消息处理**: 异步消息队列处理

### 2.3 前端技术栈
- **框架**: Vue 3.x + TypeScript
- **UI组件**: Element Plus (继承现有)
- **状态管理**: Pinia
- **图表**: ECharts 5.x
- **构建工具**: Vite

### 2.4 基础设施
- **容器化**: Docker + Docker Compose
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack
- **CI/CD**: GitHub Actions

## 3. 模块设计

### 3.1 continew-accounting (记账核心模块)

```
continew-accounting/
├── src/main/java/top/continew/admin/accounting/
│   ├── controller/          # REST API控制器
│   │   ├── TransactionController.java
│   │   ├── GroupController.java
│   │   ├── WalletController.java
│   │   └── BudgetController.java
│   ├── service/            # 业务逻辑层
│   │   ├── TransactionService.java
│   │   ├── GroupService.java
│   │   ├── SplitService.java
│   │   └── DebtService.java
│   ├── model/              # 数据模型
│   │   ├── entity/         # 实体类
│   │   ├── dto/           # 数据传输对象
│   │   └── vo/            # 视图对象
│   ├── mapper/             # 数据访问层
│   └── enums/              # 枚举类
```

**核心功能**:
- 群组管理 (创建、配置、成员管理)
- 账单管理 (增删改查、分摊、历史记录)
- 多币种钱包管理
- 预算管理
- 债务跟踪

### 3.2 continew-bot (机器人集成模块)

```
continew-bot/
├── src/main/java/top/continew/admin/bot/
│   ├── telegram/           # Telegram机器人
│   │   ├── TelegramBotService.java
│   │   ├── TelegramCommandHandler.java
│   │   └── TelegramWebhookController.java
│   ├── discord/            # Discord机器人
│   │   ├── DiscordBotService.java
│   │   ├── DiscordCommandHandler.java
│   │   └── DiscordEventListener.java
│   ├── common/             # 通用组件
│   │   ├── CommandParser.java
│   │   ├── MessageFormatter.java
│   │   └── BotMessageQueue.java
│   └── config/             # 配置类
```

**核心功能**:
- 多平台机器人支持 (Telegram/Discord)
- 命令解析与处理
- 消息格式化与发送
- 异步消息处理
- Webhook接收

### 3.3 continew-analytics (数据分析模块)

```
continew-analytics/
├── src/main/java/top/continew/admin/analytics/
│   ├── controller/         # 分析API
│   ├── service/           # 分析服务
│   │   ├── DashboardService.java
│   │   ├── ReportService.java
│   │   └── StatisticsService.java
│   ├── model/             # 分析模型
│   └── scheduler/         # 定时任务
```

**核心功能**:
- 仪表盘数据聚合
- 财务报表生成
- 统计分析
- 定时报告

## 4. 数据流设计

### 4.1 机器人消息处理流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Bot as 机器人
    participant MQ as 消息队列
    participant Service as 业务服务
    participant DB as 数据库
    
    User->>Bot: 发送命令 (+100 #餐费)
    Bot->>MQ: 推送消息到队列
    MQ->>Service: 异步处理消息
    Service->>Service: 解析命令
    Service->>Service: 权限验证
    Service->>DB: 保存账单数据
    Service->>Bot: 返回处理结果
    Bot->>User: 发送确认消息
```

### 4.2 Web后台数据流

```mermaid
sequenceDiagram
    participant Web as Web前端
    participant API as API网关
    participant Auth as 认证服务
    participant Service as 业务服务
    participant Cache as Redis缓存
    participant DB as 数据库
    
    Web->>API: 请求数据
    API->>Auth: 验证Token
    Auth->>API: 返回用户信息
    API->>Service: 调用业务服务
    Service->>Cache: 查询缓存
    alt 缓存命中
        Cache->>Service: 返回缓存数据
    else 缓存未命中
        Service->>DB: 查询数据库
        DB->>Service: 返回数据
        Service->>Cache: 更新缓存
    end
    Service->>API: 返回业务数据
    API->>Web: 返回响应
```

## 5. 安全设计

### 5.1 认证与授权
- **Token认证**: 基于SaToken的JWT认证
- **角色权限**: RBAC模型 (Owner/Accountant/Member/Auditor)
- **API权限**: 基于注解的权限控制
- **数据隔离**: 基于群组的数据隔离

### 5.2 数据安全
- **传输加密**: HTTPS/TLS 1.3
- **存储加密**: 敏感数据AES加密
- **访问控制**: IP白名单、频率限制
- **审计日志**: 完整的操作审计

## 6. 性能设计

### 6.1 缓存策略
- **多级缓存**: JetCache (本地+Redis)
- **缓存预热**: 启动时预加载热点数据
- **缓存更新**: 基于事件的缓存失效

### 6.2 数据库优化
- **读写分离**: 主从复制
- **分库分表**: 按群组ID分表
- **索引优化**: 基于查询模式的索引设计

### 6.3 异步处理
- **消息队列**: RabbitMQ异步处理
- **批量操作**: 批量插入/更新
- **定时任务**: Quartz调度器

## 7. 监控与运维

### 7.1 应用监控
- **性能监控**: Spring Boot Actuator + Micrometer
- **业务监控**: 自定义指标收集
- **错误追踪**: 异常日志聚合

### 7.2 基础设施监控
- **系统监控**: Prometheus + Grafana
- **日志监控**: ELK Stack
- **告警机制**: 基于阈值的自动告警
