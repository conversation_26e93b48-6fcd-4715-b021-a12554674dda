package top.continew.admin.accounting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.accounting.model.entity.WalletHistoryDO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 钱包历史记录 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface WalletHistoryMapper extends BaseMapper<WalletHistoryDO> {

    /**
     * 查询钱包历史记录
     *
     * @param groupId  群组ID
     * @param currency 币种
     * @param limit    限制数量
     * @return 历史记录列表
     */
    List<WalletHistoryDO> selectWalletHistory(@Param("groupId") Long groupId, 
                                              @Param("currency") String currency, 
                                              @Param("limit") Integer limit);

    /**
     * 查询钱包历史记录（带操作人信息）
     *
     * @param groupId  群组ID
     * @param currency 币种
     * @param limit    限制数量
     * @return 历史记录列表（包含操作人信息）
     */
    List<Map<String, Object>> selectWalletHistoryWithOperator(@Param("groupId") Long groupId, 
                                                              @Param("currency") String currency, 
                                                              @Param("limit") Integer limit);

    /**
     * 查询指定时间范围内的钱包历史记录
     *
     * @param groupId   群组ID
     * @param currency  币种
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 历史记录列表
     */
    List<WalletHistoryDO> selectWalletHistoryByTimeRange(@Param("groupId") Long groupId,
                                                         @Param("currency") String currency,
                                                         @Param("startTime") LocalDateTime startTime,
                                                         @Param("endTime") LocalDateTime endTime);

    /**
     * 查询指定操作类型的历史记录
     *
     * @param groupId       群组ID
     * @param currency      币种
     * @param operationType 操作类型
     * @param limit         限制数量
     * @return 历史记录列表
     */
    List<WalletHistoryDO> selectWalletHistoryByOperationType(@Param("groupId") Long groupId,
                                                             @Param("currency") String currency,
                                                             @Param("operationType") String operationType,
                                                             @Param("limit") Integer limit);

    /**
     * 查询指定业务的历史记录
     *
     * @param businessId   业务ID
     * @param businessType 业务类型
     * @return 历史记录列表
     */
    List<WalletHistoryDO> selectWalletHistoryByBusiness(@Param("businessId") Long businessId,
                                                        @Param("businessType") String businessType);

    /**
     * 统计钱包操作次数
     *
     * @param groupId   群组ID
     * @param currency  币种
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 操作统计
     */
    Map<String, Object> countWalletOperations(@Param("groupId") Long groupId,
                                              @Param("currency") String currency,
                                              @Param("startTime") LocalDateTime startTime,
                                              @Param("endTime") LocalDateTime endTime);

    /**
     * 查询钱包余额变化趋势
     *
     * @param groupId   群组ID
     * @param currency  币种
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 余额变化趋势
     */
    List<Map<String, Object>> selectBalanceTrend(@Param("groupId") Long groupId,
                                                 @Param("currency") String currency,
                                                 @Param("startTime") LocalDateTime startTime,
                                                 @Param("endTime") LocalDateTime endTime);

    /**
     * 删除过期的历史记录
     *
     * @param retentionDays 保留天数
     * @return 删除的记录数
     */
    int deleteExpiredHistory(@Param("retentionDays") Integer retentionDays);

    /**
     * 删除指定群组的历史记录
     *
     * @param groupId 群组ID
     * @return 删除的记录数
     */
    int deleteHistoryByGroupId(@Param("groupId") Long groupId);

    /**
     * 删除指定钱包的历史记录
     *
     * @param walletId 钱包ID
     * @return 删除的记录数
     */
    int deleteHistoryByWalletId(@Param("walletId") Long walletId);

    /**
     * 查询最近的历史记录
     *
     * @param groupId  群组ID
     * @param currency 币种
     * @return 最近的历史记录
     */
    WalletHistoryDO selectLatestHistory(@Param("groupId") Long groupId, @Param("currency") String currency);

    /**
     * 批量插入历史记录
     *
     * @param historyList 历史记录列表
     * @return 插入的记录数
     */
    int batchInsertHistory(@Param("historyList") List<WalletHistoryDO> historyList);

    /**
     * 查询操作人的历史记录
     *
     * @param operatorId 操作人ID
     * @param groupId    群组ID
     * @param limit      限制数量
     * @return 历史记录列表
     */
    List<WalletHistoryDO> selectHistoryByOperator(@Param("operatorId") Long operatorId,
                                                  @Param("groupId") Long groupId,
                                                  @Param("limit") Integer limit);

    /**
     * 查询异常操作记录
     *
     * @param groupId   群组ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 异常操作记录
     */
    List<WalletHistoryDO> selectAbnormalOperations(@Param("groupId") Long groupId,
                                                   @Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);
}
