package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.accounting.enums.FileAccessTypeEnum;
import top.continew.admin.accounting.enums.FileProcessStatusEnum;
import top.continew.admin.accounting.enums.FileStorageTypeEnum;
import top.continew.admin.system.enums.FileTypeEnum;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 文件上传响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "文件上传响应")
public class FileUploadResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 文件ID
     */
    @Schema(description = "文件ID", example = "1")
    private Long fileId;

    /**
     * 文件名称
     */
    @Schema(description = "文件名称", example = "receipt_20250101_001.jpg")
    private String fileName;

    /**
     * 原始文件名
     */
    @Schema(description = "原始文件名", example = "receipt.jpg")
    private String originalFileName;

    /**
     * 文件URL
     */
    @Schema(description = "文件URL", example = "https://example.com/files/receipt_20250101_001.jpg")
    private String fileUrl;

    /**
     * CDN URL
     */
    @Schema(description = "CDN URL", example = "https://cdn.example.com/files/receipt_20250101_001.jpg")
    private String cdnUrl;

    /**
     * 缩略图URL
     */
    @Schema(description = "缩略图URL", example = "https://example.com/thumbnails/receipt_20250101_001_thumb.jpg")
    private String thumbnailUrl;

    /**
     * 预览URL
     */
    @Schema(description = "预览URL", example = "https://example.com/preview/receipt_20250101_001.jpg")
    private String previewUrl;

    /**
     * 文件大小（字节）
     */
    @Schema(description = "文件大小（字节）", example = "1048576")
    private Long fileSize;

    /**
     * 文件类型
     */
    @Schema(description = "文件类型", example = "IMAGE")
    private FileTypeEnum fileType;

    /**
     * 文件扩展名
     */
    @Schema(description = "文件扩展名", example = "jpg")
    private String fileExtension;

    /**
     * MIME类型
     */
    @Schema(description = "MIME类型", example = "image/jpeg")
    private String mimeType;

    /**
     * 存储类型
     */
    @Schema(description = "存储类型", example = "ALIYUN_OSS")
    private FileStorageTypeEnum storageType;

    /**
     * 访问权限类型
     */
    @Schema(description = "访问权限类型", example = "PRIVATE")
    private FileAccessTypeEnum accessType;

    /**
     * 处理状态
     */
    @Schema(description = "处理状态", example = "COMPLETED")
    private FileProcessStatusEnum processStatus;

    /**
     * 文件MD5值
     */
    @Schema(description = "文件MD5值", example = "d41d8cd98f00b204e9800998ecf8427e")
    private String fileMd5;

    /**
     * 文件SHA256值
     */
    @Schema(description = "文件SHA256值", example = "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855")
    private String fileSha256;

    /**
     * 上传耗时（毫秒）
     */
    @Schema(description = "上传耗时（毫秒）", example = "1500")
    private Long uploadDuration;

    /**
     * 处理结果
     */
    @Schema(description = "处理结果")
    private Map<String, Object> processResult;

    /**
     * 关联业务ID
     */
    @Schema(description = "关联业务ID", example = "123456")
    private String businessId;

    /**
     * 关联业务类型
     */
    @Schema(description = "关联业务类型", example = "TRANSACTION")
    private String businessType;

    /**
     * 上传时间
     */
    @Schema(description = "上传时间")
    private LocalDateTime uploadTime;

    /**
     * 是否成功
     */
    @Schema(description = "是否成功", example = "true")
    private Boolean success;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 警告信息
     */
    @Schema(description = "警告信息")
    private String warningMessage;

}
