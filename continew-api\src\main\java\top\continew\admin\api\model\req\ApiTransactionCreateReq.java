package top.continew.admin.api.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * API交易创建请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "API交易创建请求")
public class ApiTransactionCreateReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    @NotNull(message = "群组ID不能为空")
    private Long groupId;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型", example = "EXPENSE", allowableValues = {"INCOME", "EXPENSE", "TRANSFER"})
    @NotBlank(message = "交易类型不能为空")
    private String type;

    /**
     * 金额
     */
    @Schema(description = "交易金额", example = "100.50")
    @NotNull(message = "交易金额不能为空")
    @DecimalMin(value = "0.01", message = "交易金额必须大于0")
    @Digits(integer = 10, fraction = 2, message = "金额格式不正确")
    private BigDecimal amount;

    /**
     * 货币
     */
    @Schema(description = "货币类型", example = "CNY")
    @Size(max = 10, message = "货币类型长度不能超过 {max} 个字符")
    private String currency;

    /**
     * 描述
     */
    @Schema(description = "交易描述", example = "午餐费用")
    @NotBlank(message = "交易描述不能为空")
    @Size(max = 200, message = "交易描述长度不能超过 {max} 个字符")
    private String description;

    /**
     * 分类ID
     */
    @Schema(description = "分类ID", example = "1")
    private Long categoryId;

    /**
     * 钱包ID
     */
    @Schema(description = "钱包ID", example = "1")
    private Long walletId;

    /**
     * 目标钱包ID（转账时使用）
     */
    @Schema(description = "目标钱包ID（转账时使用）", example = "2")
    private Long targetWalletId;

    /**
     * 交易时间
     */
    @Schema(description = "交易时间")
    private LocalDateTime transactionTime;

    /**
     * 标签列表
     */
    @Schema(description = "标签列表", example = "[\"餐饮\", \"工作日\"]")
    private List<String> tags;

    /**
     * 参与者列表
     */
    @Schema(description = "参与者列表")
    private List<Long> participants;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "与同事聚餐")
    @Size(max = 500, message = "备注长度不能超过 {max} 个字符")
    private String remark;

    /**
     * 位置信息
     */
    @Schema(description = "位置信息")
    private LocationInfo location;

    /**
     * 附件列表
     */
    @Schema(description = "附件列表")
    private List<AttachmentInfo> attachments;

    /**
     * 是否定期交易
     */
    @Schema(description = "是否定期交易", example = "false")
    private Boolean isRecurring;

    /**
     * 定期交易设置
     */
    @Schema(description = "定期交易设置")
    private RecurringInfo recurring;

    /**
     * 分摊信息
     */
    @Schema(description = "分摊信息")
    private SplitInfo split;

    /**
     * 位置信息
     */
    @Data
    @Schema(description = "位置信息")
    public static class LocationInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 纬度
         */
        @Schema(description = "纬度", example = "39.9042")
        private Double latitude;

        /**
         * 经度
         */
        @Schema(description = "经度", example = "116.4074")
        private Double longitude;

        /**
         * 地址
         */
        @Schema(description = "地址", example = "北京市朝阳区")
        @Size(max = 200, message = "地址长度不能超过 {max} 个字符")
        private String address;

        /**
         * 地点名称
         */
        @Schema(description = "地点名称", example = "某某餐厅")
        @Size(max = 100, message = "地点名称长度不能超过 {max} 个字符")
        private String placeName;
    }

    /**
     * 附件信息
     */
    @Data
    @Schema(description = "附件信息")
    public static class AttachmentInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 文件名
         */
        @Schema(description = "文件名", example = "receipt.jpg")
        @NotBlank(message = "文件名不能为空")
        @Size(max = 255, message = "文件名长度不能超过 {max} 个字符")
        private String fileName;

        /**
         * 文件URL
         */
        @Schema(description = "文件URL", example = "https://example.com/files/receipt.jpg")
        @NotBlank(message = "文件URL不能为空")
        @Size(max = 500, message = "文件URL长度不能超过 {max} 个字符")
        private String fileUrl;

        /**
         * 文件类型
         */
        @Schema(description = "文件类型", example = "image/jpeg")
        @Size(max = 50, message = "文件类型长度不能超过 {max} 个字符")
        private String fileType;

        /**
         * 文件大小
         */
        @Schema(description = "文件大小（字节）", example = "1024000")
        private Long fileSize;

        /**
         * 缩略图URL
         */
        @Schema(description = "缩略图URL", example = "https://example.com/thumbnails/receipt_thumb.jpg")
        @Size(max = 500, message = "缩略图URL长度不能超过 {max} 个字符")
        private String thumbnailUrl;
    }

    /**
     * 定期交易信息
     */
    @Data
    @Schema(description = "定期交易信息")
    public static class RecurringInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 重复类型
         */
        @Schema(description = "重复类型", example = "MONTHLY", allowableValues = {"DAILY", "WEEKLY", "MONTHLY", "YEARLY"})
        @NotBlank(message = "重复类型不能为空")
        private String type;

        /**
         * 重复间隔
         */
        @Schema(description = "重复间隔", example = "1")
        @Min(value = 1, message = "重复间隔必须大于0")
        private Integer interval;

        /**
         * 结束日期
         */
        @Schema(description = "结束日期")
        private LocalDateTime endDate;

        /**
         * 重复次数
         */
        @Schema(description = "重复次数", example = "12")
        @Min(value = 1, message = "重复次数必须大于0")
        private Integer count;

        /**
         * 是否自动创建
         */
        @Schema(description = "是否自动创建", example = "true")
        private Boolean autoCreate;
    }

    /**
     * 分摊信息
     */
    @Data
    @Schema(description = "分摊信息")
    public static class SplitInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 分摊类型
         */
        @Schema(description = "分摊类型", example = "EQUAL", allowableValues = {"EQUAL", "PERCENTAGE", "AMOUNT", "CUSTOM"})
        @NotBlank(message = "分摊类型不能为空")
        private String type;

        /**
         * 分摊详情
         */
        @Schema(description = "分摊详情")
        private List<SplitDetail> details;

        /**
         * 分摊详情
         */
        @Data
        @Schema(description = "分摊详情")
        public static class SplitDetail implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 用户ID
             */
            @Schema(description = "用户ID", example = "1")
            @NotNull(message = "用户ID不能为空")
            private Long userId;

            /**
             * 分摊金额
             */
            @Schema(description = "分摊金额", example = "50.25")
            @DecimalMin(value = "0.01", message = "分摊金额必须大于0")
            private BigDecimal amount;

            /**
             * 分摊比例
             */
            @Schema(description = "分摊比例", example = "0.5")
            @DecimalMin(value = "0.01", message = "分摊比例必须大于0")
            @DecimalMax(value = "1.0", message = "分摊比例不能超过1")
            private BigDecimal percentage;

            /**
             * 是否已支付
             */
            @Schema(description = "是否已支付", example = "false")
            private Boolean isPaid;

            /**
             * 支付时间
             */
            @Schema(description = "支付时间")
            private LocalDateTime paidTime;
        }
    }
}
