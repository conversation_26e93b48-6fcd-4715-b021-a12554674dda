package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 报表总览响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "报表总览响应")
public class ReportOverviewResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 总收入
     */
    @Schema(description = "总收入", example = "10000.00")
    private BigDecimal totalIncome;

    /**
     * 总支出
     */
    @Schema(description = "总支出", example = "8000.00")
    private BigDecimal totalExpense;

    /**
     * 净收入（收入-支出）
     */
    @Schema(description = "净收入", example = "2000.00")
    private BigDecimal netIncome;

    /**
     * 交易笔数
     */
    @Schema(description = "交易笔数", example = "150")
    private Integer transactionCount;

    /**
     * 平均交易金额
     */
    @Schema(description = "平均交易金额", example = "120.00")
    private BigDecimal avgTransactionAmount;

    /**
     * 最大单笔支出
     */
    @Schema(description = "最大单笔支出", example = "1500.00")
    private BigDecimal maxExpense;

    /**
     * 最大单笔收入
     */
    @Schema(description = "最大单笔收入", example = "5000.00")
    private BigDecimal maxIncome;

    /**
     * 收入增长率（相比上期）
     */
    @Schema(description = "收入增长率", example = "15.5")
    private BigDecimal incomeGrowthRate;

    /**
     * 支出增长率（相比上期）
     */
    @Schema(description = "支出增长率", example = "8.2")
    private BigDecimal expenseGrowthRate;

    /**
     * 活跃天数
     */
    @Schema(description = "活跃天数", example = "25")
    private Integer activeDays;

    /**
     * 日均支出
     */
    @Schema(description = "日均支出", example = "320.00")
    private BigDecimal avgDailyExpense;

    /**
     * 日均收入
     */
    @Schema(description = "日均收入", example = "400.00")
    private BigDecimal avgDailyIncome;

    /**
     * 趋势数据
     */
    @Schema(description = "趋势数据")
    private List<ReportTrendResp> trendData;

    /**
     * 分类统计（Top 5）
     */
    @Schema(description = "分类统计")
    private List<ReportCategoryResp> topCategories;

    /**
     * 钱包统计
     */
    @Schema(description = "钱包统计")
    private List<ReportWalletResp> walletStats;
}
