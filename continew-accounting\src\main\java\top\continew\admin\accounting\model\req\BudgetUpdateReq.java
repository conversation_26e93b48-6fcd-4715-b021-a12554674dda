package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import jakarta.validation.Valid;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 预算更新请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "预算更新请求")
public class BudgetUpdateReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 预算名称
     */
    @Schema(description = "预算名称", example = "2025年度预算")
    @Size(max = 100, message = "预算名称长度不能超过100个字符")
    private String budgetName;

    /**
     * 预算描述
     */
    @Schema(description = "预算描述", example = "2025年度收支预算计划")
    @Size(max = 500, message = "预算描述长度不能超过500个字符")
    private String description;

    /**
     * 结束日期
     */
    @Schema(description = "结束日期", example = "2025-12-31")
    private LocalDate endDate;

    /**
     * 总预算金额
     */
    @Schema(description = "总预算金额", example = "100000.00")
    @DecimalMin(value = "0.01", message = "总预算金额必须大于0")
    @Digits(integer = 10, fraction = 2, message = "总预算金额格式不正确")
    private BigDecimal totalAmount;

    /**
     * 预算分配
     */
    @Schema(description = "预算分配")
    @Valid
    private List<BudgetCreateReq.BudgetAllocation> allocations;

    /**
     * 预警设置
     */
    @Schema(description = "预警设置")
    @Valid
    private BudgetCreateReq.AlertSettings alertSettings;

    /**
     * 审批设置
     */
    @Schema(description = "审批设置")
    @Valid
    private BudgetCreateReq.ApprovalSettings approvalSettings;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    /**
     * 扩展属性
     */
    @Schema(description = "扩展属性")
    private Map<String, Object> attributes;

    /**
     * 更新原因
     */
    @Schema(description = "更新原因", example = "调整预算分配")
    @Size(max = 200, message = "更新原因长度不能超过200个字符")
    private String updateReason;
}
