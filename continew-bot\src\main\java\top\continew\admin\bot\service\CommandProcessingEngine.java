package top.continew.admin.bot.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.continew.admin.accounting.enums.PlatformType;
import top.continew.admin.accounting.enums.TransactionType;
import top.continew.admin.accounting.model.req.TransactionAddReq;
import top.continew.admin.accounting.service.TransactionService;
import top.continew.admin.bot.model.dto.BotMessage;
import top.continew.admin.bot.model.dto.CommandExecutionResult;
import top.continew.admin.bot.model.dto.ParsedCommand;
import top.continew.admin.bot.model.entity.CommandHistoryDO;
import top.continew.admin.common.util.helper.LoginHelper;
import top.continew.starter.core.exception.BusinessException;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 智能命令处理引擎
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CommandProcessingEngine {

    private final CommandParser commandParser;
    private final TransactionService transactionService;
    private final CommandHistoryService commandHistoryService;

    /**
     * 处理单个命令
     */
    public CommandExecutionResult processCommand(String text, PlatformType platform, Long groupId, Long userId) {
        long startTime = System.currentTimeMillis();
        ParsedCommand command = null;
        CommandExecutionResult result;

        try {
            // 解析命令
            command = commandParser.parseCommand(text);
            if (command == null || !command.getValid()) {
                result = CommandExecutionResult.failure(
                    command != null ? command.getErrorMessage() : "无法解析命令"
                );
            } else {
                // 执行命令
                result = executeCommand(command, platform, groupId, userId);
            }

        } catch (Exception e) {
            log.error("命令处理失败: {}", text, e);
            result = CommandExecutionResult.failure("命令处理失败: " + e.getMessage());
        }

        // 设置执行信息
        result.withDuration(startTime)
              .withOriginalCommand(text)
              .withParsedCommand(command);

        // 记录历史
        commandHistoryService.recordCommandExecution(text, command, result, platform, groupId, userId);

        return result;
    }

    /**
     * 批量处理命令
     */
    public List<CommandExecutionResult> processCommands(List<String> texts, PlatformType platform, Long groupId, Long userId) {
        List<CompletableFuture<CommandExecutionResult>> futures = texts.stream()
            .map(text -> CompletableFuture.supplyAsync(() -> 
                processCommand(text, platform, groupId, userId)))
            .toList();

        return futures.stream()
            .map(CompletableFuture::join)
            .toList();
    }

    /**
     * 异步处理命令
     */
    public CompletableFuture<CommandExecutionResult> processCommandAsync(String text, PlatformType platform, Long groupId, Long userId) {
        return CompletableFuture.supplyAsync(() -> 
            processCommand(text, platform, groupId, userId));
    }

    /**
     * 执行解析后的命令
     */
    private CommandExecutionResult executeCommand(ParsedCommand command, PlatformType platform, Long groupId, Long userId) {
        try {
            switch (command.getType()) {
                case INCOME, EXPENSE -> {
                    return executeTransactionCommand(command, platform, groupId, userId);
                }
                case TRANSFER -> {
                    return executeTransferCommand(command, platform, groupId, userId);
                }
                default -> {
                    return CommandExecutionResult.failure("不支持的交易类型: " + command.getType());
                }
            }
        } catch (BusinessException e) {
            return CommandExecutionResult.failure(e.getMessage());
        } catch (Exception e) {
            log.error("命令执行失败", e);
            return CommandExecutionResult.failure("命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行交易命令
     */
    private CommandExecutionResult executeTransactionCommand(ParsedCommand command, PlatformType platform, Long groupId, Long userId) {
        TransactionAddReq req = new TransactionAddReq();
        req.setGroupId(groupId);
        req.setType(command.getType());
        req.setAmount(command.getAmount());
        req.setCurrency(command.getCurrency());
        req.setDescription(command.getDescription());
        req.setCategory(command.getCategory());
        req.setTags(command.getTags());
        req.setTransactionTime(command.getTransactionTime() != null ? 
            command.getTransactionTime() : LocalDateTime.now());
        req.setPlatform(platform);
        req.setCreatedBy(userId);

        // 处理分摊
        if (Boolean.TRUE.equals(command.getSplitEnabled())) {
            // TODO: 实现分摊逻辑
            log.info("分摊功能待实现: {}", command.getSplitUsers());
        }

        // 处理重复交易
        if (Boolean.TRUE.equals(command.getRecurringEnabled())) {
            // TODO: 实现重复交易逻辑
            log.info("重复交易功能待实现: {}", command.getRecurringPattern());
        }

        Long transactionId = transactionService.add(req);
        
        return CommandExecutionResult.success(
            "记账成功！" + formatTransactionSummary(command),
            transactionId
        );
    }

    /**
     * 执行转账命令
     */
    private CommandExecutionResult executeTransferCommand(ParsedCommand command, PlatformType platform, Long groupId, Long userId) {
        // TODO: 实现转账逻辑
        log.info("转账功能待实现: 从 {} 到 {} 转账 {}", 
            command.getFromWallet(), command.getToWallet(), command.getAmount());
        
        return CommandExecutionResult.success(
            String.format("转账成功！从 %s 到 %s 转账 %.2f %s",
                command.getFromWallet(), command.getToWallet(), 
                command.getAmount(), command.getCurrency()),
            null
        );
    }

    /**
     * 格式化交易摘要
     */
    private String formatTransactionSummary(ParsedCommand command) {
        StringBuilder summary = new StringBuilder();
        
        summary.append("\n💰 金额: ").append(command.getType().getSymbol())
               .append(command.getAmount()).append(" ").append(command.getCurrency());
        
        if (StringUtils.isNotBlank(command.getDescription())) {
            summary.append("\n📝 描述: ").append(command.getDescription());
        }
        
        if (StringUtils.isNotBlank(command.getCategory())) {
            summary.append("\n📂 分类: ").append(command.getCategory());
        }
        
        if (command.getTags() != null && !command.getTags().isEmpty()) {
            summary.append("\n🏷️ 标签: ").append(String.join(", ", command.getTags()));
        }
        
        if (command.getTransactionTime() != null) {
            summary.append("\n⏰ 时间: ").append(command.getTransactionTime().toString());
        }
        
        if (StringUtils.isNotBlank(command.getWallet())) {
            summary.append("\n💳 钱包: ").append(command.getWallet());
        }
        
        if (Boolean.TRUE.equals(command.getSplitEnabled())) {
            summary.append("\n👥 分摊: ");
            if (command.getSplitUsers() != null && !command.getSplitUsers().isEmpty()) {
                summary.append("与 ").append(String.join(", ", command.getSplitUsers())).append(" 分摊");
            } else if (command.getSplitCount() != null) {
                summary.append(command.getSplitCount()).append("人平均分摊");
            }
        }
        
        // 显示智能推荐
        if (StringUtils.isNotBlank(command.getSuggestedCategory())) {
            summary.append("\n💡 推荐分类: ").append(command.getSuggestedCategory());
        }
        
        if (command.getSuggestedTags() != null && !command.getSuggestedTags().isEmpty()) {
            summary.append("\n💡 推荐标签: ").append(String.join(", ", command.getSuggestedTags()));
        }
        
        if (command.getConfidenceScore() != null) {
            summary.append("\n📊 置信度: ").append(String.format("%.1f%%", command.getConfidenceScore()));
        }
        
        return summary.toString();
    }

    /**
     * 处理系统命令
     */
    public CommandExecutionResult processSystemCommand(String command, PlatformType platform, Long groupId, Long userId) {
        String cmd = command.toLowerCase().trim();
        
        return switch (cmd) {
            case "/help", "帮助" -> CommandExecutionResult.success(commandParser.getHelpMessage(), null);
            case "/balance", "余额" -> processBalanceCommand(groupId, userId);
            case "/history", "历史" -> processHistoryCommand(groupId, userId);
            case "/suggest", "建议" -> processSuggestCommand();
            case "/stats", "统计" -> processStatsCommand(groupId, userId);
            default -> CommandExecutionResult.failure("未知命令: " + command);
        };
    }

    /**
     * 处理余额查询命令
     */
    private CommandExecutionResult processBalanceCommand(Long groupId, Long userId) {
        // TODO: 实现余额查询逻辑
        return CommandExecutionResult.success("余额查询功能待实现", null);
    }

    /**
     * 处理历史记录命令
     */
    private CommandExecutionResult processHistoryCommand(Long groupId, Long userId) {
        try {
            List<CommandHistoryDO> history = commandHistoryService.getUserCommandHistory(userId, groupId, 10);

            if (history.isEmpty()) {
                return CommandExecutionResult.success("暂无命令历史记录", null);
            }

            StringBuilder message = new StringBuilder("📋 最近命令历史:\n\n");
            for (int i = 0; i < history.size(); i++) {
                CommandHistoryDO record = history.get(i);
                message.append(String.format("%d. %s %s\n",
                    i + 1,
                    record.getOriginalCommand(),
                    record.getSuccess() ? "✅" : "❌"
                ));

                if (record.getAmount() != null) {
                    message.append(String.format("   💰 %s%.2f %s\n",
                        record.getTransactionType() != null ? record.getTransactionType().getSymbol() : "",
                        record.getAmount(),
                        record.getCurrency() != null ? record.getCurrency() : "CNY"
                    ));
                }

                if (record.getExecutionTime() != null) {
                    message.append(String.format("   ⏰ %s\n", record.getExecutionTime().toString()));
                }

                message.append("\n");
            }

            return CommandExecutionResult.success(message.toString(), null, history);

        } catch (Exception e) {
            log.error("查询历史记录失败", e);
            return CommandExecutionResult.failure("查询历史记录失败: " + e.getMessage());
        }
    }

    /**
     * 处理建议命令
     */
    private CommandExecutionResult processSuggestCommand() {
        List<String> suggestions = commandParser.getCommandSuggestions("");
        return CommandExecutionResult.success(
            "💡 命令建议:\n" + String.join("\n", suggestions), 
            null
        );
    }

    /**
     * 处理统计命令
     */
    private CommandExecutionResult processStatsCommand(Long groupId, Long userId) {
        try {
            // 获取最近30天的统计数据
            Map<String, Object> successRate = commandHistoryService.getSuccessRateStatistics(userId, groupId, 30);
            Map<String, Object> executionTime = commandHistoryService.getAverageExecutionTime(userId, groupId, 30);
            Map<String, Object> complexity = commandHistoryService.getCommandComplexityAnalysis(userId, groupId, 30);
            List<String> mostUsed = commandHistoryService.getMostUsedCommands(userId, groupId, 5);

            StringBuilder message = new StringBuilder("📊 命令使用统计 (最近30天):\n\n");

            // 成功率统计
            message.append("✅ 成功率统计:\n");
            message.append(String.format("   总命令数: %d\n", successRate.get("totalCommands")));
            message.append(String.format("   成功: %d\n", successRate.get("successfulCommands")));
            message.append(String.format("   失败: %d\n", successRate.get("failedCommands")));
            message.append(String.format("   成功率: %.1f%%\n\n", successRate.get("successRate")));

            // 执行时间统计
            message.append("⏱️ 执行时间统计:\n");
            message.append(String.format("   平均: %.1fms\n", executionTime.get("averageExecutionTime")));
            message.append(String.format("   最快: %dms\n", executionTime.get("minExecutionTime")));
            message.append(String.format("   最慢: %dms\n\n", executionTime.get("maxExecutionTime")));

            // 复杂度分析
            message.append("🧠 命令复杂度:\n");
            message.append(String.format("   平均置信度: %.1f%%\n", complexity.get("averageConfidenceScore")));
            message.append(String.format("   简单命令: %d\n", complexity.get("simpleCommands")));
            message.append(String.format("   复杂命令: %d\n\n", complexity.get("complexCommands")));

            // 最常用命令
            if (!mostUsed.isEmpty()) {
                message.append("🔥 最常用命令:\n");
                for (int i = 0; i < mostUsed.size(); i++) {
                    message.append(String.format("   %d. %s\n", i + 1, mostUsed.get(i)));
                }
            }

            Map<String, Object> data = Map.of(
                "successRate", successRate,
                "executionTime", executionTime,
                "complexity", complexity,
                "mostUsed", mostUsed
            );

            return CommandExecutionResult.success(message.toString(), null, data);

        } catch (Exception e) {
            log.error("查询统计信息失败", e);
            return CommandExecutionResult.failure("查询统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 验证命令权限
     */
    public boolean hasPermission(String command, Long userId, Long groupId) {
        // TODO: 实现权限验证逻辑
        return true;
    }

    /**
     * 获取命令执行历史
     */
    public List<CommandExecutionResult> getExecutionHistory(Long userId, Long groupId, int limit) {
        try {
            List<CommandHistoryDO> history = commandHistoryService.getUserCommandHistory(userId, groupId, limit);
            return history.stream()
                .map(this::convertToExecutionResult)
                .toList();
        } catch (Exception e) {
            log.error("获取执行历史失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 转换历史记录为执行结果
     */
    private CommandExecutionResult convertToExecutionResult(CommandHistoryDO history) {
        CommandExecutionResult result = new CommandExecutionResult();
        result.setSuccess(history.getSuccess());
        result.setMessage(history.getSuccess() ? "执行成功" : history.getErrorMessage());
        result.setExecutionTime(history.getExecutionTime());
        result.setExecutionDuration(history.getExecutionDuration());
        result.setBusinessId(history.getBusinessId());
        result.setOriginalCommand(history.getOriginalCommand());

        // 构建ParsedCommand
        if (history.getTransactionType() != null) {
            ParsedCommand parsedCommand = new ParsedCommand();
            parsedCommand.setType(history.getTransactionType());
            parsedCommand.setAmount(history.getAmount());
            parsedCommand.setCurrency(history.getCurrency());
            parsedCommand.setDescription(history.getDescription());
            parsedCommand.setCategory(history.getCategory());
            parsedCommand.setConfidenceScore(history.getConfidenceScore());
            result.setParsedCommand(parsedCommand);
        }

        return result;
    }
}
