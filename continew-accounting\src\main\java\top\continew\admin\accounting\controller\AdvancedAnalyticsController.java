package top.continew.admin.accounting.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.accounting.model.query.DrillDownQuery;
import top.continew.admin.accounting.model.query.MultiDimensionQuery;
import top.continew.admin.accounting.model.resp.*;
import top.continew.admin.accounting.model.resp.AnalysisResponseModels.*;
import top.continew.admin.accounting.service.AdvancedAnalyticsService;
import top.continew.starter.web.model.R;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.util.List;
import java.util.Map;

/**
 * 高级数据分析控制器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "高级数据分析 API")
@RestController
@RequestMapping("/accounting/advanced-analytics")
@RequiredArgsConstructor
@Validated
public class AdvancedAnalyticsController {

    private final AdvancedAnalyticsService advancedAnalyticsService;

    @Operation(summary = "多维数据分析", description = "执行多维数据分析，支持多个维度和度量的交叉分析")
    @PostMapping("/multi-dimension")
    public R<MultiDimensionAnalysisResp> getMultiDimensionAnalysis(@Valid @RequestBody MultiDimensionQuery query) {
        return R.ok(advancedAnalyticsService.getMultiDimensionAnalysis(query));
    }

    @Operation(summary = "数据钻取分析", description = "执行数据钻取分析，支持向上、向下、横向钻取")
    @PostMapping("/drill-down")
    public R<DrillDownAnalysisResp> getDrillDownAnalysis(@Valid @RequestBody DrillDownQuery query) {
        return R.ok(advancedAnalyticsService.getDrillDownAnalysis(query));
    }

    @Operation(summary = "数据立方体", description = "构建数据立方体，支持OLAP分析")
    @GetMapping("/data-cube")
    public R<DataCubeResp> getDataCube(
            @Parameter(description = "群组ID", example = "1") @RequestParam @NotNull Long groupId,
            @Parameter(description = "维度列表", example = "category,time,member") @RequestParam List<String> dimensions,
            @Parameter(description = "度量列表", example = "amount,count") @RequestParam List<String> measures) {
        return R.ok(advancedAnalyticsService.getDataCube(groupId, dimensions, measures));
    }

    @Operation(summary = "关联分析", description = "执行关联分析，发现数据间的关联规则")
    @GetMapping("/correlation")
    public R<CorrelationAnalysisResp> getCorrelationAnalysis(
            @Parameter(description = "群组ID", example = "1") @RequestParam @NotNull Long groupId,
            @Parameter(description = "分析类型", example = "category_correlation") @RequestParam @NotBlank String analysisType) {
        return R.ok(advancedAnalyticsService.getCorrelationAnalysis(groupId, analysisType));
    }

    @Operation(summary = "异常检测", description = "执行异常检测，识别异常数据点")
    @GetMapping("/anomaly-detection")
    public R<AnomalyDetectionResp> getAnomalyDetection(
            @Parameter(description = "群组ID", example = "1") @RequestParam @NotNull Long groupId,
            @Parameter(description = "检测类型", example = "outlier_detection") @RequestParam @NotBlank String detectionType) {
        return R.ok(advancedAnalyticsService.getAnomalyDetection(groupId, detectionType));
    }

    @Operation(summary = "预测分析", description = "执行预测分析，预测未来趋势")
    @GetMapping("/prediction")
    public R<PredictionAnalysisResp> getPredictionAnalysis(
            @Parameter(description = "群组ID", example = "1") @RequestParam @NotNull Long groupId,
            @Parameter(description = "预测类型", example = "expense_forecast") @RequestParam @NotBlank String predictionType,
            @Parameter(description = "预测期数", example = "12") @RequestParam @Positive int periods) {
        return R.ok(advancedAnalyticsService.getPredictionAnalysis(groupId, predictionType, periods));
    }

    @Operation(summary = "聚合分析", description = "执行聚合分析，按指定字段分组聚合")
    @GetMapping("/aggregation")
    public R<AggregationAnalysisResp> getAggregationAnalysis(
            @Parameter(description = "群组ID", example = "1") @RequestParam @NotNull Long groupId,
            @Parameter(description = "聚合类型", example = "sum") @RequestParam @NotBlank String aggregationType,
            @Parameter(description = "分组字段", example = "category,month") @RequestParam List<String> groupByFields) {
        return R.ok(advancedAnalyticsService.getAggregationAnalysis(groupId, aggregationType, groupByFields));
    }

    @Operation(summary = "同期对比分析", description = "执行同期对比分析，支持同比、环比")
    @GetMapping("/period-comparison")
    public R<PeriodComparisonResp> getPeriodComparison(
            @Parameter(description = "群组ID", example = "1") @RequestParam @NotNull Long groupId,
            @Parameter(description = "对比类型", example = "year_over_year") @RequestParam @NotBlank String compareType,
            @Parameter(description = "对比周期", example = "monthly") @RequestParam @NotBlank String period) {
        return R.ok(advancedAnalyticsService.getPeriodComparison(groupId, compareType, period));
    }

    @Operation(summary = "漏斗分析", description = "执行漏斗分析，分析转化流程")
    @GetMapping("/funnel")
    public R<FunnelAnalysisResp> getFunnelAnalysis(
            @Parameter(description = "群组ID", example = "1") @RequestParam @NotNull Long groupId,
            @Parameter(description = "漏斗步骤", example = "step1,step2,step3") @RequestParam List<String> funnelSteps) {
        return R.ok(advancedAnalyticsService.getFunnelAnalysis(groupId, funnelSteps));
    }

    @Operation(summary = "队列分析", description = "执行队列分析，分析用户行为队列")
    @GetMapping("/cohort")
    public R<CohortAnalysisResp> getCohortAnalysis(
            @Parameter(description = "群组ID", example = "1") @RequestParam @NotNull Long groupId,
            @Parameter(description = "队列类型", example = "monthly_cohort") @RequestParam @NotBlank String cohortType) {
        return R.ok(advancedAnalyticsService.getCohortAnalysis(groupId, cohortType));
    }

    @Operation(summary = "实时分析", description = "获取实时分析数据")
    @GetMapping("/real-time")
    public R<RealTimeAnalysisResp> getRealTimeAnalysis(
            @Parameter(description = "群组ID", example = "1") @RequestParam @NotNull Long groupId,
            @Parameter(description = "指标列表", example = "total_amount,transaction_count") @RequestParam List<String> metrics) {
        return R.ok(advancedAnalyticsService.getRealTimeAnalysis(groupId, metrics));
    }

    @Operation(summary = "自定义分析", description = "执行自定义分析，支持灵活配置")
    @PostMapping("/custom")
    public R<CustomAnalysisResp> getCustomAnalysis(
            @Parameter(description = "群组ID", example = "1") @RequestParam @NotNull Long groupId,
            @Parameter(description = "分析配置") @RequestBody Map<String, Object> analysisConfig) {
        return R.ok(advancedAnalyticsService.getCustomAnalysis(groupId, analysisConfig));
    }

    @Operation(summary = "导出分析结果", description = "导出分析结果到文件")
    @GetMapping("/export/{analysisId}")
    public R<String> exportAnalysisResult(
            @Parameter(description = "分析ID", example = "analysis_123456") @PathVariable @NotBlank String analysisId,
            @Parameter(description = "导出格式", example = "excel") @RequestParam @NotBlank String format) {
        return R.ok(advancedAnalyticsService.exportAnalysisResult(analysisId, format));
    }

    @Operation(summary = "保存分析配置", description = "保存分析配置模板")
    @PostMapping("/config")
    public R<String> saveAnalysisConfig(
            @Parameter(description = "群组ID", example = "1") @RequestParam @NotNull Long groupId,
            @Parameter(description = "配置名称", example = "月度收支分析") @RequestParam @NotBlank String configName,
            @Parameter(description = "分析配置") @RequestBody Map<String, Object> analysisConfig) {
        return R.ok(advancedAnalyticsService.saveAnalysisConfig(groupId, configName, analysisConfig));
    }

    @Operation(summary = "获取分析配置列表", description = "获取已保存的分析配置")
    @GetMapping("/config")
    public R<List<AnalysisConfigResp>> getAnalysisConfigs(
            @Parameter(description = "群组ID", example = "1") @RequestParam @NotNull Long groupId) {
        return R.ok(advancedAnalyticsService.getAnalysisConfigs(groupId));
    }

    @Operation(summary = "删除分析配置", description = "删除指定的分析配置")
    @DeleteMapping("/config/{configId}")
    public R<Void> deleteAnalysisConfig(
            @Parameter(description = "配置ID", example = "config_123456") @PathVariable @NotBlank String configId) {
        advancedAnalyticsService.deleteAnalysisConfig(configId);
        return R.ok();
    }
}
