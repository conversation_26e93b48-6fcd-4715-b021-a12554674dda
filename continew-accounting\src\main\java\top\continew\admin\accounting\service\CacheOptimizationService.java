package top.continew.admin.accounting.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import top.continew.admin.accounting.model.req.CacheConfigReq;
import top.continew.admin.accounting.model.req.CacheQueryReq;
import top.continew.admin.accounting.model.resp.CacheConfigResp;
import top.continew.admin.accounting.model.resp.CacheStatisticsResp;

import java.util.List;
import java.util.Map;

/**
 * 缓存优化服务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface CacheOptimizationService {

    // ==================== 缓存配置管理 ====================

    /**
     * 创建缓存配置
     *
     * @param configReq 配置请求
     * @return 配置响应
     */
    CacheConfigResp createCacheConfig(CacheConfigReq configReq);

    /**
     * 更新缓存配置
     *
     * @param configId  配置ID
     * @param configReq 配置请求
     * @return 配置响应
     */
    CacheConfigResp updateCacheConfig(Long configId, CacheConfigReq configReq);

    /**
     * 删除缓存配置
     *
     * @param configId 配置ID
     * @return 是否成功
     */
    Boolean deleteCacheConfig(Long configId);

    /**
     * 获取缓存配置详情
     *
     * @param configId 配置ID
     * @return 配置响应
     */
    CacheConfigResp getCacheConfig(Long configId);

    /**
     * 分页查询缓存配置
     *
     * @param queryReq 查询请求
     * @return 分页结果
     */
    IPage<CacheConfigResp> pageCacheConfigs(CacheQueryReq queryReq);

    /**
     * 查询缓存配置列表
     *
     * @param queryReq 查询请求
     * @return 配置列表
     */
    List<CacheConfigResp> listCacheConfigs(CacheQueryReq queryReq);

    // ==================== 缓存操作管理 ====================

    /**
     * 应用缓存配置
     *
     * @param configId 配置ID
     * @return 是否成功
     */
    Boolean applyCacheConfig(Long configId);

    /**
     * 批量应用缓存配置
     *
     * @param configIds 配置ID列表
     * @return 应用结果
     */
    Map<Long, Boolean> batchApplyCacheConfigs(List<Long> configIds);

    /**
     * 预热缓存
     *
     * @param cacheName 缓存名称
     * @return 是否成功
     */
    Boolean preloadCache(String cacheName);

    /**
     * 批量预热缓存
     *
     * @param cacheNames 缓存名称列表
     * @return 预热结果
     */
    Map<String, Boolean> batchPreloadCaches(List<String> cacheNames);

    /**
     * 刷新缓存
     *
     * @param cacheName 缓存名称
     * @param cacheKey  缓存键（可选）
     * @return 是否成功
     */
    Boolean refreshCache(String cacheName, String cacheKey);

    /**
     * 清除缓存
     *
     * @param cacheName 缓存名称
     * @param cacheKey  缓存键（可选）
     * @return 是否成功
     */
    Boolean clearCache(String cacheName, String cacheKey);

    /**
     * 批量清除缓存
     *
     * @param cacheName 缓存名称
     * @param cacheKeys 缓存键列表
     * @return 清除结果
     */
    Map<String, Boolean> batchClearCache(String cacheName, List<String> cacheKeys);

    /**
     * 清除所有缓存
     *
     * @return 是否成功
     */
    Boolean clearAllCaches();

    // ==================== 缓存监控管理 ====================

    /**
     * 获取缓存统计信息
     *
     * @param groupId 群组ID
     * @return 统计信息
     */
    CacheStatisticsResp getCacheStatistics(Long groupId);

    /**
     * 获取缓存性能指标
     *
     * @param cacheName 缓存名称
     * @param hours     时间范围（小时）
     * @return 性能指标
     */
    List<Map<String, Object>> getCachePerformanceMetrics(String cacheName, Integer hours);

    /**
     * 获取热点数据
     *
     * @param cacheName 缓存名称
     * @param limit     限制数量
     * @return 热点数据
     */
    List<Map<String, Object>> getHotspotData(String cacheName, Integer limit);

    /**
     * 获取缓存健康状态
     *
     * @return 健康状态
     */
    Map<String, Object> getCacheHealthStatus();

    /**
     * 检测缓存异常
     *
     * @return 异常信息
     */
    List<Map<String, Object>> detectCacheAnomalies();

    // ==================== 缓存优化建议 ====================

    /**
     * 获取缓存优化建议
     *
     * @param groupId 群组ID
     * @return 优化建议
     */
    List<Map<String, Object>> getCacheOptimizationSuggestions(Long groupId);

    /**
     * 分析缓存使用模式
     *
     * @param cacheName 缓存名称
     * @param days      分析天数
     * @return 使用模式分析
     */
    Map<String, Object> analyzeCacheUsagePattern(String cacheName, Integer days);

    /**
     * 预测缓存容量需求
     *
     * @param cacheName 缓存名称
     * @param days      预测天数
     * @return 容量预测
     */
    Map<String, Object> predictCacheCapacity(String cacheName, Integer days);

    // ==================== 缓存工具方法 ====================

    /**
     * 测试缓存连接
     *
     * @param configId 配置ID
     * @return 测试结果
     */
    Map<String, Object> testCacheConnection(Long configId);

    /**
     * 导出缓存配置
     *
     * @param configIds 配置ID列表
     * @return 配置数据
     */
    Map<String, Object> exportCacheConfigs(List<Long> configIds);

    /**
     * 导入缓存配置
     *
     * @param configData 配置数据
     * @return 导入结果
     */
    Map<String, Object> importCacheConfigs(Map<String, Object> configData);

    /**
     * 验证缓存配置
     *
     * @param configReq 配置请求
     * @return 验证结果
     */
    Map<String, Object> validateCacheConfig(CacheConfigReq configReq);

    /**
     * 获取缓存键列表
     *
     * @param cacheName 缓存名称
     * @param pattern   键模式
     * @param limit     限制数量
     * @return 缓存键列表
     */
    List<String> getCacheKeys(String cacheName, String pattern, Integer limit);

    /**
     * 获取缓存值
     *
     * @param cacheName 缓存名称
     * @param cacheKey  缓存键
     * @return 缓存值
     */
    Object getCacheValue(String cacheName, String cacheKey);

    /**
     * 设置缓存值
     *
     * @param cacheName  缓存名称
     * @param cacheKey   缓存键
     * @param cacheValue 缓存值
     * @param expireTime 过期时间（秒）
     * @return 是否成功
     */
    Boolean setCacheValue(String cacheName, String cacheKey, Object cacheValue, Long expireTime);

}
