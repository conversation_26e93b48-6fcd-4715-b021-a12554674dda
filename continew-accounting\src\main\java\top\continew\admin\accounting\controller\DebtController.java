package top.continew.admin.accounting.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.accounting.model.query.DebtQuery;
import top.continew.admin.accounting.model.req.DebtCreateReq;
import top.continew.admin.accounting.model.req.DebtPaymentReq;
import top.continew.admin.accounting.model.req.DebtUpdateReq;
import top.continew.admin.accounting.model.resp.DebtDetailResp;
import top.continew.admin.accounting.model.resp.DebtListResp;
import top.continew.admin.accounting.service.DebtService;
import top.continew.admin.accounting.mapper.DebtMapper.DebtStatisticsResp;
import top.continew.starter.crud.annotation.CrudRequestMapping;
import top.continew.starter.crud.base.BaseController;
import top.continew.starter.crud.enums.Api;
import top.continew.starter.web.model.R;

import java.math.BigDecimal;
import java.util.List;

/**
 * 债务管理 API
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "债务管理 API")
@RestController
@RequiredArgsConstructor
@RequestMapping("/accounting/debt")
@Validated
public class DebtController extends BaseController<DebtService, DebtListResp, DebtDetailResp, DebtQuery, DebtCreateReq> {

    /**
     * 启用标准 CRUD API
     */
    @CrudRequestMapping(value = "", api = {Api.PAGE, Api.LIST, Api.GET, Api.CREATE, Api.UPDATE, Api.BATCH_DELETE, Api.EXPORT, Api.DICT})
    @SaCheckPermission("accounting:debt")
    public void crud() {
        // 标准 CRUD 操作由 @CrudRequestMapping 自动生成
    }

    /**
     * 更新债务信息
     */
    @Operation(summary = "更新债务信息", description = "更新债务信息")
    @PutMapping("/{id}")
    @SaCheckPermission("accounting:debt:update")
    public R<Void> update(@Valid @RequestBody DebtUpdateReq req, 
                         @Parameter(description = "债务ID", example = "1") @PathVariable Long id) {
        baseService.update(req, id);
        return R.ok();
    }

    /**
     * 还款
     */
    @Operation(summary = "还款", description = "创建还款记录")
    @PostMapping("/payment")
    @SaCheckPermission("accounting:debt:payment")
    public R<Long> makePayment(@Valid @RequestBody DebtPaymentReq req) {
        Long paymentId = baseService.makePayment(req);
        return R.ok(paymentId);
    }

    /**
     * 确认还款
     */
    @Operation(summary = "确认还款", description = "确认还款记录")
    @PutMapping("/payment/{paymentId}/confirm")
    @SaCheckPermission("accounting:debt:payment:confirm")
    public R<Void> confirmPayment(@Parameter(description = "还款记录ID", example = "1") @PathVariable Long paymentId) {
        baseService.confirmPayment(paymentId);
        return R.ok();
    }

    /**
     * 取消还款
     */
    @Operation(summary = "取消还款", description = "取消还款记录")
    @PutMapping("/payment/{paymentId}/cancel")
    @SaCheckPermission("accounting:debt:payment:cancel")
    public R<Void> cancelPayment(@Parameter(description = "还款记录ID", example = "1") @PathVariable Long paymentId) {
        baseService.cancelPayment(paymentId);
        return R.ok();
    }

    /**
     * 获取用户债务列表
     */
    @Operation(summary = "获取用户债务列表", description = "获取用户相关的债务列表")
    @GetMapping("/user/{userId}")
    @SaCheckPermission("accounting:debt:list")
    public R<List<DebtListResp>> getUserDebts(@Parameter(description = "用户ID", example = "1") @PathVariable Long userId,
                                             @Parameter(description = "群组ID", example = "1") @RequestParam Long groupId,
                                             @Parameter(description = "状态", example = "ACTIVE") @RequestParam(required = false) String status) {
        List<DebtListResp> debts = baseService.getUserDebts(userId, groupId, status);
        return R.ok(debts);
    }

    /**
     * 获取群组债务统计
     */
    @Operation(summary = "获取群组债务统计", description = "获取群组债务统计信息")
    @GetMapping("/statistics/group/{groupId}")
    @SaCheckPermission("accounting:debt:statistics")
    public R<DebtStatisticsResp> getGroupStatistics(@Parameter(description = "群组ID", example = "1") @PathVariable Long groupId,
                                                    @Parameter(description = "状态", example = "ACTIVE") @RequestParam(required = false) String status) {
        DebtStatisticsResp statistics = baseService.getGroupStatistics(groupId, status);
        return R.ok(statistics);
    }

    /**
     * 获取用户债务统计
     */
    @Operation(summary = "获取用户债务统计", description = "获取用户债务统计信息")
    @GetMapping("/statistics/user/{userId}")
    @SaCheckPermission("accounting:debt:statistics")
    public R<DebtStatisticsResp> getUserStatistics(@Parameter(description = "用户ID", example = "1") @PathVariable Long userId,
                                                   @Parameter(description = "群组ID", example = "1") @RequestParam Long groupId,
                                                   @Parameter(description = "状态", example = "ACTIVE") @RequestParam(required = false) String status) {
        DebtStatisticsResp statistics = baseService.getUserStatistics(userId, groupId, status);
        return R.ok(statistics);
    }

    /**
     * 获取即将到期的债务
     */
    @Operation(summary = "获取即将到期的债务", description = "获取即将到期的债务列表")
    @GetMapping("/due-soon/{groupId}")
    @SaCheckPermission("accounting:debt:list")
    public R<List<DebtListResp>> getDueSoonDebts(@Parameter(description = "群组ID", example = "1") @PathVariable Long groupId,
                                                 @Parameter(description = "天数", example = "7") @RequestParam(defaultValue = "7") Integer days) {
        List<DebtListResp> debts = baseService.getDueSoonDebts(groupId, days);
        return R.ok(debts);
    }

    /**
     * 获取逾期债务
     */
    @Operation(summary = "获取逾期债务", description = "获取逾期债务列表")
    @GetMapping("/overdue/{groupId}")
    @SaCheckPermission("accounting:debt:list")
    public R<List<DebtListResp>> getOverdueDebts(@Parameter(description = "群组ID", example = "1") @PathVariable Long groupId) {
        List<DebtListResp> debts = baseService.getOverdueDebts(groupId);
        return R.ok(debts);
    }

    /**
     * 结清债务
     */
    @Operation(summary = "结清债务", description = "结清债务")
    @PutMapping("/{id}/settle")
    @SaCheckPermission("accounting:debt:settle")
    public R<Void> settleDebt(@Parameter(description = "债务ID", example = "1") @PathVariable Long id) {
        baseService.settleDebt(id);
        return R.ok();
    }

    /**
     * 取消债务
     */
    @Operation(summary = "取消债务", description = "取消债务")
    @PutMapping("/{id}/cancel")
    @SaCheckPermission("accounting:debt:cancel")
    public R<Void> cancelDebt(@Parameter(description = "债务ID", example = "1") @PathVariable Long id) {
        baseService.cancelDebt(id);
        return R.ok();
    }

    /**
     * 合并债务
     */
    @Operation(summary = "合并债务", description = "合并多个债务到目标债务")
    @PostMapping("/merge")
    @SaCheckPermission("accounting:debt:merge")
    public R<Void> mergeDebts(@Parameter(description = "源债务ID列表") @RequestParam List<Long> sourceIds,
                             @Parameter(description = "目标债务ID", example = "1") @RequestParam Long targetId) {
        baseService.mergeDebts(sourceIds, targetId);
        return R.ok();
    }

    /**
     * 分割债务
     */
    @Operation(summary = "分割债务", description = "分割债务")
    @PostMapping("/{id}/split")
    @SaCheckPermission("accounting:debt:split")
    public R<Long> splitDebt(@Parameter(description = "债务ID", example = "1") @PathVariable Long id,
                            @Parameter(description = "分割金额", example = "500.00") @RequestParam BigDecimal amount) {
        Long newDebtId = baseService.splitDebt(id, amount);
        return R.ok(newDebtId);
    }

    /**
     * 转移债务
     */
    @Operation(summary = "转移债务", description = "转移债务到新的债权人或债务人")
    @PutMapping("/{id}/transfer")
    @SaCheckPermission("accounting:debt:transfer")
    public R<Void> transferDebt(@Parameter(description = "债务ID", example = "1") @PathVariable Long id,
                               @Parameter(description = "新债权人ID", example = "2") @RequestParam(required = false) Long newCreditorId,
                               @Parameter(description = "新债务人ID", example = "3") @RequestParam(required = false) Long newDebtorId) {
        baseService.transferDebt(id, newCreditorId, newDebtorId);
        return R.ok();
    }

    /**
     * 计算利息
     */
    @Operation(summary = "计算利息", description = "计算债务利息")
    @GetMapping("/{id}/interest")
    @SaCheckPermission("accounting:debt:interest")
    public R<BigDecimal> calculateInterest(@Parameter(description = "债务ID", example = "1") @PathVariable Long id) {
        BigDecimal interest = baseService.calculateInterest(id);
        return R.ok(interest);
    }

    /**
     * 生成还款计划
     */
    @Operation(summary = "生成还款计划", description = "生成还款计划")
    @PostMapping("/{id}/payment-plan")
    @SaCheckPermission("accounting:debt:payment-plan")
    public R<Void> generatePaymentPlan(@Parameter(description = "债务ID", example = "1") @PathVariable Long id,
                                      @Parameter(description = "期数", example = "12") @RequestParam Integer periods,
                                      @Parameter(description = "还款频率", example = "MONTHLY") @RequestParam String paymentFrequency) {
        baseService.generatePaymentPlan(id, periods, paymentFrequency);
        return R.ok();
    }

    /**
     * 发送还款提醒
     */
    @Operation(summary = "发送还款提醒", description = "发送还款提醒")
    @PostMapping("/reminder/{groupId}")
    @SaCheckPermission("accounting:debt:reminder")
    public R<Void> sendPaymentReminder(@Parameter(description = "群组ID", example = "1") @PathVariable Long groupId,
                                      @Parameter(description = "提前天数", example = "3") @RequestParam(defaultValue = "3") Integer days) {
        baseService.sendPaymentReminder(groupId, days);
        return R.ok();
    }

    /**
     * 自动结算债务
     */
    @Operation(summary = "自动结算债务", description = "自动结算群组内的债务")
    @PostMapping("/auto-settle/{groupId}")
    @SaCheckPermission("accounting:debt:auto-settle")
    public R<Void> autoSettleDebts(@Parameter(description = "群组ID", example = "1") @PathVariable Long groupId) {
        baseService.autoSettleDebts(groupId);
        return R.ok();
    }

    /**
     * 获取债务关系图
     */
    @Operation(summary = "获取债务关系图", description = "获取群组债务关系图数据")
    @GetMapping("/relationship-graph/{groupId}")
    @SaCheckPermission("accounting:debt:graph")
    public R<Object> getDebtRelationshipGraph(@Parameter(description = "群组ID", example = "1") @PathVariable Long groupId) {
        Object graph = baseService.getDebtRelationshipGraph(groupId);
        return R.ok(graph);
    }
}
