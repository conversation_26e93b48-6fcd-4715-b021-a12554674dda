package top.continew.admin.api.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.accounting.model.query.GroupQuery;
import top.continew.admin.api.model.req.ApiGroupCreateReq;
import top.continew.admin.api.model.req.ApiGroupUpdateReq;
import top.continew.admin.api.model.resp.ApiGroupResp;
import top.continew.admin.api.service.ApiGroupService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.web.model.R;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 群组管理 API
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "群组管理 API", description = "提供群组的创建、查询、更新、删除等操作")
@RestController
@RequestMapping("/api/v1/groups")
@RequiredArgsConstructor
@Validated
public class ApiGroupController {

    private final ApiGroupService apiGroupService;

    @Operation(summary = "分页查询群组", description = "分页查询群组列表")
    @GetMapping
    public R<PageResp<ApiGroupResp>> page(
        @Parameter(description = "查询条件") GroupQuery query,
        @Parameter(description = "分页参数") PageQuery pageQuery
    ) {
        PageResp<ApiGroupResp> result = apiGroupService.page(query, pageQuery);
        return R.ok(result);
    }

    @Operation(summary = "查询群组列表", description = "查询所有群组列表")
    @GetMapping("/list")
    public R<List<ApiGroupResp>> list(@Parameter(description = "查询条件") GroupQuery query) {
        List<ApiGroupResp> result = apiGroupService.list(query);
        return R.ok(result);
    }

    @Operation(summary = "查询群组详情", description = "根据ID查询群组详细信息")
    @GetMapping("/{id}")
    public R<ApiGroupResp> get(@PathVariable Long id) {
        ApiGroupResp result = apiGroupService.get(id);
        return R.ok(result);
    }

    @Operation(summary = "创建群组", description = "创建新的群组")
    @PostMapping
    public R<Long> create(@RequestBody @Valid ApiGroupCreateReq req) {
        Long id = apiGroupService.create(req);
        return R.ok(id);
    }

    @Operation(summary = "更新群组", description = "更新群组信息")
    @PutMapping("/{id}")
    public R<Void> update(
        @PathVariable Long id,
        @RequestBody @Valid ApiGroupUpdateReq req
    ) {
        apiGroupService.update(req, id);
        return R.ok();
    }

    @Operation(summary = "删除群组", description = "删除指定群组")
    @DeleteMapping("/{id}")
    public R<Void> delete(@PathVariable Long id) {
        apiGroupService.delete(List.of(id));
        return R.ok();
    }

    @Operation(summary = "批量删除群组", description = "批量删除多个群组")
    @DeleteMapping("/batch")
    public R<Void> batchDelete(@RequestBody List<Long> ids) {
        apiGroupService.delete(ids);
        return R.ok();
    }

    @Operation(summary = "加入群组", description = "用户加入指定群组")
    @PostMapping("/{id}/join")
    public R<Void> joinGroup(
        @PathVariable Long id,
        @Parameter(description = "邀请码") @RequestParam(required = false) String inviteCode
    ) {
        apiGroupService.joinGroup(id, inviteCode);
        return R.ok();
    }

    @Operation(summary = "离开群组", description = "用户离开指定群组")
    @PostMapping("/{id}/leave")
    public R<Void> leaveGroup(@PathVariable Long id) {
        apiGroupService.leaveGroup(id);
        return R.ok();
    }

    @Operation(summary = "获取群组成员", description = "获取群组成员列表")
    @GetMapping("/{id}/members")
    public R<List<ApiGroupResp.MemberInfo>> getMembers(@PathVariable Long id) {
        List<ApiGroupResp.MemberInfo> members = apiGroupService.getMembers(id);
        return R.ok(members);
    }

    @Operation(summary = "添加群组成员", description = "添加用户到群组")
    @PostMapping("/{id}/members")
    public R<Void> addMember(
        @PathVariable Long id,
        @Parameter(description = "用户ID") @RequestParam Long userId,
        @Parameter(description = "角色") @RequestParam(defaultValue = "MEMBER") String role
    ) {
        apiGroupService.addMember(id, userId, role);
        return R.ok();
    }

    @Operation(summary = "移除群组成员", description = "从群组中移除用户")
    @DeleteMapping("/{id}/members/{userId}")
    public R<Void> removeMember(
        @PathVariable Long id,
        @PathVariable Long userId
    ) {
        apiGroupService.removeMember(id, userId);
        return R.ok();
    }

    @Operation(summary = "更新成员角色", description = "更新群组成员的角色")
    @PutMapping("/{id}/members/{userId}/role")
    public R<Void> updateMemberRole(
        @PathVariable Long id,
        @PathVariable Long userId,
        @Parameter(description = "新角色") @RequestParam String role
    ) {
        apiGroupService.updateMemberRole(id, userId, role);
        return R.ok();
    }

    @Operation(summary = "生成邀请码", description = "生成群组邀请码")
    @PostMapping("/{id}/invite-code")
    public R<String> generateInviteCode(
        @PathVariable Long id,
        @Parameter(description = "有效期(小时)") @RequestParam(defaultValue = "24") int expireHours
    ) {
        String inviteCode = apiGroupService.generateInviteCode(id, expireHours);
        return R.ok(inviteCode);
    }

    @Operation(summary = "获取群组统计", description = "获取群组统计信息")
    @GetMapping("/{id}/statistics")
    public R<ApiGroupResp.GroupStatistics> getStatistics(@PathVariable Long id) {
        ApiGroupResp.GroupStatistics statistics = apiGroupService.getStatistics(id);
        return R.ok(statistics);
    }

    @Operation(summary = "获取群组设置", description = "获取群组配置设置")
    @GetMapping("/{id}/settings")
    public R<ApiGroupResp.GroupSettings> getSettings(@PathVariable Long id) {
        ApiGroupResp.GroupSettings settings = apiGroupService.getSettings(id);
        return R.ok(settings);
    }

    @Operation(summary = "更新群组设置", description = "更新群组配置设置")
    @PutMapping("/{id}/settings")
    public R<Void> updateSettings(
        @PathVariable Long id,
        @RequestBody @Valid ApiGroupResp.GroupSettings settings
    ) {
        apiGroupService.updateSettings(id, settings);
        return R.ok();
    }

    @Operation(summary = "搜索群组", description = "根据关键词搜索群组")
    @GetMapping("/search")
    public R<List<ApiGroupResp>> search(
        @Parameter(description = "搜索关键词") @RequestParam String keyword,
        @Parameter(description = "限制数量") @RequestParam(defaultValue = "10") int limit
    ) {
        List<ApiGroupResp> result = apiGroupService.search(keyword, limit);
        return R.ok(result);
    }

    @Operation(summary = "获取用户群组", description = "获取当前用户所属的群组列表")
    @GetMapping("/my")
    public R<List<ApiGroupResp>> getMyGroups() {
        List<ApiGroupResp> result = apiGroupService.getMyGroups();
        return R.ok(result);
    }

    @Operation(summary = "检查群组权限", description = "检查用户在群组中的权限")
    @GetMapping("/{id}/permissions")
    public R<List<String>> checkPermissions(@PathVariable Long id) {
        List<String> permissions = apiGroupService.checkPermissions(id);
        return R.ok(permissions);
    }

    @Operation(summary = "获取群组活动日志", description = "获取群组活动日志")
    @GetMapping("/{id}/activity-logs")
    public R<PageResp<ApiGroupResp.ActivityLog>> getActivityLogs(
        @PathVariable Long id,
        @Parameter(description = "分页参数") PageQuery pageQuery
    ) {
        PageResp<ApiGroupResp.ActivityLog> result = apiGroupService.getActivityLogs(id, pageQuery);
        return R.ok(result);
    }

    @Operation(summary = "导出群组数据", description = "导出群组相关数据")
    @GetMapping("/{id}/export")
    public R<String> exportData(
        @PathVariable Long id,
        @Parameter(description = "导出格式") @RequestParam(defaultValue = "excel") String format,
        @Parameter(description = "数据类型") @RequestParam(defaultValue = "transactions") String dataType
    ) {
        String downloadUrl = apiGroupService.exportData(id, format, dataType);
        return R.ok(downloadUrl);
    }
}
