/*
 * Copyright (c) 2022-present <PERSON>7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.system.api;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import top.continew.admin.common.api.system.RoleMenuApi;
import top.continew.admin.system.model.entity.RoleMenuDO;
import top.continew.admin.system.service.RoleMenuService;
import top.continew.starter.core.util.CollUtils;

import java.util.List;
import java.util.Set;

/**
 * 角色和菜单关联业务 API 实现
 *
 * <AUTHOR>
 * @since 2025/7/26 9:39
 */
@Service
@RequiredArgsConstructor
public class RoleMenuApiImpl implements RoleMenuApi {

    private final RoleMenuService baseService;

    @Override
    public Set<Long> listRoleIdByNotInMenuIds(List<Long> menuIds) {
        List<RoleMenuDO> roleMenuList = baseService.lambdaQuery()
            .select(RoleMenuDO::getRoleId)
            .notIn(RoleMenuDO::getMenuId, menuIds)
            .list();
        return CollUtils.mapToSet(roleMenuList, RoleMenuDO::getRoleId);
    }

    @Override
    public List<Long> listMenuIdByRoleIds(List<Long> roleIds) {
        return baseService.listMenuIdByRoleIds(roleIds);
    }

    @Override
    public void deleteByNotInMenuIds(List<Long> menuIds) {
        baseService.lambdaUpdate().notIn(RoleMenuDO::getMenuId, menuIds).remove();
    }

    @Override
    public boolean add(List<Long> menuIds, Long roleId) {
        return baseService.add(menuIds, roleId);
    }
}
