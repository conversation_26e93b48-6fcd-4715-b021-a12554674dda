<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.accounting.mapper.GoogleSheetsConfigMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="top.continew.admin.accounting.model.entity.GoogleSheetsConfig">
        <id column="id" property="id" />
        <result column="group_id" property="groupId" />
        <result column="config_name" property="configName" />
        <result column="config_description" property="configDescription" />
        <result column="spreadsheet_id" property="spreadsheetId" />
        <result column="spreadsheet_name" property="spreadsheetName" />
        <result column="sheet_name" property="sheetName" />
        <result column="sync_direction" property="syncDirection" />
        <result column="sync_mode" property="syncMode" />
        <result column="field_mapping_json" property="fieldMappingJson" />
        <result column="filter_condition_json" property="filterConditionJson" />
        <result column="sync_settings_json" property="syncSettingsJson" />
        <result column="auth_type" property="authType" />
        <result column="auth_config_json" property="authConfigJson" />
        <result column="auth_status" property="authStatus" />
        <result column="token_expires_at" property="tokenExpiresAt" />
        <result column="enabled" property="enabled" />
        <result column="config_status" property="configStatus" />
        <result column="config_version" property="configVersion" />
        <result column="version_notes" property="versionNotes" />
        <result column="last_sync_time" property="lastSyncTime" />
        <result column="last_sync_status" property="lastSyncStatus" />
        <result column="last_sync_id" property="lastSyncId" />
        <result column="last_sync_message" property="lastSyncMessage" />
        <result column="total_sync_count" property="totalSyncCount" />
        <result column="success_sync_count" property="successSyncCount" />
        <result column="failed_sync_count" property="failedSyncCount" />
        <result column="success_rate" property="successRate" />
        <result column="total_processed_records" property="totalProcessedRecords" />
        <result column="total_success_records" property="totalSuccessRecords" />
        <result column="total_failed_records" property="totalFailedRecords" />
        <result column="avg_sync_duration" property="avgSyncDuration" />
        <result column="max_sync_duration" property="maxSyncDuration" />
        <result column="min_sync_duration" property="minSyncDuration" />
        <result column="recent_sync_count" property="recentSyncCount" />
        <result column="recent_success_rate" property="recentSuccessRate" />
        <result column="custom_tags_json" property="customTagsJson" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_time" property="createTime" />
        <result column="update_user_id" property="updateUserId" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 基础查询 -->
    <select id="selectByConfigNameAndGroupId" resultMap="BaseResultMap">
        SELECT * FROM acc_google_sheets_config
        WHERE config_name = #{configName} AND group_id = #{groupId}
        LIMIT 1
    </select>

    <select id="selectBySpreadsheetIdAndGroupId" resultMap="BaseResultMap">
        SELECT * FROM acc_google_sheets_config
        WHERE spreadsheet_id = #{spreadsheetId} AND group_id = #{groupId}
        LIMIT 1
    </select>

    <select id="selectByGroupId" resultMap="BaseResultMap">
        SELECT * FROM acc_google_sheets_config
        WHERE group_id = #{groupId}
        ORDER BY update_time DESC
    </select>

    <select id="selectEnabledByGroupId" resultMap="BaseResultMap">
        SELECT * FROM acc_google_sheets_config
        WHERE group_id = #{groupId} AND enabled = 1 AND config_status = 'ACTIVE'
        ORDER BY config_name ASC
    </select>

    <select id="selectScheduledConfigs" resultMap="BaseResultMap">
        SELECT * FROM acc_google_sheets_config
        WHERE enabled = 1 
        AND config_status = 'ACTIVE'
        AND sync_mode = 'SCHEDULED'
        AND auth_status = 'VALID'
        AND (last_sync_time IS NULL OR last_sync_time &lt; #{currentTime})
        ORDER BY last_sync_time ASC
    </select>

    <!-- 统计查询 -->
    <select id="countConfigsByGroupId" resultType="map">
        SELECT 
            COUNT(*) as total_count,
            COUNT(CASE WHEN enabled = 1 THEN 1 END) as enabled_count,
            COUNT(CASE WHEN config_status = 'ACTIVE' THEN 1 END) as active_count,
            COUNT(CASE WHEN auth_status = 'VALID' THEN 1 END) as auth_valid_count,
            COUNT(CASE WHEN last_sync_time IS NOT NULL THEN 1 END) as synced_count
        FROM acc_google_sheets_config
        WHERE group_id = #{groupId}
    </select>

    <select id="countConfigsByStatus" resultType="map">
        SELECT 
            config_status as status,
            COUNT(*) as count
        FROM acc_google_sheets_config
        WHERE group_id = #{groupId}
        GROUP BY config_status
        ORDER BY count DESC
    </select>

    <select id="countConfigsByAuthType" resultType="map">
        SELECT 
            auth_type as type,
            COUNT(*) as count
        FROM acc_google_sheets_config
        WHERE group_id = #{groupId}
        GROUP BY auth_type
        ORDER BY count DESC
    </select>

    <select id="countConfigsBySyncMode" resultType="map">
        SELECT 
            sync_mode as mode,
            COUNT(*) as count
        FROM acc_google_sheets_config
        WHERE group_id = #{groupId}
        GROUP BY sync_mode
        ORDER BY count DESC
    </select>

    <select id="countConfigsBySyncDirection" resultType="map">
        SELECT 
            sync_direction as direction,
            COUNT(*) as count
        FROM acc_google_sheets_config
        WHERE group_id = #{groupId}
        GROUP BY sync_direction
        ORDER BY count DESC
    </select>

    <select id="countConfigCreationTrend" resultType="map">
        SELECT 
            DATE(create_time) as date,
            COUNT(*) as count
        FROM acc_google_sheets_config
        WHERE group_id = #{groupId}
        AND create_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY DATE(create_time)
        ORDER BY date ASC
    </select>

    <!-- 性能统计 -->
    <select id="selectConfigSyncPerformance" resultType="map">
        SELECT 
            id,
            config_name,
            total_sync_count,
            success_sync_count,
            failed_sync_count,
            success_rate,
            avg_sync_duration,
            max_sync_duration,
            min_sync_duration,
            total_processed_records,
            recent_sync_count,
            recent_success_rate
        FROM acc_google_sheets_config
        WHERE group_id = #{groupId}
        AND total_sync_count > 0
        ORDER BY success_rate DESC, total_sync_count DESC
    </select>

    <select id="selectTopConfigsBySuccessRate" resultType="map">
        SELECT 
            id,
            config_name,
            success_rate,
            total_sync_count,
            success_sync_count
        FROM acc_google_sheets_config
        WHERE group_id = #{groupId}
        AND total_sync_count >= 5
        ORDER BY success_rate DESC, total_sync_count DESC
        LIMIT #{limit}
    </select>

    <select id="selectTopConfigsBySyncCount" resultType="map">
        SELECT 
            id,
            config_name,
            total_sync_count,
            success_sync_count,
            success_rate,
            last_sync_time
        FROM acc_google_sheets_config
        WHERE group_id = #{groupId}
        ORDER BY total_sync_count DESC
        LIMIT #{limit}
    </select>

    <select id="selectTopConfigsByProcessedRecords" resultType="map">
        SELECT 
            id,
            config_name,
            total_processed_records,
            total_success_records,
            total_failed_records,
            ROUND(total_success_records * 100.0 / NULLIF(total_processed_records, 0), 2) as record_success_rate
        FROM acc_google_sheets_config
        WHERE group_id = #{groupId}
        AND total_processed_records > 0
        ORDER BY total_processed_records DESC
        LIMIT #{limit}
    </select>

    <!-- 健康检查 -->
    <select id="selectConfigsWithExpiringAuth" resultMap="BaseResultMap">
        SELECT * FROM acc_google_sheets_config
        WHERE group_id = #{groupId}
        AND enabled = 1
        AND auth_status = 'VALID'
        AND token_expires_at IS NOT NULL
        AND token_expires_at &lt;= DATE_ADD(NOW(), INTERVAL #{expireHours} HOUR)
        ORDER BY token_expires_at ASC
    </select>

    <select id="selectConfigsWithoutRecentSync" resultMap="BaseResultMap">
        SELECT * FROM acc_google_sheets_config
        WHERE group_id = #{groupId}
        AND enabled = 1
        AND config_status = 'ACTIVE'
        AND sync_mode IN ('REAL_TIME', 'SCHEDULED')
        AND (last_sync_time IS NULL OR last_sync_time &lt; DATE_SUB(NOW(), INTERVAL #{hours} HOUR))
        ORDER BY last_sync_time ASC
    </select>

    <select id="selectConfigsWithHighFailureRate" resultMap="BaseResultMap">
        SELECT * FROM acc_google_sheets_config
        WHERE group_id = #{groupId}
        AND enabled = 1
        AND total_sync_count >= #{minSyncCount}
        AND (100.0 - success_rate) >= #{minFailureRate}
        ORDER BY success_rate ASC
    </select>

    <select id="selectConfigHealthStatus" resultType="map">
        SELECT 
            id,
            config_name,
            enabled,
            config_status,
            auth_status,
            token_expires_at,
            last_sync_time,
            last_sync_status,
            success_rate,
            total_sync_count,
            CASE 
                WHEN enabled = 0 THEN 'DISABLED'
                WHEN config_status != 'ACTIVE' THEN 'INACTIVE'
                WHEN auth_status != 'VALID' THEN 'AUTH_INVALID'
                WHEN token_expires_at IS NOT NULL AND token_expires_at &lt; NOW() THEN 'TOKEN_EXPIRED'
                WHEN last_sync_status = 'FAILED' THEN 'SYNC_FAILED'
                WHEN success_rate &lt; 80 AND total_sync_count >= 5 THEN 'LOW_SUCCESS_RATE'
                ELSE 'HEALTHY'
            END as health_status
        FROM acc_google_sheets_config
        WHERE id = #{configId}
    </select>

    <!-- 批量操作 -->
    <update id="batchUpdateConfigStatus">
        UPDATE acc_google_sheets_config
        SET config_status = #{configStatus}, update_time = NOW()
        WHERE id IN
        <foreach collection="configIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="batchUpdateEnabled">
        UPDATE acc_google_sheets_config
        SET enabled = #{enabled}, update_time = NOW()
        WHERE id IN
        <foreach collection="configIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="batchUpdateAuthStatus">
        UPDATE acc_google_sheets_config
        SET auth_status = #{authStatus}, update_time = NOW()
        WHERE id IN
        <foreach collection="configIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="batchUpdateLastSyncTime">
        UPDATE acc_google_sheets_config
        SET last_sync_time = #{lastSyncTime}, 
            last_sync_status = #{lastSyncStatus}, 
            update_time = NOW()
        WHERE id IN
        <foreach collection="configIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 数据维护 -->
    <update id="updateConfigStatistics">
        UPDATE acc_google_sheets_config c
        SET 
            total_sync_count = (
                SELECT COUNT(*) FROM acc_google_sheets_sync_log l 
                WHERE l.config_id = c.id
            ),
            success_sync_count = (
                SELECT COUNT(*) FROM acc_google_sheets_sync_log l 
                WHERE l.config_id = c.id AND l.sync_status = 'SUCCESS'
            ),
            failed_sync_count = (
                SELECT COUNT(*) FROM acc_google_sheets_sync_log l 
                WHERE l.config_id = c.id AND l.sync_status = 'FAILED'
            ),
            success_rate = (
                SELECT ROUND(
                    COUNT(CASE WHEN l.sync_status = 'SUCCESS' THEN 1 END) * 100.0 / COUNT(*), 2
                ) FROM acc_google_sheets_sync_log l 
                WHERE l.config_id = c.id
            ),
            total_processed_records = (
                SELECT COALESCE(SUM(l.processed_records), 0) FROM acc_google_sheets_sync_log l 
                WHERE l.config_id = c.id
            ),
            total_success_records = (
                SELECT COALESCE(SUM(l.success_records), 0) FROM acc_google_sheets_sync_log l 
                WHERE l.config_id = c.id
            ),
            total_failed_records = (
                SELECT COALESCE(SUM(l.failed_records), 0) FROM acc_google_sheets_sync_log l 
                WHERE l.config_id = c.id
            ),
            avg_sync_duration = (
                SELECT ROUND(AVG(l.sync_duration), 2) FROM acc_google_sheets_sync_log l 
                WHERE l.config_id = c.id AND l.sync_status = 'SUCCESS'
            ),
            max_sync_duration = (
                SELECT MAX(l.sync_duration) FROM acc_google_sheets_sync_log l 
                WHERE l.config_id = c.id AND l.sync_status = 'SUCCESS'
            ),
            min_sync_duration = (
                SELECT MIN(l.sync_duration) FROM acc_google_sheets_sync_log l 
                WHERE l.config_id = c.id AND l.sync_status = 'SUCCESS'
            ),
            recent_sync_count = (
                SELECT COUNT(*) FROM acc_google_sheets_sync_log l 
                WHERE l.config_id = c.id AND l.start_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            ),
            recent_success_rate = (
                SELECT ROUND(
                    COUNT(CASE WHEN l.sync_status = 'SUCCESS' THEN 1 END) * 100.0 / COUNT(*), 2
                ) FROM acc_google_sheets_sync_log l 
                WHERE l.config_id = c.id AND l.start_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            ),
            update_time = NOW()
        WHERE id = #{configId}
    </update>

    <update id="batchUpdateConfigStatistics">
        UPDATE acc_google_sheets_config c
        SET 
            total_sync_count = (
                SELECT COUNT(*) FROM acc_google_sheets_sync_log l 
                WHERE l.config_id = c.id
            ),
            success_sync_count = (
                SELECT COUNT(*) FROM acc_google_sheets_sync_log l 
                WHERE l.config_id = c.id AND l.sync_status = 'SUCCESS'
            ),
            failed_sync_count = (
                SELECT COUNT(*) FROM acc_google_sheets_sync_log l 
                WHERE l.config_id = c.id AND l.sync_status = 'FAILED'
            ),
            success_rate = (
                SELECT ROUND(
                    COUNT(CASE WHEN l.sync_status = 'SUCCESS' THEN 1 END) * 100.0 / COUNT(*), 2
                ) FROM acc_google_sheets_sync_log l 
                WHERE l.config_id = c.id
            ),
            update_time = NOW()
        WHERE group_id = #{groupId}
    </update>

    <update id="resetConfigStatistics">
        UPDATE acc_google_sheets_config
        SET 
            total_sync_count = 0,
            success_sync_count = 0,
            failed_sync_count = 0,
            success_rate = 0.0,
            total_processed_records = 0,
            total_success_records = 0,
            total_failed_records = 0,
            avg_sync_duration = NULL,
            max_sync_duration = NULL,
            min_sync_duration = NULL,
            recent_sync_count = 0,
            recent_success_rate = 0.0,
            update_time = NOW()
        WHERE id = #{configId}
    </update>

    <delete id="cleanupInvalidConfigs">
        DELETE FROM acc_google_sheets_config
        WHERE group_id = #{groupId}
        AND enabled = 0
        AND config_status = 'DISABLED'
        AND update_time &lt; DATE_SUB(NOW(), INTERVAL #{days} DAY)
    </delete>

    <!-- 高级查询 -->
    <select id="searchConfigs" resultMap="BaseResultMap">
        SELECT * FROM acc_google_sheets_config
        WHERE group_id = #{groupId}
        AND (
            config_name LIKE CONCAT('%', #{keyword}, '%')
            OR config_description LIKE CONCAT('%', #{keyword}, '%')
            OR spreadsheet_name LIKE CONCAT('%', #{keyword}, '%')
        )
        ORDER BY 
            CASE WHEN config_name LIKE CONCAT(#{keyword}, '%') THEN 1 ELSE 2 END,
            config_name ASC
        LIMIT #{limit}
    </select>

    <select id="selectSimilarConfigs" resultMap="BaseResultMap">
        SELECT c2.* FROM acc_google_sheets_config c1
        JOIN acc_google_sheets_config c2 ON c1.group_id = c2.group_id
        WHERE c1.id = #{configId}
        AND c2.id != #{configId}
        AND (
            c1.sync_direction = c2.sync_direction
            OR c1.sync_mode = c2.sync_mode
            OR c1.auth_type = c2.auth_type
        )
        ORDER BY 
            (CASE WHEN c1.sync_direction = c2.sync_direction THEN 1 ELSE 0 END +
             CASE WHEN c1.sync_mode = c2.sync_mode THEN 1 ELSE 0 END +
             CASE WHEN c1.auth_type = c2.auth_type THEN 1 ELSE 0 END) DESC,
            c2.update_time DESC
        LIMIT #{limit}
    </select>

    <select id="selectConfigDependencies" resultType="map">
        SELECT 
            'sync_log' as dependency_type,
            COUNT(*) as count,
            'Google Sheets同步日志' as description
        FROM acc_google_sheets_sync_log
        WHERE config_id = #{configId}
        UNION ALL
        SELECT 
            'backup' as dependency_type,
            COUNT(*) as count,
            'Google Sheets备份' as description
        FROM acc_google_sheets_backup
        WHERE config_id = #{configId}
    </select>

    <select id="selectConfigUsageHistory" resultType="map">
        SELECT 
            DATE(start_time) as date,
            COUNT(*) as sync_count,
            COUNT(CASE WHEN sync_status = 'SUCCESS' THEN 1 END) as success_count,
            COUNT(CASE WHEN sync_status = 'FAILED' THEN 1 END) as failed_count,
            COALESCE(SUM(processed_records), 0) as total_records
        FROM acc_google_sheets_sync_log
        WHERE config_id = #{configId}
        AND start_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY DATE(start_time)
        ORDER BY date ASC
    </select>

    <!-- 导出导入 -->
    <select id="selectConfigsForExport" resultType="map">
        SELECT 
            config_name,
            config_description,
            spreadsheet_id,
            spreadsheet_name,
            sheet_name,
            sync_direction,
            sync_mode,
            field_mapping_json,
            filter_condition_json,
            sync_settings_json,
            auth_type,
            enabled,
            custom_tags_json
        FROM acc_google_sheets_config
        WHERE id IN
        <foreach collection="configIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="validateImportData" resultType="map">
        SELECT 
            COUNT(CASE WHEN config_name = #{configName} THEN 1 END) as name_exists,
            COUNT(CASE WHEN spreadsheet_id = #{spreadsheetId} THEN 1 END) as spreadsheet_exists
        FROM acc_google_sheets_config
        WHERE group_id = #{groupId}
    </select>

</mapper>
