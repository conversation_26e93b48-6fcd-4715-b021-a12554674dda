package top.continew.admin.accounting.event;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.ApplicationEvent;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 交易创建事件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TransactionCreatedEvent extends ApplicationEvent {

    private Long transactionId;
    private Long groupId;
    private BigDecimal amount;
    private String description;
    private Long categoryId;
    private String categoryName;
    private Long walletId;
    private String walletName;
    private String type;
    private LocalDateTime createTime;
    private Long createUser;
    private List<String> tags;
    private List<Long> participants;

    public TransactionCreatedEvent(Object source, Long transactionId, Long groupId, BigDecimal amount,
                                   String description, Long categoryId, String categoryName, Long walletId,
                                   String walletName, String type, LocalDateTime createTime, Long createUser,
                                   List<String> tags, List<Long> participants) {
        super(source);
        this.transactionId = transactionId;
        this.groupId = groupId;
        this.amount = amount;
        this.description = description;
        this.categoryId = categoryId;
        this.categoryName = categoryName;
        this.walletId = walletId;
        this.walletName = walletName;
        this.type = type;
        this.createTime = createTime;
        this.createUser = createUser;
        this.tags = tags;
        this.participants = participants;
    }
}

/**
 * 交易更新事件
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TransactionUpdatedEvent extends ApplicationEvent {
    private Long transactionId;
    private Long groupId;
    private BigDecimal oldAmount;
    private BigDecimal newAmount;
    private String oldDescription;
    private String newDescription;
    private Long oldCategoryId;
    private Long newCategoryId;
    private LocalDateTime updateTime;
    private Long updateUser;
    private List<String> changeFields;

    public TransactionUpdatedEvent(Object source, Long transactionId, Long groupId, BigDecimal oldAmount,
                                   BigDecimal newAmount, String oldDescription, String newDescription,
                                   Long oldCategoryId, Long newCategoryId, LocalDateTime updateTime,
                                   Long updateUser, List<String> changeFields) {
        super(source);
        this.transactionId = transactionId;
        this.groupId = groupId;
        this.oldAmount = oldAmount;
        this.newAmount = newAmount;
        this.oldDescription = oldDescription;
        this.newDescription = newDescription;
        this.oldCategoryId = oldCategoryId;
        this.newCategoryId = newCategoryId;
        this.updateTime = updateTime;
        this.updateUser = updateUser;
        this.changeFields = changeFields;
    }
}

/**
 * 交易删除事件
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TransactionDeletedEvent extends ApplicationEvent {
    private Long transactionId;
    private Long groupId;
    private BigDecimal amount;
    private String description;
    private Long categoryId;
    private String categoryName;
    private Long walletId;
    private String walletName;
    private String type;
    private LocalDateTime deleteTime;
    private Long deleteUser;
    private String deleteReason;

    public TransactionDeletedEvent(Object source, Long transactionId, Long groupId, BigDecimal amount,
                                   String description, Long categoryId, String categoryName, Long walletId,
                                   String walletName, String type, LocalDateTime deleteTime, Long deleteUser,
                                   String deleteReason) {
        super(source);
        this.transactionId = transactionId;
        this.groupId = groupId;
        this.amount = amount;
        this.description = description;
        this.categoryId = categoryId;
        this.categoryName = categoryName;
        this.walletId = walletId;
        this.walletName = walletName;
        this.type = type;
        this.deleteTime = deleteTime;
        this.deleteUser = deleteUser;
        this.deleteReason = deleteReason;
    }
}

/**
 * 钱包更新事件
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WalletUpdatedEvent extends ApplicationEvent {
    private Long walletId;
    private Long groupId;
    private String walletName;
    private BigDecimal oldBalance;
    private BigDecimal newBalance;
    private BigDecimal balanceChange;
    private String currency;
    private LocalDateTime updateTime;
    private Long updateUser;
    private String updateReason;
    private Long relatedTransactionId;

    public WalletUpdatedEvent(Object source, Long walletId, Long groupId, String walletName,
                              BigDecimal oldBalance, BigDecimal newBalance, BigDecimal balanceChange,
                              String currency, LocalDateTime updateTime, Long updateUser,
                              String updateReason, Long relatedTransactionId) {
        super(source);
        this.walletId = walletId;
        this.groupId = groupId;
        this.walletName = walletName;
        this.oldBalance = oldBalance;
        this.newBalance = newBalance;
        this.balanceChange = balanceChange;
        this.currency = currency;
        this.updateTime = updateTime;
        this.updateUser = updateUser;
        this.updateReason = updateReason;
        this.relatedTransactionId = relatedTransactionId;
    }
}
