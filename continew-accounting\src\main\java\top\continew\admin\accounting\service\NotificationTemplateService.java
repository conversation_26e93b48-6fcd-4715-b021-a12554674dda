package top.continew.admin.accounting.service;

import top.continew.admin.accounting.model.entity.NotificationTemplateDO;

import java.util.List;
import java.util.Map;

/**
 * 通知模板服务
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface NotificationTemplateService {

    // ==================== 模板管理 ====================

    /**
     * 创建通知模板
     *
     * @param template 模板信息
     * @return 模板ID
     */
    Long createTemplate(NotificationTemplateDO template);

    /**
     * 更新通知模板
     *
     * @param template 模板信息
     * @return 是否成功
     */
    Boolean updateTemplate(NotificationTemplateDO template);

    /**
     * 删除通知模板
     *
     * @param templateId 模板ID
     * @return 是否成功
     */
    Boolean deleteTemplate(Long templateId);

    /**
     * 获取模板详情
     *
     * @param templateId 模板ID
     * @return 模板详情
     */
    NotificationTemplateDO getTemplateById(Long templateId);

    /**
     * 根据代码获取模板
     *
     * @param templateCode 模板代码
     * @return 模板信息
     */
    NotificationTemplateDO getTemplateByCode(String templateCode);

    /**
     * 查询模板列表
     *
     * @param groupId 群组ID
     * @param category 模板分类
     * @param enabled 是否启用
     * @return 模板列表
     */
    List<NotificationTemplateDO> getTemplateList(Long groupId, String category, Boolean enabled);

    /**
     * 分页查询模板
     *
     * @param groupId 群组ID
     * @param category 模板分类
     * @param keyword 关键词
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    Map<String, Object> getTemplatePageList(Long groupId, String category, String keyword, 
                                           Integer pageNum, Integer pageSize);

    // ==================== 模板渲染 ====================

    /**
     * 渲染模板
     *
     * @param templateCode 模板代码
     * @param channel 渠道
     * @param params 参数
     * @return 渲染结果
     */
    Map<String, String> renderTemplate(String templateCode, String channel, Map<String, Object> params);

    /**
     * 批量渲染模板
     *
     * @param templateCode 模板代码
     * @param channels 渠道列表
     * @param params 参数
     * @return 渲染结果
     */
    Map<String, Map<String, String>> batchRenderTemplate(String templateCode, List<String> channels, 
                                                         Map<String, Object> params);

    /**
     * 预览模板
     *
     * @param templateCode 模板代码
     * @param channel 渠道
     * @param params 参数
     * @return 预览结果
     */
    Map<String, String> previewTemplate(String templateCode, String channel, Map<String, Object> params);

    /**
     * 验证模板语法
     *
     * @param templateContent 模板内容
     * @param params 参数
     * @return 验证结果
     */
    Map<String, Object> validateTemplateSyntax(String templateContent, Map<String, Object> params);

    // ==================== 模板变量 ====================

    /**
     * 获取模板变量
     *
     * @param templateCode 模板代码
     * @return 变量列表
     */
    List<Map<String, Object>> getTemplateVariables(String templateCode);

    /**
     * 解析模板变量
     *
     * @param templateContent 模板内容
     * @return 变量列表
     */
    List<String> parseTemplateVariables(String templateContent);

    /**
     * 验证模板参数
     *
     * @param templateCode 模板代码
     * @param params 参数
     * @return 验证结果
     */
    Map<String, Object> validateTemplateParams(String templateCode, Map<String, Object> params);

    /**
     * 获取默认参数
     *
     * @param templateCode 模板代码
     * @return 默认参数
     */
    Map<String, Object> getDefaultParams(String templateCode);

    // ==================== 模板分类 ====================

    /**
     * 获取模板分类列表
     *
     * @param groupId 群组ID
     * @return 分类列表
     */
    List<Map<String, Object>> getTemplateCategories(Long groupId);

    /**
     * 创建模板分类
     *
     * @param groupId 群组ID
     * @param categoryName 分类名称
     * @param description 描述
     * @return 是否成功
     */
    Boolean createTemplateCategory(Long groupId, String categoryName, String description);

    /**
     * 更新模板分类
     *
     * @param categoryId 分类ID
     * @param categoryName 分类名称
     * @param description 描述
     * @return 是否成功
     */
    Boolean updateTemplateCategory(Long categoryId, String categoryName, String description);

    /**
     * 删除模板分类
     *
     * @param categoryId 分类ID
     * @return 是否成功
     */
    Boolean deleteTemplateCategory(Long categoryId);

    // ==================== 模板统计 ====================

    /**
     * 获取模板使用统计
     *
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 使用统计
     */
    List<Map<String, Object>> getTemplateUsageStatistics(Long groupId, String startDate, String endDate);

    /**
     * 获取热门模板
     *
     * @param groupId 群组ID
     * @param limit 限制数量
     * @return 热门模板列表
     */
    List<Map<String, Object>> getPopularTemplates(Long groupId, Integer limit);

    /**
     * 获取模板性能统计
     *
     * @param templateCode 模板代码
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 性能统计
     */
    Map<String, Object> getTemplatePerformanceStatistics(String templateCode, String startDate, String endDate);

    // ==================== 模板导入导出 ====================

    /**
     * 导出模板
     *
     * @param templateIds 模板ID列表
     * @return 导出数据
     */
    Map<String, Object> exportTemplates(List<Long> templateIds);

    /**
     * 导入模板
     *
     * @param groupId 群组ID
     * @param templateData 模板数据
     * @return 导入结果
     */
    Map<String, Object> importTemplates(Long groupId, Map<String, Object> templateData);

    /**
     * 复制模板
     *
     * @param templateId 源模板ID
     * @param newTemplateCode 新模板代码
     * @param newTemplateName 新模板名称
     * @return 新模板ID
     */
    Long copyTemplate(Long templateId, String newTemplateCode, String newTemplateName);

    /**
     * 批量启用/禁用模板
     *
     * @param templateIds 模板ID列表
     * @param enabled 是否启用
     * @return 操作结果
     */
    Map<String, Object> batchUpdateTemplateStatus(List<Long> templateIds, Boolean enabled);

    // ==================== 模板版本管理 ====================

    /**
     * 创建模板版本
     *
     * @param templateId 模板ID
     * @param version 版本号
     * @param description 版本描述
     * @return 版本ID
     */
    Long createTemplateVersion(Long templateId, String version, String description);

    /**
     * 获取模板版本列表
     *
     * @param templateId 模板ID
     * @return 版本列表
     */
    List<Map<String, Object>> getTemplateVersions(Long templateId);

    /**
     * 回滚模板版本
     *
     * @param templateId 模板ID
     * @param versionId 版本ID
     * @return 是否成功
     */
    Boolean rollbackTemplateVersion(Long templateId, Long versionId);

    /**
     * 删除模板版本
     *
     * @param versionId 版本ID
     * @return 是否成功
     */
    Boolean deleteTemplateVersion(Long versionId);

}
