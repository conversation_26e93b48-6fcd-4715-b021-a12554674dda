package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * Google Sheets配置请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "Google Sheets配置请求")
public class GoogleSheetsConfigReq {

    /**
     * 配置名称
     */
    @Schema(description = "配置名称", example = "我的记账表格")
    @NotBlank(message = "配置名称不能为空")
    private String configName;

    /**
     * Google Sheets ID
     */
    @Schema(description = "Google Sheets ID", example = "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms")
    @NotBlank(message = "Google Sheets ID不能为空")
    private String spreadsheetId;

    /**
     * 工作表名称
     */
    @Schema(description = "工作表名称", example = "账单记录")
    @NotBlank(message = "工作表名称不能为空")
    private String sheetName;

    /**
     * 同步方向
     */
    @Schema(description = "同步方向", example = "BIDIRECTIONAL", allowableValues = {"TO_SHEETS", "FROM_SHEETS", "BIDIRECTIONAL"})
    @NotBlank(message = "同步方向不能为空")
    private String syncDirection;

    /**
     * 同步模式
     */
    @Schema(description = "同步模式", example = "REAL_TIME", allowableValues = {"REAL_TIME", "SCHEDULED", "MANUAL"})
    @NotBlank(message = "同步模式不能为空")
    private String syncMode;

    /**
     * 调度表达式（定时同步时使用）
     */
    @Schema(description = "调度表达式", example = "0 0 * * * ?")
    private String cronExpression;

    /**
     * 字段映射配置
     */
    @Schema(description = "字段映射配置")
    @NotNull(message = "字段映射配置不能为空")
    private FieldMapping fieldMapping;

    /**
     * 过滤条件
     */
    @Schema(description = "过滤条件")
    private FilterCondition filterCondition;

    /**
     * 同步设置
     */
    @Schema(description = "同步设置")
    private SyncSettings syncSettings;

    /**
     * 认证配置
     */
    @Schema(description = "认证配置")
    @NotNull(message = "认证配置不能为空")
    private AuthConfig authConfig;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled = true;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "用于同步日常开销记录")
    private String remark;

    /**
     * 字段映射配置
     */
    @Data
    @Schema(description = "字段映射配置")
    public static class FieldMapping {

        /**
         * 数据起始行
         */
        @Schema(description = "数据起始行", example = "2")
        @NotNull(message = "数据起始行不能为空")
        private Integer dataStartRow;

        /**
         * 表头行
         */
        @Schema(description = "表头行", example = "1")
        @NotNull(message = "表头行不能为空")
        private Integer headerRow;

        /**
         * 字段映射关系
         */
        @Schema(description = "字段映射关系")
        @NotNull(message = "字段映射关系不能为空")
        private Map<String, String> fieldMap;

        /**
         * 日期格式
         */
        @Schema(description = "日期格式", example = "yyyy-MM-dd")
        private String dateFormat = "yyyy-MM-dd";

        /**
         * 时间格式
         */
        @Schema(description = "时间格式", example = "HH:mm:ss")
        private String timeFormat = "HH:mm:ss";

        /**
         * 数字格式
         */
        @Schema(description = "数字格式", example = "#,##0.00")
        private String numberFormat = "#,##0.00";

        /**
         * 货币符号
         */
        @Schema(description = "货币符号", example = "¥")
        private String currencySymbol = "¥";

        /**
         * 是否包含表头
         */
        @Schema(description = "是否包含表头", example = "true")
        private Boolean includeHeader = true;

        /**
         * 自定义列配置
         */
        @Schema(description = "自定义列配置")
        private List<CustomColumn> customColumns;
    }

    /**
     * 自定义列配置
     */
    @Data
    @Schema(description = "自定义列配置")
    public static class CustomColumn {

        /**
         * 列名
         */
        @Schema(description = "列名", example = "分类名称")
        @NotBlank(message = "列名不能为空")
        private String columnName;

        /**
         * 列索引
         */
        @Schema(description = "列索引", example = "5")
        @NotNull(message = "列索引不能为空")
        private Integer columnIndex;

        /**
         * 数据类型
         */
        @Schema(description = "数据类型", example = "STRING", allowableValues = {"STRING", "NUMBER", "DATE", "BOOLEAN", "FORMULA"})
        @NotBlank(message = "数据类型不能为空")
        private String dataType;

        /**
         * 默认值
         */
        @Schema(description = "默认值", example = "其他")
        private String defaultValue;

        /**
         * 公式表达式
         */
        @Schema(description = "公式表达式", example = "=SUM(D2:D100)")
        private String formula;

        /**
         * 是否必填
         */
        @Schema(description = "是否必填", example = "false")
        private Boolean required = false;

        /**
         * 验证规则
         */
        @Schema(description = "验证规则")
        private String validationRule;
    }

    /**
     * 过滤条件
     */
    @Data
    @Schema(description = "过滤条件")
    public static class FilterCondition {

        /**
         * 日期范围过滤
         */
        @Schema(description = "日期范围过滤")
        private DateRangeFilter dateRange;

        /**
         * 金额范围过滤
         */
        @Schema(description = "金额范围过滤")
        private AmountRangeFilter amountRange;

        /**
         * 分类过滤
         */
        @Schema(description = "分类过滤")
        private List<Long> categoryIds;

        /**
         * 标签过滤
         */
        @Schema(description = "标签过滤")
        private List<String> tags;

        /**
         * 账单类型过滤
         */
        @Schema(description = "账单类型过滤", allowableValues = {"INCOME", "EXPENSE"})
        private List<String> transactionTypes;

        /**
         * 自定义过滤条件
         */
        @Schema(description = "自定义过滤条件")
        private String customFilter;

        /**
         * 是否包含已删除数据
         */
        @Schema(description = "是否包含已删除数据", example = "false")
        private Boolean includeDeleted = false;
    }

    /**
     * 日期范围过滤
     */
    @Data
    @Schema(description = "日期范围过滤")
    public static class DateRangeFilter {

        /**
         * 开始日期
         */
        @Schema(description = "开始日期", example = "2025-01-01")
        private String startDate;

        /**
         * 结束日期
         */
        @Schema(description = "结束日期", example = "2025-12-31")
        private String endDate;

        /**
         * 相对日期类型
         */
        @Schema(description = "相对日期类型", example = "LAST_30_DAYS", allowableValues = {
                "TODAY", "YESTERDAY", "LAST_7_DAYS", "LAST_30_DAYS", "THIS_MONTH", "LAST_MONTH", "THIS_YEAR", "LAST_YEAR"
        })
        private String relativeDateType;
    }

    /**
     * 金额范围过滤
     */
    @Data
    @Schema(description = "金额范围过滤")
    public static class AmountRangeFilter {

        /**
         * 最小金额
         */
        @Schema(description = "最小金额", example = "0.01")
        private String minAmount;

        /**
         * 最大金额
         */
        @Schema(description = "最大金额", example = "10000.00")
        private String maxAmount;
    }

    /**
     * 同步设置
     */
    @Data
    @Schema(description = "同步设置")
    public static class SyncSettings {

        /**
         * 批量大小
         */
        @Schema(description = "批量大小", example = "100")
        private Integer batchSize = 100;

        /**
         * 同步超时时间（秒）
         */
        @Schema(description = "同步超时时间（秒）", example = "300")
        private Integer timeoutSeconds = 300;

        /**
         * 重试次数
         */
        @Schema(description = "重试次数", example = "3")
        private Integer retryCount = 3;

        /**
         * 冲突解决策略
         */
        @Schema(description = "冲突解决策略", example = "LOCAL_WINS", allowableValues = {
                "LOCAL_WINS", "REMOTE_WINS", "MERGE", "SKIP", "MANUAL"
        })
        private String conflictResolution = "LOCAL_WINS";

        /**
         * 是否启用增量同步
         */
        @Schema(description = "是否启用增量同步", example = "true")
        private Boolean enableIncrementalSync = true;

        /**
         * 是否启用数据验证
         */
        @Schema(description = "是否启用数据验证", example = "true")
        private Boolean enableDataValidation = true;

        /**
         * 是否启用备份
         */
        @Schema(description = "是否启用备份", example = "true")
        private Boolean enableBackup = true;

        /**
         * 备份保留天数
         */
        @Schema(description = "备份保留天数", example = "30")
        private Integer backupRetentionDays = 30;

        /**
         * 通知设置
         */
        @Schema(description = "通知设置")
        private NotificationSettings notification;
    }

    /**
     * 通知设置
     */
    @Data
    @Schema(description = "通知设置")
    public static class NotificationSettings {

        /**
         * 是否启用同步成功通知
         */
        @Schema(description = "是否启用同步成功通知", example = "false")
        private Boolean enableSuccessNotification = false;

        /**
         * 是否启用同步失败通知
         */
        @Schema(description = "是否启用同步失败通知", example = "true")
        private Boolean enableFailureNotification = true;

        /**
         * 通知邮箱
         */
        @Schema(description = "通知邮箱", example = "<EMAIL>")
        private String notificationEmail;

        /**
         * 通知Webhook URL
         */
        @Schema(description = "通知Webhook URL", example = "https://hooks.slack.com/services/...")
        private String webhookUrl;
    }

    /**
     * 认证配置
     */
    @Data
    @Schema(description = "认证配置")
    public static class AuthConfig {

        /**
         * 认证类型
         */
        @Schema(description = "认证类型", example = "SERVICE_ACCOUNT", allowableValues = {"SERVICE_ACCOUNT", "OAUTH2", "API_KEY"})
        @NotBlank(message = "认证类型不能为空")
        private String authType;

        /**
         * 服务账号密钥文件内容（Base64编码）
         */
        @Schema(description = "服务账号密钥文件内容（Base64编码）")
        private String serviceAccountKey;

        /**
         * OAuth2客户端ID
         */
        @Schema(description = "OAuth2客户端ID")
        private String clientId;

        /**
         * OAuth2客户端密钥
         */
        @Schema(description = "OAuth2客户端密钥")
        private String clientSecret;

        /**
         * OAuth2刷新令牌
         */
        @Schema(description = "OAuth2刷新令牌")
        private String refreshToken;

        /**
         * API密钥
         */
        @Schema(description = "API密钥")
        private String apiKey;

        /**
         * 授权范围
         */
        @Schema(description = "授权范围")
        private List<String> scopes;

        /**
         * 令牌过期时间
         */
        @Schema(description = "令牌过期时间")
        private Long tokenExpiresAt;
    }
}
