package top.continew.admin.accounting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.accounting.mapper.GoogleSheetsConfigMapper;
import top.continew.admin.accounting.mapper.GoogleSheetsSyncLogMapper;
import top.continew.admin.accounting.model.entity.GoogleSheetsConfig;
import top.continew.admin.accounting.model.entity.GoogleSheetsSyncLog;
import top.continew.admin.accounting.model.query.GoogleSheetsConfigQuery;
import top.continew.admin.accounting.model.query.GoogleSheetsSyncLogQuery;
import top.continew.admin.accounting.model.req.GoogleSheetsConfigReq;
import top.continew.admin.accounting.model.req.GoogleSheetsConfigUpdateReq;
import top.continew.admin.accounting.model.req.GoogleSheetsSyncReq;
import top.continew.admin.accounting.model.resp.GoogleSheetsConfigResp;
import top.continew.admin.accounting.model.resp.GoogleSheetsSyncLogResp;
import top.continew.admin.accounting.model.resp.GoogleSheetsSyncResp;
import top.continew.admin.accounting.service.GoogleSheetsService;
import top.continew.admin.common.model.resp.LabelValueResp;
import top.continew.admin.common.util.helper.LoginHelper;
import top.continew.admin.common.util.validate.CheckUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Google Sheets服务实现类
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GoogleSheetsServiceImpl implements GoogleSheetsService {

    private final GoogleSheetsConfigMapper configMapper;
    private final GoogleSheetsSyncLogMapper syncLogMapper;

    // ==================== 配置管理 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createConfig(GoogleSheetsConfigReq req) {
        log.info("创建Google Sheets配置: {}", req.getConfigName());

        // 验证配置名称唯一性
        this.checkConfigNameUnique(req.getConfigName(), req.getGroupId(), null);

        // 验证Google Sheets ID格式
        this.validateSpreadsheetId(req.getSpreadsheetId());

        // 创建配置实体
        GoogleSheetsConfig config = new GoogleSheetsConfig();
        BeanUtil.copyProperties(req, config);

        // 设置JSON字段
        config.setFieldMappingJson(JSONUtil.toJsonStr(req.getFieldMapping()));
        config.setFilterConditionJson(JSONUtil.toJsonStr(req.getFilterCondition()));
        config.setSyncSettingsJson(JSONUtil.toJsonStr(req.getSyncSettings()));
        config.setAuthConfigJson(JSONUtil.toJsonStr(req.getAuthConfig()));
        config.setCustomTagsJson(JSONUtil.toJsonStr(req.getCustomTags()));

        // 设置默认值
        config.setEnabled(true);
        config.setConfigStatus("ACTIVE");
        config.setConfigVersion(1);
        config.setAuthStatus("PENDING");
        config.setTotalSyncCount(0);
        config.setSuccessSyncCount(0);
        config.setFailedSyncCount(0);
        config.setSuccessRate(0.0);
        config.setTotalProcessedRecords(0L);
        config.setTotalSuccessRecords(0L);
        config.setTotalFailedRecords(0L);
        config.setRecentSyncCount(0);
        config.setRecentSuccessRate(0.0);

        // 保存配置
        configMapper.insert(config);

        log.info("Google Sheets配置创建成功，配置ID: {}", config.getId());
        return config.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateConfig(Long configId, GoogleSheetsConfigUpdateReq req) {
        log.info("更新Google Sheets配置: {}", configId);

        // 检查配置是否存在
        GoogleSheetsConfig existingConfig = this.getConfigById(configId);

        // 验证配置名称唯一性
        if (StrUtil.isNotBlank(req.getConfigName()) && !req.getConfigName().equals(existingConfig.getConfigName())) {
            this.checkConfigNameUnique(req.getConfigName(), existingConfig.getGroupId(), configId);
        }

        // 验证Google Sheets ID格式
        if (StrUtil.isNotBlank(req.getSpreadsheetId()) && !req.getSpreadsheetId().equals(existingConfig.getSpreadsheetId())) {
            this.validateSpreadsheetId(req.getSpreadsheetId());
        }

        // 更新配置
        GoogleSheetsConfig config = new GoogleSheetsConfig();
        BeanUtil.copyProperties(req, config);
        config.setId(configId);

        // 更新JSON字段
        if (req.getFieldMapping() != null) {
            config.setFieldMappingJson(JSONUtil.toJsonStr(req.getFieldMapping()));
        }
        if (req.getFilterCondition() != null) {
            config.setFilterConditionJson(JSONUtil.toJsonStr(req.getFilterCondition()));
        }
        if (req.getSyncSettings() != null) {
            config.setSyncSettingsJson(JSONUtil.toJsonStr(req.getSyncSettings()));
        }
        if (req.getAuthConfig() != null) {
            config.setAuthConfigJson(JSONUtil.toJsonStr(req.getAuthConfig()));
        }
        if (req.getCustomTags() != null) {
            config.setCustomTagsJson(JSONUtil.toJsonStr(req.getCustomTags()));
        }

        // 更新版本信息
        if (req.getKeepHistory() != null && req.getKeepHistory()) {
            config.setConfigVersion(existingConfig.getConfigVersion() + 1);
            config.setVersionNotes(req.getVersionNotes());
        }

        // 如果认证配置发生变化，重置认证状态
        if (req.getAuthConfig() != null) {
            config.setAuthStatus("PENDING");
            config.setTokenExpiresAt(null);
        }

        int updated = configMapper.updateById(config);
        boolean success = updated > 0;

        if (success) {
            log.info("Google Sheets配置更新成功，配置ID: {}", configId);
        } else {
            log.warn("Google Sheets配置更新失败，配置ID: {}", configId);
        }

        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteConfig(Long configId) {
        log.info("删除Google Sheets配置: {}", configId);

        // 检查配置是否存在
        GoogleSheetsConfig config = this.getConfigById(configId);

        // 检查是否有正在进行的同步任务
        this.checkNoActiveSyncTasks(configId);

        // 删除配置
        int deleted = configMapper.deleteById(configId);
        boolean success = deleted > 0;

        if (success) {
            log.info("Google Sheets配置删除成功，配置ID: {}", configId);
        } else {
            log.warn("Google Sheets配置删除失败，配置ID: {}", configId);
        }

        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteConfigs(List<Long> configIds) {
        log.info("批量删除Google Sheets配置: {}", configIds);

        if (CollUtil.isEmpty(configIds)) {
            return true;
        }

        // 检查所有配置是否存在
        for (Long configId : configIds) {
            this.getConfigById(configId);
            this.checkNoActiveSyncTasks(configId);
        }

        // 批量删除配置
        int deleted = configMapper.deleteBatchIds(configIds);
        boolean success = deleted == configIds.size();

        if (success) {
            log.info("Google Sheets配置批量删除成功，删除数量: {}", deleted);
        } else {
            log.warn("Google Sheets配置批量删除失败，期望删除: {}, 实际删除: {}", configIds.size(), deleted);
        }

        return success;
    }

    @Override
    public GoogleSheetsConfigResp getConfigDetail(Long configId) {
        log.debug("获取Google Sheets配置详情: {}", configId);

        GoogleSheetsConfig config = this.getConfigById(configId);
        return this.convertToConfigResp(config);
    }

    @Override
    public IPage<GoogleSheetsConfigResp> pageConfigs(GoogleSheetsConfigQuery query) {
        log.debug("分页查询Google Sheets配置: {}", query);

        // 构建查询条件
        LambdaQueryWrapper<GoogleSheetsConfig> queryWrapper = this.buildConfigQueryWrapper(query);

        // 分页查询
        Page<GoogleSheetsConfig> page = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<GoogleSheetsConfig> configPage = configMapper.selectPage(page, queryWrapper);

        // 转换响应
        return configPage.convert(this::convertToConfigResp);
    }

    @Override
    public List<GoogleSheetsConfigResp> listConfigs(GoogleSheetsConfigQuery query) {
        log.debug("查询Google Sheets配置列表: {}", query);

        // 构建查询条件
        LambdaQueryWrapper<GoogleSheetsConfig> queryWrapper = this.buildConfigQueryWrapper(query);

        // 查询列表
        List<GoogleSheetsConfig> configs = configMapper.selectList(queryWrapper);

        // 转换响应
        return configs.stream()
                .map(this::convertToConfigResp)
                .collect(Collectors.toList());
    }

    @Override
    public List<LabelValueResp<Long>> listConfigOptions(Long groupId) {
        log.debug("获取配置选项列表，群组ID: {}", groupId);

        LambdaQueryWrapper<GoogleSheetsConfig> queryWrapper = Wrappers.lambdaQueryWrapper();
        queryWrapper.eq(GoogleSheetsConfig::getGroupId, groupId)
                .eq(GoogleSheetsConfig::getEnabled, true)
                .eq(GoogleSheetsConfig::getConfigStatus, "ACTIVE")
                .select(GoogleSheetsConfig::getId, GoogleSheetsConfig::getConfigName)
                .orderByAsc(GoogleSheetsConfig::getConfigName);

        List<GoogleSheetsConfig> configs = configMapper.selectList(queryWrapper);

        return configs.stream()
                .map(config -> new LabelValueResp<>(config.getConfigName(), config.getId()))
                .collect(Collectors.toList());
    }

    // ==================== 配置操作 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean enableConfig(Long configId) {
        log.info("启用Google Sheets配置: {}", configId);

        GoogleSheetsConfig config = this.getConfigById(configId);
        config.setEnabled(true);
        config.setConfigStatus("ACTIVE");

        int updated = configMapper.updateById(config);
        boolean success = updated > 0;

        if (success) {
            log.info("Google Sheets配置启用成功，配置ID: {}", configId);
        }

        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean disableConfig(Long configId) {
        log.info("禁用Google Sheets配置: {}", configId);

        GoogleSheetsConfig config = this.getConfigById(configId);
        config.setEnabled(false);
        config.setConfigStatus("DISABLED");

        int updated = configMapper.updateById(config);
        boolean success = updated > 0;

        if (success) {
            log.info("Google Sheets配置禁用成功，配置ID: {}", configId);
        }

        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long copyConfig(Long configId, String newName) {
        log.info("复制Google Sheets配置: {} -> {}", configId, newName);

        // 获取原配置
        GoogleSheetsConfig originalConfig = this.getConfigById(configId);

        // 验证新名称唯一性
        this.checkConfigNameUnique(newName, originalConfig.getGroupId(), null);

        // 创建新配置
        GoogleSheetsConfig newConfig = new GoogleSheetsConfig();
        BeanUtil.copyProperties(originalConfig, newConfig);
        newConfig.setId(null);
        newConfig.setConfigName(newName);
        newConfig.setConfigVersion(1);
        newConfig.setVersionNotes("从配置 " + originalConfig.getConfigName() + " 复制");
        newConfig.setAuthStatus("PENDING");
        newConfig.setTokenExpiresAt(null);
        newConfig.setLastSyncTime(null);
        newConfig.setLastSyncStatus(null);
        newConfig.setLastSyncId(null);
        newConfig.setLastSyncMessage(null);
        newConfig.setTotalSyncCount(0);
        newConfig.setSuccessSyncCount(0);
        newConfig.setFailedSyncCount(0);
        newConfig.setSuccessRate(0.0);
        newConfig.setTotalProcessedRecords(0L);
        newConfig.setTotalSuccessRecords(0L);
        newConfig.setTotalFailedRecords(0L);
        newConfig.setAvgSyncDuration(null);
        newConfig.setMaxSyncDuration(null);
        newConfig.setMinSyncDuration(null);
        newConfig.setRecentSyncCount(0);
        newConfig.setRecentSuccessRate(0.0);
        newConfig.setCreateTime(LocalDateTime.now());
        newConfig.setUpdateTime(LocalDateTime.now());

        configMapper.insert(newConfig);

        log.info("Google Sheets配置复制成功，新配置ID: {}", newConfig.getId());
        return newConfig.getId();
    }

    @Override
    public String exportConfigs(List<Long> configIds) {
        log.info("导出Google Sheets配置: {}", configIds);

        // TODO: 实现配置导出逻辑
        // 1. 查询配置数据
        // 2. 转换为导出格式（JSON/Excel）
        // 3. 生成文件并返回路径

        return "/exports/google_sheets_configs_" + DateUtil.format(LocalDateTime.now(), "yyyyMMdd_HHmmss") + ".json";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> importConfigs(String filePath, Long groupId) {
        log.info("导入Google Sheets配置: {} -> 群组ID: {}", filePath, groupId);

        Map<String, Object> result = new HashMap<>();

        // TODO: 实现配置导入逻辑
        // 1. 读取文件内容
        // 2. 解析配置数据
        // 3. 验证数据格式
        // 4. 批量创建配置
        // 5. 返回导入结果

        result.put("success", true);
        result.put("totalCount", 0);
        result.put("successCount", 0);
        result.put("failedCount", 0);
        result.put("errors", new ArrayList<>());

        return result;
    }

    // ==================== 私有方法 ====================

    /**
     * 根据ID获取配置
     */
    private GoogleSheetsConfig getConfigById(Long configId) {
        GoogleSheetsConfig config = configMapper.selectById(configId);
        CheckUtils.throwIfNull(config, "Google Sheets配置不存在");
        return config;
    }

    /**
     * 检查配置名称唯一性
     */
    private void checkConfigNameUnique(String configName, Long groupId, Long excludeId) {
        LambdaQueryWrapper<GoogleSheetsConfig> queryWrapper = Wrappers.lambdaQueryWrapper();
        queryWrapper.eq(GoogleSheetsConfig::getConfigName, configName)
                .eq(GoogleSheetsConfig::getGroupId, groupId);

        if (excludeId != null) {
            queryWrapper.ne(GoogleSheetsConfig::getId, excludeId);
        }

        long count = configMapper.selectCount(queryWrapper);
        CheckUtils.throwIf(count > 0, "配置名称已存在");
    }

    /**
     * 验证Google Sheets ID格式
     */
    private void validateSpreadsheetId(String spreadsheetId) {
        CheckUtils.throwIfBlank(spreadsheetId, "Google Sheets ID不能为空");
        // TODO: 添加更严格的格式验证
    }

    /**
     * 检查是否有活跃的同步任务
     */
    private void checkNoActiveSyncTasks(Long configId) {
        // TODO: 检查是否有正在进行的同步任务
        // 查询同步日志表中状态为RUNNING的记录
    }

    /**
     * 构建配置查询条件
     */
    private LambdaQueryWrapper<GoogleSheetsConfig> buildConfigQueryWrapper(GoogleSheetsConfigQuery query) {
        LambdaQueryWrapper<GoogleSheetsConfig> queryWrapper = Wrappers.lambdaQueryWrapper();

        // 基础条件
        queryWrapper.eq(GoogleSheetsConfig::getGroupId, query.getGroupId());

        // 配置名称
        if (StrUtil.isNotBlank(query.getConfigName())) {
            queryWrapper.like(GoogleSheetsConfig::getConfigName, query.getConfigName());
        }

        // 同步状态
        if (StrUtil.isNotBlank(query.getSyncStatus())) {
            queryWrapper.eq(GoogleSheetsConfig::getLastSyncStatus, query.getSyncStatus());
        }

        // 认证类型
        if (StrUtil.isNotBlank(query.getAuthType())) {
            queryWrapper.eq(GoogleSheetsConfig::getAuthType, query.getAuthType());
        }

        // 启用状态
        if (query.getEnabled() != null) {
            queryWrapper.eq(GoogleSheetsConfig::getEnabled, query.getEnabled());
        }

        // 配置状态
        if (StrUtil.isNotBlank(query.getConfigStatus())) {
            queryWrapper.eq(GoogleSheetsConfig::getConfigStatus, query.getConfigStatus());
        }

        // 创建时间范围
        if (query.getCreateTimeStart() != null) {
            queryWrapper.ge(GoogleSheetsConfig::getCreateTime, query.getCreateTimeStart());
        }
        if (query.getCreateTimeEnd() != null) {
            queryWrapper.le(GoogleSheetsConfig::getCreateTime, query.getCreateTimeEnd());
        }

        // 最后同步时间范围
        if (query.getLastSyncTimeStart() != null) {
            queryWrapper.ge(GoogleSheetsConfig::getLastSyncTime, query.getLastSyncTimeStart());
        }
        if (query.getLastSyncTimeEnd() != null) {
            queryWrapper.le(GoogleSheetsConfig::getLastSyncTime, query.getLastSyncTimeEnd());
        }

        // 排序
        queryWrapper.orderByDesc(GoogleSheetsConfig::getUpdateTime);

        return queryWrapper;
    }

    /**
     * 转换为配置响应
     */
    private GoogleSheetsConfigResp convertToConfigResp(GoogleSheetsConfig config) {
        GoogleSheetsConfigResp resp = new GoogleSheetsConfigResp();
        BeanUtil.copyProperties(config, resp);

        // 设置配置ID
        resp.setConfigId(config.getId());

        // 解析JSON字段
        if (StrUtil.isNotBlank(config.getFieldMappingJson())) {
            // TODO: 解析字段映射JSON并转换为响应对象
        }
        if (StrUtil.isNotBlank(config.getFilterConditionJson())) {
            // TODO: 解析过滤条件JSON并转换为响应对象
        }
        if (StrUtil.isNotBlank(config.getSyncSettingsJson())) {
            // TODO: 解析同步设置JSON并转换为响应对象
        }
        if (StrUtil.isNotBlank(config.getCustomTagsJson())) {
            resp.setCustomTags(JSONUtil.toList(config.getCustomTagsJson(), String.class));
        }

        // 设置描述字段
        resp.setSyncDirectionDesc(this.getSyncDirectionDesc(config.getSyncDirection()));
        resp.setSyncModeDesc(this.getSyncModeDesc(config.getSyncMode()));
        resp.setAuthTypeDesc(this.getAuthTypeDesc(config.getAuthType()));
        resp.setAuthStatusDesc(this.getAuthStatusDesc(config.getAuthStatus()));
        resp.setConfigStatusDesc(this.getConfigStatusDesc(config.getConfigStatus()));

        // TODO: 设置其他复杂字段

        return resp;
    }

    /**
     * 获取同步方向描述
     */
    private String getSyncDirectionDesc(String syncDirection) {
        if (StrUtil.isBlank(syncDirection)) {
            return "";
        }
        switch (syncDirection) {
            case "TO_SHEETS":
                return "同步到表格";
            case "FROM_SHEETS":
                return "从表格同步";
            case "BIDIRECTIONAL":
                return "双向同步";
            default:
                return syncDirection;
        }
    }

    /**
     * 获取同步模式描述
     */
    private String getSyncModeDesc(String syncMode) {
        if (StrUtil.isBlank(syncMode)) {
            return "";
        }
        switch (syncMode) {
            case "REAL_TIME":
                return "实时同步";
            case "SCHEDULED":
                return "定时同步";
            case "MANUAL":
                return "手动同步";
            default:
                return syncMode;
        }
    }

    /**
     * 获取认证类型描述
     */
    private String getAuthTypeDesc(String authType) {
        if (StrUtil.isBlank(authType)) {
            return "";
        }
        switch (authType) {
            case "SERVICE_ACCOUNT":
                return "服务账号";
            case "OAUTH2":
                return "OAuth2";
            case "API_KEY":
                return "API密钥";
            default:
                return authType;
        }
    }

    /**
     * 获取认证状态描述
     */
    private String getAuthStatusDesc(String authStatus) {
        if (StrUtil.isBlank(authStatus)) {
            return "";
        }
        switch (authStatus) {
            case "VALID":
                return "认证有效";
            case "EXPIRED":
                return "认证过期";
            case "INVALID":
                return "认证无效";
            case "PENDING":
                return "待认证";
            default:
                return authStatus;
        }
    }

    /**
     * 获取配置状态描述
     */
    private String getConfigStatusDesc(String configStatus) {
        if (StrUtil.isBlank(configStatus)) {
            return "";
        }
        switch (configStatus) {
            case "ACTIVE":
                return "活跃";
            case "DISABLED":
                return "已禁用";
            case "ERROR":
                return "错误";
            case "MAINTENANCE":
                return "维护中";
            default:
                return configStatus;
        }
    }

    // ==================== 认证管理 ====================

    @Override
    public Map<String, Object> validateAuth(Long configId) {
        log.info("验证Google认证: {}", configId);

        GoogleSheetsConfig config = this.getConfigById(configId);
        Map<String, Object> result = new HashMap<>();

        try {
            // TODO: 实现具体的认证验证逻辑
            // 1. 根据认证类型验证
            // 2. 检查令牌有效性
            // 3. 测试API连接

            result.put("valid", true);
            result.put("authType", config.getAuthType());
            result.put("authStatus", config.getAuthStatus());
            result.put("tokenExpiresAt", config.getTokenExpiresAt());
            result.put("message", "认证验证成功");

        } catch (Exception e) {
            log.error("认证验证失败: {}", e.getMessage(), e);
            result.put("valid", false);
            result.put("message", "认证验证失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean refreshAuthToken(Long configId) {
        log.info("刷新认证令牌: {}", configId);

        GoogleSheetsConfig config = this.getConfigById(configId);

        try {
            // TODO: 实现具体的令牌刷新逻辑
            // 1. 根据认证类型刷新令牌
            // 2. 更新配置中的令牌信息
            // 3. 更新过期时间

            config.setAuthStatus("VALID");
            config.setTokenExpiresAt(LocalDateTime.now().plusHours(1)); // 示例：1小时后过期
            config.setUpdateTime(LocalDateTime.now());

            int updated = configMapper.updateById(config);
            boolean success = updated > 0;

            if (success) {
                log.info("认证令牌刷新成功，配置ID: {}", configId);
            }

            return success;

        } catch (Exception e) {
            log.error("认证令牌刷新失败: {}", e.getMessage(), e);

            // 更新认证状态为无效
            config.setAuthStatus("INVALID");
            config.setUpdateTime(LocalDateTime.now());
            configMapper.updateById(config);

            return false;
        }
    }

    @Override
    public String getOAuth2AuthUrl(Long configId) {
        log.info("获取OAuth2授权URL: {}", configId);

        GoogleSheetsConfig config = this.getConfigById(configId);
        CheckUtils.throwIf(!"OAUTH2".equals(config.getAuthType()), "配置不是OAuth2认证类型");

        try {
            // TODO: 实现OAuth2授权URL生成逻辑
            // 1. 构建授权URL
            // 2. 包含必要的参数（client_id, redirect_uri, scope等）
            // 3. 生成state参数用于安全验证

            String authUrl = "https://accounts.google.com/oauth/authorize" +
                    "?client_id=YOUR_CLIENT_ID" +
                    "&redirect_uri=YOUR_REDIRECT_URI" +
                    "&scope=https://www.googleapis.com/auth/spreadsheets" +
                    "&response_type=code" +
                    "&access_type=offline" +
                    "&state=" + configId;

            log.info("OAuth2授权URL生成成功，配置ID: {}", configId);
            return authUrl;

        } catch (Exception e) {
            log.error("OAuth2授权URL生成失败: {}", e.getMessage(), e);
            throw new RuntimeException("OAuth2授权URL生成失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean handleOAuth2Callback(Long configId, String code) {
        log.info("处理OAuth2回调: {}", configId);

        GoogleSheetsConfig config = this.getConfigById(configId);
        CheckUtils.throwIf(!"OAUTH2".equals(config.getAuthType()), "配置不是OAuth2认证类型");

        try {
            // TODO: 实现OAuth2回调处理逻辑
            // 1. 使用授权码换取访问令牌
            // 2. 获取刷新令牌
            // 3. 更新配置中的认证信息

            config.setAuthStatus("VALID");
            config.setTokenExpiresAt(LocalDateTime.now().plusHours(1)); // 示例：1小时后过期
            config.setUpdateTime(LocalDateTime.now());

            int updated = configMapper.updateById(config);
            boolean success = updated > 0;

            if (success) {
                log.info("OAuth2回调处理成功，配置ID: {}", configId);
            }

            return success;

        } catch (Exception e) {
            log.error("OAuth2回调处理失败: {}", e.getMessage(), e);

            // 更新认证状态为无效
            config.setAuthStatus("INVALID");
            config.setUpdateTime(LocalDateTime.now());
            configMapper.updateById(config);

            return false;
        }
    }

    // ==================== 表格操作 ====================

    @Override
    public Map<String, Object> getSpreadsheetInfo(Long configId) {
        log.info("获取表格信息: {}", configId);

        GoogleSheetsConfig config = this.getConfigById(configId);
        Map<String, Object> result = new HashMap<>();

        try {
            // TODO: 实现表格信息获取逻辑
            // 1. 验证认证状态
            // 2. 调用Google Sheets API获取表格信息
            // 3. 返回表格基本信息

            result.put("spreadsheetId", config.getSpreadsheetId());
            result.put("spreadsheetName", config.getSpreadsheetName());
            result.put("sheetCount", 1); // 示例数据
            result.put("lastModified", LocalDateTime.now());
            result.put("owner", "<EMAIL>");
            result.put("permissions", "edit");

        } catch (Exception e) {
            log.error("获取表格信息失败: {}", e.getMessage(), e);
            result.put("error", "获取表格信息失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public List<Map<String, Object>> getSheetList(Long configId) {
        log.info("获取工作表列表: {}", configId);

        GoogleSheetsConfig config = this.getConfigById(configId);
        List<Map<String, Object>> result = new ArrayList<>();

        try {
            // TODO: 实现工作表列表获取逻辑
            // 1. 验证认证状态
            // 2. 调用Google Sheets API获取工作表列表
            // 3. 返回工作表信息

            Map<String, Object> sheet = new HashMap<>();
            sheet.put("sheetId", 0);
            sheet.put("sheetName", config.getSheetName());
            sheet.put("rowCount", 1000);
            sheet.put("columnCount", 26);
            result.add(sheet);

        } catch (Exception e) {
            log.error("获取工作表列表失败: {}", e.getMessage(), e);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createSheet(Long configId, String sheetName) {
        log.info("创建工作表: {} -> {}", configId, sheetName);

        GoogleSheetsConfig config = this.getConfigById(configId);

        try {
            // TODO: 实现工作表创建逻辑
            // 1. 验证认证状态
            // 2. 调用Google Sheets API创建工作表
            // 3. 返回创建结果

            log.info("工作表创建成功: {}", sheetName);
            return true;

        } catch (Exception e) {
            log.error("工作表创建失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteSheet(Long configId, String sheetName) {
        log.info("删除工作表: {} -> {}", configId, sheetName);

        GoogleSheetsConfig config = this.getConfigById(configId);

        try {
            // TODO: 实现工作表删除逻辑
            // 1. 验证认证状态
            // 2. 调用Google Sheets API删除工作表
            // 3. 返回删除结果

            log.info("工作表删除成功: {}", sheetName);
            return true;

        } catch (Exception e) {
            log.error("工作表删除失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Map<String, Object> previewData(Long configId, Integer limit) {
        log.info("预览表格数据: {} -> 限制: {}", configId, limit);

        GoogleSheetsConfig config = this.getConfigById(configId);
        Map<String, Object> result = new HashMap<>();

        try {
            // TODO: 实现数据预览逻辑
            // 1. 验证认证状态
            // 2. 调用Google Sheets API获取数据
            // 3. 返回预览数据

            List<List<String>> data = new ArrayList<>();
            List<String> headers = List.of("日期", "描述", "金额", "分类");
            data.add(headers);

            for (int i = 1; i <= Math.min(limit, 5); i++) {
                List<String> row = List.of(
                    "2025-01-0" + i,
                    "示例交易 " + i,
                    "100.00",
                    "餐饮"
                );
                data.add(row);
            }

            result.put("data", data);
            result.put("totalRows", data.size());
            result.put("hasMore", false);

        } catch (Exception e) {
            log.error("预览表格数据失败: {}", e.getMessage(), e);
            result.put("error", "预览表格数据失败: " + e.getMessage());
        }

        return result;
    }
}
