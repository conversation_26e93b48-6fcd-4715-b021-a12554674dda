package top.continew.admin.accounting.service;

import top.continew.admin.common.base.service.BaseService;
import top.continew.admin.accounting.model.entity.GroupDO;
import top.continew.admin.accounting.model.query.GroupQuery;
import top.continew.admin.accounting.model.req.GroupCreateReq;
import top.continew.admin.accounting.model.req.GroupUpdateReq;
import top.continew.admin.accounting.model.resp.GroupDetailResp;
import top.continew.admin.accounting.model.resp.GroupListResp;
import top.continew.admin.accounting.enums.PlatformType;
import top.continew.starter.data.service.IService;

import java.util.List;

/**
 * 群组管理业务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface GroupService extends BaseService<GroupListResp, GroupDetailResp, GroupQuery, GroupCreateReq>, IService<GroupDO> {

    /**
     * 更新群组信息
     *
     * @param req 更新请求
     * @param id  群组ID
     */
    void update(GroupUpdateReq req, Long id);

    /**
     * 根据平台信息查找群组
     *
     * @param platform        平台类型
     * @param platformGroupId 平台群组ID
     * @return 群组信息
     */
    GroupDO getByPlatformInfo(PlatformType platform, String platformGroupId);

    /**
     * 获取用户的群组列表
     *
     * @param userId 用户ID
     * @return 群组列表
     */
    List<GroupListResp> getUserGroups(Long userId);

    /**
     * 检查用户是否为群组成员
     *
     * @param groupId 群组ID
     * @param userId  用户ID
     * @return 是否为成员
     */
    boolean isMember(Long groupId, Long userId);

    /**
     * 检查用户是否为群组管理员
     *
     * @param groupId 群组ID
     * @param userId  用户ID
     * @return 是否为管理员
     */
    boolean isAdmin(Long groupId, Long userId);

    /**
     * 检查交易次数是否超限
     *
     * @param groupId 群组ID
     * @return 是否超限
     */
    boolean isTransactionLimitExceeded(Long groupId);

    /**
     * 添加群组成员
     *
     * @param groupId        群组ID
     * @param userId         用户ID
     * @param platformUserId 平台用户ID
     * @param nickname       用户昵称
     * @param inviterId      邀请人ID
     */
    void addMember(Long groupId, Long userId, String platformUserId, String nickname, Long inviterId);

    /**
     * 移除群组成员
     *
     * @param groupId 群组ID
     * @param userId  用户ID
     */
    void removeMember(Long groupId, Long userId);
}
