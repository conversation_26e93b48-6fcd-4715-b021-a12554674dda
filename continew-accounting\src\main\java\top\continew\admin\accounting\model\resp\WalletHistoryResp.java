package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 钱包历史记录响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "钱包历史记录响应")
public class WalletHistoryResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Schema(description = "ID", example = "1")
    private Long id;

    /**
     * 操作类型
     */
    @Schema(description = "操作类型", example = "INCOME")
    private String operationType;

    /**
     * 金额
     */
    @Schema(description = "金额", example = "100.00")
    private BigDecimal amount;

    /**
     * 操作前余额
     */
    @Schema(description = "操作前余额", example = "500.00")
    private BigDecimal balanceBefore;

    /**
     * 操作后余额
     */
    @Schema(description = "操作后余额", example = "600.00")
    private BigDecimal balanceAfter;

    /**
     * 描述
     */
    @Schema(description = "描述", example = "午餐费用")
    private String description;

    /**
     * 操作人ID
     */
    @Schema(description = "操作人ID", example = "1")
    private Long operatorId;

    /**
     * 操作人姓名
     */
    @Schema(description = "操作人姓名", example = "张三")
    private String operatorName;

    /**
     * 操作时间
     */
    @Schema(description = "操作时间", example = "2025-01-01T12:00:00")
    private LocalDateTime operateTime;

    /**
     * 关联业务ID
     */
    @Schema(description = "关联业务ID", example = "123")
    private Long businessId;

    /**
     * 业务类型
     */
    @Schema(description = "业务类型", example = "TRANSACTION")
    private String businessType;

    /**
     * 币种
     */
    @Schema(description = "币种", example = "CNY")
    private String currency;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "系统自动记录")
    private String remark;
}
