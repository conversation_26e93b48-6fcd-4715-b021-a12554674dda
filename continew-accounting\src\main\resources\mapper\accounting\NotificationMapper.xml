<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.accounting.mapper.NotificationMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="top.continew.admin.accounting.model.entity.NotificationDO">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="content" property="content" />
        <result column="notification_type" property="notificationType" />
        <result column="priority" property="priority" />
        <result column="status" property="status" />
        <result column="channels" property="channels" typeHandler="top.continew.starter.data.mybatis.plus.handler.ListTypeHandler" />
        <result column="target_users" property="targetUsers" typeHandler="top.continew.starter.data.mybatis.plus.handler.ListTypeHandler" />
        <result column="target_groups" property="targetGroups" typeHandler="top.continew.starter.data.mybatis.plus.handler.ListTypeHandler" />
        <result column="target_roles" property="targetRoles" typeHandler="top.continew.starter.data.mybatis.plus.handler.ListTypeHandler" />
        <result column="send_time" property="sendTime" />
        <result column="scheduled_time" property="scheduledTime" />
        <result column="template_code" property="templateCode" />
        <result column="template_params" property="templateParams" typeHandler="top.continew.starter.data.mybatis.plus.handler.JsonTypeHandler" />
        <result column="attachments" property="attachments" typeHandler="top.continew.starter.data.mybatis.plus.handler.JsonTypeHandler" />
        <result column="extra_data" property="extraData" typeHandler="top.continew.starter.data.mybatis.plus.handler.JsonTypeHandler" />
        <result column="send_result" property="sendResult" typeHandler="top.continew.starter.data.mybatis.plus.handler.JsonTypeHandler" />
        <result column="retry_count" property="retryCount" />
        <result column="max_retries" property="maxRetries" />
        <result column="retry_interval" property="retryInterval" />
        <result column="group_id" property="groupId" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 查询待发送的计划通知 -->
    <select id="selectPendingScheduledNotifications" resultType="java.lang.Long">
        SELECT id
        FROM acc_notification
        WHERE status = 'PENDING'
          AND scheduled_time IS NOT NULL
          AND scheduled_time &lt;= #{currentTime}
        ORDER BY scheduled_time ASC
        LIMIT #{limit}
    </select>

    <!-- 查询需要重试的通知 -->
    <select id="selectNotificationsForRetry" resultType="java.lang.Long">
        SELECT id
        FROM acc_notification
        WHERE status = 'FAILED'
          AND retry_count &lt; max_retries
          AND (
              retry_count = 0 
              OR DATE_ADD(update_time, INTERVAL retry_interval SECOND) &lt;= #{currentTime}
          )
        ORDER BY update_time ASC
        LIMIT #{limit}
    </select>

    <!-- 删除过期通知 -->
    <delete id="deleteExpiredNotifications">
        DELETE FROM acc_notification
        WHERE create_time &lt; #{expiredBefore}
          AND status IN ('SENT', 'FAILED', 'CANCELLED')
    </delete>

    <!-- 查询通知发送日志 -->
    <select id="selectNotificationLogs" resultType="java.util.Map">
        SELECT 
            nl.id,
            nl.channel,
            nl.target_user,
            nl.status,
            nl.send_time,
            nl.response_data,
            nl.error_message,
            nl.duration,
            nl.external_message_id,
            u.nickname as target_user_name
        FROM acc_notification_log nl
        LEFT JOIN sys_user u ON nl.target_user = u.id
        WHERE nl.notification_id = #{notificationId}
        ORDER BY nl.send_time DESC
    </select>

    <!-- 查询通知发送详情 -->
    <select id="selectNotificationSendDetail" resultType="java.util.Map">
        SELECT 
            n.*,
            COUNT(nl.id) as log_count,
            COUNT(CASE WHEN nl.status = 'SENT' THEN 1 END) as success_count,
            COUNT(CASE WHEN nl.status = 'FAILED' THEN 1 END) as failed_count,
            AVG(nl.duration) as avg_duration
        FROM acc_notification n
        LEFT JOIN acc_notification_log nl ON n.id = nl.notification_id
        WHERE n.id = #{notificationId}
        GROUP BY n.id
    </select>

    <!-- 查询通知统计 -->
    <select id="selectNotificationStatistics" resultType="top.continew.admin.accounting.model.resp.NotificationStatisticsResp">
        SELECT 
            COUNT(*) as totalNotifications,
            COUNT(CASE WHEN DATE(create_time) = CURDATE() THEN 1 END) as todayNotifications,
            COUNT(CASE WHEN status = 'SENT' THEN 1 END) as successNotifications,
            COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failedNotifications,
            COUNT(CASE WHEN status = 'PENDING' THEN 1 END) as pendingNotifications,
            ROUND(
                COUNT(CASE WHEN status = 'SENT' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2
            ) as successRate,
            ROUND(AVG(
                CASE WHEN send_result IS NOT NULL 
                THEN JSON_EXTRACT(send_result, '$.duration') 
                END
            ), 2) as avgDuration
        FROM acc_notification
        WHERE 1=1
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE(create_time) &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE(create_time) &lt;= #{endDate}
        </if>
    </select>

    <!-- 查询渠道性能统计 -->
    <select id="selectChannelPerformanceStatistics" resultType="java.util.Map">
        SELECT 
            nl.channel,
            COUNT(*) as sendCount,
            COUNT(CASE WHEN nl.status = 'SENT' THEN 1 END) as successCount,
            COUNT(CASE WHEN nl.status = 'FAILED' THEN 1 END) as failedCount,
            ROUND(
                COUNT(CASE WHEN nl.status = 'SENT' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2
            ) as successRate,
            ROUND(AVG(nl.duration), 2) as avgDuration,
            MIN(nl.duration) as minDuration,
            MAX(nl.duration) as maxDuration
        FROM acc_notification_log nl
        INNER JOIN acc_notification n ON nl.notification_id = n.id
        WHERE 1=1
        <if test="groupId != null">
            AND n.group_id = #{groupId}
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE(nl.send_time) &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE(nl.send_time) &lt;= #{endDate}
        </if>
        GROUP BY nl.channel
        ORDER BY sendCount DESC
    </select>

    <!-- 查询发送趋势 -->
    <select id="selectSendTrends" resultType="java.util.Map">
        SELECT 
            <choose>
                <when test="groupBy == 'hour'">
                    DATE_FORMAT(n.create_time, '%Y-%m-%d %H:00:00') as timePoint
                </when>
                <when test="groupBy == 'day'">
                    DATE(n.create_time) as timePoint
                </when>
                <when test="groupBy == 'week'">
                    DATE_FORMAT(n.create_time, '%Y-%u') as timePoint
                </when>
                <when test="groupBy == 'month'">
                    DATE_FORMAT(n.create_time, '%Y-%m') as timePoint
                </when>
                <otherwise>
                    DATE(n.create_time) as timePoint
                </otherwise>
            </choose>,
            COUNT(*) as sendCount,
            COUNT(CASE WHEN n.status = 'SENT' THEN 1 END) as successCount,
            COUNT(CASE WHEN n.status = 'FAILED' THEN 1 END) as failedCount
        FROM acc_notification n
        WHERE 1=1
        <if test="groupId != null">
            AND n.group_id = #{groupId}
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE(n.create_time) &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE(n.create_time) &lt;= #{endDate}
        </if>
        GROUP BY timePoint
        ORDER BY timePoint ASC
    </select>

    <!-- 查询失败分析 -->
    <select id="selectFailureAnalysis" resultType="java.util.Map">
        SELECT 
            COUNT(*) as totalFailures,
            COUNT(CASE WHEN retry_count > 0 THEN 1 END) as retriedFailures,
            COUNT(CASE WHEN retry_count >= max_retries THEN 1 END) as permanentFailures,
            ROUND(AVG(retry_count), 2) as avgRetryCount,
            (
                SELECT JSON_ARRAYAGG(
                    JSON_OBJECT(
                        'errorType', error_type,
                        'count', error_count,
                        'percentage', ROUND(error_count * 100.0 / total_errors, 2)
                    )
                )
                FROM (
                    SELECT 
                        CASE 
                            WHEN JSON_EXTRACT(send_result, '$.errorMessage') LIKE '%timeout%' THEN 'TIMEOUT'
                            WHEN JSON_EXTRACT(send_result, '$.errorMessage') LIKE '%connection%' THEN 'CONNECTION'
                            WHEN JSON_EXTRACT(send_result, '$.errorMessage') LIKE '%auth%' THEN 'AUTHENTICATION'
                            WHEN JSON_EXTRACT(send_result, '$.errorMessage') LIKE '%rate%' THEN 'RATE_LIMIT'
                            ELSE 'OTHER'
                        END as error_type,
                        COUNT(*) as error_count,
                        (SELECT COUNT(*) FROM acc_notification WHERE status = 'FAILED') as total_errors
                    FROM acc_notification
                    WHERE status = 'FAILED'
                      AND send_result IS NOT NULL
                    <if test="groupId != null">
                        AND group_id = #{groupId}
                    </if>
                    <if test="startDate != null and startDate != ''">
                        AND DATE(create_time) &gt;= #{startDate}
                    </if>
                    <if test="endDate != null and endDate != ''">
                        AND DATE(create_time) &lt;= #{endDate}
                    </if>
                    GROUP BY error_type
                    ORDER BY error_count DESC
                ) error_stats
            ) as errorBreakdown
        FROM acc_notification
        WHERE status = 'FAILED'
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE(create_time) &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE(create_time) &lt;= #{endDate}
        </if>
    </select>

    <!-- 查询通知类型统计 -->
    <select id="selectNotificationTypeStatistics" resultType="java.util.Map">
        SELECT 
            notification_type as typeName,
            COUNT(*) as sendCount,
            COUNT(CASE WHEN status = 'SENT' THEN 1 END) as successCount,
            COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failedCount,
            ROUND(
                COUNT(CASE WHEN status = 'SENT' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2
            ) as successRate
        FROM acc_notification
        WHERE 1=1
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE(create_time) &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE(create_time) &lt;= #{endDate}
        </if>
        GROUP BY notification_type
        ORDER BY sendCount DESC
    </select>

    <!-- 查询渠道统计 -->
    <select id="selectChannelStatistics" resultType="java.util.Map">
        SELECT 
            nl.channel as channelName,
            COUNT(*) as sendCount,
            COUNT(CASE WHEN nl.status = 'SENT' THEN 1 END) as successCount,
            COUNT(CASE WHEN nl.status = 'FAILED' THEN 1 END) as failedCount,
            ROUND(
                COUNT(CASE WHEN nl.status = 'SENT' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2
            ) as successRate,
            ROUND(AVG(nl.duration), 2) as avgDuration
        FROM acc_notification_log nl
        INNER JOIN acc_notification n ON nl.notification_id = n.id
        WHERE 1=1
        <if test="groupId != null">
            AND n.group_id = #{groupId}
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE(nl.send_time) &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE(nl.send_time) &lt;= #{endDate}
        </if>
        GROUP BY nl.channel
        ORDER BY sendCount DESC
    </select>

    <!-- 查询用户通知统计 -->
    <select id="selectUserNotificationStatistics" resultType="java.util.Map">
        SELECT 
            nl.target_user as userId,
            u.nickname as userName,
            COUNT(*) as receiveCount,
            COUNT(CASE WHEN nl.status = 'SENT' THEN 1 END) as successCount,
            COUNT(CASE WHEN nl.status = 'FAILED' THEN 1 END) as failedCount,
            COUNT(DISTINCT n.notification_type) as typeCount,
            COUNT(DISTINCT nl.channel) as channelCount
        FROM acc_notification_log nl
        INNER JOIN acc_notification n ON nl.notification_id = n.id
        LEFT JOIN sys_user u ON nl.target_user = u.id
        WHERE 1=1
        <if test="groupId != null">
            AND n.group_id = #{groupId}
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE(nl.send_time) &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE(nl.send_time) &lt;= #{endDate}
        </if>
        GROUP BY nl.target_user, u.nickname
        ORDER BY receiveCount DESC
        LIMIT #{limit}
    </select>

    <!-- 查询模板使用统计 -->
    <select id="selectTemplateUsageStatistics" resultType="java.util.Map">
        SELECT 
            template_code as templateCode,
            nt.name as templateName,
            COUNT(*) as usageCount,
            COUNT(CASE WHEN n.status = 'SENT' THEN 1 END) as successCount,
            ROUND(
                COUNT(CASE WHEN n.status = 'SENT' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2
            ) as successRate,
            AVG(
                CASE WHEN send_result IS NOT NULL 
                THEN JSON_EXTRACT(send_result, '$.duration') 
                END
            ) as avgDuration
        FROM acc_notification n
        LEFT JOIN acc_notification_template nt ON n.template_code = nt.code
        WHERE template_code IS NOT NULL
        <if test="groupId != null">
            AND n.group_id = #{groupId}
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE(n.create_time) &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE(n.create_time) &lt;= #{endDate}
        </if>
        GROUP BY template_code, nt.name
        ORDER BY usageCount DESC
        LIMIT #{limit}
    </select>

    <!-- 查询发送性能分析 -->
    <select id="selectSendPerformanceAnalysis" resultType="java.util.Map">
        SELECT
            COUNT(*) as totalSends,
            ROUND(AVG(
                CASE WHEN send_result IS NOT NULL
                THEN JSON_EXTRACT(send_result, '$.duration')
                END
            ), 2) as avgDuration,
            MIN(
                CASE WHEN send_result IS NOT NULL
                THEN JSON_EXTRACT(send_result, '$.duration')
                END
            ) as minDuration,
            MAX(
                CASE WHEN send_result IS NOT NULL
                THEN JSON_EXTRACT(send_result, '$.duration')
                END
            ) as maxDuration,
            COUNT(CASE WHEN
                send_result IS NOT NULL
                AND JSON_EXTRACT(send_result, '$.duration') > 5000
                THEN 1 END
            ) as slowSends,
            ROUND(
                COUNT(CASE WHEN status = 'SENT' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2
            ) as overallSuccessRate
        FROM acc_notification
        WHERE send_time IS NOT NULL
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE(send_time) &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE(send_time) &lt;= #{endDate}
        </if>
    </select>

    <!-- 查询渠道响应时间统计 -->
    <select id="selectChannelResponseTimeStatistics" resultType="java.util.Map">
        SELECT
            nl.channel,
            COUNT(*) as sendCount,
            ROUND(AVG(nl.duration), 2) as avgDuration,
            MIN(nl.duration) as minDuration,
            MAX(nl.duration) as maxDuration,
            ROUND(STDDEV(nl.duration), 2) as stdDuration,
            COUNT(CASE WHEN nl.duration > 5000 THEN 1 END) as slowCount,
            ROUND(
                COUNT(CASE WHEN nl.duration > 5000 THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2
            ) as slowPercentage
        FROM acc_notification_log nl
        INNER JOIN acc_notification n ON nl.notification_id = n.id
        WHERE nl.duration IS NOT NULL
        <if test="groupId != null">
            AND n.group_id = #{groupId}
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE(nl.send_time) &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE(nl.send_time) &lt;= #{endDate}
        </if>
        GROUP BY nl.channel
        ORDER BY avgDuration DESC
    </select>

    <!-- 查询重试分析 -->
    <select id="selectRetryAnalysis" resultType="java.util.Map">
        SELECT
            COUNT(*) as totalNotifications,
            COUNT(CASE WHEN retry_count > 0 THEN 1 END) as retriedNotifications,
            ROUND(
                COUNT(CASE WHEN retry_count > 0 THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2
            ) as retryPercentage,
            ROUND(AVG(retry_count), 2) as avgRetryCount,
            MAX(retry_count) as maxRetryCount,
            COUNT(CASE WHEN retry_count >= max_retries THEN 1 END) as exhaustedRetries,
            COUNT(CASE WHEN retry_count > 0 AND status = 'SENT' THEN 1 END) as successAfterRetry
        FROM acc_notification
        WHERE 1=1
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE(create_time) &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE(create_time) &lt;= #{endDate}
        </if>
    </select>

    <!-- 查询通知健康状态 -->
    <select id="selectNotificationHealthStatus" resultType="java.util.Map">
        SELECT
            COUNT(*) as totalNotifications,
            COUNT(CASE WHEN create_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) as lastHourNotifications,
            COUNT(CASE WHEN create_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as last24HourNotifications,
            COUNT(CASE WHEN status = 'PENDING' THEN 1 END) as pendingNotifications,
            COUNT(CASE WHEN status = 'FAILED' AND retry_count >= max_retries THEN 1 END) as permanentFailures,
            ROUND(
                COUNT(CASE WHEN status = 'SENT' AND create_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) * 100.0 /
                NULLIF(COUNT(CASE WHEN create_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END), 0), 2
            ) as last24HourSuccessRate
        FROM acc_notification
    </select>

    <!-- 查询渠道健康状态 -->
    <select id="selectChannelHealthStatus" resultType="java.util.Map">
        SELECT
            nl.channel,
            COUNT(*) as totalSends,
            COUNT(CASE WHEN nl.send_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) as lastHourSends,
            COUNT(CASE WHEN nl.status = 'SENT' THEN 1 END) as successSends,
            COUNT(CASE WHEN nl.status = 'FAILED' THEN 1 END) as failedSends,
            ROUND(
                COUNT(CASE WHEN nl.status = 'SENT' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2
            ) as successRate,
            ROUND(AVG(nl.duration), 2) as avgDuration,
            MAX(nl.send_time) as lastSendTime,
            CASE
                WHEN MAX(nl.send_time) < DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 'INACTIVE'
                WHEN COUNT(CASE WHEN nl.status = 'SENT' AND nl.send_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) = 0
                     AND COUNT(CASE WHEN nl.send_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) > 0 THEN 'UNHEALTHY'
                ELSE 'HEALTHY'
            END as healthStatus
        FROM acc_notification_log nl
        WHERE nl.send_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        GROUP BY nl.channel
        ORDER BY successRate DESC
    </select>

    <!-- 查询异常通知 -->
    <select id="selectAbnormalNotifications" resultType="java.util.Map">
        SELECT
            n.id,
            n.title,
            n.notification_type,
            n.status,
            n.retry_count,
            n.max_retries,
            n.create_time,
            n.update_time,
            JSON_EXTRACT(n.send_result, '$.errorMessage') as errorMessage,
            TIMESTAMPDIFF(MINUTE, n.create_time, NOW()) as ageMinutes
        FROM acc_notification n
        WHERE (
            (n.status = 'FAILED' AND n.retry_count >= n.max_retries)
            OR (n.status = 'PENDING' AND n.create_time < DATE_SUB(NOW(), INTERVAL #{hours} HOUR))
            OR (n.status = 'SENDING' AND n.update_time < DATE_SUB(NOW(), INTERVAL 30 MINUTE))
        )
        ORDER BY n.create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 查询发送队列状态 -->
    <select id="selectSendQueueStatus" resultType="java.util.Map">
        SELECT
            COUNT(CASE WHEN status = 'PENDING' THEN 1 END) as pendingCount,
            COUNT(CASE WHEN status = 'SENDING' THEN 1 END) as sendingCount,
            COUNT(CASE WHEN status = 'PENDING' AND scheduled_time IS NOT NULL AND scheduled_time > NOW() THEN 1 END) as scheduledCount,
            COUNT(CASE WHEN status = 'FAILED' AND retry_count < max_retries THEN 1 END) as retryQueueCount,
            MIN(CASE WHEN status = 'PENDING' AND scheduled_time IS NOT NULL THEN scheduled_time END) as nextScheduledTime,
            COUNT(CASE WHEN status = 'PENDING' AND create_time < DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) as stalePendingCount
        FROM acc_notification
    </select>

</mapper>
