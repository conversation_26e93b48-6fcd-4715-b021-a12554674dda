package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 缓存统计响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "缓存统计响应")
public class CacheStatisticsResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 总体统计
     */
    @Schema(description = "总体统计")
    private OverallStatistics overall;

    /**
     * 缓存类型统计
     */
    @Schema(description = "缓存类型统计")
    private List<CacheTypeStatistics> cacheTypes;

    /**
     * 热点数据统计
     */
    @Schema(description = "热点数据统计")
    private List<HotspotStatistics> hotspots;

    /**
     * 性能趋势统计
     */
    @Schema(description = "性能趋势统计")
    private List<PerformanceTrend> trends;

    /**
     * 错误统计
     */
    @Schema(description = "错误统计")
    private List<ErrorStatistics> errors;

    /**
     * 总体统计
     */
    @Data
    @Schema(description = "总体统计")
    public static class OverallStatistics {
        /**
         * 总请求数
         */
        @Schema(description = "总请求数", example = "10000")
        private Long totalRequests;

        /**
         * 总命中数
         */
        @Schema(description = "总命中数", example = "8500")
        private Long totalHits;

        /**
         * 总未命中数
         */
        @Schema(description = "总未命中数", example = "1500")
        private Long totalMisses;

        /**
         * 总体命中率
         */
        @Schema(description = "总体命中率", example = "0.85")
        private BigDecimal overallHitRate;

        /**
         * 平均响应时间（毫秒）
         */
        @Schema(description = "平均响应时间（毫秒）", example = "15.5")
        private BigDecimal avgResponseTime;

        /**
         * QPS（每秒查询数）
         */
        @Schema(description = "QPS（每秒查询数）", example = "1000.0")
        private BigDecimal qps;

        /**
         * 总缓存大小
         */
        @Schema(description = "总缓存大小", example = "50000")
        private Long totalCacheSize;

        /**
         * 内存使用量（字节）
         */
        @Schema(description = "内存使用量（字节）", example = "104857600")
        private Long memoryUsage;

        /**
         * 内存使用量格式化
         */
        @Schema(description = "内存使用量格式化", example = "100.0 MB")
        private String memoryUsageFormatted;

        /**
         * 错误率
         */
        @Schema(description = "错误率", example = "0.01")
        private BigDecimal errorRate;

        /**
         * 活跃缓存数量
         */
        @Schema(description = "活跃缓存数量", example = "25")
        private Integer activeCacheCount;
    }

    /**
     * 缓存类型统计
     */
    @Data
    @Schema(description = "缓存类型统计")
    public static class CacheTypeStatistics {
        /**
         * 缓存名称
         */
        @Schema(description = "缓存名称", example = "USER_INFO")
        private String cacheName;

        /**
         * 缓存类型
         */
        @Schema(description = "缓存类型", example = "BOTH")
        private String cacheType;

        /**
         * 请求数
         */
        @Schema(description = "请求数", example = "5000")
        private Long requests;

        /**
         * 命中数
         */
        @Schema(description = "命中数", example = "4200")
        private Long hits;

        /**
         * 命中率
         */
        @Schema(description = "命中率", example = "0.84")
        private BigDecimal hitRate;

        /**
         * 平均加载时间（毫秒）
         */
        @Schema(description = "平均加载时间（毫秒）", example = "12.3")
        private BigDecimal avgLoadTime;

        /**
         * 缓存大小
         */
        @Schema(description = "缓存大小", example = "2000")
        private Long cacheSize;

        /**
         * 内存使用量（字节）
         */
        @Schema(description = "内存使用量（字节）", example = "20971520")
        private Long memoryUsage;

        /**
         * 错误次数
         */
        @Schema(description = "错误次数", example = "5")
        private Long errorCount;
    }

    /**
     * 热点数据统计
     */
    @Data
    @Schema(description = "热点数据统计")
    public static class HotspotStatistics {
        /**
         * 缓存键
         */
        @Schema(description = "缓存键", example = "USER:123")
        private String cacheKey;

        /**
         * 访问次数
         */
        @Schema(description = "访问次数", example = "500")
        private Long accessCount;

        /**
         * 命中次数
         */
        @Schema(description = "命中次数", example = "480")
        private Long hitCount;

        /**
         * 命中率
         */
        @Schema(description = "命中率", example = "0.96")
        private BigDecimal hitRate;

        /**
         * 最后访问时间
         */
        @Schema(description = "最后访问时间", example = "2025-01-01 12:00:00")
        private LocalDateTime lastAccessTime;

        /**
         * 数据大小（字节）
         */
        @Schema(description = "数据大小（字节）", example = "1024")
        private Long dataSize;
    }

    /**
     * 性能趋势统计
     */
    @Data
    @Schema(description = "性能趋势统计")
    public static class PerformanceTrend {
        /**
         * 时间点
         */
        @Schema(description = "时间点", example = "2025-01-01 12:00:00")
        private LocalDateTime timestamp;

        /**
         * 请求数
         */
        @Schema(description = "请求数", example = "1000")
        private Long requests;

        /**
         * 命中率
         */
        @Schema(description = "命中率", example = "0.85")
        private BigDecimal hitRate;

        /**
         * 平均响应时间（毫秒）
         */
        @Schema(description = "平均响应时间（毫秒）", example = "15.5")
        private BigDecimal avgResponseTime;

        /**
         * QPS
         */
        @Schema(description = "QPS", example = "100.0")
        private BigDecimal qps;

        /**
         * 错误率
         */
        @Schema(description = "错误率", example = "0.01")
        private BigDecimal errorRate;

        /**
         * 内存使用量（字节）
         */
        @Schema(description = "内存使用量（字节）", example = "10485760")
        private Long memoryUsage;
    }

    /**
     * 错误统计
     */
    @Data
    @Schema(description = "错误统计")
    public static class ErrorStatistics {
        /**
         * 错误类型
         */
        @Schema(description = "错误类型", example = "TIMEOUT")
        private String errorType;

        /**
         * 错误次数
         */
        @Schema(description = "错误次数", example = "10")
        private Long errorCount;

        /**
         * 错误率
         */
        @Schema(description = "错误率", example = "0.001")
        private BigDecimal errorRate;

        /**
         * 最后发生时间
         */
        @Schema(description = "最后发生时间", example = "2025-01-01 12:00:00")
        private LocalDateTime lastOccurTime;

        /**
         * 错误详情
         */
        @Schema(description = "错误详情")
        private Map<String, Object> errorDetails;
    }

}
