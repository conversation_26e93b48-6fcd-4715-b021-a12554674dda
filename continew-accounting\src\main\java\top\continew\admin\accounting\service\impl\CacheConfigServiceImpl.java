package top.continew.admin.accounting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.accounting.mapper.CacheConfigMapper;
import top.continew.admin.accounting.model.entity.CacheConfigDO;
import top.continew.admin.accounting.model.req.CacheConfigReq;
import top.continew.admin.accounting.model.req.CacheQueryReq;
import top.continew.admin.accounting.service.CacheConfigService;
import top.continew.starter.core.util.validate.CheckUtils;
import top.continew.starter.extension.crud.service.impl.BaseServiceImpl;

import java.time.LocalDateTime;

/**
 * 缓存配置服务实现
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CacheConfigServiceImpl extends BaseServiceImpl<CacheConfigMapper, CacheConfigDO, Long, CacheQueryReq, CacheConfigReq> implements CacheConfigService {

    @Override
    public CacheConfigDO getByConfigCode(String configCode) {
        LambdaQueryWrapper<CacheConfigDO> queryWrapper = Wrappers.lambdaQueryWrapper();
        queryWrapper.eq(CacheConfigDO::getConfigCode, configCode);
        queryWrapper.last("LIMIT 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public CacheConfigDO getByConfigCodeAndGroupId(String configCode, Long groupId) {
        LambdaQueryWrapper<CacheConfigDO> queryWrapper = Wrappers.lambdaQueryWrapper();
        queryWrapper.eq(CacheConfigDO::getConfigCode, configCode);
        if (groupId != null) {
            queryWrapper.eq(CacheConfigDO::getGroupId, groupId);
        } else {
            queryWrapper.isNull(CacheConfigDO::getGroupId);
        }
        queryWrapper.last("LIMIT 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public Boolean existsByConfigCode(String configCode, Long excludeId, Long groupId) {
        LambdaQueryWrapper<CacheConfigDO> queryWrapper = Wrappers.lambdaQueryWrapper();
        queryWrapper.eq(CacheConfigDO::getConfigCode, configCode);
        if (excludeId != null) {
            queryWrapper.ne(CacheConfigDO::getId, excludeId);
        }
        if (groupId != null) {
            queryWrapper.eq(CacheConfigDO::getGroupId, groupId);
        } else {
            queryWrapper.isNull(CacheConfigDO::getGroupId);
        }
        return this.count(queryWrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean applyCacheConfig(Long configId) {
        CacheConfigDO config = this.getById(configId);
        CheckUtils.throwIfNull(config, "缓存配置不存在");

        try {
            // 更新应用统计
            config.setLastAppliedTime(LocalDateTime.now());
            config.setAppliedCount(config.getAppliedCount() + 1);
            config.setStatus("ENABLE");
            this.updateById(config);

            log.info("缓存配置应用成功: {}", config.getConfigCode());
            return true;
        } catch (Exception e) {
            log.error("缓存配置应用失败: {}", config.getConfigCode(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean enableCacheConfig(Long configId) {
        CacheConfigDO config = this.getById(configId);
        CheckUtils.throwIfNull(config, "缓存配置不存在");

        config.setStatus("ENABLE");
        return this.updateById(config);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean disableCacheConfig(Long configId) {
        CacheConfigDO config = this.getById(configId);
        CheckUtils.throwIfNull(config, "缓存配置不存在");

        config.setStatus("DISABLE");
        return this.updateById(config);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long copyCacheConfig(Long configId, String newName, String newCode) {
        CacheConfigDO originalConfig = this.getById(configId);
        CheckUtils.throwIfNull(originalConfig, "缓存配置不存在");

        // 验证新配置代码唯一性
        CheckUtils.throwIf(this.existsByConfigCode(newCode, null, originalConfig.getGroupId()),
                "配置代码 [{}] 已存在", newCode);

        // 复制配置
        CacheConfigDO newConfig = new CacheConfigDO();
        BeanUtil.copyProperties(originalConfig, newConfig);
        newConfig.setId(null);
        newConfig.setConfigName(newName);
        newConfig.setConfigCode(newCode);
        newConfig.setStatus("DISABLE"); // 新配置默认禁用
        newConfig.setLastAppliedTime(null);
        newConfig.setAppliedCount(0L);

        this.save(newConfig);
        return newConfig.getId();
    }

    @Override
    public Boolean validateCacheConfig(Long configId) {
        CacheConfigDO config = this.getById(configId);
        CheckUtils.throwIfNull(config, "缓存配置不存在");

        try {
            // 验证配置的有效性
            if (StrUtil.isBlank(config.getConfigName()) || StrUtil.isBlank(config.getConfigCode())) {
                return false;
            }

            if (config.getCacheType() == null || config.getCacheStrategy() == null) {
                return false;
            }

            if (config.getExpireTime() != null && config.getExpireTime() <= 0) {
                return false;
            }

            if (config.getLocalMaxSize() != null && config.getLocalMaxSize() <= 0) {
                return false;
            }

            if (config.getAutoRefresh() != null && config.getAutoRefresh() &&
                (config.getRefreshInterval() == null || config.getRefreshInterval() <= 0)) {
                return false;
            }

            return true;
        } catch (Exception e) {
            log.error("缓存配置验证失败: {}", config.getConfigCode(), e);
            return false;
        }
    }

    @Override
    public Boolean testCacheConfig(Long configId) {
        CacheConfigDO config = this.getById(configId);
        CheckUtils.throwIfNull(config, "缓存配置不存在");

        try {
            // 执行缓存配置测试
            // 这里可以根据具体需求实现测试逻辑
            log.info("缓存配置测试成功: {}", config.getConfigCode());
            return true;
        } catch (Exception e) {
            log.error("缓存配置测试失败: {}", config.getConfigCode(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean resetCacheConfigStatistics(Long configId) {
        CacheConfigDO config = this.getById(configId);
        CheckUtils.throwIfNull(config, "缓存配置不存在");

        config.setLastAppliedTime(null);
        config.setAppliedCount(0L);
        return this.updateById(config);
    }

    @Override
    protected void fillQueryWrapper(CacheQueryReq req, LambdaQueryWrapper<CacheConfigDO> queryWrapper) {
        queryWrapper.like(StrUtil.isNotBlank(req.getConfigName()), CacheConfigDO::getConfigName, req.getConfigName())
                .like(StrUtil.isNotBlank(req.getConfigCode()), CacheConfigDO::getConfigCode, req.getConfigCode())
                .eq(req.getCacheType() != null, CacheConfigDO::getCacheType, req.getCacheType())
                .like(StrUtil.isNotBlank(req.getKeyPrefix()), CacheConfigDO::getKeyPrefix, req.getKeyPrefix())
                .eq(StrUtil.isNotBlank(req.getStatus()), CacheConfigDO::getStatus, req.getStatus())
                .eq(req.getGroupId() != null, CacheConfigDO::getGroupId, req.getGroupId())
                .eq(req.getStatisticsEnabled() != null, CacheConfigDO::getStatisticsEnabled, req.getStatisticsEnabled())
                .eq(req.getMonitorEnabled() != null, CacheConfigDO::getMonitorEnabled, req.getMonitorEnabled())
                .eq(req.getHotspotDetection() != null, CacheConfigDO::getHotspotDetection, req.getHotspotDetection())
                .ge(req.getCreateTimeStart() != null, CacheConfigDO::getCreateTime, req.getCreateTimeStart())
                .le(req.getCreateTimeEnd() != null, CacheConfigDO::getCreateTime, req.getCreateTimeEnd())
                .ge(req.getLastAppliedTimeStart() != null, CacheConfigDO::getLastAppliedTime, req.getLastAppliedTimeStart())
                .le(req.getLastAppliedTimeEnd() != null, CacheConfigDO::getLastAppliedTime, req.getLastAppliedTimeEnd())
                .orderByDesc(CacheConfigDO::getCreateTime);
    }

    @Override
    protected void fillEntity(CacheConfigReq req, CacheConfigDO entity) {
        entity.setConfigName(req.getConfigName());
        entity.setConfigCode(req.getConfigCode());
        entity.setCacheType(req.getCacheType());
        entity.setCacheStrategy(req.getCacheStrategy());
        entity.setEvictionPolicy(req.getEvictionPolicy());
        entity.setKeyPrefix(req.getKeyPrefix());
        entity.setKeyPattern(req.getKeyPattern());
        entity.setExpireTime(req.getExpireTime());
        entity.setLocalMaxSize(req.getLocalMaxSize());
        entity.setLocalExpireTime(req.getLocalExpireTime());
        entity.setRemoteExpireTime(req.getRemoteExpireTime());
        entity.setPenetrationProtect(req.getPenetrationProtect());
        entity.setAvalancheProtect(req.getAvalancheProtect());
        entity.setBreakdownProtect(req.getBreakdownProtect());
        entity.setAutoRefresh(req.getAutoRefresh());
        entity.setRefreshInterval(req.getRefreshInterval());
        entity.setPreloadEnabled(req.getPreloadEnabled());
        entity.setPreloadDataSource(req.getPreloadDataSource());
        entity.setPreloadStrategy(req.getPreloadStrategy());
        entity.setStatisticsEnabled(req.getStatisticsEnabled());
        entity.setStatisticsInterval(req.getStatisticsInterval());
        entity.setMonitorEnabled(req.getMonitorEnabled());
        entity.setMonitorThresholds(req.getMonitorThresholds());
        entity.setHotspotDetection(req.getHotspotDetection());
        entity.setHotspotThreshold(req.getHotspotThreshold());
        entity.setHotspotTimeWindow(req.getHotspotTimeWindow());
        entity.setStatus(req.getStatus());
        entity.setPriority(req.getPriority());
        entity.setDescription(req.getDescription());
        entity.setExtendConfig(req.getExtendConfig());
    }

}
