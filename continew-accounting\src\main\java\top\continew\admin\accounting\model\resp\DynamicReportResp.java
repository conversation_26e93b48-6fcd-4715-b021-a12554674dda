package top.continew.admin.accounting.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 动态报表响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "动态报表响应")
public class DynamicReportResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 报表ID
     */
    @Schema(description = "报表ID", example = "1")
    private Long reportId;

    /**
     * 任务ID（异步生成时）
     */
    @Schema(description = "任务ID", example = "task_12345")
    private String taskId;

    /**
     * 模板ID
     */
    @Schema(description = "模板ID", example = "1")
    private Long templateId;

    /**
     * 模板名称
     */
    @Schema(description = "模板名称", example = "月度财务报表")
    private String templateName;

    /**
     * 报表名称
     */
    @Schema(description = "报表名称", example = "2025年1月财务报表")
    private String reportName;

    /**
     * 报表描述
     */
    @Schema(description = "报表描述", example = "2025年1月份的详细财务分析报表")
    private String reportDescription;

    /**
     * 生成状态
     */
    @Schema(description = "生成状态", example = "SUCCESS", allowableValues = {"PENDING", "RUNNING", "SUCCESS", "FAILED", "CANCELLED"})
    private String status;

    /**
     * 生成状态名称
     */
    @Schema(description = "生成状态名称", example = "成功")
    private String statusName;

    /**
     * 进度百分比
     */
    @Schema(description = "进度百分比", example = "100")
    private Integer progress;

    /**
     * 当前步骤
     */
    @Schema(description = "当前步骤", example = "数据导出完成")
    private String currentStep;

    /**
     * 导出格式
     */
    @Schema(description = "导出格式", example = "PDF")
    private String exportFormat;

    /**
     * 文件路径
     */
    @Schema(description = "文件路径", example = "/reports/financial/2025-01-monthly-report.pdf")
    private String filePath;

    /**
     * 下载URL
     */
    @Schema(description = "下载URL", example = "https://example.com/download/reports/12345")
    private String downloadUrl;

    /**
     * 文件大小（字节）
     */
    @Schema(description = "文件大小", example = "2048576")
    private Long fileSize;

    /**
     * 文件大小（可读格式）
     */
    @Schema(description = "文件大小（可读格式）", example = "2.0 MB")
    private String fileSizeFormatted;

    /**
     * 数据量
     */
    @Schema(description = "数据量", example = "1500")
    private Integer dataVolume;

    /**
     * 执行时间（毫秒）
     */
    @Schema(description = "执行时间", example = "3500")
    private Long executionTime;

    /**
     * 执行时间（可读格式）
     */
    @Schema(description = "执行时间（可读格式）", example = "3.5秒")
    private String executionTimeFormatted;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2025-01-01 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", example = "2025-01-01 10:03:30")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID", example = "1")
    private Long createdBy;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名", example = "张三")
    private String createdByName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-01-01 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 过期时间
     */
    @Schema(description = "过期时间", example = "2025-01-31 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expireTime;

    /**
     * 是否已过期
     */
    @Schema(description = "是否已过期", example = "false")
    private Boolean expired;

    /**
     * 访问次数
     */
    @Schema(description = "访问次数", example = "5")
    private Integer accessCount;

    /**
     * 最后访问时间
     */
    @Schema(description = "最后访问时间", example = "2025-01-01 15:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastAccessTime;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 错误详情
     */
    @Schema(description = "错误详情")
    private String errorDetail;

    /**
     * 警告信息
     */
    @Schema(description = "警告信息")
    private List<String> warnings;

    /**
     * 生成参数
     */
    @Schema(description = "生成参数")
    private GenerationParams generationParams;

    /**
     * 执行统计
     */
    @Schema(description = "执行统计")
    private ExecutionStatistics executionStats;

    /**
     * 数据统计
     */
    @Schema(description = "数据统计")
    private DataStatistics dataStats;

    /**
     * 质量评估
     */
    @Schema(description = "质量评估")
    private QualityAssessment qualityAssessment;

    /**
     * 标签
     */
    @Schema(description = "标签", example = "[\"月报\", \"财务\"]")
    private List<String> tags;

    /**
     * 扩展属性
     */
    @Schema(description = "扩展属性")
    private Map<String, Object> attributes;

    /**
     * 生成参数
     */
    @Data
    @Schema(description = "生成参数")
    public static class GenerationParams implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 过滤器参数
         */
        @Schema(description = "过滤器参数")
        private Map<String, Object> filterParams;

        /**
         * 动态参数
         */
        @Schema(description = "动态参数")
        private Map<String, Object> dynamicParams;

        /**
         * 数据范围
         */
        @Schema(description = "数据范围")
        private Map<String, Object> dataRange;

        /**
         * 生成选项
         */
        @Schema(description = "生成选项")
        private Map<String, Object> generationOptions;

        /**
         * 输出配置
         */
        @Schema(description = "输出配置")
        private Map<String, Object> outputConfig;
    }

    /**
     * 执行统计
     */
    @Data
    @Schema(description = "执行统计")
    public static class ExecutionStatistics implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 数据查询时间（毫秒）
         */
        @Schema(description = "数据查询时间", example = "1200")
        private Long dataQueryTime;

        /**
         * 数据处理时间（毫秒）
         */
        @Schema(description = "数据处理时间", example = "800")
        private Long dataProcessingTime;

        /**
         * 报表渲染时间（毫秒）
         */
        @Schema(description = "报表渲染时间", example = "1500")
        private Long reportRenderingTime;

        /**
         * 文件生成时间（毫秒）
         */
        @Schema(description = "文件生成时间", example = "500")
        private Long fileGenerationTime;

        /**
         * 内存使用峰值（MB）
         */
        @Schema(description = "内存使用峰值", example = "128")
        private Long peakMemoryUsage;

        /**
         * CPU使用率峰值（百分比）
         */
        @Schema(description = "CPU使用率峰值", example = "75.5")
        private Double peakCpuUsage;

        /**
         * 缓存命中次数
         */
        @Schema(description = "缓存命中次数", example = "3")
        private Integer cacheHits;

        /**
         * 缓存未命中次数
         */
        @Schema(description = "缓存未命中次数", example = "1")
        private Integer cacheMisses;

        /**
         * 缓存命中率（百分比）
         */
        @Schema(description = "缓存命中率", example = "75.0")
        private Double cacheHitRate;
    }

    /**
     * 数据统计
     */
    @Data
    @Schema(description = "数据统计")
    public static class DataStatistics implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 总记录数
         */
        @Schema(description = "总记录数", example = "1500")
        private Integer totalRecords;

        /**
         * 处理记录数
         */
        @Schema(description = "处理记录数", example = "1500")
        private Integer processedRecords;

        /**
         * 过滤记录数
         */
        @Schema(description = "过滤记录数", example = "50")
        private Integer filteredRecords;

        /**
         * 错误记录数
         */
        @Schema(description = "错误记录数", example = "2")
        private Integer errorRecords;

        /**
         * 数据完整性（百分比）
         */
        @Schema(description = "数据完整性", example = "98.7")
        private Double dataIntegrity;

        /**
         * 数据准确性（百分比）
         */
        @Schema(description = "数据准确性", example = "99.2")
        private Double dataAccuracy;

        /**
         * 数据时效性评分
         */
        @Schema(description = "数据时效性评分", example = "95")
        private Integer dataFreshnessScore;

        /**
         * 数据源统计
         */
        @Schema(description = "数据源统计")
        private Map<String, Integer> dataSourceStats;

        /**
         * 字段统计
         */
        @Schema(description = "字段统计")
        private Map<String, Object> fieldStats;
    }

    /**
     * 质量评估
     */
    @Data
    @Schema(description = "质量评估")
    public static class QualityAssessment implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 总体质量评分
         */
        @Schema(description = "总体质量评分", example = "92")
        private Integer overallScore;

        /**
         * 数据质量评分
         */
        @Schema(description = "数据质量评分", example = "95")
        private Integer dataQualityScore;

        /**
         * 性能评分
         */
        @Schema(description = "性能评分", example = "88")
        private Integer performanceScore;

        /**
         * 可读性评分
         */
        @Schema(description = "可读性评分", example = "90")
        private Integer readabilityScore;

        /**
         * 完整性评分
         */
        @Schema(description = "完整性评分", example = "94")
        private Integer completenessScore;

        /**
         * 质量等级
         */
        @Schema(description = "质量等级", example = "EXCELLENT", allowableValues = {"POOR", "FAIR", "GOOD", "VERY_GOOD", "EXCELLENT"})
        private String qualityGrade;

        /**
         * 质量等级名称
         */
        @Schema(description = "质量等级名称", example = "优秀")
        private String qualityGradeName;

        /**
         * 改进建议
         */
        @Schema(description = "改进建议")
        private List<String> improvementSuggestions;

        /**
         * 质量问题
         */
        @Schema(description = "质量问题")
        private List<QualityIssue> qualityIssues;

        @Data
        @Schema(description = "质量问题")
        public static class QualityIssue implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 问题类型
             */
            @Schema(description = "问题类型", example = "DATA_MISSING")
            private String issueType;

            /**
             * 问题描述
             */
            @Schema(description = "问题描述", example = "部分数据缺失")
            private String description;

            /**
             * 严重程度
             */
            @Schema(description = "严重程度", example = "MEDIUM", allowableValues = {"LOW", "MEDIUM", "HIGH", "CRITICAL"})
            private String severity;

            /**
             * 影响范围
             */
            @Schema(description = "影响范围", example = "5%的数据受影响")
            private String impact;

            /**
             * 建议解决方案
             */
            @Schema(description = "建议解决方案", example = "检查数据源完整性")
            private String suggestedSolution;
        }
    }
}
