package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import top.continew.admin.accounting.enums.PlatformType;

/**
 * 群组创建请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "群组创建请求")
public class GroupCreateReq {

    /**
     * 群组名称
     */
    @Schema(description = "群组名称")
    @NotBlank(message = "群组名称不能为空")
    @Size(max = 100, message = "群组名称长度不能超过100个字符")
    private String name;

    /**
     * 群组描述
     */
    @Schema(description = "群组描述")
    @Size(max = 500, message = "群组描述长度不能超过500个字符")
    private String description;

    /**
     * 平台类型
     */
    @Schema(description = "平台类型")
    @NotNull(message = "平台类型不能为空")
    private PlatformType platform;

    /**
     * 平台群组ID
     */
    @Schema(description = "平台群组ID")
    @NotBlank(message = "平台群组ID不能为空")
    @Size(max = 100, message = "平台群组ID长度不能超过100个字符")
    private String platformGroupId;

    /**
     * 群主用户ID
     */
    @Schema(description = "群主用户ID")
    @NotNull(message = "群主用户ID不能为空")
    private Long ownerId;

    /**
     * 默认币种
     */
    @Schema(description = "默认币种")
    @Size(max = 10, message = "默认币种长度不能超过10个字符")
    private String defaultCurrency = "CNY";

    /**
     * 时区
     */
    @Schema(description = "时区")
    @Size(max = 50, message = "时区长度不能超过50个字符")
    private String timezone = "Asia/Shanghai";
}
