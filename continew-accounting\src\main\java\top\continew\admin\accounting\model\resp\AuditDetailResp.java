package top.continew.admin.accounting.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 审核详情响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "审核详情响应")
public class AuditDetailResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 审核ID
     */
    @Schema(description = "审核ID", example = "1")
    private Long auditId;

    /**
     * 审核编号
     */
    @Schema(description = "审核编号", example = "AUDIT_20250101_001")
    private String auditNumber;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 群组名称
     */
    @Schema(description = "群组名称", example = "家庭账本")
    private String groupName;

    /**
     * 审核标题
     */
    @Schema(description = "审核标题", example = "2025年1月餐饮费用审核")
    private String auditTitle;

    /**
     * 审核描述
     */
    @Schema(description = "审核描述", example = "请审核1月份的餐饮消费记录")
    private String auditDescription;

    /**
     * 审核类型
     */
    @Schema(description = "审核类型", example = "EXPENSE_AUDIT")
    private String auditType;

    /**
     * 审核类型名称
     */
    @Schema(description = "审核类型名称", example = "支出审核")
    private String auditTypeName;

    /**
     * 审核状态
     */
    @Schema(description = "审核状态", example = "PENDING")
    private String auditStatus;

    /**
     * 审核状态名称
     */
    @Schema(description = "审核状态名称", example = "待审核")
    private String auditStatusName;

    /**
     * 优先级
     */
    @Schema(description = "优先级", example = "NORMAL")
    private String priority;

    /**
     * 优先级名称
     */
    @Schema(description = "优先级名称", example = "普通")
    private String priorityName;

    /**
     * 提交人信息
     */
    @Schema(description = "提交人信息")
    private SubmitterInfo submitter;

    /**
     * 审核人信息
     */
    @Schema(description = "审核人信息")
    private List<AuditorInfo> auditors;

    /**
     * 交易信息
     */
    @Schema(description = "交易信息")
    private List<TransactionInfo> transactions;

    /**
     * 审核进度
     */
    @Schema(description = "审核进度")
    private AuditProgress progress;

    /**
     * 审核历史
     */
    @Schema(description = "审核历史")
    private List<AuditHistory> auditHistory;

    /**
     * 审核规则
     */
    @Schema(description = "审核规则")
    private AuditRulesInfo rulesInfo;

    /**
     * 统计信息
     */
    @Schema(description = "统计信息")
    private AuditStatistics statistics;

    /**
     * 附件列表
     */
    @Schema(description = "附件列表")
    private List<String> attachments;

    /**
     * 扩展属性
     */
    @Schema(description = "扩展属性")
    private Map<String, Object> attributes;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-01-01 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2025-01-01 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 截止时间
     */
    @Schema(description = "截止时间", example = "2025-01-15 18:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deadline;

    /**
     * 提交人信息
     */
    @Data
    @Schema(description = "提交人信息")
    public static class SubmitterInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 用户ID
         */
        @Schema(description = "用户ID", example = "1")
        private Long userId;

        /**
         * 用户姓名
         */
        @Schema(description = "用户姓名", example = "张三")
        private String userName;

        /**
         * 用户昵称
         */
        @Schema(description = "用户昵称", example = "小张")
        private String nickname;

        /**
         * 部门名称
         */
        @Schema(description = "部门名称", example = "财务部")
        private String departmentName;

        /**
         * 职位名称
         */
        @Schema(description = "职位名称", example = "会计")
        private String positionName;

        /**
         * 联系方式
         */
        @Schema(description = "联系方式")
        private ContactInfo contactInfo;

        @Data
        @Schema(description = "联系方式")
        public static class ContactInfo implements Serializable {

            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 邮箱
             */
            @Schema(description = "邮箱", example = "<EMAIL>")
            private String email;

            /**
             * 手机号
             */
            @Schema(description = "手机号", example = "13800138000")
            private String phone;

            /**
             * 办公电话
             */
            @Schema(description = "办公电话", example = "010-12345678")
            private String officePhone;
        }
    }

    /**
     * 审核人信息
     */
    @Data
    @Schema(description = "审核人信息")
    public static class AuditorInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 审核人ID
         */
        @Schema(description = "审核人ID", example = "2")
        private Long auditorId;

        /**
         * 审核人姓名
         */
        @Schema(description = "审核人姓名", example = "李四")
        private String auditorName;

        /**
         * 审核人昵称
         */
        @Schema(description = "审核人昵称", example = "小李")
        private String auditorNickname;

        /**
         * 审核角色
         */
        @Schema(description = "审核角色", example = "PRIMARY_AUDITOR")
        private String auditorRole;

        /**
         * 审核角色名称
         */
        @Schema(description = "审核角色名称", example = "主审核人")
        private String auditorRoleName;

        /**
         * 审核状态
         */
        @Schema(description = "审核状态", example = "PENDING")
        private String auditStatus;

        /**
         * 审核状态名称
         */
        @Schema(description = "审核状态名称", example = "待审核")
        private String auditStatusName;

        /**
         * 审核结果
         */
        @Schema(description = "审核结果", example = "APPROVED")
        private String auditResult;

        /**
         * 审核结果名称
         */
        @Schema(description = "审核结果名称", example = "通过")
        private String auditResultName;

        /**
         * 审核意见
         */
        @Schema(description = "审核意见", example = "费用合理，同意报销")
        private String auditComment;

        /**
         * 审核时间
         */
        @Schema(description = "审核时间", example = "2025-01-02 14:30:00")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime auditedAt;

        /**
         * 是否必须审核
         */
        @Schema(description = "是否必须审核", example = "true")
        private Boolean required;

        /**
         * 审核权重
         */
        @Schema(description = "审核权重", example = "1.0")
        private BigDecimal weight;

        /**
         * 部门名称
         */
        @Schema(description = "部门名称", example = "财务部")
        private String departmentName;

        /**
         * 职位名称
         */
        @Schema(description = "职位名称", example = "财务经理")
        private String positionName;
    }

    /**
     * 交易信息
     */
    @Data
    @Schema(description = "交易信息")
    public static class TransactionInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 交易ID
         */
        @Schema(description = "交易ID", example = "1")
        private Long transactionId;

        /**
         * 交易编号
         */
        @Schema(description = "交易编号", example = "TXN_20250101_001")
        private String transactionNumber;

        /**
         * 交易类型
         */
        @Schema(description = "交易类型", example = "EXPENSE")
        private String transactionType;

        /**
         * 交易类型名称
         */
        @Schema(description = "交易类型名称", example = "支出")
        private String transactionTypeName;

        /**
         * 交易金额
         */
        @Schema(description = "交易金额", example = "100.00")
        private BigDecimal amount;

        /**
         * 币种
         */
        @Schema(description = "币种", example = "CNY")
        private String currency;

        /**
         * 交易描述
         */
        @Schema(description = "交易描述", example = "午餐费用")
        private String description;

        /**
         * 分类名称
         */
        @Schema(description = "分类名称", example = "餐饮")
        private String categoryName;

        /**
         * 标签列表
         */
        @Schema(description = "标签列表", example = "[\"工作餐\", \"团队聚餐\"]")
        private List<String> tags;

        /**
         * 交易时间
         */
        @Schema(description = "交易时间", example = "2025-01-01 12:00:00")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime transactionTime;

        /**
         * 审核状态
         */
        @Schema(description = "审核状态", example = "PENDING")
        private String auditStatus;

        /**
         * 审核状态名称
         */
        @Schema(description = "审核状态名称", example = "待审核")
        private String auditStatusName;

        /**
         * 风险等级
         */
        @Schema(description = "风险等级", example = "LOW")
        private String riskLevel;

        /**
         * 风险等级名称
         */
        @Schema(description = "风险等级名称", example = "低风险")
        private String riskLevelName;

        /**
         * 异常标记
         */
        @Schema(description = "异常标记")
        private List<String> anomalyFlags;
    }

    /**
     * 审核进度
     */
    @Data
    @Schema(description = "审核进度")
    public static class AuditProgress implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 总审核人数
         */
        @Schema(description = "总审核人数", example = "3")
        private Integer totalAuditors;

        /**
         * 已审核人数
         */
        @Schema(description = "已审核人数", example = "2")
        private Integer completedAuditors;

        /**
         * 待审核人数
         */
        @Schema(description = "待审核人数", example = "1")
        private Integer pendingAuditors;

        /**
         * 进度百分比
         */
        @Schema(description = "进度百分比", example = "66.67")
        private BigDecimal progressPercentage;

        /**
         * 当前步骤
         */
        @Schema(description = "当前步骤", example = "DEPARTMENT_REVIEW")
        private String currentStep;

        /**
         * 当前步骤名称
         */
        @Schema(description = "当前步骤名称", example = "部门审核")
        private String currentStepName;

        /**
         * 下一步骤
         */
        @Schema(description = "下一步骤", example = "FINANCE_APPROVAL")
        private String nextStep;

        /**
         * 下一步骤名称
         */
        @Schema(description = "下一步骤名称", example = "财务审批")
        private String nextStepName;

        /**
         * 预计完成时间
         */
        @Schema(description = "预计完成时间", example = "2025-01-05 18:00:00")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime estimatedCompletionTime;

        /**
         * 是否可以加速
         */
        @Schema(description = "是否可以加速", example = "true")
        private Boolean canAccelerate;

        /**
         * 阻塞原因
         */
        @Schema(description = "阻塞原因")
        private List<String> blockingReasons;
    }

    /**
     * 审核历史
     */
    @Data
    @Schema(description = "审核历史")
    public static class AuditHistory implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 操作ID
         */
        @Schema(description = "操作ID", example = "1")
        private Long operationId;

        /**
         * 操作类型
         */
        @Schema(description = "操作类型", example = "AUDIT")
        private String operationType;

        /**
         * 操作类型名称
         */
        @Schema(description = "操作类型名称", example = "审核")
        private String operationTypeName;

        /**
         * 操作人ID
         */
        @Schema(description = "操作人ID", example = "2")
        private Long operatorId;

        /**
         * 操作人姓名
         */
        @Schema(description = "操作人姓名", example = "李四")
        private String operatorName;

        /**
         * 操作结果
         */
        @Schema(description = "操作结果", example = "APPROVED")
        private String operationResult;

        /**
         * 操作结果名称
         */
        @Schema(description = "操作结果名称", example = "通过")
        private String operationResultName;

        /**
         * 操作意见
         */
        @Schema(description = "操作意见", example = "费用合理，同意报销")
        private String operationComment;

        /**
         * 操作时间
         */
        @Schema(description = "操作时间", example = "2025-01-02 14:30:00")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime operationTime;

        /**
         * 耗时（分钟）
         */
        @Schema(description = "耗时（分钟）", example = "30")
        private Long durationMinutes;

        /**
         * 操作详情
         */
        @Schema(description = "操作详情")
        private Map<String, Object> operationDetails;
    }

    /**
     * 审核规则信息
     */
    @Data
    @Schema(description = "审核规则信息")
    public static class AuditRulesInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 是否需要全部通过
         */
        @Schema(description = "是否需要全部通过", example = "false")
        private Boolean requireAllApproval;

        /**
         * 是否允许部分通过
         */
        @Schema(description = "是否允许部分通过", example = "true")
        private Boolean allowPartialApproval;

        /**
         * 是否允许修改
         */
        @Schema(description = "是否允许修改", example = "true")
        private Boolean allowModification;

        /**
         * 审核流程
         */
        @Schema(description = "审核流程", example = "PARALLEL")
        private String auditFlow;

        /**
         * 审核流程名称
         */
        @Schema(description = "审核流程名称", example = "并行审核")
        private String auditFlowName;

        /**
         * 超时处理
         */
        @Schema(description = "超时处理", example = "NOTIFY_ONLY")
        private String timeoutAction;

        /**
         * 超时处理名称
         */
        @Schema(description = "超时处理名称", example = "仅通知")
        private String timeoutActionName;

        /**
         * 自动审核规则
         */
        @Schema(description = "自动审核规则")
        private Map<String, Object> autoAuditRules;

        /**
         * 升级规则
         */
        @Schema(description = "升级规则")
        private List<Map<String, Object>> escalationRules;
    }

    /**
     * 审核统计
     */
    @Data
    @Schema(description = "审核统计")
    public static class AuditStatistics implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 总交易数
         */
        @Schema(description = "总交易数", example = "10")
        private Long totalTransactions;

        /**
         * 总金额
         */
        @Schema(description = "总金额", example = "1500.00")
        private BigDecimal totalAmount;

        /**
         * 已审核交易数
         */
        @Schema(description = "已审核交易数", example = "7")
        private Long auditedTransactions;

        /**
         * 已审核金额
         */
        @Schema(description = "已审核金额", example = "1050.00")
        private BigDecimal auditedAmount;

        /**
         * 通过交易数
         */
        @Schema(description = "通过交易数", example = "6")
        private Long approvedTransactions;

        /**
         * 通过金额
         */
        @Schema(description = "通过金额", example = "900.00")
        private BigDecimal approvedAmount;

        /**
         * 拒绝交易数
         */
        @Schema(description = "拒绝交易数", example = "1")
        private Long rejectedTransactions;

        /**
         * 拒绝金额
         */
        @Schema(description = "拒绝金额", example = "150.00")
        private BigDecimal rejectedAmount;

        /**
         * 平均审核时间（分钟）
         */
        @Schema(description = "平均审核时间（分钟）", example = "45")
        private Long avgAuditTimeMinutes;

        /**
         * 风险分布
         */
        @Schema(description = "风险分布")
        private Map<String, Long> riskDistribution;

        /**
         * 分类分布
         */
        @Schema(description = "分类分布")
        private Map<String, BigDecimal> categoryDistribution;
    }
}
