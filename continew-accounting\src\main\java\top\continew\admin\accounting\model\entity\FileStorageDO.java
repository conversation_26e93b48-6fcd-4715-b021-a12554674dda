package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import top.continew.admin.accounting.enums.FileAccessTypeEnum;
import top.continew.admin.accounting.enums.FileProcessStatusEnum;
import top.continew.admin.accounting.enums.FileStorageTypeEnum;
import top.continew.admin.common.base.model.entity.BaseDO;
import top.continew.admin.system.enums.FileTypeEnum;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 文件存储实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@TableName("acc_file_storage")
public class FileStorageDO extends BaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 原始文件名
     */
    private String originalFileName;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 文件URL
     */
    private String fileUrl;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件类型
     */
    private FileTypeEnum fileType;

    /**
     * 文件扩展名
     */
    private String fileExtension;

    /**
     * MIME类型
     */
    private String mimeType;

    /**
     * 文件MD5值
     */
    private String fileMd5;

    /**
     * 文件SHA256值
     */
    private String fileSha256;

    /**
     * 存储类型
     */
    private FileStorageTypeEnum storageType;

    /**
     * 存储桶名称
     */
    private String bucketName;

    /**
     * 存储区域
     */
    private String storageRegion;

    /**
     * 访问权限类型
     */
    private FileAccessTypeEnum accessType;

    /**
     * CDN URL
     */
    private String cdnUrl;

    /**
     * 缩略图URL
     */
    private String thumbnailUrl;

    /**
     * 预览URL
     */
    private String previewUrl;

    /**
     * 下载次数
     */
    private Long downloadCount;

    /**
     * 访问次数
     */
    private Long accessCount;

    /**
     * 最后访问时间
     */
    private LocalDateTime lastAccessTime;

    /**
     * 处理状态
     */
    private FileProcessStatusEnum processStatus;

    /**
     * 处理结果
     */
    private String processResult;

    /**
     * 文件标签
     */
    private String fileTags;

    /**
     * 文件描述
     */
    private String fileDescription;

    /**
     * 是否为临时文件
     */
    private Boolean isTemporary;

    /**
     * 过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 关联业务ID
     */
    private String businessId;

    /**
     * 关联业务类型
     */
    private String businessType;

    /**
     * 群组ID
     */
    private Long groupId;

    /**
     * 上传用户ID
     */
    private Long uploadUserId;

    /**
     * 文件元数据（JSON格式）
     */
    private String metadata;

    /**
     * 安全扫描结果
     */
    private String securityScanResult;

    /**
     * 是否已删除
     */
    private Boolean isDeleted;

    /**
     * 删除时间
     */
    private LocalDateTime deleteTime;

}
