package top.continew.admin.accounting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.accounting.model.entity.RuleEngine;
import top.continew.admin.accounting.model.query.RuleEngineQuery;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 规则引擎Mapper接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface RuleEngineMapper extends BaseMapper<RuleEngine> {

    // ==================== 基础查询 ====================

    /**
     * 查询规则详情
     *
     * @param ruleId  规则ID
     * @param groupId 群组ID
     * @return 规则详情
     */
    RuleEngine selectRuleDetail(@Param("ruleId") Long ruleId, @Param("groupId") Long groupId);

    /**
     * 分页查询规则
     *
     * @param page  分页对象
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<RuleEngine> selectRulePage(Page<RuleEngine> page, @Param("query") RuleEngineQuery query);

    /**
     * 列表查询规则
     *
     * @param query 查询条件
     * @return 规则列表
     */
    List<RuleEngine> selectRuleList(@Param("query") RuleEngineQuery query);

    /**
     * 查询启用的规则
     *
     * @param groupId    群组ID
     * @param ruleType   规则类型
     * @param eventType  事件类型
     * @return 规则列表
     */
    List<RuleEngine> selectEnabledRules(@Param("groupId") Long groupId, 
                                       @Param("ruleType") String ruleType, 
                                       @Param("eventType") String eventType);

    /**
     * 查询需要调度的规则
     *
     * @param currentTime 当前时间
     * @return 规则列表
     */
    List<RuleEngine> selectSchedulableRules(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 查询规则执行统计
     *
     * @param ruleId    规则ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 执行统计
     */
    Map<String, Object> selectRuleExecutionStats(@Param("ruleId") Long ruleId, 
                                                 @Param("startTime") LocalDateTime startTime, 
                                                 @Param("endTime") LocalDateTime endTime);

    /**
     * 查询规则性能统计
     *
     * @param ruleId    规则ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 性能统计
     */
    Map<String, Object> selectRulePerformanceStats(@Param("ruleId") Long ruleId, 
                                                   @Param("startTime") LocalDateTime startTime, 
                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 查询规则执行趋势
     *
     * @param ruleId    规则ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 执行趋势
     */
    List<Map<String, Object>> selectRuleExecutionTrend(@Param("ruleId") Long ruleId, 
                                                       @Param("startTime") LocalDateTime startTime, 
                                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 查询热门规则
     *
     * @param groupId   群组ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param limit     返回数量
     * @return 热门规则列表
     */
    List<Map<String, Object>> selectPopularRules(@Param("groupId") Long groupId, 
                                                 @Param("startTime") LocalDateTime startTime, 
                                                 @Param("endTime") LocalDateTime endTime, 
                                                 @Param("limit") Integer limit);

    /**
     * 查询规则类型统计
     *
     * @param groupId 群组ID
     * @return 类型统计
     */
    List<Map<String, Object>> selectRuleTypeStats(@Param("groupId") Long groupId);

    /**
     * 查询群组规则统计
     *
     * @param groupId 群组ID
     * @return 群组统计
     */
    Map<String, Object> selectGroupRuleStats(@Param("groupId") Long groupId);

    /**
     * 查询系统规则统计
     *
     * @return 系统统计
     */
    Map<String, Object> selectSystemRuleStats();

    // ==================== 规则版本管理 ====================

    /**
     * 插入规则版本
     *
     * @param ruleId      规则ID
     * @param version     版本号
     * @param versionNote 版本说明
     * @param configData  配置数据
     * @param createdBy   创建人
     */
    void insertRuleVersion(@Param("ruleId") Long ruleId, 
                          @Param("version") String version, 
                          @Param("versionNote") String versionNote, 
                          @Param("configData") String configData, 
                          @Param("createdBy") Long createdBy);

    /**
     * 查询规则版本历史
     *
     * @param ruleId 规则ID
     * @return 版本历史
     */
    List<Map<String, Object>> selectRuleVersionHistory(@Param("ruleId") Long ruleId);

    /**
     * 查询规则版本配置
     *
     * @param ruleId  规则ID
     * @param version 版本号
     * @return 版本配置
     */
    Map<String, Object> selectRuleVersionConfig(@Param("ruleId") Long ruleId, @Param("version") String version);

    /**
     * 删除规则版本
     *
     * @param ruleId 规则ID
     */
    void deleteRuleVersions(@Param("ruleId") Long ruleId);

    // ==================== 规则执行记录 ====================

    /**
     * 插入规则执行记录
     *
     * @param executionRecord 执行记录
     */
    void insertRuleExecutionRecord(@Param("record") Map<String, Object> executionRecord);

    /**
     * 更新规则执行记录
     *
     * @param executionId     执行ID
     * @param executionStatus 执行状态
     * @param executionResult 执行结果
     * @param endTime         结束时间
     * @param executionTime   执行时间
     */
    void updateRuleExecutionRecord(@Param("executionId") Long executionId, 
                                  @Param("executionStatus") String executionStatus, 
                                  @Param("executionResult") String executionResult, 
                                  @Param("endTime") LocalDateTime endTime, 
                                  @Param("executionTime") Long executionTime);

    /**
     * 查询规则执行记录
     *
     * @param executionId 执行ID
     * @return 执行记录
     */
    Map<String, Object> selectRuleExecutionRecord(@Param("executionId") Long executionId);

    /**
     * 查询规则执行历史
     *
     * @param ruleId 规则ID
     * @param limit  返回数量
     * @return 执行历史
     */
    List<Map<String, Object>> selectRuleExecutionHistory(@Param("ruleId") Long ruleId, @Param("limit") Integer limit);

    /**
     * 删除规则执行记录
     *
     * @param ruleId 规则ID
     */
    void deleteRuleExecutionRecords(@Param("ruleId") Long ruleId);

    /**
     * 清理过期执行记录
     *
     * @param beforeTime 清理时间点
     * @return 清理数量
     */
    int cleanupExpiredExecutionRecords(@Param("beforeTime") LocalDateTime beforeTime);

    // ==================== 规则统计更新 ====================

    /**
     * 更新规则执行统计
     *
     * @param ruleId          规则ID
     * @param executionCount  执行次数增量
     * @param successCount    成功次数增量
     * @param failureCount    失败次数增量
     * @param executionTime   执行时间
     * @param lastExecutionTime 最后执行时间
     * @param lastExecutionStatus 最后执行状态
     * @param lastExecutionResult 最后执行结果
     */
    void updateRuleExecutionStats(@Param("ruleId") Long ruleId, 
                                 @Param("executionCount") Integer executionCount, 
                                 @Param("successCount") Integer successCount, 
                                 @Param("failureCount") Integer failureCount, 
                                 @Param("executionTime") Long executionTime, 
                                 @Param("lastExecutionTime") LocalDateTime lastExecutionTime, 
                                 @Param("lastExecutionStatus") String lastExecutionStatus, 
                                 @Param("lastExecutionResult") String lastExecutionResult);

    /**
     * 更新规则性能指标
     *
     * @param ruleId           规则ID
     * @param avgExecutionTime 平均执行时间
     * @param maxExecutionTime 最大执行时间
     * @param minExecutionTime 最小执行时间
     * @param successRate      成功率
     * @param errorRate        错误率
     * @param actualMatchRate  实际匹配率
     */
    void updateRulePerformanceMetrics(@Param("ruleId") Long ruleId, 
                                     @Param("avgExecutionTime") Long avgExecutionTime, 
                                     @Param("maxExecutionTime") Long maxExecutionTime, 
                                     @Param("minExecutionTime") Long minExecutionTime, 
                                     @Param("successRate") Double successRate, 
                                     @Param("errorRate") Double errorRate, 
                                     @Param("actualMatchRate") Double actualMatchRate);

    /**
     * 更新规则调度信息
     *
     * @param ruleId            规则ID
     * @param scheduleStatus    调度状态
     * @param nextExecutionTime 下次执行时间
     */
    void updateRuleScheduleInfo(@Param("ruleId") Long ruleId, 
                               @Param("scheduleStatus") String scheduleStatus, 
                               @Param("nextExecutionTime") LocalDateTime nextExecutionTime);

    /**
     * 更新规则通知信息
     *
     * @param ruleId               规则ID
     * @param notificationCount    通知次数增量
     * @param lastNotificationTime 最后通知时间
     */
    void updateRuleNotificationInfo(@Param("ruleId") Long ruleId, 
                                   @Param("notificationCount") Integer notificationCount, 
                                   @Param("lastNotificationTime") LocalDateTime lastNotificationTime);

    /**
     * 更新规则错误信息
     *
     * @param ruleId           规则ID
     * @param lastErrorMessage 最后错误信息
     * @param lastErrorTime    最后错误时间
     */
    void updateRuleErrorInfo(@Param("ruleId") Long ruleId, 
                            @Param("lastErrorMessage") String lastErrorMessage, 
                            @Param("lastErrorTime") LocalDateTime lastErrorTime);

    // ==================== 规则依赖和冲突 ====================

    /**
     * 查询规则依赖关系
     *
     * @param ruleId 规则ID
     * @return 依赖关系
     */
    List<Map<String, Object>> selectRuleDependencies(@Param("ruleId") Long ruleId);

    /**
     * 查询规则冲突
     *
     * @param ruleId  规则ID
     * @param groupId 群组ID
     * @return 冲突列表
     */
    List<Map<String, Object>> selectRuleConflicts(@Param("ruleId") Long ruleId, @Param("groupId") Long groupId);

    /**
     * 查询规则影响范围
     *
     * @param ruleId 规则ID
     * @return 影响范围
     */
    Map<String, Object> selectRuleImpactScope(@Param("ruleId") Long ruleId);

    // ==================== 规则模板 ====================

    /**
     * 查询规则模板
     *
     * @param ruleType 规则类型
     * @return 模板列表
     */
    List<Map<String, Object>> selectRuleTemplates(@Param("ruleType") String ruleType);

    /**
     * 插入规则模板
     *
     * @param templateData 模板数据
     */
    void insertRuleTemplate(@Param("template") Map<String, Object> templateData);

    /**
     * 查询规则模板配置
     *
     * @param templateId 模板ID
     * @return 模板配置
     */
    Map<String, Object> selectRuleTemplateConfig(@Param("templateId") Long templateId);

    // ==================== 批量操作 ====================

    /**
     * 批量更新规则状态
     *
     * @param ruleIds   规则ID列表
     * @param enabled   是否启用
     * @param updatedBy 更新人
     */
    void batchUpdateRuleStatus(@Param("ruleIds") List<Long> ruleIds, 
                              @Param("enabled") Boolean enabled, 
                              @Param("updatedBy") Long updatedBy);

    /**
     * 批量删除规则
     *
     * @param ruleIds   规则ID列表
     * @param deletedBy 删除人
     */
    void batchDeleteRules(@Param("ruleIds") List<Long> ruleIds, @Param("deletedBy") Long deletedBy);

    /**
     * 批量更新规则优先级
     *
     * @param rulePriorities 规则优先级映射
     * @param updatedBy      更新人
     */
    void batchUpdateRulePriority(@Param("rulePriorities") Map<Long, Integer> rulePriorities, 
                                @Param("updatedBy") Long updatedBy);

    // ==================== 数据清理 ====================

    /**
     * 清理已删除规则的相关数据
     *
     * @param beforeTime 清理时间点
     * @return 清理数量
     */
    int cleanupDeletedRuleData(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 清理过期版本数据
     *
     * @param beforeTime 清理时间点
     * @param keepCount  保留版本数
     * @return 清理数量
     */
    int cleanupExpiredVersionData(@Param("beforeTime") LocalDateTime beforeTime, @Param("keepCount") Integer keepCount);

    /**
     * 归档历史执行数据
     *
     * @param beforeTime 归档时间点
     * @return 归档数量
     */
    int archiveHistoricalExecutionData(@Param("beforeTime") LocalDateTime beforeTime);
}
