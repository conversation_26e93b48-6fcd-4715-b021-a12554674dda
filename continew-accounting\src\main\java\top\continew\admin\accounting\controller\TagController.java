package top.continew.admin.accounting.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.accounting.mapper.TagMapper;
import top.continew.admin.accounting.model.entity.TagDO;
import top.continew.admin.accounting.model.query.TagQuery;
import top.continew.admin.accounting.model.req.TagCreateReq;
import top.continew.admin.accounting.model.req.TagUpdateReq;
import top.continew.admin.accounting.model.resp.TagDetailResp;
import top.continew.admin.accounting.model.resp.TagListResp;
import top.continew.admin.accounting.service.TagService;
import top.continew.admin.common.base.controller.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.log.annotation.Log;
import top.continew.starter.web.model.R;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 标签管理 API
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "标签管理 API")
@RestController
@RequiredArgsConstructor
@Validated
@CrudRequestMapping(value = "/accounting/tag", api = {CrudRequestMapping.Api.PAGE, CrudRequestMapping.Api.GET, CrudRequestMapping.Api.ADD, CrudRequestMapping.Api.DELETE})
public class TagController extends BaseController<TagService, TagListResp, TagDetailResp, TagQuery, TagCreateReq> {

    @Operation(summary = "修改标签", description = "修改标签")
    @Log(value = "修改标签")
    @PutMapping("/{id}")
    public R<Void> update(@Valid @RequestBody TagUpdateReq req,
                         @Parameter(description = "ID", example = "1") @PathVariable Long id) {
        baseService.update(req, id);
        return R.ok();
    }

    @Operation(summary = "获取群组标签列表", description = "获取指定群组的所有标签")
    @GetMapping("/group/{groupId}")
    public R<List<TagListResp>> getGroupTags(@Parameter(description = "群组ID", example = "1") @PathVariable Long groupId) {
        return R.ok(baseService.getGroupTags(groupId));
    }

    @Operation(summary = "获取默认标签", description = "获取群组的默认标签")
    @GetMapping("/group/{groupId}/default")
    public R<List<TagDO>> getDefaultTags(@Parameter(description = "群组ID", example = "1") @PathVariable Long groupId) {
        return R.ok(baseService.getDefaultTags(groupId));
    }

    @Operation(summary = "创建默认标签", description = "为群组创建默认标签")
    @Log(value = "创建默认标签")
    @PostMapping("/group/{groupId}/default")
    public R<Void> createDefaultTags(@Parameter(description = "群组ID", example = "1") @PathVariable Long groupId) {
        baseService.createDefaultTags(groupId);
        return R.ok();
    }

    @Operation(summary = "批量删除标签", description = "批量删除标签")
    @Log(value = "批量删除标签")
    @DeleteMapping("/batch")
    public R<Void> batchDelete(@Parameter(description = "标签ID列表") @RequestBody @NotEmpty List<Long> tagIds) {
        baseService.batchDelete(tagIds);
        return R.ok();
    }

    @Operation(summary = "复制标签到其他群组", description = "将标签复制到其他群组")
    @Log(value = "复制标签")
    @PostMapping("/{tagId}/copy")
    public R<Long> copyToGroup(@Parameter(description = "标签ID", example = "1") @PathVariable Long tagId,
                              @Parameter(description = "目标群组ID", example = "2") @RequestParam Long targetGroupId) {
        Long newTagId = baseService.copyToGroup(tagId, targetGroupId, getCurrentUserId());
        return R.ok(newTagId);
    }

    @Operation(summary = "批量导入标签", description = "批量导入标签")
    @Log(value = "批量导入标签")
    @PostMapping("/group/{groupId}/import")
    public R<TagService.ImportResult> batchImport(@Parameter(description = "群组ID", example = "1") @PathVariable Long groupId,
                                                 @RequestBody @Valid List<TagCreateReq> tags) {
        TagService.ImportResult result = baseService.batchImport(groupId, tags, getCurrentUserId());
        return R.ok(result);
    }

    @Operation(summary = "获取热门标签", description = "获取使用频率最高的标签")
    @GetMapping("/group/{groupId}/hot")
    public R<List<TagDO>> getHotTags(@Parameter(description = "群组ID", example = "1") @PathVariable Long groupId,
                                    @Parameter(description = "限制数量", example = "10") @RequestParam(defaultValue = "10") int limit) {
        return R.ok(baseService.getHotTags(groupId, limit));
    }

    @Operation(summary = "获取未使用的标签", description = "获取未被任何交易使用的标签")
    @GetMapping("/group/{groupId}/unused")
    public R<List<TagDO>> getUnusedTags(@Parameter(description = "群组ID", example = "1") @PathVariable Long groupId) {
        return R.ok(baseService.getUnusedTags(groupId));
    }

    @Operation(summary = "获取标签使用统计", description = "获取标签的使用统计信息")
    @GetMapping("/group/{groupId}/statistics")
    public R<List<TagMapper.TagStatistics>> getTagStatistics(@Parameter(description = "群组ID", example = "1") @PathVariable Long groupId) {
        return R.ok(baseService.getTagStatistics(groupId));
    }

    @Operation(summary = "合并标签", description = "将多个标签合并为一个标签")
    @Log(value = "合并标签")
    @PostMapping("/merge")
    public R<Void> mergeTags(@Parameter(description = "源标签ID列表") @RequestParam @NotEmpty List<Long> sourceTagIds,
                            @Parameter(description = "目标标签ID", example = "1") @RequestParam @NotNull Long targetTagId) {
        baseService.mergeTags(sourceTagIds, targetTagId, getCurrentUserId());
        return R.ok();
    }

    @Operation(summary = "自动创建标签", description = "根据标签名称列表自动创建不存在的标签")
    @Log(value = "自动创建标签")
    @PostMapping("/group/{groupId}/auto-create")
    public R<List<TagDO>> autoCreateTags(@Parameter(description = "群组ID", example = "1") @PathVariable Long groupId,
                                        @Parameter(description = "标签名称列表") @RequestBody @NotEmpty List<String> tagNames) {
        List<TagDO> tags = baseService.autoCreateTags(groupId, tagNames, getCurrentUserId());
        return R.ok(tags);
    }

    @Operation(summary = "清理未使用的标签", description = "删除所有未被使用的非系统标签")
    @Log(value = "清理未使用标签")
    @DeleteMapping("/group/{groupId}/clean")
    public R<Integer> cleanUnusedTags(@Parameter(description = "群组ID", example = "1") @PathVariable Long groupId) {
        int cleanedCount = baseService.cleanUnusedTags(groupId, getCurrentUserId());
        return R.ok(cleanedCount);
    }

    @Operation(summary = "检查标签名称", description = "检查标签名称是否已存在")
    @GetMapping("/check-name")
    public R<Boolean> checkName(@Parameter(description = "标签名称", example = "工作") @RequestParam String name,
                               @Parameter(description = "群组ID", example = "1") @RequestParam Long groupId,
                               @Parameter(description = "排除的标签ID", example = "1") @RequestParam(required = false) Long excludeId) {
        boolean exists = baseService.isNameExists(name, groupId, excludeId);
        return R.ok(exists);
    }

    @Operation(summary = "检查是否可删除", description = "检查标签是否可以删除")
    @GetMapping("/{id}/can-delete")
    public R<Boolean> canDelete(@Parameter(description = "标签ID", example = "1") @PathVariable Long id) {
        boolean canDelete = baseService.canDelete(id);
        return R.ok(canDelete);
    }

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        return top.continew.starter.security.context.SecurityContextHolder.getUserId();
    }
}
