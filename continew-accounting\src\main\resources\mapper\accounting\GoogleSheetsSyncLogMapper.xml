<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.accounting.mapper.GoogleSheetsSyncLogMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="top.continew.admin.accounting.model.entity.GoogleSheetsSyncLog">
        <id column="id" property="id" />
        <result column="sync_id" property="syncId" />
        <result column="config_id" property="configId" />
        <result column="group_id" property="groupId" />
        <result column="sync_type" property="syncType" />
        <result column="sync_direction" property="syncDirection" />
        <result column="trigger_type" property="triggerType" />
        <result column="trigger_user_id" property="triggerUserId" />
        <result column="sync_status" property="syncStatus" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="sync_duration" property="syncDuration" />
        <result column="processed_records" property="processedRecords" />
        <result column="success_records" property="successRecords" />
        <result column="failed_records" property="failedRecords" />
        <result column="skipped_records" property="skippedRecords" />
        <result column="inserted_records" property="insertedRecords" />
        <result column="updated_records" property="updatedRecords" />
        <result column="deleted_records" property="deletedRecords" />
        <result column="error_count" property="errorCount" />
        <result column="warning_count" property="warningCount" />
        <result column="error_message" property="errorMessage" />
        <result column="error_details_json" property="errorDetailsJson" />
        <result column="warning_details_json" property="warningDetailsJson" />
        <result column="sync_summary_json" property="syncSummaryJson" />
        <result column="performance_metrics_json" property="performanceMetricsJson" />
        <result column="backup_id" property="backupId" />
        <result column="backup_created" property="backupCreated" />
        <result column="rollback_available" property="rollbackAvailable" />
        <result column="data_hash_before" property="dataHashBefore" />
        <result column="data_hash_after" property="dataHashAfter" />
        <result column="sync_scope_json" property="syncScopeJson" />
        <result column="retry_count" property="retryCount" />
        <result column="max_retries" property="maxRetries" />
        <result column="next_retry_time" property="nextRetryTime" />
        <result column="callback_url" property="callbackUrl" />
        <result column="callback_status" property="callbackStatus" />
        <result column="callback_response" property="callbackResponse" />
        <result column="progress_percentage" property="progressPercentage" />
        <result column="current_step" property="currentStep" />
        <result column="total_steps" property="totalSteps" />
        <result column="step_details_json" property="stepDetailsJson" />
        <result column="memory_usage_mb" property="memoryUsageMb" />
        <result column="cpu_usage_percent" property="cpuUsagePercent" />
        <result column="network_bytes_sent" property="networkBytesSent" />
        <result column="network_bytes_received" property="networkBytesReceived" />
        <result column="api_calls_count" property="apiCallsCount" />
        <result column="api_quota_used" property="apiQuotaUsed" />
        <result column="custom_metadata_json" property="customMetadataJson" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 基础查询 -->
    <select id="selectBySyncId" resultMap="BaseResultMap">
        SELECT * FROM acc_google_sheets_sync_log
        WHERE sync_id = #{syncId}
        LIMIT 1
    </select>

    <select id="selectByConfigId" resultMap="BaseResultMap">
        SELECT * FROM acc_google_sheets_sync_log
        WHERE config_id = #{configId}
        ORDER BY start_time DESC
        LIMIT #{limit}
    </select>

    <select id="selectByGroupId" resultMap="BaseResultMap">
        SELECT * FROM acc_google_sheets_sync_log
        WHERE group_id = #{groupId}
        ORDER BY start_time DESC
        LIMIT #{limit}
    </select>

    <select id="selectLatestByConfigId" resultMap="BaseResultMap">
        SELECT * FROM acc_google_sheets_sync_log
        WHERE config_id = #{configId}
        ORDER BY start_time DESC
        LIMIT 1
    </select>

    <select id="selectRunningSyncTasks" resultMap="BaseResultMap">
        SELECT * FROM acc_google_sheets_sync_log
        WHERE config_id = #{configId}
        AND sync_status IN ('RUNNING', 'PENDING')
        ORDER BY start_time ASC
    </select>

    <!-- 状态查询 -->
    <select id="selectBySyncStatus" resultMap="BaseResultMap">
        SELECT * FROM acc_google_sheets_sync_log
        WHERE sync_status = #{syncStatus}
        AND group_id = #{groupId}
        ORDER BY start_time DESC
        LIMIT #{limit}
    </select>

    <select id="selectFailedSyncLogs" resultMap="BaseResultMap">
        SELECT * FROM acc_google_sheets_sync_log
        WHERE config_id = #{configId}
        AND sync_status = 'FAILED'
        AND start_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY start_time DESC
    </select>

    <select id="selectTimeoutSyncTasks" resultMap="BaseResultMap">
        SELECT * FROM acc_google_sheets_sync_log
        WHERE sync_status = 'RUNNING'
        AND start_time &lt; DATE_SUB(NOW(), INTERVAL #{timeoutMinutes} MINUTE)
        ORDER BY start_time ASC
    </select>

    <!-- 统计查询 -->
    <select id="countSyncLogsByGroupId" resultType="map">
        SELECT 
            COUNT(*) as total_count,
            COUNT(CASE WHEN sync_status = 'SUCCESS' THEN 1 END) as success_count,
            COUNT(CASE WHEN sync_status = 'FAILED' THEN 1 END) as failed_count,
            COUNT(CASE WHEN sync_status = 'RUNNING' THEN 1 END) as running_count,
            ROUND(COUNT(CASE WHEN sync_status = 'SUCCESS' THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate
        FROM acc_google_sheets_sync_log
        WHERE group_id = #{groupId}
    </select>

    <select id="countSyncLogsByConfigId" resultType="map">
        SELECT 
            COUNT(*) as total_count,
            COUNT(CASE WHEN sync_status = 'SUCCESS' THEN 1 END) as success_count,
            COUNT(CASE WHEN sync_status = 'FAILED' THEN 1 END) as failed_count,
            ROUND(COUNT(CASE WHEN sync_status = 'SUCCESS' THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate,
            COALESCE(SUM(processed_records), 0) as total_processed,
            COALESCE(SUM(success_records), 0) as total_success,
            COALESCE(SUM(failed_records), 0) as total_failed
        FROM acc_google_sheets_sync_log
        WHERE config_id = #{configId}
    </select>

    <select id="countSyncLogsBySyncStatus" resultType="map">
        SELECT 
            sync_status as status,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM acc_google_sheets_sync_log WHERE group_id = #{groupId} AND start_time BETWEEN #{startTime} AND #{endTime}), 2) as percentage
        FROM acc_google_sheets_sync_log
        WHERE group_id = #{groupId}
        AND start_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY sync_status
        ORDER BY count DESC
    </select>

    <select id="countSyncLogsBySyncType" resultType="map">
        SELECT 
            sync_type as type,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM acc_google_sheets_sync_log WHERE group_id = #{groupId} AND start_time BETWEEN #{startTime} AND #{endTime}), 2) as percentage
        FROM acc_google_sheets_sync_log
        WHERE group_id = #{groupId}
        AND start_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY sync_type
        ORDER BY count DESC
    </select>

    <select id="countSyncLogsByTriggerType" resultType="map">
        SELECT 
            trigger_type as type,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM acc_google_sheets_sync_log WHERE group_id = #{groupId} AND start_time BETWEEN #{startTime} AND #{endTime}), 2) as percentage
        FROM acc_google_sheets_sync_log
        WHERE group_id = #{groupId}
        AND start_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY trigger_type
        ORDER BY count DESC
    </select>

    <select id="countSyncTrend" resultType="map">
        SELECT 
            <choose>
                <when test="interval == 'hour'">
                    DATE_FORMAT(start_time, '%Y-%m-%d %H:00:00') as time_period
                </when>
                <when test="interval == 'day'">
                    DATE(start_time) as time_period
                </when>
                <when test="interval == 'week'">
                    DATE_FORMAT(start_time, '%Y-%u') as time_period
                </when>
                <when test="interval == 'month'">
                    DATE_FORMAT(start_time, '%Y-%m') as time_period
                </when>
                <otherwise>
                    DATE(start_time) as time_period
                </otherwise>
            </choose>,
            COUNT(*) as total_count,
            COUNT(CASE WHEN sync_status = 'SUCCESS' THEN 1 END) as success_count,
            COUNT(CASE WHEN sync_status = 'FAILED' THEN 1 END) as failed_count,
            COALESCE(SUM(processed_records), 0) as total_records
        FROM acc_google_sheets_sync_log
        WHERE group_id = #{groupId}
        AND start_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY time_period
        ORDER BY time_period ASC
    </select>

    <!-- 性能统计 -->
    <select id="selectSyncPerformanceMetrics" resultType="map">
        SELECT 
            COUNT(*) as total_syncs,
            COUNT(CASE WHEN sync_status = 'SUCCESS' THEN 1 END) as success_syncs,
            COUNT(CASE WHEN sync_status = 'FAILED' THEN 1 END) as failed_syncs,
            ROUND(COUNT(CASE WHEN sync_status = 'SUCCESS' THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate,
            ROUND(AVG(CASE WHEN sync_status = 'SUCCESS' THEN sync_duration END), 2) as avg_duration,
            MAX(CASE WHEN sync_status = 'SUCCESS' THEN sync_duration END) as max_duration,
            MIN(CASE WHEN sync_status = 'SUCCESS' THEN sync_duration END) as min_duration,
            COALESCE(SUM(processed_records), 0) as total_processed,
            COALESCE(SUM(success_records), 0) as total_success,
            COALESCE(SUM(failed_records), 0) as total_failed,
            ROUND(AVG(CASE WHEN sync_status = 'SUCCESS' THEN processed_records END), 0) as avg_processed_per_sync
        FROM acc_google_sheets_sync_log
        WHERE config_id = #{configId}
        AND start_time BETWEEN #{startTime} AND #{endTime}
    </select>

    <select id="selectAvgSyncDuration" resultType="map">
        SELECT 
            config_id,
            COUNT(*) as sync_count,
            ROUND(AVG(sync_duration), 2) as avg_duration,
            MAX(sync_duration) as max_duration,
            MIN(sync_duration) as min_duration
        FROM acc_google_sheets_sync_log
        WHERE group_id = #{groupId}
        AND sync_status = 'SUCCESS'
        AND start_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY config_id
        HAVING sync_count >= 3
        ORDER BY avg_duration DESC
    </select>

    <select id="selectSyncSuccessRate" resultType="map">
        SELECT 
            config_id,
            COUNT(*) as total_syncs,
            COUNT(CASE WHEN sync_status = 'SUCCESS' THEN 1 END) as success_syncs,
            ROUND(COUNT(CASE WHEN sync_status = 'SUCCESS' THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate
        FROM acc_google_sheets_sync_log
        WHERE group_id = #{groupId}
        AND start_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY config_id
        HAVING total_syncs >= 3
        ORDER BY success_rate DESC
    </select>

    <select id="selectProcessedRecordsDistribution" resultType="map">
        SELECT 
            CASE 
                WHEN processed_records = 0 THEN '0'
                WHEN processed_records BETWEEN 1 AND 10 THEN '1-10'
                WHEN processed_records BETWEEN 11 AND 50 THEN '11-50'
                WHEN processed_records BETWEEN 51 AND 100 THEN '51-100'
                WHEN processed_records BETWEEN 101 AND 500 THEN '101-500'
                WHEN processed_records BETWEEN 501 AND 1000 THEN '501-1000'
                ELSE '1000+'
            END as record_range,
            COUNT(*) as count
        FROM acc_google_sheets_sync_log
        WHERE group_id = #{groupId}
        AND start_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY record_range
        ORDER BY 
            CASE record_range
                WHEN '0' THEN 1
                WHEN '1-10' THEN 2
                WHEN '11-50' THEN 3
                WHEN '51-100' THEN 4
                WHEN '101-500' THEN 5
                WHEN '501-1000' THEN 6
                WHEN '1000+' THEN 7
            END
    </select>

    <!-- 错误分析 -->
    <select id="countErrorsByType" resultType="map">
        SELECT 
            SUBSTRING_INDEX(error_message, ':', 1) as error_type,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM acc_google_sheets_sync_log WHERE group_id = #{groupId} AND sync_status = 'FAILED' AND start_time BETWEEN #{startTime} AND #{endTime}), 2) as percentage
        FROM acc_google_sheets_sync_log
        WHERE group_id = #{groupId}
        AND sync_status = 'FAILED'
        AND start_time BETWEEN #{startTime} AND #{endTime}
        AND error_message IS NOT NULL
        GROUP BY error_type
        ORDER BY count DESC
    </select>

    <select id="selectTopErrorsByFrequency" resultType="map">
        SELECT 
            error_message,
            COUNT(*) as frequency,
            COUNT(DISTINCT config_id) as affected_configs,
            MIN(start_time) as first_occurrence,
            MAX(start_time) as last_occurrence
        FROM acc_google_sheets_sync_log
        WHERE group_id = #{groupId}
        AND sync_status = 'FAILED'
        AND start_time BETWEEN #{startTime} AND #{endTime}
        AND error_message IS NOT NULL
        GROUP BY error_message
        ORDER BY frequency DESC
        LIMIT #{limit}
    </select>

    <select id="selectRepeatedErrors" resultMap="BaseResultMap">
        SELECT * FROM acc_google_sheets_sync_log
        WHERE config_id = #{configId}
        AND sync_status = 'FAILED'
        AND error_message LIKE CONCAT('%', #{errorCode}, '%')
        AND start_time >= DATE_SUB(NOW(), INTERVAL #{hours} HOUR)
        ORDER BY start_time DESC
    </select>

    <!-- 排行榜查询 -->
    <select id="selectTopConfigsBySyncCount" resultType="map">
        SELECT 
            config_id,
            COUNT(*) as sync_count,
            COUNT(CASE WHEN sync_status = 'SUCCESS' THEN 1 END) as success_count,
            ROUND(COUNT(CASE WHEN sync_status = 'SUCCESS' THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate
        FROM acc_google_sheets_sync_log
        WHERE group_id = #{groupId}
        AND start_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY config_id
        ORDER BY sync_count DESC
        LIMIT #{limit}
    </select>

    <select id="selectTopConfigsByProcessedRecords" resultType="map">
        SELECT 
            config_id,
            COUNT(*) as sync_count,
            COALESCE(SUM(processed_records), 0) as total_processed,
            COALESCE(SUM(success_records), 0) as total_success,
            ROUND(COALESCE(SUM(success_records), 0) * 100.0 / NULLIF(COALESCE(SUM(processed_records), 0), 0), 2) as record_success_rate
        FROM acc_google_sheets_sync_log
        WHERE group_id = #{groupId}
        AND start_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY config_id
        ORDER BY total_processed DESC
        LIMIT #{limit}
    </select>

    <select id="selectTopSyncTasksByDuration" resultType="map">
        SELECT 
            sync_id,
            config_id,
            sync_duration,
            processed_records,
            start_time,
            end_time,
            ROUND(processed_records / NULLIF(sync_duration, 0), 2) as records_per_second
        FROM acc_google_sheets_sync_log
        WHERE group_id = #{groupId}
        AND sync_status = 'SUCCESS'
        AND start_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY sync_duration DESC
        LIMIT #{limit}
    </select>

    <!-- 数据清理 -->
    <delete id="deleteExpiredLogs">
        DELETE FROM acc_google_sheets_sync_log
        WHERE create_time &lt; DATE_SUB(NOW(), INTERVAL #{retentionDays} DAY)
    </delete>

    <delete id="deleteLogsByConfigId">
        DELETE FROM acc_google_sheets_sync_log
        WHERE config_id = #{configId}
    </delete>

    <delete id="deleteLogsByGroupId">
        DELETE FROM acc_google_sheets_sync_log
        WHERE group_id = #{groupId}
    </delete>

    <delete id="cleanupFailedLogs">
        DELETE FROM acc_google_sheets_sync_log
        WHERE config_id = #{configId}
        AND sync_status = 'FAILED'
        AND create_time &lt; DATE_SUB(NOW(), INTERVAL #{retentionDays} DAY)
    </delete>

    <update id="compressHistoryLogs">
        UPDATE acc_google_sheets_sync_log
        SET 
            error_details_json = NULL,
            warning_details_json = NULL,
            sync_summary_json = NULL,
            performance_metrics_json = NULL,
            step_details_json = NULL,
            custom_metadata_json = NULL
        WHERE create_time &lt; DATE_SUB(NOW(), INTERVAL #{compressDays} DAY)
        AND sync_status IN ('SUCCESS', 'FAILED')
    </update>

    <!-- 批量操作 -->
    <update id="batchUpdateSyncStatus">
        UPDATE acc_google_sheets_sync_log
        SET sync_status = #{syncStatus}, update_time = NOW()
        WHERE sync_id IN
        <foreach collection="syncIds" item="syncId" open="(" separator="," close=")">
            #{syncId}
        </foreach>
    </update>

    <update id="batchUpdateEndTime">
        UPDATE acc_google_sheets_sync_log
        SET end_time = #{endTime}, update_time = NOW()
        WHERE sync_id IN
        <foreach collection="syncIds" item="syncId" open="(" separator="," close=")">
            #{syncId}
        </foreach>
    </update>

    <update id="batchMarkAsProcessed">
        UPDATE acc_google_sheets_sync_log
        SET 
            sync_status = CASE WHEN sync_status = 'RUNNING' THEN 'SUCCESS' ELSE sync_status END,
            end_time = CASE WHEN end_time IS NULL THEN NOW() ELSE end_time END,
            update_time = NOW()
        WHERE id IN
        <foreach collection="logIds" item="logId" open="(" separator="," close=")">
            #{logId}
        </foreach>
    </update>

    <!-- 监控查询 -->
    <select id="selectSyncTasksForMonitoring" resultMap="BaseResultMap">
        SELECT * FROM acc_google_sheets_sync_log
        WHERE group_id = #{groupId}
        AND sync_status IN ('RUNNING', 'PENDING')
        ORDER BY start_time ASC
    </select>

    <select id="selectAbnormalSyncTasks" resultMap="BaseResultMap">
        SELECT * FROM acc_google_sheets_sync_log
        WHERE group_id = #{groupId}
        AND (
            (sync_status = 'RUNNING' AND start_time &lt; DATE_SUB(NOW(), INTERVAL 30 MINUTE))
            OR (sync_status = 'FAILED' AND retry_count &lt; max_retries)
            OR (sync_status = 'SUCCESS' AND sync_duration > 300)
        )
        ORDER BY start_time DESC
    </select>

    <select id="selectPerformanceAbnormalTasks" resultMap="BaseResultMap">
        SELECT * FROM acc_google_sheets_sync_log
        WHERE group_id = #{groupId}
        AND sync_status = 'SUCCESS'
        AND sync_duration > #{maxDurationSeconds}
        ORDER BY sync_duration DESC
    </select>

    <!-- 报表查询 -->
    <select id="generateSyncReport" resultType="map">
        SELECT 
            COUNT(*) as total_syncs,
            COUNT(CASE WHEN sync_status = 'SUCCESS' THEN 1 END) as success_syncs,
            COUNT(CASE WHEN sync_status = 'FAILED' THEN 1 END) as failed_syncs,
            ROUND(COUNT(CASE WHEN sync_status = 'SUCCESS' THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate,
            COALESCE(SUM(processed_records), 0) as total_processed,
            COALESCE(SUM(success_records), 0) as total_success,
            COALESCE(SUM(failed_records), 0) as total_failed,
            ROUND(AVG(CASE WHEN sync_status = 'SUCCESS' THEN sync_duration END), 2) as avg_duration,
            MAX(CASE WHEN sync_status = 'SUCCESS' THEN sync_duration END) as max_duration,
            MIN(CASE WHEN sync_status = 'SUCCESS' THEN sync_duration END) as min_duration
        FROM acc_google_sheets_sync_log
        WHERE config_id = #{configId}
        AND start_time BETWEEN #{startTime} AND #{endTime}
    </select>

    <select id="generatePerformanceReport" resultType="map">
        SELECT 
            'sync_performance' as metric_type,
            COUNT(*) as total_syncs,
            ROUND(AVG(sync_duration), 2) as avg_duration,
            ROUND(AVG(processed_records), 0) as avg_records,
            ROUND(AVG(api_calls_count), 0) as avg_api_calls,
            ROUND(AVG(memory_usage_mb), 2) as avg_memory_usage,
            ROUND(AVG(cpu_usage_percent), 2) as avg_cpu_usage
        FROM acc_google_sheets_sync_log
        WHERE group_id = #{groupId}
        AND sync_status = 'SUCCESS'
        AND start_time BETWEEN #{startTime} AND #{endTime}
    </select>

    <select id="generateErrorReport" resultType="map">
        SELECT 
            'error_analysis' as report_type,
            COUNT(*) as total_errors,
            COUNT(DISTINCT config_id) as affected_configs,
            COUNT(DISTINCT SUBSTRING_INDEX(error_message, ':', 1)) as unique_error_types,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM acc_google_sheets_sync_log WHERE group_id = #{groupId} AND start_time BETWEEN #{startTime} AND #{endTime}), 2) as error_rate
        FROM acc_google_sheets_sync_log
        WHERE group_id = #{groupId}
        AND sync_status = 'FAILED'
        AND start_time BETWEEN #{startTime} AND #{endTime}
    </select>

</mapper>
