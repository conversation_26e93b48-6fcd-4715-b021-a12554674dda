package top.continew.admin.accounting.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Google Sheets配置响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "Google Sheets配置响应")
public class GoogleSheetsConfigResp {

    /**
     * 配置ID
     */
    @Schema(description = "配置ID", example = "1")
    private Long configId;

    /**
     * 配置名称
     */
    @Schema(description = "配置名称", example = "我的记账表格")
    private String configName;

    /**
     * Google Sheets ID
     */
    @Schema(description = "Google Sheets ID", example = "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms")
    private String spreadsheetId;

    /**
     * 工作表名称
     */
    @Schema(description = "工作表名称", example = "账单记录")
    private String sheetName;

    /**
     * 表格标题
     */
    @Schema(description = "表格标题", example = "家庭记账表")
    private String spreadsheetTitle;

    /**
     * 表格URL
     */
    @Schema(description = "表格URL", example = "https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit")
    private String spreadsheetUrl;

    /**
     * 同步方向
     */
    @Schema(description = "同步方向", example = "BIDIRECTIONAL")
    private String syncDirection;

    /**
     * 同步方向描述
     */
    @Schema(description = "同步方向描述", example = "双向同步")
    private String syncDirectionDesc;

    /**
     * 同步模式
     */
    @Schema(description = "同步模式", example = "REAL_TIME")
    private String syncMode;

    /**
     * 同步模式描述
     */
    @Schema(description = "同步模式描述", example = "实时同步")
    private String syncModeDesc;

    /**
     * 调度表达式
     */
    @Schema(description = "调度表达式", example = "0 0 * * * ?")
    private String cronExpression;

    /**
     * 下次执行时间
     */
    @Schema(description = "下次执行时间", example = "2025-01-02T00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime nextExecutionTime;

    /**
     * 认证类型
     */
    @Schema(description = "认证类型", example = "SERVICE_ACCOUNT")
    private String authType;

    /**
     * 认证类型描述
     */
    @Schema(description = "认证类型描述", example = "服务账号")
    private String authTypeDesc;

    /**
     * 认证状态
     */
    @Schema(description = "认证状态", example = "VALID")
    private String authStatus;

    /**
     * 认证状态描述
     */
    @Schema(description = "认证状态描述", example = "认证有效")
    private String authStatusDesc;

    /**
     * 令牌过期时间
     */
    @Schema(description = "令牌过期时间", example = "2025-12-31T23:59:59")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tokenExpiresAt;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    /**
     * 配置状态
     */
    @Schema(description = "配置状态", example = "ACTIVE")
    private String configStatus;

    /**
     * 配置状态描述
     */
    @Schema(description = "配置状态描述", example = "活跃")
    private String configStatusDesc;

    /**
     * 字段映射配置
     */
    @Schema(description = "字段映射配置")
    private FieldMappingInfo fieldMapping;

    /**
     * 过滤条件
     */
    @Schema(description = "过滤条件")
    private FilterConditionInfo filterCondition;

    /**
     * 同步设置
     */
    @Schema(description = "同步设置")
    private SyncSettingsInfo syncSettings;

    /**
     * 同步统计
     */
    @Schema(description = "同步统计")
    private SyncStatistics syncStatistics;

    /**
     * 最后同步信息
     */
    @Schema(description = "最后同步信息")
    private LastSyncInfo lastSync;

    /**
     * 配置版本
     */
    @Schema(description = "配置版本", example = "1")
    private Integer configVersion;

    /**
     * 版本说明
     */
    @Schema(description = "版本说明", example = "v1.1 - 新增自定义列支持")
    private String versionNotes;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 群组名称
     */
    @Schema(description = "群组名称", example = "家庭记账群")
    private String groupName;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID", example = "1")
    private Long createdBy;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名", example = "张三")
    private String createdByName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-01-01T10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID", example = "1")
    private Long updatedBy;

    /**
     * 更新人姓名
     */
    @Schema(description = "更新人姓名", example = "张三")
    private String updatedByName;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2025-01-01T10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "用于同步日常开销记录")
    private String remark;

    /**
     * 自定义标签
     */
    @Schema(description = "自定义标签")
    private List<String> customTags;

    /**
     * 字段映射信息
     */
    @Data
    @Schema(description = "字段映射信息")
    public static class FieldMappingInfo {

        /**
         * 数据起始行
         */
        @Schema(description = "数据起始行", example = "2")
        private Integer dataStartRow;

        /**
         * 表头行
         */
        @Schema(description = "表头行", example = "1")
        private Integer headerRow;

        /**
         * 字段映射关系
         */
        @Schema(description = "字段映射关系")
        private Map<String, String> fieldMap;

        /**
         * 字段映射描述
         */
        @Schema(description = "字段映射描述")
        private Map<String, String> fieldMapDesc;

        /**
         * 日期格式
         */
        @Schema(description = "日期格式", example = "yyyy-MM-dd")
        private String dateFormat;

        /**
         * 时间格式
         */
        @Schema(description = "时间格式", example = "HH:mm:ss")
        private String timeFormat;

        /**
         * 数字格式
         */
        @Schema(description = "数字格式", example = "#,##0.00")
        private String numberFormat;

        /**
         * 货币符号
         */
        @Schema(description = "货币符号", example = "¥")
        private String currencySymbol;

        /**
         * 是否包含表头
         */
        @Schema(description = "是否包含表头", example = "true")
        private Boolean includeHeader;

        /**
         * 自定义列配置
         */
        @Schema(description = "自定义列配置")
        private List<CustomColumnInfo> customColumns;

        /**
         * 映射字段数量
         */
        @Schema(description = "映射字段数量", example = "8")
        private Integer mappedFieldCount;
    }

    /**
     * 自定义列信息
     */
    @Data
    @Schema(description = "自定义列信息")
    public static class CustomColumnInfo {

        /**
         * 列名
         */
        @Schema(description = "列名", example = "分类名称")
        private String columnName;

        /**
         * 列索引
         */
        @Schema(description = "列索引", example = "5")
        private Integer columnIndex;

        /**
         * 数据类型
         */
        @Schema(description = "数据类型", example = "STRING")
        private String dataType;

        /**
         * 数据类型描述
         */
        @Schema(description = "数据类型描述", example = "字符串")
        private String dataTypeDesc;

        /**
         * 默认值
         */
        @Schema(description = "默认值", example = "其他")
        private String defaultValue;

        /**
         * 公式表达式
         */
        @Schema(description = "公式表达式", example = "=SUM(D2:D100)")
        private String formula;

        /**
         * 是否必填
         */
        @Schema(description = "是否必填", example = "false")
        private Boolean required;

        /**
         * 验证规则
         */
        @Schema(description = "验证规则")
        private String validationRule;
    }

    /**
     * 过滤条件信息
     */
    @Data
    @Schema(description = "过滤条件信息")
    public static class FilterConditionInfo {

        /**
         * 日期范围过滤
         */
        @Schema(description = "日期范围过滤")
        private DateRangeFilterInfo dateRange;

        /**
         * 金额范围过滤
         */
        @Schema(description = "金额范围过滤")
        private AmountRangeFilterInfo amountRange;

        /**
         * 分类过滤
         */
        @Schema(description = "分类过滤")
        private List<CategoryFilterInfo> categories;

        /**
         * 标签过滤
         */
        @Schema(description = "标签过滤")
        private List<String> tags;

        /**
         * 账单类型过滤
         */
        @Schema(description = "账单类型过滤")
        private List<String> transactionTypes;

        /**
         * 自定义过滤条件
         */
        @Schema(description = "自定义过滤条件")
        private String customFilter;

        /**
         * 是否包含已删除数据
         */
        @Schema(description = "是否包含已删除数据", example = "false")
        private Boolean includeDeleted;

        /**
         * 过滤条件数量
         */
        @Schema(description = "过滤条件数量", example = "3")
        private Integer filterCount;
    }

    /**
     * 日期范围过滤信息
     */
    @Data
    @Schema(description = "日期范围过滤信息")
    public static class DateRangeFilterInfo {

        /**
         * 开始日期
         */
        @Schema(description = "开始日期", example = "2025-01-01")
        private String startDate;

        /**
         * 结束日期
         */
        @Schema(description = "结束日期", example = "2025-12-31")
        private String endDate;

        /**
         * 相对日期类型
         */
        @Schema(description = "相对日期类型", example = "LAST_30_DAYS")
        private String relativeDateType;

        /**
         * 相对日期类型描述
         */
        @Schema(description = "相对日期类型描述", example = "最近30天")
        private String relativeDateTypeDesc;
    }

    /**
     * 金额范围过滤信息
     */
    @Data
    @Schema(description = "金额范围过滤信息")
    public static class AmountRangeFilterInfo {

        /**
         * 最小金额
         */
        @Schema(description = "最小金额", example = "0.01")
        private String minAmount;

        /**
         * 最大金额
         */
        @Schema(description = "最大金额", example = "10000.00")
        private String maxAmount;
    }

    /**
     * 分类过滤信息
     */
    @Data
    @Schema(description = "分类过滤信息")
    public static class CategoryFilterInfo {

        /**
         * 分类ID
         */
        @Schema(description = "分类ID", example = "1")
        private Long categoryId;

        /**
         * 分类名称
         */
        @Schema(description = "分类名称", example = "餐饮")
        private String categoryName;

        /**
         * 分类路径
         */
        @Schema(description = "分类路径", example = "生活开销/餐饮")
        private String categoryPath;
    }

    /**
     * 同步设置信息
     */
    @Data
    @Schema(description = "同步设置信息")
    public static class SyncSettingsInfo {

        /**
         * 批量大小
         */
        @Schema(description = "批量大小", example = "100")
        private Integer batchSize;

        /**
         * 同步超时时间（秒）
         */
        @Schema(description = "同步超时时间（秒）", example = "300")
        private Integer timeoutSeconds;

        /**
         * 重试次数
         */
        @Schema(description = "重试次数", example = "3")
        private Integer retryCount;

        /**
         * 冲突解决策略
         */
        @Schema(description = "冲突解决策略", example = "LOCAL_WINS")
        private String conflictResolution;

        /**
         * 冲突解决策略描述
         */
        @Schema(description = "冲突解决策略描述", example = "本地优先")
        private String conflictResolutionDesc;

        /**
         * 是否启用增量同步
         */
        @Schema(description = "是否启用增量同步", example = "true")
        private Boolean enableIncrementalSync;

        /**
         * 是否启用数据验证
         */
        @Schema(description = "是否启用数据验证", example = "true")
        private Boolean enableDataValidation;

        /**
         * 是否启用备份
         */
        @Schema(description = "是否启用备份", example = "true")
        private Boolean enableBackup;

        /**
         * 备份保留天数
         */
        @Schema(description = "备份保留天数", example = "30")
        private Integer backupRetentionDays;

        /**
         * 通知设置
         */
        @Schema(description = "通知设置")
        private NotificationSettingsInfo notification;
    }

    /**
     * 通知设置信息
     */
    @Data
    @Schema(description = "通知设置信息")
    public static class NotificationSettingsInfo {

        /**
         * 是否启用同步成功通知
         */
        @Schema(description = "是否启用同步成功通知", example = "false")
        private Boolean enableSuccessNotification;

        /**
         * 是否启用同步失败通知
         */
        @Schema(description = "是否启用同步失败通知", example = "true")
        private Boolean enableFailureNotification;

        /**
         * 通知邮箱
         */
        @Schema(description = "通知邮箱", example = "<EMAIL>")
        private String notificationEmail;

        /**
         * 通知Webhook URL
         */
        @Schema(description = "通知Webhook URL", example = "https://hooks.slack.com/services/...")
        private String webhookUrl;
    }

    /**
     * 同步统计信息
     */
    @Data
    @Schema(description = "同步统计信息")
    public static class SyncStatistics {

        /**
         * 总同步次数
         */
        @Schema(description = "总同步次数", example = "150")
        private Integer totalSyncCount;

        /**
         * 成功同步次数
         */
        @Schema(description = "成功同步次数", example = "145")
        private Integer successSyncCount;

        /**
         * 失败同步次数
         */
        @Schema(description = "失败同步次数", example = "5")
        private Integer failedSyncCount;

        /**
         * 同步成功率
         */
        @Schema(description = "同步成功率", example = "96.67")
        private Double successRate;

        /**
         * 总处理记录数
         */
        @Schema(description = "总处理记录数", example = "15000")
        private Long totalProcessedRecords;

        /**
         * 总成功记录数
         */
        @Schema(description = "总成功记录数", example = "14850")
        private Long totalSuccessRecords;

        /**
         * 总失败记录数
         */
        @Schema(description = "总失败记录数", example = "150")
        private Long totalFailedRecords;

        /**
         * 平均同步耗时（秒）
         */
        @Schema(description = "平均同步耗时（秒）", example = "45")
        private Double avgSyncDuration;

        /**
         * 最大同步耗时（秒）
         */
        @Schema(description = "最大同步耗时（秒）", example = "120")
        private Integer maxSyncDuration;

        /**
         * 最小同步耗时（秒）
         */
        @Schema(description = "最小同步耗时（秒）", example = "10")
        private Integer minSyncDuration;

        /**
         * 最近30天同步次数
         */
        @Schema(description = "最近30天同步次数", example = "30")
        private Integer recentSyncCount;

        /**
         * 最近30天成功率
         */
        @Schema(description = "最近30天成功率", example = "98.33")
        private Double recentSuccessRate;
    }

    /**
     * 最后同步信息
     */
    @Data
    @Schema(description = "最后同步信息")
    public static class LastSyncInfo {

        /**
         * 同步ID
         */
        @Schema(description = "同步ID", example = "SYNC_20250101_001")
        private String syncId;

        /**
         * 同步状态
         */
        @Schema(description = "同步状态", example = "SUCCESS")
        private String syncStatus;

        /**
         * 同步状态描述
         */
        @Schema(description = "同步状态描述", example = "同步成功")
        private String syncStatusDesc;

        /**
         * 同步类型
         */
        @Schema(description = "同步类型", example = "INCREMENTAL")
        private String syncType;

        /**
         * 同步类型描述
         */
        @Schema(description = "同步类型描述", example = "增量同步")
        private String syncTypeDesc;

        /**
         * 同步方向
         */
        @Schema(description = "同步方向", example = "TO_SHEETS")
        private String syncDirection;

        /**
         * 同步方向描述
         */
        @Schema(description = "同步方向描述", example = "同步到表格")
        private String syncDirectionDesc;

        /**
         * 开始时间
         */
        @Schema(description = "开始时间", example = "2025-01-01T10:00:00")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime startTime;

        /**
         * 结束时间
         */
        @Schema(description = "结束时间", example = "2025-01-01T10:02:30")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime endTime;

        /**
         * 执行耗时（秒）
         */
        @Schema(description = "执行耗时（秒）", example = "150")
        private Integer durationSeconds;

        /**
         * 处理记录数
         */
        @Schema(description = "处理记录数", example = "100")
        private Integer processedCount;

        /**
         * 成功记录数
         */
        @Schema(description = "成功记录数", example = "98")
        private Integer successCount;

        /**
         * 失败记录数
         */
        @Schema(description = "失败记录数", example = "2")
        private Integer failedCount;

        /**
         * 错误消息
         */
        @Schema(description = "错误消息", example = "部分记录格式不正确")
        private String errorMessage;

        /**
         * 执行人ID
         */
        @Schema(description = "执行人ID", example = "1")
        private Long executedBy;

        /**
         * 执行人姓名
         */
        @Schema(description = "执行人姓名", example = "张三")
        private String executedByName;

        /**
         * 触发方式
         */
        @Schema(description = "触发方式", example = "SCHEDULED")
        private String triggerType;

        /**
         * 触发方式描述
         */
        @Schema(description = "触发方式描述", example = "定时触发")
        private String triggerTypeDesc;
    }
}
