package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.data.mybatis.base.BaseEntity;

import java.util.List;
import java.util.Map;

/**
 * 通知模板实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "acc_notification_template", autoResultMap = true)
public class NotificationTemplateDO extends BaseEntity {

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板代码
     */
    private String templateCode;

    /**
     * 通知类型
     */
    private String notificationType;

    /**
     * 支持的渠道
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> supportedChannels;

    /**
     * 标题模板
     */
    private String titleTemplate;

    /**
     * 内容模板
     */
    private String contentTemplate;

    /**
     * HTML内容模板（用于邮件）
     */
    private String htmlTemplate;

    /**
     * 短信模板
     */
    private String smsTemplate;

    /**
     * 推送模板
     */
    private String pushTemplate;

    /**
     * 机器人模板
     */
    private String botTemplate;

    /**
     * 模板参数定义
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Map<String, Object>> templateParams;

    /**
     * 默认参数值
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> defaultParams;

    /**
     * 模板变量
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> variables;

    /**
     * 模板样式配置
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> styleConfig;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 模板分类
     */
    private String category;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remark;

}
