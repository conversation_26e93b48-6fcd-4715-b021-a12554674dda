package top.continew.admin.accounting.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.accounting.model.req.CacheConfigReq;
import top.continew.admin.accounting.model.req.CacheQueryReq;
import top.continew.admin.accounting.model.resp.CacheConfigResp;
import top.continew.admin.accounting.model.resp.CacheStatisticsResp;
import top.continew.admin.accounting.service.CacheOptimizationService;
import top.continew.starter.core.constant.StringConstants;
import top.continew.starter.log.core.annotation.Log;
import top.continew.starter.web.model.R;

import java.util.List;
import java.util.Map;

/**
 * 缓存优化 API
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "缓存优化 API")
@RestController
@RequiredArgsConstructor
@Validated
@RequestMapping("/accounting/cache")
public class CacheOptimizationController {

    private final CacheOptimizationService cacheOptimizationService;

    @Operation(summary = "创建缓存配置", description = "创建新的缓存配置")
    @Log(value = "创建缓存配置")
    @PostMapping("/config")
    public R<CacheConfigResp> createCacheConfig(@Valid @RequestBody CacheConfigReq configReq) {
        return R.ok(cacheOptimizationService.createCacheConfig(configReq));
    }

    @Operation(summary = "更新缓存配置", description = "更新指定的缓存配置")
    @Log(value = "更新缓存配置")
    @PutMapping("/config/{configId}")
    public R<CacheConfigResp> updateCacheConfig(@Parameter(description = "配置ID") @PathVariable Long configId,
                                                @Valid @RequestBody CacheConfigReq configReq) {
        return R.ok(cacheOptimizationService.updateCacheConfig(configId, configReq));
    }

    @Operation(summary = "删除缓存配置", description = "删除指定的缓存配置")
    @Log(value = "删除缓存配置")
    @DeleteMapping("/config/{configId}")
    public R<Boolean> deleteCacheConfig(@Parameter(description = "配置ID") @PathVariable Long configId) {
        return R.ok(cacheOptimizationService.deleteCacheConfig(configId));
    }

    @Operation(summary = "获取缓存配置详情", description = "获取指定缓存配置的详细信息")
    @GetMapping("/config/{configId}")
    public R<CacheConfigResp> getCacheConfig(@Parameter(description = "配置ID") @PathVariable Long configId) {
        return R.ok(cacheOptimizationService.getCacheConfig(configId));
    }

    @Operation(summary = "分页查询缓存配置", description = "分页查询缓存配置列表")
    @GetMapping("/config/page")
    public R<IPage<CacheConfigResp>> pageCacheConfigs(@Valid CacheQueryReq queryReq) {
        return R.ok(cacheOptimizationService.pageCacheConfigs(queryReq));
    }

    @Operation(summary = "查询缓存配置列表", description = "查询缓存配置列表")
    @GetMapping("/config/list")
    public R<List<CacheConfigResp>> listCacheConfigs(@Valid CacheQueryReq queryReq) {
        return R.ok(cacheOptimizationService.listCacheConfigs(queryReq));
    }

    @Operation(summary = "应用缓存配置", description = "应用指定的缓存配置")
    @Log(value = "应用缓存配置")
    @PostMapping("/config/{configId}/apply")
    public R<Boolean> applyCacheConfig(@Parameter(description = "配置ID") @PathVariable Long configId) {
        return R.ok(cacheOptimizationService.applyCacheConfig(configId));
    }

    @Operation(summary = "批量应用缓存配置", description = "批量应用多个缓存配置")
    @Log(value = "批量应用缓存配置")
    @PostMapping("/config/batch-apply")
    public R<Map<Long, Boolean>> batchApplyCacheConfigs(@RequestBody List<Long> configIds) {
        return R.ok(cacheOptimizationService.batchApplyCacheConfigs(configIds));
    }

    @Operation(summary = "预热缓存", description = "预热指定的缓存")
    @Log(value = "预热缓存")
    @PostMapping("/preload/{cacheName}")
    public R<Boolean> preloadCache(@Parameter(description = "缓存名称") @PathVariable String cacheName) {
        return R.ok(cacheOptimizationService.preloadCache(cacheName));
    }

    @Operation(summary = "批量预热缓存", description = "批量预热多个缓存")
    @Log(value = "批量预热缓存")
    @PostMapping("/preload/batch")
    public R<Map<String, Boolean>> batchPreloadCaches(@RequestBody List<String> cacheNames) {
        return R.ok(cacheOptimizationService.batchPreloadCaches(cacheNames));
    }

    @Operation(summary = "刷新缓存", description = "刷新指定的缓存")
    @Log(value = "刷新缓存")
    @PostMapping("/refresh/{cacheName}")
    public R<Boolean> refreshCache(@Parameter(description = "缓存名称") @PathVariable String cacheName,
                                   @Parameter(description = "缓存键") @RequestParam(required = false) String cacheKey) {
        return R.ok(cacheOptimizationService.refreshCache(cacheName, cacheKey));
    }

    @Operation(summary = "清除缓存", description = "清除指定的缓存")
    @Log(value = "清除缓存")
    @DeleteMapping("/clear/{cacheName}")
    public R<Boolean> clearCache(@Parameter(description = "缓存名称") @PathVariable String cacheName,
                                 @Parameter(description = "缓存键") @RequestParam(required = false) String cacheKey) {
        return R.ok(cacheOptimizationService.clearCache(cacheName, cacheKey));
    }

    @Operation(summary = "批量清除缓存", description = "批量清除多个缓存键")
    @Log(value = "批量清除缓存")
    @DeleteMapping("/clear/{cacheName}/batch")
    public R<Map<String, Boolean>> batchClearCache(@Parameter(description = "缓存名称") @PathVariable String cacheName,
                                                    @RequestBody List<String> cacheKeys) {
        return R.ok(cacheOptimizationService.batchClearCache(cacheName, cacheKeys));
    }

    @Operation(summary = "清除所有缓存", description = "清除所有缓存数据")
    @Log(value = "清除所有缓存")
    @DeleteMapping("/clear/all")
    public R<Boolean> clearAllCaches() {
        return R.ok(cacheOptimizationService.clearAllCaches());
    }

    @Operation(summary = "获取缓存统计信息", description = "获取缓存的统计信息")
    @GetMapping("/statistics")
    public R<CacheStatisticsResp> getCacheStatistics(@Parameter(description = "群组ID") @RequestParam(required = false) Long groupId) {
        return R.ok(cacheOptimizationService.getCacheStatistics(groupId));
    }

    @Operation(summary = "获取缓存性能指标", description = "获取指定缓存的性能指标")
    @GetMapping("/metrics/{cacheName}")
    public R<List<Map<String, Object>>> getCachePerformanceMetrics(@Parameter(description = "缓存名称") @PathVariable String cacheName,
                                                                    @Parameter(description = "时间范围（小时）") @RequestParam(defaultValue = "24") Integer hours) {
        return R.ok(cacheOptimizationService.getCachePerformanceMetrics(cacheName, hours));
    }

    @Operation(summary = "获取热点数据", description = "获取指定缓存的热点数据")
    @GetMapping("/hotspot/{cacheName}")
    public R<List<Map<String, Object>>> getHotspotData(@Parameter(description = "缓存名称") @PathVariable String cacheName,
                                                        @Parameter(description = "限制数量") @RequestParam(defaultValue = "10") Integer limit) {
        return R.ok(cacheOptimizationService.getHotspotData(cacheName, limit));
    }

    @Operation(summary = "获取缓存健康状态", description = "获取缓存系统的健康状态")
    @GetMapping("/health")
    public R<Map<String, Object>> getCacheHealthStatus() {
        return R.ok(cacheOptimizationService.getCacheHealthStatus());
    }

    @Operation(summary = "检测缓存异常", description = "检测缓存系统的异常情况")
    @GetMapping("/anomalies")
    public R<List<Map<String, Object>>> detectCacheAnomalies() {
        return R.ok(cacheOptimizationService.detectCacheAnomalies());
    }

    @Operation(summary = "获取缓存优化建议", description = "获取缓存系统的优化建议")
    @GetMapping("/suggestions")
    public R<List<Map<String, Object>>> getCacheOptimizationSuggestions(@Parameter(description = "群组ID") @RequestParam(required = false) Long groupId) {
        return R.ok(cacheOptimizationService.getCacheOptimizationSuggestions(groupId));
    }

    @Operation(summary = "分析缓存使用模式", description = "分析指定缓存的使用模式")
    @GetMapping("/analysis/{cacheName}/usage")
    public R<Map<String, Object>> analyzeCacheUsagePattern(@Parameter(description = "缓存名称") @PathVariable String cacheName,
                                                            @Parameter(description = "分析天数") @RequestParam(defaultValue = "7") Integer days) {
        return R.ok(cacheOptimizationService.analyzeCacheUsagePattern(cacheName, days));
    }

    @Operation(summary = "预测缓存容量需求", description = "预测指定缓存的容量需求")
    @GetMapping("/analysis/{cacheName}/capacity")
    public R<Map<String, Object>> predictCacheCapacity(@Parameter(description = "缓存名称") @PathVariable String cacheName,
                                                        @Parameter(description = "预测天数") @RequestParam(defaultValue = "30") Integer days) {
        return R.ok(cacheOptimizationService.predictCacheCapacity(cacheName, days));
    }

    @Operation(summary = "测试缓存连接", description = "测试指定缓存配置的连接")
    @PostMapping("/config/{configId}/test")
    public R<Map<String, Object>> testCacheConnection(@Parameter(description = "配置ID") @PathVariable Long configId) {
        return R.ok(cacheOptimizationService.testCacheConnection(configId));
    }

    @Operation(summary = "导出缓存配置", description = "导出指定的缓存配置")
    @PostMapping("/config/export")
    public R<Map<String, Object>> exportCacheConfigs(@RequestBody List<Long> configIds) {
        return R.ok(cacheOptimizationService.exportCacheConfigs(configIds));
    }

    @Operation(summary = "导入缓存配置", description = "导入缓存配置数据")
    @Log(value = "导入缓存配置")
    @PostMapping("/config/import")
    public R<Map<String, Object>> importCacheConfigs(@RequestBody Map<String, Object> configData) {
        return R.ok(cacheOptimizationService.importCacheConfigs(configData));
    }

    @Operation(summary = "验证缓存配置", description = "验证缓存配置的有效性")
    @PostMapping("/config/validate")
    public R<Map<String, Object>> validateCacheConfig(@Valid @RequestBody CacheConfigReq configReq) {
        return R.ok(cacheOptimizationService.validateCacheConfig(configReq));
    }

    @Operation(summary = "获取缓存键列表", description = "获取指定缓存的键列表")
    @GetMapping("/keys/{cacheName}")
    public R<List<String>> getCacheKeys(@Parameter(description = "缓存名称") @PathVariable String cacheName,
                                        @Parameter(description = "键模式") @RequestParam(required = false) String pattern,
                                        @Parameter(description = "限制数量") @RequestParam(defaultValue = "100") Integer limit) {
        return R.ok(cacheOptimizationService.getCacheKeys(cacheName, pattern, limit));
    }

    @Operation(summary = "获取缓存值", description = "获取指定缓存键的值")
    @GetMapping("/value/{cacheName}/{cacheKey}")
    public R<Object> getCacheValue(@Parameter(description = "缓存名称") @PathVariable String cacheName,
                                   @Parameter(description = "缓存键") @PathVariable String cacheKey) {
        return R.ok(cacheOptimizationService.getCacheValue(cacheName, cacheKey));
    }

    @Operation(summary = "设置缓存值", description = "设置指定缓存键的值")
    @Log(value = "设置缓存值")
    @PostMapping("/value/{cacheName}/{cacheKey}")
    public R<Boolean> setCacheValue(@Parameter(description = "缓存名称") @PathVariable String cacheName,
                                    @Parameter(description = "缓存键") @PathVariable String cacheKey,
                                    @RequestBody Object cacheValue,
                                    @Parameter(description = "过期时间（秒）") @RequestParam(required = false) Long expireTime) {
        return R.ok(cacheOptimizationService.setCacheValue(cacheName, cacheKey, cacheValue, expireTime));
    }

}
