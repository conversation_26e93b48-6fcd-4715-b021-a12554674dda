package top.continew.admin.bot.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.continew.admin.accounting.enums.PlatformType;
import top.continew.admin.accounting.enums.TransactionType;
import top.continew.admin.accounting.model.resp.TransactionDetailResp;
import top.continew.admin.accounting.model.resp.TransactionListResp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 消息格式化工具
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
public class MessageFormatter {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("MM-dd");

    /**
     * 格式化交易确认消息
     */
    public String formatTransactionConfirmation(TransactionDetailResp transaction, PlatformType platform) {
        StringBuilder sb = new StringBuilder();
        
        // 根据平台选择格式
        switch (platform) {
            case TELEGRAM -> formatTelegramTransactionConfirmation(sb, transaction);
            case DISCORD -> formatDiscordTransactionConfirmation(sb, transaction);
            default -> formatDefaultTransactionConfirmation(sb, transaction);
        }
        
        return sb.toString();
    }

    /**
     * 格式化Telegram交易确认消息
     */
    private void formatTelegramTransactionConfirmation(StringBuilder sb, TransactionDetailResp transaction) {
        sb.append("✅ <b>记账成功！</b>\n\n");
        
        // 金额和类型
        String typeIcon = getTransactionTypeIcon(transaction.getType());
        sb.append(String.format("💰 <b>金额:</b> %s %s %s\n", 
                typeIcon, transaction.getAmount(), transaction.getCurrency()));
        
        // 描述
        sb.append(String.format("📝 <b>描述:</b> %s\n", transaction.getDescription()));
        
        // 时间
        sb.append(String.format("🕐 <b>时间:</b> %s\n", 
                transaction.getTransactionTime().format(DATE_TIME_FORMATTER)));
        
        // 分类
        if (StrUtil.isNotBlank(transaction.getCategoryName())) {
            sb.append(String.format("📂 <b>分类:</b> %s\n", transaction.getCategoryName()));
        }
        
        // 钱包
        if (StrUtil.isNotBlank(transaction.getWalletName())) {
            sb.append(String.format("💳 <b>钱包:</b> %s\n", transaction.getWalletName()));
        }
        
        // 标签
        if (CollUtil.isNotEmpty(transaction.getTags())) {
            sb.append(String.format("🏷️ <b>标签:</b> %s\n", String.join(", ", transaction.getTags())));
        }
        
        // 分摊信息
        if (transaction.getSplitEnabled() != null && transaction.getSplitEnabled()) {
            sb.append("👥 <b>分摊:</b> 已启用\n");
        }
        
        sb.append(String.format("\n📊 <b>交易ID:</b> <code>%d</code>", transaction.getId()));
    }

    /**
     * 格式化Discord交易确认消息
     */
    private void formatDiscordTransactionConfirmation(StringBuilder sb, TransactionDetailResp transaction) {
        sb.append("✅ **记账成功！**\n\n");
        
        // 金额和类型
        String typeIcon = getTransactionTypeIcon(transaction.getType());
        sb.append(String.format("💰 **金额:** %s %s %s\n", 
                typeIcon, transaction.getAmount(), transaction.getCurrency()));
        
        // 描述
        sb.append(String.format("📝 **描述:** %s\n", transaction.getDescription()));
        
        // 时间
        sb.append(String.format("🕐 **时间:** %s\n", 
                transaction.getTransactionTime().format(DATE_TIME_FORMATTER)));
        
        // 其他信息...
        sb.append(String.format("\n📊 **交易ID:** `%d`", transaction.getId()));
    }

    /**
     * 格式化默认交易确认消息
     */
    private void formatDefaultTransactionConfirmation(StringBuilder sb, TransactionDetailResp transaction) {
        sb.append("✅ 记账成功！\n\n");
        
        String typeIcon = getTransactionTypeIcon(transaction.getType());
        sb.append(String.format("💰 金额: %s %s %s\n", 
                typeIcon, transaction.getAmount(), transaction.getCurrency()));
        sb.append(String.format("📝 描述: %s\n", transaction.getDescription()));
        sb.append(String.format("🕐 时间: %s\n", 
                transaction.getTransactionTime().format(DATE_TIME_FORMATTER)));
        sb.append(String.format("\n📊 交易ID: %d", transaction.getId()));
    }

    /**
     * 格式化交易列表消息
     */
    public String formatTransactionList(List<TransactionListResp> transactions, PlatformType platform) {
        if (CollUtil.isEmpty(transactions)) {
            return "📋 暂无交易记录";
        }
        
        StringBuilder sb = new StringBuilder();
        
        switch (platform) {
            case TELEGRAM -> formatTelegramTransactionList(sb, transactions);
            case DISCORD -> formatDiscordTransactionList(sb, transactions);
            default -> formatDefaultTransactionList(sb, transactions);
        }
        
        return sb.toString();
    }

    /**
     * 格式化Telegram交易列表
     */
    private void formatTelegramTransactionList(StringBuilder sb, List<TransactionListResp> transactions) {
        sb.append("📋 <b>最近交易记录</b>\n\n");
        
        for (int i = 0; i < Math.min(transactions.size(), 10); i++) {
            TransactionListResp transaction = transactions.get(i);
            String typeIcon = getTransactionTypeIcon(transaction.getType());
            
            sb.append(String.format("%d. %s <b>%s</b> %s\n", 
                    i + 1, typeIcon, transaction.getAmount(), transaction.getCurrency()));
            sb.append(String.format("   📝 %s\n", transaction.getDescription()));
            sb.append(String.format("   🕐 %s\n\n", 
                    transaction.getTransactionTime().format(DATE_FORMATTER)));
        }
        
        if (transactions.size() > 10) {
            sb.append(String.format("... 还有 %d 条记录", transactions.size() - 10));
        }
    }

    /**
     * 格式化Discord交易列表
     */
    private void formatDiscordTransactionList(StringBuilder sb, List<TransactionListResp> transactions) {
        sb.append("📋 **最近交易记录**\n\n");
        
        for (int i = 0; i < Math.min(transactions.size(), 10); i++) {
            TransactionListResp transaction = transactions.get(i);
            String typeIcon = getTransactionTypeIcon(transaction.getType());
            
            sb.append(String.format("%d. %s **%s** %s\n", 
                    i + 1, typeIcon, transaction.getAmount(), transaction.getCurrency()));
            sb.append(String.format("   📝 %s\n", transaction.getDescription()));
            sb.append(String.format("   🕐 %s\n\n", 
                    transaction.getTransactionTime().format(DATE_FORMATTER)));
        }
        
        if (transactions.size() > 10) {
            sb.append(String.format("... 还有 %d 条记录", transactions.size() - 10));
        }
    }

    /**
     * 格式化默认交易列表
     */
    private void formatDefaultTransactionList(StringBuilder sb, List<TransactionListResp> transactions) {
        sb.append("📋 最近交易记录\n\n");
        
        for (int i = 0; i < Math.min(transactions.size(), 10); i++) {
            TransactionListResp transaction = transactions.get(i);
            String typeIcon = getTransactionTypeIcon(transaction.getType());
            
            sb.append(String.format("%d. %s %s %s\n", 
                    i + 1, typeIcon, transaction.getAmount(), transaction.getCurrency()));
            sb.append(String.format("   📝 %s\n", transaction.getDescription()));
            sb.append(String.format("   🕐 %s\n\n", 
                    transaction.getTransactionTime().format(DATE_FORMATTER)));
        }
    }

    /**
     * 格式化余额消息
     */
    public String formatBalance(BigDecimal balance, String currency, PlatformType platform) {
        String balanceText = String.format("💰 当前余额: %s %s", balance, currency);
        
        return switch (platform) {
            case TELEGRAM -> String.format("<b>%s</b>", balanceText);
            case DISCORD -> String.format("**%s**", balanceText);
            default -> balanceText;
        };
    }

    /**
     * 格式化错误消息
     */
    public String formatError(String errorMessage, PlatformType platform) {
        String errorText = "❌ " + errorMessage;
        
        return switch (platform) {
            case TELEGRAM -> String.format("<b>%s</b>", errorText);
            case DISCORD -> String.format("**%s**", errorText);
            default -> errorText;
        };
    }

    /**
     * 格式化成功消息
     */
    public String formatSuccess(String successMessage, PlatformType platform) {
        String successText = "✅ " + successMessage;
        
        return switch (platform) {
            case TELEGRAM -> String.format("<b>%s</b>", successText);
            case DISCORD -> String.format("**%s**", successText);
            default -> successText;
        };
    }

    /**
     * 格式化警告消息
     */
    public String formatWarning(String warningMessage, PlatformType platform) {
        String warningText = "⚠️ " + warningMessage;
        
        return switch (platform) {
            case TELEGRAM -> String.format("<i>%s</i>", warningText);
            case DISCORD -> String.format("*%s*", warningText);
            default -> warningText;
        };
    }

    /**
     * 格式化信息消息
     */
    public String formatInfo(String infoMessage, PlatformType platform) {
        String infoText = "ℹ️ " + infoMessage;
        
        return switch (platform) {
            case TELEGRAM -> infoText;
            case DISCORD -> infoText;
            default -> infoText;
        };
    }

    /**
     * 获取交易类型图标
     */
    private String getTransactionTypeIcon(TransactionType type) {
        return switch (type) {
            case INCOME -> "📈";
            case EXPENSE -> "📉";
            case TRANSFER -> "🔄";
        };
    }

    /**
     * 转义特殊字符（Telegram HTML模式）
     */
    public String escapeTelegramHtml(String text) {
        if (StrUtil.isBlank(text)) {
            return text;
        }
        
        return text.replace("&", "&amp;")
                  .replace("<", "&lt;")
                  .replace(">", "&gt;");
    }

    /**
     * 转义特殊字符（Discord Markdown模式）
     */
    public String escapeDiscordMarkdown(String text) {
        if (StrUtil.isBlank(text)) {
            return text;
        }
        
        return text.replace("*", "\\*")
                  .replace("_", "\\_")
                  .replace("`", "\\`")
                  .replace("~", "\\~")
                  .replace("|", "\\|");
    }

    /**
     * 截断长文本
     */
    public String truncateText(String text, int maxLength) {
        if (StrUtil.isBlank(text) || text.length() <= maxLength) {
            return text;
        }
        
        return text.substring(0, maxLength - 3) + "...";
    }

    /**
     * 格式化时间为相对时间
     */
    public String formatRelativeTime(LocalDateTime dateTime) {
        LocalDateTime now = LocalDateTime.now();
        long minutes = java.time.Duration.between(dateTime, now).toMinutes();
        
        if (minutes < 1) {
            return "刚刚";
        } else if (minutes < 60) {
            return minutes + "分钟前";
        } else if (minutes < 1440) {
            return (minutes / 60) + "小时前";
        } else {
            return dateTime.format(DATE_FORMATTER);
        }
    }
}
