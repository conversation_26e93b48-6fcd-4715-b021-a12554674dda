package top.continew.admin.accounting.base;

import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service层测试基类
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@SpringBootTest
@ActiveProfiles("test")
@ExtendWith(MockitoExtension.class)
@Transactional
public abstract class BaseServiceTest extends BaseTest {

    /**
     * 创建测试数据的方法，子类可重写
     */
    protected void createTestData() {
        // 默认空实现，子类可重写
    }

    /**
     * 清理测试数据的方法，子类可重写
     */
    protected void cleanTestData() {
        // 默认空实现，子类可重写
    }

}
