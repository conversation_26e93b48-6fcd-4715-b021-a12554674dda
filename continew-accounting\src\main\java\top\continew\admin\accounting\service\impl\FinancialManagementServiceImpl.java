package top.continew.admin.accounting.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.accounting.mapper.FinancialManagementMapper;
import top.continew.admin.accounting.model.query.BudgetQuery;
import top.continew.admin.accounting.model.query.CostAnalysisQuery;
import top.continew.admin.accounting.model.req.*;
import top.continew.admin.accounting.model.resp.*;
import top.continew.admin.accounting.service.FinancialManagementService;
import top.continew.admin.accounting.service.GroupService;
import top.continew.admin.common.util.helper.LoginHelper;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.core.util.validate.CheckUtils;
import top.continew.starter.data.core.util.SecurityContextHolder;
import top.continew.starter.extension.crud.model.resp.PageResp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 财务管理服务实现
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FinancialManagementServiceImpl implements FinancialManagementService {

    private final FinancialManagementMapper financialManagementMapper;
    private final GroupService groupService;

    // ==================== 预算管理 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createBudget(BudgetCreateReq req) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查群组权限
        CheckUtils.throwIf(!groupService.isAdmin(req.getGroupId(), userId), "您没有权限创建预算");
        
        // 验证预算数据
        this.validateBudgetData(req);
        
        // 检查预算重复
        CheckUtils.throwIf(this.isBudgetExists(req.getGroupId(), req.getBudgetName(), req.getBudgetPeriod(), null),
                "该期间已存在同名预算");
        
        // 创建预算
        Long budgetId = financialManagementMapper.createBudget(req);
        
        // 创建预算分配
        if (CollUtil.isNotEmpty(req.getAllocations())) {
            for (BudgetCreateReq.BudgetAllocation allocation : req.getAllocations()) {
                allocation.setBudgetId(budgetId);
                financialManagementMapper.createBudgetAllocation(allocation);
            }
        }
        
        // 创建预警设置
        if (req.getAlertSettings() != null) {
            req.getAlertSettings().setBudgetId(budgetId);
            financialManagementMapper.createBudgetAlert(req.getAlertSettings());
        }
        
        // 创建审批设置
        if (req.getApprovalSettings() != null) {
            req.getApprovalSettings().setBudgetId(budgetId);
            financialManagementMapper.createBudgetApproval(req.getApprovalSettings());
        }
        
        log.info("创建预算成功，ID: {}, 群组: {}, 名称: {}", budgetId, req.getGroupId(), req.getBudgetName());
        return budgetId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBudget(Long budgetId, BudgetUpdateReq req) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查预算是否存在
        CheckUtils.throwIfNull(financialManagementMapper.getBudgetById(budgetId), "预算不存在");
        
        // 检查操作权限
        CheckUtils.throwIf(!financialManagementMapper.hasBudgetPermission(budgetId, userId), "您没有权限修改此预算");
        
        // 验证预算数据
        if (req.getBudgetName() != null || req.getTotalAmount() != null || req.getAllocations() != null) {
            this.validateBudgetUpdateData(req);
        }
        
        // 更新预算基本信息
        financialManagementMapper.updateBudget(budgetId, req);
        
        // 更新预算分配
        if (req.getAllocations() != null) {
            // 删除原有分配
            financialManagementMapper.deleteBudgetAllocations(budgetId);
            // 创建新分配
            for (BudgetUpdateReq.BudgetAllocation allocation : req.getAllocations()) {
                allocation.setBudgetId(budgetId);
                financialManagementMapper.createBudgetAllocation(allocation);
            }
        }
        
        // 更新预警设置
        if (req.getAlertSettings() != null) {
            req.getAlertSettings().setBudgetId(budgetId);
            financialManagementMapper.updateBudgetAlert(budgetId, req.getAlertSettings());
        }
        
        // 更新审批设置
        if (req.getApprovalSettings() != null) {
            req.getApprovalSettings().setBudgetId(budgetId);
            financialManagementMapper.updateBudgetApproval(budgetId, req.getApprovalSettings());
        }
        
        log.info("更新预算成功，ID: {}", budgetId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBudget(Long budgetId) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查预算是否存在
        CheckUtils.throwIfNull(financialManagementMapper.getBudgetById(budgetId), "预算不存在");
        
        // 检查操作权限
        CheckUtils.throwIf(!financialManagementMapper.hasBudgetPermission(budgetId, userId), "您没有权限删除此预算");
        
        // 检查预算是否可以删除
        CheckUtils.throwIf(financialManagementMapper.isBudgetInUse(budgetId), "预算正在使用中，无法删除");
        
        // 删除相关数据
        financialManagementMapper.deleteBudgetAllocations(budgetId);
        financialManagementMapper.deleteBudgetAlert(budgetId);
        financialManagementMapper.deleteBudgetApproval(budgetId);
        financialManagementMapper.deleteBudget(budgetId);
        
        log.info("删除预算成功，ID: {}", budgetId);
    }

    @Override
    public PageResp<BudgetListResp> getBudgetList(BudgetQuery query) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查群组权限
        CheckUtils.throwIf(!groupService.isMember(query.getGroupId(), userId), "您没有权限查看此群组的预算");
        
        return financialManagementMapper.getBudgetList(query);
    }

    @Override
    public BudgetDetailResp getBudgetDetail(Long budgetId) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查预算是否存在
        BudgetDetailResp budget = financialManagementMapper.getBudgetDetail(budgetId);
        CheckUtils.throwIfNull(budget, "预算不存在");
        
        // 检查查看权限
        CheckUtils.throwIf(!groupService.isMember(budget.getGroupId(), userId), "您没有权限查看此预算");
        
        return budget;
    }

    @Override
    public BudgetExecutionResp getBudgetExecution(Long budgetId) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查预算是否存在和权限
        BudgetDetailResp budget = financialManagementMapper.getBudgetDetail(budgetId);
        CheckUtils.throwIfNull(budget, "预算不存在");
        CheckUtils.throwIf(!groupService.isMember(budget.getGroupId(), userId), "您没有权限查看此预算执行情况");
        
        return financialManagementMapper.getBudgetExecution(budgetId);
    }

    // ==================== 审核管理 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long submitAudit(AuditSubmitReq req) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查群组权限
        CheckUtils.throwIf(!groupService.isMember(req.getGroupId(), userId), "您没有权限提交审核");
        
        // 验证审核数据
        this.validateAuditData(req);
        
        // 创建审核
        Long auditId = financialManagementMapper.createAudit(req);
        
        // 创建审核规则
        if (req.getAuditRules() != null) {
            req.getAuditRules().setAuditId(auditId);
            financialManagementMapper.createAuditRules(req.getAuditRules());
        }
        
        // 创建通知设置
        if (req.getNotificationSettings() != null) {
            req.getNotificationSettings().setAuditId(auditId);
            financialManagementMapper.createAuditNotification(req.getNotificationSettings());
        }
        
        log.info("提交审核成功，ID: {}, 群组: {}, 标题: {}", auditId, req.getGroupId(), req.getAuditTitle());
        return auditId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approveAudit(Long auditId, AuditReq req) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查审核是否存在
        CheckUtils.throwIfNull(financialManagementMapper.getAuditById(auditId), "审核不存在");
        
        // 检查审核权限
        CheckUtils.throwIf(!financialManagementMapper.hasAuditPermission(auditId, userId), "您没有权限审核此项目");
        
        // 检查审核状态
        CheckUtils.throwIf(!financialManagementMapper.isAuditPending(auditId), "审核状态不正确");
        
        // 执行审核
        financialManagementMapper.approveAudit(auditId, req);
        
        // 记录审核历史
        financialManagementMapper.createAuditHistory(auditId, userId, "APPROVE", req.getAuditComment());
        
        log.info("审核通过，ID: {}, 审核人: {}", auditId, userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAudit(BatchAuditReq req) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 验证批量审核数据
        this.validateBatchAuditData(req);
        
        // 执行批量审核
        for (BatchAuditReq.IndividualAuditDetail detail : req.getAuditDetails()) {
            try {
                // 检查审核权限
                if (financialManagementMapper.hasAuditPermission(detail.getAuditId(), userId)) {
                    financialManagementMapper.approveAudit(detail.getAuditId(), detail);
                    financialManagementMapper.createAuditHistory(detail.getAuditId(), userId, "BATCH_APPROVE", detail.getAuditComment());
                }
            } catch (Exception e) {
                log.error("批量审核失败，审核ID: {}, 错误: {}", detail.getAuditId(), e.getMessage());
                // 根据错误处理策略决定是否继续
                if ("STOP_ON_ERROR".equals(req.getBatchRules().getErrorHandling())) {
                    throw new BusinessException("批量审核失败：" + e.getMessage());
                }
            }
        }
        
        log.info("批量审核完成，用户: {}, 审核数量: {}", userId, req.getAuditDetails().size());
    }

    // ==================== 财务预警 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createAlertRule(AlertRuleCreateReq req) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查群组权限
        CheckUtils.throwIf(!groupService.isAdmin(req.getGroupId(), userId), "您没有权限创建预警规则");
        
        // 验证预警规则数据
        this.validateAlertRuleData(req);
        
        // 检查规则名称重复
        CheckUtils.throwIf(this.isAlertRuleNameExists(req.getGroupId(), req.getRuleName(), null),
                "群组内已存在同名预警规则");
        
        // 创建预警规则
        Long ruleId = financialManagementMapper.createAlertRule(req);
        
        log.info("创建预警规则成功，ID: {}, 群组: {}, 名称: {}", ruleId, req.getGroupId(), req.getRuleName());
        return ruleId;
    }

    @Override
    public void checkAlerts(Long groupId) {
        // 获取群组的所有启用预警规则
        List<AlertRuleCreateReq> rules = financialManagementMapper.getEnabledAlertRules(groupId);
        
        for (AlertRuleCreateReq rule : rules) {
            try {
                // 检查预警条件
                boolean triggered = this.checkAlertCondition(rule);
                
                if (triggered) {
                    // 创建预警
                    financialManagementMapper.createAlert(rule.getGroupId(), rule.getRuleName(), 
                            rule.getAlertLevel(), "预警条件触发", LocalDateTime.now());
                    
                    // 发送通知
                    this.sendAlertNotification(rule);
                    
                    log.info("预警触发，规则: {}, 群组: {}", rule.getRuleName(), groupId);
                }
            } catch (Exception e) {
                log.error("检查预警规则失败，规则ID: {}, 错误: {}", rule.getRuleName(), e.getMessage());
            }
        }
    }

    // ==================== 成本分析 ====================

    @Override
    public CostAnalysisResp analyzeCost(CostAnalysisQuery query) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查群组权限
        CheckUtils.throwIf(!groupService.isMember(query.getGroupId(), userId), "您没有权限查看此群组的成本分析");
        
        // 验证查询参数
        this.validateCostAnalysisQuery(query);
        
        // 执行成本分析
        return financialManagementMapper.analyzeCost(query);
    }

    // ==================== 私有方法 ====================

    /**
     * 验证预算数据
     */
    private void validateBudgetData(BudgetCreateReq req) {
        CheckUtils.throwIfBlank(req.getBudgetName(), "预算名称不能为空");
        CheckUtils.throwIfNull(req.getTotalAmount(), "预算总金额不能为空");
        CheckUtils.throwIf(req.getTotalAmount().compareTo(BigDecimal.ZERO) <= 0, "预算总金额必须大于0");
        CheckUtils.throwIfBlank(req.getBudgetPeriod(), "预算期间不能为空");
        
        // 验证分配总额不超过预算总额
        if (CollUtil.isNotEmpty(req.getAllocations())) {
            BigDecimal totalAllocation = req.getAllocations().stream()
                    .map(BudgetCreateReq.BudgetAllocation::getAllocatedAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            CheckUtils.throwIf(totalAllocation.compareTo(req.getTotalAmount()) > 0, "分配总金额不能超过预算总金额");
        }
    }

    /**
     * 验证预算更新数据
     */
    private void validateBudgetUpdateData(BudgetUpdateReq req) {
        if (req.getTotalAmount() != null) {
            CheckUtils.throwIf(req.getTotalAmount().compareTo(BigDecimal.ZERO) <= 0, "预算总金额必须大于0");
        }
        
        // 验证分配总额
        if (CollUtil.isNotEmpty(req.getAllocations()) && req.getTotalAmount() != null) {
            BigDecimal totalAllocation = req.getAllocations().stream()
                    .map(BudgetUpdateReq.BudgetAllocation::getAllocatedAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            CheckUtils.throwIf(totalAllocation.compareTo(req.getTotalAmount()) > 0, "分配总金额不能超过预算总金额");
        }
    }

    /**
     * 验证审核数据
     */
    private void validateAuditData(AuditSubmitReq req) {
        CheckUtils.throwIfBlank(req.getAuditTitle(), "审核标题不能为空");
        CheckUtils.throwIfBlank(req.getAuditType(), "审核类型不能为空");
        CheckUtils.throwIf(CollUtil.isEmpty(req.getTransactionIds()), "审核交易列表不能为空");
    }

    /**
     * 验证批量审核数据
     */
    private void validateBatchAuditData(BatchAuditReq req) {
        CheckUtils.throwIf(CollUtil.isEmpty(req.getAuditDetails()), "批量审核列表不能为空");
        CheckUtils.throwIfNull(req.getBatchRules(), "批量审核规则不能为空");
    }

    /**
     * 验证预警规则数据
     */
    private void validateAlertRuleData(AlertRuleCreateReq req) {
        CheckUtils.throwIfBlank(req.getRuleName(), "规则名称不能为空");
        CheckUtils.throwIfBlank(req.getAlertType(), "预警类型不能为空");
        CheckUtils.throwIfBlank(req.getAlertLevel(), "预警级别不能为空");
        CheckUtils.throwIfNull(req.getMonitoringScope(), "监控范围不能为空");
        CheckUtils.throwIfNull(req.getTriggerConditions(), "触发条件不能为空");
        CheckUtils.throwIfNull(req.getNotificationSettings(), "通知设置不能为空");
    }

    /**
     * 验证成本分析查询
     */
    private void validateCostAnalysisQuery(CostAnalysisQuery query) {
        CheckUtils.throwIfNull(query.getGroupId(), "群组ID不能为空");
        if (query.getTimeRange() != null) {
            CheckUtils.throwIfBlank(query.getTimeRange().getStartDate(), "开始日期不能为空");
            CheckUtils.throwIfBlank(query.getTimeRange().getEndDate(), "结束日期不能为空");
        }
    }

    /**
     * 检查预算是否存在
     */
    private boolean isBudgetExists(Long groupId, String budgetName, String budgetPeriod, Long excludeId) {
        return financialManagementMapper.isBudgetExists(groupId, budgetName, budgetPeriod, excludeId);
    }

    /**
     * 检查预警规则名称是否存在
     */
    private boolean isAlertRuleNameExists(Long groupId, String ruleName, Long excludeId) {
        return financialManagementMapper.isAlertRuleNameExists(groupId, ruleName, excludeId);
    }

    /**
     * 检查预警条件
     */
    private boolean checkAlertCondition(AlertRuleCreateReq rule) {
        // 根据预警类型检查条件
        return switch (rule.getAlertType()) {
            case "BUDGET_THRESHOLD" -> this.checkBudgetThreshold(rule);
            case "AMOUNT_LIMIT" -> this.checkAmountLimit(rule);
            case "FREQUENCY_LIMIT" -> this.checkFrequencyLimit(rule);
            case "ANOMALY_DETECTION" -> this.checkAnomalyDetection(rule);
            case "TREND_ANALYSIS" -> this.checkTrendAnalysis(rule);
            default -> false;
        };
    }

    /**
     * 检查预算阈值
     */
    private boolean checkBudgetThreshold(AlertRuleCreateReq rule) {
        // 实现预算阈值检查逻辑
        return financialManagementMapper.checkBudgetThreshold(rule);
    }

    /**
     * 检查金额限制
     */
    private boolean checkAmountLimit(AlertRuleCreateReq rule) {
        // 实现金额限制检查逻辑
        return financialManagementMapper.checkAmountLimit(rule);
    }

    /**
     * 检查频率限制
     */
    private boolean checkFrequencyLimit(AlertRuleCreateReq rule) {
        // 实现频率限制检查逻辑
        return financialManagementMapper.checkFrequencyLimit(rule);
    }

    /**
     * 检查异常检测
     */
    private boolean checkAnomalyDetection(AlertRuleCreateReq rule) {
        // 实现异常检测逻辑
        return financialManagementMapper.checkAnomalyDetection(rule);
    }

    /**
     * 检查趋势分析
     */
    private boolean checkTrendAnalysis(AlertRuleCreateReq rule) {
        // 实现趋势分析逻辑
        return financialManagementMapper.checkTrendAnalysis(rule);
    }

    /**
     * 发送预警通知
     */
    private void sendAlertNotification(AlertRuleCreateReq rule) {
        // 实现预警通知发送逻辑
        log.info("发送预警通知，规则: {}", rule.getRuleName());
    }
}
