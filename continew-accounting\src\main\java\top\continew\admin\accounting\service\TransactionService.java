package top.continew.admin.accounting.service;

import top.continew.admin.common.base.service.BaseService;
import top.continew.admin.accounting.model.entity.TransactionDO;
import top.continew.admin.accounting.model.query.TransactionQuery;
import top.continew.admin.accounting.model.req.TransactionCreateReq;
import top.continew.admin.accounting.model.req.TransactionUpdateReq;
import top.continew.admin.accounting.model.resp.TransactionDetailResp;
import top.continew.admin.accounting.model.resp.TransactionListResp;
import top.continew.admin.accounting.model.resp.TransactionStatisticsResp;
import top.continew.starter.data.service.IService;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 账单管理业务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface TransactionService extends BaseService<TransactionListResp, TransactionDetailResp, TransactionQuery, TransactionCreateReq>, IService<TransactionDO> {

    /**
     * 更新账单信息
     *
     * @param req 更新请求
     * @param id  账单ID
     */
    void update(TransactionUpdateReq req, Long id);

    /**
     * 获取群组账单统计
     *
     * @param groupId   群组ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 统计信息
     */
    TransactionStatisticsResp getStatistics(Long groupId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取用户在群组中的账单列表
     *
     * @param groupId 群组ID
     * @param userId  用户ID
     * @return 账单列表
     */
    List<TransactionListResp> getUserTransactions(Long groupId, Long userId);

    /**
     * 计算用户在群组中的余额
     *
     * @param groupId  群组ID
     * @param userId   用户ID
     * @param currency 币种
     * @return 余额
     */
    BigDecimal calculateUserBalance(Long groupId, Long userId, String currency);

    /**
     * 删除账单
     *
     * @param id     账单ID
     * @param userId 操作用户ID
     */
    void deleteTransaction(Long id, Long userId);

    /**
     * 批量导入账单
     *
     * @param groupId      群组ID
     * @param transactions 账单列表
     * @param userId       操作用户ID
     * @return 导入结果
     */
    ImportResult batchImport(Long groupId, List<TransactionCreateReq> transactions, Long userId);

    /**
     * 导入结果
     */
    class ImportResult {
        private int totalCount;
        private int successCount;
        private int failureCount;
        private List<String> errors;

        public int getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(int totalCount) {
            this.totalCount = totalCount;
        }

        public int getSuccessCount() {
            return successCount;
        }

        public void setSuccessCount(int successCount) {
            this.successCount = successCount;
        }

        public int getFailureCount() {
            return failureCount;
        }

        public void setFailureCount(int failureCount) {
            this.failureCount = failureCount;
        }

        public List<String> getErrors() {
            return errors;
        }

        public void setErrors(List<String> errors) {
            this.errors = errors;
        }
    }
}
