package top.continew.admin.accounting.interceptor;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import top.continew.admin.accounting.annotation.UsageLimit;
import top.continew.admin.accounting.service.UsageTrackingService;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.security.util.SecurityUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;

/**
 * 使用限制拦截器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UsageLimitInterceptor implements HandlerInterceptor {

    private final UsageTrackingService usageTrackingService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        try {
            // 检查是否有使用限制注解
            UsageLimit usageLimit = getUsageLimitAnnotation(handler);
            if (usageLimit == null) {
                return true;
            }

            // 获取当前用户的群组ID
            Long groupId = getCurrentGroupId(request);
            if (groupId == null) {
                log.debug("无法获取群组ID，跳过使用限制检查");
                return true;
            }

            // 检查使用限制
            String limitType = usageLimit.limitType();
            if (StrUtil.isNotBlank(limitType)) {
                boolean exceeded = usageTrackingService.isLimitExceeded(groupId, limitType);
                if (exceeded) {
                    String message = StrUtil.isNotBlank(usageLimit.message()) 
                            ? usageLimit.message() 
                            : "已达到使用限制，请升级订阅套餐";
                    
                    log.warn("使用限制检查失败: groupId={}, limitType={}, message={}", 
                            groupId, limitType, message);
                    
                    // 返回JSON错误响应
                    response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                    response.setContentType("application/json;charset=UTF-8");
                    
                    ErrorResponse errorResponse = new ErrorResponse();
                    errorResponse.setCode("USAGE_LIMIT_EXCEEDED");
                    errorResponse.setMessage(message);
                    errorResponse.setLimitType(limitType);
                    errorResponse.setCurrentUsage(usageTrackingService.getCurrentUsage(groupId, limitType));
                    errorResponse.setLimit(usageTrackingService.getLimit(groupId, limitType));
                    
                    response.getWriter().write(JSON.toJSONString(errorResponse));
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            log.error("使用限制检查异常: {}", e.getMessage(), e);
            // 异常时允许通过，避免影响正常业务
            return true;
        }
    }

    /**
     * 获取使用限制注解
     */
    private UsageLimit getUsageLimitAnnotation(Object handler) {
        try {
            if (handler instanceof org.springframework.web.method.HandlerMethod) {
                org.springframework.web.method.HandlerMethod handlerMethod = 
                        (org.springframework.web.method.HandlerMethod) handler;
                
                Method method = handlerMethod.getMethod();
                
                // 先检查方法上的注解
                UsageLimit usageLimit = method.getAnnotation(UsageLimit.class);
                if (usageLimit != null) {
                    return usageLimit;
                }
                
                // 再检查类上的注解
                return handlerMethod.getBeanType().getAnnotation(UsageLimit.class);
            }
        } catch (Exception e) {
            log.debug("获取使用限制注解失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取当前请求的群组ID
     */
    private Long getCurrentGroupId(HttpServletRequest request) {
        try {
            // 1. 从路径参数中获取
            String pathInfo = request.getPathInfo();
            if (StrUtil.isNotBlank(pathInfo)) {
                String[] pathParts = pathInfo.split("/");
                for (int i = 0; i < pathParts.length - 1; i++) {
                    if ("group".equals(pathParts[i]) || "groups".equals(pathParts[i])) {
                        try {
                            return Long.parseLong(pathParts[i + 1]);
                        } catch (NumberFormatException e) {
                            log.debug("路径参数中的群组ID格式错误: {}", pathParts[i + 1]);
                        }
                    }
                }
            }

            // 2. 从请求参数中获取
            String groupIdParam = request.getParameter("groupId");
            if (StrUtil.isNotBlank(groupIdParam)) {
                try {
                    return Long.parseLong(groupIdParam);
                } catch (NumberFormatException e) {
                    log.debug("请求参数中的群组ID格式错误: {}", groupIdParam);
                }
            }

            // 3. 从请求头中获取
            String groupIdHeader = request.getHeader("X-Group-Id");
            if (StrUtil.isNotBlank(groupIdHeader)) {
                try {
                    return Long.parseLong(groupIdHeader);
                } catch (NumberFormatException e) {
                    log.debug("请求头中的群组ID格式错误: {}", groupIdHeader);
                }
            }

            // 4. 从用户会话中获取当前选中的群组
            Long userId = SecurityUtils.getUserId();
            if (userId != null) {
                // TODO: 从用户会话或缓存中获取当前选中的群组ID
                // 这里需要根据实际的业务逻辑来实现
                return getCurrentUserSelectedGroupId(userId);
            }

        } catch (Exception e) {
            log.debug("获取群组ID失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取用户当前选中的群组ID
     */
    private Long getCurrentUserSelectedGroupId(Long userId) {
        // TODO: 实现获取用户当前选中群组的逻辑
        // 可以从Redis缓存、数据库或用户会话中获取
        return null;
    }

    /**
     * 错误响应
     */
    public static class ErrorResponse {
        private String code;
        private String message;
        private String limitType;
        private Integer currentUsage;
        private Integer limit;

        // getters and setters
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public String getLimitType() { return limitType; }
        public void setLimitType(String limitType) { this.limitType = limitType; }
        public Integer getCurrentUsage() { return currentUsage; }
        public void setCurrentUsage(Integer currentUsage) { this.currentUsage = currentUsage; }
        public Integer getLimit() { return limit; }
        public void setLimit(Integer limit) { this.limit = limit; }
    }
}
