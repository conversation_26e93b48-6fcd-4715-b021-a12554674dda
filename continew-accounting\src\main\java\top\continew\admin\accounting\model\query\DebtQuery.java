package top.continew.admin.accounting.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import top.continew.starter.data.mybatis.query.annotation.Query;
import top.continew.starter.data.mybatis.query.enums.QueryType;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 债务查询条件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "债务查询条件")
public class DebtQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    @Query(type = QueryType.EQ)
    private Long groupId;

    /**
     * 债权人ID
     */
    @Schema(description = "债权人ID", example = "1")
    @Query(type = QueryType.EQ)
    private Long creditorId;

    /**
     * 债务人ID
     */
    @Schema(description = "债务人ID", example = "2")
    @Query(type = QueryType.EQ)
    private Long debtorId;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "ACTIVE")
    @Query(type = QueryType.EQ)
    private String status;

    /**
     * 状态列表
     */
    @Schema(description = "状态列表")
    @Query(type = QueryType.IN, column = "status")
    private List<String> statusList;

    /**
     * 币种
     */
    @Schema(description = "币种", example = "CNY")
    @Query(type = QueryType.EQ)
    private String currency;

    /**
     * 债务类型
     */
    @Schema(description = "债务类型", example = "PERSONAL")
    @Query(type = QueryType.EQ)
    private String debtType;

    /**
     * 最小金额
     */
    @Schema(description = "最小金额", example = "100.00")
    @Query(type = QueryType.GE, column = "amount")
    private BigDecimal minAmount;

    /**
     * 最大金额
     */
    @Schema(description = "最大金额", example = "10000.00")
    @Query(type = QueryType.LE, column = "amount")
    private BigDecimal maxAmount;

    /**
     * 创建开始时间
     */
    @Schema(description = "创建开始时间", example = "2025-01-01 00:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Query(type = QueryType.GE, column = "create_time")
    private LocalDateTime createTimeStart;

    /**
     * 创建结束时间
     */
    @Schema(description = "创建结束时间", example = "2025-01-31 23:59:59")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Query(type = QueryType.LE, column = "create_time")
    private LocalDateTime createTimeEnd;

    /**
     * 到期开始时间
     */
    @Schema(description = "到期开始时间", example = "2025-01-01 00:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Query(type = QueryType.GE, column = "due_date")
    private LocalDateTime dueDateStart;

    /**
     * 到期结束时间
     */
    @Schema(description = "到期结束时间", example = "2025-01-31 23:59:59")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Query(type = QueryType.LE, column = "due_date")
    private LocalDateTime dueDateEnd;

    /**
     * 关键词搜索
     */
    @Schema(description = "关键词搜索", example = "借款")
    @Query(type = QueryType.LIKE, column = "description")
    private String keyword;

    /**
     * 是否逾期
     */
    @Schema(description = "是否逾期", example = "true")
    private Boolean overdue;

    /**
     * 是否即将到期（7天内）
     */
    @Schema(description = "是否即将到期", example = "true")
    private Boolean dueSoon;

    /**
     * 用户ID（查询与该用户相关的债务）
     */
    @Schema(description = "用户ID", example = "1")
    private Long userId;
}
