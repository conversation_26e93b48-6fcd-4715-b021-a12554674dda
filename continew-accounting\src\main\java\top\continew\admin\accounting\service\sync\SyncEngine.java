package top.continew.admin.accounting.service.sync;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import top.continew.admin.accounting.model.entity.DataSyncConfigDO;
import top.continew.admin.accounting.model.entity.DataSyncLogDO;
import top.continew.admin.accounting.service.DataSyncService;
import top.continew.starter.core.exception.BusinessException;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 同步执行引擎
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SyncEngine {

    private final DataSourceAdapterFactory adapterFactory;
    private final DataSyncService dataSyncService;
    private final ConflictResolver conflictResolver;
    private final IncrementalSyncManager incrementalSyncManager;
    private final DataTransformEngine dataTransformEngine;
    
    /**
     * 同步进度缓存
     */
    private final Map<String, SyncProgress> syncProgressMap = new ConcurrentHashMap<>();

    /**
     * 执行同步任务
     *
     * @param configId 配置ID
     * @param syncType 同步类型
     * @param isAsync 是否异步执行
     * @return 同步结果
     */
    public Map<String, Object> executeSync(Long configId, String syncType, boolean isAsync) {
        String syncId = generateSyncId(configId);
        
        try {
            // 获取同步配置
            DataSyncConfigDO config = dataSyncService.getConfigById(configId);
            if (config == null) {
                throw new BusinessException("同步配置不存在: " + configId);
            }
            
            // 检查配置状态
            if (!"ACTIVE".equals(config.getStatus())) {
                throw new BusinessException("同步配置未激活: " + configId);
            }
            
            // 初始化同步进度
            SyncProgress progress = new SyncProgress(syncId, configId);
            syncProgressMap.put(syncId, progress);
            
            if (isAsync) {
                // 异步执行
                CompletableFuture.runAsync(() -> doExecuteSync(config, syncType, progress));
                
                Map<String, Object> result = new HashMap<>();
                result.put("syncId", syncId);
                result.put("async", true);
                result.put("message", "同步任务已启动");
                return result;
            } else {
                // 同步执行
                return doExecuteSync(config, syncType, progress);
            }
            
        } catch (Exception e) {
            log.error("执行同步任务失败: configId={}, error={}", configId, e.getMessage(), e);
            
            // 更新进度状态
            SyncProgress progress = syncProgressMap.get(syncId);
            if (progress != null) {
                progress.setStatus("FAILED");
                progress.setErrorMessage(e.getMessage());
                progress.setEndTime(LocalDateTime.now());
            }
            
            throw new BusinessException("执行同步任务失败: " + e.getMessage());
        }
    }

    /**
     * 执行同步任务的核心逻辑
     */
    private Map<String, Object> doExecuteSync(DataSyncConfigDO config, String syncType, SyncProgress progress) {
        String syncId = progress.getSyncId();
        Long configId = config.getId();
        
        try {
            log.info("开始执行同步任务: configId={}, syncId={}, syncType={}", configId, syncId, syncType);
            
            // 更新进度状态
            progress.setStatus("RUNNING");
            progress.setStartTime(LocalDateTime.now());
            
            // 创建同步日志
            DataSyncLogDO syncLog = createSyncLog(config, syncId, syncType);
            
            // 获取源和目标适配器
            DataSourceAdapter sourceAdapter = adapterFactory.getAdapter(config.getSourceType());
            DataSourceAdapter targetAdapter = adapterFactory.getAdapter(config.getTargetType());
            
            // 解析配置
            Map<String, Object> sourceConfig = parseConfig(config.getSourceConfigJson());
            Map<String, Object> targetConfig = parseConfig(config.getTargetConfigJson());
            Map<String, Object> fieldMapping = parseConfig(config.getFieldMappingJson());
            Map<String, Object> transformRules = parseConfig(config.getTransformRulesJson());
            
            // 执行同步
            Map<String, Object> syncResult;
            switch (syncType) {
                case "FULL":
                    syncResult = executeFullSync(sourceAdapter, targetAdapter, sourceConfig, targetConfig, fieldMapping, transformRules, progress);
                    break;
                case "INCREMENTAL":
                    syncResult = executeIncrementalSync(config, sourceAdapter, targetAdapter, sourceConfig, targetConfig, fieldMapping, transformRules, progress);
                    break;
                case "BIDIRECTIONAL":
                    syncResult = executeBidirectionalSync(sourceAdapter, targetAdapter, sourceConfig, targetConfig, fieldMapping, transformRules, progress);
                    break;
                default:
                    throw new BusinessException("不支持的同步类型: " + syncType);
            }
            
            // 更新同步日志
            updateSyncLog(syncLog, syncResult, progress);
            
            // 更新配置统计信息
            updateConfigStatistics(config, syncResult);
            
            // 更新进度状态
            progress.setStatus("COMPLETED");
            progress.setEndTime(LocalDateTime.now());
            progress.setResult(syncResult);
            
            log.info("同步任务执行完成: configId={}, syncId={}, result={}", configId, syncId, syncResult);
            
            Map<String, Object> result = new HashMap<>();
            result.put("syncId", syncId);
            result.put("success", true);
            result.put("syncResult", syncResult);
            result.put("duration", progress.getDuration());
            
            return result;
            
        } catch (Exception e) {
            log.error("同步任务执行失败: configId={}, syncId={}, error={}", configId, syncId, e.getMessage(), e);
            
            // 更新进度状态
            progress.setStatus("FAILED");
            progress.setErrorMessage(e.getMessage());
            progress.setEndTime(LocalDateTime.now());
            
            // 记录错误日志
            DataSyncLogDO errorLog = new DataSyncLogDO();
            errorLog.setConfigId(configId);
            errorLog.setSyncId(syncId);
            errorLog.setSyncType(syncType);
            errorLog.setStatus("FAILED");
            errorLog.setErrorMessage(e.getMessage());
            errorLog.setStartTime(progress.getStartTime());
            errorLog.setEndTime(LocalDateTime.now());
            dataSyncService.createSyncLog(errorLog);
            
            throw new BusinessException("同步任务执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行全量同步
     */
    private Map<String, Object> executeFullSync(DataSourceAdapter sourceAdapter, DataSourceAdapter targetAdapter,
                                                Map<String, Object> sourceConfig, Map<String, Object> targetConfig,
                                                Map<String, Object> fieldMapping, Map<String, Object> transformRules, SyncProgress progress) {
        log.info("执行全量同步: syncId={}", progress.getSyncId());
        
        Map<String, Object> result = new HashMap<>();
        int totalCount = 0;
        int successCount = 0;
        int failedCount = 0;
        List<String> errors = new ArrayList<>();
        
        try {
            // 获取源数据总数
            Long dataCount = sourceAdapter.getDataCount(sourceConfig, null);
            progress.setTotalCount(dataCount.intValue());
            
            // 分批读取和写入数据
            int batchSize = 1000;
            int offset = 0;
            
            while (offset < dataCount) {
                // 读取源数据
                List<Map<String, Object>> sourceData = sourceAdapter.readData(
                        sourceConfig, fieldMapping, null, null, batchSize);

                if (CollUtil.isEmpty(sourceData)) {
                    break;
                }

                totalCount += sourceData.size();

                // 数据转换
                List<Map<String, Object>> transformedData = dataTransformEngine.transformData(
                        sourceData, fieldMapping, transformRules);

                // 写入目标数据源
                Map<String, Object> writeResult = targetAdapter.writeData(
                        targetConfig, fieldMapping, transformedData, "CREATE");
                
                Integer batchSuccessCount = (Integer) writeResult.get("successCount");
                Integer batchFailedCount = (Integer) writeResult.get("failedCount");
                @SuppressWarnings("unchecked")
                List<String> batchErrors = (List<String>) writeResult.get("errors");
                
                successCount += batchSuccessCount != null ? batchSuccessCount : 0;
                failedCount += batchFailedCount != null ? batchFailedCount : 0;
                if (batchErrors != null) {
                    errors.addAll(batchErrors);
                }
                
                // 更新进度
                progress.setProcessedCount(totalCount);
                progress.setSuccessCount(successCount);
                progress.setFailedCount(failedCount);
                
                offset += batchSize;
            }
            
        } catch (Exception e) {
            log.error("全量同步执行失败: {}", e.getMessage(), e);
            errors.add("全量同步执行失败: " + e.getMessage());
        }
        
        result.put("syncType", "FULL");
        result.put("totalCount", totalCount);
        result.put("successCount", successCount);
        result.put("failedCount", failedCount);
        result.put("errors", errors);
        
        return result;
    }

    /**
     * 执行增量同步
     */
    private Map<String, Object> executeIncrementalSync(DataSyncConfigDO config, DataSourceAdapter sourceAdapter, DataSourceAdapter targetAdapter,
                                                       Map<String, Object> sourceConfig, Map<String, Object> targetConfig,
                                                       Map<String, Object> fieldMapping, Map<String, Object> transformRules, SyncProgress progress) {
        log.info("执行增量同步: syncId={}, configId={}", progress.getSyncId(), config.getId());
        
        // 获取上次同步时间
        LocalDateTime lastSyncTime = config.getLastSyncTime();
        
        // 使用增量同步管理器执行同步
        return incrementalSyncManager.executeIncrementalSync(
                config, sourceAdapter, targetAdapter, sourceConfig, targetConfig, fieldMapping, transformRules, lastSyncTime, progress);
    }

    /**
     * 执行双向同步
     */
    private Map<String, Object> executeBidirectionalSync(DataSourceAdapter sourceAdapter, DataSourceAdapter targetAdapter,
                                                         Map<String, Object> sourceConfig, Map<String, Object> targetConfig,
                                                         Map<String, Object> fieldMapping, Map<String, Object> transformRules, SyncProgress progress) {
        log.info("执行双向同步: syncId={}", progress.getSyncId());
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 执行源到目标的同步
            Map<String, Object> sourceToTargetResult = executeFullSync(
                    sourceAdapter, targetAdapter, sourceConfig, targetConfig, fieldMapping, transformRules, progress);

            // 执行目标到源的同步
            Map<String, Object> targetToSourceResult = executeFullSync(
                    targetAdapter, sourceAdapter, targetConfig, sourceConfig, reverseFieldMapping(fieldMapping), transformRules, progress);
            
            // 合并结果
            result.put("syncType", "BIDIRECTIONAL");
            result.put("sourceToTarget", sourceToTargetResult);
            result.put("targetToSource", targetToSourceResult);
            
            // 计算总计数
            int totalCount = (Integer) sourceToTargetResult.get("totalCount") + (Integer) targetToSourceResult.get("totalCount");
            int successCount = (Integer) sourceToTargetResult.get("successCount") + (Integer) targetToSourceResult.get("successCount");
            int failedCount = (Integer) sourceToTargetResult.get("failedCount") + (Integer) targetToSourceResult.get("failedCount");
            
            result.put("totalCount", totalCount);
            result.put("successCount", successCount);
            result.put("failedCount", failedCount);
            
        } catch (Exception e) {
            log.error("双向同步执行失败: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取同步进度
     */
    public Map<String, Object> getSyncProgress(String syncId) {
        SyncProgress progress = syncProgressMap.get(syncId);
        if (progress == null) {
            throw new BusinessException("同步任务不存在: " + syncId);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("syncId", syncId);
        result.put("configId", progress.getConfigId());
        result.put("status", progress.getStatus());
        result.put("totalCount", progress.getTotalCount());
        result.put("processedCount", progress.getProcessedCount());
        result.put("successCount", progress.getSuccessCount());
        result.put("failedCount", progress.getFailedCount());
        result.put("progress", progress.getProgressPercentage());
        result.put("startTime", progress.getStartTime());
        result.put("endTime", progress.getEndTime());
        result.put("duration", progress.getDuration());
        result.put("errorMessage", progress.getErrorMessage());
        
        return result;
    }

    /**
     * 停止同步任务
     */
    public boolean stopSync(String syncId) {
        SyncProgress progress = syncProgressMap.get(syncId);
        if (progress == null) {
            return false;
        }
        
        progress.setStatus("STOPPED");
        progress.setEndTime(LocalDateTime.now());
        
        log.info("同步任务已停止: syncId={}", syncId);
        return true;
    }

    /**
     * 清理同步进度缓存
     */
    public void cleanupSyncProgress(String syncId) {
        syncProgressMap.remove(syncId);
        log.debug("清理同步进度缓存: syncId={}", syncId);
    }

    /**
     * 生成同步ID
     */
    private String generateSyncId(Long configId) {
        return "SYNC_" + configId + "_" + System.currentTimeMillis();
    }

    /**
     * 解析配置JSON
     */
    private Map<String, Object> parseConfig(String configJson) {
        if (StrUtil.isBlank(configJson)) {
            return new HashMap<>();
        }
        
        try {
            return JSONUtil.toBean(configJson, Map.class);
        } catch (Exception e) {
            log.error("解析配置JSON失败: {}", e.getMessage());
            return new HashMap<>();
        }
    }

    /**
     * 反向字段映射
     */
    private Map<String, Object> reverseFieldMapping(Map<String, Object> fieldMapping) {
        if (fieldMapping == null || fieldMapping.isEmpty()) {
            return new HashMap<>();
        }
        
        Map<String, Object> reverseMapping = new HashMap<>();
        for (Map.Entry<String, Object> entry : fieldMapping.entrySet()) {
            reverseMapping.put((String) entry.getValue(), entry.getKey());
        }
        
        return reverseMapping;
    }

    /**
     * 创建同步日志
     */
    private DataSyncLogDO createSyncLog(DataSyncConfigDO config, String syncId, String syncType) {
        DataSyncLogDO syncLog = new DataSyncLogDO();
        syncLog.setConfigId(config.getId());
        syncLog.setSyncId(syncId);
        syncLog.setSyncType(syncType);
        syncLog.setStatus("RUNNING");
        syncLog.setStartTime(LocalDateTime.now());
        
        return dataSyncService.createSyncLog(syncLog);
    }

    /**
     * 更新同步日志
     */
    private void updateSyncLog(DataSyncLogDO syncLog, Map<String, Object> syncResult, SyncProgress progress) {
        syncLog.setStatus("COMPLETED");
        syncLog.setEndTime(progress.getEndTime());
        syncLog.setDuration(progress.getDuration());
        syncLog.setTotalCount((Integer) syncResult.get("totalCount"));
        syncLog.setSuccessCount((Integer) syncResult.get("successCount"));
        syncLog.setFailedCount((Integer) syncResult.get("failedCount"));
        
        @SuppressWarnings("unchecked")
        List<String> errors = (List<String>) syncResult.get("errors");
        if (CollUtil.isNotEmpty(errors)) {
            syncLog.setErrorMessage(String.join("; ", errors));
        }
        
        dataSyncService.updateSyncLog(syncLog.getId(), syncLog);
    }

    /**
     * 更新配置统计信息
     */
    private void updateConfigStatistics(DataSyncConfigDO config, Map<String, Object> syncResult) {
        config.setLastSyncTime(LocalDateTime.now());
        config.setTotalSyncCount(config.getTotalSyncCount() + 1);
        
        Integer successCount = (Integer) syncResult.get("successCount");
        Integer failedCount = (Integer) syncResult.get("failedCount");
        
        if (successCount != null && failedCount != null) {
            if (failedCount == 0) {
                config.setSuccessfulSyncCount(config.getSuccessfulSyncCount() + 1);
            } else {
                config.setFailedSyncCount(config.getFailedSyncCount() + 1);
            }
        }
        
        dataSyncService.updateConfig(config.getId(), config);
    }

    /**
     * 同步进度类
     */
    public static class SyncProgress {
        private String syncId;
        private Long configId;
        private String status = "PENDING";
        private int totalCount = 0;
        private int processedCount = 0;
        private int successCount = 0;
        private int failedCount = 0;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private String errorMessage;
        private Map<String, Object> result;

        public SyncProgress(String syncId, Long configId) {
            this.syncId = syncId;
            this.configId = configId;
        }

        public double getProgressPercentage() {
            if (totalCount == 0) {
                return 0.0;
            }
            return (double) processedCount / totalCount * 100;
        }

        public Long getDuration() {
            if (startTime == null) {
                return 0L;
            }
            LocalDateTime end = endTime != null ? endTime : LocalDateTime.now();
            return java.time.Duration.between(startTime, end).toMillis();
        }

        // Getters and Setters
        public String getSyncId() { return syncId; }
        public void setSyncId(String syncId) { this.syncId = syncId; }
        
        public Long getConfigId() { return configId; }
        public void setConfigId(Long configId) { this.configId = configId; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }
        
        public int getProcessedCount() { return processedCount; }
        public void setProcessedCount(int processedCount) { this.processedCount = processedCount; }
        
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        
        public int getFailedCount() { return failedCount; }
        public void setFailedCount(int failedCount) { this.failedCount = failedCount; }
        
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        
        public LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public Map<String, Object> getResult() { return result; }
        public void setResult(Map<String, Object> result) { this.result = result; }
    }
}
