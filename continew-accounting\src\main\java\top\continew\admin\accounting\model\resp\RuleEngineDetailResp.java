package top.continew.admin.accounting.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 规则引擎详细响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "规则引擎详细响应")
public class RuleEngineDetailResp extends RuleEngineResp {

    /**
     * 触发条件配置
     */
    @Schema(description = "触发条件配置")
    private TriggerConditionDetail triggerCondition;

    /**
     * 执行动作配置
     */
    @Schema(description = "执行动作配置")
    private List<ExecutionActionDetail> executionActions;

    /**
     * 调度配置详情
     */
    @Schema(description = "调度配置详情")
    private ScheduleConfigDetail scheduleConfig;

    /**
     * 通知配置详情
     */
    @Schema(description = "通知配置详情")
    private NotificationConfigDetail notificationConfig;

    /**
     * 版本历史
     */
    @Schema(description = "版本历史")
    private List<VersionHistory> versionHistory;

    /**
     * 执行历史
     */
    @Schema(description = "执行历史")
    private List<ExecutionHistory> executionHistory;

    /**
     * 依赖关系
     */
    @Schema(description = "依赖关系")
    private DependencyInfo dependencyInfo;

    /**
     * 影响分析
     */
    @Schema(description = "影响分析")
    private ImpactAnalysis impactAnalysis;

    /**
     * 配置验证结果
     */
    @Schema(description = "配置验证结果")
    private ValidationResult validationResult;

    /**
     * 触发条件详情
     */
    @Data
    @Schema(description = "触发条件详情")
    public static class TriggerConditionDetail {

        /**
         * 触发类型
         */
        @Schema(description = "触发类型", example = "EVENT")
        private String triggerType;

        /**
         * 事件类型
         */
        @Schema(description = "事件类型", example = "TRANSACTION_CREATED")
        private String eventType;

        /**
         * 条件表达式
         */
        @Schema(description = "条件表达式")
        private List<ConditionExpressionDetail> conditions;

        /**
         * 条件逻辑
         */
        @Schema(description = "条件逻辑", example = "AND")
        private String conditionLogic;

        /**
         * 时间条件
         */
        @Schema(description = "时间条件")
        private TimeConditionDetail timeCondition;

        /**
         * 频率限制
         */
        @Schema(description = "频率限制")
        private FrequencyLimitDetail frequencyLimit;

        /**
         * 条件复杂度
         */
        @Schema(description = "条件复杂度", example = "MEDIUM")
        private String complexity;

        /**
         * 预估匹配率
         */
        @Schema(description = "预估匹配率", example = "0.15")
        private Double estimatedMatchRate;
    }

    /**
     * 条件表达式详情
     */
    @Data
    @Schema(description = "条件表达式详情")
    public static class ConditionExpressionDetail {

        /**
         * 字段名称
         */
        @Schema(description = "字段名称", example = "description")
        private String fieldName;

        /**
         * 字段显示名称
         */
        @Schema(description = "字段显示名称", example = "交易描述")
        private String fieldDisplayName;

        /**
         * 操作符
         */
        @Schema(description = "操作符", example = "CONTAINS")
        private String operator;

        /**
         * 操作符显示名称
         */
        @Schema(description = "操作符显示名称", example = "包含")
        private String operatorDisplayName;

        /**
         * 比较值
         */
        @Schema(description = "比较值", example = "餐饮")
        private Object value;

        /**
         * 数据类型
         */
        @Schema(description = "数据类型", example = "STRING")
        private String dataType;

        /**
         * 是否忽略大小写
         */
        @Schema(description = "是否忽略大小写", example = "true")
        private Boolean ignoreCase;

        /**
         * 正则表达式模式
         */
        @Schema(description = "正则表达式模式")
        private String regexPattern;

        /**
         * 范围条件
         */
        @Schema(description = "范围条件")
        private RangeConditionDetail rangeCondition;

        /**
         * 条件权重
         */
        @Schema(description = "条件权重", example = "1.0")
        private Double weight;

        /**
         * 匹配统计
         */
        @Schema(description = "匹配统计")
        private MatchStatistics matchStatistics;
    }

    /**
     * 范围条件详情
     */
    @Data
    @Schema(description = "范围条件详情")
    public static class RangeConditionDetail {

        /**
         * 最小值
         */
        @Schema(description = "最小值")
        private Object minValue;

        /**
         * 最大值
         */
        @Schema(description = "最大值")
        private Object maxValue;

        /**
         * 是否包含最小值
         */
        @Schema(description = "是否包含最小值", example = "true")
        private Boolean includeMin;

        /**
         * 是否包含最大值
         */
        @Schema(description = "是否包含最大值", example = "true")
        private Boolean includeMax;

        /**
         * 范围类型
         */
        @Schema(description = "范围类型", example = "NUMERIC")
        private String rangeType;
    }

    /**
     * 时间条件详情
     */
    @Data
    @Schema(description = "时间条件详情")
    public static class TimeConditionDetail {

        /**
         * 开始时间
         */
        @Schema(description = "开始时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime startTime;

        /**
         * 结束时间
         */
        @Schema(description = "结束时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime endTime;

        /**
         * 时间范围类型
         */
        @Schema(description = "时间范围类型", example = "DAILY")
        private String timeRangeType;

        /**
         * 星期几
         */
        @Schema(description = "星期几")
        private List<Integer> daysOfWeek;

        /**
         * 小时范围
         */
        @Schema(description = "小时范围")
        private List<Integer> hoursOfDay;

        /**
         * 时区
         */
        @Schema(description = "时区", example = "Asia/Shanghai")
        private String timezone;

        /**
         * 时间条件描述
         */
        @Schema(description = "时间条件描述", example = "工作日9-18点")
        private String description;
    }

    /**
     * 频率限制详情
     */
    @Data
    @Schema(description = "频率限制详情")
    public static class FrequencyLimitDetail {

        /**
         * 最大执行次数
         */
        @Schema(description = "最大执行次数", example = "10")
        private Integer maxExecutions;

        /**
         * 时间窗口（秒）
         */
        @Schema(description = "时间窗口（秒）", example = "3600")
        private Integer timeWindowSeconds;

        /**
         * 冷却时间（秒）
         */
        @Schema(description = "冷却时间（秒）", example = "300")
        private Integer cooldownSeconds;

        /**
         * 是否启用去重
         */
        @Schema(description = "是否启用去重", example = "true")
        private Boolean enableDeduplication;

        /**
         * 去重字段
         */
        @Schema(description = "去重字段")
        private List<String> deduplicationFields;

        /**
         * 当前窗口执行次数
         */
        @Schema(description = "当前窗口执行次数", example = "3")
        private Integer currentWindowExecutions;

        /**
         * 下次可执行时间
         */
        @Schema(description = "下次可执行时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime nextAvailableTime;
    }

    /**
     * 执行动作详情
     */
    @Data
    @Schema(description = "执行动作详情")
    public static class ExecutionActionDetail {

        /**
         * 动作类型
         */
        @Schema(description = "动作类型", example = "SET_CATEGORY")
        private String actionType;

        /**
         * 动作显示名称
         */
        @Schema(description = "动作显示名称", example = "设置分类")
        private String actionDisplayName;

        /**
         * 动作参数
         */
        @Schema(description = "动作参数")
        private Map<String, Object> actionParams;

        /**
         * 执行顺序
         */
        @Schema(description = "执行顺序", example = "1")
        private Integer executionOrder;

        /**
         * 是否异步执行
         */
        @Schema(description = "是否异步执行", example = "false")
        private Boolean asyncExecution;

        /**
         * 重试配置
         */
        @Schema(description = "重试配置")
        private RetryConfigDetail retryConfig;

        /**
         * 条件执行
         */
        @Schema(description = "条件执行")
        private ConditionalExecutionDetail conditionalExecution;

        /**
         * 执行统计
         */
        @Schema(description = "执行统计")
        private ActionExecutionStats executionStats;

        /**
         * 动作描述
         */
        @Schema(description = "动作描述", example = "将交易分类设置为餐饮")
        private String description;
    }

    /**
     * 重试配置详情
     */
    @Data
    @Schema(description = "重试配置详情")
    public static class RetryConfigDetail {

        /**
         * 最大重试次数
         */
        @Schema(description = "最大重试次数", example = "3")
        private Integer maxRetries;

        /**
         * 重试间隔（秒）
         */
        @Schema(description = "重试间隔（秒）", example = "60")
        private Integer retryIntervalSeconds;

        /**
         * 重试策略
         */
        @Schema(description = "重试策略", example = "EXPONENTIAL_BACKOFF")
        private String retryStrategy;

        /**
         * 最大重试间隔（秒）
         */
        @Schema(description = "最大重试间隔（秒）", example = "3600")
        private Integer maxRetryIntervalSeconds;

        /**
         * 当前重试次数
         */
        @Schema(description = "当前重试次数", example = "0")
        private Integer currentRetries;

        /**
         * 下次重试时间
         */
        @Schema(description = "下次重试时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime nextRetryTime;
    }

    /**
     * 条件执行详情
     */
    @Data
    @Schema(description = "条件执行详情")
    public static class ConditionalExecutionDetail {

        /**
         * 执行条件
         */
        @Schema(description = "执行条件")
        private List<ConditionExpressionDetail> conditions;

        /**
         * 条件逻辑
         */
        @Schema(description = "条件逻辑", example = "AND")
        private String conditionLogic;

        /**
         * 失败时是否继续
         */
        @Schema(description = "失败时是否继续", example = "true")
        private Boolean continueOnFailure;

        /**
         * 条件匹配率
         */
        @Schema(description = "条件匹配率", example = "0.8")
        private Double conditionMatchRate;
    }

    /**
     * 调度配置详情
     */
    @Data
    @Schema(description = "调度配置详情")
    public static class ScheduleConfigDetail {

        /**
         * 调度类型
         */
        @Schema(description = "调度类型", example = "CRON")
        private String scheduleType;

        /**
         * Cron表达式
         */
        @Schema(description = "Cron表达式", example = "0 0 * * * ?")
        private String cronExpression;

        /**
         * Cron表达式描述
         */
        @Schema(description = "Cron表达式描述", example = "每小时执行一次")
        private String cronDescription;

        /**
         * 固定间隔（秒）
         */
        @Schema(description = "固定间隔（秒）", example = "3600")
        private Integer fixedIntervalSeconds;

        /**
         * 初始延迟（秒）
         */
        @Schema(description = "初始延迟（秒）", example = "0")
        private Integer initialDelaySeconds;

        /**
         * 是否启用
         */
        @Schema(description = "是否启用", example = "true")
        private Boolean enabled;

        /**
         * 开始时间
         */
        @Schema(description = "开始时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime startTime;

        /**
         * 结束时间
         */
        @Schema(description = "结束时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime endTime;

        /**
         * 时区
         */
        @Schema(description = "时区", example = "Asia/Shanghai")
        private String timezone;

        /**
         * 调度状态
         */
        @Schema(description = "调度状态", example = "RUNNING")
        private String scheduleStatus;

        /**
         * 下次执行时间列表
         */
        @Schema(description = "下次执行时间列表")
        private List<LocalDateTime> nextExecutionTimes;
    }

    /**
     * 通知配置详情
     */
    @Data
    @Schema(description = "通知配置详情")
    public static class NotificationConfigDetail {

        /**
         * 是否启用通知
         */
        @Schema(description = "是否启用通知", example = "true")
        private Boolean enabled;

        /**
         * 通知渠道
         */
        @Schema(description = "通知渠道")
        private List<String> channels;

        /**
         * 通知模板
         */
        @Schema(description = "通知模板")
        private String template;

        /**
         * 通知接收人
         */
        @Schema(description = "通知接收人")
        private List<NotificationRecipient> recipients;

        /**
         * 通知条件
         */
        @Schema(description = "通知条件", example = "ON_FAILURE")
        private String notificationCondition;

        /**
         * 通知频率限制
         */
        @Schema(description = "通知频率限制")
        private FrequencyLimitDetail frequencyLimit;

        /**
         * 通知统计
         */
        @Schema(description = "通知统计")
        private NotificationStats notificationStats;
    }

    /**
     * 通知接收人
     */
    @Data
    @Schema(description = "通知接收人")
    public static class NotificationRecipient {

        /**
         * 用户ID
         */
        @Schema(description = "用户ID", example = "1")
        private Long userId;

        /**
         * 用户姓名
         */
        @Schema(description = "用户姓名", example = "张三")
        private String userName;

        /**
         * 通知渠道
         */
        @Schema(description = "通知渠道")
        private List<String> channels;

        /**
         * 是否启用
         */
        @Schema(description = "是否启用", example = "true")
        private Boolean enabled;
    }

    /**
     * 匹配统计
     */
    @Data
    @Schema(description = "匹配统计")
    public static class MatchStatistics {

        /**
         * 匹配次数
         */
        @Schema(description = "匹配次数", example = "100")
        private Integer matchCount;

        /**
         * 总检查次数
         */
        @Schema(description = "总检查次数", example = "1000")
        private Integer totalChecks;

        /**
         * 匹配率
         */
        @Schema(description = "匹配率", example = "0.1")
        private Double matchRate;

        /**
         * 最后匹配时间
         */
        @Schema(description = "最后匹配时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastMatchTime;
    }

    /**
     * 动作执行统计
     */
    @Data
    @Schema(description = "动作执行统计")
    public static class ActionExecutionStats {

        /**
         * 执行次数
         */
        @Schema(description = "执行次数", example = "50")
        private Integer executionCount;

        /**
         * 成功次数
         */
        @Schema(description = "成功次数", example = "48")
        private Integer successCount;

        /**
         * 失败次数
         */
        @Schema(description = "失败次数", example = "2")
        private Integer failureCount;

        /**
         * 成功率
         */
        @Schema(description = "成功率", example = "0.96")
        private Double successRate;

        /**
         * 平均执行时间（毫秒）
         */
        @Schema(description = "平均执行时间（毫秒）", example = "120")
        private Long avgExecutionTime;

        /**
         * 最后执行时间
         */
        @Schema(description = "最后执行时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastExecutionTime;

        /**
         * 最后执行状态
         */
        @Schema(description = "最后执行状态", example = "SUCCESS")
        private String lastExecutionStatus;
    }

    /**
     * 通知统计
     */
    @Data
    @Schema(description = "通知统计")
    public static class NotificationStats {

        /**
         * 发送次数
         */
        @Schema(description = "发送次数", example = "10")
        private Integer sentCount;

        /**
         * 成功次数
         */
        @Schema(description = "成功次数", example = "9")
        private Integer successCount;

        /**
         * 失败次数
         */
        @Schema(description = "失败次数", example = "1")
        private Integer failureCount;

        /**
         * 成功率
         */
        @Schema(description = "成功率", example = "0.9")
        private Double successRate;

        /**
         * 最后发送时间
         */
        @Schema(description = "最后发送时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastSentTime;
    }

    /**
     * 版本历史
     */
    @Data
    @Schema(description = "版本历史")
    public static class VersionHistory {

        /**
         * 版本号
         */
        @Schema(description = "版本号", example = "1.0")
        private String version;

        /**
         * 版本说明
         */
        @Schema(description = "版本说明", example = "初始版本")
        private String versionNote;

        /**
         * 更新内容
         */
        @Schema(description = "更新内容", example = "创建规则")
        private String updateContent;

        /**
         * 变更类型
         */
        @Schema(description = "变更类型", example = "CREATE")
        private String changeType;

        /**
         * 更新人ID
         */
        @Schema(description = "更新人ID", example = "1")
        private Long updatedBy;

        /**
         * 更新人姓名
         */
        @Schema(description = "更新人姓名", example = "张三")
        private String updatedByName;

        /**
         * 创建时间
         */
        @Schema(description = "创建时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createTime;
    }

    /**
     * 执行历史
     */
    @Data
    @Schema(description = "执行历史")
    public static class ExecutionHistory {

        /**
         * 执行ID
         */
        @Schema(description = "执行ID", example = "1")
        private Long executionId;

        /**
         * 执行状态
         */
        @Schema(description = "执行状态", example = "SUCCESS")
        private String executionStatus;

        /**
         * 执行结果
         */
        @Schema(description = "执行结果", example = "成功处理1条记录")
        private String executionResult;

        /**
         * 执行时间（毫秒）
         */
        @Schema(description = "执行时间（毫秒）", example = "150")
        private Long executionTime;

        /**
         * 处理记录数
         */
        @Schema(description = "处理记录数", example = "1")
        private Integer processedRecords;

        /**
         * 错误信息
         */
        @Schema(description = "错误信息")
        private String errorMessage;

        /**
         * 触发方式
         */
        @Schema(description = "触发方式", example = "EVENT")
        private String triggerType;

        /**
         * 开始时间
         */
        @Schema(description = "开始时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime startTime;

        /**
         * 结束时间
         */
        @Schema(description = "结束时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime endTime;
    }

    /**
     * 依赖关系
     */
    @Data
    @Schema(description = "依赖关系")
    public static class DependencyInfo {

        /**
         * 依赖的规则
         */
        @Schema(description = "依赖的规则")
        private List<RuleDependency> dependencies;

        /**
         * 被依赖的规则
         */
        @Schema(description = "被依赖的规则")
        private List<RuleDependency> dependents;

        /**
         * 冲突的规则
         */
        @Schema(description = "冲突的规则")
        private List<RuleConflict> conflicts;
    }

    /**
     * 规则依赖
     */
    @Data
    @Schema(description = "规则依赖")
    public static class RuleDependency {

        /**
         * 规则ID
         */
        @Schema(description = "规则ID", example = "2")
        private Long ruleId;

        /**
         * 规则名称
         */
        @Schema(description = "规则名称", example = "前置规则")
        private String ruleName;

        /**
         * 依赖类型
         */
        @Schema(description = "依赖类型", example = "PREREQUISITE")
        private String dependencyType;

        /**
         * 依赖描述
         */
        @Schema(description = "依赖描述", example = "需要先执行前置规则")
        private String description;
    }

    /**
     * 规则冲突
     */
    @Data
    @Schema(description = "规则冲突")
    public static class RuleConflict {

        /**
         * 规则ID
         */
        @Schema(description = "规则ID", example = "3")
        private Long ruleId;

        /**
         * 规则名称
         */
        @Schema(description = "规则名称", example = "冲突规则")
        private String ruleName;

        /**
         * 冲突类型
         */
        @Schema(description = "冲突类型", example = "PRIORITY")
        private String conflictType;

        /**
         * 冲突描述
         */
        @Schema(description = "冲突描述", example = "优先级冲突")
        private String description;

        /**
         * 严重程度
         */
        @Schema(description = "严重程度", example = "HIGH")
        private String severity;
    }

    /**
     * 影响分析
     */
    @Data
    @Schema(description = "影响分析")
    public static class ImpactAnalysis {

        /**
         * 影响范围
         */
        @Schema(description = "影响范围", example = "GROUP")
        private String impactScope;

        /**
         * 预估影响记录数
         */
        @Schema(description = "预估影响记录数", example = "1000")
        private Integer estimatedAffectedRecords;

        /**
         * 影响的数据类型
         */
        @Schema(description = "影响的数据类型")
        private List<String> affectedDataTypes;

        /**
         * 风险等级
         */
        @Schema(description = "风险等级", example = "MEDIUM")
        private String riskLevel;

        /**
         * 风险描述
         */
        @Schema(description = "风险描述", example = "可能影响现有分类")
        private String riskDescription;

        /**
         * 建议措施
         */
        @Schema(description = "建议措施")
        private List<String> recommendations;
    }

    /**
     * 配置验证结果
     */
    @Data
    @Schema(description = "配置验证结果")
    public static class ValidationResult {

        /**
         * 是否有效
         */
        @Schema(description = "是否有效", example = "true")
        private Boolean isValid;

        /**
         * 验证错误
         */
        @Schema(description = "验证错误")
        private List<ValidationError> errors;

        /**
         * 验证警告
         */
        @Schema(description = "验证警告")
        private List<ValidationWarning> warnings;

        /**
         * 验证时间
         */
        @Schema(description = "验证时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime validationTime;
    }

    /**
     * 验证错误
     */
    @Data
    @Schema(description = "验证错误")
    public static class ValidationError {

        /**
         * 错误代码
         */
        @Schema(description = "错误代码", example = "INVALID_CONDITION")
        private String errorCode;

        /**
         * 错误信息
         */
        @Schema(description = "错误信息", example = "条件表达式无效")
        private String errorMessage;

        /**
         * 错误字段
         */
        @Schema(description = "错误字段", example = "triggerCondition.conditions[0].value")
        private String errorField;

        /**
         * 严重程度
         */
        @Schema(description = "严重程度", example = "ERROR")
        private String severity;
    }

    /**
     * 验证警告
     */
    @Data
    @Schema(description = "验证警告")
    public static class ValidationWarning {

        /**
         * 警告代码
         */
        @Schema(description = "警告代码", example = "LOW_MATCH_RATE")
        private String warningCode;

        /**
         * 警告信息
         */
        @Schema(description = "警告信息", example = "条件匹配率较低")
        private String warningMessage;

        /**
         * 警告字段
         */
        @Schema(description = "警告字段", example = "triggerCondition")
        private String warningField;

        /**
         * 建议
         */
        @Schema(description = "建议", example = "建议调整条件参数")
        private String suggestion;
    }
}
