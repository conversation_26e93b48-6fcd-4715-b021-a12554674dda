package top.continew.admin.accounting.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * OCR收据识别响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "OCR收据识别响应")
public class OcrReceiptResp {

    /**
     * 识别任务ID
     */
    @Schema(description = "识别任务ID", example = "ocr_20250101_001")
    private String taskId;

    /**
     * 识别状态
     */
    @Schema(description = "识别状态", example = "SUCCESS", allowableValues = {"PROCESSING", "SUCCESS", "FAILED", "TIMEOUT"})
    private String status;

    /**
     * 识别结果
     */
    @Schema(description = "识别结果")
    private ReceiptInfo receiptInfo;

    /**
     * 原始OCR结果
     */
    @Schema(description = "原始OCR结果")
    private OcrRawResult rawResult;

    /**
     * 处理时间（毫秒）
     */
    @Schema(description = "处理时间（毫秒）", example = "1500")
    private Long processingTime;

    /**
     * 识别置信度
     */
    @Schema(description = "识别置信度", example = "0.95")
    private Double confidence;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 错误代码
     */
    @Schema(description = "错误代码")
    private String errorCode;

    /**
     * 是否自动创建了账单
     */
    @Schema(description = "是否自动创建了账单", example = "true")
    private Boolean transactionCreated;

    /**
     * 创建的账单ID
     */
    @Schema(description = "创建的账单ID", example = "123")
    private Long transactionId;

    /**
     * 图片信息
     */
    @Schema(description = "图片信息")
    private ImageInfo imageInfo;

    /**
     * 识别时间
     */
    @Schema(description = "识别时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime recognitionTime;

    /**
     * 使用的OCR引擎
     */
    @Schema(description = "使用的OCR引擎", example = "BAIDU")
    private String ocrEngine;

    /**
     * 收据信息
     */
    @Data
    @Schema(description = "收据信息")
    public static class ReceiptInfo {

        /**
         * 商家名称
         */
        @Schema(description = "商家名称", example = "星巴克咖啡")
        private String merchantName;

        /**
         * 商家地址
         */
        @Schema(description = "商家地址", example = "北京市朝阳区建国门外大街1号")
        private String merchantAddress;

        /**
         * 商家电话
         */
        @Schema(description = "商家电话", example = "010-12345678")
        private String merchantPhone;

        /**
         * 交易时间
         */
        @Schema(description = "交易时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime transactionTime;

        /**
         * 总金额
         */
        @Schema(description = "总金额", example = "58.50")
        private BigDecimal totalAmount;

        /**
         * 税额
         */
        @Schema(description = "税额", example = "5.50")
        private BigDecimal taxAmount;

        /**
         * 小费
         */
        @Schema(description = "小费", example = "8.00")
        private BigDecimal tipAmount;

        /**
         * 折扣金额
         */
        @Schema(description = "折扣金额", example = "5.00")
        private BigDecimal discountAmount;

        /**
         * 货币类型
         */
        @Schema(description = "货币类型", example = "CNY")
        private String currency;

        /**
         * 支付方式
         */
        @Schema(description = "支付方式", example = "信用卡")
        private String paymentMethod;

        /**
         * 卡号后四位
         */
        @Schema(description = "卡号后四位", example = "1234")
        private String cardLastFour;

        /**
         * 收据编号
         */
        @Schema(description = "收据编号", example = "R20250101001")
        private String receiptNumber;

        /**
         * 订单编号
         */
        @Schema(description = "订单编号", example = "ORD20250101001")
        private String orderNumber;

        /**
         * 商品明细
         */
        @Schema(description = "商品明细")
        private List<ReceiptItem> items;

        /**
         * 识别的分类
         */
        @Schema(description = "识别的分类", example = "餐饮")
        private String recognizedCategory;

        /**
         * 识别的标签
         */
        @Schema(description = "识别的标签")
        private List<String> recognizedTags;

        /**
         * 置信度详情
         */
        @Schema(description = "置信度详情")
        private Map<String, Double> confidenceDetails;
    }

    /**
     * 收据商品明细
     */
    @Data
    @Schema(description = "收据商品明细")
    public static class ReceiptItem {

        /**
         * 商品名称
         */
        @Schema(description = "商品名称", example = "拿铁咖啡")
        private String itemName;

        /**
         * 商品描述
         */
        @Schema(description = "商品描述", example = "大杯，加糖")
        private String itemDescription;

        /**
         * 数量
         */
        @Schema(description = "数量", example = "2")
        private Integer quantity;

        /**
         * 单价
         */
        @Schema(description = "单价", example = "25.00")
        private BigDecimal unitPrice;

        /**
         * 小计
         */
        @Schema(description = "小计", example = "50.00")
        private BigDecimal subtotal;

        /**
         * 商品分类
         */
        @Schema(description = "商品分类", example = "饮品")
        private String category;

        /**
         * 置信度
         */
        @Schema(description = "置信度", example = "0.92")
        private Double confidence;
    }

    /**
     * 原始OCR结果
     */
    @Data
    @Schema(description = "原始OCR结果")
    public static class OcrRawResult {

        /**
         * 识别的文本行
         */
        @Schema(description = "识别的文本行")
        private List<TextLine> textLines;

        /**
         * 识别的表格
         */
        @Schema(description = "识别的表格")
        private List<Table> tables;

        /**
         * 识别的关键字段
         */
        @Schema(description = "识别的关键字段")
        private Map<String, String> keyFields;

        /**
         * 原始响应数据
         */
        @Schema(description = "原始响应数据")
        private String rawResponse;
    }

    /**
     * 文本行
     */
    @Data
    @Schema(description = "文本行")
    public static class TextLine {

        /**
         * 文本内容
         */
        @Schema(description = "文本内容", example = "星巴克咖啡")
        private String text;

        /**
         * 置信度
         */
        @Schema(description = "置信度", example = "0.98")
        private Double confidence;

        /**
         * 位置坐标
         */
        @Schema(description = "位置坐标")
        private BoundingBox boundingBox;
    }

    /**
     * 表格
     */
    @Data
    @Schema(description = "表格")
    public static class Table {

        /**
         * 表格行
         */
        @Schema(description = "表格行")
        private List<TableRow> rows;

        /**
         * 表格列数
         */
        @Schema(description = "表格列数", example = "4")
        private Integer columnCount;

        /**
         * 表格行数
         */
        @Schema(description = "表格行数", example = "5")
        private Integer rowCount;
    }

    /**
     * 表格行
     */
    @Data
    @Schema(description = "表格行")
    public static class TableRow {

        /**
         * 单元格
         */
        @Schema(description = "单元格")
        private List<TableCell> cells;
    }

    /**
     * 表格单元格
     */
    @Data
    @Schema(description = "表格单元格")
    public static class TableCell {

        /**
         * 单元格文本
         */
        @Schema(description = "单元格文本", example = "拿铁咖啡")
        private String text;

        /**
         * 置信度
         */
        @Schema(description = "置信度", example = "0.95")
        private Double confidence;

        /**
         * 位置坐标
         */
        @Schema(description = "位置坐标")
        private BoundingBox boundingBox;
    }

    /**
     * 边界框
     */
    @Data
    @Schema(description = "边界框")
    public static class BoundingBox {

        /**
         * 左上角X坐标
         */
        @Schema(description = "左上角X坐标", example = "100")
        private Integer x;

        /**
         * 左上角Y坐标
         */
        @Schema(description = "左上角Y坐标", example = "50")
        private Integer y;

        /**
         * 宽度
         */
        @Schema(description = "宽度", example = "200")
        private Integer width;

        /**
         * 高度
         */
        @Schema(description = "高度", example = "30")
        private Integer height;
    }

    /**
     * 图片信息
     */
    @Data
    @Schema(description = "图片信息")
    public static class ImageInfo {

        /**
         * 原始文件名
         */
        @Schema(description = "原始文件名", example = "receipt.jpg")
        private String originalFileName;

        /**
         * 文件大小（字节）
         */
        @Schema(description = "文件大小（字节）", example = "1024000")
        private Long fileSize;

        /**
         * 图片宽度
         */
        @Schema(description = "图片宽度", example = "1920")
        private Integer width;

        /**
         * 图片高度
         */
        @Schema(description = "图片高度", example = "1080")
        private Integer height;

        /**
         * 图片格式
         */
        @Schema(description = "图片格式", example = "JPEG")
        private String format;

        /**
         * 存储路径
         */
        @Schema(description = "存储路径", example = "/uploads/receipts/2025/01/01/receipt_123.jpg")
        private String storagePath;

        /**
         * 访问URL
         */
        @Schema(description = "访问URL", example = "https://example.com/uploads/receipts/2025/01/01/receipt_123.jpg")
        private String accessUrl;

        /**
         * 缩略图URL
         */
        @Schema(description = "缩略图URL", example = "https://example.com/uploads/receipts/2025/01/01/receipt_123_thumb.jpg")
        private String thumbnailUrl;
    }
}
