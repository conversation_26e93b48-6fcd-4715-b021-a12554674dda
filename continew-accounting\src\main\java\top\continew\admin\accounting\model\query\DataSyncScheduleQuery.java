package top.continew.admin.accounting.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.starter.extension.crud.model.query.SortQuery;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 数据同步计划查询条件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "数据同步计划查询条件")
public class DataSyncScheduleQuery extends SortQuery implements Serializable {

    /**
     * 配置ID
     */
    @Schema(description = "配置ID", example = "1")
    private Long configId;

    /**
     * 计划名称
     */
    @Schema(description = "计划名称", example = "每日同步")
    private String scheduleName;

    /**
     * 计划类型
     */
    @Schema(description = "计划类型", example = "CRON")
    private String scheduleType;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    /**
     * 最后执行状态
     */
    @Schema(description = "最后执行状态", example = "SUCCESS")
    private String lastExecutionStatus;

    /**
     * 健康状态
     */
    @Schema(description = "健康状态", example = "HEALTHY")
    private String healthStatus;

    /**
     * 创建时间开始
     */
    @Schema(description = "创建时间开始", example = "2025-01-01T00:00:00")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    @Schema(description = "创建时间结束", example = "2025-01-31T23:59:59")
    private LocalDateTime createTimeEnd;

    /**
     * 最后执行时间开始
     */
    @Schema(description = "最后执行时间开始", example = "2025-01-01T00:00:00")
    private LocalDateTime lastExecutionTimeStart;

    /**
     * 最后执行时间结束
     */
    @Schema(description = "最后执行时间结束", example = "2025-01-31T23:59:59")
    private LocalDateTime lastExecutionTimeEnd;

    /**
     * 下次执行时间开始
     */
    @Schema(description = "下次执行时间开始", example = "2025-01-01T00:00:00")
    private LocalDateTime nextExecutionTimeStart;

    /**
     * 下次执行时间结束
     */
    @Schema(description = "下次执行时间结束", example = "2025-01-31T23:59:59")
    private LocalDateTime nextExecutionTimeEnd;

}
