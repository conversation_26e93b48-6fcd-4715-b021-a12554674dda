package top.continew.admin.accounting.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 规则执行响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "规则执行响应")
public class RuleExecutionResp {

    /**
     * 执行ID
     */
    @Schema(description = "执行ID", example = "1")
    private Long executionId;

    /**
     * 批次ID
     */
    @Schema(description = "批次ID", example = "BATCH_20240101_001")
    private String batchId;

    /**
     * 执行状态
     */
    @Schema(description = "执行状态", example = "RUNNING")
    private String executionStatus;

    /**
     * 执行模式
     */
    @Schema(description = "执行模式", example = "SYNC")
    private String executionMode;

    /**
     * 总规则数
     */
    @Schema(description = "总规则数", example = "5")
    private Integer totalRules;

    /**
     * 已执行规则数
     */
    @Schema(description = "已执行规则数", example = "3")
    private Integer executedRules;

    /**
     * 成功规则数
     */
    @Schema(description = "成功规则数", example = "2")
    private Integer successRules;

    /**
     * 失败规则数
     */
    @Schema(description = "失败规则数", example = "1")
    private Integer failedRules;

    /**
     * 跳过规则数
     */
    @Schema(description = "跳过规则数", example = "0")
    private Integer skippedRules;

    /**
     * 总处理记录数
     */
    @Schema(description = "总处理记录数", example = "1000")
    private Integer totalRecords;

    /**
     * 已处理记录数
     */
    @Schema(description = "已处理记录数", example = "600")
    private Integer processedRecords;

    /**
     * 成功处理记录数
     */
    @Schema(description = "成功处理记录数", example = "580")
    private Integer successRecords;

    /**
     * 失败处理记录数
     */
    @Schema(description = "失败处理记录数", example = "20")
    private Integer failedRecords;

    /**
     * 执行进度
     */
    @Schema(description = "执行进度", example = "60.0")
    private Double progress;

    /**
     * 当前步骤
     */
    @Schema(description = "当前步骤", example = "执行规则3")
    private String currentStep;

    /**
     * 预估剩余时间（秒）
     */
    @Schema(description = "预估剩余时间（秒）", example = "120")
    private Integer estimatedRemainingSeconds;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2024-01-01 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", example = "2024-01-01 10:05:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 执行时间（毫秒）
     */
    @Schema(description = "执行时间（毫秒）", example = "300000")
    private Long executionTimeMs;

    /**
     * 平均处理速度（记录/秒）
     */
    @Schema(description = "平均处理速度（记录/秒）", example = "10.5")
    private Double avgProcessingSpeed;

    /**
     * 规则执行结果
     */
    @Schema(description = "规则执行结果")
    private List<RuleExecutionResult> ruleResults;

    /**
     * 执行摘要
     */
    @Schema(description = "执行摘要")
    private ExecutionSummary executionSummary;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private List<ExecutionError> errors;

    /**
     * 警告信息
     */
    @Schema(description = "警告信息")
    private List<ExecutionWarning> warnings;

    /**
     * 执行日志
     */
    @Schema(description = "执行日志")
    private List<ExecutionLog> executionLogs;

    /**
     * 性能指标
     */
    @Schema(description = "性能指标")
    private PerformanceMetrics performanceMetrics;

    /**
     * 资源使用情况
     */
    @Schema(description = "资源使用情况")
    private ResourceUsage resourceUsage;

    /**
     * 规则执行结果
     */
    @Data
    @Schema(description = "规则执行结果")
    public static class RuleExecutionResult {

        /**
         * 规则ID
         */
        @Schema(description = "规则ID", example = "1")
        private Long ruleId;

        /**
         * 规则名称
         */
        @Schema(description = "规则名称", example = "自动分类规则")
        private String ruleName;

        /**
         * 执行状态
         */
        @Schema(description = "执行状态", example = "SUCCESS")
        private String executionStatus;

        /**
         * 处理记录数
         */
        @Schema(description = "处理记录数", example = "100")
        private Integer processedRecords;

        /**
         * 成功记录数
         */
        @Schema(description = "成功记录数", example = "95")
        private Integer successRecords;

        /**
         * 失败记录数
         */
        @Schema(description = "失败记录数", example = "5")
        private Integer failedRecords;

        /**
         * 执行时间（毫秒）
         */
        @Schema(description = "执行时间（毫秒）", example = "1500")
        private Long executionTimeMs;

        /**
         * 开始时间
         */
        @Schema(description = "开始时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime startTime;

        /**
         * 结束时间
         */
        @Schema(description = "结束时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime endTime;

        /**
         * 执行结果
         */
        @Schema(description = "执行结果", example = "成功处理95条记录，失败5条")
        private String executionResult;

        /**
         * 错误信息
         */
        @Schema(description = "错误信息")
        private String errorMessage;

        /**
         * 动作执行结果
         */
        @Schema(description = "动作执行结果")
        private List<ActionExecutionResult> actionResults;

        /**
         * 执行统计
         */
        @Schema(description = "执行统计")
        private Map<String, Object> executionStats;
    }

    /**
     * 动作执行结果
     */
    @Data
    @Schema(description = "动作执行结果")
    public static class ActionExecutionResult {

        /**
         * 动作类型
         */
        @Schema(description = "动作类型", example = "SET_CATEGORY")
        private String actionType;

        /**
         * 执行状态
         */
        @Schema(description = "执行状态", example = "SUCCESS")
        private String executionStatus;

        /**
         * 处理记录数
         */
        @Schema(description = "处理记录数", example = "50")
        private Integer processedRecords;

        /**
         * 成功记录数
         */
        @Schema(description = "成功记录数", example = "48")
        private Integer successRecords;

        /**
         * 失败记录数
         */
        @Schema(description = "失败记录数", example = "2")
        private Integer failedRecords;

        /**
         * 执行时间（毫秒）
         */
        @Schema(description = "执行时间（毫秒）", example = "800")
        private Long executionTimeMs;

        /**
         * 执行结果
         */
        @Schema(description = "执行结果", example = "成功设置48条记录的分类")
        private String executionResult;

        /**
         * 错误信息
         */
        @Schema(description = "错误信息")
        private String errorMessage;

        /**
         * 重试次数
         */
        @Schema(description = "重试次数", example = "1")
        private Integer retryCount;
    }

    /**
     * 执行摘要
     */
    @Data
    @Schema(description = "执行摘要")
    public static class ExecutionSummary {

        /**
         * 总体成功率
         */
        @Schema(description = "总体成功率", example = "0.95")
        private Double overallSuccessRate;

        /**
         * 平均执行时间（毫秒）
         */
        @Schema(description = "平均执行时间（毫秒）", example = "1200")
        private Long avgExecutionTimeMs;

        /**
         * 最快执行时间（毫秒）
         */
        @Schema(description = "最快执行时间（毫秒）", example = "500")
        private Long fastestExecutionTimeMs;

        /**
         * 最慢执行时间（毫秒）
         */
        @Schema(description = "最慢执行时间（毫秒）", example = "3000")
        private Long slowestExecutionTimeMs;

        /**
         * 处理速度（记录/秒）
         */
        @Schema(description = "处理速度（记录/秒）", example = "15.5")
        private Double processingSpeed;

        /**
         * 影响的数据类型
         */
        @Schema(description = "影响的数据类型")
        private List<String> affectedDataTypes;

        /**
         * 主要操作类型
         */
        @Schema(description = "主要操作类型")
        private Map<String, Integer> operationTypes;

        /**
         * 执行建议
         */
        @Schema(description = "执行建议")
        private List<String> recommendations;
    }

    /**
     * 执行错误
     */
    @Data
    @Schema(description = "执行错误")
    public static class ExecutionError {

        /**
         * 错误代码
         */
        @Schema(description = "错误代码", example = "RULE_EXECUTION_FAILED")
        private String errorCode;

        /**
         * 错误信息
         */
        @Schema(description = "错误信息", example = "规则执行失败")
        private String errorMessage;

        /**
         * 规则ID
         */
        @Schema(description = "规则ID", example = "1")
        private Long ruleId;

        /**
         * 规则名称
         */
        @Schema(description = "规则名称", example = "自动分类规则")
        private String ruleName;

        /**
         * 错误详情
         */
        @Schema(description = "错误详情")
        private String errorDetails;

        /**
         * 发生时间
         */
        @Schema(description = "发生时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime occurredTime;

        /**
         * 严重程度
         */
        @Schema(description = "严重程度", example = "HIGH")
        private String severity;

        /**
         * 建议解决方案
         */
        @Schema(description = "建议解决方案")
        private String suggestedSolution;
    }

    /**
     * 执行警告
     */
    @Data
    @Schema(description = "执行警告")
    public static class ExecutionWarning {

        /**
         * 警告代码
         */
        @Schema(description = "警告代码", example = "LOW_MATCH_RATE")
        private String warningCode;

        /**
         * 警告信息
         */
        @Schema(description = "警告信息", example = "匹配率较低")
        private String warningMessage;

        /**
         * 规则ID
         */
        @Schema(description = "规则ID", example = "1")
        private Long ruleId;

        /**
         * 规则名称
         */
        @Schema(description = "规则名称", example = "自动分类规则")
        private String ruleName;

        /**
         * 警告详情
         */
        @Schema(description = "警告详情")
        private String warningDetails;

        /**
         * 发生时间
         */
        @Schema(description = "发生时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime occurredTime;

        /**
         * 建议
         */
        @Schema(description = "建议")
        private String suggestion;
    }

    /**
     * 执行日志
     */
    @Data
    @Schema(description = "执行日志")
    public static class ExecutionLog {

        /**
         * 日志级别
         */
        @Schema(description = "日志级别", example = "INFO")
        private String logLevel;

        /**
         * 日志信息
         */
        @Schema(description = "日志信息", example = "开始执行规则")
        private String logMessage;

        /**
         * 规则ID
         */
        @Schema(description = "规则ID", example = "1")
        private Long ruleId;

        /**
         * 规则名称
         */
        @Schema(description = "规则名称", example = "自动分类规则")
        private String ruleName;

        /**
         * 日志时间
         */
        @Schema(description = "日志时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime logTime;

        /**
         * 执行步骤
         */
        @Schema(description = "执行步骤", example = "CONDITION_CHECK")
        private String executionStep;

        /**
         * 附加信息
         */
        @Schema(description = "附加信息")
        private Map<String, Object> additionalInfo;
    }

    /**
     * 性能指标
     */
    @Data
    @Schema(description = "性能指标")
    public static class PerformanceMetrics {

        /**
         * CPU使用率
         */
        @Schema(description = "CPU使用率", example = "0.65")
        private Double cpuUsage;

        /**
         * 内存使用量（MB）
         */
        @Schema(description = "内存使用量（MB）", example = "256")
        private Long memoryUsageMB;

        /**
         * 数据库连接数
         */
        @Schema(description = "数据库连接数", example = "5")
        private Integer dbConnections;

        /**
         * 数据库查询次数
         */
        @Schema(description = "数据库查询次数", example = "150")
        private Integer dbQueries;

        /**
         * 平均查询时间（毫秒）
         */
        @Schema(description = "平均查询时间（毫秒）", example = "25")
        private Long avgQueryTimeMs;

        /**
         * 缓存命中率
         */
        @Schema(description = "缓存命中率", example = "0.85")
        private Double cacheHitRate;

        /**
         * 网络IO（KB）
         */
        @Schema(description = "网络IO（KB）", example = "1024")
        private Long networkIOKB;

        /**
         * 磁盘IO（KB）
         */
        @Schema(description = "磁盘IO（KB）", example = "512")
        private Long diskIOKB;
    }

    /**
     * 资源使用情况
     */
    @Data
    @Schema(description = "资源使用情况")
    public static class ResourceUsage {

        /**
         * 线程池使用情况
         */
        @Schema(description = "线程池使用情况")
        private Map<String, Integer> threadPoolUsage;

        /**
         * 连接池使用情况
         */
        @Schema(description = "连接池使用情况")
        private Map<String, Integer> connectionPoolUsage;

        /**
         * 队列使用情况
         */
        @Schema(description = "队列使用情况")
        private Map<String, Integer> queueUsage;

        /**
         * 临时文件数量
         */
        @Schema(description = "临时文件数量", example = "3")
        private Integer tempFileCount;

        /**
         * 临时文件大小（KB）
         */
        @Schema(description = "临时文件大小（KB）", example = "2048")
        private Long tempFileSizeKB;

        /**
         * 资源清理状态
         */
        @Schema(description = "资源清理状态", example = "COMPLETED")
        private String cleanupStatus;
    }
}
