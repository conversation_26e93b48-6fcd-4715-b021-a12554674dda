package top.continew.admin.accounting.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import top.continew.admin.accounting.base.BaseControllerTest;
import top.continew.admin.accounting.enums.TransactionType;
import top.continew.admin.accounting.model.req.TransactionCreateReq;
import top.continew.admin.accounting.model.req.TransactionQueryReq;
import top.continew.admin.accounting.model.req.TransactionUpdateReq;
import top.continew.admin.accounting.model.resp.TransactionResp;
import top.continew.admin.accounting.service.TransactionService;
import top.continew.admin.accounting.util.TestDataUtil;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 交易控制器测试
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
class TransactionControllerTest extends BaseControllerTest {

    @MockBean
    private TransactionService transactionService;

    private TransactionCreateReq createReq;
    private TransactionUpdateReq updateReq;
    private TransactionResp transactionResp;

    @BeforeEach
    @Override
    protected void beforeEach() {
        super.beforeEach();
        createReq = TestDataUtil.createTestTransactionCreateReq();
        
        updateReq = new TransactionUpdateReq();
        updateReq.setAmount(new BigDecimal("75.00"));
        updateReq.setDescription("更新后的交易");
        
        transactionResp = new TransactionResp();
        transactionResp.setId(1L);
        transactionResp.setGroupId(1L);
        transactionResp.setUserId(1L);
        transactionResp.setType(TransactionType.EXPENSE);
        transactionResp.setAmount(new BigDecimal("50.00"));
        transactionResp.setDescription("午餐");
        transactionResp.setCurrency("CNY");
    }

    @Test
    void testAdd() throws Exception {
        // Given
        when(transactionService.add(any(TransactionCreateReq.class))).thenReturn(1L);

        // When & Then
        performPost("/transaction", createReq)
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(1L));

        verify(transactionService).add(any(TransactionCreateReq.class));
    }

    @Test
    void testGetById() throws Exception {
        // Given
        when(transactionService.get(1L)).thenReturn(transactionResp);

        // When & Then
        performGet("/transaction/1")
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value(1L))
                .andExpect(jsonPath("$.data.amount").value(50.00))
                .andExpect(jsonPath("$.data.description").value("午餐"));

        verify(transactionService).get(1L);
    }

    @Test
    void testUpdate() throws Exception {
        // Given
        doNothing().when(transactionService).update(any(TransactionUpdateReq.class), eq(1L));

        // When & Then
        performPut("/transaction/1", updateReq)
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        verify(transactionService).update(any(TransactionUpdateReq.class), eq(1L));
    }

    @Test
    void testDelete() throws Exception {
        // Given
        doNothing().when(transactionService).delete(anyList());

        // When & Then
        performDelete("/transaction/1")
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        verify(transactionService).delete(anyList());
    }

    @Test
    void testPage() throws Exception {
        // Given
        PageResp<TransactionResp> pageResp = new PageResp<>();
        pageResp.setTotal(1L);
        pageResp.setList(Arrays.asList(transactionResp));
        
        when(transactionService.page(any(TransactionQueryReq.class), any(PageQuery.class)))
                .thenReturn(pageResp);

        // When & Then
        mockMvc.perform(get("/transaction/page")
                .param("page", "1")
                .param("size", "10")
                .param("groupId", "1")
                .param("type", "EXPENSE")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.total").value(1))
                .andExpect(jsonPath("$.data.list[0].amount").value(50.00));

        verify(transactionService).page(any(TransactionQueryReq.class), any(PageQuery.class));
    }

    @Test
    void testList() throws Exception {
        // Given
        when(transactionService.list(any(TransactionQueryReq.class)))
                .thenReturn(Arrays.asList(transactionResp));

        // When & Then
        mockMvc.perform(get("/transaction/list")
                .param("groupId", "1")
                .param("type", "EXPENSE")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data[0].amount").value(50.00));

        verify(transactionService).list(any(TransactionQueryReq.class));
    }

    @Test
    void testBatchAdd() throws Exception {
        // Given
        when(transactionService.batchAdd(anyList())).thenReturn(Arrays.asList(1L, 2L));

        // When & Then
        performPost("/transaction/batch", Arrays.asList(createReq, createReq))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data[0]").value(1L))
                .andExpect(jsonPath("$.data[1]").value(2L));

        verify(transactionService).batchAdd(anyList());
    }

    @Test
    void testGetStatistics() throws Exception {
        // Given
        when(transactionService.getStatistics(anyLong(), any(), any(), any()))
                .thenReturn(new Object());

        // When & Then
        mockMvc.perform(get("/transaction/statistics")
                .param("groupId", "1")
                .param("type", "EXPENSE")
                .param("startTime", "2024-01-01T00:00:00")
                .param("endTime", "2024-12-31T23:59:59")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        verify(transactionService).getStatistics(anyLong(), any(), any(), any());
    }

    @Test
    void testGetMonthlyTrend() throws Exception {
        // Given
        when(transactionService.getMonthlyTrend(anyLong(), any(), anyInt()))
                .thenReturn(Arrays.asList());

        // When & Then
        mockMvc.perform(get("/transaction/monthly-trend")
                .param("groupId", "1")
                .param("type", "EXPENSE")
                .param("months", "12")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        verify(transactionService).getMonthlyTrend(1L, TransactionType.EXPENSE, 12);
    }

    @Test
    void testGetCategoryStatistics() throws Exception {
        // Given
        when(transactionService.getCategoryStatistics(anyLong(), any(), any(), any()))
                .thenReturn(Arrays.asList());

        // When & Then
        mockMvc.perform(get("/transaction/category-statistics")
                .param("groupId", "1")
                .param("type", "EXPENSE")
                .param("startTime", "2024-01-01T00:00:00")
                .param("endTime", "2024-12-31T23:59:59")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        verify(transactionService).getCategoryStatistics(anyLong(), any(), any(), any());
    }

    @Test
    void testGetUserStatistics() throws Exception {
        // Given
        when(transactionService.getUserStatistics(anyLong(), any(), any(), any()))
                .thenReturn(Arrays.asList());

        // When & Then
        mockMvc.perform(get("/transaction/user-statistics")
                .param("groupId", "1")
                .param("type", "EXPENSE")
                .param("startTime", "2024-01-01T00:00:00")
                .param("endTime", "2024-12-31T23:59:59")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        verify(transactionService).getUserStatistics(anyLong(), any(), any(), any());
    }

}
