package top.continew.admin.bot.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.continew.admin.bot.model.dto.BotMessage;
import top.continew.admin.bot.telegram.service.TelegramBotService;
import top.continew.admin.bot.discord.service.DiscordBotService;

/**
 * 消息处理器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MessageProcessor {

    private final TelegramBotService telegramBotService;
    private final DiscordBotService discordBotService;
    private final CommandParser commandParser;

    /**
     * 处理文本消息
     */
    public String processTextMessage(BotMessage message) {
        try {
            String content = message.getContent();
            
            // 检查是否是记账相关的消息
            if (isAccountingMessage(content)) {
                return "检测到记账信息，请使用正确的记账格式，如：-50 午餐 @餐饮 #日常";
            }
            
            // 检查是否是问候语
            if (isGreeting(content)) {
                return "你好！我是记账机器人，可以帮助你记录收支。\n" +
                       "发送 /help 查看使用帮助。";
            }
            
            // 检查是否是帮助请求
            if (isHelpRequest(content)) {
                return commandParser.getHelpMessage();
            }
            
            return null; // 不需要回复
            
        } catch (Exception e) {
            log.error("处理文本消息失败: {}", message, e);
            return "处理消息时发生错误，请稍后重试。";
        }
    }

    /**
     * 发送Telegram消息
     */
    public void sendTelegramMessage(String chatId, String message) {
        try {
            telegramBotService.sendMessage(chatId, message);
        } catch (Exception e) {
            log.error("发送Telegram消息失败: chatId={}, message={}", chatId, message, e);
            throw new RuntimeException("发送Telegram消息失败", e);
        }
    }

    /**
     * 发送Discord消息
     */
    public void sendDiscordMessage(String channelId, String message) {
        try {
            discordBotService.sendMessage(channelId, message);
        } catch (Exception e) {
            log.error("发送Discord消息失败: channelId={}, message={}", channelId, message, e);
            throw new RuntimeException("发送Discord消息失败", e);
        }
    }

    /**
     * 发送微信消息
     */
    public void sendWechatMessage(String chatId, String message) {
        try {
            // TODO: 实现微信消息发送
            log.info("发送微信消息: chatId={}, message={}", chatId, message);
        } catch (Exception e) {
            log.error("发送微信消息失败: chatId={}, message={}", chatId, message, e);
            throw new RuntimeException("发送微信消息失败", e);
        }
    }

    /**
     * 处理Telegram回调
     */
    public void processTelegramCallback(BotMessage message) {
        try {
            // 解析回调数据
            String callbackData = message.getCallbackData();
            if (callbackData == null) {
                return;
            }
            
            // 根据回调数据处理
            if (callbackData.startsWith("confirm_")) {
                handleConfirmCallback(message);
            } else if (callbackData.startsWith("cancel_")) {
                handleCancelCallback(message);
            } else if (callbackData.startsWith("edit_")) {
                handleEditCallback(message);
            } else {
                log.warn("未知的回调数据: {}", callbackData);
            }
            
        } catch (Exception e) {
            log.error("处理Telegram回调失败: {}", message, e);
        }
    }

    /**
     * 处理Discord交互
     */
    public void processDiscordInteraction(BotMessage message) {
        try {
            // 根据交互类型处理
            String interactionType = message.getInteractionType();
            if (interactionType == null) {
                return;
            }
            
            switch (interactionType) {
                case "BUTTON" -> handleDiscordButtonInteraction(message);
                case "SELECT_MENU" -> handleDiscordSelectMenuInteraction(message);
                case "MODAL" -> handleDiscordModalInteraction(message);
                default -> log.warn("未知的Discord交互类型: {}", interactionType);
            }
            
        } catch (Exception e) {
            log.error("处理Discord交互失败: {}", message, e);
        }
    }

    /**
     * 检查是否是记账消息
     */
    private boolean isAccountingMessage(String content) {
        if (content == null || content.trim().isEmpty()) {
            return false;
        }
        
        // 简单的记账关键词检测
        String[] keywords = {"花了", "赚了", "收入", "支出", "买", "卖", "转账", "还款"};
        String lowerContent = content.toLowerCase();
        
        for (String keyword : keywords) {
            if (lowerContent.contains(keyword)) {
                return true;
            }
        }
        
        // 检查是否包含金额
        return content.matches(".*\\d+.*");
    }

    /**
     * 检查是否是问候语
     */
    private boolean isGreeting(String content) {
        if (content == null) {
            return false;
        }
        
        String[] greetings = {"你好", "hello", "hi", "嗨", "开始", "start"};
        String lowerContent = content.toLowerCase();
        
        for (String greeting : greetings) {
            if (lowerContent.contains(greeting)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查是否是帮助请求
     */
    private boolean isHelpRequest(String content) {
        if (content == null) {
            return false;
        }
        
        String[] helpKeywords = {"帮助", "help", "使用", "怎么", "如何"};
        String lowerContent = content.toLowerCase();
        
        for (String keyword : helpKeywords) {
            if (lowerContent.contains(keyword)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 处理确认回调
     */
    private void handleConfirmCallback(BotMessage message) {
        String callbackData = message.getCallbackData();
        String transactionId = callbackData.substring("confirm_".length());
        
        // TODO: 实现确认交易逻辑
        log.info("确认交易: {}", transactionId);
        
        sendTelegramMessage(message.getChatId(), "✅ 交易已确认");
    }

    /**
     * 处理取消回调
     */
    private void handleCancelCallback(BotMessage message) {
        String callbackData = message.getCallbackData();
        String transactionId = callbackData.substring("cancel_".length());
        
        // TODO: 实现取消交易逻辑
        log.info("取消交易: {}", transactionId);
        
        sendTelegramMessage(message.getChatId(), "❌ 交易已取消");
    }

    /**
     * 处理编辑回调
     */
    private void handleEditCallback(BotMessage message) {
        String callbackData = message.getCallbackData();
        String transactionId = callbackData.substring("edit_".length());
        
        // TODO: 实现编辑交易逻辑
        log.info("编辑交易: {}", transactionId);
        
        sendTelegramMessage(message.getChatId(), "✏️ 请发送新的交易信息");
    }

    /**
     * 处理Discord按钮交互
     */
    private void handleDiscordButtonInteraction(BotMessage message) {
        // TODO: 实现Discord按钮交互处理
        log.info("处理Discord按钮交互: {}", message.getContent());
    }

    /**
     * 处理Discord选择菜单交互
     */
    private void handleDiscordSelectMenuInteraction(BotMessage message) {
        // TODO: 实现Discord选择菜单交互处理
        log.info("处理Discord选择菜单交互: {}", message.getContent());
    }

    /**
     * 处理Discord模态框交互
     */
    private void handleDiscordModalInteraction(BotMessage message) {
        // TODO: 实现Discord模态框交互处理
        log.info("处理Discord模态框交互: {}", message.getContent());
    }
}
