package top.continew.admin.bot.discord;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.dv8tion.jda.api.JDA;
import net.dv8tion.jda.api.JDABuilder;
import net.dv8tion.jda.api.OnlineStatus;
import net.dv8tion.jda.api.entities.Activity;
import net.dv8tion.jda.api.entities.Guild;
import net.dv8tion.jda.api.entities.Message;
import net.dv8tion.jda.api.entities.channel.concrete.TextChannel;
import net.dv8tion.jda.api.interactions.commands.OptionType;
import net.dv8tion.jda.api.interactions.commands.build.Commands;
import net.dv8tion.jda.api.interactions.commands.build.OptionData;
import net.dv8tion.jda.api.interactions.components.buttons.Button;
import net.dv8tion.jda.api.requests.GatewayIntent;
import net.dv8tion.jda.api.utils.ChunkingFilter;
import net.dv8tion.jda.api.utils.MemberCachePolicy;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import top.continew.admin.bot.config.DiscordBotConfig;
import top.continew.admin.bot.discord.handler.DiscordCommandHandler;
import top.continew.admin.bot.discord.listener.DiscordEventListener;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.concurrent.CompletableFuture;

/**
 * Discord机器人服务
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "bot.discord", name = "enabled", havingValue = "true")
public class DiscordBotService {

    private final DiscordBotConfig discordBotConfig;
    private final DiscordCommandHandler commandHandler;
    private final DiscordEventListener eventListener;
    
    private JDA jda;

    @PostConstruct
    public void init() {
        log.info("Discord机器人服务初始化开始...");
        
        try {
            // 构建JDA实例
            JDABuilder builder = JDABuilder.createDefault(discordBotConfig.getToken())
                    .setChunkingFilter(ChunkingFilter.ALL)
                    .setMemberCachePolicy(MemberCachePolicy.ALL)
                    .enableIntents(
                            GatewayIntent.GUILD_MESSAGES,
                            GatewayIntent.DIRECT_MESSAGES,
                            GatewayIntent.MESSAGE_CONTENT,
                            GatewayIntent.GUILD_MEMBERS,
                            GatewayIntent.GUILD_MESSAGE_REACTIONS
                    );

            // 添加事件监听器
            builder.addEventListeners(eventListener);

            // 设置活动状态
            if (discordBotConfig.getActivity().getEnabled()) {
                Activity activity = createActivity();
                if (activity != null) {
                    builder.setActivity(activity);
                }
            }

            // 设置在线状态
            OnlineStatus status = parseOnlineStatus(discordBotConfig.getStatus().getOnlineStatus());
            builder.setStatus(status);

            // 构建并启动
            jda = builder.build();
            
            // 等待JDA准备完成
            jda.awaitReady();
            
            log.info("Discord机器人连接成功，用户: {}", jda.getSelfUser().getAsTag());
            
            // 注册命令
            registerCommands();
            
        } catch (Exception e) {
            log.error("Discord机器人初始化失败", e);
        }
    }

    /**
     * 注册Slash Commands
     */
    private void registerCommands() {
        try {
            log.info("开始注册Discord Slash Commands...");
            
            // 根据配置决定注册范围
            switch (discordBotConfig.getCommandSyncMode()) {
                case GLOBAL -> registerGlobalCommands();
                case GUILD -> registerGuildCommands();
                case HYBRID -> {
                    registerGlobalCommands();
                    registerGuildCommands();
                }
            }
            
            log.info("Discord Slash Commands注册完成");
        } catch (Exception e) {
            log.error("注册Discord Slash Commands失败", e);
        }
    }

    /**
     * 注册全局命令
     */
    private void registerGlobalCommands() {
        jda.updateCommands().addCommands(
                // 记账命令
                Commands.slash("add", "添加收支记录")
                        .addOption(OptionType.STRING, "amount", "金额 (例如: +100, -50)", true)
                        .addOption(OptionType.STRING, "description", "描述", true)
                        .addOption(OptionType.STRING, "category", "分类", false)
                        .addOption(OptionType.STRING, "tags", "标签 (用逗号分隔)", false)
                        .addOption(OptionType.STRING, "wallet", "钱包", false),
                
                // 查询余额
                Commands.slash("balance", "查询余额")
                        .addOption(OptionType.STRING, "wallet", "钱包名称", false),
                
                // 查询历史
                Commands.slash("history", "查询交易历史")
                        .addOption(OptionType.INTEGER, "limit", "显示条数", false)
                        .addOption(OptionType.STRING, "category", "分类筛选", false)
                        .addOption(OptionType.STRING, "date", "日期筛选 (YYYY-MM-DD)", false),
                
                // 统计报表
                Commands.slash("stats", "查看统计报表")
                        .addOption(OptionType.STRING, "period", "统计周期", false)
                        .addOptions(new OptionData(OptionType.STRING, "type", "报表类型")
                                .addChoice("概览", "overview")
                                .addChoice("分类", "category")
                                .addChoice("趋势", "trend")),
                
                // 群组管理
                Commands.slash("group", "群组管理")
                        .addOptions(new OptionData(OptionType.STRING, "action", "操作类型", true)
                                .addChoice("注册", "register")
                                .addChoice("设置", "settings")
                                .addChoice("成员", "members")
                                .addChoice("权限", "permissions")),
                
                // 帮助命令
                Commands.slash("help", "显示帮助信息")
                        .addOption(OptionType.STRING, "command", "具体命令", false),
                
                // 设置命令
                Commands.slash("settings", "机器人设置")
                        .addOptions(new OptionData(OptionType.STRING, "setting", "设置项", true)
                                .addChoice("语言", "language")
                                .addChoice("币种", "currency")
                                .addChoice("时区", "timezone")
                                .addChoice("通知", "notifications"))
        ).queue(
                success -> log.info("全局命令注册成功"),
                error -> log.error("全局命令注册失败", error)
        );
    }

    /**
     * 注册服务器命令（用于测试）
     */
    private void registerGuildCommands() {
        if (discordBotConfig.getTestGuildId() != null && !discordBotConfig.getTestGuildId().isEmpty()) {
            Guild guild = jda.getGuildById(discordBotConfig.getTestGuildId());
            if (guild != null) {
                guild.updateCommands().addCommands(
                        // 测试命令
                        Commands.slash("test", "测试命令")
                                .addOption(OptionType.STRING, "message", "测试消息", false),
                        
                        // 调试命令
                        Commands.slash("debug", "调试信息")
                                .addOption(OptionType.BOOLEAN, "verbose", "详细信息", false)
                ).queue(
                        success -> log.info("服务器命令注册成功: {}", guild.getName()),
                        error -> log.error("服务器命令注册失败: {}", guild.getName(), error)
                );
            }
        }
    }

    /**
     * 发送消息
     */
    public CompletableFuture<Message> sendMessage(String channelId, String content) {
        try {
            TextChannel channel = jda.getTextChannelById(channelId);
            if (channel != null) {
                return channel.sendMessage(content).submit();
            } else {
                log.warn("找不到频道: {}", channelId);
                return CompletableFuture.failedFuture(new IllegalArgumentException("频道不存在"));
            }
        } catch (Exception e) {
            log.error("发送Discord消息失败", e);
            return CompletableFuture.failedFuture(e);
        }
    }

    /**
     * 发送带按钮的消息
     */
    public CompletableFuture<Message> sendMessageWithButtons(String channelId, String content, Button... buttons) {
        try {
            TextChannel channel = jda.getTextChannelById(channelId);
            if (channel != null) {
                return channel.sendMessage(content)
                        .setActionRow(buttons)
                        .submit();
            } else {
                log.warn("找不到频道: {}", channelId);
                return CompletableFuture.failedFuture(new IllegalArgumentException("频道不存在"));
            }
        } catch (Exception e) {
            log.error("发送Discord消息失败", e);
            return CompletableFuture.failedFuture(e);
        }
    }

    /**
     * 编辑消息
     */
    public CompletableFuture<Message> editMessage(String channelId, String messageId, String newContent) {
        try {
            TextChannel channel = jda.getTextChannelById(channelId);
            if (channel != null) {
                return channel.editMessageById(messageId, newContent).submit();
            } else {
                log.warn("找不到频道: {}", channelId);
                return CompletableFuture.failedFuture(new IllegalArgumentException("频道不存在"));
            }
        } catch (Exception e) {
            log.error("编辑Discord消息失败", e);
            return CompletableFuture.failedFuture(e);
        }
    }

    /**
     * 删除消息
     */
    public CompletableFuture<Void> deleteMessage(String channelId, String messageId) {
        try {
            TextChannel channel = jda.getTextChannelById(channelId);
            if (channel != null) {
                return channel.deleteMessageById(messageId).submit();
            } else {
                log.warn("找不到频道: {}", channelId);
                return CompletableFuture.failedFuture(new IllegalArgumentException("频道不存在"));
            }
        } catch (Exception e) {
            log.error("删除Discord消息失败", e);
            return CompletableFuture.failedFuture(e);
        }
    }

    /**
     * 创建活动状态
     */
    private Activity createActivity() {
        try {
            String type = discordBotConfig.getActivity().getType();
            String name = discordBotConfig.getActivity().getName();
            String url = discordBotConfig.getActivity().getUrl();

            return switch (type.toUpperCase()) {
                case "PLAYING" -> Activity.playing(name);
                case "STREAMING" -> Activity.streaming(name, url);
                case "LISTENING" -> Activity.listening(name);
                case "WATCHING" -> Activity.watching(name);
                case "COMPETING" -> Activity.competing(name);
                default -> Activity.playing(name);
            };
        } catch (Exception e) {
            log.warn("创建活动状态失败，使用默认状态", e);
            return Activity.playing("记账助手");
        }
    }

    /**
     * 解析在线状态
     */
    private OnlineStatus parseOnlineStatus(String status) {
        try {
            return switch (status.toUpperCase()) {
                case "ONLINE" -> OnlineStatus.ONLINE;
                case "IDLE" -> OnlineStatus.IDLE;
                case "DO_NOT_DISTURB", "DND" -> OnlineStatus.DO_NOT_DISTURB;
                case "INVISIBLE" -> OnlineStatus.INVISIBLE;
                default -> OnlineStatus.ONLINE;
            };
        } catch (Exception e) {
            log.warn("解析在线状态失败，使用默认状态", e);
            return OnlineStatus.ONLINE;
        }
    }

    /**
     * 获取JDA实例
     */
    public JDA getJda() {
        return jda;
    }

    /**
     * 检查机器人是否在线
     */
    public boolean isOnline() {
        return jda != null && jda.getStatus() == JDA.Status.CONNECTED;
    }

    @PreDestroy
    public void destroy() {
        if (jda != null) {
            log.info("Discord机器人服务关闭...");
            jda.shutdown();
        }
    }
}
