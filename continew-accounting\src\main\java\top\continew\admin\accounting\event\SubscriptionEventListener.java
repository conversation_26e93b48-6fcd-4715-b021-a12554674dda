package top.continew.admin.accounting.event;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import top.continew.admin.accounting.model.entity.SubscriptionDO;
import top.continew.admin.accounting.model.entity.SubscriptionPlanDO;
import top.continew.admin.accounting.model.event.SubscriptionCancelledEvent;
import top.continew.admin.accounting.model.event.SubscriptionCreatedEvent;
import top.continew.admin.accounting.model.event.SubscriptionExpiredEvent;
import top.continew.admin.accounting.service.SubscriptionPlanService;
import top.continew.admin.accounting.service.SubscriptionService;
import top.continew.admin.accounting.service.UsageStatisticsService;

/**
 * 订阅事件监听器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SubscriptionEventListener {

    private final SubscriptionService subscriptionService;
    private final SubscriptionPlanService subscriptionPlanService;
    private final UsageStatisticsService usageStatisticsService;

    /**
     * 监听订阅创建事件
     */
    @EventListener
    @Async("subscriptionExecutor")
    public void handleSubscriptionCreated(SubscriptionCreatedEvent event) {
        log.info("处理订阅创建事件: subscriptionId={}, groupId={}, planId={}", 
                event.getSubscriptionId(), event.getGroupId(), event.getPlanId());

        try {
            // 1. 初始化使用量统计
            usageStatisticsService.initializeUsageStatistics(event.getGroupId());
            log.debug("已初始化群组使用量统计: groupId={}", event.getGroupId());

            // 2. 发送欢迎邮件/通知
            sendWelcomeNotification(event);

            // 3. 记录订阅创建日志
            logSubscriptionActivity(event.getSubscriptionId(), "SUBSCRIPTION_CREATED", 
                    String.format("订阅已创建，套餐: %s", event.getPlanName()));

            // 4. 更新套餐统计
            subscriptionPlanService.updatePlanStatistics(event.getPlanId());

            log.info("订阅创建事件处理完成: subscriptionId={}", event.getSubscriptionId());

        } catch (Exception e) {
            log.error("处理订阅创建事件失败: subscriptionId={}, error={}", 
                    event.getSubscriptionId(), e.getMessage(), e);
        }
    }

    /**
     * 监听订阅过期事件
     */
    @EventListener
    @Async("subscriptionExecutor")
    public void handleSubscriptionExpired(SubscriptionExpiredEvent event) {
        log.info("处理订阅过期事件: subscriptionId={}, groupId={}", 
                event.getSubscriptionId(), event.getGroupId());

        try {
            // 1. 降级群组权限
            downgradeGroupPermissions(event.getGroupId());

            // 2. 发送过期通知
            sendExpirationNotification(event);

            // 3. 记录订阅过期日志
            logSubscriptionActivity(event.getSubscriptionId(), "SUBSCRIPTION_EXPIRED", 
                    String.format("订阅已过期，过期时间: %s", event.getExpiredAt()));

            // 4. 清理临时数据
            cleanupTemporaryData(event.getGroupId());

            // 5. 更新套餐统计
            subscriptionPlanService.updatePlanStatistics(event.getPlanId());

            log.info("订阅过期事件处理完成: subscriptionId={}", event.getSubscriptionId());

        } catch (Exception e) {
            log.error("处理订阅过期事件失败: subscriptionId={}, error={}", 
                    event.getSubscriptionId(), e.getMessage(), e);
        }
    }

    /**
     * 监听订阅取消事件
     */
    @EventListener
    @Async("subscriptionExecutor")
    public void handleSubscriptionCancelled(SubscriptionCancelledEvent event) {
        log.info("处理订阅取消事件: subscriptionId={}, groupId={}, reason={}", 
                event.getSubscriptionId(), event.getGroupId(), event.getCancellationReason());

        try {
            // 1. 发送取消确认通知
            sendCancellationNotification(event);

            // 2. 记录取消原因分析
            analyzeCancellationReason(event);

            // 3. 记录订阅取消日志
            logSubscriptionActivity(event.getSubscriptionId(), "SUBSCRIPTION_CANCELLED", 
                    String.format("订阅已取消，原因: %s", event.getCancellationReason()));

            // 4. 更新套餐统计
            subscriptionPlanService.updatePlanStatistics(event.getPlanId());

            // 5. 触发挽回流程（如果适用）
            triggerRetentionProcess(event);

            log.info("订阅取消事件处理完成: subscriptionId={}", event.getSubscriptionId());

        } catch (Exception e) {
            log.error("处理订阅取消事件失败: subscriptionId={}, error={}", 
                    event.getSubscriptionId(), e.getMessage(), e);
        }
    }

    /**
     * 发送欢迎通知
     */
    private void sendWelcomeNotification(SubscriptionCreatedEvent event) {
        try {
            // TODO: 集成邮件/短信/推送通知服务
            log.info("发送欢迎通知: groupId={}, planName={}", event.getGroupId(), event.getPlanName());
            
            // 这里可以集成具体的通知服务
            // notificationService.sendWelcomeEmail(event.getUserId(), event.getPlanName());
            // notificationService.sendWelcomePush(event.getGroupId(), event.getPlanName());
            
        } catch (Exception e) {
            log.error("发送欢迎通知失败: groupId={}, error={}", event.getGroupId(), e.getMessage());
        }
    }

    /**
     * 发送过期通知
     */
    private void sendExpirationNotification(SubscriptionExpiredEvent event) {
        try {
            // TODO: 集成邮件/短信/推送通知服务
            log.info("发送过期通知: groupId={}, expiredAt={}", event.getGroupId(), event.getExpiredAt());
            
            // 这里可以集成具体的通知服务
            // notificationService.sendExpirationEmail(event.getUserId(), event.getExpiredAt());
            // notificationService.sendExpirationPush(event.getGroupId(), event.getExpiredAt());
            
        } catch (Exception e) {
            log.error("发送过期通知失败: groupId={}, error={}", event.getGroupId(), e.getMessage());
        }
    }

    /**
     * 发送取消确认通知
     */
    private void sendCancellationNotification(SubscriptionCancelledEvent event) {
        try {
            // TODO: 集成邮件/短信/推送通知服务
            log.info("发送取消确认通知: groupId={}, reason={}", event.getGroupId(), event.getCancellationReason());
            
            // 这里可以集成具体的通知服务
            // notificationService.sendCancellationEmail(event.getUserId(), event.getCancellationReason());
            
        } catch (Exception e) {
            log.error("发送取消确认通知失败: groupId={}, error={}", event.getGroupId(), e.getMessage());
        }
    }

    /**
     * 降级群组权限
     */
    private void downgradeGroupPermissions(Long groupId) {
        try {
            // TODO: 实现权限降级逻辑
            log.info("降级群组权限: groupId={}", groupId);
            
            // 这里可以实现具体的权限降级逻辑
            // groupService.downgradeToTrialPlan(groupId);
            
        } catch (Exception e) {
            log.error("降级群组权限失败: groupId={}, error={}", groupId, e.getMessage());
        }
    }

    /**
     * 清理临时数据
     */
    private void cleanupTemporaryData(Long groupId) {
        try {
            log.info("清理临时数据: groupId={}", groupId);
            
            // 这里可以实现具体的数据清理逻辑
            // 例如：清理缓存、临时文件、过期的导出数据等
            
        } catch (Exception e) {
            log.error("清理临时数据失败: groupId={}, error={}", groupId, e.getMessage());
        }
    }

    /**
     * 分析取消原因
     */
    private void analyzeCancellationReason(SubscriptionCancelledEvent event) {
        try {
            log.info("分析取消原因: subscriptionId={}, reason={}", 
                    event.getSubscriptionId(), event.getCancellationReason());
            
            // TODO: 实现取消原因分析逻辑
            // 可以用于改进产品和服务
            // analyticsService.recordCancellationReason(event.getCancellationReason(), event.getPlanId());
            
        } catch (Exception e) {
            log.error("分析取消原因失败: subscriptionId={}, error={}", 
                    event.getSubscriptionId(), e.getMessage());
        }
    }

    /**
     * 触发挽回流程
     */
    private void triggerRetentionProcess(SubscriptionCancelledEvent event) {
        try {
            // 只对高价值客户触发挽回流程
            SubscriptionDO subscription = subscriptionService.getById(event.getSubscriptionId());
            if (subscription != null && subscription.getAmountPaid().compareTo(java.math.BigDecimal.valueOf(50)) > 0) {
                log.info("触发挽回流程: subscriptionId={}, amount={}", 
                        event.getSubscriptionId(), subscription.getAmountPaid());
                
                // TODO: 实现客户挽回逻辑
                // retentionService.startRetentionProcess(event.getUserId(), event.getCancellationReason());
            }
            
        } catch (Exception e) {
            log.error("触发挽回流程失败: subscriptionId={}, error={}", 
                    event.getSubscriptionId(), e.getMessage());
        }
    }

    /**
     * 记录订阅活动日志
     */
    private void logSubscriptionActivity(Long subscriptionId, String activityType, String description) {
        try {
            log.info("记录订阅活动: subscriptionId={}, type={}, description={}", 
                    subscriptionId, activityType, description);
            
            // TODO: 实现活动日志记录
            // activityLogService.log(subscriptionId, activityType, description);
            
        } catch (Exception e) {
            log.error("记录订阅活动失败: subscriptionId={}, error={}", subscriptionId, e.getMessage());
        }
    }
}
