package top.continew.admin.accounting.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.accounting.model.entity.SubscriptionPlanDO;
import top.continew.starter.extension.crud.mapper.BaseMapper;

import java.util.List;

/**
 * 订阅套餐 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface SubscriptionPlanMapper extends BaseMapper<SubscriptionPlanDO> {

    /**
     * 查询启用的套餐列表
     */
    List<SubscriptionPlanDO> selectEnabledPlans();

    /**
     * 根据代码查询套餐
     */
    SubscriptionPlanDO selectByCode(@Param("code") String code);

    /**
     * 查询热门套餐
     */
    List<SubscriptionPlanDO> selectPopularPlans();

    /**
     * 更新套餐状态
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 批量更新排序
     */
    int batchUpdateSortOrder(@Param("plans") List<SubscriptionPlanDO> plans);
}
