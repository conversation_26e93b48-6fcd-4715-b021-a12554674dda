package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.accounting.enums.FileAccessTypeEnum;
import top.continew.admin.accounting.enums.FileProcessStatusEnum;
import top.continew.admin.accounting.enums.FileStorageTypeEnum;
import top.continew.admin.common.base.model.query.BaseQuery;
import top.continew.admin.system.enums.FileTypeEnum;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 文件查询请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "文件查询请求")
public class FileQueryReq extends BaseQuery {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 文件名称
     */
    @Schema(description = "文件名称", example = "receipt.jpg")
    private String fileName;

    /**
     * 文件类型
     */
    @Schema(description = "文件类型", example = "IMAGE")
    private FileTypeEnum fileType;

    /**
     * 存储类型
     */
    @Schema(description = "存储类型", example = "ALIYUN_OSS")
    private FileStorageTypeEnum storageType;

    /**
     * 访问权限类型
     */
    @Schema(description = "访问权限类型", example = "PRIVATE")
    private FileAccessTypeEnum accessType;

    /**
     * 处理状态
     */
    @Schema(description = "处理状态", example = "COMPLETED")
    private FileProcessStatusEnum processStatus;

    /**
     * 关联业务类型
     */
    @Schema(description = "关联业务类型", example = "TRANSACTION")
    private String businessType;

    /**
     * 关联业务ID
     */
    @Schema(description = "关联业务ID", example = "123456")
    private String businessId;

    /**
     * 文件标签
     */
    @Schema(description = "文件标签", example = "receipt")
    private String fileTags;

    /**
     * 是否为临时文件
     */
    @Schema(description = "是否为临时文件", example = "false")
    private Boolean isTemporary;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 上传用户ID
     */
    @Schema(description = "上传用户ID", example = "1")
    private Long uploadUserId;

    /**
     * 最小文件大小
     */
    @Schema(description = "最小文件大小（字节）", example = "1024")
    private Long minFileSize;

    /**
     * 最大文件大小
     */
    @Schema(description = "最大文件大小（字节）", example = "10485760")
    private Long maxFileSize;

    /**
     * 上传开始时间
     */
    @Schema(description = "上传开始时间")
    private LocalDateTime uploadStartTime;

    /**
     * 上传结束时间
     */
    @Schema(description = "上传结束时间")
    private LocalDateTime uploadEndTime;

    /**
     * 是否已删除
     */
    @Schema(description = "是否已删除", example = "false")
    private Boolean isDeleted;

}
