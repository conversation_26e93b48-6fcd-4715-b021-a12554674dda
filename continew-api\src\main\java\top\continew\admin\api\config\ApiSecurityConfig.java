package top.continew.admin.api.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import top.continew.admin.api.filter.ApiAuthenticationFilter;
import top.continew.admin.api.handler.ApiAccessDeniedHandler;
import top.continew.admin.api.handler.ApiAuthenticationEntryPoint;

/**
 * API安全配置
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class ApiSecurityConfig {

    private final ApiAuthenticationFilter apiAuthenticationFilter;
    private final ApiAccessDeniedHandler apiAccessDeniedHandler;
    private final ApiAuthenticationEntryPoint apiAuthenticationEntryPoint;

    /**
     * API安全过滤器链
     */
    @Bean
    @Order(1)
    public SecurityFilterChain apiSecurityFilterChain(HttpSecurity http) throws Exception {
        http
            // 匹配API路径
            .securityMatcher("/api/**")
            
            // 禁用CSRF
            .csrf(csrf -> csrf.disable())
            
            // 无状态会话
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            
            // 配置授权规则
            .authorizeHttpRequests(auth -> auth
                // 公开接口
                .requestMatchers("/api/v1/auth/**").permitAll()
                .requestMatchers("/api/v1/public/**").permitAll()
                .requestMatchers("/api/docs/**").permitAll()
                .requestMatchers("/api/swagger-ui/**").permitAll()
                
                // 需要认证的接口
                .anyRequest().authenticated()
            )
            
            // 添加自定义认证过滤器
            .addFilterBefore(apiAuthenticationFilter, UsernamePasswordAuthenticationFilter.class)
            
            // 异常处理
            .exceptionHandling(ex -> ex
                .authenticationEntryPoint(apiAuthenticationEntryPoint)
                .accessDeniedHandler(apiAccessDeniedHandler)
            );

        return http.build();
    }
}
