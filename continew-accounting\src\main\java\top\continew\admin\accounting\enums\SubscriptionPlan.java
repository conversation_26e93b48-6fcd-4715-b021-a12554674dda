package top.continew.admin.accounting.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.base.IBaseEnum;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 订阅套餐枚举
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Getter
@RequiredArgsConstructor
public enum SubscriptionPlan implements IBaseEnum<String> {

    /**
     * 试用版
     */
    TRIAL("TRIAL", "试用版", BigDecimal.ZERO,
          Map.of(
              "maxGroups", 1,
              "maxTransactionsPerMonth", 50,
              "maxMembers", 5,
              "maxStorageGB", 1,
              "ocrEnabled", false,
              "apiEnabled", false,
              "advancedReports", false,
              "webhookEnabled", false,
              "prioritySupport", false
          )),

    /**
     * 专业版
     */
    PRO("PRO", "专业版", new BigDecimal("9.99"),
        Map.of(
            "maxGroups", 3,
            "maxTransactionsPerMonth", 500,
            "maxMembers", 20,
            "maxStorageGB", 10,
            "ocrEnabled", true,
            "apiEnabled", true,
            "advancedReports", true,
            "webhookEnabled", false,
            "prioritySupport", false
        )),

    /**
     * 商业版
     */
    BUSINESS("BUSINESS", "商业版", new BigDecimal("29.99"),
             Map.of(
                 "maxGroups", 10,
                 "maxTransactionsPerMonth", 2000,
                 "maxMembers", 100,
                 "maxStorageGB", 50,
                 "ocrEnabled", true,
                 "apiEnabled", true,
                 "advancedReports", true,
                 "webhookEnabled", true,
                 "prioritySupport", true
             )),

    /**
     * 企业版
     */
    ENTERPRISE("ENTERPRISE", "企业版", new BigDecimal("99.99"),
               Map.of(
                   "maxGroups", -1, // 无限制
                   "maxTransactionsPerMonth", -1, // 无限制
                   "maxMembers", -1, // 无限制
                   "maxStorageGB", -1, // 无限制
                   "ocrEnabled", true,
                   "apiEnabled", true,
                   "advancedReports", true,
                   "webhookEnabled", true,
                   "prioritySupport", true,
                   "customIntegrations", true,
                   "dedicatedSupport", true
               ));

    private final String value;
    private final String description;
    private final BigDecimal monthlyPrice;
    private final Map<String, Object> features;

    /**
     * 检查是否支持某个功能
     */
    public boolean hasFeature(String featureName) {
        Object value = features.get(featureName);
        return Boolean.TRUE.equals(value);
    }

    /**
     * 获取功能限制值
     */
    public Integer getLimit(String limitName) {
        Object value = features.get(limitName);
        if (value instanceof Integer) {
            return (Integer) value;
        }
        return null;
    }

    /**
     * 是否无限制
     */
    public boolean isUnlimited(String limitName) {
        Integer limit = getLimit(limitName);
        return limit != null && limit == -1;
    }

    /**
     * 检查交易次数限制
     */
    public boolean isTransactionLimitExceeded(int currentCount) {
        Integer maxTransactions = getLimit("maxTransactionsPerMonth");
        return maxTransactions != null && maxTransactions > 0 && currentCount >= maxTransactions;
    }

    /**
     * 检查群组数量限制
     */
    public boolean isGroupLimitExceeded(int currentCount) {
        Integer maxGroups = getLimit("maxGroups");
        return maxGroups != null && maxGroups > 0 && currentCount >= maxGroups;
    }

    /**
     * 检查成员数量限制
     */
    public boolean isMemberLimitExceeded(int currentCount) {
        Integer maxMembers = getLimit("maxMembers");
        return maxMembers != null && maxMembers > 0 && currentCount >= maxMembers;
    }

    /**
     * 检查存储空间限制
     */
    public boolean isStorageLimitExceeded(long currentUsageGB) {
        Integer maxStorageGB = getLimit("maxStorageGB");
        return maxStorageGB != null && maxStorageGB > 0 && currentUsageGB >= maxStorageGB;
    }
}
