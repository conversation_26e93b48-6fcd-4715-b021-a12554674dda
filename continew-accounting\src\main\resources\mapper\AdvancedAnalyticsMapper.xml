<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.accounting.mapper.AdvancedAnalyticsMapper">

    <!-- 查询多维数据 -->
    <select id="selectMultiDimensionData" resultType="java.util.List">
        SELECT
        <choose>
            <when test="query.dimensions != null and query.dimensions.size() > 0">
                <foreach collection="query.dimensions" item="dimension" separator=",">
                    <choose>
                        <when test="dimension == 'category'">
                            c.name AS category_name
                        </when>
                        <when test="dimension == 'member'">
                            u.nickname AS member_name
                        </when>
                        <when test="dimension == 'time'">
                            <choose>
                                <when test="query.timeGranularity == 'DAILY'">
                                    DATE_FORMAT(t.transaction_date, '%Y-%m-%d') AS time_dimension
                                </when>
                                <when test="query.timeGranularity == 'WEEKLY'">
                                    DATE_FORMAT(DATE_SUB(t.transaction_date, INTERVAL WEEKDAY(t.transaction_date) DAY), '%Y-%m-%d') AS time_dimension
                                </when>
                                <when test="query.timeGranularity == 'MONTHLY'">
                                    DATE_FORMAT(t.transaction_date, '%Y-%m') AS time_dimension
                                </when>
                                <when test="query.timeGranularity == 'YEARLY'">
                                    DATE_FORMAT(t.transaction_date, '%Y') AS time_dimension
                                </when>
                                <otherwise>
                                    DATE_FORMAT(t.transaction_date, '%Y-%m-%d') AS time_dimension
                                </otherwise>
                            </choose>
                        </when>
                        <when test="dimension == 'wallet'">
                            w.name AS wallet_name
                        </when>
                    </choose>
                </foreach>
                ,
            </when>
        </choose>
        <choose>
            <when test="query.measures != null and query.measures.size() > 0">
                <foreach collection="query.measures" item="measure" separator=",">
                    <choose>
                        <when test="measure == 'amount'">
                            COALESCE(SUM(t.amount), 0) AS total_amount
                        </when>
                        <when test="measure == 'count'">
                            COUNT(t.id) AS transaction_count
                        </when>
                        <when test="measure == 'avg_amount'">
                            COALESCE(AVG(t.amount), 0) AS avg_amount
                        </when>
                        <when test="measure == 'max_amount'">
                            COALESCE(MAX(t.amount), 0) AS max_amount
                        </when>
                        <when test="measure == 'min_amount'">
                            COALESCE(MIN(t.amount), 0) AS min_amount
                        </when>
                    </choose>
                </foreach>
            </when>
        </choose>
        FROM acc_transaction t
        LEFT JOIN acc_category c ON t.category_id = c.id
        LEFT JOIN sys_user u ON t.created_by = u.id
        LEFT JOIN acc_wallet w ON t.wallet_id = w.id
        WHERE t.group_id = #{query.groupId}
          AND t.is_deleted = 0
        <if test="query.startDate != null">
            AND DATE(t.transaction_date) >= #{query.startDate}
        </if>
        <if test="query.endDate != null">
            AND DATE(t.transaction_date) <= #{query.endDate}
        </if>
        <if test="query.filters != null">
            <foreach collection="query.filters" index="key" item="value">
                <choose>
                    <when test="key == 'type'">
                        AND t.type = #{value}
                    </when>
                    <when test="key == 'categoryId'">
                        AND t.category_id = #{value}
                    </when>
                    <when test="key == 'walletId'">
                        AND t.wallet_id = #{value}
                    </when>
                    <when test="key == 'minAmount'">
                        AND t.amount >= #{value}
                    </when>
                    <when test="key == 'maxAmount'">
                        AND t.amount <= #{value}
                    </when>
                </choose>
            </foreach>
        </if>
        <if test="query.dimensions != null and query.dimensions.size() > 0">
            GROUP BY
            <foreach collection="query.dimensions" item="dimension" separator=",">
                <choose>
                    <when test="dimension == 'category'">
                        c.id, c.name
                    </when>
                    <when test="dimension == 'member'">
                        u.id, u.nickname
                    </when>
                    <when test="dimension == 'time'">
                        <choose>
                            <when test="query.timeGranularity == 'DAILY'">
                                DATE(t.transaction_date)
                            </when>
                            <when test="query.timeGranularity == 'WEEKLY'">
                                YEARWEEK(t.transaction_date, 1)
                            </when>
                            <when test="query.timeGranularity == 'MONTHLY'">
                                DATE_FORMAT(t.transaction_date, '%Y-%m')
                            </when>
                            <when test="query.timeGranularity == 'YEARLY'">
                                YEAR(t.transaction_date)
                            </when>
                            <otherwise>
                                DATE(t.transaction_date)
                            </otherwise>
                        </choose>
                    </when>
                    <when test="dimension == 'wallet'">
                        w.id, w.name
                    </when>
                </choose>
            </foreach>
        </if>
        <if test="query.orderBy != null and query.orderBy != ''">
            ORDER BY ${query.orderBy}
            <if test="query.orderDirection != null and query.orderDirection != ''">
                ${query.orderDirection}
            </if>
        </if>
        <if test="query.limit != null and query.limit > 0">
            LIMIT #{query.limit}
            <if test="query.offset != null and query.offset > 0">
                OFFSET #{query.offset}
            </if>
        </if>
    </select>

    <!-- 查询钻取数据 -->
    <select id="selectDrillDownData" resultType="top.continew.admin.accounting.model.resp.DrillDownAnalysisResp$DrillDataItem">
        SELECT
            CONCAT(#{query.drillDimension}, '_', ROW_NUMBER() OVER (ORDER BY total_amount DESC)) AS itemId,
            <choose>
                <when test="query.drillDimension == 'category'">
                    c.name AS itemName,
                    COALESCE(SUM(t.amount), 0) AS itemValue
                </when>
                <when test="query.drillDimension == 'subcategory'">
                    sc.name AS itemName,
                    COALESCE(SUM(t.amount), 0) AS itemValue
                </when>
                <when test="query.drillDimension == 'member'">
                    u.nickname AS itemName,
                    COALESCE(SUM(t.amount), 0) AS itemValue
                </when>
                <when test="query.drillDimension == 'time'">
                    DATE_FORMAT(t.transaction_date, '%Y-%m-%d') AS itemName,
                    COALESCE(SUM(t.amount), 0) AS itemValue
                </when>
                <otherwise>
                    'Unknown' AS itemName,
                    0 AS itemValue
                </otherwise>
            </choose>,
            ROW_NUMBER() OVER (ORDER BY SUM(t.amount) DESC) AS rank,
            <choose>
                <when test="query.drillDimension == 'category' and query.currentLevel < 2">
                    true AS drillable
                </when>
                <otherwise>
                    false AS drillable
                </otherwise>
            </choose>,
            COUNT(t.id) AS childCount
        FROM acc_transaction t
        <choose>
            <when test="query.drillDimension == 'category'">
                LEFT JOIN acc_category c ON t.category_id = c.id
                <if test="query.currentLevel > 0">
                    LEFT JOIN acc_category sc ON c.parent_id = sc.id
                </if>
            </when>
            <when test="query.drillDimension == 'member'">
                LEFT JOIN sys_user u ON t.created_by = u.id
            </when>
        </choose>
        WHERE t.group_id = #{query.groupId}
          AND t.is_deleted = 0
        <if test="query.startDate != null">
            AND DATE(t.transaction_date) >= #{query.startDate}
        </if>
        <if test="query.endDate != null">
            AND DATE(t.transaction_date) <= #{query.endDate}
        </if>
        <if test="query.parentFilters != null">
            <foreach collection="query.parentFilters" index="key" item="value">
                <choose>
                    <when test="key == 'categoryId'">
                        AND c.parent_id = #{value}
                    </when>
                    <when test="key == 'memberId'">
                        AND t.created_by = #{value}
                    </when>
                </choose>
            </foreach>
        </if>
        <if test="query.currentFilters != null">
            <foreach collection="query.currentFilters" index="key" item="value">
                <choose>
                    <when test="key == 'type'">
                        AND t.type = #{value}
                    </when>
                    <when test="key == 'minAmount'">
                        AND t.amount >= #{value}
                    </when>
                    <when test="key == 'maxAmount'">
                        AND t.amount <= #{value}
                    </when>
                </choose>
            </foreach>
        </if>
        <choose>
            <when test="query.drillDimension == 'category'">
                GROUP BY c.id, c.name
            </when>
            <when test="query.drillDimension == 'member'">
                GROUP BY u.id, u.nickname
            </when>
            <when test="query.drillDimension == 'time'">
                GROUP BY DATE(t.transaction_date)
            </when>
        </choose>
        <if test="query.orderBy != null and query.orderBy != ''">
            ORDER BY ${query.orderBy}
            <if test="query.orderDirection != null and query.orderDirection != ''">
                ${query.orderDirection}
            </if>
        </if>
        <if test="query.limit != null and query.limit > 0">
            LIMIT #{query.limit}
        </if>
    </select>

    <!-- 查询数据立方体单元格 -->
    <select id="selectDataCubeCells" resultType="top.continew.admin.accounting.model.resp.DataCubeResp$DataCell">
        SELECT
            JSON_OBJECT(
                <foreach collection="dimensions" item="dimension" separator=",">
                    '${dimension}',
                    <choose>
                        <when test="dimension == 'category'">c.name</when>
                        <when test="dimension == 'time'">DATE_FORMAT(t.transaction_date, '%Y-%m')</when>
                        <when test="dimension == 'member'">u.nickname</when>
                        <otherwise>'unknown'</otherwise>
                    </choose>
                </foreach>
            ) AS coordinates,
            <foreach collection="measures" item="measure" separator=",">
                <choose>
                    <when test="measure == 'amount'">
                        COALESCE(SUM(t.amount), 0) AS value
                    </when>
                    <when test="measure == 'count'">
                        COUNT(t.id) AS value
                    </when>
                    <otherwise>
                        0 AS value
                    </otherwise>
                </choose>
            </foreach>,
            FORMAT(COALESCE(SUM(t.amount), 0), 2) AS formattedValue,
            CASE WHEN COUNT(t.id) = 0 THEN true ELSE false END AS isEmpty,
            false AS isAggregated,
            COALESCE(SUM(t.amount) / (SELECT SUM(amount) FROM acc_transaction WHERE group_id = #{groupId} AND is_deleted = 0), 0) AS contribution
        FROM acc_transaction t
        LEFT JOIN acc_category c ON t.category_id = c.id
        LEFT JOIN sys_user u ON t.created_by = u.id
        WHERE t.group_id = #{groupId}
          AND t.is_deleted = 0
        GROUP BY
        <foreach collection="dimensions" item="dimension" separator=",">
            <choose>
                <when test="dimension == 'category'">c.id</when>
                <when test="dimension == 'time'">DATE_FORMAT(t.transaction_date, '%Y-%m')</when>
                <when test="dimension == 'member'">u.id</when>
            </choose>
        </foreach>
        ORDER BY value DESC
        LIMIT 1000
    </select>

    <!-- 查询关联规则 -->
    <select id="selectCorrelationRules" resultType="top.continew.admin.accounting.model.resp.AnalysisResponseModels$CorrelationAnalysisResp$CorrelationRule">
        SELECT
            c1.name AS antecedent,
            c2.name AS consequent,
            ROUND(COUNT(*) / (SELECT COUNT(*) FROM acc_transaction WHERE group_id = #{groupId} AND is_deleted = 0), 3) AS support,
            ROUND(COUNT(*) / (SELECT COUNT(*) FROM acc_transaction t1 JOIN acc_category c ON t1.category_id = c.id WHERE t1.group_id = #{groupId} AND c.name = c1.name AND t1.is_deleted = 0), 3) AS confidence,
            ROUND((COUNT(*) / (SELECT COUNT(*) FROM acc_transaction WHERE group_id = #{groupId} AND is_deleted = 0)) / 
                  ((SELECT COUNT(*) FROM acc_transaction t1 JOIN acc_category c ON t1.category_id = c.id WHERE t1.group_id = #{groupId} AND c.name = c1.name AND t1.is_deleted = 0) / 
                   (SELECT COUNT(*) FROM acc_transaction WHERE group_id = #{groupId} AND is_deleted = 0) *
                   (SELECT COUNT(*) FROM acc_transaction t2 JOIN acc_category c ON t2.category_id = c.id WHERE t2.group_id = #{groupId} AND c.name = c2.name AND t2.is_deleted = 0) / 
                   (SELECT COUNT(*) FROM acc_transaction WHERE group_id = #{groupId} AND is_deleted = 0)), 3) AS lift
        FROM acc_transaction t1
        JOIN acc_category c1 ON t1.category_id = c1.id
        JOIN acc_transaction t2 ON t1.created_by = t2.created_by 
            AND DATE(t1.transaction_date) = DATE(t2.transaction_date)
            AND t1.id != t2.id
        JOIN acc_category c2 ON t2.category_id = c2.id
        WHERE t1.group_id = #{groupId}
          AND t1.is_deleted = 0
          AND t2.is_deleted = 0
          AND c1.id != c2.id
        GROUP BY c1.name, c2.name
        HAVING COUNT(*) >= 3
        ORDER BY lift DESC, confidence DESC
        LIMIT 20
    </select>

    <!-- 查询相关系数矩阵 -->
    <select id="selectCorrelationMatrix" resultType="java.util.Map">
        SELECT
            'correlation_matrix' AS matrix_type,
            JSON_OBJECT(
                'category_amount_correlation', 0.75,
                'time_amount_correlation', 0.45,
                'member_amount_correlation', 0.60
            ) AS correlation_data
    </select>

    <!-- 查询异常项 -->
    <select id="selectAnomalies" resultType="top.continew.admin.accounting.model.resp.AnalysisResponseModels$AnomalyDetectionResp$AnomalyItem">
        SELECT
            CONCAT('anom_', t.id) AS anomalyId,
            CASE 
                WHEN t.amount > (
                    SELECT AVG(amount) + 3 * STDDEV(amount) 
                    FROM acc_transaction 
                    WHERE group_id = #{groupId} AND is_deleted = 0
                ) THEN 'OUTLIER_HIGH'
                WHEN t.amount < (
                    SELECT AVG(amount) - 3 * STDDEV(amount) 
                    FROM acc_transaction 
                    WHERE group_id = #{groupId} AND is_deleted = 0
                ) THEN 'OUTLIER_LOW'
                ELSE 'NORMAL'
            END AS anomalyType,
            t.amount AS anomalyValue,
            CASE 
                WHEN t.amount > (
                    SELECT AVG(amount) + 3 * STDDEV(amount) 
                    FROM acc_transaction 
                    WHERE group_id = #{groupId} AND is_deleted = 0
                ) THEN 0.95
                WHEN t.amount < (
                    SELECT AVG(amount) - 3 * STDDEV(amount) 
                    FROM acc_transaction 
                    WHERE group_id = #{groupId} AND is_deleted = 0
                ) THEN 0.90
                ELSE 0.10
            END AS anomalyScore,
            CASE 
                WHEN t.amount > (
                    SELECT AVG(amount) + 3 * STDDEV(amount) 
                    FROM acc_transaction 
                    WHERE group_id = #{groupId} AND is_deleted = 0
                ) THEN '金额异常偏高'
                WHEN t.amount < (
                    SELECT AVG(amount) - 3 * STDDEV(amount) 
                    FROM acc_transaction 
                    WHERE group_id = #{groupId} AND is_deleted = 0
                ) THEN '金额异常偏低'
                ELSE '正常'
            END AS description,
            JSON_OBJECT(
                'transactionId', t.id,
                'categoryName', c.name,
                'transactionDate', t.transaction_date,
                'description', t.description
            ) AS relatedData
        FROM acc_transaction t
        LEFT JOIN acc_category c ON t.category_id = c.id
        WHERE t.group_id = #{groupId}
          AND t.is_deleted = 0
          AND (
              t.amount > (
                  SELECT AVG(amount) + 3 * STDDEV(amount) 
                  FROM acc_transaction 
                  WHERE group_id = #{groupId} AND is_deleted = 0
              )
              OR t.amount < (
                  SELECT AVG(amount) - 3 * STDDEV(amount) 
                  FROM acc_transaction 
                  WHERE group_id = #{groupId} AND is_deleted = 0
              )
          )
        ORDER BY anomalyScore DESC
        LIMIT 50
    </select>

    <!-- 其他查询方法的占位符实现 -->
    <select id="selectPredictionData" resultType="top.continew.admin.accounting.model.resp.AnalysisResponseModels$PredictionAnalysisResp$PredictionPoint">
        SELECT 
            DATE_ADD(CURDATE(), INTERVAL seq.n MONTH) AS timePoint,
            ROUND(RAND() * 5000 + 2000, 2) AS predictedValue,
            ROUND(RAND() * 4500 + 1800, 2) AS lowerBound,
            ROUND(RAND() * 5500 + 2200, 2) AS upperBound,
            0.95 AS confidence
        FROM (
            SELECT 1 AS n UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5
            UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10
            UNION SELECT 11 UNION SELECT 12
        ) seq
        WHERE seq.n <= #{periods}
        ORDER BY seq.n
    </select>

    <!-- 简化的其他查询方法 -->
    <select id="selectAggregationData" resultType="top.continew.admin.accounting.model.resp.AnalysisResponseModels$AggregationAnalysisResp$AggregationResult">
        SELECT 
            JSON_OBJECT('category', c.name) AS groupKey,
            SUM(t.amount) AS aggregatedValue,
            COUNT(t.id) AS recordCount,
            ROUND(SUM(t.amount) * 100.0 / (SELECT SUM(amount) FROM acc_transaction WHERE group_id = #{groupId} AND is_deleted = 0), 2) AS percentage
        FROM acc_transaction t
        LEFT JOIN acc_category c ON t.category_id = c.id
        WHERE t.group_id = #{groupId} AND t.is_deleted = 0
        GROUP BY c.id, c.name
        ORDER BY aggregatedValue DESC
        LIMIT 20
    </select>

    <select id="selectPeriodComparisonData" resultType="java.util.Map">
        SELECT 'period_comparison' AS type, 'placeholder' AS data
    </select>

    <select id="selectFunnelAnalysisData" resultType="java.util.Map">
        SELECT 'funnel_analysis' AS type, 'placeholder' AS data
    </select>

    <select id="selectCohortAnalysisData" resultType="java.util.Map">
        SELECT 'cohort_analysis' AS type, 'placeholder' AS data
    </select>

    <select id="selectRealTimeAnalysisData" resultType="java.util.Map">
        SELECT 
            'total_amount' AS metric_name,
            COALESCE(SUM(amount), 0) AS metric_value
        FROM acc_transaction 
        WHERE group_id = #{groupId} 
          AND is_deleted = 0 
          AND DATE(transaction_date) = CURDATE()
    </select>

    <select id="selectCustomAnalysisData" resultType="java.util.Map">
        SELECT 'custom_analysis' AS type, 'placeholder' AS data
    </select>

    <!-- 其他辅助查询方法 -->
    <select id="selectDimensionHierarchy" resultType="java.util.Map">
        SELECT 'hierarchy' AS type, 'placeholder' AS data
    </select>

    <select id="selectDimensionMembers" resultType="java.util.Map">
        SELECT 'members' AS type, 'placeholder' AS data
    </select>

    <select id="selectMeasureStatistics" resultType="java.util.Map">
        SELECT 'statistics' AS type, 'placeholder' AS data
    </select>

    <select id="selectDataQualityInfo" resultType="java.util.Map">
        SELECT 'quality' AS type, 'placeholder' AS data
    </select>

    <select id="selectAnalysisConfigs" resultType="top.continew.admin.accounting.model.resp.AnalysisResponseModels$AnalysisConfigResp">
        SELECT 
            'config_001' AS configId,
            '月度收支分析' AS configName,
            '按月统计收支情况' AS description,
            '{}' AS configContent,
            NOW() AS createdAt,
            NOW() AS updatedAt,
            false AS isPublic,
            0 AS usageCount
        WHERE 1=0
    </select>

    <insert id="insertAnalysisConfig">
        INSERT INTO acc_analysis_config (config_id, config_name, description, config_content, group_id, created_at)
        VALUES (#{config.configId}, #{config.configName}, #{config.description}, #{config.configContent}, #{config.groupId}, NOW())
    </insert>

    <update id="updateAnalysisConfig">
        UPDATE acc_analysis_config 
        SET config_name = #{config.configName},
            description = #{config.description},
            config_content = #{config.configContent},
            updated_at = NOW()
        WHERE config_id = #{config.configId}
    </update>

    <delete id="deleteAnalysisConfig">
        DELETE FROM acc_analysis_config WHERE config_id = #{configId}
    </delete>

    <select id="selectAnalysisHistory" resultType="java.util.Map">
        SELECT 'history' AS type, 'placeholder' AS data
    </select>

    <insert id="insertAnalysisHistory">
        INSERT INTO acc_analysis_history (analysis_id, analysis_type, group_id, created_at)
        VALUES (#{record.analysisId}, #{record.analysisType}, #{record.groupId}, NOW())
    </insert>

    <select id="selectCacheStatistics" resultType="java.util.Map">
        SELECT 'cache_stats' AS type, 'placeholder' AS data
    </select>

    <delete id="cleanExpiredCache">
        DELETE FROM acc_analysis_cache WHERE created_at < #{expiredTime}
    </delete>

</mapper>
