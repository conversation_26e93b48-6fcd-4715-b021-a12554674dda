package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Zapier配置响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "Zapier配置响应")
public class ZapierConfigResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Schema(description = "ID", example = "1")
    private Long id;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID", example = "1")
    private Long groupId;

    /**
     * 群组名称
     */
    @Schema(description = "群组名称", example = "我的记账群")
    private String groupName;

    /**
     * 配置名称
     */
    @Schema(description = "配置名称", example = "交易同步到Google Sheets")
    private String name;

    /**
     * 配置描述
     */
    @Schema(description = "配置描述", example = "自动将新交易同步到Google Sheets表格")
    private String description;

    /**
     * Webhook URL
     */
    @Schema(description = "Webhook URL", example = "https://hooks.zapier.com/hooks/catch/123456/abcdef/")
    private String webhookUrl;

    /**
     * 触发器类型
     */
    @Schema(description = "触发器类型", example = "TRANSACTION_CREATED")
    private String triggerType;

    /**
     * 触发器类型名称
     */
    @Schema(description = "触发器类型名称", example = "交易创建")
    private String triggerTypeName;

    /**
     * 触发条件
     */
    @Schema(description = "触发条件")
    private Map<String, Object> triggerConditions;

    /**
     * 数据映射配置
     */
    @Schema(description = "数据映射配置")
    private DataMappingInfo dataMapping;

    /**
     * 过滤规则
     */
    @Schema(description = "过滤规则")
    private List<FilterRuleInfo> filterRules;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    /**
     * 状态
     */
    @Schema(description = "状态", example = "ACTIVE")
    private String status;

    /**
     * 状态名称
     */
    @Schema(description = "状态名称", example = "活跃")
    private String statusName;

    /**
     * 最后触发时间
     */
    @Schema(description = "最后触发时间", example = "2025-01-01T12:00:00")
    private LocalDateTime lastTriggeredAt;

    /**
     * 触发次数
     */
    @Schema(description = "触发次数", example = "150")
    private Long triggerCount;

    /**
     * 成功次数
     */
    @Schema(description = "成功次数", example = "145")
    private Long successCount;

    /**
     * 失败次数
     */
    @Schema(description = "失败次数", example = "5")
    private Long failureCount;

    /**
     * 成功率
     */
    @Schema(description = "成功率", example = "96.67")
    private Double successRate;

    /**
     * 最后错误信息
     */
    @Schema(description = "最后错误信息", example = "连接超时")
    private String lastError;

    /**
     * 最后错误时间
     */
    @Schema(description = "最后错误时间", example = "2025-01-01T11:30:00")
    private LocalDateTime lastErrorAt;

    /**
     * 最大重试次数
     */
    @Schema(description = "最大重试次数", example = "3")
    private Integer maxRetries;

    /**
     * 超时时间（秒）
     */
    @Schema(description = "超时时间（秒）", example = "30")
    private Integer timeoutSeconds;

    /**
     * 请求头配置
     */
    @Schema(description = "请求头配置")
    private Map<String, String> headers;

    /**
     * 认证配置
     */
    @Schema(description = "认证配置")
    private AuthConfigInfo authConfig;

    /**
     * 标签
     */
    @Schema(description = "标签", example = "['自动化', '同步']")
    private List<String> tags;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "用于自动化工作流")
    private String remark;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID", example = "1")
    private Long createUserId;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名", example = "张三")
    private String createUserName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-01-01T10:00:00")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2025-01-01T12:00:00")
    private LocalDateTime updateTime;

    /**
     * 数据映射信息
     */
    @Data
    @Schema(description = "数据映射信息")
    public static class DataMappingInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 字段映射
         */
        @Schema(description = "字段映射")
        private Map<String, String> fieldMapping;

        /**
         * 数据转换规则
         */
        @Schema(description = "数据转换规则")
        private Map<String, String> transformRules;

        /**
         * 默认值
         */
        @Schema(description = "默认值")
        private Map<String, Object> defaultValues;

        /**
         * 是否包含元数据
         */
        @Schema(description = "是否包含元数据", example = "true")
        private Boolean includeMetadata;

        /**
         * 日期格式
         */
        @Schema(description = "日期格式", example = "yyyy-MM-dd HH:mm:ss")
        private String dateFormat;

        /**
         * 数字格式
         */
        @Schema(description = "数字格式", example = "#,##0.00")
        private String numberFormat;
    }

    /**
     * 过滤规则信息
     */
    @Data
    @Schema(description = "过滤规则信息")
    public static class FilterRuleInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 字段名
         */
        @Schema(description = "字段名", example = "amount")
        private String field;

        /**
         * 字段显示名
         */
        @Schema(description = "字段显示名", example = "金额")
        private String fieldName;

        /**
         * 操作符
         */
        @Schema(description = "操作符", example = ">=")
        private String operator;

        /**
         * 操作符显示名
         */
        @Schema(description = "操作符显示名", example = "大于等于")
        private String operatorName;

        /**
         * 值
         */
        @Schema(description = "值", example = "100")
        private Object value;

        /**
         * 逻辑操作符
         */
        @Schema(description = "逻辑操作符", example = "AND")
        private String logicOperator;
    }

    /**
     * 认证配置信息
     */
    @Data
    @Schema(description = "认证配置信息")
    public static class AuthConfigInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 认证类型
         */
        @Schema(description = "认证类型", example = "API_KEY")
        private String type;

        /**
         * 认证类型名称
         */
        @Schema(description = "认证类型名称", example = "API密钥")
        private String typeName;

        /**
         * 是否已配置
         */
        @Schema(description = "是否已配置", example = "true")
        private Boolean configured;

        /**
         * 配置状态
         */
        @Schema(description = "配置状态", example = "VALID")
        private String status;
    }
}
