package top.continew.admin.accounting.service.sync;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import top.continew.starter.core.exception.BusinessException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据源适配器工厂
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataSourceAdapterFactory implements InitializingBean {

    private final List<DataSourceAdapter> adapters;
    private final Map<String, DataSourceAdapter> adapterMap = new HashMap<>();

    @Override
    public void afterPropertiesSet() {
        // 注册所有适配器
        for (DataSourceAdapter adapter : adapters) {
            String adapterType = adapter.getAdapterType();
            adapterMap.put(adapterType, adapter);
            log.info("注册数据源适配器: {}", adapterType);
        }
        
        log.info("数据源适配器工厂初始化完成，共注册 {} 个适配器", adapterMap.size());
    }

    /**
     * 获取适配器
     *
     * @param adapterType 适配器类型
     * @return 适配器实例
     */
    public DataSourceAdapter getAdapter(String adapterType) {
        DataSourceAdapter adapter = adapterMap.get(adapterType);
        if (adapter == null) {
            throw new BusinessException("不支持的数据源类型: " + adapterType);
        }
        return adapter;
    }

    /**
     * 获取所有支持的适配器类型
     *
     * @return 适配器类型列表
     */
    public Map<String, String> getSupportedAdapterTypes() {
        Map<String, String> types = new HashMap<>();
        types.put("DATABASE", "数据库");
        types.put("GOOGLE_SHEETS", "Google Sheets");
        types.put("API", "REST API");
        types.put("WEBHOOK", "Webhook");
        return types;
    }

    /**
     * 检查是否支持指定的适配器类型
     *
     * @param adapterType 适配器类型
     * @return 是否支持
     */
    public boolean isSupported(String adapterType) {
        return adapterMap.containsKey(adapterType);
    }

    /**
     * 获取适配器数量
     *
     * @return 适配器数量
     */
    public int getAdapterCount() {
        return adapterMap.size();
    }
}
