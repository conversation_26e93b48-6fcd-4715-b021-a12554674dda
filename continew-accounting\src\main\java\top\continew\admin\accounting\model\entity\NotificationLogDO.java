package top.continew.admin.accounting.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.data.mybatis.base.BaseEntity;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 通知日志实体
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "acc_notification_log", autoResultMap = true)
public class NotificationLogDO extends BaseEntity {

    /**
     * 通知ID
     */
    private Long notificationId;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 目标用户ID
     */
    private Long targetUserId;

    /**
     * 目标地址（邮箱、手机号、聊天ID等）
     */
    private String targetAddress;

    /**
     * 发送状态
     */
    private String status;

    /**
     * 发送时间
     */
    private LocalDateTime sendTime;

    /**
     * 响应时间
     */
    private LocalDateTime responseTime;

    /**
     * 耗时（毫秒）
     */
    private Long duration;

    /**
     * 第三方消息ID
     */
    private String externalMessageId;

    /**
     * 响应代码
     */
    private String responseCode;

    /**
     * 响应消息
     */
    private String responseMessage;

    /**
     * 响应数据
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> responseData;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 错误堆栈
     */
    private String errorStack;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 发送内容
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private String content;

    /**
     * 扩展信息
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> extraInfo;

    /**
     * 备注
     */
    private String remark;

}
