package top.continew.admin.accounting.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 通知类型枚举
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Getter
@RequiredArgsConstructor
public enum NotificationTypeEnum {

    /**
     * 系统通知
     */
    SYSTEM("SYSTEM", "系统通知"),

    /**
     * 账单通知
     */
    TRANSACTION("TRANSACTION", "账单通知"),

    /**
     * 债务提醒
     */
    DEBT_REMINDER("DEBT_REMINDER", "债务提醒"),

    /**
     * 预算警告
     */
    BUDGET_WARNING("BUDGET_WARNING", "预算警告"),

    /**
     * 订阅通知
     */
    SUBSCRIPTION("SUBSCRIPTION", "订阅通知"),

    /**
     * 报表通知
     */
    REPORT("REPORT", "报表通知"),

    /**
     * 同步通知
     */
    SYNC("SYNC", "同步通知"),

    /**
     * 安全通知
     */
    SECURITY("SECURITY", "安全通知"),

    /**
     * 营销通知
     */
    MARKETING("MARKETING", "营销通知"),

    /**
     * 自定义通知
     */
    CUSTOM("CUSTOM", "自定义通知");

    /**
     * 类型代码
     */
    private final String code;

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 根据代码获取枚举
     *
     * @param code 类型代码
     * @return 通知类型枚举
     */
    public static NotificationTypeEnum getByCode(String code) {
        for (NotificationTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 是否为重要通知
     *
     * @return 是否为重要通知
     */
    public boolean isImportant() {
        return this == SECURITY || this == DEBT_REMINDER || this == BUDGET_WARNING;
    }

    /**
     * 是否为业务通知
     *
     * @return 是否为业务通知
     */
    public boolean isBusiness() {
        return this == TRANSACTION || this == DEBT_REMINDER || this == BUDGET_WARNING 
               || this == SUBSCRIPTION || this == REPORT || this == SYNC;
    }

}
