package top.continew.admin.accounting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 债务还款请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "债务还款请求")
public class DebtPaymentReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 债务ID
     */
    @Schema(description = "债务ID", example = "1")
    @NotNull(message = "债务ID不能为空")
    private Long debtId;

    /**
     * 还款金额
     */
    @Schema(description = "还款金额", example = "100.00")
    @NotNull(message = "还款金额不能为空")
    @DecimalMin(value = "0.01", message = "还款金额必须大于0")
    @Digits(integer = 13, fraction = 2, message = "还款金额格式不正确")
    private BigDecimal amount;

    /**
     * 币种
     */
    @Schema(description = "币种", example = "CNY")
    @NotBlank(message = "币种不能为空")
    @Size(max = 10, message = "币种长度不能超过10个字符")
    private String currency;

    /**
     * 还款方式
     */
    @Schema(description = "还款方式", example = "CASH")
    @NotBlank(message = "还款方式不能为空")
    @Size(max = 20, message = "还款方式长度不能超过20个字符")
    private String paymentMethod;

    /**
     * 还款时间
     */
    @Schema(description = "还款时间", example = "2025-01-15 10:30:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime paymentDate;

    /**
     * 关联交易ID
     */
    @Schema(description = "关联交易ID", example = "1")
    private Long transactionId;

    /**
     * 还款类型
     */
    @Schema(description = "还款类型", example = "PARTIAL")
    @NotBlank(message = "还款类型不能为空")
    @Size(max = 20, message = "还款类型长度不能超过20个字符")
    private String paymentType;

    /**
     * 利息金额
     */
    @Schema(description = "利息金额", example = "5.00")
    @DecimalMin(value = "0", message = "利息金额不能为负数")
    @Digits(integer = 13, fraction = 2, message = "利息金额格式不正确")
    private BigDecimal interestAmount;

    /**
     * 本金金额
     */
    @Schema(description = "本金金额", example = "95.00")
    @DecimalMin(value = "0", message = "本金金额不能为负数")
    @Digits(integer = 13, fraction = 2, message = "本金金额格式不正确")
    private BigDecimal principalAmount;

    /**
     * 手续费
     */
    @Schema(description = "手续费", example = "2.00")
    @DecimalMin(value = "0", message = "手续费不能为负数")
    @Digits(integer = 13, fraction = 2, message = "手续费格式不正确")
    private BigDecimal feeAmount;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "部分还款")
    @Size(max = 1000, message = "备注长度不能超过1000个字符")
    private String remark;

    /**
     * 是否需要确认
     */
    @Schema(description = "是否需要确认", example = "true")
    private Boolean needConfirm = true;

    /**
     * 自动创建交易记录
     */
    @Schema(description = "自动创建交易记录", example = "true")
    private Boolean autoCreateTransaction = false;
}
