package top.continew.admin.bot.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.continew.admin.accounting.enums.PlatformType;
import top.continew.admin.bot.model.dto.BotMessage;
import top.continew.admin.bot.telegram.service.TelegramBotService;
import top.continew.admin.bot.discord.service.DiscordBotService;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 通知服务
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationService {

    private final TelegramBotService telegramBotService;
    private final DiscordBotService discordBotService;
    private final MessageFormatter messageFormatter;

    /**
     * 发送Telegram通知
     */
    public void sendTelegramNotification(BotMessage notification) {
        try {
            String formattedMessage = messageFormatter.formatTelegramMessage(notification.getContent());
            telegramBotService.sendMessage(notification.getChatId(), formattedMessage);
            log.info("Telegram通知发送成功: chatId={}", notification.getChatId());
        } catch (Exception e) {
            log.error("发送Telegram通知失败: {}", notification, e);
            throw new RuntimeException("发送Telegram通知失败", e);
        }
    }

    /**
     * 发送Discord通知
     */
    public void sendDiscordNotification(BotMessage notification) {
        try {
            String formattedMessage = messageFormatter.formatDiscordMessage(notification.getContent());
            discordBotService.sendMessage(notification.getChatId(), formattedMessage);
            log.info("Discord通知发送成功: channelId={}", notification.getChatId());
        } catch (Exception e) {
            log.error("发送Discord通知失败: {}", notification, e);
            throw new RuntimeException("发送Discord通知失败", e);
        }
    }

    /**
     * 发送微信通知
     */
    public void sendWechatNotification(BotMessage notification) {
        try {
            // TODO: 实现微信通知发送
            log.info("微信通知发送成功: chatId={}", notification.getChatId());
        } catch (Exception e) {
            log.error("发送微信通知失败: {}", notification, e);
            throw new RuntimeException("发送微信通知失败", e);
        }
    }

    /**
     * 发送交易确认通知
     */
    public void sendTransactionConfirmation(PlatformType platform, String chatId, Long transactionId, 
                                          String description, String amount, String category) {
        try {
            String message = String.format(
                "✅ 交易记录成功\n\n" +
                "💰 金额: %s\n" +
                "📝 描述: %s\n" +
                "🏷️ 分类: %s\n" +
                "🕐 时间: %s\n" +
                "🆔 交易ID: %s",
                amount, description, category,
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                transactionId
            );

            BotMessage notification = BotMessage.createNotificationMessage(platform, chatId, message);
            sendNotification(notification);
            
        } catch (Exception e) {
            log.error("发送交易确认通知失败", e);
        }
    }

    /**
     * 发送余额提醒
     */
    public void sendBalanceAlert(PlatformType platform, String chatId, String walletName, 
                               String currentBalance, String threshold) {
        try {
            String message = String.format(
                "⚠️ 余额提醒\n\n" +
                "💳 钱包: %s\n" +
                "💰 当前余额: %s\n" +
                "📊 预警阈值: %s\n" +
                "🕐 时间: %s",
                walletName, currentBalance, threshold,
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
            );

            BotMessage notification = BotMessage.createNotificationMessage(platform, chatId, message);
            sendNotification(notification);
            
        } catch (Exception e) {
            log.error("发送余额提醒失败", e);
        }
    }

    /**
     * 发送预算超支提醒
     */
    public void sendBudgetExceededAlert(PlatformType platform, String chatId, String category, 
                                      String spent, String budget, String percentage) {
        try {
            String message = String.format(
                "🚨 预算超支提醒\n\n" +
                "🏷️ 分类: %s\n" +
                "💸 已花费: %s\n" +
                "💰 预算: %s\n" +
                "📊 超支比例: %s\n" +
                "🕐 时间: %s",
                category, spent, budget, percentage,
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
            );

            BotMessage notification = BotMessage.createNotificationMessage(platform, chatId, message);
            sendNotification(notification);
            
        } catch (Exception e) {
            log.error("发送预算超支提醒失败", e);
        }
    }

    /**
     * 发送定期报告
     */
    public void sendPeriodicReport(PlatformType platform, String chatId, String period, 
                                 Map<String, Object> reportData) {
        try {
            StringBuilder message = new StringBuilder();
            message.append("📊 ").append(period).append("财务报告\n\n");
            
            // 收支概览
            if (reportData.containsKey("totalIncome")) {
                message.append("💰 总收入: ").append(reportData.get("totalIncome")).append("\n");
            }
            if (reportData.containsKey("totalExpense")) {
                message.append("💸 总支出: ").append(reportData.get("totalExpense")).append("\n");
            }
            if (reportData.containsKey("netIncome")) {
                message.append("📈 净收入: ").append(reportData.get("netIncome")).append("\n");
            }
            
            message.append("\n");
            
            // 分类统计
            if (reportData.containsKey("categoryStats")) {
                message.append("🏷️ 分类统计:\n");
                @SuppressWarnings("unchecked")
                Map<String, String> categoryStats = (Map<String, String>) reportData.get("categoryStats");
                categoryStats.forEach((category, amount) -> 
                    message.append("  • ").append(category).append(": ").append(amount).append("\n"));
            }
            
            message.append("\n🕐 生成时间: ")
                   .append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            BotMessage notification = BotMessage.createNotificationMessage(platform, chatId, message.toString());
            sendNotification(notification);
            
        } catch (Exception e) {
            log.error("发送定期报告失败", e);
        }
    }

    /**
     * 发送债务提醒
     */
    public void sendDebtReminder(PlatformType platform, String chatId, String debtorName, 
                               String amount, String dueDate, int daysOverdue) {
        try {
            String message;
            if (daysOverdue > 0) {
                message = String.format(
                    "🔴 债务逾期提醒\n\n" +
                    "👤 债务人: %s\n" +
                    "💰 金额: %s\n" +
                    "📅 到期日: %s\n" +
                    "⏰ 逾期天数: %d天\n" +
                    "🕐 提醒时间: %s",
                    debtorName, amount, dueDate, daysOverdue,
                    LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                );
            } else {
                message = String.format(
                    "🟡 债务到期提醒\n\n" +
                    "👤 债务人: %s\n" +
                    "💰 金额: %s\n" +
                    "📅 到期日: %s\n" +
                    "🕐 提醒时间: %s",
                    debtorName, amount, dueDate,
                    LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                );
            }

            BotMessage notification = BotMessage.createNotificationMessage(platform, chatId, message);
            sendNotification(notification);
            
        } catch (Exception e) {
            log.error("发送债务提醒失败", e);
        }
    }

    /**
     * 批量发送通知
     */
    public void sendBulkNotifications(List<BotMessage> notifications) {
        for (BotMessage notification : notifications) {
            try {
                sendNotification(notification);
                // 添加延迟避免频率限制
                Thread.sleep(100);
            } catch (Exception e) {
                log.error("批量发送通知失败: {}", notification, e);
            }
        }
    }

    /**
     * 发送系统维护通知
     */
    public void sendMaintenanceNotification(PlatformType platform, String chatId, 
                                          String startTime, String endTime, String reason) {
        try {
            String message = String.format(
                "🔧 系统维护通知\n\n" +
                "⏰ 维护时间: %s - %s\n" +
                "📝 维护原因: %s\n" +
                "💡 维护期间服务可能暂时不可用，请谅解。\n" +
                "🕐 通知时间: %s",
                startTime, endTime, reason,
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
            );

            BotMessage notification = BotMessage.createSystemMessage(platform, chatId, message);
            sendNotification(notification);
            
        } catch (Exception e) {
            log.error("发送系统维护通知失败", e);
        }
    }

    /**
     * 统一发送通知方法
     */
    private void sendNotification(BotMessage notification) {
        switch (notification.getPlatform()) {
            case TELEGRAM -> sendTelegramNotification(notification);
            case DISCORD -> sendDiscordNotification(notification);
            case WECHAT -> sendWechatNotification(notification);
            default -> log.warn("不支持的平台类型: {}", notification.getPlatform());
        }
    }
}
