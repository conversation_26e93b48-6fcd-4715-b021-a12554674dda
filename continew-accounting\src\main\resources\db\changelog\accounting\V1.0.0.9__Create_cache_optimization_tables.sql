-- 缓存配置表
CREATE TABLE acc_cache_config (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    config_name VARCHAR(100) NOT NULL COMMENT '配置名称',
    config_code VARCHAR(100) NOT NULL COMMENT '配置代码',
    cache_type VARCHAR(20) NOT NULL COMMENT '缓存类型：LOCAL-本地缓存，REMOTE-远程缓存，BOTH-两级缓存，NONE-无缓存',
    cache_strategy VARCHAR(30) NOT NULL COMMENT '缓存策略：WRITE_THROUGH-写穿透，WRITE_BEHIND-写回，WRITE_INVALIDATE-写失效，READ_THROUGH-读穿透，LAZY_LOADING-懒加载，PRELOAD-预加载，SCHEDULED_REFRESH-定时刷新，EVENT_DRIVEN-事件驱动',
    eviction_policy VARCHAR(20) COMMENT '淘汰策略：LRU-最近最少使用，LFU-最少使用频率，FIFO-先进先出，RANDOM-随机，TTL-基于时间，SIZE-基于大小，WEIGHT-基于权重，NONE-无淘汰',
    key_prefix VARCHAR(100) COMMENT '键前缀',
    key_pattern VARCHAR(200) COMMENT '键模式',
    expire_time BIGINT COMMENT '过期时间（秒）',
    local_max_size BIGINT COMMENT '本地缓存最大大小',
    local_expire_time BIGINT COMMENT '本地缓存过期时间（秒）',
    remote_expire_time BIGINT COMMENT '远程缓存过期时间（秒）',
    penetration_protect TINYINT(1) DEFAULT 0 COMMENT '是否启用穿透保护',
    avalanche_protect TINYINT(1) DEFAULT 0 COMMENT '是否启用雪崩保护',
    breakdown_protect TINYINT(1) DEFAULT 0 COMMENT '是否启用击穿保护',
    auto_refresh TINYINT(1) DEFAULT 0 COMMENT '是否启用自动刷新',
    refresh_interval BIGINT COMMENT '刷新间隔（秒）',
    preload_enabled TINYINT(1) DEFAULT 0 COMMENT '是否启用预加载',
    preload_data_source VARCHAR(200) COMMENT '预加载数据源',
    preload_strategy VARCHAR(50) COMMENT '预加载策略',
    statistics_enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用统计',
    statistics_interval BIGINT DEFAULT 300 COMMENT '统计间隔（秒）',
    monitor_enabled TINYINT(1) DEFAULT 0 COMMENT '是否启用监控',
    monitor_thresholds TEXT COMMENT '监控阈值配置（JSON格式）',
    hotspot_detection TINYINT(1) DEFAULT 0 COMMENT '是否启用热点数据识别',
    hotspot_threshold BIGINT COMMENT '热点数据阈值',
    hotspot_time_window BIGINT COMMENT '热点数据时间窗口（秒）',
    status VARCHAR(20) DEFAULT 'ENABLE' COMMENT '状态：ENABLE-启用，DISABLE-禁用',
    priority INT DEFAULT 0 COMMENT '优先级',
    description TEXT COMMENT '描述',
    extend_config TEXT COMMENT '扩展配置（JSON格式）',
    last_applied_time DATETIME COMMENT '最后应用时间',
    applied_count BIGINT DEFAULT 0 COMMENT '应用次数',
    group_id BIGINT COMMENT '群组ID',
    create_user_id BIGINT COMMENT '创建人ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_user_id BIGINT COMMENT '修改人ID',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (id),
    UNIQUE KEY uk_config_code_group (config_code, group_id, deleted),
    KEY idx_cache_type (cache_type),
    KEY idx_cache_strategy (cache_strategy),
    KEY idx_status (status),
    KEY idx_group_id (group_id),
    KEY idx_create_time (create_time),
    KEY idx_last_applied_time (last_applied_time),
    KEY idx_applied_count (applied_count)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='缓存配置表';

-- 缓存统计表
CREATE TABLE acc_cache_statistics (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    cache_name VARCHAR(100) NOT NULL COMMENT '缓存名称',
    cache_type VARCHAR(20) NOT NULL COMMENT '缓存类型',
    cache_key VARCHAR(500) COMMENT '缓存键',
    hit_count BIGINT DEFAULT 0 COMMENT '命中次数',
    miss_count BIGINT DEFAULT 0 COMMENT '未命中次数',
    load_count BIGINT DEFAULT 0 COMMENT '加载次数',
    eviction_count BIGINT DEFAULT 0 COMMENT '淘汰次数',
    hit_rate DECIMAL(5,4) DEFAULT 0.0000 COMMENT '命中率',
    avg_load_time DECIMAL(10,2) DEFAULT 0.00 COMMENT '平均加载时间（毫秒）',
    min_load_time DECIMAL(10,2) DEFAULT 0.00 COMMENT '最小加载时间（毫秒）',
    max_load_time DECIMAL(10,2) DEFAULT 0.00 COMMENT '最大加载时间（毫秒）',
    qps DECIMAL(10,2) DEFAULT 0.00 COMMENT '每秒查询数',
    tps DECIMAL(10,2) DEFAULT 0.00 COMMENT '每秒事务数',
    peak_qps DECIMAL(10,2) DEFAULT 0.00 COMMENT '峰值QPS',
    peak_tps DECIMAL(10,2) DEFAULT 0.00 COMMENT '峰值TPS',
    memory_usage DECIMAL(10,2) DEFAULT 0.00 COMMENT '内存使用率（%）',
    network_in_bytes BIGINT DEFAULT 0 COMMENT '网络入流量（字节）',
    network_out_bytes BIGINT DEFAULT 0 COMMENT '网络出流量（字节）',
    error_count BIGINT DEFAULT 0 COMMENT '错误次数',
    timeout_count BIGINT DEFAULT 0 COMMENT '超时次数',
    exception_count BIGINT DEFAULT 0 COMMENT '异常次数',
    error_rate DECIMAL(5,4) DEFAULT 0.0000 COMMENT '错误率',
    statistic_time DATETIME NOT NULL COMMENT '统计时间',
    group_id BIGINT COMMENT '群组ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (id),
    KEY idx_cache_name (cache_name),
    KEY idx_cache_type (cache_type),
    KEY idx_cache_key (cache_key(255)),
    KEY idx_statistic_time (statistic_time),
    KEY idx_group_id (group_id),
    KEY idx_hit_rate (hit_rate),
    KEY idx_qps (qps),
    KEY idx_error_rate (error_rate),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='缓存统计表';

-- 缓存访问日志表
CREATE TABLE acc_cache_access_log (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    cache_name VARCHAR(100) NOT NULL COMMENT '缓存名称',
    cache_key VARCHAR(500) NOT NULL COMMENT '缓存键',
    operation_type VARCHAR(20) NOT NULL COMMENT '操作类型：GET-获取，PUT-设置，REMOVE-删除，CLEAR-清空',
    hit_status VARCHAR(10) NOT NULL COMMENT '命中状态：HIT-命中，MISS-未命中',
    load_time DECIMAL(10,2) DEFAULT 0.00 COMMENT '加载时间（毫秒）',
    data_size BIGINT DEFAULT 0 COMMENT '数据大小（字节）',
    client_ip VARCHAR(50) COMMENT '客户端IP',
    user_agent VARCHAR(500) COMMENT '用户代理',
    request_id VARCHAR(100) COMMENT '请求ID',
    trace_id VARCHAR(100) COMMENT '链路追踪ID',
    error_message TEXT COMMENT '错误信息',
    access_time DATETIME NOT NULL COMMENT '访问时间',
    group_id BIGINT COMMENT '群组ID',
    user_id BIGINT COMMENT '用户ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id),
    KEY idx_cache_name (cache_name),
    KEY idx_cache_key (cache_key(255)),
    KEY idx_operation_type (operation_type),
    KEY idx_hit_status (hit_status),
    KEY idx_access_time (access_time),
    KEY idx_group_id (group_id),
    KEY idx_user_id (user_id),
    KEY idx_request_id (request_id),
    KEY idx_trace_id (trace_id),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='缓存访问日志表';

-- 插入默认缓存配置
INSERT INTO acc_cache_config (
    config_name, config_code, cache_type, cache_strategy, eviction_policy,
    key_prefix, expire_time, local_max_size, local_expire_time, remote_expire_time,
    penetration_protect, avalanche_protect, breakdown_protect, auto_refresh,
    refresh_interval, preload_enabled, statistics_enabled, statistics_interval,
    monitor_enabled, hotspot_detection, status, priority, description,
    create_user_id, update_user_id
) VALUES 
('用户信息缓存', 'USER_INFO_CACHE', 'BOTH', 'LAZY_LOADING', 'LRU', 
 'USER:', 3600, 10000, 1800, 3600, 1, 1, 1, 1, 
 300, 0, 1, 300, 1, 1, 'ENABLE', 1, '用户基本信息缓存配置',
 1, 1),
('会话信息缓存', 'SESSION_INFO_CACHE', 'REMOTE', 'WRITE_THROUGH', 'TTL',
 'SESSION:', 1800, NULL, NULL, 1800, 1, 1, 1, 0,
 NULL, 0, 1, 300, 1, 0, 'ENABLE', 2, '用户会话信息缓存配置',
 1, 1),
('字典数据缓存', 'DICT_DATA_CACHE', 'LOCAL', 'PRELOAD', 'LFU',
 'DICT:', 86400, 5000, 86400, NULL, 1, 0, 0, 1,
 3600, 1, 1, 600, 0, 0, 'ENABLE', 3, '系统字典数据缓存配置',
 1, 1),
('配置选项缓存', 'CONFIG_OPTION_CACHE', 'BOTH', 'EVENT_DRIVEN', 'LRU',
 'CONFIG:', 7200, 2000, 3600, 7200, 1, 1, 1, 0,
 NULL, 1, 1, 300, 1, 0, 'ENABLE', 4, '系统配置选项缓存配置',
 1, 1);

