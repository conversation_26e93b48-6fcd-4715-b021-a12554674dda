package top.continew.admin.accounting.service;

import top.continew.admin.accounting.model.entity.FileStorageConfigDO;
import top.continew.admin.common.base.service.BaseService;

import java.util.List;
import java.util.Map;

/**
 * 文件存储配置服务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface FileStorageConfigService extends BaseService<FileStorageConfigDO> {

    /**
     * 获取默认存储配置
     *
     * @param groupId 群组ID
     * @return 存储配置
     */
    FileStorageConfigDO getDefaultConfig(Long groupId);

    /**
     * 根据配置代码获取存储配置
     *
     * @param configCode 配置代码
     * @param groupId    群组ID
     * @return 存储配置
     */
    FileStorageConfigDO getConfigByCode(String configCode, Long groupId);

    /**
     * 获取可用的存储配置列表
     *
     * @param groupId 群组ID
     * @return 配置列表
     */
    List<FileStorageConfigDO> getAvailableConfigs(Long groupId);

    /**
     * 测试存储配置连接
     *
     * @param configId 配置ID
     * @return 测试结果
     */
    Map<String, Object> testConnection(Long configId);

    /**
     * 设置默认配置
     *
     * @param configId 配置ID
     * @param groupId  群组ID
     * @return 是否成功
     */
    Boolean setDefaultConfig(Long configId, Long groupId);

    /**
     * 启用/禁用配置
     *
     * @param configId 配置ID
     * @param enabled  是否启用
     * @return 是否成功
     */
    Boolean toggleConfig(Long configId, Boolean enabled);

    /**
     * 获取配置统计信息
     *
     * @param configId 配置ID
     * @return 统计信息
     */
    Map<String, Object> getConfigStatistics(Long configId);

    /**
     * 同步配置到存储引擎
     *
     * @param configId 配置ID
     * @return 是否成功
     */
    Boolean syncConfigToEngine(Long configId);

    /**
     * 验证配置参数
     *
     * @param config 配置信息
     * @return 验证结果
     */
    Map<String, Object> validateConfig(FileStorageConfigDO config);

    /**
     * 获取配置模板
     *
     * @param storageType 存储类型
     * @return 配置模板
     */
    Map<String, Object> getConfigTemplate(String storageType);

}
