package top.continew.admin.accounting.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.accounting.model.query.ReportTemplateQuery;
import top.continew.admin.accounting.model.req.DynamicReportGenerateReq;
import top.continew.admin.accounting.model.req.ReportTemplateCreateReq;
import top.continew.admin.accounting.model.req.ReportTemplateUpdateReq;
import top.continew.admin.accounting.model.resp.DynamicReportResp;
import top.continew.admin.accounting.model.resp.ReportTemplateDetailResp;
import top.continew.admin.accounting.model.resp.ReportTemplateResp;
import top.continew.admin.accounting.service.ReportEngineService;
import top.continew.admin.common.model.resp.R;
import top.continew.admin.common.util.validate.CheckUtils;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 报表引擎控制器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Tag(name = "报表引擎 API")
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/accounting/report-engine")
public class ReportEngineController {

    private final ReportEngineService reportEngineService;

    // ==================== 报表模板管理 ====================

    @Operation(summary = "创建报表模板", description = "创建新的报表模板")
    @SaCheckPermission("accounting:report:template:create")
    @PostMapping("/templates")
    public R<Long> createTemplate(@Valid @RequestBody ReportTemplateCreateReq createReq) {
        Long templateId = reportEngineService.createTemplate(createReq);
        return R.ok("报表模板创建成功", templateId);
    }

    @Operation(summary = "更新报表模板", description = "更新指定的报表模板")
    @SaCheckPermission("accounting:report:template:update")
    @PutMapping("/templates/{templateId}")
    public R<Void> updateTemplate(@Parameter(description = "模板ID") @PathVariable Long templateId,
                                  @Valid @RequestBody ReportTemplateUpdateReq updateReq) {
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        reportEngineService.updateTemplate(templateId, updateReq);
        return R.ok("报表模板更新成功");
    }

    @Operation(summary = "删除报表模板", description = "删除指定的报表模板")
    @SaCheckPermission("accounting:report:template:delete")
    @DeleteMapping("/templates/{templateId}")
    public R<Void> deleteTemplate(@Parameter(description = "模板ID") @PathVariable Long templateId) {
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        reportEngineService.deleteTemplate(templateId);
        return R.ok("报表模板删除成功");
    }

    @Operation(summary = "批量删除报表模板", description = "批量删除多个报表模板")
    @SaCheckPermission("accounting:report:template:delete")
    @DeleteMapping("/templates")
    public R<Void> deleteTemplates(@Parameter(description = "模板ID列表") @RequestBody @NotEmpty List<Long> templateIds) {
        reportEngineService.deleteTemplates(templateIds);
        return R.ok("报表模板批量删除成功");
    }

    @Operation(summary = "获取报表模板详情", description = "获取指定报表模板的详细信息")
    @SaCheckPermission("accounting:report:template:view")
    @GetMapping("/templates/{templateId}")
    public R<ReportTemplateDetailResp> getTemplateDetail(@Parameter(description = "模板ID") @PathVariable Long templateId) {
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        ReportTemplateDetailResp detail = reportEngineService.getTemplateDetail(templateId);
        return R.ok(detail);
    }

    @Operation(summary = "分页查询报表模板", description = "分页查询报表模板列表")
    @SaCheckPermission("accounting:report:template:view")
    @GetMapping("/templates")
    public R<IPage<ReportTemplateResp>> pageTemplates(@Valid ReportTemplateQuery query) {
        IPage<ReportTemplateResp> page = reportEngineService.pageTemplates(query);
        return R.ok(page);
    }

    @Operation(summary = "查询报表模板列表", description = "查询报表模板列表（不分页）")
    @SaCheckPermission("accounting:report:template:view")
    @GetMapping("/templates/list")
    public R<List<ReportTemplateResp>> listTemplates(@Valid ReportTemplateQuery query) {
        List<ReportTemplateResp> list = reportEngineService.listTemplates(query);
        return R.ok(list);
    }

    @Operation(summary = "复制报表模板", description = "复制现有的报表模板")
    @SaCheckPermission("accounting:report:template:create")
    @PostMapping("/templates/{templateId}/copy")
    public R<Long> copyTemplate(@Parameter(description = "源模板ID") @PathVariable Long templateId,
                                @Parameter(description = "新模板名称") @RequestParam String newName) {
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        CheckUtils.throwIfBlank(newName, "新模板名称不能为空");
        Long newTemplateId = reportEngineService.copyTemplate(templateId, newName);
        return R.ok("报表模板复制成功", newTemplateId);
    }

    @Operation(summary = "启用/禁用报表模板", description = "切换报表模板的启用状态")
    @SaCheckPermission("accounting:report:template:update")
    @PutMapping("/templates/{templateId}/toggle-status")
    public R<Void> toggleTemplateStatus(@Parameter(description = "模板ID") @PathVariable Long templateId,
                                        @Parameter(description = "是否启用") @RequestParam Boolean enabled) {
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        CheckUtils.throwIfNull(enabled, "启用状态不能为空");
        reportEngineService.toggleTemplateStatus(templateId, enabled);
        return R.ok("模板状态更新成功");
    }

    @Operation(summary = "发布报表模板", description = "发布报表模板供其他用户使用")
    @SaCheckPermission("accounting:report:template:publish")
    @PutMapping("/templates/{templateId}/publish")
    public R<Void> publishTemplate(@Parameter(description = "模板ID") @PathVariable Long templateId) {
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        reportEngineService.publishTemplate(templateId);
        return R.ok("报表模板发布成功");
    }

    @Operation(summary = "撤销发布报表模板", description = "撤销报表模板的发布状态")
    @SaCheckPermission("accounting:report:template:publish")
    @PutMapping("/templates/{templateId}/unpublish")
    public R<Void> unpublishTemplate(@Parameter(description = "模板ID") @PathVariable Long templateId) {
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        reportEngineService.unpublishTemplate(templateId);
        return R.ok("报表模板撤销发布成功");
    }

    // ==================== 模板版本管理 ====================

    @Operation(summary = "创建模板版本", description = "为模板创建新版本")
    @SaCheckPermission("accounting:report:template:version")
    @PostMapping("/templates/{templateId}/versions")
    public R<String> createTemplateVersion(@Parameter(description = "模板ID") @PathVariable Long templateId,
                                           @Parameter(description = "版本说明") @RequestParam String versionNote) {
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        CheckUtils.throwIfBlank(versionNote, "版本说明不能为空");
        String version = reportEngineService.createTemplateVersion(templateId, versionNote);
        return R.ok("模板版本创建成功", version);
    }

    @Operation(summary = "回滚到指定版本", description = "将模板回滚到指定版本")
    @SaCheckPermission("accounting:report:template:version")
    @PutMapping("/templates/{templateId}/versions/{version}/rollback")
    public R<Void> rollbackToVersion(@Parameter(description = "模板ID") @PathVariable Long templateId,
                                     @Parameter(description = "版本号") @PathVariable String version) {
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        CheckUtils.throwIfBlank(version, "版本号不能为空");
        reportEngineService.rollbackToVersion(templateId, version);
        return R.ok("模板版本回滚成功");
    }

    @Operation(summary = "获取模板版本历史", description = "获取模板的版本历史记录")
    @SaCheckPermission("accounting:report:template:view")
    @GetMapping("/templates/{templateId}/versions")
    public R<List<ReportTemplateDetailResp.VersionHistory>> getTemplateVersionHistory(
            @Parameter(description = "模板ID") @PathVariable Long templateId) {
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        List<ReportTemplateDetailResp.VersionHistory> history = reportEngineService.getTemplateVersionHistory(templateId);
        return R.ok(history);
    }

    @Operation(summary = "比较模板版本", description = "比较两个模板版本的差异")
    @SaCheckPermission("accounting:report:template:view")
    @GetMapping("/templates/{templateId}/versions/compare")
    public R<Map<String, Object>> compareTemplateVersions(@Parameter(description = "模板ID") @PathVariable Long templateId,
                                                          @Parameter(description = "版本1") @RequestParam String version1,
                                                          @Parameter(description = "版本2") @RequestParam String version2) {
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        CheckUtils.throwIfBlank(version1, "版本1不能为空");
        CheckUtils.throwIfBlank(version2, "版本2不能为空");
        Map<String, Object> diff = reportEngineService.compareTemplateVersions(templateId, version1, version2);
        return R.ok(diff);
    }

    // ==================== 动态报表生成 ====================

    @Operation(summary = "生成动态报表", description = "根据模板和参数生成动态报表")
    @SaCheckPermission("accounting:report:generate")
    @PostMapping("/reports/generate")
    public R<DynamicReportResp> generateReport(@Valid @RequestBody DynamicReportGenerateReq generateReq) {
        DynamicReportResp report = reportEngineService.generateReport(generateReq);
        return R.ok("报表生成成功", report);
    }

    @Operation(summary = "异步生成动态报表", description = "异步生成动态报表，返回任务ID")
    @SaCheckPermission("accounting:report:generate")
    @PostMapping("/reports/generate-async")
    public R<String> generateReportAsync(@Valid @RequestBody DynamicReportGenerateReq generateReq) {
        String taskId = reportEngineService.generateReportAsync(generateReq);
        return R.ok("报表生成任务已提交", taskId);
    }

    @Operation(summary = "获取报表生成状态", description = "获取异步报表生成的状态")
    @SaCheckPermission("accounting:report:view")
    @GetMapping("/reports/tasks/{taskId}/status")
    public R<DynamicReportResp> getReportStatus(@Parameter(description = "任务ID") @PathVariable String taskId) {
        CheckUtils.throwIfBlank(taskId, "任务ID不能为空");
        DynamicReportResp status = reportEngineService.getReportStatus(taskId);
        return R.ok(status);
    }

    @Operation(summary = "取消报表生成", description = "取消正在进行的报表生成任务")
    @SaCheckPermission("accounting:report:generate")
    @DeleteMapping("/reports/tasks/{taskId}")
    public R<Void> cancelReportGeneration(@Parameter(description = "任务ID") @PathVariable String taskId) {
        CheckUtils.throwIfBlank(taskId, "任务ID不能为空");
        reportEngineService.cancelReportGeneration(taskId);
        return R.ok("报表生成任务已取消");
    }

    @Operation(summary = "重新生成报表", description = "重新生成已存在的报表")
    @SaCheckPermission("accounting:report:generate")
    @PostMapping("/reports/{reportId}/regenerate")
    public R<DynamicReportResp> regenerateReport(@Parameter(description = "报表ID") @PathVariable Long reportId) {
        CheckUtils.throwIfNull(reportId, "报表ID不能为空");
        DynamicReportResp report = reportEngineService.regenerateReport(reportId);
        return R.ok("报表重新生成成功", report);
    }

    @Operation(summary = "下载报表文件", description = "下载生成的报表文件")
    @SaCheckPermission("accounting:report:download")
    @GetMapping("/reports/{reportId}/download")
    public R<byte[]> downloadReport(@Parameter(description = "报表ID") @PathVariable Long reportId) {
        CheckUtils.throwIfNull(reportId, "报表ID不能为空");
        byte[] fileData = reportEngineService.downloadReport(reportId);
        return R.ok("报表下载成功", fileData);
    }

    @Operation(summary = "获取报表下载URL", description = "获取报表文件的下载链接")
    @SaCheckPermission("accounting:report:view")
    @GetMapping("/reports/{reportId}/download-url")
    public R<String> getReportDownloadUrl(@Parameter(description = "报表ID") @PathVariable Long reportId) {
        CheckUtils.throwIfNull(reportId, "报表ID不能为空");
        String downloadUrl = reportEngineService.getReportDownloadUrl(reportId);
        return R.ok(downloadUrl);
    }

    // ==================== 报表调度管理 ====================

    @Operation(summary = "创建报表调度任务", description = "为模板创建定时调度任务")
    @SaCheckPermission("accounting:report:schedule:create")
    @PostMapping("/templates/{templateId}/schedules")
    public R<String> createReportSchedule(@Parameter(description = "模板ID") @PathVariable Long templateId,
                                          @RequestBody Map<String, Object> scheduleConfig) {
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        CheckUtils.throwIfEmpty(scheduleConfig, "调度配置不能为空");
        String scheduleId = reportEngineService.createReportSchedule(templateId, scheduleConfig);
        return R.ok("调度任务创建成功", scheduleId);
    }

    @Operation(summary = "更新报表调度任务", description = "更新调度任务配置")
    @SaCheckPermission("accounting:report:schedule:update")
    @PutMapping("/schedules/{scheduleId}")
    public R<Void> updateReportSchedule(@Parameter(description = "调度ID") @PathVariable String scheduleId,
                                        @RequestBody Map<String, Object> scheduleConfig) {
        CheckUtils.throwIfBlank(scheduleId, "调度ID不能为空");
        CheckUtils.throwIfEmpty(scheduleConfig, "调度配置不能为空");
        reportEngineService.updateReportSchedule(scheduleId, scheduleConfig);
        return R.ok("调度任务更新成功");
    }

    @Operation(summary = "删除报表调度任务", description = "删除指定的调度任务")
    @SaCheckPermission("accounting:report:schedule:delete")
    @DeleteMapping("/schedules/{scheduleId}")
    public R<Void> deleteReportSchedule(@Parameter(description = "调度ID") @PathVariable String scheduleId) {
        CheckUtils.throwIfBlank(scheduleId, "调度ID不能为空");
        reportEngineService.deleteReportSchedule(scheduleId);
        return R.ok("调度任务删除成功");
    }

    @Operation(summary = "启用/禁用调度任务", description = "切换调度任务的启用状态")
    @SaCheckPermission("accounting:report:schedule:update")
    @PutMapping("/schedules/{scheduleId}/toggle-status")
    public R<Void> toggleScheduleStatus(@Parameter(description = "调度ID") @PathVariable String scheduleId,
                                        @Parameter(description = "是否启用") @RequestParam Boolean enabled) {
        CheckUtils.throwIfBlank(scheduleId, "调度ID不能为空");
        CheckUtils.throwIfNull(enabled, "启用状态不能为空");
        reportEngineService.toggleScheduleStatus(scheduleId, enabled);
        return R.ok("调度任务状态更新成功");
    }

    @Operation(summary = "立即执行调度任务", description = "立即执行指定的调度任务")
    @SaCheckPermission("accounting:report:schedule:execute")
    @PostMapping("/schedules/{scheduleId}/execute")
    public R<DynamicReportResp> executeScheduleNow(@Parameter(description = "调度ID") @PathVariable String scheduleId) {
        CheckUtils.throwIfBlank(scheduleId, "调度ID不能为空");
        DynamicReportResp result = reportEngineService.executeScheduleNow(scheduleId);
        return R.ok("调度任务执行成功", result);
    }

    @Operation(summary = "获取调度任务列表", description = "获取模板的调度任务列表")
    @SaCheckPermission("accounting:report:schedule:view")
    @GetMapping("/templates/{templateId}/schedules")
    public R<List<Map<String, Object>>> listReportSchedules(@Parameter(description = "模板ID") @PathVariable Long templateId) {
        List<Map<String, Object>> schedules = reportEngineService.listReportSchedules(templateId);
        return R.ok(schedules);
    }

    // ==================== 报表统计分析 ====================

    @Operation(summary = "获取模板使用统计", description = "获取模板的使用统计信息")
    @SaCheckPermission("accounting:report:stats:view")
    @GetMapping("/templates/{templateId}/usage-stats")
    public R<Map<String, Object>> getTemplateUsageStats(@Parameter(description = "模板ID") @PathVariable Long templateId,
                                                        @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days) {
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        Map<String, Object> stats = reportEngineService.getTemplateUsageStats(templateId, days);
        return R.ok(stats);
    }

    @Operation(summary = "获取模板性能统计", description = "获取模板的性能统计信息")
    @SaCheckPermission("accounting:report:stats:view")
    @GetMapping("/templates/{templateId}/performance-stats")
    public R<Map<String, Object>> getTemplatePerformanceStats(@Parameter(description = "模板ID") @PathVariable Long templateId,
                                                              @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days) {
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        Map<String, Object> stats = reportEngineService.getTemplatePerformanceStats(templateId, days);
        return R.ok(stats);
    }

    @Operation(summary = "获取报表生成趋势", description = "获取报表生成的趋势分析")
    @SaCheckPermission("accounting:report:stats:view")
    @GetMapping("/reports/generation-trend")
    public R<Map<String, Object>> getReportGenerationTrend(@Parameter(description = "群组ID") @RequestParam(required = false) Long groupId,
                                                           @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days) {
        Map<String, Object> trend = reportEngineService.getReportGenerationTrend(groupId, days);
        return R.ok(trend);
    }

    @Operation(summary = "获取热门模板排行", description = "获取热门模板排行榜")
    @SaCheckPermission("accounting:report:stats:view")
    @GetMapping("/templates/popular")
    public R<List<Map<String, Object>>> getPopularTemplates(@Parameter(description = "群组ID") @RequestParam(required = false) Long groupId,
                                                            @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days,
                                                            @Parameter(description = "返回数量") @RequestParam(defaultValue = "10") Integer limit) {
        List<Map<String, Object>> popular = reportEngineService.getPopularTemplates(groupId, days, limit);
        return R.ok(popular);
    }

    @Operation(summary = "获取用户报表使用分析", description = "获取用户的报表使用分析")
    @SaCheckPermission("accounting:report:stats:view")
    @GetMapping("/users/{userId}/usage-analysis")
    public R<Map<String, Object>> getUserReportUsageAnalysis(@Parameter(description = "用户ID") @PathVariable Long userId,
                                                             @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days) {
        CheckUtils.throwIfNull(userId, "用户ID不能为空");
        Map<String, Object> analysis = reportEngineService.getUserReportUsageAnalysis(userId, days);
        return R.ok(analysis);
    }

    // ==================== 模板权限管理 ====================

    @Operation(summary = "授权模板访问权限", description = "为用户授权模板访问权限")
    @SaCheckPermission("accounting:report:permission:grant")
    @PostMapping("/templates/{templateId}/permissions/grant")
    public R<Void> grantTemplatePermission(@Parameter(description = "模板ID") @PathVariable Long templateId,
                                           @Parameter(description = "用户ID") @RequestParam Long userId,
                                           @Parameter(description = "权限列表") @RequestBody @NotEmpty List<String> permissions) {
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        CheckUtils.throwIfNull(userId, "用户ID不能为空");
        reportEngineService.grantTemplatePermission(templateId, userId, permissions);
        return R.ok("权限授予成功");
    }

    @Operation(summary = "撤销模板访问权限", description = "撤销用户的模板访问权限")
    @SaCheckPermission("accounting:report:permission:revoke")
    @PostMapping("/templates/{templateId}/permissions/revoke")
    public R<Void> revokeTemplatePermission(@Parameter(description = "模板ID") @PathVariable Long templateId,
                                            @Parameter(description = "用户ID") @RequestParam Long userId,
                                            @Parameter(description = "权限列表") @RequestBody @NotEmpty List<String> permissions) {
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        CheckUtils.throwIfNull(userId, "用户ID不能为空");
        reportEngineService.revokeTemplatePermission(templateId, userId, permissions);
        return R.ok("权限撤销成功");
    }

    @Operation(summary = "获取模板权限列表", description = "获取模板的权限配置列表")
    @SaCheckPermission("accounting:report:permission:view")
    @GetMapping("/templates/{templateId}/permissions")
    public R<List<Map<String, Object>>> getTemplatePermissions(@Parameter(description = "模板ID") @PathVariable Long templateId) {
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        List<Map<String, Object>> permissions = reportEngineService.getTemplatePermissions(templateId);
        return R.ok(permissions);
    }

    // ==================== 模板收藏管理 ====================

    @Operation(summary = "收藏/取消收藏模板", description = "切换模板的收藏状态")
    @SaCheckPermission("accounting:report:favorite")
    @PostMapping("/templates/{templateId}/favorite")
    public R<Void> toggleTemplateFavorite(@Parameter(description = "模板ID") @PathVariable Long templateId,
                                          @Parameter(description = "是否收藏") @RequestParam Boolean favorite) {
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        CheckUtils.throwIfNull(favorite, "收藏状态不能为空");
        reportEngineService.toggleTemplateFavorite(templateId, favorite);
        return R.ok(favorite ? "收藏成功" : "取消收藏成功");
    }

    @Operation(summary = "获取用户收藏的模板", description = "获取当前用户收藏的模板列表")
    @SaCheckPermission("accounting:report:favorite")
    @GetMapping("/users/{userId}/favorite-templates")
    public R<List<ReportTemplateResp>> getUserFavoriteTemplates(@Parameter(description = "用户ID") @PathVariable Long userId) {
        CheckUtils.throwIfNull(userId, "用户ID不能为空");
        List<ReportTemplateResp> favorites = reportEngineService.getUserFavoriteTemplates(userId);
        return R.ok(favorites);
    }

    @Operation(summary = "模板评分", description = "为模板进行评分和评价")
    @SaCheckPermission("accounting:report:rate")
    @PostMapping("/templates/{templateId}/rate")
    public R<Void> rateTemplate(@Parameter(description = "模板ID") @PathVariable Long templateId,
                                @Parameter(description = "评分") @RequestParam @NotNull Integer rating,
                                @Parameter(description = "评价内容") @RequestParam(required = false) String comment) {
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        CheckUtils.throwIfTrue(rating < 1 || rating > 5, "评分必须在1-5之间");
        reportEngineService.rateTemplate(templateId, rating, comment);
        return R.ok("评分成功");
    }

    @Operation(summary = "获取模板评价列表", description = "获取模板的用户评价列表")
    @SaCheckPermission("accounting:report:view")
    @GetMapping("/templates/{templateId}/ratings")
    public R<List<ReportTemplateDetailResp.UserRating>> getTemplateRatings(@Parameter(description = "模板ID") @PathVariable Long templateId) {
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        List<ReportTemplateDetailResp.UserRating> ratings = reportEngineService.getTemplateRatings(templateId);
        return R.ok(ratings);
    }

    // ==================== 缓存管理 ====================

    @Operation(summary = "清除模板缓存", description = "清除指定模板的缓存")
    @SaCheckPermission("accounting:report:cache:clear")
    @DeleteMapping("/templates/{templateId}/cache")
    public R<Void> clearTemplateCache(@Parameter(description = "模板ID") @PathVariable Long templateId) {
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        reportEngineService.clearTemplateCache(templateId);
        return R.ok("模板缓存清除成功");
    }

    @Operation(summary = "清除所有报表缓存", description = "清除所有报表相关的缓存")
    @SaCheckPermission("accounting:report:cache:clear")
    @DeleteMapping("/cache/all")
    public R<Void> clearAllReportCache() {
        reportEngineService.clearAllReportCache();
        return R.ok("所有报表缓存清除成功");
    }

    @Operation(summary = "预热模板缓存", description = "预热指定模板的缓存")
    @SaCheckPermission("accounting:report:cache:warmup")
    @PostMapping("/templates/{templateId}/cache/warmup")
    public R<Void> warmupTemplateCache(@Parameter(description = "模板ID") @PathVariable Long templateId) {
        CheckUtils.throwIfNull(templateId, "模板ID不能为空");
        reportEngineService.warmupTemplateCache(templateId);
        return R.ok("模板缓存预热成功");
    }
}
