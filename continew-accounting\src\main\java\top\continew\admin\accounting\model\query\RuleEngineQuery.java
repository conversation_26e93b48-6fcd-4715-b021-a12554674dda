package top.continew.admin.accounting.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.common.model.query.PageQuery;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 规则引擎查询条件
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "规则引擎查询条件")
public class RuleEngineQuery extends PageQuery {

    /**
     * 规则名称
     */
    @Schema(description = "规则名称", example = "自动分类")
    private String ruleName;

    /**
     * 规则类型
     */
    @Schema(description = "规则类型", example = "AUTO_CATEGORY")
    private String ruleType;

    /**
     * 规则状态
     */
    @Schema(description = "规则状态", example = "ACTIVE")
    private String ruleStatus;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    /**
     * 触发类型
     */
    @Schema(description = "触发类型", example = "EVENT")
    private String triggerType;

    /**
     * 事件类型
     */
    @Schema(description = "事件类型", example = "TRANSACTION_CREATED")
    private String eventType;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID", example = "1")
    private Long createdBy;

    /**
     * 创建时间开始
     */
    @Schema(description = "创建时间开始", example = "2024-01-01 00:00:00")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    @Schema(description = "创建时间结束", example = "2024-12-31 23:59:59")
    private LocalDateTime createTimeEnd;

    /**
     * 最后执行时间开始
     */
    @Schema(description = "最后执行时间开始", example = "2024-01-01 00:00:00")
    private LocalDateTime lastExecutionTimeStart;

    /**
     * 最后执行时间结束
     */
    @Schema(description = "最后执行时间结束", example = "2024-12-31 23:59:59")
    private LocalDateTime lastExecutionTimeEnd;

    /**
     * 最小优先级
     */
    @Schema(description = "最小优先级", example = "1")
    private Integer minPriority;

    /**
     * 最大优先级
     */
    @Schema(description = "最大优先级", example = "100")
    private Integer maxPriority;

    /**
     * 最小执行次数
     */
    @Schema(description = "最小执行次数", example = "0")
    private Integer minExecutionCount;

    /**
     * 最大执行次数
     */
    @Schema(description = "最大执行次数", example = "1000")
    private Integer maxExecutionCount;

    /**
     * 最小成功率
     */
    @Schema(description = "最小成功率", example = "0.8")
    private Double minSuccessRate;

    /**
     * 最大成功率
     */
    @Schema(description = "最大成功率", example = "1.0")
    private Double maxSuccessRate;

    /**
     * 是否有调度配置
     */
    @Schema(description = "是否有调度配置", example = "true")
    private Boolean hasSchedule;

    /**
     * 调度状态
     */
    @Schema(description = "调度状态", example = "ACTIVE")
    private String scheduleStatus;

    /**
     * 是否有通知配置
     */
    @Schema(description = "是否有通知配置", example = "true")
    private Boolean hasNotification;

    /**
     * 标签列表
     */
    @Schema(description = "标签列表", example = "[\"自动化\", \"分类\"]")
    private List<String> tags;

    /**
     * 关键词搜索
     */
    @Schema(description = "关键词搜索", example = "自动分类")
    private String keyword;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段", example = "priority")
    private String sortField;

    /**
     * 排序方向
     */
    @Schema(description = "排序方向", example = "DESC")
    private String sortOrder;

    /**
     * 是否包含已删除
     */
    @Schema(description = "是否包含已删除", example = "false")
    private Boolean includeDeleted = false;

    /**
     * 性能过滤
     */
    @Schema(description = "性能过滤")
    private PerformanceFilter performanceFilter;

    /**
     * 执行统计过滤
     */
    @Schema(description = "执行统计过滤")
    private ExecutionStatsFilter executionStatsFilter;

    /**
     * 性能过滤条件
     */
    @Data
    @Schema(description = "性能过滤条件")
    public static class PerformanceFilter {

        /**
         * 最小平均执行时间（毫秒）
         */
        @Schema(description = "最小平均执行时间（毫秒）", example = "0")
        private Long minAvgExecutionTime;

        /**
         * 最大平均执行时间（毫秒）
         */
        @Schema(description = "最大平均执行时间（毫秒）", example = "5000")
        private Long maxAvgExecutionTime;

        /**
         * 最小最大执行时间（毫秒）
         */
        @Schema(description = "最小最大执行时间（毫秒）", example = "0")
        private Long minMaxExecutionTime;

        /**
         * 最大最大执行时间（毫秒）
         */
        @Schema(description = "最大最大执行时间（毫秒）", example = "10000")
        private Long maxMaxExecutionTime;

        /**
         * 最小错误率
         */
        @Schema(description = "最小错误率", example = "0.0")
        private Double minErrorRate;

        /**
         * 最大错误率
         */
        @Schema(description = "最大错误率", example = "0.1")
        private Double maxErrorRate;
    }

    /**
     * 执行统计过滤条件
     */
    @Data
    @Schema(description = "执行统计过滤条件")
    public static class ExecutionStatsFilter {

        /**
         * 统计时间范围（天）
         */
        @Schema(description = "统计时间范围（天）", example = "30")
        private Integer days = 30;

        /**
         * 最小今日执行次数
         */
        @Schema(description = "最小今日执行次数", example = "0")
        private Integer minTodayExecutions;

        /**
         * 最大今日执行次数
         */
        @Schema(description = "最大今日执行次数", example = "100")
        private Integer maxTodayExecutions;

        /**
         * 最小本周执行次数
         */
        @Schema(description = "最小本周执行次数", example = "0")
        private Integer minWeekExecutions;

        /**
         * 最大本周执行次数
         */
        @Schema(description = "最大本周执行次数", example = "500")
        private Integer maxWeekExecutions;

        /**
         * 最小本月执行次数
         */
        @Schema(description = "最小本月执行次数", example = "0")
        private Integer minMonthExecutions;

        /**
         * 最大本月执行次数
         */
        @Schema(description = "最大本月执行次数", example = "2000")
        private Integer maxMonthExecutions;

        /**
         * 是否活跃规则
         */
        @Schema(description = "是否活跃规则", example = "true")
        private Boolean isActive;

        /**
         * 活跃度阈值（天）
         */
        @Schema(description = "活跃度阈值（天）", example = "7")
        private Integer activityThresholdDays = 7;
    }
}
