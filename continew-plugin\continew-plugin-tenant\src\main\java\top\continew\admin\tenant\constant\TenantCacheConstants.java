/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.tenant.constant;

import top.continew.admin.common.constant.CacheConstants;

/**
 * 租户缓存相关常量
 *
 * <AUTHOR>
 * @since 2025/7/14 20:35
 */
public class TenantCacheConstants {

    /**
     * 分隔符
     */
    public static final String DELIMITER = CacheConstants.DELIMITER;

    /**
     * 租户前缀
     */
    public static final String TENANT_KEY_PREFIX = "TENANT" + DELIMITER;

    private TenantCacheConstants() {
    }
}
