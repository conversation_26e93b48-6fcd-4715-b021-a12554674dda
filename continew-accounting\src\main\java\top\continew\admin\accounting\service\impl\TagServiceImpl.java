package top.continew.admin.accounting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.accounting.mapper.TagMapper;
import top.continew.admin.accounting.model.entity.TagDO;
import top.continew.admin.accounting.model.query.TagQuery;
import top.continew.admin.accounting.model.req.TagCreateReq;
import top.continew.admin.accounting.model.req.TagUpdateReq;
import top.continew.admin.accounting.model.resp.TagDetailResp;
import top.continew.admin.accounting.model.resp.TagListResp;
import top.continew.admin.accounting.service.GroupService;
import top.continew.admin.accounting.service.TagService;
import top.continew.admin.common.base.service.impl.BaseServiceImpl;
import top.continew.starter.core.util.validate.CheckUtils;
import top.continew.starter.security.context.SecurityContextHolder;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 标签管理业务实现
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TagServiceImpl extends BaseServiceImpl<TagMapper, TagDO, TagListResp, TagDetailResp, TagQuery, TagCreateReq> implements TagService {

    private final GroupService groupService;

    @Override
    public void beforeCreate(TagCreateReq req) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查群组权限
        CheckUtils.throwIf(!groupService.isAdmin(req.getGroupId(), userId), "您没有权限创建标签");
        
        // 检查标签名称是否重复
        CheckUtils.throwIf(this.isNameExists(req.getName(), req.getGroupId(), null), 
                "群组内已存在相同名称的标签");
        
        // 设置默认值
        if (req.getSort() == null) {
            req.setSort(0);
        }
        if (req.getColor() == null) {
            req.setColor("#2196F3"); // 默认蓝色
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(TagCreateReq req) {
        TagDO tag = new TagDO();
        BeanUtil.copyProperties(req, tag);
        tag.setIsSystem(false); // 用户创建的标签不是系统标签
        
        super.save(tag);
        
        log.info("创建标签成功，群组: {}, 名称: {}", req.getGroupId(), req.getName());
        return tag.getId();
    }

    @Override
    public void beforeUpdate(TagUpdateReq req, Long id) {
        Long userId = SecurityContextHolder.getUserId();
        TagDO tag = super.getById(id);
        CheckUtils.throwIfNull(tag, "标签不存在");
        
        // 检查群组权限
        CheckUtils.throwIf(!groupService.isAdmin(tag.getGroupId(), userId), "您没有权限修改标签");
        
        // 检查系统标签
        CheckUtils.throwIf(Boolean.TRUE.equals(tag.getIsSystem()), "系统标签不允许修改");
        
        // 检查标签名称是否重复
        if (StrUtil.isNotBlank(req.getName())) {
            CheckUtils.throwIf(this.isNameExists(req.getName(), tag.getGroupId(), id), 
                    "群组内已存在相同名称的标签");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TagUpdateReq req, Long id) {
        TagDO tag = super.getById(id);
        CheckUtils.throwIfNull(tag, "标签不存在");
        
        // 更新标签信息
        BeanUtil.copyProperties(req, tag, "id", "groupId", "isSystem", "createUser", "createTime");
        
        super.updateById(tag);
        
        log.info("更新标签成功，ID: {}", id);
    }

    @Override
    public List<TagListResp> getGroupTags(Long groupId) {
        Long userId = SecurityContextHolder.getUserId();
        
        // 检查群组权限
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组成员");
        
        return baseMapper.selectGroupTags(groupId);
    }

    @Override
    public List<TagDO> getDefaultTags(Long groupId) {
        return baseMapper.selectDefaultTags(groupId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createDefaultTags(Long groupId) {
        // 创建默认标签
        List<TagCreateReq> defaultTags = List.of(
            createTagReq(groupId, "工作", "#FF9800", "icon-work", "工作相关", 1, true),
            createTagReq(groupId, "生活", "#4CAF50", "icon-life", "日常生活", 2, false),
            createTagReq(groupId, "娱乐", "#E91E63", "icon-entertainment", "娱乐休闲", 3, false),
            createTagReq(groupId, "学习", "#3F51B5", "icon-study", "学习教育", 4, false),
            createTagReq(groupId, "健康", "#F44336", "icon-health", "健康医疗", 5, false),
            createTagReq(groupId, "旅行", "#9C27B0", "icon-travel", "旅行出游", 6, false),
            createTagReq(groupId, "投资", "#607D8B", "icon-investment", "投资理财", 7, false),
            createTagReq(groupId, "其他", "#9E9E9E", "icon-other", "其他类型", 99, false)
        );
        
        for (TagCreateReq req : defaultTags) {
            TagDO tag = new TagDO();
            BeanUtil.copyProperties(req, tag);
            tag.setIsSystem(true);
            super.save(tag);
        }
        
        log.info("创建默认标签成功，群组: {}", groupId);
    }

    @Override
    public boolean isNameExists(String name, Long groupId, Long id) {
        return baseMapper.existsByName(name, groupId, id);
    }

    @Override
    public boolean canDelete(Long tagId) {
        // 检查是否被使用
        int usageCount = baseMapper.countUsage(tagId);
        return usageCount == 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<Long> tagIds) {
        if (CollUtil.isEmpty(tagIds)) {
            return;
        }
        
        // 检查是否可以删除
        for (Long tagId : tagIds) {
            TagDO tag = super.getById(tagId);
            CheckUtils.throwIfNull(tag, "标签不存在");
            CheckUtils.throwIf(Boolean.TRUE.equals(tag.getIsSystem()), "系统标签不允许删除");
            
            int usageCount = baseMapper.countUsage(tagId);
            CheckUtils.throwIf(usageCount > 0, "标签 [{}] 已被使用，不允许删除", tag.getName());
        }
        
        // 执行删除
        baseMapper.batchDelete(tagIds);
        
        log.info("批量删除标签成功，删除数量: {}", tagIds.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long copyToGroup(Long tagId, Long targetGroupId, Long operatorId) {
        TagDO sourceTag = super.getById(tagId);
        CheckUtils.throwIfNull(sourceTag, "源标签不存在");
        
        // 检查权限
        CheckUtils.throwIf(!groupService.isAdmin(sourceTag.getGroupId(), operatorId), "您没有权限复制标签");
        CheckUtils.throwIf(!groupService.isAdmin(targetGroupId, operatorId), "您没有权限向目标群组复制标签");
        
        // 创建新标签
        TagDO newTag = new TagDO();
        BeanUtil.copyProperties(sourceTag, newTag, "id", "groupId", "createUser", "createTime", "updateUser", "updateTime");
        newTag.setGroupId(targetGroupId);
        newTag.setIsSystem(false); // 复制的标签不是系统标签
        newTag.setIsDefault(false); // 复制的标签不是默认标签
        
        // 检查名称冲突
        if (this.isNameExists(newTag.getName(), targetGroupId, null)) {
            newTag.setName(newTag.getName() + "_复制");
        }
        
        super.save(newTag);
        
        log.info("复制标签成功，源标签: {}, 目标群组: {}, 新标签: {}", tagId, targetGroupId, newTag.getId());
        return newTag.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResult batchImport(Long groupId, List<TagCreateReq> tags, Long operatorId) {
        CheckUtils.throwIf(!groupService.isAdmin(groupId, operatorId), "您没有权限导入标签");
        
        ImportResult result = new ImportResult();
        result.setTotalCount(tags.size());
        result.setSuccessCount(0);
        result.setFailureCount(0);
        result.setErrorMessages(new ArrayList<>());
        
        for (TagCreateReq req : tags) {
            try {
                req.setGroupId(groupId);
                this.create(req);
                result.setSuccessCount(result.getSuccessCount() + 1);
            } catch (Exception e) {
                result.setFailureCount(result.getFailureCount() + 1);
                result.getErrorMessages().add("标签 [" + req.getName() + "] 导入失败: " + e.getMessage());
                log.warn("导入标签失败，名称: {}, 错误: {}", req.getName(), e.getMessage());
            }
        }
        
        log.info("批量导入标签完成，群组: {}, 总数: {}, 成功: {}, 失败: {}", 
                groupId, result.getTotalCount(), result.getSuccessCount(), result.getFailureCount());
        
        return result;
    }

    @Override
    public List<TagDO> getHotTags(Long groupId, int limit) {
        Long userId = SecurityContextHolder.getUserId();
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组成员");
        
        return baseMapper.selectHotTags(groupId, limit);
    }

    @Override
    public List<TagDO> getUnusedTags(Long groupId) {
        Long userId = SecurityContextHolder.getUserId();
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组成员");
        
        return baseMapper.selectUnusedTags(groupId);
    }

    @Override
    public List<TagDO> getByNames(List<String> names, Long groupId) {
        if (CollUtil.isEmpty(names)) {
            return new ArrayList<>();
        }
        
        return baseMapper.selectByNames(names, groupId);
    }

    @Override
    public List<TagMapper.TagStatistics> getTagStatistics(Long groupId) {
        Long userId = SecurityContextHolder.getUserId();
        CheckUtils.throwIf(!groupService.isMember(groupId, userId), "您不是该群组成员");
        
        return baseMapper.selectTagStatistics(groupId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void mergeTags(List<Long> sourceTagIds, Long targetTagId, Long operatorId) {
        CheckUtils.throwIf(CollUtil.isEmpty(sourceTagIds), "源标签ID列表不能为空");
        CheckUtils.throwIf(sourceTagIds.contains(targetTagId), "目标标签不能在源标签列表中");

        TagDO targetTag = super.getById(targetTagId);
        CheckUtils.throwIfNull(targetTag, "目标标签不存在");
        CheckUtils.throwIf(!groupService.isAdmin(targetTag.getGroupId(), operatorId), "您没有权限合并标签");

        // 验证所有源标签都存在且属于同一群组
        List<TagDO> sourceTags = super.listByIds(sourceTagIds);
        CheckUtils.throwIf(sourceTags.size() != sourceTagIds.size(), "部分源标签不存在");

        for (TagDO sourceTag : sourceTags) {
            CheckUtils.throwIf(!sourceTag.getGroupId().equals(targetTag.getGroupId()),
                    "所有标签必须属于同一群组");
            CheckUtils.throwIf(Boolean.TRUE.equals(sourceTag.getIsSystem()),
                    "系统标签 [{}] 不允许合并", sourceTag.getName());
        }

        // TODO: 这里需要更新所有使用了源标签的交易记录，将源标签替换为目标标签
        // 由于涉及JSON字段操作，需要在实际实现时处理

        // 删除源标签
        super.removeByIds(sourceTagIds);

        log.info("合并标签成功，源标签: {}, 目标标签: {}", sourceTagIds, targetTagId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TagDO> autoCreateTags(Long groupId, List<String> tagNames, Long operatorId) {
        CheckUtils.throwIf(!groupService.isAdmin(groupId, operatorId), "您没有权限创建标签");

        if (CollUtil.isEmpty(tagNames)) {
            return new ArrayList<>();
        }

        // 过滤掉已存在的标签
        List<TagDO> existingTags = this.getByNames(tagNames, groupId);
        List<String> existingTagNames = existingTags.stream()
                .map(TagDO::getName)
                .collect(Collectors.toList());

        List<String> newTagNames = tagNames.stream()
                .filter(name -> !existingTagNames.contains(name))
                .distinct()
                .collect(Collectors.toList());

        List<TagDO> createdTags = new ArrayList<>();

        for (String tagName : newTagNames) {
            try {
                TagCreateReq req = new TagCreateReq();
                req.setGroupId(groupId);
                req.setName(tagName);
                req.setColor("#2196F3"); // 默认颜色
                req.setSort(0);
                req.setIsDefault(false);

                Long tagId = this.create(req);
                TagDO tag = super.getById(tagId);
                createdTags.add(tag);
            } catch (Exception e) {
                log.warn("自动创建标签失败，名称: {}, 错误: {}", tagName, e.getMessage());
            }
        }

        log.info("自动创建标签完成，群组: {}, 创建数量: {}", groupId, createdTags.size());
        return createdTags;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanUnusedTags(Long groupId, Long operatorId) {
        CheckUtils.throwIf(!groupService.isAdmin(groupId, operatorId), "您没有权限清理标签");

        List<TagDO> unusedTags = this.getUnusedTags(groupId);

        // 过滤掉系统标签和默认标签
        List<Long> deletableTagIds = unusedTags.stream()
                .filter(tag -> !Boolean.TRUE.equals(tag.getIsSystem()) && !Boolean.TRUE.equals(tag.getIsDefault()))
                .map(TagDO::getId)
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(deletableTagIds)) {
            super.removeByIds(deletableTagIds);
        }

        log.info("清理未使用标签完成，群组: {}, 清理数量: {}", groupId, deletableTagIds.size());
        return deletableTagIds.size();
    }

    /**
     * 创建标签请求对象
     */
    private TagCreateReq createTagReq(Long groupId, String name, String color, String icon,
                                     String description, Integer sort, Boolean isDefault) {
        TagCreateReq req = new TagCreateReq();
        req.setGroupId(groupId);
        req.setName(name);
        req.setColor(color);
        req.setIcon(icon);
        req.setDescription(description);
        req.setSort(sort);
        req.setIsDefault(isDefault);
        return req;
    }
}
