package top.continew.admin.accounting.service.notification.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.continew.admin.accounting.enums.NotificationChannelEnum;
import top.continew.admin.accounting.model.entity.NotificationDO;
import top.continew.admin.accounting.service.notification.AbstractNotificationChannel;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 推送通知渠道实现
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PushNotificationChannel extends AbstractNotificationChannel {

    @Override
    public String getChannelCode() {
        return NotificationChannelEnum.PUSH_NOTIFICATION.getCode();
    }

    @Override
    public String getChannelName() {
        return NotificationChannelEnum.PUSH_NOTIFICATION.getName();
    }

    @Override
    public boolean isEnabled() {
        // 检查推送服务配置
        Map<String, Object> config = getChannelConfig();
        return config != null && Boolean.TRUE.equals(config.get("enabled"));
    }

    @Override
    protected Map<String, Object> doSendSingle(NotificationDO notification, Long targetUserId, Map<String, Object> config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取用户设备令牌
            List<String> deviceTokens = getUserDeviceTokens(targetUserId);
            if (deviceTokens.isEmpty()) {
                throw new RuntimeException("用户没有注册的设备");
            }

            // 构建推送消息
            Map<String, Object> pushMessage = buildPushMessage(notification, config);
            
            // 发送推送通知
            Map<String, Object> sendResult = sendPushNotification(deviceTokens, pushMessage, config);
            
            result.put("success", true);
            result.put("deviceCount", deviceTokens.size());
            result.put("sentCount", sendResult.get("sentCount"));
            result.put("failedCount", sendResult.get("failedCount"));
            result.put("messageId", sendResult.get("messageId"));
            
            log.info("推送通知发送成功 - 用户: {}, 设备数: {}, 成功: {}, 失败: {}", 
                    targetUserId, deviceTokens.size(), 
                    sendResult.get("sentCount"), sendResult.get("failedCount"));
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            log.error("推送通知发送失败 - 用户: {}, 错误: {}", targetUserId, e.getMessage(), e);
        }
        
        return result;
    }

    @Override
    protected Map<String, Object> doBatchSend(NotificationDO notification, List<Long> targetUserIds, Map<String, Object> config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取所有用户的设备令牌
            Map<Long, List<String>> userDeviceMap = getBatchUserDeviceTokens(targetUserIds);
            
            // 收集所有设备令牌
            List<String> allDeviceTokens = userDeviceMap.values().stream()
                    .flatMap(List::stream)
                    .toList();
            
            if (allDeviceTokens.isEmpty()) {
                throw new RuntimeException("没有有效的设备令牌");
            }

            // 构建推送消息
            Map<String, Object> pushMessage = buildPushMessage(notification, config);
            
            // 批量发送推送通知
            Map<String, Object> sendResult = batchSendPushNotification(allDeviceTokens, pushMessage, config);
            
            result.put("success", true);
            result.put("userCount", targetUserIds.size());
            result.put("deviceCount", allDeviceTokens.size());
            result.put("sentCount", sendResult.get("sentCount"));
            result.put("failedCount", sendResult.get("failedCount"));
            result.put("batchId", sendResult.get("batchId"));
            
            log.info("批量推送通知发送完成 - 用户数: {}, 设备数: {}, 成功: {}, 失败: {}", 
                    targetUserIds.size(), allDeviceTokens.size(), 
                    sendResult.get("sentCount"), sendResult.get("failedCount"));
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            log.error("批量推送通知发送失败 - 用户数: {}, 错误: {}", targetUserIds.size(), e.getMessage(), e);
        }
        
        return result;
    }

    @Override
    public Map<String, Object> testConnection(Map<String, Object> config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 验证配置参数
            validateConfig(config);
            
            // 发送测试推送
            String testToken = (String) config.get("testToken");
            if (StrUtil.isNotBlank(testToken)) {
                Map<String, Object> testMessage = new HashMap<>();
                testMessage.put("title", "ContiNew记账系统");
                testMessage.put("body", "推送服务测试消息");
                testMessage.put("data", Map.of("test", true));
                
                Map<String, Object> testResult = sendPushNotification(List.of(testToken), testMessage, config);
                result.put("success", true);
                result.put("messageId", testResult.get("messageId"));
                result.put("message", "测试推送发送成功");
            } else {
                result.put("success", true);
                result.put("message", "配置验证通过，但未提供测试设备令牌");
            }
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            log.error("推送通知渠道测试失败: {}", e.getMessage(), e);
        }
        
        return result;
    }

    @Override
    public boolean validateConfig(Map<String, Object> config) {
        if (config == null) {
            throw new IllegalArgumentException("推送通知配置不能为空");
        }
        
        String provider = (String) config.get("provider");
        if (StrUtil.isBlank(provider)) {
            throw new IllegalArgumentException("推送服务提供商不能为空");
        }
        
        switch (provider.toLowerCase()) {
            case "firebase":
                validateFirebaseConfig(config);
                break;
            case "apns":
                validateApnsConfig(config);
                break;
            case "jpush":
                validateJPushConfig(config);
                break;
            default:
                throw new IllegalArgumentException("不支持的推送服务提供商: " + provider);
        }
        
        return true;
    }

    @Override
    public Map<String, Object> getChannelConfig() {
        // 从配置中心或数据库获取推送通知渠道配置
        Map<String, Object> config = new HashMap<>();
        config.put("enabled", false); // 默认禁用，需要配置后启用
        config.put("provider", "firebase"); // 推送服务提供商
        config.put("firebase", Map.of(
                "projectId", "",
                "privateKeyId", "",
                "privateKey", "",
                "clientEmail", "",
                "clientId", "",
                "authUri", "https://accounts.google.com/o/oauth2/auth",
                "tokenUri", "https://oauth2.googleapis.com/token"
        ));
        config.put("apns", Map.of(
                "keyId", "",
                "teamId", "",
                "bundleId", "",
                "privateKey", "",
                "production", false
        ));
        config.put("jpush", Map.of(
                "appKey", "",
                "masterSecret", "",
                "production", false
        ));
        config.put("rateLimit", 1000); // 每分钟限制1000条
        config.put("batchSize", 100); // 批量发送大小
        return config;
    }

    @Override
    public void updateChannelConfig(Map<String, Object> config) {
        // 更新推送通知渠道配置到配置中心或数据库
        validateConfig(config);
        log.info("推送通知渠道配置已更新");
    }

    /**
     * 验证Firebase配置
     */
    private void validateFirebaseConfig(Map<String, Object> config) {
        @SuppressWarnings("unchecked")
        Map<String, Object> firebaseConfig = (Map<String, Object>) config.get("firebase");
        if (firebaseConfig == null) {
            throw new IllegalArgumentException("Firebase配置不能为空");
        }
        
        String projectId = (String) firebaseConfig.get("projectId");
        String privateKey = (String) firebaseConfig.get("privateKey");
        String clientEmail = (String) firebaseConfig.get("clientEmail");
        
        if (StrUtil.isBlank(projectId)) {
            throw new IllegalArgumentException("Firebase项目ID不能为空");
        }
        if (StrUtil.isBlank(privateKey)) {
            throw new IllegalArgumentException("Firebase私钥不能为空");
        }
        if (StrUtil.isBlank(clientEmail)) {
            throw new IllegalArgumentException("Firebase客户端邮箱不能为空");
        }
    }

    /**
     * 验证APNS配置
     */
    private void validateApnsConfig(Map<String, Object> config) {
        @SuppressWarnings("unchecked")
        Map<String, Object> apnsConfig = (Map<String, Object>) config.get("apns");
        if (apnsConfig == null) {
            throw new IllegalArgumentException("APNS配置不能为空");
        }
        
        String keyId = (String) apnsConfig.get("keyId");
        String teamId = (String) apnsConfig.get("teamId");
        String bundleId = (String) apnsConfig.get("bundleId");
        String privateKey = (String) apnsConfig.get("privateKey");
        
        if (StrUtil.isBlank(keyId)) {
            throw new IllegalArgumentException("APNS Key ID不能为空");
        }
        if (StrUtil.isBlank(teamId)) {
            throw new IllegalArgumentException("APNS Team ID不能为空");
        }
        if (StrUtil.isBlank(bundleId)) {
            throw new IllegalArgumentException("APNS Bundle ID不能为空");
        }
        if (StrUtil.isBlank(privateKey)) {
            throw new IllegalArgumentException("APNS私钥不能为空");
        }
    }

    /**
     * 验证极光推送配置
     */
    private void validateJPushConfig(Map<String, Object> config) {
        @SuppressWarnings("unchecked")
        Map<String, Object> jpushConfig = (Map<String, Object>) config.get("jpush");
        if (jpushConfig == null) {
            throw new IllegalArgumentException("极光推送配置不能为空");
        }
        
        String appKey = (String) jpushConfig.get("appKey");
        String masterSecret = (String) jpushConfig.get("masterSecret");
        
        if (StrUtil.isBlank(appKey)) {
            throw new IllegalArgumentException("极光推送AppKey不能为空");
        }
        if (StrUtil.isBlank(masterSecret)) {
            throw new IllegalArgumentException("极光推送MasterSecret不能为空");
        }
    }

    /**
     * 获取用户设备令牌
     */
    private List<String> getUserDeviceTokens(Long userId) {
        // 从设备管理服务获取用户的设备令牌
        // 这里应该调用设备管理服务的接口
        return List.of(); // 占位符，实际需要实现
    }

    /**
     * 批量获取用户设备令牌
     */
    private Map<Long, List<String>> getBatchUserDeviceTokens(List<Long> userIds) {
        // 从设备管理服务批量获取用户的设备令牌
        // 这里应该调用设备管理服务的接口
        return new HashMap<>(); // 占位符，实际需要实现
    }

    /**
     * 构建推送消息
     */
    private Map<String, Object> buildPushMessage(NotificationDO notification, Map<String, Object> config) {
        Map<String, Object> message = new HashMap<>();
        
        // 基本信息
        message.put("title", notification.getTitle());
        message.put("body", notification.getContent());
        
        // 自定义数据
        Map<String, Object> data = new HashMap<>();
        data.put("notificationId", notification.getId());
        data.put("type", notification.getNotificationType());
        data.put("priority", notification.getPriority());
        
        if (notification.getExtraData() != null) {
            data.putAll(notification.getExtraData());
        }
        
        message.put("data", data);
        
        // 推送选项
        Map<String, Object> options = new HashMap<>();
        options.put("priority", "high");
        options.put("timeToLive", 3600); // 1小时过期
        
        message.put("options", options);
        
        return message;
    }

    /**
     * 发送推送通知
     */
    private Map<String, Object> sendPushNotification(List<String> deviceTokens, Map<String, Object> message, Map<String, Object> config) {
        // 实际的推送通知发送逻辑
        // 这里应该调用推送服务提供商的API
        Map<String, Object> result = new HashMap<>();
        result.put("messageId", "PUSH_" + System.currentTimeMillis());
        result.put("sentCount", deviceTokens.size());
        result.put("failedCount", 0);
        return result;
    }

    /**
     * 批量发送推送通知
     */
    private Map<String, Object> batchSendPushNotification(List<String> deviceTokens, Map<String, Object> message, Map<String, Object> config) {
        // 实际的批量推送通知发送逻辑
        // 这里应该调用推送服务提供商的批量API
        Map<String, Object> result = new HashMap<>();
        result.put("batchId", "BATCH_PUSH_" + System.currentTimeMillis());
        result.put("sentCount", deviceTokens.size());
        result.put("failedCount", 0);
        return result;
    }

    @Override
    public int getPriority() {
        return 2; // 推送通知优先级较高
    }

    @Override
    public boolean supportsRetry() {
        return true;
    }

    @Override
    public long getRetryInterval() {
        return 30000; // 30秒重试间隔
    }

    @Override
    public int getMaxRetries() {
        return 3;
    }

    @Override
    public boolean supportsCallback() {
        return true; // 支持推送状态回调
    }

    @Override
    public void handleCallback(Map<String, Object> callbackData) {
        // 处理推送状态回调
        String messageId = (String) callbackData.get("messageId");
        String status = (String) callbackData.get("status");
        
        log.info("收到推送状态回调 - 消息ID: {}, 状态: {}", messageId, status);
        
        // 更新发送状态
        // 这里应该更新数据库中的发送状态
    }

}
