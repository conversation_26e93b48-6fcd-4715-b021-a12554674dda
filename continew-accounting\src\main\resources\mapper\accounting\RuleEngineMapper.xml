<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.continew.admin.accounting.mapper.RuleEngineMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="top.continew.admin.accounting.model.entity.RuleEngine">
        <id column="rule_id" property="ruleId"/>
        <result column="group_id" property="groupId"/>
        <result column="rule_name" property="ruleName"/>
        <result column="rule_description" property="ruleDescription"/>
        <result column="rule_type" property="ruleType"/>
        <result column="rule_status" property="ruleStatus"/>
        <result column="priority" property="priority"/>
        <result column="enabled" property="enabled"/>
        <result column="version" property="version"/>
        <result column="trigger_condition_json" property="triggerConditionJson"/>
        <result column="execution_actions_json" property="executionActionsJson"/>
        <result column="schedule_config_json" property="scheduleConfigJson"/>
        <result column="notification_config_json" property="notificationConfigJson"/>
        <result column="tags_json" property="tagsJson"/>
        <result column="attributes_json" property="attributesJson"/>
        <result column="trigger_type" property="triggerType"/>
        <result column="event_type" property="eventType"/>
        <result column="execution_count" property="executionCount"/>
        <result column="success_count" property="successCount"/>
        <result column="failure_count" property="failureCount"/>
        <result column="success_rate" property="successRate"/>
        <result column="avg_execution_time" property="avgExecutionTime"/>
        <result column="max_execution_time" property="maxExecutionTime"/>
        <result column="min_execution_time" property="minExecutionTime"/>
        <result column="last_execution_time" property="lastExecutionTime"/>
        <result column="last_execution_status" property="lastExecutionStatus"/>
        <result column="last_execution_result" property="lastExecutionResult"/>
        <result column="next_execution_time" property="nextExecutionTime"/>
        <result column="complexity" property="complexity"/>
        <result column="estimated_match_rate" property="estimatedMatchRate"/>
        <result column="actual_match_rate" property="actualMatchRate"/>
        <result column="error_rate" property="errorRate"/>
        <result column="last_error_message" property="lastErrorMessage"/>
        <result column="last_error_time" property="lastErrorTime"/>
        <result column="has_schedule" property="hasSchedule"/>
        <result column="schedule_status" property="scheduleStatus"/>
        <result column="has_notification" property="hasNotification"/>
        <result column="notification_status" property="notificationStatus"/>
        <result column="last_notification_time" property="lastNotificationTime"/>
        <result column="notification_count" property="notificationCount"/>
        <result column="config_hash" property="configHash"/>
        <result column="published" property="published"/>
        <result column="publish_time" property="publishTime"/>
        <result column="published_by" property="publishedBy"/>
        <result column="is_system_rule" property="isSystemRule"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="created_by" property="createdBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <!-- 详情结果映射 -->
    <resultMap id="DetailResultMap" type="top.continew.admin.accounting.model.entity.RuleEngine" extends="BaseResultMap">
        <result column="created_by_name" property="createdByName"/>
        <result column="updated_by_name" property="updatedByName"/>
        <result column="published_by_name" property="publishedByName"/>
        <result column="today_executions" property="todayExecutions"/>
        <result column="week_executions" property="weekExecutions"/>
        <result column="month_executions" property="monthExecutions"/>
        <result column="today_successes" property="todaySuccesses"/>
        <result column="week_successes" property="weekSuccesses"/>
        <result column="month_successes" property="monthSuccesses"/>
        <result column="today_success_rate" property="todaySuccessRate"/>
        <result column="week_success_rate" property="weekSuccessRate"/>
        <result column="month_success_rate" property="monthSuccessRate"/>
        <result column="execution_trend" property="executionTrend"/>
        <result column="is_active" property="isActive"/>
        <result column="performance_grade" property="performanceGrade"/>
        <result column="performance_score" property="performanceScore"/>
        <result column="throughput" property="throughput"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        rule_id, group_id, rule_name, rule_description, rule_type, rule_status, priority, enabled, version,
        trigger_condition_json, execution_actions_json, schedule_config_json, notification_config_json,
        tags_json, attributes_json, trigger_type, event_type, execution_count, success_count, failure_count,
        success_rate, avg_execution_time, max_execution_time, min_execution_time, last_execution_time,
        last_execution_status, last_execution_result, next_execution_time, complexity, estimated_match_rate,
        actual_match_rate, error_rate, last_error_message, last_error_time, has_schedule, schedule_status,
        has_notification, notification_status, last_notification_time, notification_count, config_hash,
        published, publish_time, published_by, is_system_rule, sort_order, remark,
        create_time, created_by, update_time, updated_by, deleted
    </sql>

    <!-- 查询规则详情 -->
    <select id="selectRuleDetail" resultMap="DetailResultMap">
        SELECT 
            r.*,
            cu.nickname AS created_by_name,
            uu.nickname AS updated_by_name,
            pu.nickname AS published_by_name,
            -- 今日执行统计
            COALESCE(today_stats.execution_count, 0) AS today_executions,
            COALESCE(today_stats.success_count, 0) AS today_successes,
            CASE 
                WHEN COALESCE(today_stats.execution_count, 0) > 0 
                THEN ROUND(COALESCE(today_stats.success_count, 0) * 100.0 / today_stats.execution_count, 2)
                ELSE 0 
            END AS today_success_rate,
            -- 本周执行统计
            COALESCE(week_stats.execution_count, 0) AS week_executions,
            COALESCE(week_stats.success_count, 0) AS week_successes,
            CASE 
                WHEN COALESCE(week_stats.execution_count, 0) > 0 
                THEN ROUND(COALESCE(week_stats.success_count, 0) * 100.0 / week_stats.execution_count, 2)
                ELSE 0 
            END AS week_success_rate,
            -- 本月执行统计
            COALESCE(month_stats.execution_count, 0) AS month_executions,
            COALESCE(month_stats.success_count, 0) AS month_successes,
            CASE 
                WHEN COALESCE(month_stats.execution_count, 0) > 0 
                THEN ROUND(COALESCE(month_stats.success_count, 0) * 100.0 / month_stats.execution_count, 2)
                ELSE 0 
            END AS month_success_rate,
            -- 执行趋势
            CASE 
                WHEN COALESCE(today_stats.execution_count, 0) > COALESCE(yesterday_stats.execution_count, 0) THEN 'UP'
                WHEN COALESCE(today_stats.execution_count, 0) < COALESCE(yesterday_stats.execution_count, 0) THEN 'DOWN'
                ELSE 'STABLE'
            END AS execution_trend,
            -- 是否活跃
            CASE 
                WHEN r.last_execution_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1
                ELSE 0
            END AS is_active,
            -- 性能等级
            CASE 
                WHEN r.success_rate >= 95 AND r.avg_execution_time <= 1000 THEN 'EXCELLENT'
                WHEN r.success_rate >= 90 AND r.avg_execution_time <= 3000 THEN 'GOOD'
                WHEN r.success_rate >= 80 AND r.avg_execution_time <= 5000 THEN 'FAIR'
                ELSE 'POOR'
            END AS performance_grade,
            -- 性能评分
            CASE 
                WHEN r.success_rate >= 95 AND r.avg_execution_time <= 1000 THEN 95
                WHEN r.success_rate >= 90 AND r.avg_execution_time <= 3000 THEN 85
                WHEN r.success_rate >= 80 AND r.avg_execution_time <= 5000 THEN 75
                ELSE 60
            END AS performance_score,
            -- 吞吐量（次/分钟）
            CASE 
                WHEN r.avg_execution_time > 0 THEN ROUND(60000.0 / r.avg_execution_time, 2)
                ELSE 0
            END AS throughput
        FROM acc_rule_engine r
        LEFT JOIN sys_user cu ON r.created_by = cu.user_id
        LEFT JOIN sys_user uu ON r.updated_by = uu.user_id
        LEFT JOIN sys_user pu ON r.published_by = pu.user_id
        -- 今日统计
        LEFT JOIN (
            SELECT rule_id, 
                   COUNT(*) AS execution_count,
                   SUM(CASE WHEN execution_status = 'SUCCESS' THEN 1 ELSE 0 END) AS success_count
            FROM acc_rule_execution_record 
            WHERE DATE(start_time) = CURDATE()
            GROUP BY rule_id
        ) today_stats ON r.rule_id = today_stats.rule_id
        -- 昨日统计
        LEFT JOIN (
            SELECT rule_id, 
                   COUNT(*) AS execution_count
            FROM acc_rule_execution_record 
            WHERE DATE(start_time) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
            GROUP BY rule_id
        ) yesterday_stats ON r.rule_id = yesterday_stats.rule_id
        -- 本周统计
        LEFT JOIN (
            SELECT rule_id, 
                   COUNT(*) AS execution_count,
                   SUM(CASE WHEN execution_status = 'SUCCESS' THEN 1 ELSE 0 END) AS success_count
            FROM acc_rule_execution_record 
            WHERE YEARWEEK(start_time) = YEARWEEK(NOW())
            GROUP BY rule_id
        ) week_stats ON r.rule_id = week_stats.rule_id
        -- 本月统计
        LEFT JOIN (
            SELECT rule_id, 
                   COUNT(*) AS execution_count,
                   SUM(CASE WHEN execution_status = 'SUCCESS' THEN 1 ELSE 0 END) AS success_count
            FROM acc_rule_execution_record 
            WHERE YEAR(start_time) = YEAR(NOW()) AND MONTH(start_time) = MONTH(NOW())
            GROUP BY rule_id
        ) month_stats ON r.rule_id = month_stats.rule_id
        WHERE r.rule_id = #{ruleId} 
          AND r.group_id = #{groupId} 
          AND r.deleted = 0
    </select>

    <!-- 分页查询规则 -->
    <select id="selectRulePage" resultMap="DetailResultMap">
        SELECT 
            r.*,
            cu.nickname AS created_by_name,
            uu.nickname AS updated_by_name,
            -- 今日执行统计
            COALESCE(today_stats.execution_count, 0) AS today_executions,
            COALESCE(today_stats.success_count, 0) AS today_successes,
            CASE 
                WHEN COALESCE(today_stats.execution_count, 0) > 0 
                THEN ROUND(COALESCE(today_stats.success_count, 0) * 100.0 / today_stats.execution_count, 2)
                ELSE 0 
            END AS today_success_rate,
            -- 是否活跃
            CASE 
                WHEN r.last_execution_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1
                ELSE 0
            END AS is_active,
            -- 性能等级
            CASE 
                WHEN r.success_rate >= 95 AND r.avg_execution_time <= 1000 THEN 'EXCELLENT'
                WHEN r.success_rate >= 90 AND r.avg_execution_time <= 3000 THEN 'GOOD'
                WHEN r.success_rate >= 80 AND r.avg_execution_time <= 5000 THEN 'FAIR'
                ELSE 'POOR'
            END AS performance_grade
        FROM acc_rule_engine r
        LEFT JOIN sys_user cu ON r.created_by = cu.user_id
        LEFT JOIN sys_user uu ON r.updated_by = uu.user_id
        -- 今日统计
        LEFT JOIN (
            SELECT rule_id, 
                   COUNT(*) AS execution_count,
                   SUM(CASE WHEN execution_status = 'SUCCESS' THEN 1 ELSE 0 END) AS success_count
            FROM acc_rule_execution_record 
            WHERE DATE(start_time) = CURDATE()
            GROUP BY rule_id
        ) today_stats ON r.rule_id = today_stats.rule_id
        <where>
            r.deleted = 0
            <if test="query.groupId != null">
                AND r.group_id = #{query.groupId}
            </if>
            <if test="query.ruleName != null and query.ruleName != ''">
                AND r.rule_name LIKE CONCAT('%', #{query.ruleName}, '%')
            </if>
            <if test="query.ruleType != null and query.ruleType != ''">
                AND r.rule_type = #{query.ruleType}
            </if>
            <if test="query.ruleStatus != null and query.ruleStatus != ''">
                AND r.rule_status = #{query.ruleStatus}
            </if>
            <if test="query.enabled != null">
                AND r.enabled = #{query.enabled}
            </if>
            <if test="query.triggerType != null and query.triggerType != ''">
                AND r.trigger_type = #{query.triggerType}
            </if>
            <if test="query.eventType != null and query.eventType != ''">
                AND r.event_type = #{query.eventType}
            </if>
            <if test="query.published != null">
                AND r.published = #{query.published}
            </if>
            <if test="query.isSystemRule != null">
                AND r.is_system_rule = #{query.isSystemRule}
            </if>
            <if test="query.hasSchedule != null">
                AND r.has_schedule = #{query.hasSchedule}
            </if>
            <if test="query.scheduleStatus != null and query.scheduleStatus != ''">
                AND r.schedule_status = #{query.scheduleStatus}
            </if>
            <if test="query.priorityMin != null">
                AND r.priority >= #{query.priorityMin}
            </if>
            <if test="query.priorityMax != null">
                AND r.priority <= #{query.priorityMax}
            </if>
            <if test="query.successRateMin != null">
                AND r.success_rate >= #{query.successRateMin}
            </if>
            <if test="query.successRateMax != null">
                AND r.success_rate <= #{query.successRateMax}
            </if>
            <if test="query.avgExecutionTimeMin != null">
                AND r.avg_execution_time >= #{query.avgExecutionTimeMin}
            </if>
            <if test="query.avgExecutionTimeMax != null">
                AND r.avg_execution_time <= #{query.avgExecutionTimeMax}
            </if>
            <if test="query.errorRateMin != null">
                AND r.error_rate >= #{query.errorRateMin}
            </if>
            <if test="query.errorRateMax != null">
                AND r.error_rate <= #{query.errorRateMax}
            </if>
            <if test="query.executionCountMin != null">
                AND r.execution_count >= #{query.executionCountMin}
            </if>
            <if test="query.executionCountMax != null">
                AND r.execution_count <= #{query.executionCountMax}
            </if>
            <if test="query.lastExecutionTimeStart != null">
                AND r.last_execution_time >= #{query.lastExecutionTimeStart}
            </if>
            <if test="query.lastExecutionTimeEnd != null">
                AND r.last_execution_time <= #{query.lastExecutionTimeEnd}
            </if>
            <if test="query.createTimeStart != null">
                AND r.create_time >= #{query.createTimeStart}
            </if>
            <if test="query.createTimeEnd != null">
                AND r.create_time <= #{query.createTimeEnd}
            </if>
            <if test="query.createdBy != null">
                AND r.created_by = #{query.createdBy}
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                AND (r.rule_name LIKE CONCAT('%', #{query.keyword}, '%') 
                     OR r.rule_description LIKE CONCAT('%', #{query.keyword}, '%')
                     OR r.remark LIKE CONCAT('%', #{query.keyword}, '%'))
            </if>
            <if test="query.tags != null and query.tags.size() > 0">
                AND JSON_OVERLAPS(r.tags_json, #{query.tagsJson})
            </if>
        </where>
        <choose>
            <when test="query.sortField != null and query.sortField != ''">
                ORDER BY 
                <choose>
                    <when test="query.sortField == 'ruleName'">r.rule_name</when>
                    <when test="query.sortField == 'ruleType'">r.rule_type</when>
                    <when test="query.sortField == 'priority'">r.priority</when>
                    <when test="query.sortField == 'successRate'">r.success_rate</when>
                    <when test="query.sortField == 'executionCount'">r.execution_count</when>
                    <when test="query.sortField == 'avgExecutionTime'">r.avg_execution_time</when>
                    <when test="query.sortField == 'lastExecutionTime'">r.last_execution_time</when>
                    <when test="query.sortField == 'createTime'">r.create_time</when>
                    <when test="query.sortField == 'updateTime'">r.update_time</when>
                    <otherwise>r.create_time</otherwise>
                </choose>
                <choose>
                    <when test="query.sortOrder != null and query.sortOrder.toUpperCase() == 'ASC'">ASC</when>
                    <otherwise>DESC</otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY r.priority DESC, r.create_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 列表查询规则 -->
    <select id="selectRuleList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM acc_rule_engine
        <where>
            deleted = 0
            <if test="query.groupId != null">
                AND group_id = #{query.groupId}
            </if>
            <if test="query.ruleType != null and query.ruleType != ''">
                AND rule_type = #{query.ruleType}
            </if>
            <if test="query.enabled != null">
                AND enabled = #{query.enabled}
            </if>
            <if test="query.published != null">
                AND published = #{query.published}
            </if>
        </where>
        ORDER BY priority DESC, sort_order ASC, create_time DESC
    </select>

    <!-- 查询启用的规则 -->
    <select id="selectEnabledRules" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM acc_rule_engine
        WHERE deleted = 0 
          AND enabled = 1 
          AND published = 1
          AND group_id = #{groupId}
        <if test="ruleType != null and ruleType != ''">
            AND rule_type = #{ruleType}
        </if>
        <if test="eventType != null and eventType != ''">
            AND event_type = #{eventType}
        </if>
        ORDER BY priority DESC, sort_order ASC
    </select>

    <!-- 查询需要调度的规则 -->
    <select id="selectSchedulableRules" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM acc_rule_engine
        WHERE deleted = 0
          AND enabled = 1
          AND published = 1
          AND has_schedule = 1
          AND schedule_status = 'ACTIVE'
          AND (next_execution_time IS NULL OR next_execution_time <= #{currentTime})
        ORDER BY priority DESC, next_execution_time ASC
    </select>

    <!-- 查询规则执行统计 -->
    <select id="selectRuleExecutionStats" resultType="map">
        SELECT
            COUNT(*) AS total_executions,
            SUM(CASE WHEN execution_status = 'SUCCESS' THEN 1 ELSE 0 END) AS success_executions,
            SUM(CASE WHEN execution_status = 'FAILED' THEN 1 ELSE 0 END) AS failed_executions,
            ROUND(AVG(execution_time_ms), 2) AS avg_execution_time,
            MAX(execution_time_ms) AS max_execution_time,
            MIN(execution_time_ms) AS min_execution_time,
            ROUND(SUM(CASE WHEN execution_status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) AS success_rate,
            ROUND(SUM(CASE WHEN execution_status = 'FAILED' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) AS failure_rate
        FROM acc_rule_execution_record
        WHERE rule_id = #{ruleId}
          AND start_time >= #{startTime}
          AND start_time <= #{endTime}
    </select>

    <!-- 查询规则性能统计 -->
    <select id="selectRulePerformanceStats" resultType="map">
        SELECT
            ROUND(AVG(execution_time_ms), 2) AS avg_response_time,
            ROUND(STDDEV(execution_time_ms), 2) AS response_time_stddev,
            PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY execution_time_ms) AS median_response_time,
            PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY execution_time_ms) AS p95_response_time,
            PERCENTILE_CONT(0.99) WITHIN GROUP (ORDER BY execution_time_ms) AS p99_response_time,
            COUNT(*) / (TIMESTAMPDIFF(MINUTE, #{startTime}, #{endTime}) + 1) AS throughput_per_minute,
            SUM(processed_records) AS total_processed_records,
            ROUND(AVG(processed_records), 2) AS avg_processed_records,
            MAX(processed_records) AS max_processed_records,
            MIN(processed_records) AS min_processed_records
        FROM acc_rule_execution_record
        WHERE rule_id = #{ruleId}
          AND start_time >= #{startTime}
          AND start_time <= #{endTime}
          AND execution_status IN ('SUCCESS', 'FAILED')
    </select>

    <!-- 查询规则执行趋势 -->
    <select id="selectRuleExecutionTrend" resultType="map">
        SELECT
            DATE(start_time) AS execution_date,
            COUNT(*) AS execution_count,
            SUM(CASE WHEN execution_status = 'SUCCESS' THEN 1 ELSE 0 END) AS success_count,
            SUM(CASE WHEN execution_status = 'FAILED' THEN 1 ELSE 0 END) AS failed_count,
            ROUND(AVG(execution_time_ms), 2) AS avg_execution_time,
            SUM(processed_records) AS total_processed_records,
            ROUND(SUM(CASE WHEN execution_status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) AS success_rate
        FROM acc_rule_execution_record
        WHERE rule_id = #{ruleId}
          AND start_time >= #{startTime}
          AND start_time <= #{endTime}
        GROUP BY DATE(start_time)
        ORDER BY execution_date ASC
    </select>

    <!-- 查询热门规则 -->
    <select id="selectPopularRules" resultType="map">
        SELECT
            r.rule_id,
            r.rule_name,
            r.rule_type,
            r.priority,
            COUNT(er.execution_id) AS execution_count,
            SUM(CASE WHEN er.execution_status = 'SUCCESS' THEN 1 ELSE 0 END) AS success_count,
            ROUND(SUM(CASE WHEN er.execution_status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(er.execution_id), 2) AS success_rate,
            ROUND(AVG(er.execution_time_ms), 2) AS avg_execution_time,
            SUM(er.processed_records) AS total_processed_records
        FROM acc_rule_engine r
        INNER JOIN acc_rule_execution_record er ON r.rule_id = er.rule_id
        WHERE r.group_id = #{groupId}
          AND r.deleted = 0
          AND er.start_time >= #{startTime}
          AND er.start_time <= #{endTime}
        GROUP BY r.rule_id, r.rule_name, r.rule_type, r.priority
        ORDER BY execution_count DESC, success_rate DESC
        LIMIT #{limit}
    </select>

    <!-- 查询规则类型统计 -->
    <select id="selectRuleTypeStats" resultType="map">
        SELECT
            rule_type,
            COUNT(*) AS rule_count,
            SUM(CASE WHEN enabled = 1 THEN 1 ELSE 0 END) AS enabled_count,
            SUM(CASE WHEN published = 1 THEN 1 ELSE 0 END) AS published_count,
            SUM(execution_count) AS total_executions,
            ROUND(AVG(success_rate), 2) AS avg_success_rate,
            ROUND(AVG(avg_execution_time), 2) AS avg_execution_time
        FROM acc_rule_engine
        WHERE group_id = #{groupId} AND deleted = 0
        GROUP BY rule_type
        ORDER BY rule_count DESC
    </select>

    <!-- 查询群组规则统计 -->
    <select id="selectGroupRuleStats" resultType="map">
        SELECT
            COUNT(*) AS total_rules,
            SUM(CASE WHEN enabled = 1 THEN 1 ELSE 0 END) AS enabled_rules,
            SUM(CASE WHEN published = 1 THEN 1 ELSE 0 END) AS published_rules,
            SUM(CASE WHEN has_schedule = 1 THEN 1 ELSE 0 END) AS scheduled_rules,
            SUM(CASE WHEN has_notification = 1 THEN 1 ELSE 0 END) AS notification_rules,
            SUM(execution_count) AS total_executions,
            SUM(success_count) AS total_successes,
            SUM(failure_count) AS total_failures,
            ROUND(SUM(success_count) * 100.0 / NULLIF(SUM(execution_count), 0), 2) AS overall_success_rate,
            ROUND(AVG(avg_execution_time), 2) AS avg_execution_time,
            COUNT(DISTINCT rule_type) AS rule_type_count,
            COUNT(CASE WHEN last_execution_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) AS active_rules
        FROM acc_rule_engine
        WHERE group_id = #{groupId} AND deleted = 0
    </select>

    <!-- 查询系统规则统计 -->
    <select id="selectSystemRuleStats" resultType="map">
        SELECT
            COUNT(*) AS total_rules,
            SUM(CASE WHEN enabled = 1 THEN 1 ELSE 0 END) AS enabled_rules,
            SUM(CASE WHEN published = 1 THEN 1 ELSE 0 END) AS published_rules,
            COUNT(DISTINCT group_id) AS active_groups,
            SUM(execution_count) AS total_executions,
            SUM(success_count) AS total_successes,
            ROUND(SUM(success_count) * 100.0 / NULLIF(SUM(execution_count), 0), 2) AS overall_success_rate,
            ROUND(AVG(avg_execution_time), 2) AS avg_execution_time,
            COUNT(DISTINCT rule_type) AS rule_type_count,
            COUNT(CASE WHEN last_execution_time >= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 1 END) AS daily_active_rules,
            COUNT(CASE WHEN last_execution_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) AS weekly_active_rules
        FROM acc_rule_engine
        WHERE deleted = 0
    </select>

    <!-- 插入规则版本 -->
    <insert id="insertRuleVersion">
        INSERT INTO acc_rule_version (
            rule_id, version, version_note, config_data,
            create_time, created_by
        ) VALUES (
            #{ruleId}, #{version}, #{versionNote}, #{configData},
            NOW(), #{createdBy}
        )
    </insert>

    <!-- 查询规则版本历史 -->
    <select id="selectRuleVersionHistory" resultType="map">
        SELECT
            version_id,
            version,
            version_note,
            create_time,
            created_by,
            u.nickname AS created_by_name
        FROM acc_rule_version rv
        LEFT JOIN sys_user u ON rv.created_by = u.user_id
        WHERE rv.rule_id = #{ruleId}
        ORDER BY rv.create_time DESC
    </select>

    <!-- 查询规则版本配置 -->
    <select id="selectRuleVersionConfig" resultType="map">
        SELECT config_data
        FROM acc_rule_version
        WHERE rule_id = #{ruleId} AND version = #{version}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 删除规则版本 -->
    <delete id="deleteRuleVersions">
        DELETE FROM acc_rule_version WHERE rule_id = #{ruleId}
    </delete>

    <!-- 插入规则执行记录 -->
    <insert id="insertRuleExecutionRecord" useGeneratedKeys="true" keyProperty="record.execution_id">
        INSERT INTO acc_rule_execution_record (
            rule_id, batch_id, execution_status, execution_mode, start_time,
            total_rules, total_records, execution_scope, batch_config,
            callback_config, created_by, create_time
        ) VALUES (
            #{record.rule_id}, #{record.batch_id}, #{record.execution_status},
            #{record.execution_mode}, #{record.start_time}, #{record.total_rules},
            #{record.total_records}, #{record.execution_scope}, #{record.batch_config},
            #{record.callback_config}, #{record.created_by}, #{record.create_time}
        )
    </insert>

    <!-- 更新规则执行记录 -->
    <update id="updateRuleExecutionRecord">
        UPDATE acc_rule_execution_record
        SET execution_status = #{executionStatus},
            execution_result = #{executionResult},
            end_time = #{endTime},
            execution_time_ms = #{executionTime},
            update_time = NOW()
        WHERE execution_id = #{executionId}
    </update>

    <!-- 查询规则执行记录 -->
    <select id="selectRuleExecutionRecord" resultType="map">
        SELECT
            execution_id, rule_id, batch_id, execution_status, execution_mode,
            total_rules, executed_rules, success_rules, failed_rules, skipped_rules,
            total_records, processed_records, success_records, failed_records,
            progress, current_step, estimated_remaining_seconds,
            start_time, end_time, execution_time_ms, avg_processing_speed,
            execution_result, error_message, execution_scope, batch_config,
            callback_config, created_by, create_time, update_time
        FROM acc_rule_execution_record
        WHERE execution_id = #{executionId}
    </select>

    <!-- 查询规则执行历史 -->
    <select id="selectRuleExecutionHistory" resultType="map">
        SELECT
            execution_id, batch_id, execution_status, execution_mode,
            total_rules, executed_rules, success_rules, failed_rules,
            total_records, processed_records, success_records, failed_records,
            start_time, end_time, execution_time_ms, avg_processing_speed,
            execution_result, error_message
        FROM acc_rule_execution_record
        WHERE rule_id = #{ruleId}
        ORDER BY start_time DESC
        LIMIT #{limit}
    </select>

    <!-- 删除规则执行记录 -->
    <delete id="deleteRuleExecutionRecords">
        DELETE FROM acc_rule_execution_record WHERE rule_id = #{ruleId}
    </delete>

    <!-- 清理过期执行记录 -->
    <delete id="cleanupExpiredExecutionRecords">
        DELETE FROM acc_rule_execution_record
        WHERE create_time < #{beforeTime}
    </delete>

    <!-- 更新规则执行统计 -->
    <update id="updateRuleExecutionStats">
        UPDATE acc_rule_engine
        SET execution_count = execution_count + #{executionCount},
            success_count = success_count + #{successCount},
            failure_count = failure_count + #{failureCount},
            last_execution_time = #{lastExecutionTime},
            last_execution_status = #{lastExecutionStatus},
            last_execution_result = #{lastExecutionResult},
            update_time = NOW()
        WHERE rule_id = #{ruleId}
    </update>

    <!-- 更新规则性能指标 -->
    <update id="updateRulePerformanceMetrics">
        UPDATE acc_rule_engine
        SET avg_execution_time = #{avgExecutionTime},
            max_execution_time = #{maxExecutionTime},
            min_execution_time = #{minExecutionTime},
            success_rate = #{successRate},
            error_rate = #{errorRate},
            actual_match_rate = #{actualMatchRate},
            update_time = NOW()
        WHERE rule_id = #{ruleId}
    </update>

    <!-- 更新规则调度信息 -->
    <update id="updateRuleScheduleInfo">
        UPDATE acc_rule_engine
        SET schedule_status = #{scheduleStatus},
            next_execution_time = #{nextExecutionTime},
            update_time = NOW()
        WHERE rule_id = #{ruleId}
    </update>

    <!-- 更新规则通知信息 -->
    <update id="updateRuleNotificationInfo">
        UPDATE acc_rule_engine
        SET notification_count = notification_count + #{notificationCount},
            last_notification_time = #{lastNotificationTime},
            update_time = NOW()
        WHERE rule_id = #{ruleId}
    </update>

    <!-- 更新规则错误信息 -->
    <update id="updateRuleErrorInfo">
        UPDATE acc_rule_engine
        SET last_error_message = #{lastErrorMessage},
            last_error_time = #{lastErrorTime},
            update_time = NOW()
        WHERE rule_id = #{ruleId}
    </update>

    <!-- 查询规则依赖关系 -->
    <select id="selectRuleDependencies" resultType="map">
        SELECT
            rd.dependency_id,
            rd.dependent_rule_id,
            rd.dependency_type,
            rd.dependency_condition,
            r.rule_name AS dependent_rule_name,
            r.rule_type AS dependent_rule_type,
            r.enabled AS dependent_rule_enabled
        FROM acc_rule_dependency rd
        INNER JOIN acc_rule_engine r ON rd.dependent_rule_id = r.rule_id
        WHERE rd.rule_id = #{ruleId} AND r.deleted = 0
        ORDER BY rd.dependency_type, r.rule_name
    </select>

    <!-- 查询规则冲突 -->
    <select id="selectRuleConflicts" resultType="map">
        SELECT
            r.rule_id,
            r.rule_name,
            r.rule_type,
            r.priority,
            r.trigger_type,
            r.event_type,
            'TRIGGER_CONFLICT' AS conflict_type,
            '触发条件冲突' AS conflict_description
        FROM acc_rule_engine r
        WHERE r.group_id = #{groupId}
          AND r.rule_id != #{ruleId}
          AND r.deleted = 0
          AND r.enabled = 1
          AND EXISTS (
              SELECT 1 FROM acc_rule_engine r2
              WHERE r2.rule_id = #{ruleId}
                AND r2.trigger_type = r.trigger_type
                AND r2.event_type = r.event_type
                AND r2.priority = r.priority
          )
        UNION ALL
        SELECT
            r.rule_id,
            r.rule_name,
            r.rule_type,
            r.priority,
            r.trigger_type,
            r.event_type,
            'ACTION_CONFLICT' AS conflict_type,
            '执行动作冲突' AS conflict_description
        FROM acc_rule_engine r
        WHERE r.group_id = #{groupId}
          AND r.rule_id != #{ruleId}
          AND r.deleted = 0
          AND r.enabled = 1
          AND JSON_OVERLAPS(
              r.execution_actions_json,
              (SELECT execution_actions_json FROM acc_rule_engine WHERE rule_id = #{ruleId})
          )
        ORDER BY priority DESC, rule_name ASC
    </select>

    <!-- 查询规则影响范围 -->
    <select id="selectRuleImpactScope" resultType="map">
        SELECT
            'TRANSACTION' AS entity_type,
            COUNT(*) AS affected_count,
            'acc_transaction' AS table_name
        FROM acc_transaction t
        INNER JOIN acc_rule_engine r ON r.rule_id = #{ruleId}
        WHERE t.group_id = r.group_id
          AND r.deleted = 0
        UNION ALL
        SELECT
            'CATEGORY' AS entity_type,
            COUNT(*) AS affected_count,
            'acc_category' AS table_name
        FROM acc_category c
        INNER JOIN acc_rule_engine r ON r.rule_id = #{ruleId}
        WHERE c.group_id = r.group_id
          AND r.deleted = 0
    </select>

    <!-- 查询规则模板 -->
    <select id="selectRuleTemplates" resultType="map">
        SELECT
            template_id,
            template_name,
            template_description,
            rule_type,
            template_config,
            usage_count,
            create_time,
            created_by,
            u.nickname AS created_by_name
        FROM acc_rule_template rt
        LEFT JOIN sys_user u ON rt.created_by = u.user_id
        WHERE rt.deleted = 0
        <if test="ruleType != null and ruleType != ''">
            AND rt.rule_type = #{ruleType}
        </if>
        ORDER BY rt.usage_count DESC, rt.create_time DESC
    </select>

    <!-- 插入规则模板 -->
    <insert id="insertRuleTemplate" useGeneratedKeys="true" keyProperty="template.template_id">
        INSERT INTO acc_rule_template (
            template_name, template_description, rule_type, template_config,
            usage_count, create_time, created_by
        ) VALUES (
            #{template.template_name}, #{template.template_description},
            #{template.rule_type}, #{template.template_config},
            0, NOW(), #{template.created_by}
        )
    </insert>

    <!-- 查询规则模板配置 -->
    <select id="selectRuleTemplateConfig" resultType="map">
        SELECT template_config
        FROM acc_rule_template
        WHERE template_id = #{templateId} AND deleted = 0
    </select>

    <!-- 批量更新规则状态 -->
    <update id="batchUpdateRuleStatus">
        UPDATE acc_rule_engine
        SET enabled = #{enabled},
            updated_by = #{updatedBy},
            update_time = NOW()
        WHERE rule_id IN
        <foreach collection="ruleIds" item="ruleId" open="(" separator="," close=")">
            #{ruleId}
        </foreach>
        AND deleted = 0
    </update>

    <!-- 批量删除规则 -->
    <update id="batchDeleteRules">
        UPDATE acc_rule_engine
        SET deleted = 1,
            updated_by = #{deletedBy},
            update_time = NOW()
        WHERE rule_id IN
        <foreach collection="ruleIds" item="ruleId" open="(" separator="," close=")">
            #{ruleId}
        </foreach>
        AND deleted = 0
    </update>

    <!-- 批量更新规则优先级 -->
    <update id="batchUpdateRulePriority">
        <foreach collection="rulePriorities" item="priority" index="ruleId" separator=";">
            UPDATE acc_rule_engine
            SET priority = #{priority},
                updated_by = #{updatedBy},
                update_time = NOW()
            WHERE rule_id = #{ruleId} AND deleted = 0
        </foreach>
    </update>

    <!-- 清理已删除规则的相关数据 -->
    <delete id="cleanupDeletedRuleData">
        DELETE rv FROM acc_rule_version rv
        INNER JOIN acc_rule_engine r ON rv.rule_id = r.rule_id
        WHERE r.deleted = 1 AND r.update_time < #{beforeTime};

        DELETE rer FROM acc_rule_execution_record rer
        INNER JOIN acc_rule_engine r ON rer.rule_id = r.rule_id
        WHERE r.deleted = 1 AND r.update_time < #{beforeTime};

        DELETE rd FROM acc_rule_dependency rd
        INNER JOIN acc_rule_engine r ON rd.rule_id = r.rule_id
        WHERE r.deleted = 1 AND r.update_time < #{beforeTime};

        DELETE FROM acc_rule_engine
        WHERE deleted = 1 AND update_time < #{beforeTime}
    </delete>

    <!-- 清理过期版本数据 -->
    <delete id="cleanupExpiredVersionData">
        DELETE FROM acc_rule_version
        WHERE create_time < #{beforeTime}
          AND version_id NOT IN (
              SELECT * FROM (
                  SELECT version_id
                  FROM acc_rule_version rv2
                  WHERE rv2.rule_id = acc_rule_version.rule_id
                  ORDER BY rv2.create_time DESC
                  LIMIT #{keepCount}
              ) AS keep_versions
          )
    </delete>

    <!-- 归档历史执行数据 -->
    <insert id="archiveHistoricalExecutionData">
        INSERT INTO acc_rule_execution_record_archive
        SELECT * FROM acc_rule_execution_record
        WHERE create_time < #{beforeTime};

        DELETE FROM acc_rule_execution_record
        WHERE create_time < #{beforeTime}
    </insert>

</mapper>
