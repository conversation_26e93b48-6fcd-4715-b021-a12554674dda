package top.continew.admin.accounting.service;

import top.continew.admin.accounting.model.entity.ZapierConfigDO;
import top.continew.admin.accounting.model.query.ZapierConfigQuery;
import top.continew.admin.accounting.model.req.ZapierConfigReq;
import top.continew.admin.accounting.model.req.ZapierConfigUpdateReq;
import top.continew.admin.accounting.model.resp.ZapierConfigResp;
import top.continew.starter.extension.crud.service.BaseService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Zapier配置服务
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface ZapierConfigService extends BaseService<ZapierConfigResp, ZapierConfigResp, ZapierConfigQuery, ZapierConfigReq, ZapierConfigUpdateReq> {

    /**
     * 查询群组的Zapier配置列表
     *
     * @param groupId 群组ID
     * @return 配置列表
     */
    List<ZapierConfigResp> listByGroupId(Long groupId);

    /**
     * 查询启用的Zapier配置
     *
     * @param groupId     群组ID
     * @param triggerType 触发器类型
     * @return 配置列表
     */
    List<ZapierConfigDO> listEnabledConfigs(Long groupId, String triggerType);

    /**
     * 查询所有启用的配置
     *
     * @return 配置列表
     */
    List<ZapierConfigDO> listAllEnabledConfigs();

    /**
     * 启用/禁用配置
     *
     * @param id      配置ID
     * @param enabled 是否启用
     */
    void updateEnabled(Long id, Boolean enabled);

    /**
     * 更新配置状态
     *
     * @param id     配置ID
     * @param status 状态
     */
    void updateStatus(Long id, String status);

    /**
     * 更新触发统计
     *
     * @param id            配置ID
     * @param triggerCount  触发次数
     * @param successCount  成功次数
     * @param failureCount  失败次数
     * @param lastTriggered 最后触发时间
     */
    void updateTriggerStats(Long id, Long triggerCount, Long successCount, Long failureCount, LocalDateTime lastTriggered);

    /**
     * 更新错误信息
     *
     * @param id        配置ID
     * @param error     错误信息
     * @param errorTime 错误时间
     */
    void updateError(Long id, String error, LocalDateTime errorTime);

    /**
     * 测试配置连接
     *
     * @param id 配置ID
     * @return 测试结果
     */
    Map<String, Object> testConnection(Long id);

    /**
     * 测试Webhook URL
     *
     * @param webhookUrl Webhook URL
     * @param testData   测试数据
     * @return 测试结果
     */
    Map<String, Object> testWebhook(String webhookUrl, Map<String, Object> testData);

    /**
     * 触发配置执行
     *
     * @param id         配置ID
     * @param eventType  事件类型
     * @param businessId 业务ID
     * @param data       数据
     * @return 执行结果
     */
    Map<String, Object> triggerExecution(Long id, String eventType, Long businessId, Map<String, Object> data);

    /**
     * 批量触发配置执行
     *
     * @param groupId     群组ID
     * @param triggerType 触发器类型
     * @param eventType   事件类型
     * @param businessId  业务ID
     * @param data        数据
     * @return 执行结果列表
     */
    List<Map<String, Object>> batchTriggerExecution(Long groupId, String triggerType, String eventType, Long businessId, Map<String, Object> data);

    /**
     * 查询配置统计信息
     *
     * @param groupId 群组ID
     * @return 统计信息
     */
    Map<String, Object> getConfigStats(Long groupId);

    /**
     * 查询触发器类型统计
     *
     * @param groupId 群组ID
     * @return 触发器类型统计
     */
    List<Map<String, Object>> getTriggerTypeStats(Long groupId);

    /**
     * 查询活跃配置排行
     *
     * @param groupId 群组ID
     * @param limit   限制数量
     * @return 活跃配置排行
     */
    List<Map<String, Object>> getActiveConfigRanking(Long groupId, Integer limit);

    /**
     * 查询配置性能统计
     *
     * @param groupId   群组ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 性能统计
     */
    List<Map<String, Object>> getPerformanceStats(Long groupId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询错误配置
     *
     * @param groupId 群组ID
     * @param hours   小时数
     * @return 错误配置列表
     */
    List<ZapierConfigResp> getErrorConfigs(Long groupId, Integer hours);

    /**
     * 查询长时间未触发的配置
     *
     * @param groupId 群组ID
     * @param days    天数
     * @return 配置列表
     */
    List<ZapierConfigResp> getInactiveConfigs(Long groupId, Integer days);

    /**
     * 批量更新配置状态
     *
     * @param ids    配置ID列表
     * @param status 状态
     */
    void batchUpdateStatus(List<Long> ids, String status);

    /**
     * 批量启用/禁用配置
     *
     * @param ids     配置ID列表
     * @param enabled 是否启用
     */
    void batchUpdateEnabled(List<Long> ids, Boolean enabled);

    /**
     * 复制配置
     *
     * @param id   配置ID
     * @param name 新配置名称
     * @return 新配置ID
     */
    Long copyConfig(Long id, String name);

    /**
     * 导出配置
     *
     * @param ids 配置ID列表
     * @return 导出数据
     */
    Map<String, Object> exportConfigs(List<Long> ids);

    /**
     * 导入配置
     *
     * @param groupId    群组ID
     * @param configData 配置数据
     * @return 导入结果
     */
    Map<String, Object> importConfigs(Long groupId, Map<String, Object> configData);

    /**
     * 查询配置的触发历史趋势
     *
     * @param configId  配置ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 触发历史趋势
     */
    List<Map<String, Object>> getTriggerTrend(Long configId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 重置配置统计
     *
     * @param id 配置ID
     */
    void resetStats(Long id);

    /**
     * 查询即将过期的配置
     *
     * @param days 天数阈值
     * @return 配置列表
     */
    List<ZapierConfigResp> getExpiringConfigs(Integer days);

    /**
     * 查询高频触发配置
     *
     * @param groupId   群组ID
     * @param threshold 触发次数阈值
     * @param hours     时间范围（小时）
     * @return 配置列表
     */
    List<ZapierConfigResp> getHighFrequencyConfigs(Long groupId, Long threshold, Integer hours);

    /**
     * 查询配置健康状态
     *
     * @param id 配置ID
     * @return 健康状态信息
     */
    Map<String, Object> getConfigHealth(Long id);

    /**
     * 验证配置
     *
     * @param config 配置信息
     * @return 验证结果
     */
    Map<String, Object> validateConfig(ZapierConfigReq config);

    /**
     * 获取支持的触发器类型
     *
     * @return 触发器类型列表
     */
    List<Map<String, Object>> getSupportedTriggerTypes();

    /**
     * 获取数据映射模板
     *
     * @param triggerType 触发器类型
     * @return 数据映射模板
     */
    Map<String, Object> getDataMappingTemplate(String triggerType);

    /**
     * 预览数据映射结果
     *
     * @param config   配置信息
     * @param testData 测试数据
     * @return 映射结果
     */
    Map<String, Object> previewDataMapping(ZapierConfigReq config, Map<String, Object> testData);
}
