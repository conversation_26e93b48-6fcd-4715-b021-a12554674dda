package top.continew.admin.accounting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.accounting.model.entity.GoogleSheetsConfig;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Google Sheets配置Mapper接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface GoogleSheetsConfigMapper extends BaseMapper<GoogleSheetsConfig> {

    // ==================== 基础查询 ====================

    /**
     * 根据配置名称和群组ID查询配置
     *
     * @param configName 配置名称
     * @param groupId    群组ID
     * @return 配置信息
     */
    GoogleSheetsConfig selectByConfigNameAndGroupId(@Param("configName") String configName, @Param("groupId") Long groupId);

    /**
     * 根据Google Sheets ID查询配置
     *
     * @param spreadsheetId Google Sheets ID
     * @param groupId       群组ID
     * @return 配置信息
     */
    GoogleSheetsConfig selectBySpreadsheetIdAndGroupId(@Param("spreadsheetId") String spreadsheetId, @Param("groupId") Long groupId);

    /**
     * 查询群组下的所有配置
     *
     * @param groupId 群组ID
     * @return 配置列表
     */
    List<GoogleSheetsConfig> selectByGroupId(@Param("groupId") Long groupId);

    /**
     * 查询启用的配置列表
     *
     * @param groupId 群组ID
     * @return 配置列表
     */
    List<GoogleSheetsConfig> selectEnabledByGroupId(@Param("groupId") Long groupId);

    /**
     * 查询需要执行的定时同步配置
     *
     * @param currentTime 当前时间
     * @return 配置列表
     */
    List<GoogleSheetsConfig> selectScheduledConfigs(@Param("currentTime") LocalDateTime currentTime);

    // ==================== 统计查询 ====================

    /**
     * 统计群组下的配置数量
     *
     * @param groupId 群组ID
     * @return 统计结果
     */
    Map<String, Object> countConfigsByGroupId(@Param("groupId") Long groupId);

    /**
     * 统计配置状态分布
     *
     * @param groupId 群组ID
     * @return 统计结果
     */
    List<Map<String, Object>> countConfigsByStatus(@Param("groupId") Long groupId);

    /**
     * 统计认证类型分布
     *
     * @param groupId 群组ID
     * @return 统计结果
     */
    List<Map<String, Object>> countConfigsByAuthType(@Param("groupId") Long groupId);

    /**
     * 统计同步模式分布
     *
     * @param groupId 群组ID
     * @return 统计结果
     */
    List<Map<String, Object>> countConfigsBySyncMode(@Param("groupId") Long groupId);

    /**
     * 统计同步方向分布
     *
     * @param groupId 群组ID
     * @return 统计结果
     */
    List<Map<String, Object>> countConfigsBySyncDirection(@Param("groupId") Long groupId);

    /**
     * 统计配置创建趋势
     *
     * @param groupId   群组ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计结果
     */
    List<Map<String, Object>> countConfigCreationTrend(@Param("groupId") Long groupId,
                                                        @Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime);

    // ==================== 性能统计 ====================

    /**
     * 统计配置同步性能
     *
     * @param groupId 群组ID
     * @return 统计结果
     */
    List<Map<String, Object>> selectConfigSyncPerformance(@Param("groupId") Long groupId);

    /**
     * 统计配置成功率排行
     *
     * @param groupId 群组ID
     * @param limit   限制数量
     * @return 统计结果
     */
    List<Map<String, Object>> selectTopConfigsBySuccessRate(@Param("groupId") Long groupId, @Param("limit") Integer limit);

    /**
     * 统计配置使用频率排行
     *
     * @param groupId 群组ID
     * @param limit   限制数量
     * @return 统计结果
     */
    List<Map<String, Object>> selectTopConfigsBySyncCount(@Param("groupId") Long groupId, @Param("limit") Integer limit);

    /**
     * 统计配置处理记录数排行
     *
     * @param groupId 群组ID
     * @param limit   限制数量
     * @return 统计结果
     */
    List<Map<String, Object>> selectTopConfigsByProcessedRecords(@Param("groupId") Long groupId, @Param("limit") Integer limit);

    // ==================== 健康检查 ====================

    /**
     * 查询认证即将过期的配置
     *
     * @param groupId     群组ID
     * @param expireHours 过期小时数
     * @return 配置列表
     */
    List<GoogleSheetsConfig> selectConfigsWithExpiringAuth(@Param("groupId") Long groupId, @Param("expireHours") Integer expireHours);

    /**
     * 查询长时间未同步的配置
     *
     * @param groupId 群组ID
     * @param hours   小时数
     * @return 配置列表
     */
    List<GoogleSheetsConfig> selectConfigsWithoutRecentSync(@Param("groupId") Long groupId, @Param("hours") Integer hours);

    /**
     * 查询同步失败率高的配置
     *
     * @param groupId           群组ID
     * @param minFailureRate    最小失败率
     * @param minSyncCount      最小同步次数
     * @return 配置列表
     */
    List<GoogleSheetsConfig> selectConfigsWithHighFailureRate(@Param("groupId") Long groupId,
                                                               @Param("minFailureRate") Double minFailureRate,
                                                               @Param("minSyncCount") Integer minSyncCount);

    /**
     * 查询配置健康状态
     *
     * @param configId 配置ID
     * @return 健康状态
     */
    Map<String, Object> selectConfigHealthStatus(@Param("configId") Long configId);

    // ==================== 批量操作 ====================

    /**
     * 批量更新配置状态
     *
     * @param configIds    配置ID列表
     * @param configStatus 配置状态
     * @return 更新数量
     */
    Integer batchUpdateConfigStatus(@Param("configIds") List<Long> configIds, @Param("configStatus") String configStatus);

    /**
     * 批量更新启用状态
     *
     * @param configIds 配置ID列表
     * @param enabled   启用状态
     * @return 更新数量
     */
    Integer batchUpdateEnabled(@Param("configIds") List<Long> configIds, @Param("enabled") Boolean enabled);

    /**
     * 批量更新认证状态
     *
     * @param configIds  配置ID列表
     * @param authStatus 认证状态
     * @return 更新数量
     */
    Integer batchUpdateAuthStatus(@Param("configIds") List<Long> configIds, @Param("authStatus") String authStatus);

    /**
     * 批量更新最后同步时间
     *
     * @param configIds      配置ID列表
     * @param lastSyncTime   最后同步时间
     * @param lastSyncStatus 最后同步状态
     * @return 更新数量
     */
    Integer batchUpdateLastSyncTime(@Param("configIds") List<Long> configIds,
                                    @Param("lastSyncTime") LocalDateTime lastSyncTime,
                                    @Param("lastSyncStatus") String lastSyncStatus);

    // ==================== 数据维护 ====================

    /**
     * 更新配置统计信息
     *
     * @param configId 配置ID
     * @return 更新数量
     */
    Integer updateConfigStatistics(@Param("configId") Long configId);

    /**
     * 批量更新配置统计信息
     *
     * @param groupId 群组ID
     * @return 更新数量
     */
    Integer batchUpdateConfigStatistics(@Param("groupId") Long groupId);

    /**
     * 重置配置统计信息
     *
     * @param configId 配置ID
     * @return 更新数量
     */
    Integer resetConfigStatistics(@Param("configId") Long configId);

    /**
     * 清理无效配置
     *
     * @param groupId 群组ID
     * @param days    天数
     * @return 清理数量
     */
    Integer cleanupInvalidConfigs(@Param("groupId") Long groupId, @Param("days") Integer days);

    // ==================== 高级查询 ====================

    /**
     * 搜索配置
     *
     * @param groupId  群组ID
     * @param keyword  关键词
     * @param limit    限制数量
     * @return 配置列表
     */
    List<GoogleSheetsConfig> searchConfigs(@Param("groupId") Long groupId,
                                           @Param("keyword") String keyword,
                                           @Param("limit") Integer limit);

    /**
     * 查询相似配置
     *
     * @param configId 配置ID
     * @param limit    限制数量
     * @return 配置列表
     */
    List<GoogleSheetsConfig> selectSimilarConfigs(@Param("configId") Long configId, @Param("limit") Integer limit);

    /**
     * 查询配置依赖关系
     *
     * @param configId 配置ID
     * @return 依赖关系
     */
    List<Map<String, Object>> selectConfigDependencies(@Param("configId") Long configId);

    /**
     * 查询配置使用历史
     *
     * @param configId  配置ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 使用历史
     */
    List<Map<String, Object>> selectConfigUsageHistory(@Param("configId") Long configId,
                                                        @Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime);

    // ==================== 导出导入 ====================

    /**
     * 查询配置导出数据
     *
     * @param configIds 配置ID列表
     * @return 导出数据
     */
    List<Map<String, Object>> selectConfigsForExport(@Param("configIds") List<Long> configIds);

    /**
     * 验证导入数据
     *
     * @param configName    配置名称
     * @param spreadsheetId Google Sheets ID
     * @param groupId       群组ID
     * @return 验证结果
     */
    Map<String, Object> validateImportData(@Param("configName") String configName,
                                           @Param("spreadsheetId") String spreadsheetId,
                                           @Param("groupId") Long groupId);
}
