package top.continew.admin.accounting.service.sync;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import top.continew.starter.core.exception.BusinessException;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 冲突解决器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ConflictResolver {

    /**
     * 解决冲突
     *
     * @param sourceRecord 源记录
     * @param conflictInfo 冲突信息
     * @param resolutionStrategy 解决策略
     * @return 解决结果
     */
    public Map<String, Object> resolveConflict(Map<String, Object> sourceRecord,
                                               Map<String, Object> conflictInfo,
                                               String resolutionStrategy) {
        log.info("解决数据冲突: strategy={}, conflictType={}", resolutionStrategy, conflictInfo.get("conflictType"));
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> targetRecord = (Map<String, Object>) conflictInfo.get("targetRecord");
            String conflictType = (String) conflictInfo.get("conflictType");
            
            Map<String, Object> resolvedRecord;
            
            switch (resolutionStrategy) {
                case "SOURCE_WINS":
                    resolvedRecord = resolveSourceWins(sourceRecord, targetRecord);
                    break;
                case "TARGET_WINS":
                    resolvedRecord = resolveTargetWins(sourceRecord, targetRecord);
                    break;
                case "TIMESTAMP_WINS":
                    resolvedRecord = resolveTimestampWins(sourceRecord, targetRecord, conflictInfo);
                    break;
                case "MERGE_FIELDS":
                    resolvedRecord = resolveMergeFields(sourceRecord, targetRecord, conflictInfo);
                    break;
                case "MANUAL_REVIEW":
                    resolvedRecord = resolveManualReview(sourceRecord, targetRecord, conflictInfo);
                    break;
                case "CUSTOM_RULES":
                    resolvedRecord = resolveCustomRules(sourceRecord, targetRecord, conflictInfo);
                    break;
                default:
                    throw new BusinessException("不支持的冲突解决策略: " + resolutionStrategy);
            }
            
            result.put("status", "RESOLVED");
            result.put("strategy", resolutionStrategy);
            result.put("conflictType", conflictType);
            result.put("resolvedData", resolvedRecord);
            result.put("resolveTime", LocalDateTime.now());
            
            log.info("冲突解决成功: strategy={}, conflictType={}", resolutionStrategy, conflictType);
            
        } catch (Exception e) {
            log.error("冲突解决失败: strategy={}, error={}", resolutionStrategy, e.getMessage(), e);
            
            result.put("status", "FAILED");
            result.put("strategy", resolutionStrategy);
            result.put("error", e.getMessage());
            result.put("sourceRecord", sourceRecord);
            result.put("targetRecord", conflictInfo.get("targetRecord"));
            result.put("conflictInfo", conflictInfo);
        }
        
        return result;
    }

    /**
     * 源记录优先策略
     */
    private Map<String, Object> resolveSourceWins(Map<String, Object> sourceRecord, Map<String, Object> targetRecord) {
        log.debug("应用源记录优先策略");
        
        // 保留目标记录的ID和系统字段，其他字段使用源记录
        Map<String, Object> resolvedRecord = new HashMap<>(sourceRecord);
        
        // 保留目标记录的系统字段
        Set<String> systemFields = getSystemFields();
        for (String field : systemFields) {
            if (targetRecord.containsKey(field)) {
                resolvedRecord.put(field, targetRecord.get(field));
            }
        }
        
        return resolvedRecord;
    }

    /**
     * 目标记录优先策略
     */
    private Map<String, Object> resolveTargetWins(Map<String, Object> sourceRecord, Map<String, Object> targetRecord) {
        log.debug("应用目标记录优先策略");
        
        // 直接返回目标记录，不做任何修改
        return new HashMap<>(targetRecord);
    }

    /**
     * 时间戳优先策略
     */
    private Map<String, Object> resolveTimestampWins(Map<String, Object> sourceRecord,
                                                     Map<String, Object> targetRecord,
                                                     Map<String, Object> conflictInfo) {
        log.debug("应用时间戳优先策略");
        
        String timestampField = getTimestampField();
        Object sourceTimestamp = sourceRecord.get(timestampField);
        Object targetTimestamp = targetRecord.get(timestampField);
        
        if (sourceTimestamp == null && targetTimestamp == null) {
            // 都没有时间戳，使用源记录优先
            return resolveSourceWins(sourceRecord, targetRecord);
        }
        
        if (sourceTimestamp == null) {
            return resolveTargetWins(sourceRecord, targetRecord);
        }
        
        if (targetTimestamp == null) {
            return resolveSourceWins(sourceRecord, targetRecord);
        }
        
        // 比较时间戳
        int comparison = compareTimestamps(sourceTimestamp, targetTimestamp);
        
        if (comparison >= 0) {
            // 源记录时间戳更新或相等，使用源记录
            return resolveSourceWins(sourceRecord, targetRecord);
        } else {
            // 目标记录时间戳更新，使用目标记录
            return resolveTargetWins(sourceRecord, targetRecord);
        }
    }

    /**
     * 字段合并策略
     */
    private Map<String, Object> resolveMergeFields(Map<String, Object> sourceRecord,
                                                   Map<String, Object> targetRecord,
                                                   Map<String, Object> conflictInfo) {
        log.debug("应用字段合并策略");
        
        Map<String, Object> resolvedRecord = new HashMap<>(targetRecord);
        
        @SuppressWarnings("unchecked")
        List<String> conflictFields = (List<String>) conflictInfo.get("conflictFields");
        
        if (CollUtil.isNotEmpty(conflictFields)) {
            for (String field : conflictFields) {
                Object sourceValue = sourceRecord.get(field);
                Object targetValue = targetRecord.get(field);
                
                // 应用字段级别的合并规则
                Object mergedValue = mergeFieldValues(field, sourceValue, targetValue);
                resolvedRecord.put(field, mergedValue);
            }
        }
        
        // 更新时间戳为当前时间
        String timestampField = getTimestampField();
        resolvedRecord.put(timestampField, LocalDateTime.now());
        
        return resolvedRecord;
    }

    /**
     * 手动审核策略
     */
    private Map<String, Object> resolveManualReview(Map<String, Object> sourceRecord,
                                                    Map<String, Object> targetRecord,
                                                    Map<String, Object> conflictInfo) {
        log.debug("应用手动审核策略");
        
        // 手动审核策略不自动解决冲突，需要人工介入
        Map<String, Object> reviewData = new HashMap<>();
        reviewData.put("sourceRecord", sourceRecord);
        reviewData.put("targetRecord", targetRecord);
        reviewData.put("conflictInfo", conflictInfo);
        reviewData.put("reviewStatus", "PENDING");
        reviewData.put("createTime", LocalDateTime.now());
        
        // TODO: 将冲突数据保存到审核队列
        saveConflictForReview(reviewData);
        
        // 返回未解决状态
        throw new BusinessException("冲突需要手动审核，已加入审核队列");
    }

    /**
     * 自定义规则策略
     */
    private Map<String, Object> resolveCustomRules(Map<String, Object> sourceRecord,
                                                   Map<String, Object> targetRecord,
                                                   Map<String, Object> conflictInfo) {
        log.debug("应用自定义规则策略");
        
        Map<String, Object> resolvedRecord = new HashMap<>(targetRecord);
        
        // 应用自定义业务规则
        List<ConflictRule> rules = getCustomConflictRules();
        
        for (ConflictRule rule : rules) {
            if (rule.matches(sourceRecord, targetRecord, conflictInfo)) {
                resolvedRecord = rule.apply(sourceRecord, targetRecord, resolvedRecord);
                log.debug("应用自定义规则: {}", rule.getName());
            }
        }
        
        return resolvedRecord;
    }

    /**
     * 合并字段值
     */
    private Object mergeFieldValues(String field, Object sourceValue, Object targetValue) {
        // 根据字段类型和业务规则合并值
        
        if (sourceValue == null) {
            return targetValue;
        }
        
        if (targetValue == null) {
            return sourceValue;
        }
        
        // 字符串类型：优先使用非空且更长的值
        if (sourceValue instanceof String && targetValue instanceof String) {
            String sourceStr = (String) sourceValue;
            String targetStr = (String) targetValue;
            
            if (StrUtil.isBlank(sourceStr)) {
                return targetStr;
            }
            if (StrUtil.isBlank(targetStr)) {
                return sourceStr;
            }
            
            // 返回更长的字符串
            return sourceStr.length() >= targetStr.length() ? sourceStr : targetStr;
        }
        
        // 数值类型：使用较大的值
        if (sourceValue instanceof Number && targetValue instanceof Number) {
            double sourceNum = ((Number) sourceValue).doubleValue();
            double targetNum = ((Number) targetValue).doubleValue();
            
            return sourceNum >= targetNum ? sourceValue : targetValue;
        }
        
        // 时间类型：使用较新的时间
        if (sourceValue instanceof LocalDateTime && targetValue instanceof LocalDateTime) {
            LocalDateTime sourceTime = (LocalDateTime) sourceValue;
            LocalDateTime targetTime = (LocalDateTime) targetValue;
            
            return sourceTime.isAfter(targetTime) ? sourceTime : targetTime;
        }
        
        // 布尔类型：优先使用true
        if (sourceValue instanceof Boolean && targetValue instanceof Boolean) {
            return (Boolean) sourceValue || (Boolean) targetValue;
        }
        
        // 默认使用源值
        return sourceValue;
    }

    /**
     * 比较时间戳
     */
    private int compareTimestamps(Object timestamp1, Object timestamp2) {
        if (timestamp1 instanceof LocalDateTime && timestamp2 instanceof LocalDateTime) {
            return ((LocalDateTime) timestamp1).compareTo((LocalDateTime) timestamp2);
        }
        
        if (timestamp1 instanceof Date && timestamp2 instanceof Date) {
            return ((Date) timestamp1).compareTo((Date) timestamp2);
        }
        
        if (timestamp1 instanceof Long && timestamp2 instanceof Long) {
            return ((Long) timestamp1).compareTo((Long) timestamp2);
        }
        
        // 转换为字符串比较
        return timestamp1.toString().compareTo(timestamp2.toString());
    }

    /**
     * 获取系统字段列表
     */
    private Set<String> getSystemFields() {
        return new HashSet<>(Arrays.asList(
                "id", "createTime", "updateTime", "createBy", "updateBy", "version"
        ));
    }

    /**
     * 获取时间戳字段
     */
    private String getTimestampField() {
        return "updateTime";
    }

    /**
     * 保存冲突数据用于审核
     */
    private void saveConflictForReview(Map<String, Object> reviewData) {
        // TODO: 实现冲突数据保存逻辑
        log.info("保存冲突数据用于审核: {}", reviewData.get("reviewStatus"));
    }

    /**
     * 获取自定义冲突规则
     */
    private List<ConflictRule> getCustomConflictRules() {
        List<ConflictRule> rules = new ArrayList<>();
        
        // 示例规则：金额字段使用较大值
        rules.add(new ConflictRule("AmountMaxRule", "金额字段使用较大值") {
            @Override
            public boolean matches(Map<String, Object> sourceRecord, Map<String, Object> targetRecord, Map<String, Object> conflictInfo) {
                @SuppressWarnings("unchecked")
                List<String> conflictFields = (List<String>) conflictInfo.get("conflictFields");
                return conflictFields != null && conflictFields.stream().anyMatch(field -> 
                        field.toLowerCase().contains("amount") || field.toLowerCase().contains("money"));
            }
            
            @Override
            public Map<String, Object> apply(Map<String, Object> sourceRecord, Map<String, Object> targetRecord, Map<String, Object> resolvedRecord) {
                @SuppressWarnings("unchecked")
                List<String> conflictFields = (List<String>) conflictInfo.get("conflictFields");
                
                for (String field : conflictFields) {
                    if (field.toLowerCase().contains("amount") || field.toLowerCase().contains("money")) {
                        Object sourceValue = sourceRecord.get(field);
                        Object targetValue = targetRecord.get(field);
                        
                        if (sourceValue instanceof Number && targetValue instanceof Number) {
                            double sourceNum = ((Number) sourceValue).doubleValue();
                            double targetNum = ((Number) targetValue).doubleValue();
                            
                            resolvedRecord.put(field, sourceNum >= targetNum ? sourceValue : targetValue);
                        }
                    }
                }
                
                return resolvedRecord;
            }
        });
        
        // 示例规则：状态字段优先级
        rules.add(new ConflictRule("StatusPriorityRule", "状态字段按优先级选择") {
            @Override
            public boolean matches(Map<String, Object> sourceRecord, Map<String, Object> targetRecord, Map<String, Object> conflictInfo) {
                @SuppressWarnings("unchecked")
                List<String> conflictFields = (List<String>) conflictInfo.get("conflictFields");
                return conflictFields != null && conflictFields.stream().anyMatch(field -> 
                        field.toLowerCase().contains("status") || field.toLowerCase().contains("state"));
            }
            
            @Override
            public Map<String, Object> apply(Map<String, Object> sourceRecord, Map<String, Object> targetRecord, Map<String, Object> resolvedRecord) {
                @SuppressWarnings("unchecked")
                List<String> conflictFields = (List<String>) conflictInfo.get("conflictFields");
                
                Map<String, Integer> statusPriority = new HashMap<>();
                statusPriority.put("ACTIVE", 3);
                statusPriority.put("PENDING", 2);
                statusPriority.put("INACTIVE", 1);
                statusPriority.put("DELETED", 0);
                
                for (String field : conflictFields) {
                    if (field.toLowerCase().contains("status") || field.toLowerCase().contains("state")) {
                        String sourceStatus = (String) sourceRecord.get(field);
                        String targetStatus = (String) targetRecord.get(field);
                        
                        int sourcePriority = statusPriority.getOrDefault(sourceStatus, 0);
                        int targetPriority = statusPriority.getOrDefault(targetStatus, 0);
                        
                        resolvedRecord.put(field, sourcePriority >= targetPriority ? sourceStatus : targetStatus);
                    }
                }
                
                return resolvedRecord;
            }
        });
        
        return rules;
    }

    /**
     * 冲突规则抽象类
     */
    public abstract static class ConflictRule {
        private final String name;
        private final String description;
        protected Map<String, Object> conflictInfo;

        public ConflictRule(String name, String description) {
            this.name = name;
            this.description = description;
        }

        public abstract boolean matches(Map<String, Object> sourceRecord, Map<String, Object> targetRecord, Map<String, Object> conflictInfo);
        
        public abstract Map<String, Object> apply(Map<String, Object> sourceRecord, Map<String, Object> targetRecord, Map<String, Object> resolvedRecord);

        public String getName() {
            return name;
        }

        public String getDescription() {
            return description;
        }
    }
}
