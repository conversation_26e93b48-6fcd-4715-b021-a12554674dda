-- 通知推送相关表结构

-- 通知主表
CREATE TABLE `acc_notification` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `title` varchar(200) NOT NULL COMMENT '通知标题',
    `content` text NOT NULL COMMENT '通知内容',
    `notification_type` varchar(50) NOT NULL COMMENT '通知类型',
    `priority` varchar(20) NOT NULL DEFAULT 'NORMAL' COMMENT '优先级',
    `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '状态',
    `channels` json NOT NULL COMMENT '发送渠道列表',
    `target_users` json DEFAULT NULL COMMENT '目标用户ID列表',
    `target_groups` json DEFAULT NULL COMMENT '目标群组ID列表',
    `target_roles` json DEFAULT NULL COMMENT '目标角色ID列表',
    `send_time` datetime DEFAULT NULL COMMENT '实际发送时间',
    `scheduled_time` datetime DEFAULT NULL COMMENT '计划发送时间',
    `template_code` varchar(100) DEFAULT NULL COMMENT '模板代码',
    `template_params` json DEFAULT NULL COMMENT '模板参数',
    `attachments` json DEFAULT NULL COMMENT '附件信息',
    `extra_data` json DEFAULT NULL COMMENT '扩展数据',
    `send_result` json DEFAULT NULL COMMENT '发送结果',
    `retry_count` int NOT NULL DEFAULT 0 COMMENT '重试次数',
    `max_retries` int NOT NULL DEFAULT 3 COMMENT '最大重试次数',
    `retry_interval` int NOT NULL DEFAULT 300 COMMENT '重试间隔(秒)',
    `group_id` bigint DEFAULT NULL COMMENT '群组ID',
    `create_user` bigint NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_notification_status` (`status`),
    KEY `idx_notification_type` (`notification_type`),
    KEY `idx_notification_scheduled_time` (`scheduled_time`),
    KEY `idx_notification_group_id` (`group_id`),
    KEY `idx_notification_create_time` (`create_time`),
    KEY `idx_notification_template_code` (`template_code`),
    KEY `idx_notification_retry` (`status`, `retry_count`, `max_retries`),
    CONSTRAINT `fk_notification_group` FOREIGN KEY (`group_id`) REFERENCES `acc_group` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知推送表';

-- 通知模板表
CREATE TABLE `acc_notification_template` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `code` varchar(100) NOT NULL COMMENT '模板代码',
    `name` varchar(200) NOT NULL COMMENT '模板名称',
    `description` varchar(500) DEFAULT NULL COMMENT '模板描述',
    `category` varchar(50) NOT NULL COMMENT '模板分类',
    `notification_type` varchar(50) NOT NULL COMMENT '通知类型',
    `title_template` text DEFAULT NULL COMMENT '标题模板',
    `content_template` text NOT NULL COMMENT '内容模板',
    `html_template` text DEFAULT NULL COMMENT 'HTML模板',
    `sms_template` text DEFAULT NULL COMMENT '短信模板',
    `push_template` text DEFAULT NULL COMMENT '推送模板',
    `bot_template` text DEFAULT NULL COMMENT '机器人模板',
    `variables` json DEFAULT NULL COMMENT '模板变量定义',
    `default_values` json DEFAULT NULL COMMENT '默认值',
    `style_config` json DEFAULT NULL COMMENT '样式配置',
    `enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    `version` varchar(20) NOT NULL DEFAULT '1.0.0' COMMENT '版本号',
    `group_id` bigint DEFAULT NULL COMMENT '群组ID',
    `create_user` bigint NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_template_code` (`code`),
    KEY `idx_template_category` (`category`),
    KEY `idx_template_type` (`notification_type`),
    KEY `idx_template_group_id` (`group_id`),
    KEY `idx_template_enabled` (`enabled`),
    CONSTRAINT `fk_template_group` FOREIGN KEY (`group_id`) REFERENCES `acc_group` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知模板表';

-- 通知发送日志表
CREATE TABLE `acc_notification_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `notification_id` bigint NOT NULL COMMENT '通知ID',
    `channel` varchar(50) NOT NULL COMMENT '发送渠道',
    `target_user` bigint NOT NULL COMMENT '目标用户ID',
    `status` varchar(20) NOT NULL COMMENT '发送状态',
    `send_time` datetime NOT NULL COMMENT '发送时间',
    `response_data` json DEFAULT NULL COMMENT '响应数据',
    `error_message` text DEFAULT NULL COMMENT '错误信息',
    `duration` bigint DEFAULT NULL COMMENT '发送耗时(毫秒)',
    `external_message_id` varchar(200) DEFAULT NULL COMMENT '外部消息ID',
    `callback_data` json DEFAULT NULL COMMENT '回调数据',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_log_notification_id` (`notification_id`),
    KEY `idx_log_channel` (`channel`),
    KEY `idx_log_target_user` (`target_user`),
    KEY `idx_log_status` (`status`),
    KEY `idx_log_send_time` (`send_time`),
    KEY `idx_log_external_message_id` (`external_message_id`),
    CONSTRAINT `fk_log_notification` FOREIGN KEY (`notification_id`) REFERENCES `acc_notification` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_log_user` FOREIGN KEY (`target_user`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知发送日志表';

-- 通知模板版本表
CREATE TABLE `acc_notification_template_version` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `template_id` bigint NOT NULL COMMENT '模板ID',
    `version` varchar(20) NOT NULL COMMENT '版本号',
    `description` varchar(500) DEFAULT NULL COMMENT '版本描述',
    `title_template` text DEFAULT NULL COMMENT '标题模板',
    `content_template` text NOT NULL COMMENT '内容模板',
    `html_template` text DEFAULT NULL COMMENT 'HTML模板',
    `sms_template` text DEFAULT NULL COMMENT '短信模板',
    `push_template` text DEFAULT NULL COMMENT '推送模板',
    `bot_template` text DEFAULT NULL COMMENT '机器人模板',
    `variables` json DEFAULT NULL COMMENT '模板变量定义',
    `default_values` json DEFAULT NULL COMMENT '默认值',
    `style_config` json DEFAULT NULL COMMENT '样式配置',
    `is_current` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否当前版本',
    `create_user` bigint NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_template_version` (`template_id`, `version`),
    KEY `idx_template_version_current` (`template_id`, `is_current`),
    CONSTRAINT `fk_version_template` FOREIGN KEY (`template_id`) REFERENCES `acc_notification_template` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知模板版本表';

-- 通知模板分类表
CREATE TABLE `acc_notification_template_category` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name` varchar(100) NOT NULL COMMENT '分类名称',
    `description` varchar(500) DEFAULT NULL COMMENT '分类描述',
    `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
    `enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    `group_id` bigint DEFAULT NULL COMMENT '群组ID',
    `create_user` bigint NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_category_name_group` (`name`, `group_id`),
    KEY `idx_category_group_id` (`group_id`),
    KEY `idx_category_enabled` (`enabled`),
    CONSTRAINT `fk_category_group` FOREIGN KEY (`group_id`) REFERENCES `acc_group` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知模板分类表';

-- 通知渠道配置表
CREATE TABLE `acc_notification_channel_config` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `channel` varchar(50) NOT NULL COMMENT '渠道代码',
    `name` varchar(100) NOT NULL COMMENT '渠道名称',
    `description` varchar(500) DEFAULT NULL COMMENT '渠道描述',
    `config_data` json NOT NULL COMMENT '配置数据',
    `enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    `rate_limit` int DEFAULT NULL COMMENT '速率限制(每分钟)',
    `retry_config` json DEFAULT NULL COMMENT '重试配置',
    `health_check_url` varchar(500) DEFAULT NULL COMMENT '健康检查URL',
    `group_id` bigint DEFAULT NULL COMMENT '群组ID',
    `create_user` bigint NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_channel_group` (`channel`, `group_id`),
    KEY `idx_channel_enabled` (`enabled`),
    KEY `idx_channel_group_id` (`group_id`),
    CONSTRAINT `fk_channel_config_group` FOREIGN KEY (`group_id`) REFERENCES `acc_group` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知渠道配置表';

-- 插入默认通知模板分类
INSERT INTO `acc_notification_template_category` (`name`, `description`, `sort_order`, `group_id`, `create_user`) VALUES
('系统通知', '系统相关通知模板', 1, NULL, 1),
('交易通知', '交易相关通知模板', 2, NULL, 1),
('债务提醒', '债务提醒通知模板', 3, NULL, 1),
('预算警告', '预算警告通知模板', 4, NULL, 1),
('订阅通知', '订阅相关通知模板', 5, NULL, 1),
('报表通知', '报表相关通知模板', 6, NULL, 1),
('同步通知', '数据同步通知模板', 7, NULL, 1),
('安全通知', '安全相关通知模板', 8, NULL, 1),
('营销通知', '营销推广通知模板', 9, NULL, 1),
('自定义通知', '自定义通知模板', 10, NULL, 1);

-- 插入默认通知模板
INSERT INTO `acc_notification_template` (`code`, `name`, `description`, `category`, `notification_type`, `title_template`, `content_template`, `variables`, `create_user`) VALUES
('TRANSACTION_CREATED', '交易创建通知', '新交易创建时的通知模板', '交易通知', 'TRANSACTION', '新交易：{{transaction.description}}', '您有一笔新的交易记录：\n金额：{{transaction.amount}} {{transaction.currency}}\n描述：{{transaction.description}}\n时间：{{transaction.createTime}}\n分类：{{transaction.category}}', '["transaction.amount", "transaction.currency", "transaction.description", "transaction.createTime", "transaction.category"]', 1),
('DEBT_REMINDER', '债务提醒通知', '债务到期提醒通知模板', '债务提醒', 'DEBT_REMINDER', '债务提醒：{{debt.description}}', '您有一笔债务即将到期：\n金额：{{debt.amount}} {{debt.currency}}\n描述：{{debt.description}}\n到期时间：{{debt.dueDate}}\n债权人：{{debt.creditor}}', '["debt.amount", "debt.currency", "debt.description", "debt.dueDate", "debt.creditor"]', 1),
('BUDGET_WARNING', '预算警告通知', '预算超支警告通知模板', '预算警告', 'BUDGET_WARNING', '预算警告：{{budget.category}}', '您的{{budget.category}}预算即将超支：\n已使用：{{budget.used}} {{budget.currency}}\n预算总额：{{budget.total}} {{budget.currency}}\n使用率：{{budget.usageRate}}%', '["budget.category", "budget.used", "budget.currency", "budget.total", "budget.usageRate"]', 1),
('SUBSCRIPTION_EXPIRED', '订阅过期通知', '订阅服务过期通知模板', '订阅通知', 'SUBSCRIPTION', '订阅即将过期', '您的{{subscription.planName}}订阅即将过期：\n过期时间：{{subscription.expireDate}}\n剩余天数：{{subscription.remainingDays}}天\n请及时续费以继续使用服务。', '["subscription.planName", "subscription.expireDate", "subscription.remainingDays"]', 1),
('REPORT_GENERATED', '报表生成通知', '报表生成完成通知模板', '报表通知', 'REPORT', '报表生成完成：{{report.name}}', '您的{{report.name}}报表已生成完成：\n报表类型：{{report.type}}\n时间范围：{{report.dateRange}}\n生成时间：{{report.generateTime}}\n点击查看报表详情。', '["report.name", "report.type", "report.dateRange", "report.generateTime"]', 1);

-- 插入默认渠道配置
INSERT INTO `acc_notification_channel_config` (`channel`, `name`, `description`, `config_data`, `rate_limit`, `group_id`, `create_user`) VALUES
('SYSTEM_MESSAGE', '系统消息', '系统内部消息通知', '{"enabled": true, "priority": 1}', 1000, NULL, 1),
('EMAIL', '邮件通知', '邮件发送通知', '{"enabled": true, "priority": 2, "smtp": {"host": "", "port": 587, "username": "", "password": ""}}', 100, NULL, 1),
('TELEGRAM_BOT', 'Telegram机器人', 'Telegram机器人通知', '{"enabled": true, "priority": 3, "botToken": "", "chatId": ""}', 30, NULL, 1),
('DISCORD_BOT', 'Discord机器人', 'Discord机器人通知', '{"enabled": true, "priority": 4, "botToken": "", "channelId": ""}', 30, NULL, 1);

