package top.continew.admin.api.handler;

import cn.hutool.json.JSONUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;
import top.continew.starter.web.model.R;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * API访问拒绝处理器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
public class ApiAccessDeniedHandler implements AccessDeniedHandler {

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response,
                      AccessDeniedException accessDeniedException) throws IOException {
        
        log.warn("API访问被拒绝: {}, URI: {}", accessDeniedException.getMessage(), request.getRequestURI());
        
        response.setStatus(HttpStatus.FORBIDDEN.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());

        R<Void> result = R.fail(HttpStatus.FORBIDDEN.value(), "访问被拒绝，权限不足");
        response.getWriter().write(JSONUtil.toJsonStr(result));
    }
}
