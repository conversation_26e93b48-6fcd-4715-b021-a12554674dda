package top.continew.admin.bot.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.accounting.enums.PlatformType;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 命令处理请求
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "命令处理请求")
public class CommandProcessReq {

    /**
     * 命令文本
     */
    @Schema(description = "命令文本", example = "-50 午餐 @餐饮 #日常")
    @NotBlank(message = "命令文本不能为空")
    @Size(max = 1000, message = "命令文本长度不能超过1000字符")
    private String command;

    /**
     * 平台类型
     */
    @Schema(description = "平台类型")
    @NotNull(message = "平台类型不能为空")
    private PlatformType platform;

    /**
     * 群组ID
     */
    @Schema(description = "群组ID")
    @NotNull(message = "群组ID不能为空")
    private Long groupId;

    /**
     * 是否异步处理
     */
    @Schema(description = "是否异步处理", defaultValue = "false")
    private Boolean async = false;

    /**
     * 是否跳过验证
     */
    @Schema(description = "是否跳过验证", defaultValue = "false")
    private Boolean skipValidation = false;

    /**
     * 是否启用智能推荐
     */
    @Schema(description = "是否启用智能推荐", defaultValue = "true")
    private Boolean enableSuggestion = true;

    /**
     * 超时时间（秒）
     */
    @Schema(description = "超时时间（秒）", defaultValue = "30")
    private Integer timeoutSeconds = 30;

    /**
     * 重试次数
     */
    @Schema(description = "重试次数", defaultValue = "0")
    private Integer retryCount = 0;

    /**
     * 上下文信息
     */
    @Schema(description = "上下文信息")
    private String context;

    /**
     * 附加参数
     */
    @Schema(description = "附加参数")
    private String metadata;
}
