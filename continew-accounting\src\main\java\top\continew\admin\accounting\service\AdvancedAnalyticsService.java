package top.continew.admin.accounting.service;

import top.continew.admin.accounting.model.query.DrillDownQuery;
import top.continew.admin.accounting.model.query.MultiDimensionQuery;
import top.continew.admin.accounting.model.resp.*;

import java.util.List;
import java.util.Map;

/**
 * 高级数据分析服务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface AdvancedAnalyticsService {

    /**
     * 多维数据分析
     *
     * @param query 多维查询条件
     * @return 多维分析结果
     */
    MultiDimensionAnalysisResp getMultiDimensionAnalysis(MultiDimensionQuery query);

    /**
     * 数据钻取分析
     *
     * @param query 钻取查询条件
     * @return 钻取分析结果
     */
    DrillDownAnalysisResp getDrillDownAnalysis(DrillDownQuery query);

    /**
     * 获取数据立方体
     *
     * @param groupId 群组ID
     * @param dimensions 维度列表
     * @param measures 度量列表
     * @return 数据立方体
     */
    DataCubeResp getDataCube(Long groupId, List<String> dimensions, List<String> measures);

    /**
     * 获取关联分析
     *
     * @param groupId 群组ID
     * @param analysisType 分析类型
     * @return 关联分析结果
     */
    CorrelationAnalysisResp getCorrelationAnalysis(Long groupId, String analysisType);

    /**
     * 获取异常检测结果
     *
     * @param groupId 群组ID
     * @param detectionType 检测类型
     * @return 异常检测结果
     */
    AnomalyDetectionResp getAnomalyDetection(Long groupId, String detectionType);

    /**
     * 获取预测分析
     *
     * @param groupId 群组ID
     * @param predictionType 预测类型
     * @param periods 预测期数
     * @return 预测分析结果
     */
    PredictionAnalysisResp getPredictionAnalysis(Long groupId, String predictionType, int periods);

    /**
     * 获取聚合分析
     *
     * @param groupId 群组ID
     * @param aggregationType 聚合类型
     * @param groupByFields 分组字段
     * @return 聚合分析结果
     */
    AggregationAnalysisResp getAggregationAnalysis(Long groupId, String aggregationType, List<String> groupByFields);

    /**
     * 获取同期对比分析
     *
     * @param groupId 群组ID
     * @param compareType 对比类型（同比/环比）
     * @param period 对比周期
     * @return 同期对比分析结果
     */
    PeriodComparisonResp getPeriodComparison(Long groupId, String compareType, String period);

    /**
     * 获取漏斗分析
     *
     * @param groupId 群组ID
     * @param funnelSteps 漏斗步骤
     * @return 漏斗分析结果
     */
    FunnelAnalysisResp getFunnelAnalysis(Long groupId, List<String> funnelSteps);

    /**
     * 获取队列分析
     *
     * @param groupId 群组ID
     * @param cohortType 队列类型
     * @return 队列分析结果
     */
    CohortAnalysisResp getCohortAnalysis(Long groupId, String cohortType);

    /**
     * 获取实时分析数据
     *
     * @param groupId 群组ID
     * @param metrics 指标列表
     * @return 实时分析数据
     */
    RealTimeAnalysisResp getRealTimeAnalysis(Long groupId, List<String> metrics);

    /**
     * 获取自定义分析
     *
     * @param groupId 群组ID
     * @param analysisConfig 分析配置
     * @return 自定义分析结果
     */
    CustomAnalysisResp getCustomAnalysis(Long groupId, Map<String, Object> analysisConfig);

    /**
     * 导出分析结果
     *
     * @param analysisId 分析ID
     * @param format 导出格式
     * @return 导出文件路径
     */
    String exportAnalysisResult(String analysisId, String format);

    /**
     * 保存分析配置
     *
     * @param groupId 群组ID
     * @param configName 配置名称
     * @param analysisConfig 分析配置
     * @return 配置ID
     */
    String saveAnalysisConfig(Long groupId, String configName, Map<String, Object> analysisConfig);

    /**
     * 获取分析配置列表
     *
     * @param groupId 群组ID
     * @return 分析配置列表
     */
    List<AnalysisConfigResp> getAnalysisConfigs(Long groupId);

    /**
     * 删除分析配置
     *
     * @param configId 配置ID
     */
    void deleteAnalysisConfig(String configId);
}
