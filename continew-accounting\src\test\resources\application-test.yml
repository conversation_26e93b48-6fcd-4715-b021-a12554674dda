# 测试环境配置
spring:
  profiles:
    active: test
  
  # 数据源配置（H2内存数据库）
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: jdbc:h2:mem:testdb;MODE=MySQL;DATABASE_TO_LOWER=TRUE;CASE_INSENSITIVE_IDENTIFIERS=TRUE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
  
  # H2控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # Redis配置（使用嵌入式Redis）
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      id-type: auto
  mapper-locations: classpath*:mapper/**/*.xml

# JetCache配置
jetcache:
  statIntervalMinutes: 15
  areaInCacheName: false
  local:
    default:
      type: caffeine
      keyConvertor: fastjson2
      valueEncoder: java
      valueDecoder: java
      limit: 100
      expireAfterWriteInMillis: 300000
  remote:
    default:
      type: redis.lettuce
      keyConvertor: fastjson2
      valueEncoder: java
      valueDecoder: java
      poolConfig:
        minIdle: 5
        maxIdle: 20
        maxTotal: 50
      host: localhost
      port: 6379
      database: 0

# 日志配置
logging:
  level:
    top.continew.admin.accounting: DEBUG
    org.springframework.test: DEBUG
    org.testcontainers: INFO
    com.github.dockerjava: WARN
    org.apache.http: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 测试配置
test:
  # 数据库初始化
  database:
    init-schema: true
    init-data: true
  
  # 缓存配置
  cache:
    enabled: true
    clear-on-startup: true
  
  # 异步配置
  async:
    enabled: false
  
  # 外部服务Mock
  external-services:
    mock-enabled: true
    exchange-rate-api:
      mock: true
      base-url: http://localhost:8080/mock
    google-sheets-api:
      mock: true
    notification-service:
      mock: true

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# 安全配置（测试环境关闭）
security:
  enabled: false

# SaToken配置（测试环境简化）
sa-token:
  token-name: Authorization
  timeout: 2592000
  activity-timeout: -1
  is-concurrent: true
  is-share: true
  token-style: uuid
  is-log: false
