package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 报表钱包统计响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "报表钱包统计响应")
public class ReportWalletResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 钱包ID
     */
    @Schema(description = "钱包ID", example = "1")
    private Long walletId;

    /**
     * 钱包名称
     */
    @Schema(description = "钱包名称", example = "支付宝")
    private String walletName;

    /**
     * 钱包图标
     */
    @Schema(description = "钱包图标", example = "icon-alipay")
    private String walletIcon;

    /**
     * 钱包颜色
     */
    @Schema(description = "钱包颜色", example = "#1677FF")
    private String walletColor;

    /**
     * 币种
     */
    @Schema(description = "币种", example = "CNY")
    private String currency;

    /**
     * 收入金额
     */
    @Schema(description = "收入金额", example = "5000.00")
    private BigDecimal incomeAmount;

    /**
     * 支出金额
     */
    @Schema(description = "支出金额", example = "3000.00")
    private BigDecimal expenseAmount;

    /**
     * 净收入
     */
    @Schema(description = "净收入", example = "2000.00")
    private BigDecimal netAmount;

    /**
     * 交易笔数
     */
    @Schema(description = "交易笔数", example = "50")
    private Integer transactionCount;

    /**
     * 收入笔数
     */
    @Schema(description = "收入笔数", example = "20")
    private Integer incomeCount;

    /**
     * 支出笔数
     */
    @Schema(description = "支出笔数", example = "30")
    private Integer expenseCount;

    /**
     * 占比（百分比）
     */
    @Schema(description = "占比", example = "25.5")
    private BigDecimal percentage;

    /**
     * 当前余额
     */
    @Schema(description = "当前余额", example = "8500.00")
    private BigDecimal currentBalance;
}
