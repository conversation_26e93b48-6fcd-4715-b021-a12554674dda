package top.continew.admin.accounting.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 报表趋势响应
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Schema(description = "报表趋势响应")
public class ReportTrendResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 日期/时间
     */
    @Schema(description = "日期/时间", example = "2025-01-01")
    private String date;

    /**
     * 收入金额
     */
    @Schema(description = "收入金额", example = "1000.00")
    private BigDecimal incomeAmount;

    /**
     * 支出金额
     */
    @Schema(description = "支出金额", example = "800.00")
    private BigDecimal expenseAmount;

    /**
     * 净收入
     */
    @Schema(description = "净收入", example = "200.00")
    private BigDecimal netAmount;

    /**
     * 交易笔数
     */
    @Schema(description = "交易笔数", example = "15")
    private Integer transactionCount;

    /**
     * 收入笔数
     */
    @Schema(description = "收入笔数", example = "5")
    private Integer incomeCount;

    /**
     * 支出笔数
     */
    @Schema(description = "支出笔数", example = "10")
    private Integer expenseCount;
}
