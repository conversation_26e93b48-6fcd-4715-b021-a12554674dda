package top.continew.admin.accounting.service;

import cn.hutool.core.lang.tree.Tree;
import top.continew.admin.common.base.service.BaseService;
import top.continew.admin.accounting.model.entity.CategoryDO;
import top.continew.admin.accounting.model.query.CategoryQuery;
import top.continew.admin.accounting.model.req.CategoryCreateReq;
import top.continew.admin.accounting.model.req.CategoryUpdateReq;
import top.continew.admin.accounting.model.resp.CategoryDetailResp;
import top.continew.admin.accounting.model.resp.CategoryListResp;
import top.continew.starter.data.service.IService;
import top.continew.starter.extension.crud.model.query.SortQuery;

import java.util.List;

/**
 * 分类管理业务接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
public interface CategoryService extends BaseService<CategoryListResp, CategoryDetailResp, CategoryQuery, CategoryCreateReq>, IService<CategoryDO> {

    /**
     * 更新分类信息
     *
     * @param req 更新请求
     * @param id  分类ID
     */
    void update(CategoryUpdateReq req, Long id);

    /**
     * 查询分类树结构
     *
     * @param query     查询条件
     * @param sortQuery 排序条件
     * @param isSimple  是否简化
     * @return 分类树
     */
    List<Tree<Long>> tree(CategoryQuery query, SortQuery sortQuery, boolean isSimple);

    /**
     * 获取群组分类列表
     *
     * @param groupId 群组ID
     * @param type    分类类型（INCOME/EXPENSE）
     * @return 分类列表
     */
    List<CategoryListResp> getGroupCategories(Long groupId, String type);

    /**
     * 获取群组分类树
     *
     * @param groupId 群组ID
     * @param type    分类类型（INCOME/EXPENSE）
     * @return 分类树
     */
    List<Tree<Long>> getGroupCategoryTree(Long groupId, String type);

    /**
     * 获取默认分类
     *
     * @param groupId 群组ID
     * @param type    分类类型（INCOME/EXPENSE）
     * @return 默认分类
     */
    CategoryDO getDefaultCategory(Long groupId, String type);

    /**
     * 创建默认分类
     *
     * @param groupId 群组ID
     */
    void createDefaultCategories(Long groupId);

    /**
     * 检查分类名称是否重复
     *
     * @param name     分类名称
     * @param parentId 父分类ID
     * @param groupId  群组ID
     * @param id       排除的分类ID
     * @return 是否重复
     */
    boolean isNameExists(String name, Long parentId, Long groupId, Long id);

    /**
     * 获取子分类ID列表（包含自身）
     *
     * @param categoryId 分类ID
     * @return 子分类ID列表
     */
    List<Long> getChildrenIds(Long categoryId);

    /**
     * 获取祖先路径
     *
     * @param parentId 父分类ID
     * @return 祖先路径
     */
    String getAncestors(Long parentId);

    /**
     * 更新子分类的祖先路径
     *
     * @param categoryId   分类ID
     * @param newAncestors 新的祖先路径
     * @param oldAncestors 旧的祖先路径
     */
    void updateChildrenAncestors(Long categoryId, String newAncestors, String oldAncestors);

    /**
     * 检查是否可以删除分类
     *
     * @param categoryId 分类ID
     * @return 是否可以删除
     */
    boolean canDelete(Long categoryId);

    /**
     * 级联删除分类及其子分类
     *
     * @param categoryIds 分类ID列表
     */
    void cascadeDelete(List<Long> categoryIds);

    /**
     * 移动分类到新的父分类下
     *
     * @param categoryId  分类ID
     * @param newParentId 新父分类ID
     */
    void moveCategory(Long categoryId, Long newParentId);

    /**
     * 复制分类到其他群组
     *
     * @param categoryId    源分类ID
     * @param targetGroupId 目标群组ID
     * @param operatorId    操作人ID
     * @return 新分类ID
     */
    Long copyToGroup(Long categoryId, Long targetGroupId, Long operatorId);

    /**
     * 批量导入分类
     *
     * @param groupId    群组ID
     * @param categories 分类列表
     * @param operatorId 操作人ID
     * @return 导入结果
     */
    ImportResult batchImport(Long groupId, List<CategoryCreateReq> categories, Long operatorId);

    /**
     * 导入结果
     */
    class ImportResult {
        private int totalCount;
        private int successCount;
        private int failureCount;
        private List<String> errorMessages;

        // Getters and Setters
        public int getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(int totalCount) {
            this.totalCount = totalCount;
        }

        public int getSuccessCount() {
            return successCount;
        }

        public void setSuccessCount(int successCount) {
            this.successCount = successCount;
        }

        public int getFailureCount() {
            return failureCount;
        }

        public void setFailureCount(int failureCount) {
            this.failureCount = failureCount;
        }

        public List<String> getErrorMessages() {
            return errorMessages;
        }

        public void setErrorMessages(List<String> errorMessages) {
            this.errorMessages = errorMessages;
        }
    }
}
