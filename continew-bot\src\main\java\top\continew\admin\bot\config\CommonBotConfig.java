package top.continew.admin.bot.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 通用机器人配置
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "bot.common")
public class CommonBotConfig {

    /**
     * 命令前缀
     */
    private String commandPrefix = "/";

    /**
     * 是否启用速率限制
     */
    private Boolean rateLimitEnabled = true;

    /**
     * 每分钟最大请求数
     */
    private Integer maxRequestsPerMinute = 30;

    /**
     * 是否记录所有消息
     */
    private Boolean logAllMessages = false;

    /**
     * 默认语言
     */
    private String defaultLanguage = "zh-CN";

    /**
     * 默认时区
     */
    private String defaultTimezone = "Asia/Shanghai";

    /**
     * 默认币种
     */
    private String defaultCurrency = "CNY";

    /**
     * 是否启用自动回复
     */
    private Boolean autoReplyEnabled = true;

    /**
     * 是否启用命令提示
     */
    private Boolean commandHintsEnabled = true;

    /**
     * 是否启用错误提示
     */
    private Boolean errorMessagesEnabled = true;

    /**
     * 消息超时时间（秒）
     */
    private Integer messageTimeout = 300;

    /**
     * 会话超时时间（秒）
     */
    private Integer sessionTimeout = 1800;

    /**
     * 最大消息长度
     */
    private Integer maxMessageLength = 4000;

    /**
     * 是否启用调试模式
     */
    private Boolean debugMode = false;

    /**
     * 管理员用户ID列表
     */
    private String[] adminUsers = {};

    /**
     * 黑名单用户ID列表
     */
    private String[] blacklistUsers = {};

    /**
     * 白名单群组ID列表
     */
    private String[] whitelistGroups = {};

    /**
     * 敏感词列表
     */
    private String[] bannedWords = {};

    /**
     * 通知配置
     */
    private NotificationConfig notification = new NotificationConfig();

    /**
     * 缓存配置
     */
    private CacheConfig cache = new CacheConfig();

    /**
     * 重试配置
     */
    private RetryConfig retry = new RetryConfig();

    /**
     * 通知配置
     */
    @Data
    public static class NotificationConfig {
        /**
         * 是否启用交易通知
         */
        private Boolean transactionNotifications = true;

        /**
         * 是否启用错误通知
         */
        private Boolean errorNotifications = true;

        /**
         * 是否启用系统通知
         */
        private Boolean systemNotifications = true;

        /**
         * 通知频率限制（秒）
         */
        private Integer notificationCooldown = 5;

        /**
         * 是否启用群组通知
         */
        private Boolean groupNotifications = true;

        /**
         * 是否启用私聊通知
         */
        private Boolean privateNotifications = true;
    }

    /**
     * 缓存配置
     */
    @Data
    public static class CacheConfig {
        /**
         * 是否启用缓存
         */
        private Boolean enabled = true;

        /**
         * 用户会话缓存时间（秒）
         */
        private Integer userSessionTtl = 1800;

        /**
         * 群组信息缓存时间（秒）
         */
        private Integer groupInfoTtl = 3600;

        /**
         * 命令结果缓存时间（秒）
         */
        private Integer commandResultTtl = 300;

        /**
         * 最大缓存大小
         */
        private Integer maxCacheSize = 10000;
    }

    /**
     * 重试配置
     */
    @Data
    public static class RetryConfig {
        /**
         * 是否启用重试
         */
        private Boolean enabled = true;

        /**
         * 最大重试次数
         */
        private Integer maxAttempts = 3;

        /**
         * 重试间隔（毫秒）
         */
        private Integer retryInterval = 1000;

        /**
         * 重试倍数
         */
        private Double retryMultiplier = 2.0;

        /**
         * 最大重试间隔（毫秒）
         */
        private Integer maxRetryInterval = 10000;
    }
}
