package top.continew.admin.accounting.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import top.continew.admin.accounting.base.BaseControllerTest;
import top.continew.admin.accounting.model.req.GroupCreateReq;
import top.continew.admin.accounting.model.req.GroupQueryReq;
import top.continew.admin.accounting.model.req.GroupUpdateReq;
import top.continew.admin.accounting.model.resp.GroupResp;
import top.continew.admin.accounting.service.GroupService;
import top.continew.admin.accounting.util.TestDataUtil;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 群组控制器测试
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
class GroupControllerTest extends BaseControllerTest {

    @MockBean
    private GroupService groupService;

    private GroupCreateReq createReq;
    private GroupUpdateReq updateReq;
    private GroupResp groupResp;

    @BeforeEach
    @Override
    protected void beforeEach() {
        super.beforeEach();
        createReq = TestDataUtil.createTestGroupCreateReq();
        
        updateReq = new GroupUpdateReq();
        updateReq.setName("更新后的群组");
        updateReq.setDescription("更新后的描述");
        
        groupResp = new GroupResp();
        groupResp.setId(1L);
        groupResp.setName("测试群组");
        groupResp.setDescription("这是一个测试群组");
        groupResp.setCurrency("CNY");
    }

    @Test
    void testAdd() throws Exception {
        // Given
        when(groupService.add(any(GroupCreateReq.class))).thenReturn(1L);

        // When & Then
        performPost("/group", createReq)
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(1L));

        verify(groupService).add(any(GroupCreateReq.class));
    }

    @Test
    void testGetById() throws Exception {
        // Given
        when(groupService.get(1L)).thenReturn(groupResp);

        // When & Then
        performGet("/group/1")
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value(1L))
                .andExpect(jsonPath("$.data.name").value("测试群组"));

        verify(groupService).get(1L);
    }

    @Test
    void testUpdate() throws Exception {
        // Given
        doNothing().when(groupService).update(any(GroupUpdateReq.class), eq(1L));

        // When & Then
        performPut("/group/1", updateReq)
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        verify(groupService).update(any(GroupUpdateReq.class), eq(1L));
    }

    @Test
    void testDelete() throws Exception {
        // Given
        doNothing().when(groupService).delete(anyList());

        // When & Then
        performDelete("/group/1")
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        verify(groupService).delete(anyList());
    }

    @Test
    void testPage() throws Exception {
        // Given
        PageResp<GroupResp> pageResp = new PageResp<>();
        pageResp.setTotal(1L);
        pageResp.setList(Arrays.asList(groupResp));
        
        when(groupService.page(any(GroupQueryReq.class), any(PageQuery.class)))
                .thenReturn(pageResp);

        // When & Then
        mockMvc.perform(get("/group/page")
                .param("page", "1")
                .param("size", "10")
                .param("name", "测试")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.total").value(1))
                .andExpect(jsonPath("$.data.list[0].name").value("测试群组"));

        verify(groupService).page(any(GroupQueryReq.class), any(PageQuery.class));
    }

    @Test
    void testList() throws Exception {
        // Given
        when(groupService.list(any(GroupQueryReq.class)))
                .thenReturn(Arrays.asList(groupResp));

        // When & Then
        mockMvc.perform(get("/group/list")
                .param("name", "测试")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data[0].name").value("测试群组"));

        verify(groupService).list(any(GroupQueryReq.class));
    }

    @Test
    void testAddMember() throws Exception {
        // Given
        doNothing().when(groupService).addMember(eq(1L), eq(2L), eq("MEMBER"));

        // When & Then
        mockMvc.perform(post("/group/1/member")
                .param("userId", "2")
                .param("role", "MEMBER")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        verify(groupService).addMember(1L, 2L, "MEMBER");
    }

    @Test
    void testRemoveMember() throws Exception {
        // Given
        doNothing().when(groupService).removeMember(eq(1L), eq(2L));

        // When & Then
        mockMvc.perform(delete("/group/1/member/2")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        verify(groupService).removeMember(1L, 2L);
    }

    @Test
    void testGenerateInviteCode() throws Exception {
        // Given
        when(groupService.generateInviteCode(1L)).thenReturn("INVITE123");

        // When & Then
        mockMvc.perform(post("/group/1/invite-code")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value("INVITE123"));

        verify(groupService).generateInviteCode(1L);
    }

    @Test
    void testJoinByInviteCode() throws Exception {
        // Given
        when(groupService.joinByInviteCode(eq("INVITE123"), eq(2L))).thenReturn(1L);

        // When & Then
        mockMvc.perform(post("/group/join")
                .param("inviteCode", "INVITE123")
                .param("userId", "2")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(1L));

        verify(groupService).joinByInviteCode("INVITE123", 2L);
    }

    @Test
    void testGetStatistics() throws Exception {
        // Given
        when(groupService.getStatistics(1L)).thenReturn(new Object());

        // When & Then
        performGet("/group/1/statistics")
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        verify(groupService).getStatistics(1L);
    }

}
