package top.continew.admin.accounting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import top.continew.admin.accounting.model.entity.GroupDO;
import top.continew.admin.accounting.model.resp.GroupListResp;
import top.continew.admin.accounting.enums.PlatformType;

import java.util.List;

/**
 * 群组 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface GroupMapper extends BaseMapper<GroupDO> {

    /**
     * 根据平台信息查找群组
     *
     * @param platform        平台类型
     * @param platformGroupId 平台群组ID
     * @return 群组信息
     */
    @Select("SELECT * FROM acc_group WHERE platform = #{platform} AND platform_group_id = #{platformGroupId} AND status = 1")
    GroupDO findByPlatformInfo(@Param("platform") PlatformType platform, @Param("platformGroupId") String platformGroupId);

    /**
     * 获取用户的群组列表
     *
     * @param userId 用户ID
     * @return 群组列表
     */
    @Select("""
        SELECT g.id, g.name, g.platform, g.platform_group_id, g.subscription_plan,
               g.status, g.create_time, COUNT(m.id) as member_count
        FROM acc_group g
        INNER JOIN acc_group_member gm ON g.id = gm.group_id
        LEFT JOIN acc_group_member m ON g.id = m.group_id AND m.status = 1
        WHERE gm.user_id = #{userId} AND g.status = 1 AND gm.status = 1
        GROUP BY g.id, g.name, g.platform, g.platform_group_id, g.subscription_plan, g.status, g.create_time
        ORDER BY g.create_time DESC
        """)
    List<GroupListResp> selectUserGroups(@Param("userId") Long userId);

    /**
     * 获取群组当月交易次数
     *
     * @param groupId 群组ID
     * @return 交易次数
     */
    @Select("""
        SELECT COUNT(*) FROM acc_transaction
        WHERE group_id = #{groupId}
        AND DATE_FORMAT(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
        AND status = 1
        """)
    int selectCurrentMonthTransactionCount(@Param("groupId") Long groupId);

    /**
     * 检查用户是否为群组成员
     *
     * @param groupId 群组ID
     * @param userId  用户ID
     * @return 是否为成员
     */
    @Select("""
        SELECT COUNT(*) > 0 FROM acc_group_member 
        WHERE group_id = #{groupId} AND user_id = #{userId} AND status = 1
        """)
    boolean isMember(@Param("groupId") Long groupId, @Param("userId") Long userId);

    /**
     * 检查用户是否为群组管理员
     *
     * @param groupId 群组ID
     * @param userId  用户ID
     * @return 是否为管理员
     */
    @Select("""
        SELECT COUNT(*) > 0 FROM acc_group_member 
        WHERE group_id = #{groupId} AND user_id = #{userId} 
        AND role IN ('OWNER', 'ADMIN') AND status = 1
        """)
    boolean isAdmin(@Param("groupId") Long groupId, @Param("userId") Long userId);
}
