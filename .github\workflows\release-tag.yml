name: Release

on:
  push:
    tags:
      - "v*" # Push events to matching v*, i.e. v1.0.0

jobs:
  release:
    if: github.repository == 'continew-org/continew-admin'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@master
      - name: Create Release for Tag
        id: release_tag
        uses: yyx990803/release-tag@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          body: |
            详情请参阅 [CHANGELOG.md](https://github.com/${{ github.repository }}/blob/dev/CHANGELOG.md) 。
